<%@ Page Title="" Language="C#" MasterPageFile="~/top.master" AutoEventWireup="true" CodeFile="address_add.aspx.cs" Inherits="order_list" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="Server">
    <script>
        $(function () {
            popTitle('新增收货地址', '');
        })
    </script>
    <style>
        .info_title {
            color: #000;
            font-size: 14px;
            font-weight: bold;
            display: flex;
            align-items: center;
            margin: 10px 0;
            margin-top: 18px;
        }

            .info_title i {
                display: inline-block;
                background: #2A3EE8;
                width: 6px;
                height: 6px;
                border-radius: 50%;
            }
    </style>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="Server">


    <div style="background: #fff; padding: 10px;">

        <div class="info_title">
            <i></i>&nbsp;&nbsp;真实姓名   
        </div>
        <div style="position: relative; background: #f6f6f6; border-radius: 8px; margin-bottom: 18px;">
            <input style="border: 0px; background: none; font-size: 14px; padding: 20px; outline: none; width: 100%; box-sizing: border-box; font-weight: bold; color: #000;" id="name" value="" placeholder="请输入真实姓名">
        </div>


        <div class="info_title">
            <i></i>&nbsp;&nbsp;电话号码   
        </div>
        <div style="position: relative; background: #f6f6f6; border-radius: 8px; margin-bottom: 18px;">
            <input style="border: 0px; background: none; font-size: 14px; padding: 20px; outline: none; width: 100%; box-sizing: border-box; font-weight: bold; color: #000;" id="phone" value="" placeholder="请输入电话号码">
        </div>

        <div class="info_title">
            <i></i>&nbsp;&nbsp;选择地区   
        </div>
        <div style="position: relative; background: #f6f6f6; border-radius: 8px; margin-bottom: 18px;"  class="addr-select-father">
            <input style="border: 0px; background: none; font-size: 14px; padding: 20px; outline: none; width: 100%; box-sizing: border-box; font-weight: bold; color: #000;" id="address" class="addr-select" value="" placeholder="请选择省市区" >
            <a style="position: absolute; top: 22px; right: 22px; display: flex; align-items: center; border-radius: 38px; justify-content: center; cursor: pointer;">
                <svg t="1692429683877" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="32319" width="10" height="10">
                    <path d="M307.2 972.8a51.2 51.2 0 0 0 36.352-14.848l409.6-409.6a51.2 51.2 0 0 0 0-72.192l-409.6-409.6a51.2 51.2 0 0 0-72.704 72.192l373.76 373.248-373.76 373.248A51.2 51.2 0 0 0 307.2 972.8z" fill="#000" p-id="32320"></path></svg>
            </a>
        </div>

        <div class="info_title">
            <i></i>&nbsp;&nbsp;详细地址
        </div>
        <div style="position: relative; background: #f6f6f6; border-radius: 8px; margin-bottom: 18px;">
            <input style="border: 0px; background: none; font-size: 14px; padding: 20px; outline: none; width: 100%; box-sizing: border-box; font-weight: bold; color: #000;" id="address_details" value="" placeholder="请输入详细地址">
        </div>


    </div>

    <div style="position: fixed; left: 0; bottom: 8px; width: 100%; padding: 5px 18px; box-sizing: border-box;">
        <a style="display: block; background: #3838f5; color: #fff; width: 100%; padding: 16px; box-sizing: border-box; border-radius: 10px; cursor: pointer; text-align: center; font-size: 15px; margin-top: 13px;" onclick="add_address()">确认添加</a>

    </div>



    <div class="demo-popup-page payway-select" style="display: none;">
        <div style="padding: 18px; font-size: 16px;">

            <h4 style="color: #3C54D1; margin-bottom: 22px;">选择主网</h4>

            <div style="display: flex; margin: 38px 0; align-items: center; color: #000; font-weight: bold;" onclick="set_payway('TRC20')">
                TRC20（波场链）           
           
            </div>
            <div style="display: flex; margin: 38px 0; align-items: center; color: #000; font-weight: bold;" onclick="set_payway('ERC20')">
                ERC20（以太坊主网）           
           
            </div>
            <div style="display: flex; margin: 38px 0; align-items: center; color: #000; font-weight: bold;" onclick="set_payway('Uliner')">
                Ulinker（内部转账）           
           
            </div>

        </div>
    </div>
    
    

    <script src="../citypicker/my.js"></script>
    <script src="../citypicker/better_scroll.min.js" type="text/javascript" charset="utf-8"></script>
    <link href="../citypicker/json.css" rel="stylesheet" />
    <script src="../citypicker/city-picker.data.js" type="text/javascript" charset="utf-8"></script>
    <script src="../citypicker/prototype.js" type="text/javascript" charset="utf-8"></script>
    
    <style>
        .betterHeader {
            height: 200px;
        }


        .betterHeader1 .wheel-scroll .wheel-item {
            font-size: 16px;
            height: 38px;
        }

        .li_position {
            height: 38px;
        }

        .Provincial_urban_areas2_xuanze {
            padding: 18px;
        }

            .Provincial_urban_areas2_xuanze span {
                padding: 6px 13px;
                font-size: 16px;
                border-radius: 5px;
            }
    </style>


    <script>
        var set_payway = function (name) {
            $('#network').val(name);
            closePopup();
        }

        var add_address = function () {
            v3api("address_add", {
                data: {
                    name: document.getElementById('name').value,
                    phone: document.getElementById('phone').value,
                    address: document.getElementById('address').value,
                    address_details: document.getElementById('address_details').value
                }
            }, function () {
                history.go(-1);
            })
        }

        new Star({
            id: ".addr-select",//input的ID
            dataId: ".addr-select-father",//父级id
            wheels: ["北京市", "北京市", "东城区"],//默认数据
            callback: function (data) {
                console.log(data)
                $(".addr-select").val(data.province + " " + data.city + " " + data.area)
            }
        })
    </script>

</asp:Content>


<%@ Page Title="" Language="C#" MasterPageFile="~/top.master" AutoEventWireup="true" CodeFile="sell_dating.aspx.cs" Inherits="order_list" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="Server">

    <link href="../slayer/layer.css" rel="stylesheet" />
    <script src="../slayer/layer.js"></script>

</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="Server">



    <div class=" pg-ontouch order_page" style="display: ;">

        <div style="position: fixed; display: flex; top: 0; width: 100%; flex-direction: column; height: 100%; z-index: 9; max-width: 600px; left: 50%; transform: translate(-50%, 0);">

            <div style="background: #fff; box-sizing: border-box; position: relative; height: 100%; display: flex; flex-direction: column;">


                <div style="display: flex; background: #73C3CB; color: #fff; padding: 12px 0; font-weight: bold;">
                    <div>
                        <a class="ppreturn" style="height: 100%; width: 50px; display: flex; align-items: baseline; justify-content: center;" onclick="javascript:history.go(-1);;">
                            <svg t="" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="27857" width="16" height="16">
                                <path d="M716.8 972.8a51.2 51.2 0 0 1-36.352-14.848l-409.6-409.6a51.2 51.2 0 0 1 0-72.192l409.6-409.6a51.2 51.2 0 0 1 72.192 72.192L379.392 512l373.248 373.248A51.2 51.2 0 0 1 716.8 972.8z" fill="#fff" p-id="27858"></path></svg></a>
                    </div>

                    <div style="width: 100%; text-align: center;">我要提款</div>

                    <div style="width: 102px;">
                        <a href="sell_dating_records.aspx" style="color: #fff; text-decoration: none;">提款记录</a>
                    </div>

                </div>

                <div style="overflow-y: auto;">

                    <div style="padding: 18px;">

                        <div style="margin-bottom: 6px; margin-top: 10px; display: flex;">
                            <div style="width: 130px">账户余额</div>
                            <span style="color: red;" class="user_amount"><%=uConfig.gnumber(userdt, "amount") %></span>
                            <img src="/static/images/coin.png" alt="" height="15" width="15" style="margin-left: 5px;">
                        </div>

                        <div style="display: flex; font-weight: bold; color: #000; padding: 3px 0; align-items: center;">
                            <div style="width: 130px;">
                                输入提款金额
                       
                            </div>
                            <div>
                                <input style="border-radius: 3px; border: 1px solid #73C3CB; padding: 8px; outline: none; font-weight: bold; text-align: center; box-shadow: 3px 2px 5px #73C3CB; font-size: 22px; box-sizing: border-box; width: 200px;" id="task_amount">
                            </div>
                        </div>


                        <div style="display: flex; font-weight: bold; color: #2a2b2c; padding: 3px 0; align-items: center;">
                            <div style="width: 130px; flex-shrink: 0; font-weight: 500; color: #5a5b5c;">手续费</div>
                            <div style="display: flex; width: 100%; align-items: center; color: #323539; font-weight: 500;">
                                <span id="tax_amount">-</span><span id="sell_tax" style="color: #5a5b5c; font-weight: 500; font-size: 13px; margin-left: 3px;" tax="<%=uConfig.stcdata("selldating_tax") %>">(<%=uConfig.stcdata("selldating_tax") %>%)</span>
                            </div>
                        </div>

                        <div style="display: flex; font-weight: bold; color: #2a2b2c; padding: 3px 0; align-items: center;">
                            <div style="width: 130px; flex-shrink: 0;">实际到账金额</div>
                            <div style="display: flex; width: 100%; align-items: center;">
                                <span id="really_amount">-</span>
                            </div>
                        </div>

                        <div style="display: flex; font-weight: bold; color: #000; padding: 3px 0; align-items: center; margin-top: 10px;">
                            <div style="width: 130px;">收款方式</div>
                            <div style="display: flex;" onclick="javascript:openButtomPage($('.payway-select').html());">
                                <div style="border-radius: 3px; border: 1px solid #cbc4c4; padding: 5px 8px; outline: none; font-weight: 500; text-align: center; display: flex; align-items: center; margin-right: 8px; background: #f1f1f1; cursor: pointer;" id="payment_type_data">
                                    <span style="margin-left: 3px; cursor: pointer;" id="payment_type">选择提款方式</span>
                                </div>
                            </div>
                        </div>
                        <div class="showdata" sd="姓名" style="display: none;">
                            <div style="display: flex; font-weight: bold; color: #2a2b2c; padding: 3px 0; align-items: center;">
                                <div style="width: 130px; flex-shrink: 0;">收款姓名</div>
                                <div style="display: flex; width: 100%;">

                                    <input style="width: 100%; border: 0; outline: none; padding: 3px 0; font-size: 16px; color: #5a5b5c;" disabled="disabled" class="name" value="<%=pmlist["payment_name"] %>">
                                </div>
                            </div>
                        </div>
                        <div class="showdata" sd="钱包" style="display: none;">
                            <div style="display: flex; font-weight: bold; color: #2a2b2c; padding: 3px 0; align-items: center;">
                                <div style="width: 130px; flex-shrink: 0;">钱包地址</div>
                                <div style="display: flex; width: 100%;">

                                    <input style="width: 100%; border: 0; outline: none; padding: 3px 0; font-size: 16px; color: #5a5b5c;" disabled="disabled" class="bankid" value="<%=pmlist["payment_name"] %>">
                                </div>
                            </div>
                        </div>
                        <div class="showdata" sd="支付宝" style="display: none;">
                            <div style="display: flex; font-weight: bold; color: #2a2b2c; padding: 3px 0; align-items: center;">
                                <div style="width: 130px; flex-shrink: 0;">支付宝账号</div>
                                <div style="display: flex; width: 100%;">

                                    <input style="width: 100%; border: 0; outline: none; padding: 3px 0; font-size: 16px; color: #5a5b5c;" disabled="disabled" class="bankid" value="<%=pmlist["payment_bankid"] %>">
                                </div>
                            </div>
                        </div>
                        <div class="showdata" sd="银行卡" style="display: none;">
                            <div style="display: flex; font-weight: bold; color: #2a2b2c; padding: 3px 0; align-items: center;">
                                <div style="width: 130px; flex-shrink: 0;">收款卡号</div>
                                <div style="display: flex; width: 100%;">

                                    <input style="width: 100%; border: 0; outline: none; padding: 3px 0; font-size: 16px; color: #5a5b5c;" disabled="disabled" class="bankid" value="<%=pmlist["payment_bankid"] %>">
                                </div>
                            </div>
                            <div style="display: flex; font-weight: bold; color: #2a2b2c; padding: 3px 0; align-items: center;">
                                <div style="width: 130px; flex-shrink: 0;">银行类型</div>
                                <div style="display: flex; width: 100%;">

                                    <input style="width: 100%; border: 0; outline: none; padding: 3px 0; font-size: 16px; color: #5a5b5c;" disabled="disabled" class="bankname" value="<%=pmlist["payment_bankname"] %>">
                                </div>
                            </div>
                            <div style="display: flex; font-weight: bold; color: #2a2b2c; padding: 3px 0; align-items: center;">
                                <div style="width: 130px; flex-shrink: 0;">开户行地址</div>
                                <div style="display: flex; width: 100%;">

                                    <input style="width: 100%; border: 0; outline: none; padding: 3px 0; font-size: 16px; color: #5a5b5c;" disabled="disabled" class="khh" value="<%=pmlist["payment_bankbranch"] %>">
                                </div>
                            </div>
                        </div>
                    </div>

                    <div style="background: #FFFBF0; padding: 15px;">
                        <div style="color: #5EBFC6; display: flex; align-items: center;">
                            <svg t="*************" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="9569" width="18" height="18">
                                <path d="M512 64a448 448 0 1 0 448 448 448 448 0 0 0-448-448z m3.2 704a48 48 0 0 1-48-47.36 48.64 48.64 0 0 1 48-48 48 48 0 0 1 47.36 48 47.36 47.36 0 0 1-47.36 47.36z m80.64-271.36A131.84 131.84 0 0 0 547.84 576v32a33.28 33.28 0 0 1-64 0v-24.32a181.76 181.76 0 0 1 67.2-120.32 165.76 165.76 0 0 0 60.16-104.96 83.2 83.2 0 0 0-90.24-89.6c-64 0-99.84 41.6-101.76 119.68a32 32 0 0 1-31.36 28.8 31.36 31.36 0 0 1-30.72-28.8V384a154.24 154.24 0 0 1 163.84-166.4 135.04 135.04 0 0 1 147.2 140.8 196.48 196.48 0 0 1-72.32 138.24z" p-id="9570" fill="#5EBFC6"></path></svg>&nbsp;提款教程
                        </div>

                        <p style="color: red; font-weight: bold; font-size: 15px;">① 输入提款金额,然后支付【等待到账即可】</p>
                        <p style="color: red; font-weight: bold; font-size: 15px;">② 提款成功,请注意收款账号余额变动!</p>
                        <p style="color: red; font-weight: bold; font-size: 15px;">③ 提款限额,<%=uConfig.stcdata("sell_limit") %>【不限制次数】</p>
                        <p style="color: red; font-weight: bold; font-size: 15px;">④ 提款提交之后1小时后未到账请与在线客服联系！</p>
                        <p style="color: red; font-weight: bold; font-size: 15px;">⑤ 提款需扣除<%=uConfig.stcdata("selldating_tax") %>%手续费</p>

                        <a style="display: inline-block; background: #72C4CC; color: #fff; width: 100%; padding: 12px; box-sizing: border-box; text-align: center; border-radius: 3px;" onclick="sell_dating()">我已了解，确认提款</a>
                    </div>


                </div>

                <div style="height: 72px; flex-shrink: 0;"></div>


            </div>
        </div>


    </div>


    <div class="pg-gd-history order_page" style="display: none;">

        <div style="position: fixed; display: flex; top: 0; width: 100%; flex-direction: column; height: 100%; z-index: 9; max-width: 600px; left: 50%; transform: translate(-50%, 0);">

            <div style="background: #fff; box-sizing: border-box; position: relative; height: 100%; display: flex; flex-direction: column;">


                <div style="display: flex; background: #73C3CB; color: #fff; padding: 12px 0; font-weight: bold;">
                    <div>
                        <a class="ppreturn" style="height: 100%; width: 50px; display: flex; align-items: baseline; justify-content: center;" onclick="return_ontouch()">
                            <svg t="" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="27857" width="16" height="16">
                                <path d="M716.8 972.8a51.2 51.2 0 0 1-36.352-14.848l-409.6-409.6a51.2 51.2 0 0 1 0-72.192l409.6-409.6a51.2 51.2 0 0 1 72.192 72.192L379.392 512l373.248 373.248A51.2 51.2 0 0 1 716.8 972.8z" fill="#fff" p-id="27858"></path></svg></a>
                    </div>

                    <div style="width: 100%; text-align: center; position: relative; left: -18px;">提款记录</div>

                    <%--<div style="width: 102px;">
                        <a onclick="open_gdlist()">挂单记录</a>
                    </div>--%>
                </div>


                <div class="guadan_list" style="background: rgb(231, 231, 231); color: rgb(202, 211, 199); font-size: 12px; text-align: left; border-radius: 10px; margin: 2px 8px; overflow-y: auto; box-sizing: border-box; flex-grow: 1; margin-top: 10px;">

                    <div class="user_guadan" style="border-radius: 8px; text-align: center; color: rgb(154, 155, 152); font-weight: bold; font-size: 14px; margin: 10px;">
                    </div>


                </div>


                <div style="height: 72px; flex-shrink: 0;"></div>


            </div>

        </div>

    </div>

    <script>

        $('#task_amount').on('keyup', function () {
            var tax = parseFloat($('#sell_tax').attr('tax'));
            var amount = $(this).val();
            if (isNaN(amount) || parseFloat(amount) < 0) {
                amount = 0
            }

            var tax_amount = amount / 100 * tax;
            var really_amount = amount - tax_amount;

            $('#tax_amount').html(tax_amount);
            $('#really_amount').html(really_amount);
        })

        var sell_dating = function (paypwd) {
            if (typeof (paypwd) == "undefined") {

                security_password(function (e) {

                    sell_dating(e.password);

                }, { error_tip_top: '<div style="color: #2a2b2c;padding: 0 30px;margin-top: 20px;font-weight: bold;text-align:center;font-size: 16px;color:#000;" class="animated-text"><div style="    margin-bottom: 10px;">您确定提款了吗？</div><div style=" margin-bottom: 5px; "><span style="color:red;">本次提款金额为 ' + $('#task_amount').val() + '<img src="/static/images/coin.png" alt="" height="15" width="15" style="margin-left: 5px;"></span></div>   </div>' })
                return;
            }

            if (update_id == "") {
                tp('请选择提款方式');
                return;
            }


            layer.open({
                type: 2,
                content: '正在提交',
                time: 99,
                shadeClose: false
            })

            v3api("sell_dating", {
                error: 1,
                data: {
                    task_amount: $('#task_amount').val(),
                    payment_id: update_id,
                    paypwd: paypwd
                }
            }, function (e) {
                layer.closeAll();
                console.log('ee', e);
                tp(e.msg);

            })


        }
    </script>


    <div class="demo-popup-page payway-select" style="display: none;">
        <div style="padding: 18px; font-size: 16px;">

            <h4 style="color: #3C54D1; margin-bottom: 22px;">选择提款方式</h4>

            <div style="display: flex; margin-bottom: 30px; align-items: center; font-weight: bold;" onclick="set_payway('银行卡')">
                <svg t="1692522586005" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="18868" width="32" height="32">
                    <path d="M534.493867 78.762667l359.850666 346.9312c26.112 25.088 26.9312 67.754667 1.8432 93.866666L697.105067 724.8896c-25.088 26.094933-67.754667 26.9312-93.8496 1.8432L243.4048 379.818667c-26.094933-25.088-26.9312-67.754667-1.8432-93.8496L440.644267 80.622933c25.088-26.112 66.696533-28.296533 93.8496-1.860266z" fill="#FFD3D7" p-id="18869"></path><path d="M322.935467 155.989333l460.288 195.396267c33.006933 13.994667 49.937067 52.718933 35.2768 87.278933l-111.36 262.3488c-14.011733 32.989867-52.736 49.937067-87.278934 35.259734L159.556267 540.893867c-32.989867-13.994667-49.937067-52.718933-35.259734-87.278934l111.36-262.3488c13.994667-33.006933 54.272-49.271467 87.278934-35.2768z" fill="#FB560A" p-id="18870"></path><path d="M150.186667 708.266667h723.626666l92.16 128c34.133333 47.786667 15.36 85.333333-44.373333 85.333333H93.866667C35.84 921.6 17.066667 884.053333 51.2 836.266667l98.986667-128z" fill="#DAE9FF" p-id="18871"></path><path d="M225.28 349.866667h597.333333c59.733333 0 107.52 47.786667 107.52 107.52v281.6c0 59.733333-47.786667 107.52-107.52 107.52h-597.333333c-59.733333 0-107.52-47.786667-107.52-107.52V457.386667c0-59.733333 47.786667-107.52 107.52-107.52z" fill="#DAE9FF" p-id="18872"></path><path d="M208.213333 349.866667h558.08c59.733333 0 107.52 47.786667 107.52 107.52v281.6c0 59.733333-47.786667 107.52-107.52 107.52H208.213333c-59.733333 0-107.52-47.786667-107.52-107.52V457.386667c0-59.733333 47.786667-107.52 107.52-107.52z" fill="#3889FF" p-id="18873"></path><path d="M605.866667 759.466667m-25.6 0a25.6 25.6 0 1 0 51.2 0 25.6 25.6 0 1 0-51.2 0Z" fill="#FFFFFF" p-id="18874"></path><path d="M691.2 759.466667m-25.6 0a25.6 25.6 0 1 0 51.2 0 25.6 25.6 0 1 0-51.2 0Z" fill="#FFFFFF" p-id="18875"></path><path d="M776.533333 759.466667m-25.6 0a25.6 25.6 0 1 0 51.2 0 25.6 25.6 0 1 0-51.2 0Z" fill="#FFFFFF" p-id="18876"></path><path d="M233.813333 460.8c-15.36 0-29.013333 11.946667-29.013333 27.306667 0 15.36 11.946667 27.306667 29.013333 27.306666h527.36c15.36 0 29.013333-11.946667 29.013334-27.306666 0-15.36-11.946667-27.306667-29.013334-27.306667H233.813333z" fill="#DAE9FF" p-id="18877"></path><path d="M216.746667 443.733333c-15.36 0-29.013333 11.946667-29.013334 27.306667 0 15.36 11.946667 27.306667 29.013334 27.306667h527.36c15.36 0 29.013333-15.36 29.013333-27.306667s-13.653333-27.306667-29.013333-27.306667H216.746667z" fill="#FFFFFF" p-id="18878"></path></svg>&nbsp;&nbsp;&nbsp;银行卡
            </div>

            <%--<div style="display: flex; margin-bottom: 30px; align-items: center; font-weight: bold;" onclick="set_payway('支付宝')">
                <svg t="1692522651546" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="20838" width="32" height="32">
                    <path d="M230.4 576.512c-12.288 9.728-25.088 24.064-28.672 41.984-5.12 24.576-1.024 55.296 22.528 79.872 28.672 29.184 72.704 37.376 91.648 38.912 51.2 3.584 105.984-22.016 147.456-50.688 16.384-11.264 44.032-34.304 70.144-69.632-59.392-30.72-133.632-64.512-212.48-61.44-40.448 1.536-69.632 9.728-90.624 20.992z m752.64 135.68c26.112-61.44 40.96-129.024 40.96-200.192C1024 229.888 794.112 0 512 0S0 229.888 0 512s229.888 512 512 512c170.496 0 321.536-83.968 414.72-211.968-88.064-43.52-232.96-115.712-322.56-159.232-42.496 48.64-105.472 97.28-176.64 118.272-44.544 13.312-84.992 18.432-126.976 9.728-41.984-8.704-72.704-28.16-90.624-47.616-9.216-10.24-19.456-22.528-27.136-37.888 0.512 1.024 1.024 2.048 1.024 3.072 0 0-4.608-7.68-7.68-19.456-1.536-6.144-3.072-11.776-3.584-17.92-0.512-4.096-0.512-8.704 0-12.8-0.512-7.68 0-15.872 1.536-24.064 4.096-20.48 12.8-44.032 35.328-65.536 49.152-48.128 114.688-50.688 148.992-50.176 50.176 0.512 138.24 22.528 211.968 48.64 20.48-43.52 33.792-90.112 41.984-121.344h-307.2v-33.28h157.696v-66.56H272.384V302.08h190.464V235.52c0-9.216 2.048-16.384 16.384-16.384h74.752V302.08h207.36v33.28h-207.36v66.56h165.888s-16.896 92.672-68.608 184.32c115.2 40.96 278.016 104.448 331.776 125.952z" fill="#06B4FD" p-id="20839"></path></svg>&nbsp;&nbsp;&nbsp;支付宝
           
            </div>--%>
            <div style="display: flex; margin-bottom: 30px; align-items: center; font-weight: bold;" onclick="set_payway('OKPAY')">
                <img src="../static/images/pay/OKPAY.png" width="32" height="32">&nbsp;&nbsp;&nbsp;OKPAY
            </div>
            <div style="display: flex; margin-bottom: 30px; align-items: center; font-weight: bold;" onclick="set_payway('GOPAY')">
                <img src="../static/images/pay/GOPAY.png" width="32" height="32">&nbsp;&nbsp;&nbsp;GOPAY
           
            </div>
            <div style="display: flex; margin-bottom: 30px; align-items: center; font-weight: bold;" onclick="set_payway('ABPAY')">
                <img src="../static/images/pay/ABPAY.png" width="32" height="32">&nbsp;&nbsp;&nbsp;ABPAY
            </div>
        </div>
    </div>


    <script>
        var update_id = '';
        var set_payway = function (name) {
            v3api("get_payway", {
                data: {
                    type: name
                }
            }, function (e) {
                if (e.update_id == "") {
                    tp('请先绑定' + name + '账号在进行提款');
                    closePopup();
                    return;
                }
                set_paywayName(name);
                update_id = e.update_id;


                $('.name').val(e.name)
                $('.bankid').val(e.bankid);
                $('.khh').val(e.khh);
                $('.bankname').val(e.bankname);

            })
        }

        var set_paywayName = function (name) {


            var payment_type_data = '<svg t="" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="5245" width="18" height="18"><path d="M229.643 233.89c-26.054 3.28-52.107 24.846-60.636 49.688-5.683 15.47-107.536 454.219-108.005 462.19-0.949 22.968 12.314 39.844 34.104 44.064 8.055 1.874 266.703 1.874 275.232 0 24.631-4.69 47.843-23.908 56.372-47.345 3.316-8.906 108.48-456.093 108.48-463.595 1.422-21.096-11.372-38.44-31.738-44.533-4.739-0.934-262.912-1.874-273.81-0.47z" fill="#E60012" p-id="5246"></path><path d="M470.762 233.89c-26.054 3.28-52.107 24.846-60.632 49.688-5.214 15.47-107.534 454.219-107.534 462.19-0.95 22.968 12.314 39.844 34.108 44.064 8.05 1.874 266.698 1.874 275.227 0 24.635-4.69 47.847-23.908 56.372-47.345 3.317-8.906 108.479-456.093 108.479-463.595 1.424-21.096-11.366-38.44-31.736-44.533-5.214-0.934-263.387-1.874-274.284-0.47z" fill="#00508E" p-id="5247"></path><path d="M722.308 233.89c-26.054 3.28-52.112 24.846-60.637 49.688-5.209 15.47-107.534 454.219-107.534 462.19-0.945 22.968 12.32 39.844 34.108 44.064 8.055 1.874 200.383 1.874 208.908 0 24.634-4.69 47.847-23.908 56.372-47.345 3.316-8.906 108.484-456.093 108.484-463.595 1.418-21.096-11.371-38.44-31.743-44.533-4.734-1.404-220.748-2.343-231.645-0.934h23.687v0.465z" fill="#00908C" p-id="5248"></path><path d="M221.589 376.39c0 0.47-0.476 3.282-1.42 6.095-10.896 36.562-21.793 85.781-20.37 91.874 3.79 18.283 30.793 16.876 40.266-2.343 2.842-5.154 23.212-90.936 23.212-97.028 0-0.47 32.212 0 32.212 0.464 0 0 0 1.408-0.475 2.817-0.474 1.403-5.683 21.091-11.366 44.529-12.795 49.688-14.213 54.842-20.845 64.686-21.793 31.878-94.746 30.94-100.429-1.404-1.422-7.032 14.213-88.124 21.32-110.159 0-1.403 37.895-0.469 37.895 0.47z m484.139 0c21.79 4.69 28.42 22.03 18.472 47.816-9.947 25.78-31.262 37.028-68.686 37.028-11.371 0-10.423-1.874-17.529 33.282-1.419 6.562-2.841 12.656-2.841 13.594-0.476 2.343-34.11 2.343-33.635 0 28.426-120.468 30.793-130.78 30.793-131.72l0.474-1.402h34.104c27.003 0.464 34.583 0.464 38.848 1.403z m-292.282 46.408c4.735 0.938 8.05 4.69 8.05 7.97 0 11.717-24.16 19.688-33.16 10.779-8.525-8.905 9.475-22.5 25.11-18.75z m-83.849 8.904c0 0.94-0.474 2.817-0.474 4.22l-0.474 1.878 5.683-2.816c15.16-7.497 29.844-6.094 34.583 3.281 2.841 5.629 2.367 8.91-5.21 43.595-1.422 6.094-3.315 14.534-3.79 18.748-1.897 9.38-0.474 8.91-17.528 8.91-14.687 0-14.687 0-14.212-1.408 0-0.938 1.896-8.435 3.79-17.345 7.58-33.277 8.055-37.967 1.422-37.967-3.79 0-9.004 2.343-9.473 3.75-0.948 3.282-9.478 44.06-9.952 47.812l-0.945 4.22-14.687 0.938c-17.998 0.47-16.58 1.873-12.79-14.064 5.21-20.626 8.055-35.154 9.949-48.28 2.367-14.063 0.948-12.655 14.212-14.532 6.158-0.94 12.315-1.874 14.213-2.343 4.735-1.409 5.683-0.94 5.683 1.403z m225.014-0.464c0 0.933-0.474 2.811-0.474 4.216l-0.476 2.346 5.688-2.816c29.37-14.998 40.737-2.813 32.212 35.628-1.893 8.436-4.265 20.623-5.683 26.25-0.949 6.094-2.372 11.248-2.842 11.717-1.898 1.874-29.844 1.41-29.375 0 0-0.938 1.898-8.435 3.791-16.875 8.056-34.216 8.056-38.436 0.949-38.436-5.683 0-8.525 1.873-9.473 6.092-1.424 5.155-8.53 38.906-9.475 45l-0.948 5.158-14.687 0.47c-17.999 0.465-16.58 2.342-12.316-15.003 4.74-18.75 8.056-36.094 10.423-48.749 1.893-12.187 0.474-10.782 12.315-12.656 5.213-0.938 11.846-1.878 14.214-2.342 4.738-2.348 6.157-1.878 6.157 0z m287.547-0.47c1.892 28.592 2.368 37.028 2.368 37.498 0 0.47 4.264-7.032 8.999-16.406 9.473-18.749 7.58-16.876 18.002-18.28 2.842-0.469 8.525-1.409 12.79-2.342 10.423-1.878 10.423-2.817-1.423 17.81-16.105 27.658-38.368 66.564-46.423 80.627-24.157 43.591-24.157 43.591-44.527 44.06l-12.316 0.47 0.945-3.282c0.474-1.873 1.897-5.623 2.37-8.435l1.42-5.159h3.79c4.265 0 5.209-0.94 9-7.502 1.423-2.342 3.79-6.093 4.738-8.435 1.42-2.343 6.158-9.844 9.949-17.345l7.58-13.125-1.897-17.34c-2.367-20.158-5.209-44.065-6.631-51.097-0.95-6.562-0.95-6.562 7.58-7.5 3.79-0.466 9.948-1.874 13.264-2.343 8.999-2.812 9.947-2.812 10.422-1.874z m-357.183 0.47c36.476 6.562 23.686 69.37-16.106 78.28-27.003 6.094-45.475-4.22-45.475-24.847 0.47-36.093 27.472-59.53 61.58-53.433z m272.86 1.873c1.893 0.938 4.739 2.812 6.158 4.22 2.367 2.342 2.367 2.342 2.367 0.934 0.475-1.873 0-1.873 18.951-4.685 15.158-2.342 14.684-2.342 13.739 1.874-6.158 26.249-11.371 49.217-13.739 60.47-3.315 16.876-0.948 14.998-19.421 14.529h-15.635v-1.874c0-1.873-0.945-2.812-1.894-1.407-5.213 8.44-30.792 5.158-37.898-5.155-17.525-26.25 19.896-81.562 47.371-68.906z m-340.129 13.595s0 2.342-0.474 4.219c-3.786 14.999-10.418 45.469-11.842 51.092l-1.422 7.032-15.632 0.469c-18.472 0.47-17.528 1.404-13.738-9.843 3.316-10.783 6.633-23.908 8.53-37.972 1.892-12.186 0-10.313 13.738-12.186 6.157-0.94 12.79-1.873 14.208-2.342 3.79-0.939 6.158-0.939 6.632-0.47z m82.9 97.028c0 0.47-0.944 2.348-2.367 4.69-0.95 2.342-2.367 4.22-2.367 4.22 27.946 0.464 28.895 0.464 28.42 1.873l-5.209 16.876h-40.74l-2.367 1.873c-5.214 4.689-32.686 10.782-32.686 7.031l5.21-16.875h3.789c6.632 0 8.05-1.404 13.738-11.247l4.735-8.91c24.636-0.465 29.843 0 29.843 0.47z m62.06 0c0 0.47-0.475 2.812-1.424 5.629-0.948 2.342-1.417 4.685-1.417 5.154 0 0 2.366-0.94 5.207-3.28 10.423-7.033 19.422-8.437 45.95-8.437 10.423 0 19.422 0 19.896 0.465 0.475 0.939-15.156 51.565-17.528 56.25-3.316 6.098-6.633 9.379-11.84 11.721l-4.74 1.874-26.998 0.47-27.003 0.468-4.738 15.937c-9.474 30.47-9.474 28.128 4.264 26.72 10.897-0.94 10.423-1.873 7.107 9.374l-2.842 9.375h-13.738c-29.844 0.47-33.634-1.404-30.793-13.594 1.423-6.094 35.528-117.656 36.002-118.595 0.474-0.465 24.635-0.465 24.635 0.47z m124.584 0c0 0.47-0.474 1.877-0.944 3.75-1.423 4.69-1.423 4.69 4.735 1.41 8.054-4.221 27.002-5.629 65.845-5.629h12.32v5.629c0 6.562 0.475 7.03 6.158 7.966l4.264 0.469-2.372 8.44-2.366 8.435h-8.53c-21.789 0.47-25.104-1.873-25.58-14.528v-6.098l-1.418 4.22-1.423 4.69H733.2c-2.367 0-4.735 0-4.735 0.47 0 0-23.211 76.401-26.528 87.184-0.474 0.94 0 1.409 2.843 1.409 4.264 0 4.264 0 2.841 3.745-1.419 4.221-1.419 4.221 3.316 4.221 3.316 0 5.214-0.47 7.58-1.873 3.317-1.878 3.317-1.408 18.473-22.5l6.158-8.909h-12.79c-15.631 0-14.207 0.939-11.366-8.435l2.367-7.502h31.268c2.841-9.844 3.785-12.656 3.785-13.125 0-0.47-6.632-0.47-15.156-0.47h-15.158l4.735-16.875h42.633c23.212 0 42.638 0 42.638 0.47 0 0.469-0.948 4.22-2.37 8.44l-2.368 7.966-14.213 0.469-14.21 0.469c-2.37 7.032-3.315 10.313-3.789 11.252l-0.474 1.873h13.738c16.106 0 15.157-0.938 11.84 8.436l-2.367 7.501h-31.266l-4.735 5.624h12.316l1.892 11.252c1.898 12.656 1.898 12.656 8.055 12.656 4.74 0 4.74-0.94 1.424 10.312l-2.847 9.375h-8.999c-15.631 0-18.473-2.342-21.32-18.28l-1.418-10.313-5.683 7.502c-15.636 21.091-16.58 21.56-36.476 21.56-12.794 0-12.794 0-10.897-3.75 0.475-1.873 0.475-1.873-3.316-1.873-3.79 0-3.79 0-4.738 2.812l-0.475 2.811H666.88l0.474-1.408c1.424-4.684 3.79-4.215-25.105-4.215-25.109 0-26.527 0-26.053-1.409l2.368-8.435c2.841-8.44 2.367-8.44 5.209-8.44 2.37 0 2.37 0 3.315-3.281 22.268-73.592 29.374-97.03 30.319-100.31l1.897-6.094h13.264c8.525 0 14.682 0.465 14.682 0.934z m-168.167 40.314l-5.213 16.406h-28.42c-2.842 9.375-3.79 12.187-4.266 13.125-0.474 1.409 0.475 1.409 14.214 1.409 8.054 0 14.686 0.47 14.686 0.47 0 0.464-0.474 1.402-0.95 2.81-0.473 0.935-1.422 4.686-2.365 8.436l-1.898 6.093H475.5l-3.316 11.252c-4.738 15.937-4.264 16.406 13.738 14.064 7.581-0.939 7.107-1.877 3.79 9.375l-2.841 9.374h-19.9c-31.263 0-31.737-0.939-23.212-27.658 2.372-8.436 4.74-15.468 4.74-15.468s-3.317-0.47-8.056-0.47c-4.26 0-8.05 0-8.05-0.469 3.315-11.716 4.734-15.468 4.734-16.406 0.474-0.934 1.423-1.403 8.53-1.403h8.05l3.79-14.534h-7.576c-5.688 0-7.58 0-7.58-0.939 0-0.933 4.264-14.528 4.733-15.937 0.95-0.933 72.483-0.469 72.008 0.47z m91.9 33.752c0 0.938-0.949 3.75-1.42 6.093-2.846 13.125-6.631 15.937-21.792 16.875-5.209 0.47-9.948 0.938-9.948 0.938-0.949 1.405-0.949 12.657 0 14.53l1.423 1.877 9.473-0.47c5.21-0.469 9.474-0.469 9.474-0.469 0 0.939-5.21 17.345-5.684 17.81-0.948 0.94-29.843 0.47-33.634-0.934-5.683-1.878-5.683-1.409-5.214-30.94l0.476-25.78h24.16v9.844h4.74c5.208 0 5.682-0.47 8.05-7.033l1.422-3.75h9.475c8.524 0.47 9.473 0.47 8.999 1.409z m44.53-200.626l-5.213 22.5h7.107c36.476 0.938 51.637-40.784 16.105-43.596-5.684-0.47-10.893-0.939-11.367-0.939-1.422 0-1.422 1.41-6.631 22.035z m-188.542 31.874c-8.999 3.75-17.524 37.501-10.417 42.656 5.208 4.22 12.79-2.812 16.58-14.529 6.158-21.565 4.26-31.878-6.163-28.127z m282.339 2.342c-9.478 4.69-16.58 37.971-9.478 41.721 9.478 5.155 21.794-12.654 21.794-31.877 0-8.905-5.214-13.125-12.316-9.844zM557.927 562.957l-2.368 8.436c-1.422 4.22-2.367 7.97-2.841 8.435 0 0.47 2.367-0.465 5.683-2.343 9.473-4.685 12.316-5.623 26.053-6.092l11.846-0.47c1.893-5.623 2.368-7.966 2.368-8.435 0.474-0.47-40.267-0.47-40.741 0.47z m-8.05 27.185l-1.899 7.5 40.741-0.47 2.368-7.5c-32.212 0-41.21 0-41.21 0.47z m128.848-16.876c-1.896 6.097-3.315 10.782-2.841 10.782l5.683-1.873c2.842-0.94 8.055-2.347 10.897-2.812 2.841-0.469 5.209-0.94 5.683-0.94 0 0 4.74-14.532 4.74-15.001 0 0-4.74-0.465-10.423-0.465H682.04l-3.316 10.31z m-7.58 23.907c0 0.469-1.893 5.159-3.317 10.782-1.892 5.624-3.315 10.783-3.315 10.783 0 0.465 2.367-0.47 5.683-1.878 3.316-1.403 8.055-2.812 10.897-3.28 6.157-0.94 6.631-1.404 7.107-3.751 0.474-0.935 1.422-4.685 2.366-7.497l1.898-5.628H682.04c-5.683 0-10.897 0-10.897 0.469z m-16.58 53.908l20.845 0.47c4.264-14.064 5.683-18.754 5.683-19.223l-20.844-0.934-5.684 19.687z" fill="#FFFFFF" p-id="5249"></path></svg>'
            switch (name) {
                case "支付宝":
                    payment_type_data = '<svg t="" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="" width="18" height="18"><path d="M230.4 576.512c-12.288 9.728-25.088 24.064-28.672 41.984-5.12 24.576-1.024 55.296 22.528 79.872 28.672 29.184 72.704 37.376 91.648 38.912 51.2 3.584 105.984-22.016 147.456-50.688 16.384-11.264 44.032-34.304 70.144-69.632-59.392-30.72-133.632-64.512-212.48-61.44-40.448 1.536-69.632 9.728-90.624 20.992z m752.64 135.68c26.112-61.44 40.96-129.024 40.96-200.192C1024 229.888 794.112 0 512 0S0 229.888 0 512s229.888 512 512 512c170.496 0 321.536-83.968 414.72-211.968-88.064-43.52-232.96-115.712-322.56-159.232-42.496 48.64-105.472 97.28-176.64 118.272-44.544 13.312-84.992 18.432-126.976 9.728-41.984-8.704-72.704-28.16-90.624-47.616-9.216-10.24-19.456-22.528-27.136-37.888 0.512 1.024 1.024 2.048 1.024 3.072 0 0-4.608-7.68-7.68-19.456-1.536-6.144-3.072-11.776-3.584-17.92-0.512-4.096-0.512-8.704 0-12.8-0.512-7.68 0-15.872 1.536-24.064 4.096-20.48 12.8-44.032 35.328-65.536 49.152-48.128 114.688-50.688 148.992-50.176 50.176 0.512 138.24 22.528 211.968 48.64 20.48-43.52 33.792-90.112 41.984-121.344h-307.2v-33.28h157.696v-66.56H272.384V302.08h190.464V235.52c0-9.216 2.048-16.384 16.384-16.384h74.752V302.08h207.36v33.28h-207.36v66.56h165.888s-16.896 92.672-68.608 184.32c115.2 40.96 278.016 104.448 331.776 125.952z" fill="#06B4FD" p-id="20839"></path></svg>'
                    break;
                case "ABPAY":
                case "GOPAY":
                case "OKPAY":
                    payment_type_data = '<img src="../static/images/pay/' + name + '.png" width="18" height="18">';
                    break;
                default:
                    break;

            }


            payment_type_data += '<span style="margin-left: 3px;" id="payment_type">' + name + '</span>';

            $('#payment_type_data').html(payment_type_data);
            $('.showdata').hide();
            $('[sd="' + name + '"]').show();
            switch (name) {
                case "ABPAY":
                case "GOPAY":
                case "OKPAY":
                    $('[sd="钱包"]').show();
                    break;
                default:
                    $('[sd="姓名"]').show();
                    break;

            }
            closePopup();
        }
        //set_paywayName($('#payment_type').html());
    </script>
</asp:Content>

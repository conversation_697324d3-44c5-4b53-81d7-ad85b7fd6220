<%@ Page Title="" Language="C#" MasterPageFile="~/top.master" AutoEventWireup="true" CodeFile="sell_dating_records.aspx.cs" Inherits="order_list" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="Server">
    <script>
        $(function () {
            popTitle('卖币记录', '');
        })
    </script>
    <style>
        .top-title {
            background: #EDEDED;
        }

        body {
            background: #fff;
        }

        .top-title .pptitle {
            font-weight: 100;
        }
    </style>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="Server">





    <div id="lists">
    </div>



    <script>

        var parseNumber = function (amount) {
            var result = 0;
            try {
                result = parseFloat(amount);
            }
            catch (e) {

            }
            return result;
        }

        var more_list = function (index) {
            show_list(index);
        }

        var show_list = function (index) {
            if (!index) {
                index = 0;
            }

            //<a style="margin-left:5px;" class="design-button-common" onclick="finish_order(' + obj.id + ')">确认收款</a>

            v3api("lists", { data: { page: 'sell_dating_list', p: index, limit: 10 } }, function (e) {
                for (var i = 0; i < e.data.list.length; i++) {
                    var obj = e.data.list[i];
                    $('#lists').append('<div sid="' + obj.id + '" style="padding-bottom: 18px; border-bottom: 1px solid #f2f2f2; margin-bottom: 18px;"><div style="color: #5a5b5c;text-shadow: 5px 5px 5px #********;font-weight: bold;">' + obj.orderNo + '</div><div style="margin-top: 10px; line-height: 22px; font-size: 12px; color: #2a2b2c;"><div>收款方式：' + obj.payment_type + '(' + obj.payment_bankid.substring(0, 2) + '***' + obj.payment_bankid.substring(obj.payment_bankid.length - 5) + ')' + '</div><div>交易订单：' + obj.buy_orderId + '</div><div style="color: gray;">交易金额：' + obj.amount + '</div><div style="color: gray;">手续费：' + parseNumber(obj.tax_amount) + '（' + parseNumber(obj.sell_tax) + '%）</div><div style="color: #9b4949;font-weight: bold;">实际到账：' + (parseNumber(obj.amount) - parseNumber(obj.tax_amount)).toFixed(2) + '</div></div><div style="font-size: 12px; margin-top: 8px; color: gray; display: flex;"><div>' + obj.create_time + '</div><div style="margin-left: auto;" class="state_text">' + (obj.state == 0 ? '<span style="color: #eee; padding: 1px 3px; margin-right: 3px; border-radius: 3px; font-size: 12px; background: linear-gradient(28deg, #ffffff, #e0ffff 100%); border: 1px solid #878181; color: #878181; font-weight: bold;">交易中</span>' : obj.state == 99 ? '审核中' : obj.state == -1 ? '交易撤销' : obj.state == 1 ? '<span style="color:#71429d;">交易成功</span>' : '状态异常') + '</div></div></div>');
                }



                if (e.data.list.length == 10) {
                    $('#lists').append('<div id="load_more" next_id="' + (parseInt(index) + 1) + '"></div>');
                }
                finish_trigger();

            })
        }

        more_list(0);

        var finish_order = function (id) {

            security_password(function (e) {
                v3api("finish_sell_order", { data: { paypwd: e.password, id: id } }, function (e) {
                    tp(e.msg);
                    $('[sid="' + id + '"] .state_text').html('<span style="color:#71429d;">交易成功</span>');

                })


            }, { error_tip_top: '<div style="color: #2a2b2c;padding: 0 30px;margin-top: 20px;font-weight: bold;text-align:center;font-size: 14px;" class="animated-text"><div style="    margin-bottom: 10px;">确定收到款了吗？</div><div>为了保证您的资金安全，<span style="color:red;">请收到款再确认收款</span></div>    </div>' });
        }


        function timestampToDateTimeString(timestamp) {
            // 将时间戳乘以1000以转换为13位时间戳（Date对象使用13位时间戳）
            var date = new Date(timestamp * 1000);

            // 获取年、月、日、时、分、秒
            var year = date.getFullYear();
            var month = String(date.getMonth() + 1).padStart(2, '0'); // 月份从0开始，需要加1
            var day = String(date.getDate()).padStart(2, '0');
            var hours = String(date.getHours()).padStart(2, '0');
            var minutes = String(date.getMinutes()).padStart(2, '0');
            var seconds = String(date.getSeconds()).padStart(2, '0');

            // 格式化日期时间字符串
            var formattedDateTimeString = year + '-' + month + '-' + day + ' ' + hours + ':' + minutes + ':' + seconds;

            return formattedDateTimeString;
        }
    </script>


    <script>
        $(document).ready(function () {
            $(window).scroll(function () {
                if ($(document).scrollTop() <= 0) {
                    console.log("滚动条已经到达顶部为0");
                }
                if ($(document).scrollTop() + 100 >= $(document).height() - $(window).height()) {
                    trigger_bottom();
                }

            });

        });

        var trigger_check = 0;
        var trigger_bottom = function () {
            if (trigger_check != 0) {
                return;
            }
            trigger_check = 1;
            console.log("滚动条已经到达底部为" + $(document).scrollTop());

            if (typeof (more_list)) {
                var m = $('#lists').find("#load_more");
                if (m.html() != undefined) {
                    m.remove();
                    more_list(m.attr("next_id"));
                }
            }
        }

        var finish_trigger = function () {
            trigger_check = 0;
        }

    </script>

</asp:Content>


using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

public partial class login_out : System.Web.UI.Page
{
    protected void Page_Load(object sender, EventArgs e)
    {
        Response.Cookies[uConfig.loginParames_token].Expires = DateTime.Now.AddDays(-1);
        Response.Cookies[uConfig.loginParames_uid].Expires = DateTime.Now.AddDays(-1);
        Response.Cookies[uConfig.loginParames_usernick].Expires = DateTime.Now.AddDays(-1);
        Response.Cookies[uConfig.loginParames_pwd].Expires = DateTime.Now.AddDays(-1);
        Response.Cookies[uConfig.loginParames_roomNumber].Expires = DateTime.Now.AddDays(-1);
        Response.Redirect("~/login.aspx");
    }
}
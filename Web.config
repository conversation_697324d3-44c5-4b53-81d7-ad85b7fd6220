<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <!--
    有关 .NET 4.5 的 web.config 更改的说明，请参见 http://go.microsoft.com/fwlink/?LinkId=235367。

    可在 <httpRuntime> 标记上设置以下特性。
      <system.Web>
        <httpRuntime targetFramework="4.5" />
      </system.Web>
  -->
  <system.web>
    <customErrors mode="Off" defaultRedirect="~/index.aspx" />
    <compilation debug="true" targetFramework="4.6">
      <assemblies>
        <add assembly="WindowsBase, Version=4.0.0.0, Culture=neutral, PublicKeyToken=31BF3856AD364E35" />
        <add assembly="System.Security, Version=4.0.0.0, Culture=neutral, PublicKeyToken=B03F5F7F11D50A3A" />
      </assemblies>
    </compilation>
    <httpRuntime requestValidationMode="2.0" executionTimeout="999" maxRequestLength="2097151" />
    <pages controlRenderingCompatibilityVersion="4.0" />
  </system.web>
  <appSettings />
  <connectionStrings>
    <add name="ConnectionSql" connectionString="server=.;database=taobw;uid=sa;pwd=sqltest123." providerName="System.Data.SqlClient" />
  </connectionStrings>
  <system.webServer>
    <directoryBrowse enabled="true" />
    <defaultDocument>
      <files>
        <add value="../index.aspx" />
      </files>
    </defaultDocument>
    <!--<rewrite>
      <rules>
        <rule name="已导入的规则 1">
          <match url="^api/server/user.aspx(.*)" ignoreCase="false" />
          <action type="Rewrite" url="/api/order/submit.aspx{R:1}" appendQueryString="true" />
        </rule>
      </rules>
    </rewrite>-->
    <rewrite>
      <rules>
        <!-- 重写到带两个参数的页面 -->
        <rule name="Rewrite to Page with Two Parameters">
          <match url="^thread-([0-9]+).html" />
          <action type="Rewrite" url="read.aspx?id={R:1}" appendQueryString="true" />
        </rule>
      </rules>
    </rewrite>
  </system.webServer>
  <runtime>
    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
      <dependentAssembly>
        <assemblyIdentity name="Newtonsoft.Json" publicKeyToken="30ad4fe6b2a6aeed" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-13.0.0.0" newVersion="13.0.0.0" />
      </dependentAssembly>
    </assemblyBinding>
  </runtime>
</configuration>
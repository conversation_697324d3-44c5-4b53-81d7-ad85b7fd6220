<%@ Page Title="" Language="C#" MasterPageFile="~/top.master" AutoEventWireup="true" CodeFile="gateway_list.aspx.cs" Inherits="order_list" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="Server">
    <script>
        $(function () {
            popTitle('当前余额：<span id="user_amount"><%=Convert.ToDouble(uConfig.gd(userdt,"amount")).ToString("0.00") %></span><img src="/static/images/coin.png" alt="" height="12" width="12" style="margin-left: 5px;">', '');
        })
    </script>

    <style>
        .top-title {
            background: none;
        }
    </style>
    <style>
        body {
            background: linear-gradient(45deg, #F0F4F9, #F5F5F8);
            background-repeat: no-repeat;
            height: 100vh;
        }

        .select_tab {
            color: #000;
            box-sizing: border-box;
            padding: 12px 20px;
            text-align: center;
            font-weight: bold;
            font-size: 13px;
            background: #eee;
            border-radius: 12px;
            width: 50%;
            margin: 0 10px;
        }

            .select_tab.activity {
                background: #3c3351;
                color: #fff;
                transition: all 0.2s;
            }

        .theme-green .back-bar .selected-bar {
            background-color: #3838f5!important;
            background-image: linear-gradient(to bottom, #4747ff, #3838f5)!important;
        }

        .slider-container {
            margin-bottom: 38px;
            margin-top: 52px;
        }
    </style>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="Server">



    <style>
        .paytype_list {
            background: #fff;
            width: 100%;
            display: flex;
            padding: 15px 25px;
            box-sizing: border-box;
            align-items: center;
            border-radius: 3px;
            font-size: 13px;
        }

        }
    </style>

    <div style="margin-bottom: 30px;">

        <div style="margin-bottom: 18px;">
            充值方式
        </div>

        <div id="paytype_select">




            <div class="paytype_list" onclick="select_pay(this,'美宜佳')">
                <div style="margin-right: 10px;">
                    <svg t="1695126716921" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="9034" width="32" height="32">
                        <path d="M894.509511 249.605689H330.752a37.660444 37.660444 0 0 0-37.546667 37.762844v342.448356a37.660444 37.660444 0 0 0 37.546667 37.762844h563.757511a37.660444 37.660444 0 0 0 37.558045-37.762844V287.368533a37.660444 37.660444 0 0 0-37.558045-37.762844z" fill="#CCCCCC" p-id="9035"></path><path d="M293.216711 333.585067H932.067556v97.655466H293.216711z" fill="#4D4D4D" p-id="9036"></path><path d="M688.685511 388.278044H124.928a37.660444 37.660444 0 0 0-37.546667 37.762845v342.448355a37.660444 37.660444 0 0 0 37.546667 37.762845h563.757511a37.660444 37.660444 0 0 0 37.546667-37.762845V426.040889a37.660444 37.660444 0 0 0-37.546667-37.762845z" fill="#FFCA6C" p-id="9037"></path><path d="M87.381333 472.257422h638.850845v97.655467H87.381333z" fill="#4D4D4D" p-id="9038"></path><path d="M213.595022 692.974933a58.595556 58.254222 90 1 0 116.508445 0 58.595556 58.254222 90 1 0-116.508445 0Z" fill="#47A7DD" p-id="9039"></path><path d="M155.3408 692.974933a58.595556 58.254222 90 1 0 116.508444 0 58.595556 58.254222 90 1 0-116.508444 0Z" fill="#FC583D" p-id="9040"></path><path d="M894.509511 234.951111H720.406756c-8.044089 0-14.563556 6.5536-14.563556 14.6432s6.519467 14.654578 14.563556 14.654578h174.102755c12.686222 0 22.994489 10.376533 22.994489 23.131022v31.561956H307.768889V287.379911c0-12.754489 10.308267-23.131022 22.994489-23.131022H671.857778c8.044089 0 14.552178-6.564978 14.552178-14.654578S679.913244 234.951111 671.869156 234.951111h-341.105778c-28.740267 0-52.1216 23.517867-52.1216 52.417422v86.254934H124.928c-28.728889 0-52.110222 23.517867-52.110222 52.417422V663.665778c0 8.100978 6.519467 14.654578 14.563555 14.654578 8.044089 0 14.563556-6.564978 14.563556-14.654578v-79.086934h609.723733v183.9104c0 12.743111-10.308267 23.108267-22.983111 23.108267H124.928a23.074133 23.074133 0 0 1-22.983111-23.108267v-55.990044c0-8.0896-6.519467-14.6432-14.563556-14.6432-8.044089 0-14.563556 6.5536-14.563555 14.6432v55.990044c0 28.899556 23.381333 52.406044 52.110222 52.406045h563.757511c28.728889 0 52.110222-23.506489 52.110222-52.406045V426.040889c0-28.899556-23.381333-52.417422-52.110222-52.417422H307.780267v-25.383823h609.735111v68.357689H772.846933c-8.044089 0-14.563556 6.5536-14.563555 14.6432s6.519467 14.654578 14.563555 14.654578h144.668445v183.9104a23.096889 23.096889 0 0 1-22.994489 23.131022H774.781156c-8.044089 0-14.552178 6.5536-14.552178 14.6432s6.508089 14.6432 14.552178 14.6432h119.728355c28.728889 0 52.1216-23.506489 52.1216-52.417422V287.379911C946.631111 258.468978 923.249778 234.951111 894.509511 234.951111z m-182.840889 191.089778v31.573333H178.642489c-8.044089 0-14.563556 6.5536-14.563556 14.6432s6.519467 14.654578 14.563556 14.654578h533.026133v68.357689H101.944889v-68.357689h28.16c8.044089 0 14.563556-6.564978 14.563555-14.654578s-6.519467-14.6432-14.563555-14.6432H101.944889v-31.573333c0-12.743111 10.308267-23.119644 22.983111-23.119645h563.757511a23.096889 23.096889 0 0 1 22.983111 23.119645z" fill="" p-id="9041"></path><path d="M242.744889 760.069689a72.100978 72.100978 0 0 0 29.104355 6.155378c40.152178 0 72.817778-32.8704 72.817778-73.250134 0-40.402489-32.6656-73.250133-72.817778-73.250133-10.069333 0-19.979378 2.127644-29.104355 6.132622a72.078222 72.078222 0 0 0-29.149867-6.132622c-40.152178 0-72.817778 32.847644-72.817778 73.250133 0 40.379733 32.6656 73.250133 72.817778 73.250134 10.365156 0 20.218311-2.218667 29.149867-6.155378z m72.795022-67.094756c0 24.223289-19.603911 43.9296-43.690667 43.9296h-0.034133a73.056711 73.056711 0 0 0 14.609067-43.9296 73.079467 73.079467 0 0 0-14.609067-43.952355h0.034133c24.098133 0 43.690667 19.706311 43.690667 43.952355z m-145.624178 0c0-24.246044 19.592533-43.952356 43.690667-43.952355 24.086756 0 43.690667 19.706311 43.690667 43.952355 0 24.223289-19.603911 43.9296-43.690667 43.9296-24.098133 0.011378-43.690667-19.706311-43.690667-43.9296zM655.633067 647.5776c8.032711 0 14.563556-6.5536 14.563555-14.6432s-6.530844-14.6432-14.563555-14.6432H440.103822c-8.044089 0-14.563556 6.5536-14.563555 14.6432s6.519467 14.6432 14.563555 14.6432h215.529245z" fill="" p-id="9042"></path></svg>
                </div>
                <span>支付</span>
                <div class="select_icon" style="margin-left: auto;">
                    <svg t="1695127337616" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="11440" width="25" height="25">
                        <path d="M218.47269380045435 793.6915279012346c-9.468623156464195 9.468623156464195-9.468623156464195 23.67155659662222 0 33.14017975308642 82.85044938271605 78.11613780448396 189.37245536205432 123.09209585588148 302.9959293560098 123.09209585588148 241.4498814280691 0 437.92380351020245-196.47392337667162 437.92380351020245-437.92380351020245S762.9185032899951 74.07619648979755 521.4686231564642 74.07619648979755 83.54481835172345 270.55011857193097 83.54481835172345 512c0 82.85044938271605 23.67155659662222 165.7008987654321 68.647515942558 236.71556984983704 7.101466720079011 11.83577829831111 21.304401454775306 14.202934734696296 33.14017975308642 7.101466720079011 11.83577829831111-7.101466720079011 14.202934734696296-21.304401454775306 7.101466720079011-33.14017975308642-40.24164647316543-61.54604792794074-61.54604792794074-134.92787415419258-61.54604792794074-210.6768568168296 0-215.41116839506174 175.16952192189632-390.580690316958 390.580690316958-390.580690316958S912.0493121788841 296.58883160493826 912.0493121788841 512s-175.16952192189632 390.580690316958-390.58068902241973 390.580690316958c-101.78769440110615 0-196.47392337667162-37.87449133131852-269.8557496029234-108.88916241572346-9.468623156464195-9.468623156464195-26.038713033007408-9.468623156464195-33.14017975308642 0z" fill="" p-id="11441"></path><path d="M481.22697538876054 677.700898765432c7.101466720079011 0 11.83577829831111-2.3671551418469137 16.57008987654321-7.101466720079011l248.55134814814815-248.55134814814815c9.468623156464195-9.468623156464195 9.468623156464195-23.67155659662222 0-33.14017975308642s-23.67155659662222-9.468623156464195-33.14017975308642 0l-231.98125827160493 231.98125827160493-153.8651191725827-153.865120467121c-9.468623156464195-9.468623156464195-23.67155659662222-9.468623156464195-33.14017975308642 0s-9.468623156464195 23.67155659662222 0 33.14017975308642l170.43520904912594 170.43521034366415c4.734311578232098 4.734311578232098 11.83577829831111 7.101466720079011 16.57008987654321 7.101466720079011z" fill="" p-id="11442"></path></svg>
                </div>
            </div>
        </div>


        <script id="payway_list_text" type="text/javascript">
            <%=pmlist["payway_lists"] %>
        </script>
        <script>

            var paylist = JSON.parse($('#payway_list_text').html());//, "支付宝二维码"
            $('#paytype_select').html('');
            //for (var i = 0; i < paylist.length; i++) {

            //    $('#paytype_select').append('<div class="paytype_list" style="cursor:pointer;" onclick="topay(\'' + paylist[i] + '\')">                <div style="margin-right: 10px;">                    <img src="../static/images/pay/' + paylist[i] + '.png" width="25" height="25">               </div>                <span style="font-size: 16px;margin-left: 6px;">' + paylist[i] + '</span>     ' + '           <div class="select_icon" style="margin-left: auto;">                    <svg t="" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="3794" width="25" height="25"><path d="M625.152 512l-271.36 271.36c-16.896 16.896-16.896 43.52 0 60.416 16.896 16.896 43.52 16.896 60.416 0l301.568-301.568c16.896-16.896 16.896-43.52 0-60.416L414.208 180.224c-16.896-16.896-43.52-16.896-60.416 0-16.896 16.896-16.896 43.52 0 60.416l271.36 271.36z" fill="#8a8a8a" p-id="3795"></path></svg>                </div>            </div>');

            //}


            for (var name in paylist) {
                var showName = name;
                switch (showName) {
                    case "支付宝转账":
                        showName = '支付宝大额充值';
                        break;
                    default:
                        break;

                }

                $('#paytype_select').append('<div class="paytype_list" style="cursor:pointer;" onclick="topay(\'' + name + '\')">                <div style="margin-right: 10px;">                    <img src="../static/images/pay/' + name + '.png?r=1" width="25" height="25">               </div>                <span style="font-size: 16px;margin-left: 6px;">' + showName + '</span>     ' + (paylist[name].reward != "" ? '<span style="font-size: 14px;background: linear-gradient(45deg, #fd1616, #b72e66);color: #ebeb35;font-weight: bold;padding: 1px 11px;margin-left: 5px;border-radius: 5px;">自动赠送' + paylist[name].reward + '%</span>' : '') + '           <div class="select_icon" style="margin-left: auto;">                    <svg t="" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="3794" width="25" height="25"><path d="M625.152 512l-271.36 271.36c-16.896 16.896-16.896 43.52 0 60.416 16.896 16.896 43.52 16.896 60.416 0l301.568-301.568c16.896-16.896 16.896-43.52 0-60.416L414.208 180.224c-16.896-16.896-43.52-16.896-60.416 0-16.896 16.896-16.896 43.52 0 60.416l271.36 271.36z" fill="#8a8a8a" p-id="3795"></path></svg>                </div>            </div>');

            }


            ////在线客服
            //$('#paytype_select').append('<div class="paytype_list" style="cursor:pointer;" onclick="topay(\'ser\')">                <div style="margin-right: 10px;display: flex;">                    <svg t="" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="" width="25" height="25"><path d="M741.33 429.67c-4.93-2.56-11.18-3.87-18.58-3.87h-35.6v56.8h35.6c7.35 0 13.55-1.34 18.43-3.99 4.71-2.55 8.11-5.82 10.39-10 2.41-4.41 3.58-9.26 3.58-14.81 0-5.51-1.16-10.27-3.53-14.53-2.25-4.02-5.61-7.16-10.29-9.6z" fill="#231815" p-id="8797"></path><path d="M981.9 313.47c-25.69-60.74-62.46-115.27-109.28-162.09C825.8 104.56 771.27 67.79 710.53 42.1 647.62 15.49 580.83 2 512 2c-68.83 0-135.62 13.49-198.53 40.1-60.74 25.69-115.27 62.46-162.09 109.28C104.56 198.2 67.79 252.73 42.1 313.47 15.49 376.38 2 443.17 2 512c0 68.83 13.49 135.62 40.1 198.53 25.69 60.74 62.46 115.27 109.28 162.09 46.82 46.82 101.36 83.59 162.09 109.28 62.91 26.61 129.7 40.1 198.53 40.1 68.83 0 135.62-13.49 198.53-40.1 60.74-25.69 115.27-62.46 162.09-109.28 46.82-46.82 83.59-101.36 109.28-162.09 26.61-62.91 40.1-129.7 40.1-198.53 0-68.83-13.49-135.62-40.1-198.53z m-505.77 85.41l-0.07 0.17-94.4 240.8c-7.95 19.88-23.19 30.35-44.11 30.35h-0.8c-20.92 0-36.16-10.47-44.08-30.29l-94.49-241.03c-5.85-15.73-1.87-26.17 2.49-32.17 6.23-8.57 16.22-12.92 29.69-12.92h1.2c12.77 0 30.01 5.12 39.69 29.51l0.05 0.13 65.86 172.7 65.9-172.82c9.67-24.39 26.93-29.51 39.7-29.51h1.2c13.13 0 23.02 4.31 29.4 12.8 4.5 6 8.64 16.48 2.77 32.28z m96.62 234.92c0 11.24-3.27 20.21-9.73 26.67-6.46 6.45-15.43 9.73-26.67 9.73h-0.8c-11.25 0-20.23-3.27-26.68-9.73-6.45-6.44-9.72-15.41-9.72-26.67V390.2c0-11.24 3.27-20.22 9.73-26.67 6.45-6.46 15.43-9.73 26.67-9.73h0.8c11.23 0 20.2 3.27 26.67 9.72 6.46 6.47 9.73 15.44 9.73 26.68v243.6z m242.22-131.92c-9.08 15.37-21.8 28.1-37.81 37.86-16.18 9.86-34.49 14.86-54.4 14.86h-35.6v79.2c0 11.24-3.27 20.21-9.73 26.67-6.46 6.45-15.43 9.73-26.67 9.73h-0.8c-11.25 0-20.22-3.27-26.67-9.73-6.45-6.44-9.72-15.41-9.72-26.67V406.6c0-16.55 4.69-29.63 13.93-38.87 7.86-7.86 18.49-12.42 31.67-13.61v-0.31h63.6c19.85 0 38.12 4.84 54.29 14.39 16.05 9.49 28.81 22.04 37.9 37.29 9.17 15.38 13.81 31.64 13.81 48.32-0.02 16.39-4.66 32.57-13.8 48.07z" fill="#231815" p-id="8798"></path></svg>              </div>                <span style="font-size: 16px;margin-left: 6px;font-weight: bold;color: #2a2b2c;">' + 'VIP人工充值  限额：50-200000' + '</span>     ' + '           <div class="select_icon" style="margin-left: auto;">                    <svg t="" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="3794" width="25" height="25"><path d="M625.152 512l-271.36 271.36c-16.896 16.896-16.896 43.52 0 60.416 16.896 16.896 43.52 16.896 60.416 0l301.568-301.568c16.896-16.896 16.896-43.52 0-60.416L414.208 180.224c-16.896-16.896-43.52-16.896-60.416 0-16.896 16.896-16.896 43.52 0 60.416l271.36 271.36z" fill="#8a8a8a" p-id="3795"></path></svg>                </div>            </div>');

        </script>




    </div>



    <script>
        var onlineUrl = '<%=uConfig.stcdata("kf_online") %>';
        var topay = function (paytype) {
            switch (paytype) {
                case "ser":
                    location.href = onlineUrl;
                    break;
                default:
                    location.href = "pay2mch.aspx?paytype=" + paytype;
                    break;

            }
        }
    </script>

</asp:Content>


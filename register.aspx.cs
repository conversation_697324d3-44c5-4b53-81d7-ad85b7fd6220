using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Data;

public partial class login : globalClass
{
    public DataTable dt = new DataTable();
    public Dictionary<string, object> pmlist = new Dictionary<string, object>();
    protected void Page_Load(object sender, EventArgs e)
    {
        dt = chelper.gdt("message_list");
        try
        {
            dt = selectDateTable(dt, " name='用户服务协议' ");
        }
        catch (Exception)
        {

        }



        DataTable temp_dt = selectDateTable(chelper.gdt("group_list"), "anony_visitor=1");
        pmlist["group_avatar"] = "";
        if (temp_dt.Rows.Count > 0)
        {
            pmlist["group_avatar"] = temp_dt.Rows[0]["avatar"] + "";
        }

    }
}
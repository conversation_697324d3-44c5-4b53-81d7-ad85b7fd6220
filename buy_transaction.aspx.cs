using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

public partial class order_list : baseClass
{
    public DataTable userdt = new DataTable();
    public Dictionary<string, object> pmlist = new Dictionary<string, object>();
    protected void Page_Load(object sender, EventArgs e)
    {
        pmlist["usertype"] = "";
        pmlist["upload_type"] = "img";
        DataTable dt = new DataTable();
        DataSet ds = new DataSet();
        string sql = string.Empty;
        List<SqlParameter> pams = new List<SqlParameter>();
        dbClass db = new dbClass();
        pams.Add(new SqlParameter("@id", Request.QueryString["id"] + ""));
        pams.Add(new SqlParameter("@userid", uConfig.p_uid));









        if (uConfig.stcdata("dating_mode") == "api")
        {
            try
            {
                string http_result = datingApi("dating_details", "id=" + Request.QueryString["id"]); 
                userdt = TransToDatatable(http_result);
            }
            catch (Exception)
            {

            }
            return;
        }







        string temp = " and userid=@userid ";
        if (Request.QueryString["key"] + "" == uConfig.stcdata("buylist_key") && uConfig.stcdata("buylist_key") != "")
        {
            temp = "";
        }
        sql = @"
select * from accounts with(nolock) where id=@userid
select * from buy_list with(nolock) where orderId=@id  " + temp;
        ds = db.getDataSet(sql, pams.ToArray());
        userdt = ds.Tables[1];



        dt = ds.Tables[0];
        Dictionary<string, object> temp_dic = chelper.getUserParames(uConfig.gd(dt, "usertype"), uConfig.gd(dt, "groupid"));
        pmlist["usertype"] = temp_dic["usertype"];
        pmlist["upload_type"] = uConfig.stcdata(pmlist["usertype"] + "_buymode");

    }
}
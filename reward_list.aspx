<%@ Page Title="" Language="C#" MasterPageFile="~/top.master" AutoEventWireup="true" CodeFile="reward_list.aspx.cs" Inherits="order_list" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="Server">
    <script>
        $(function () {
            popTitle('任务佣金', '');
        })
    </script>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="Server">

    <div style="background: linear-gradient(90deg,#f6e9f9,#fbfbfb); border-radius: 8px; padding: 12px; color: gray; font-size: 12px; margin: 18px 0; display: flex;">

        <div style="text-align: center; width: 50%;">
            <span style="color: #aaa;">我的奖励</span>
            <div style="margin: 10px 0;">
                <b style="color: #333; font-size: 18px;"><%=uConfig.gnumber(userdt,"reward_amount") %> <img src="/static/images/coin.png" alt="" height="15" width="15" style="margin-left: 5px;"></b>
            </div>
        </div>

        <div style="text-align: center; width: 50%;">
            <span style="color: #aaa;">团队返佣</span>
            <div style="margin: 10px 0;">
                <b style="color: #333; font-size: 18px;"><%=uConfig.gnumber(userdt,"brok_amount") %> <img src="/static/images/coin.png" alt="" height="15" width="15" style="margin-left: 5px;"></b>
            </div>
        </div>

    </div>


    <style>
        .select_tab {
            width: 50%;
            color: #000;
            box-sizing: border-box;
            padding: 10px 12px;
            text-align: center;
            font-weight: bold;
            font-size: 13px;
        }

            .select_tab.activity {
                background: #2B3DE9;
                color: #fff;
                border-radius: 30px;
                transition: all 0.2s;
            }
    </style>
<%--    <div style="display: flex; margin: 6px 0;">
        <div class="select_tab activity">我的奖励</div>
        <div class="select_tab">团队返佣</div>
    </div>

    <div style="background: #f1f1f1; border-radius: 8px; padding: 10px; color: gray; font-size: 12px; margin: 18px 0; display: flex;">
        <div style="margin-right: 10px;">
            <svg t="1692437259544" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="44726" width="16" height="16">
                <path d="M512.50142 958.397886c-119.320573 0-231.499491-46.465265-315.871087-130.837884C112.258737 743.188406 65.792449 631.010511 65.792449 511.688915c0-119.319549 46.466288-231.499491 130.837884-315.871087C281.002952 111.445208 393.180847 64.979944 512.50142 64.979944s231.499491 46.465265 315.871087 130.837884c84.372619 84.372619 130.837884 196.551538 130.837884 315.871087 0 119.321596-46.465265 231.499491-130.837884 315.871087C744.000911 911.932622 631.821993 958.397886 512.50142 958.397886zM512.50142 105.962334c-223.718271 0-405.726581 182.00831-405.726581 405.726581s182.00831 405.726581 405.726581 405.726581c223.718271 0 405.727605-182.00831 405.727605-405.726581S736.220714 105.962334 512.50142 105.962334z" fill="#8a8a8a" p-id="44727"></path><path d="M510.150886 775.953647c-18.107403 0-32.745798-14.678304-32.745798-32.785707L477.405087 452.191846c0-18.108426 14.638395-32.785707 32.745798-32.785707 18.107403 0 32.745798 14.678304 32.745798 32.785707l0 290.976094C542.896684 761.275343 528.258289 775.953647 510.150886 775.953647z" fill="#8a8a8a" p-id="44728"></path><path d="M511.357364 296.458969m-45.080731 0a44.054 44.054 0 1 0 90.161463 0 44.054 44.054 0 1 0-90.161463 0Z" fill="#8a8a8a" p-id="44729"></path></svg>
        </div>
        <div>我的奖励：您自己交易产生的基础奖励和额外加成奖励（您如果是合伙人下级或合伙人时，自己交易获得的额外奖励）</div>
    </div>--%>


    <div id="lists"></div>

    <div style="color: #aaa; text-align: center; font-size: 12px; margin-top: 20px;">没有更多数据了</div>

    <script>
        var show_list = function (type) {
            v3api("lists", { data: { page: 'brok_list', p: 0, limit: 10, type: type } }, function (e) {
                for (var i = 0; i < e.data.list.length; i++) {
                    var obj = e.data.list[i];
                    $('#lists').append('<div style="background: #fff; border-radius: 10px; padding: 18px 22px; margin-top: 20px; display: flex;">        <div>            <div style="font-size: 12px;display: flex;margin-bottom:10px;height: 22px;">                <svg t="1692435214533" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="43259" width="16" height="16">                    <path d="M426.7 589.3l394.1-394.1-27.6-131.3H626.5L263.9 426.5z" fill="#F44D4C" p-id="43260"></path><path d="M597.5 589.3L203.4 195.2l27.5-131.3h166.7l362.6 362.6z" fill="#FF5F5F" p-id="43261"></path><path d="M512.1 628.4m-331.2 0a331.2 331.2 0 1 0 662.4 0 331.2 331.2 0 1 0-662.4 0Z" fill="#FFF082" p-id="43262"></path><path d="M512.1 833.8c-113.3 0-205.4-92.1-205.4-205.4S398.8 423 512.1 423s205.4 92.1 205.4 205.4-92.2 205.4-205.4 205.4z" fill="#FFDD60" p-id="43263"></path><path d="M512.1 443c49.5 0 96.1 19.3 131.1 54.3s54.3 81.6 54.3 131.1-19.3 96.1-54.3 131.1-81.6 54.3-131.1 54.3S416 794.5 381 759.5s-54.3-81.6-54.3-131.1S346 532.3 381 497.3 462.6 443 512.1 443m0-40c-124.5 0-225.4 100.9-225.4 225.4s100.9 225.4 225.4 225.4 225.4-100.9 225.4-225.4S636.6 403 512.1 403z" fill="#694B4B" p-id="43264"></path></svg>&nbsp;                    <span>奖励金额</span>            </div>            <div style="margin-bottom:10px;">                <b style="color: #D1B16A; font-size: 20px;">' + obj.award_amount + ' <img src="/static/images/coin.png" alt="" height="15" width="15" style="margin-left: 5px;"></b>            </div>            <div>                <span style="color: gray; font-size: 12px;">' + obj.create_time + '</span>            </div>        </div>        <div style="margin-left: auto; text-align: center;">            <div style="margin-bottom:10px;height: 22px;">                <span style="background: #FFFBE7; color: #D1B16A; border-radius: 18px; padding: 2px 8px; font-size: 12px;">' + (obj.rank == 1 ? '下级佣金奖励' : obj.rank == 2 ? '下下级佣金奖励' : obj.rank == 3 ? '下下下级佣金奖励' : '') + '</span>            </div>            <div style="margin-bottom:10px;">                <b style="color: #333; font-size: 18px;">' + obj.ratio + '%</b>            </div>            <b style="color: #555; font-size: 12px;">奖励比例</b>        </div>    </div>');
                }

            })
        }
        show_list('my');

        $('.select_tab').on('click', function () {
            $(this).addClass("activity").siblings().removeClass("activity");

            var name = $(this).text();
            $('#lists').html('');
            if (name == "我的奖励") {
                show_list('my');
            } else {
                show_list('team');
            }
        })
    </script>
</asp:Content>


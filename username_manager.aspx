<%@ Page Title="" Language="C#" MasterPageFile="~/top.master" AutoEventWireup="true" CodeFile="username_manager.aspx.cs" Inherits="order_list" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="Server">
    <script>
        $(function () {
            popTitle('修改用户名', '');
        })
    </script>

    <style>
        .info_title {
            color: #000;
            font-size: 14px;
            font-weight: bold;
            display: flex;
            align-items: center;
            margin: 10px 0;
            margin-top: 18px;
        }

            .info_title i {
                display: inline-block;
                background: #2A3EE8;
                width: 6px;
                height: 6px;
                border-radius: 50%;
            }
    </style>
    <style>
        .select_tab {
            color: #000;
            box-sizing: border-box;
            padding: 12px 20px;
            text-align: center;
            font-weight: bold;
            font-size: 13px;
            background: #eee;
            border-radius: 12px;
            width: 50%;
            margin: 0 10px;
        }

            .select_tab.activity {
                background: #3c3351;
                color: #fff;
                transition: all 0.2s;
            }

        .theme-green .back-bar .selected-bar {
            background-color: #3838f5!important;
            background-image: linear-gradient(to bottom, #4747ff, #3838f5)!important;
        }

        .slider-container {
            margin-bottom: 38px;
            margin-top: 52px;
        }
    </style>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="Server">


    <div style="background: #fff; border-radius: 8px; padding: 18px; font-size: 14px;">


        <div style="position: relative; background: #f8f8f8; border-radius: 8px; margin-bottom: 20px;">
            <input style="border: 0px;font-size: 16px;padding: 16px;outline: none;width: 100%;box-sizing: border-box;background: #f2f2f2;border-radius: 8px;" id="username" placeholder="请输入用户名" value="<%=uConfig.gd(userdt,"username") %>">
        </div>

    </div>



    <div style="position: fixed; left: 0; bottom: 8px; width: 100%; padding: 5px 18px; box-sizing: border-box;">
        <a style="display: block; background: #3838f5; color: #fff; width: 100%; padding: 22px 16px; box-sizing: border-box; border-radius: 10px; cursor: pointer; text-align: center; font-size: 15px; margin-top: 13px;" onclick="set_username()">确认更改</a>
    </div>




    <script>



        var set_username = function () {
            v3api("set_username", {
                data: {
                    username: $('#username').val()
                }
            }, function (e) {
                tp(e.msg);
                setTimeout(function () {
                    history.go(-1);
                },200);
            })
        }
    </script>
</asp:Content>


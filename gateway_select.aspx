<%@ Page Title="" Language="C#" MasterPageFile="~/top.master" AutoEventWireup="true" CodeFile="gateway_select.aspx.cs" Inherits="order_list" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="Server">
    <script>
        $(function () {
            popTitle('选择支付方式', '', 'account.aspx');
        })
    </script>

    <style>
        .top-title {
            background: none;
        }
    </style>
    <style>
        body {
            background: linear-gradient(45deg, #F0F4F9, #F5F5F8);
            background-repeat: no-repeat;
            height: 100vh;
        }

        .select_tab {
            color: #000;
            box-sizing: border-box;
            padding: 12px 20px;
            text-align: center;
            font-weight: bold;
            font-size: 13px;
            background: #eee;
            border-radius: 12px;
            width: 50%;
            margin: 0 10px;
        }

            .select_tab.activity {
                background: #3c3351;
                color: #fff;
                transition: all 0.2s;
            }

        .theme-green .back-bar .selected-bar {
            background-color: #3838f5!important;
            background-image: linear-gradient(to bottom, #4747ff, #3838f5)!important;
        }

        .slider-container {
            margin-bottom: 38px;
            margin-top: 52px;
        }
    </style>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="Server">



    <style>
        .paytype_list {
            background: #fff;
            width: 100%;
            display: flex;
            padding: 15px 25px;
            box-sizing: border-box;
            align-items: center;
            border-radius: 3px;
            margin-bottom: 2px;
            font-size: 13px;
        }

        
    </style>

    <div style="margin-bottom: 30px;">

        <div style="margin-top: 30px; margin-bottom: 10px;">
            充值方式
        </div>

        <div id="paytype_select">




            <div class="paytype_list" onclick="select_pay(this,'美宜佳')">
                <div style="margin-right: 10px;">
                    <svg t="1695126716921" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="9034" width="32" height="32">
                        <path d="M894.509511 249.605689H330.752a37.660444 37.660444 0 0 0-37.546667 37.762844v342.448356a37.660444 37.660444 0 0 0 37.546667 37.762844h563.757511a37.660444 37.660444 0 0 0 37.558045-37.762844V287.368533a37.660444 37.660444 0 0 0-37.558045-37.762844z" fill="#CCCCCC" p-id="9035"></path><path d="M293.216711 333.585067H932.067556v97.655466H293.216711z" fill="#4D4D4D" p-id="9036"></path><path d="M688.685511 388.278044H124.928a37.660444 37.660444 0 0 0-37.546667 37.762845v342.448355a37.660444 37.660444 0 0 0 37.546667 37.762845h563.757511a37.660444 37.660444 0 0 0 37.546667-37.762845V426.040889a37.660444 37.660444 0 0 0-37.546667-37.762845z" fill="#FFCA6C" p-id="9037"></path><path d="M87.381333 472.257422h638.850845v97.655467H87.381333z" fill="#4D4D4D" p-id="9038"></path><path d="M213.595022 692.974933a58.595556 58.254222 90 1 0 116.508445 0 58.595556 58.254222 90 1 0-116.508445 0Z" fill="#47A7DD" p-id="9039"></path><path d="M155.3408 692.974933a58.595556 58.254222 90 1 0 116.508444 0 58.595556 58.254222 90 1 0-116.508444 0Z" fill="#FC583D" p-id="9040"></path><path d="M894.509511 234.951111H720.406756c-8.044089 0-14.563556 6.5536-14.563556 14.6432s6.519467 14.654578 14.563556 14.654578h174.102755c12.686222 0 22.994489 10.376533 22.994489 23.131022v31.561956H307.768889V287.379911c0-12.754489 10.308267-23.131022 22.994489-23.131022H671.857778c8.044089 0 14.552178-6.564978 14.552178-14.654578S679.913244 234.951111 671.869156 234.951111h-341.105778c-28.740267 0-52.1216 23.517867-52.1216 52.417422v86.254934H124.928c-28.728889 0-52.110222 23.517867-52.110222 52.417422V663.665778c0 8.100978 6.519467 14.654578 14.563555 14.654578 8.044089 0 14.563556-6.564978 14.563556-14.654578v-79.086934h609.723733v183.9104c0 12.743111-10.308267 23.108267-22.983111 23.108267H124.928a23.074133 23.074133 0 0 1-22.983111-23.108267v-55.990044c0-8.0896-6.519467-14.6432-14.563556-14.6432-8.044089 0-14.563556 6.5536-14.563555 14.6432v55.990044c0 28.899556 23.381333 52.406044 52.110222 52.406045h563.757511c28.728889 0 52.110222-23.506489 52.110222-52.406045V426.040889c0-28.899556-23.381333-52.417422-52.110222-52.417422H307.780267v-25.383823h609.735111v68.357689H772.846933c-8.044089 0-14.563556 6.5536-14.563555 14.6432s6.519467 14.654578 14.563555 14.654578h144.668445v183.9104a23.096889 23.096889 0 0 1-22.994489 23.131022H774.781156c-8.044089 0-14.552178 6.5536-14.552178 14.6432s6.508089 14.6432 14.552178 14.6432h119.728355c28.728889 0 52.1216-23.506489 52.1216-52.417422V287.379911C946.631111 258.468978 923.249778 234.951111 894.509511 234.951111z m-182.840889 191.089778v31.573333H178.642489c-8.044089 0-14.563556 6.5536-14.563556 14.6432s6.519467 14.654578 14.563556 14.654578h533.026133v68.357689H101.944889v-68.357689h28.16c8.044089 0 14.563556-6.564978 14.563555-14.654578s-6.519467-14.6432-14.563555-14.6432H101.944889v-31.573333c0-12.743111 10.308267-23.119644 22.983111-23.119645h563.757511a23.096889 23.096889 0 0 1 22.983111 23.119645z" fill="" p-id="9041"></path><path d="M242.744889 760.069689a72.100978 72.100978 0 0 0 29.104355 6.155378c40.152178 0 72.817778-32.8704 72.817778-73.250134 0-40.402489-32.6656-73.250133-72.817778-73.250133-10.069333 0-19.979378 2.127644-29.104355 6.132622a72.078222 72.078222 0 0 0-29.149867-6.132622c-40.152178 0-72.817778 32.847644-72.817778 73.250133 0 40.379733 32.6656 73.250133 72.817778 73.250134 10.365156 0 20.218311-2.218667 29.149867-6.155378z m72.795022-67.094756c0 24.223289-19.603911 43.9296-43.690667 43.9296h-0.034133a73.056711 73.056711 0 0 0 14.609067-43.9296 73.079467 73.079467 0 0 0-14.609067-43.952355h0.034133c24.098133 0 43.690667 19.706311 43.690667 43.952355z m-145.624178 0c0-24.246044 19.592533-43.952356 43.690667-43.952355 24.086756 0 43.690667 19.706311 43.690667 43.952355 0 24.223289-19.603911 43.9296-43.690667 43.9296-24.098133 0.011378-43.690667-19.706311-43.690667-43.9296zM655.633067 647.5776c8.032711 0 14.563556-6.5536 14.563555-14.6432s-6.530844-14.6432-14.563555-14.6432H440.103822c-8.044089 0-14.563556 6.5536-14.563555 14.6432s6.519467 14.6432 14.563555 14.6432h215.529245z" fill="" p-id="9042"></path></svg>
                </div>
                <span>支付</span>
                <div class="select_icon" style="margin-left: auto;">
                    <svg t="1695127337616" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="11440" width="25" height="25">
                        <path d="M218.47269380045435 793.6915279012346c-9.468623156464195 9.468623156464195-9.468623156464195 23.67155659662222 0 33.14017975308642 82.85044938271605 78.11613780448396 189.37245536205432 123.09209585588148 302.9959293560098 123.09209585588148 241.4498814280691 0 437.92380351020245-196.47392337667162 437.92380351020245-437.92380351020245S762.9185032899951 74.07619648979755 521.4686231564642 74.07619648979755 83.54481835172345 270.55011857193097 83.54481835172345 512c0 82.85044938271605 23.67155659662222 165.7008987654321 68.647515942558 236.71556984983704 7.101466720079011 11.83577829831111 21.304401454775306 14.202934734696296 33.14017975308642 7.101466720079011 11.83577829831111-7.101466720079011 14.202934734696296-21.304401454775306 7.101466720079011-33.14017975308642-40.24164647316543-61.54604792794074-61.54604792794074-134.92787415419258-61.54604792794074-210.6768568168296 0-215.41116839506174 175.16952192189632-390.580690316958 390.580690316958-390.580690316958S912.0493121788841 296.58883160493826 912.0493121788841 512s-175.16952192189632 390.580690316958-390.58068902241973 390.580690316958c-101.78769440110615 0-196.47392337667162-37.87449133131852-269.8557496029234-108.88916241572346-9.468623156464195-9.468623156464195-26.038713033007408-9.468623156464195-33.14017975308642 0z" fill="" p-id="11441"></path><path d="M481.22697538876054 677.700898765432c7.101466720079011 0 11.83577829831111-2.3671551418469137 16.57008987654321-7.101466720079011l248.55134814814815-248.55134814814815c9.468623156464195-9.468623156464195 9.468623156464195-23.67155659662222 0-33.14017975308642s-23.67155659662222-9.468623156464195-33.14017975308642 0l-231.98125827160493 231.98125827160493-153.8651191725827-153.865120467121c-9.468623156464195-9.468623156464195-23.67155659662222-9.468623156464195-33.14017975308642 0s-9.468623156464195 23.67155659662222 0 33.14017975308642l170.43520904912594 170.43521034366415c4.734311578232098 4.734311578232098 11.83577829831111 7.101466720079011 16.57008987654321 7.101466720079011z" fill="" p-id="11442"></path></svg>
                </div>
            </div>
        </div>


        <script id="myj_channel_list" type="text/javascript"><%=uConfig.stcdata("myj_channel_list")  %></script>
        <script id="paytotal" type="text/javascript"><%=ToJson(payTotal)  %></script>
        <script>
            var pts = JSON.parse($('#paytotal').html());

            var current_paytype = "";
            var current_channelId = "";
            var current_rules = "";
            var select_pay = function (obj, name, rules) {

                if (name.indexOf("channel_") != -1) {
                    current_channelId = name.replace(/channel_/g, "");
                    var lst = current_channelId.split('_');

                    current_paytype = lst[0];
                    current_channelId = lst[1];

                } else {
                    current_paytype = name;
                }

                if (rules != "") {
                    current_rules = rules;
                }

                console.log('name', name);
                switch (name) {
                    case "美宜佳":
                        current_channelId = "";
                        $('#paytype_select').html('');
                        var lst = $('#myj_channel_list').html().split('\n');

                        for (var i = 0; i < lst.length; i++) {
                            var temp_lst = lst[i].split('----');
                            console.log('temp_lst', temp_lst);

                            if (temp_lst.length >= 4) {
                                var is_next = false;
                                for (var c = 0; c < pts.length; c++) {
                                    if (temp_lst[1] == pts[c].channel_id) {
                                        if (temp_lst[1] == pts[c].channel_id) {
                                            if (pts[c].total_number >= temp_lst[4]) {
                                                is_next = true;
                                                break;
                                            }
                                        }
                                    }
                                }
                                if (is_next) {
                                    continue;
                                }
                            }


                            var money_limit = temp_lst[3];

                            if (money_limit.indexOf('[') != -1) {
                                var temp_array = money_limit.split('[');
                                money_limit = temp_array[0];
                                money_limit = temp_array[1].replace(/]/g, "");
                            }

                            $('#paytype_select').append('<div class="paytype_list" onclick="select_pay(this,\'channel_' + temp_lst[0] + '_' + temp_lst[1] + '\',\'' + temp_lst[3] + '\')">                <div style="margin-right: 10px;">                    <svg t="1695129657040" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="12826" width="25" height="25"><path d="M860.16 0C950.272 0 1024 73.889684 1024 164.163368v531.509895s-32.768-4.122947-180.224-53.355789c-40.96-14.362947-96.256-34.896842-157.696-57.478737 36.864-63.595789 65.536-137.485474 86.016-215.444211h-202.752v-71.841684h247.808V256.512h-247.808V135.437474h-100.352c-18.432 0-18.432 18.458947-18.432 18.458947v104.663579H200.704v41.040842h249.856v69.793684H243.712v41.013895H645.12c-14.336 51.307789-34.816 98.519579-57.344 141.608421-129.024-43.115789-268.288-77.985684-356.352-55.403789-55.296 14.362947-92.16 38.992842-112.64 63.595789-96.256 116.978526-26.624 295.504842 176.128 295.504842 120.832 0 237.568-67.718737 327.68-178.526316C757.76 742.858105 1024 853.692632 1024 853.692632v6.144C1024 950.110316 950.272 1024 860.16 1024H163.84C73.728 1024 0 950.137263 0 859.836632V164.163368C0 73.889684 73.728 0 163.84 0h696.32zM268.126316 553.121684c93.049263-10.374737 180.062316 26.974316 283.270737 78.874948-74.886737 95.501474-165.941895 155.701895-256.970106 155.701894-157.830737 0-204.368842-126.652632-125.466947-197.200842 26.300632-22.851368 72.838737-35.301053 99.166316-37.376z" fill="#00A0EA" p-id="12827"></path></svg>                </div>                <span>' + temp_lst[2] + '</span>     ' + '<span style="color:gray;max-width: 118px;text-wrap: nowrap;overflow: hidden;text-overflow: ellipsis;">（限额：' + money_limit + '）</span>' + '           <div class="select_icon" style="margin-left: auto;">                    <svg t="1695127337616" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="11440" width="25" height="25">                        <path d="M218.47269380045435 793.6915279012346c-9.468623156464195 9.468623156464195-9.468623156464195 23.67155659662222 0 33.14017975308642 82.85044938271605 78.11613780448396 189.37245536205432 123.09209585588148 302.9959293560098 123.09209585588148 241.4498814280691 0 437.92380351020245-196.47392337667162 437.92380351020245-437.92380351020245S762.9185032899951 74.07619648979755 521.4686231564642 74.07619648979755 83.54481835172345 270.55011857193097 83.54481835172345 512c0 82.85044938271605 23.67155659662222 165.7008987654321 68.647515942558 236.71556984983704 7.101466720079011 11.83577829831111 21.304401454775306 14.202934734696296 33.14017975308642 7.101466720079011 11.83577829831111-7.101466720079011 14.202934734696296-21.304401454775306 7.101466720079011-33.14017975308642-40.24164647316543-61.54604792794074-61.54604792794074-134.92787415419258-61.54604792794074-210.6768568168296 0-215.41116839506174 175.16952192189632-390.580690316958 390.580690316958-390.580690316958S912.0493121788841 296.58883160493826 912.0493121788841 512s-175.16952192189632 390.580690316958-390.58068902241973 390.580690316958c-101.78769440110615 0-196.47392337667162-37.87449133131852-269.8557496029234-108.88916241572346-9.468623156464195-9.468623156464195-26.038713033007408-9.468623156464195-33.14017975308642 0z" fill="" p-id="11441"></path><path d="M481.22697538876054 677.700898765432c7.101466720079011 0 11.83577829831111-2.3671551418469137 16.57008987654321-7.101466720079011l248.55134814814815-248.55134814814815c9.468623156464195-9.468623156464195 9.468623156464195-23.67155659662222 0-33.14017975308642s-23.67155659662222-9.468623156464195-33.14017975308642 0l-231.98125827160493 231.98125827160493-153.8651191725827-153.865120467121c-9.468623156464195-9.468623156464195-23.67155659662222-9.468623156464195-33.14017975308642 0s-9.468623156464195 23.67155659662222 0 33.14017975308642l170.43520904912594 170.43521034366415c4.734311578232098 4.734311578232098 11.83577829831111 7.101466720079011 16.57008987654321 7.101466720079011z" fill="" p-id="11442"></path></svg>                </div>            </div>')
                        }

                        break;
                    default:
                        if (current_channelId == "") {
                            current_channelId = "-";
                        }
                        $(obj).siblings().find(".select_icon").html('<svg t="1695127337616" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="11440" width="25" height="25"><path d="M218.47269380045435 793.6915279012346c-9.468623156464195 9.468623156464195-9.468623156464195 23.67155659662222 0 33.14017975308642 82.85044938271605 78.11613780448396 189.37245536205432 123.09209585588148 302.9959293560098 123.09209585588148 241.4498814280691 0 437.92380351020245-196.47392337667162 437.92380351020245-437.92380351020245S762.9185032899951 74.07619648979755 521.4686231564642 74.07619648979755 83.54481835172345 270.55011857193097 83.54481835172345 512c0 82.85044938271605 23.67155659662222 165.7008987654321 68.647515942558 236.71556984983704 7.101466720079011 11.83577829831111 21.304401454775306 14.202934734696296 33.14017975308642 7.101466720079011 11.83577829831111-7.101466720079011 14.202934734696296-21.304401454775306 7.101466720079011-33.14017975308642-40.24164647316543-61.54604792794074-61.54604792794074-134.92787415419258-61.54604792794074-210.6768568168296 0-215.41116839506174 175.16952192189632-390.580690316958 390.580690316958-390.580690316958S912.0493121788841 296.58883160493826 912.0493121788841 512s-175.16952192189632 390.580690316958-390.58068902241973 390.580690316958c-101.78769440110615 0-196.47392337667162-37.87449133131852-269.8557496029234-108.88916241572346-9.468623156464195-9.468623156464195-26.038713033007408-9.468623156464195-33.14017975308642 0z" fill="" p-id="11441"></path><path d="M481.22697538876054 677.700898765432c7.101466720079011 0 11.83577829831111-2.3671551418469137 16.57008987654321-7.101466720079011l248.55134814814815-248.55134814814815c9.468623156464195-9.468623156464195 9.468623156464195-23.67155659662222 0-33.14017975308642s-23.67155659662222-9.468623156464195-33.14017975308642 0l-231.98125827160493 231.98125827160493-153.8651191725827-153.865120467121c-9.468623156464195-9.468623156464195-23.67155659662222-9.468623156464195-33.14017975308642 0s-9.468623156464195 23.67155659662222 0 33.14017975308642l170.43520904912594 170.43521034366415c4.734311578232098 4.734311578232098 11.83577829831111 7.101466720079011 16.57008987654321 7.101466720079011z" fill="" p-id="11442"></path></svg>');
                        $(obj).find(".select_icon").html('<svg t="1695127440182" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="11787" width="25" height="25"><path d="M511.984 64C264.976 64 64 264.96 64 512.016 64 759.024 264.976 960 511.984 960 759.056 960 960 759.024 960 512.016 960 264.944 759.024 64 511.984 64z" fill="#FFBD27" p-id="11788"></path><path d="M456.624 685.696a36.8 36.8 0 0 1-26.112-10.848l-147.712-148.32c-14.4-14.464-14.4-37.968 0-52.432a36.816 36.816 0 0 1 52.224 0l121.6 122.08 232.352-233.28a36.752 36.752 0 0 1 52.224 0c14.4 14.464 14.4 37.92 0 52.4L482.736 674.848a36.864 36.864 0 0 1-26.112 10.848z" fill="#333333" p-id="11789"></path></svg>');
                        break;

                }
            }

            select_pay(null, '美宜佳')
        </script>




    </div>



    <a style="display: block; background: #3838f5; color: #fff; width: 100%; padding: 16px; box-sizing: border-box; border-radius: 10px; cursor: pointer; text-align: center; font-size: 14px; margin-top: 13px; text-decoration: none;" onclick="new_advert()" class="create_button">下一步</a>



    <script>
        var new_advert = function () {

            if (current_paytype == "" || current_channelId == "") {
                tp('请选择支付方式');
                return;
            }

            location.href = "gateway_pay.aspx?paytype=" + current_paytype + "&channel_id=" + current_channelId + "&rules=" + current_rules;
        }
    </script>

</asp:Content>


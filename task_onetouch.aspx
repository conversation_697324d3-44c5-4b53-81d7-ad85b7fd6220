<%@ Page Title="" Language="C#" MasterPageFile="~/top.master" AutoEventWireup="true" CodeFile="task_onetouch.aspx.cs" Inherits="agent_tasks" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="Server">
    <script>
        $(function () {
            popTitle('一键任务', '<a style="position: absolute;right: 10px;height: 100%;top: 0;display: flex;align-items: center;justify-content: center;color: #2A2B2C;text-decoration: none;"  href="task_onetouch_records.aspx">任务记录</a>', 'order_new.aspx');
        })
    </script>

    <link href="../slayer/layer.css" rel="stylesheet" />
    <script src="../slayer/layer.js"></script>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="Server">

    <div style="font-size: 13px; font-weight: bold; color: #3a5b5c;">


        <style>
            .progress-bar {
                width: 100%;
                background-color: #EBEEF5;
                border-radius: 13px;
                overflow: hidden;
                position: relative;
                height: 30px;
            }

            .progress-bar-inner {
                height: 100%;
                background-color: #E6A23D;
                width: 0;
                position: absolute;
                transition: width 0.5s ease;
                border-radius: 13px;
            }

            .progress-value {
                position: absolute;
                right: 10px;
                top: 5px;
                color: white;
                font-weight: bold;
            }

            #top-progress > div {
                flex-shrink: 0;
            }
        </style>
        <%if (pmlist["typename"] + "" == "user")
          {
        %>

        <div id="top-progress" style="display: flex; align-items: center; margin-bottom: 10px; display: none;">

            <div style="width: 80px; color: #913F4D; font-weight: bold;">当前成功率：</div>
            <div style="flex-grow: 1;">
                <div class="progress-bar">
                    <div class="progress-bar-inner">
                        <span class="progress-value">0%</span>
                    </div>
                </div>
            </div>
            <div style="width: 80px; text-align: center;">
                <a style="color: #913F4D; font-weight: bold; cursor: pointer;" onclick="$('#pop-cpt').show();">我要提升</a>
            </div>
        </div>

        <%
          } %>

        <script>
            function updateProgress(minvalue, maxvalue) {
                var $progressBarInner = $('.progress-bar-inner');
                var $progressValue = $('.progress-value');
                $progressBarInner.css('width', maxvalue + '%');
                $progressValue.text(minvalue + '%~' + maxvalue + '%');
            }

        </script>

        <div style="display: flex;">
            <div style="width: 50%;">最低任务金额：<span class="task_minAmount">-</span></div>
            <div style="width: 50%;">最高交易金额：<span class="task_maxAmount">-</span></div>
        </div>
        <div style="display: flex; margin-top: 15px;">
            <div style="width: 50%;">任务周期：<span class="task_days">-</span>天</div>
            <%--<div style="width: 50%;">当前等级：<span class="" style="font-size: 12px; background: linear-gradient(117deg, #e1ab48, #898071); color: #eee; padding: 2px 8px; border-radius: 4px; margin-left: 5px;"><%=pmlist["level_name"] %></span></div>--%>
            <%--<div style="width: 50%;">房主佣金：<span class="task_serve_fee">-</span>%</div>--%>
            <div style="width: 50%;">
                <div>最低<span class="task_minAmount">-</span>金额(<span style="color: #9c9f22;">必须是10的倍数</span>)</div>
            </div>
        </div>
        <%--<div style="display: flex; margin-top: 12px;">
            <div style="width: 50%;">
                <div>最低<span class="task_minAmount">-</span>金额(<span style="color: #9c9f22;">必须是10的倍数</span>)</div>
            </div>
            <div style="width: 50%;">任务佣金：<span class=""><%=pmlist["level_rate"] %></span>%</div>
        </div>--%>

        <div>
            <div style="margin-top: 10px; position: relative;">
                <input id="task_amount" style="width: 100%; border: 0px; border-bottom: 1px solid #ddd; padding: 10px 3px; outline: none; font-size: 16px; color: #2a2b2c; padding-right: 24px; box-sizing: border-box;" placeholder="请输入最低1000金额（必须是10的倍数）">
                <img src="/static/images/coin.png" alt="" height="15" width="15" style="margin-left: 5px; position: absolute; top: 9px; right: 8px;">
            </div>
        </div>



        <!-- 任务描述 -->
        <div style="background: #F6EFED; color: #a54646; padding: 8px 12px; border-radius: 5px; border: 2px solid #E7D1D5; margin-top: 12px; font-weight: 500;">
            <div style="font-size: 15px; margin-bottom: 7px;">
                <b>任务描述：</b>
            </div>
            <div>一键任务统一性强，便于优先分配任务，提升任务成功率，一键任务期间已无需任何操作，系统自动分配任务，解封双手避免忘记！</div>
        </div>



        <!-- 申请周期 -->
        <div style="margin-top: 28px;">
            <div style="font-size: 15px; margin-bottom: 14px;">
                <b>申请周期<span style="color: #2a2b2c;">【正在进行中：<span id="used_amount"><%=Convert.ToDouble(pmlist["used_amount"]+"").ToString("0.00") %></span>】【剩余额度：<span id="red_amount">0</span>】</span></b>
            </div>


            <div style="display: flex; flex-wrap: wrap;">
                <style>
                    .select_items {
                        width: 33.333%;
                        box-sizing: border-box;
                    }

                    .item-container {
                        box-sizing: border-box;
                        background: #fff;
                        color: #2a2b2c;
                        border: 1px solid #eee;
                        padding: 18px;
                        border-radius: 12px;
                        margin: 6px;
                        cursor: pointer;
                        position: relative;
                    }

                    .select_items.active .item-container {
                        background: #fe3b00;
                        color: #eee;
                    }

                    .action_button {
                        background: #fe3b00;
                        color: #fff;
                        width: 100%;
                        padding: 16px;
                        box-sizing: border-box;
                        border-radius: 5px;
                        cursor: pointer;
                        text-align: center;
                        font-size: 14px;
                        margin-top: 13px;
                        display: flex;
                        justify-content: center;
                        align-items: center;
                    }

                    .tt-table {
                        font-weight: bold;
                        border-collapse: collapse;
                    }

                        .tt-table th {
                            text-align: center;
                            color: #fff;
                            font-size: 16px;
                            font-weight: 500;
                            padding: 5px 0;
                            /* color: #000; */
                            border: solid 1px #ffd1bf;
                            background: #DF5054;
                            color: #2a2b2c2;
                            text-shadow: 2px 3px 2px #9d978a;
                        }

                        .tt-table.table-gd th {
                            border: solid 1px #efbeb3;
                            background: #fe3b00;
                        }

                        .tt-table td {
                            text-align: center;
                            border: solid 1px #999;
                            font-size: 16px;
                            line-height: 22px;
                            padding: 5px 0;
                            border: solid 1px #FE6225;
                            color: #2a2b2c;
                            text-shadow: 2px 3px 2px #5a5b5c40;
                            /*background: #FFE4E1;*/
                        }


                        .tt-table.table-gd td {
                            color: #3a3b3c;
                            border: solid 1px #efbeb3;
                            background: #FDE5E0;
                        }

                        .tt-table tr.active {
                            background: #e1aeae36;
                        }

                        .tt-table.table-gd tr.active {
                            background: #aedce136;
                        }

                    .task_point {
                        background: #ffded7;
                        color: #c7694c;
                        height: 18px;
                        line-height: 18px;
                        position: absolute;
                        border-radius: 6px;
                        text-align: center;
                        top: -6px;
                        right: -6px;
                        font-size: 12px;
                        padding: 0 6px;
                    }

                        .task_point.point2 {
                            background: #feffd7;
                            color: #c7694c;
                        }

                        .task_point.nothing {
                            background: #e9e9e9;
                            color: gray;
                        }
                </style>

                <asp:Repeater ID="task_onetouch_listbox" runat="server">
                    <ItemTemplate>

                        <div class="select_items " itemid="<%#Eval("id") %>" minamount="<%#FormatNumber(Convert.ToDouble(Eval("minAmount")).ToString("0")) %>" maxamount="<%#FormatNumber(Eval("maxAmount")+"") %>" maxamount_value="<%#Eval("maxAmount")+""%>" days="<%#Eval("days") %>" serve_fee="<%#Eval("serve_fee") %>">
                            <div class="item-container">
                                <div style="margin-bottom: 5px;">
                                    任务<%#Eval("days") %>天
                                </div>
                                <div><%#FormatNumber(Convert.ToDouble(Eval("minAmount")).ToString("0")) %>-<%#FormatNumber(Convert.ToDouble(Eval("maxAmount")).ToString("0")) %></div>

                                <div style="<%#Eval("limitNumber").ToString() == "0" ? "display:none;": "" %>" itemid="<%#Eval("id") %>" limitnumber="<%#Eval("limitNumber") %>" class="task_point">剩余<%#Eval("limitNumber") %>次</div>


                                <div style="<%#Eval("isexp").ToString() == "1" ? "": "display:none;" %>" class="task_point point2">体验金</div>
                            </div>
                        </div>
                    </ItemTemplate>
                </asp:Repeater>

                <%--<div class="select_items active">
                    <div style="margin-bottom: 5px;">
                        任务60天
                    </div>
                    <div>1000-10万</div>
                </div>
                <div class="select_items ">
                    <div style="margin-bottom: 5px;">
                        任务60天
                    </div>
                    <div>1000-10万</div>
                </div>--%>
                <script>
                    $(function () {
                        $('.select_items').eq(0).click();
                    })

                    $('.select_items').on('click', function () {
                        $(this).addClass('active').siblings().removeClass('active');
                        maxAmount_value = $(this).attr('maxAmount_value');
                        $('#red_amount').html((parseFloat(maxAmount_value) - parseFloat($('#used_amount').html())).toFixed(2));


                        $('.task_minAmount').html($(this).attr('minAmount'));
                        $('.task_maxAmount').html($(this).attr('maxAmount'));
                        $('.task_days').html($(this).attr('days'));
                        $('.task_serve_fee').html($(this).attr('serve_fee'));

                        $('#task_amount').attr('placeholder', '请输入最低' + $(this).attr('minAmount') + '金额（必须是10的倍数）');

                    })

                    $('#task_amount').on('keyup', function () {
                        var v = $(this).val();
                        if (isNaN(v)) {
                            v = "";
                        }
                        $(this).val(v);
                    });

                    $('#task_amount').on('change', function () {
                        var v = $(this).val();
                        $(this).val(roundToNearestHundred(v));
                    });

                    function roundToNearestHundred(input) {
                        var number = parseInt(input);
                        if (isNaN(number)) {
                            return "";
                        }
                        var remainder = number % 10;
                        if (remainder === 0) {
                            return number;
                        }
                        var roundedNumber = number - remainder;
                        return roundedNumber;
                    }
                </script>

            </div>



            <a onclick="task_onetouch()" id="agetn_button" class="action_button" style="margin-top: 18px;">一键任务【自动挂机赚钱】</a>

            <%--<a onclick="javascript:location.href='onetouch_balance.aspx'" class="action_button " style="margin-top: 8px; text-shadow: 5px 5px 5px #8d8b4b; background: #46b75a; color: #e3fbdb; cursor: pointer;">
                <label>一键任务【游戏转入】</label></a>--%>

            <%--<a onclick="javascript:location.href='task_rules.aspx?type=jrhhr'" class="action_button " style="margin-top: 8px; cursor: pointer;">
                <label>加入合伙人</label></a>--%>





            <div style="border-left: 3px solid #fe3b00; display: flex; align-items: center; justify-content: center; color: #2a2b2c; width: 99px; margin-bottom: 10px; margin-top: 28px; padding: 4px 0; font-weight: bold; font-size: 13px;">
                一键任务说明
           
            </div>

            <table width="100%" class="tt-table table-gd" style="margin-bottom: 30px;">
                <tbody id="levels_gd">
                    <tr class="firstRow">
                        <th>资金门槛</th>
                        <th>任务周期</th>
                        <th>佣金比例</th>
                        <th>房主佣金</th>
                    </tr>
                    <asp:Repeater ID="task_onetouch_items" runat="server">
                        <ItemTemplate>
                            <tr>
                                <td><%#FormatNumber(Convert.ToDouble(Eval("minAmount")).ToString("0")) %>-<%#FormatNumber(Eval("maxAmount")+"") %></td>
                                <td><%#Eval("days") %></td>
                                <td><%#Eval("rate_award") %>%</td>
                                <td><%#Eval("serve_fee") %>%</td>
                            </tr>
                        </ItemTemplate>
                    </asp:Repeater>
                </tbody>
            </table>






            <style>
                .tt-2table {
                    font-weight: bold;
                    border-collapse: collapse;
                }

                    .tt-2table th {
                        text-align: center;
                        color: #fff;
                        border: solid 1px #999;
                        /* background: #0082f3; */
                        font-size: 18px;
                        font-weight: 500;
                        padding: 5px 0;
                        /* color: #000; */
                        border: solid 1px #ffd1bf;
                        background: #DF5054;
                        color: #fff;
                        text-shadow: 2px 3px 2px #9d978a;
                    }

                    .tt-2table.table-gd th {
                        border: solid 1px #72c4cc;
                        background: #24b7c5;
                    }

                    .tt-2table td {
                        text-align: center;
                        border: solid 1px #999;
                        font-size: 16px;
                        line-height: 22px;
                        padding: 5px 0;
                        border: solid 1px #FE6225;
                        color: #2a2b2c;
                        text-shadow: 2px 3px 2px #5a5b5c40;
                        /*background: #FFE4E1;*/
                    }


                    .tt-2table.table-gd td {
                        border: solid 1px #72c4cc;
                    }

                    .tt-2table tr.active {
                        background: #e1aeae36;
                    }

                    .tt-2table.table-gd tr.active {
                        background: #aedce136;
                    }
            </style>

            <%--<div style="border-left: 3px solid #fe3b00; display: flex; align-items: center; justify-content: center; color: #2a2b2c; width: 130px; margin-bottom: 10px; margin-top: 28px; padding: 4px 0; font-weight: bold; font-size: 13px;">
                一键任务佣金说明
           
            </div>
            <table width="100%" class="tt-2table" style="">
                <tbody id="levels">
                    <tr class="firstRow">
                        <th>合伙人等级</th>
                        <th>团队有效人数</th>
                        <th>佣金比例</th>
                    </tr>
                    <asp:Repeater ID="levels_list" runat="server">
                        <ItemTemplate>
                            <tr minnumber="<%#Eval("minNumber") %>" rate_award="<%#Eval("rate_award") %>">
                                <td><%#Eval("name") %></td>
                                <td><%#Eval("need_text") %>人</td>
                                <td><%#Eval("rate_award") %>%</td>
                            </tr>
                        </ItemTemplate>
                    </asp:Repeater>
                </tbody>
            </table>--%>
        </div>
    </div>


    <script>
        //[ 初始参数 ]
        var valid_user_number = '<%=pmlist["valid_user_number"] %>';
        var current_rate_award = $('#levels tr[minNumber]').eq(0).attr("rate_award");
        var current_rate_award_gd = $('#levels_gd tr[minNumber]').eq(0).attr("rate_award");
        var maxNumber = 0;
        $('#levels tr[minNumber]').each(function () {
            var v = parseInt($(this).attr("minNumber"));
            var rate_award = $(this).attr("rate_award");
            if (parseInt(valid_user_number) >= v) {
                if (v > maxNumber) {
                    maxNumber = v;
                    current_rate_award = rate_award;
                    current_rate_award_gd = $('#levels_gd tr[minNumber="' + maxNumber + '"]').attr("rate_award");
                    console.log('maxNumber', maxNumber, rate_award);
                }
            }
        })

        //$('tr[minNumber="' + maxNumber + '"]').css("background", "#e1aeae36");
        $('#levels tr[minNumber="' + maxNumber + '"]').addClass('active').find('td').eq(0).prepend('<svg t="" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="5525" width="19" height="19" style="position:relative;margin-right:3px;top: 3px;"><path d="M69.7856 78.4384m312.832 0l259.328 0q312.832 0 312.832 312.832l0 259.328q0 312.832-312.832 312.832l-259.328 0q-312.832 0-312.832-312.832l0-259.328q0-312.832 312.832-312.832Z" fill="#231F18" p-id="5526"></path><path d="M708.4032 322.1504l-31.6928 13.1072A50.2784 50.2784 0 0 1 619.9296 322.56l-69.12-77.3632a50.2784 50.2784 0 0 0-75.4176 0.4608l-64.9728 74.24a50.2272 50.2272 0 0 1-55.808 13.9264l-39.8336-15.36a50.2784 50.2784 0 0 0-67.584 54.7328l40.2944 247.3984a50.2784 50.2784 0 0 0 49.7152 42.5984h350.5664a50.2784 50.2784 0 0 0 49.664-42.5984l39.936-244.48a50.3296 50.3296 0 0 0-68.9664-53.9648z m-196.1472 279.6032a103.9872 103.9872 0 1 1 103.9872-103.9872 104.0896 104.0896 0 0 1-103.9872 103.9872z" fill="#F6CB86" p-id="5527"></path><path d="M512.256 456.3968a41.4208 41.4208 0 1 0 41.4208 41.3696 41.4208 41.4208 0 0 0-41.4208-41.3696z" fill="#F6CB86" p-id="5528"></path><path d="M278.9888 704.8192m32.5632 0l401.4592 0q32.5632 0 32.5632 32.5632l0 0.0512q0 32.5632-32.5632 32.5632l-401.4592 0q-32.5632 0-32.5632-32.5632l0-0.0512q0-32.5632 32.5632-32.5632Z" fill="#F6CB86" p-id="5529"></path></svg>');
    </script>



    <script>
        function task_onetouch(paypwd) {

            //if (typeof (paypwd) == "undefined") {

            //    security_password(function (e) {

            //        task_onetouch(e.password);

            //    }, { error_tip_top: '<div style="color: #2a2b2c;padding: 0 30px;margin-top: 20px;font-weight: bold;text-align:center;font-size: 16px;color:#000;" class="animated-text"><div style="    margin-bottom: 10px;">您确定开始任务吗？</div><div style=" margin-bottom: 5px; "><span style="color:red;">本次一键任务金额为 ' + $('#task_amount').val() + '<img src="/static/images/coin.png" alt="" height="15" width="15" style="margin-left: 5px;"></span></div>  <div><span style="color: red;">请于助理保持联络！</span></div>  </div>' })
            //    return;
            //}


            layer.open({
                type: 2,
                content: '提交中',
                time: 99,
                shadeClose: false
            })

            v3api("task_onetouch", {
                error: 1,
                data: {
                    task_amount: $('#task_amount').val(),
                    itemid: $('.select_items.active').attr('itemid'),
                    paypwd: paypwd
                }
            }, function (e) {
                get_onetouch_number();
                layer.closeAll();
                console.log('ee', e);
                tp(e.msg);
            })
        }






        var get_onetouch_number = function () {
            v3api("get_onetouch_number", {
                data: {
                }
            }, function (e) {
                for (var i = 0; i < e.list.length; i++) {
                    var itemid = e.list[i].itemid;
                    $('.task_point[itemid="' + itemid + '"]').removeClass('nothing');
                    var limitNumber = $('.task_point[itemid="' + itemid + '"]').attr('limitNumber');
                    var currentNumber = parseNumber(limitNumber) - parseNumber(e.list[i].number);
                    if (currentNumber <= 0) {
                        currentNumber = 0;
                        $('.task_point[itemid="' + itemid + '"]').addClass('nothing');
                    }

                    $('.task_point[itemid="' + itemid + '"]').html('剩余' + currentNumber + '次');
                }

            })
        }
        get_onetouch_number();
    </script>



    <style>
        .pop-cpt {
            z-index: 999999999;
            position: relative;
            display: none;
        }

        .pop-cpt2 {
            display: flex;
            justify-content: center;
            align-items: center;
            position: fixed;
            left: 0;
            top: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,.7);
        }

        .pop-cpt a {
            text-decoration: none;
            color: inherit;
            display: block;
        }

            .pop-cpt a:hover {
                text-decoration: none;
            }

        .pop-cpt .pop-cpt-bd {
            line-height: 22px;
            padding: 0 5px;
            box-sizing: border-box;
            border-radius: 15px;
            background: linear-gradient(180deg, #e17434, #FEFBF0);
            font-size: 14px;
            color: #333;
            font-family: "微软雅黑";
            position: relative;
        }

        .pop-cpt .pop-cpt-close {
            display: inline-block;
            height: 36px;
            line-height: 36px;
            padding: 0 15px;
            color: #fff;
            position: absolute;
            top: 0;
            right: 0;
        }

        .pop-cpt .pop-cpt-tit {
            height: 52px;
            line-height: 52px;
            color: #000;
            text-align: center;
            font-size: 18px;
            font-weight: bold;
            color: #f5ecdc;
            text-shadow: 2px 3px 2px #7d5909;
        }

            .pop-cpt .pop-cpt-tit span {
                display: inline-block;
                padding: 0 10px;
                position: absolute;
                top: 0;
                right: 0;
            }

        .pop-cpt .pop-cpt-con {
            padding: 0 6px 3px 6px;
            border-radius: 15px;
            background: #fff;
        }

        .pop-cpt .pop-cpt-con1 {
            text-align: center;
            color: #000;
        }

        .pop-cpt .pop-cpt-con2 {
            padding-top: 10px;
        }

        .pop-cpt .pop-cpt-con2-tit {
            display: flex;
            justify-content: center;
            padding-bottom: 5px;
        }

            .pop-cpt .pop-cpt-con2-tit span {
                display: inline-block;
                width: 26px;
                line-height: 26px;
                margin-left: -3px;
                text-align: center;
                color: #FFF200;
                font-size: 16px;
                font-weight: bold;
                border-radius: 50%;
                background: #ED1B20;
            }

        .pop-cpt .pop-cpt-con2-logo {
            display: flex;
            justify-content: center;
            height: 60px;
        }

        .pop-cpt .pop-cpt-con2-logol {
            color: #fb2725;
            font-size: 54px;
            line-height: 50px;
            font-weight: bold;
            font-family: Tahoma;
        }

        .pop-cpt .pop-cpt-con2-logor {
            width: 70px;
            position: relative;
        }

        .pop-cpt .pop-cpt-con2-logor-txt1 {
        }

            .pop-cpt .pop-cpt-con2-logor-txt1 span {
                display: inline-block;
                width: 60px;
                height: 22px;
                line-height: 22px;
                text-align: center;
                color: #fff;
                border-radius: 5px;
                background: #0082f3;
                position: absolute;
                left: 8px;
                top: 2px;
            }

        .pop-cpt .pop-cpt-con2-logor-txt2 {
            line-height: 32px;
            color: #333;
            font-size: 30px;
            font-family: impact,arial;
            position: absolute;
            top: 24px;
            left: 0;
        }

        .pop-cpt .pop-cpt-con3 {
            padding: 0 2px;
        }

            .pop-cpt .pop-cpt-con3 table {
                font-weight: bold;
                border-collapse: collapse;
            }

            .pop-cpt .pop-cpt-con3 th {
                text-align: center;
                color: #fff;
                border: solid 1px #999;
                /*background: #0082f3;*/
                font-size: 18px;
                font-weight: 500;
                padding: 5px 0;
                /*color:#000;*/
                border: solid 1px #ffd1bf;
                background: #FE6225;
                color: #f5ecdc;
                text-shadow: 2px 3px 2px #7d5909;
            }

            .pop-cpt .pop-cpt-con3 td {
                text-align: center;
                border: solid 1px #999;
                font-size: 16px;
                line-height: 22px;
                padding: 5px 0;
                border: solid 1px #FE6225;
                color: #5a5b5c;
                text-shadow: 2px 3px 2px #5a5b5c40;
            }

        .pop-cpt .pop-cpt-con4 {
            text-align: center;
            margin-bottom: 10px;
        }

            .pop-cpt .pop-cpt-con4 span {
                display: inline-block;
                width: 200px;
                height: 30px;
                line-height: 30px;
                font-size: 20px;
                font-weight: bold;
                color: #ff0;
                border-radius: 10px;
                background: #fb2725;
            }

        .pop-cpt .pop-cpt-footer {
            padding: 10px 0;
            color: #fff;
            text-align: center;
        }

        .pop-cpt .pop-cpt-footer1 {
            font-size: 16px;
        }

        .pop-cpt .pop-cpt-footer2 {
            padding-top: 5px;
            font-weight: bold;
            color: #ff0;
        }
    </style>
    <div onclick="$(this).hide()" id="pop-cpt" class="pop-cpt">
        <div class="pop-cpt2">
            <div class="pop-cpt-bd">
                <a>
                    <div class="pop-cpt-tit">如何提升一键任务成功率</div>
                    <div class="pop-cpt-con">
                        <div class="pop-cpt-con2">
                        </div>
                        <div class="pop-cpt-con3" style="margin: 10 x 0;">
                            <table width="100%" border="1">
                                <tbody id="share_list">
                                    <tr class="firstRow">
                                        <th>周期</th>
                                        <th>成功率</th>
                                        <th style="width: 10px;">说明</th>
                                    </tr>
                                    <asp:Repeater ID="onetouch_success_rate" runat="server">
                                        <ItemTemplate>
                                            <tr lessday="<%#Eval("lessday") %>" rate1="<%#Eval("rate1") %>" rate2="<%#Eval("rate2") %>">
                                                <td>连续<%#Eval("lessday") %>天未新增成员</td>
                                                <td><%#Eval("rate1") %>%~<%#Eval("rate2") %>%</td>
                                            </tr>
                                        </ItemTemplate>
                                    </asp:Repeater>
                                </tbody>
                            </table>
                        </div>

                    </div>
                </a>
                <div class="pop-cpt-footer">
                    <a></a><a style="color: #000; border: 1px solid #ccc; display: inline-block; padding: 6px 28px; border-radius: 50px; font-weight: bold; background: #efb397; color: #854825; border: 1px solid #c17345;" href="partners_manage.aspx?top=1" target="_blank">马上提升成功率</a>

                </div>

            </div>
        </div>
    </div>

    <script>
        $('#share_list tr').eq(1).find('td').eq(0).html('默认成功率');
        var default_rate = $('#share_list tr').eq(1).find('td').eq(1).text();

        $('#share_list tr').eq(1).append('<td rowspan="' + ($('#share_list tr').length - 1) + '">新增1个直属下级<span style="color: #bf4723;">一键任务</span>恢复默认成功率' + default_rate + '</td>');

        var lessday = '<%=pmlist["lessday"] %>';



        updateProgress($('#share_list tr[lessday]').eq(-1).attr('rate1'), $('#share_list tr[lessday]').eq(-1).attr('rate2'));
        $('#share_list tr[lessday]').each(function () {
            var day = $(this).attr('lessday');
            console.log('lessday', day, lessday);
            if (parseFloat(lessday) <= parseFloat(day)) {
                updateProgress($(this).attr('rate1'), $(this).attr('rate2'));
                return false;
            }
        })
    </script>




</asp:Content>


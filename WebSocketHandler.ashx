<%@ WebHandler Language="C#" Class="WebSocketHandler" %>

using System;
using System.Net.WebSockets;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using System.Web;


public class WebSocketHandler : IHttpHandler
{
    public void ProcessRequest(HttpContext context)
    {
        if (context.IsWebSocketRequest)
        {
            context.AcceptWebSocketRequest(ProcessWebSocketSession);
        }
        else
        {
            context.Response.StatusCode = 400;
            context.Response.StatusDescription = "Bad Request";
        }
    }

    private async Task ProcessWebSocketSession(AspNetWebSocketContext context)
    {
        WebSocket socket = context.WebSocket;
        var buffer = new byte[1024];

        while (socket.State == WebSocketState.Open)
        {
            WebSocketReceiveResult result = await socket.ReceiveAsync(new ArraySegment<byte>(buffer), CancellationToken.None);
            if (result.MessageType == WebSocketMessageType.Close)
            {
                await socket.CloseAsync(WebSocketCloseStatus.NormalClosure, "Closing", CancellationToken.None);
            }
            else
            {
                string receivedMessage = Encoding.UTF8.GetString(buffer, 0, result.Count);
                string responseMessage = "Server received: " + receivedMessage;
                byte[] responseBuffer = Encoding.UTF8.GetBytes(responseMessage);
                await socket.SendAsync(new ArraySegment<byte>(responseBuffer), WebSocketMessageType.Text, true, CancellationToken.None);
            }
        }
    }

    public bool IsReusable => false;
}
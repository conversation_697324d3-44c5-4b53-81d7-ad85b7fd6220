using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

public partial class order_list : baseClass
{
    public DataTable userdt = new DataTable();
    public Dictionary<string, object> pmlist = new Dictionary<string, object>();
    protected void Page_Load(object sender, EventArgs e)
    {
        if (uConfig.stcdata("partners_page", "", "agent") == "agent")
        {
            Response.Redirect("~/partners_manage.aspx");
            Response.End();
        }

        List<SqlParameter> pams = new List<SqlParameter>();
        dbClass db = new dbClass();
        pams.Add(new SqlParameter("@userid", uConfig.p_uid));
        string sql = string.Empty;



        sql = "select a.*,p.parent_code as parent_usercode from accounts a with(nolock) left join accounts p with(nolock) on a.parentid=p.id where a.id=@userid";
        sql += " SELECT COUNT(0) as num,isnull(SUM((case when parentid=@userid then 1 else 0 end)),0) as parentNum FROM [accounts] with(nolock) where datediff(day,create_time,getdate())=0 and ','+relaids+',' like '%," + uConfig.p_uid + ",%' ";
        //sql += " SELECT sum(amount) as amount,isnull(SUM((case when parentid=" + uConfig.p_uid + " then amount else 0 end)),0) as parentAmount FROM [play_total] with(nolock) where datediff(day,create_date,getdate())=0 and ','+relaids+',' like '%," + uConfig.p_uid + ",%' ";
        sql += " select * from play_user_daily with(nolock) where datediff(day,create_date,getdate())=0 and userid=@userid ";

        sql += " select * from [play_user_data] with(nolock) where userid=@userid and datediff(day,getdate(),create_date)=0 ";
        DataSet ds = db.getDataSet(sql, pams.ToArray());
        userdt = ds.Tables[0];

        DataTable dt = ds.Tables[1];
        pmlist["team_usernum"] = dt.Rows[0]["parentNum"] + "";
        pmlist["usernum"] = dt.Rows[0]["num"] + "";


        dt = ds.Tables[2];
        //pmlist["team_amount"] = dt.Rows[0]["parentAmount"] + "";
        //pmlist["user_amount"] = dt.Rows[0]["amount"] + "";

        pmlist["team_amount"] = "0";
        pmlist["user_amount"] = "0";
        pmlist["bork_user"] = "0";
        pmlist["bork_team"] = "0";
        pmlist["total_bork"] = "0";

        if (dt.Rows.Count > 0)
        {
            pmlist["team_amount"] = dt.Rows[0]["team_amount"] + "";
            pmlist["user_amount"] = dt.Rows[0]["user_amount"] + "";
            pmlist["bork_user"] = dt.Rows[0]["bork_user"] + "";
            pmlist["bork_team"] = dt.Rows[0]["bork_team"] + "";
            pmlist["total_bork"] = (Convert.ToDouble(pmlist["bork_user"] + "") + Convert.ToDouble(pmlist["bork_team"] + "")).ToString("0.00");
        }



        dt = ds.Tables[3];
        pmlist["team_amount"] = 0;
        pmlist["user_amount"] = 0;
        if (dt.Rows.Count > 0)
        {
            pmlist["team_amount"] = dt.Rows[0]["team_amount"] + "";
            pmlist["user_amount"] = dt.Rows[0]["user_amount"] + "";
        }


        DataTable temp_dt = chelper.gdt("play_rules");//规则表
        temp_dt = SortDataTable(temp_dt, "team_amount asc");
        pmlist["return_amount"] = 0;

        for (int t = 0; t < temp_dt.Rows.Count; t++)
        {
            if (Convert.ToDouble(pmlist["team_amount"] + "") + Convert.ToDouble(pmlist["user_amount"] + "") >= Convert.ToDouble(temp_dt.Rows[t]["team_amount"] + ""))
            {
                pmlist["return_amount"] = temp_dt.Rows[t]["brok_amount"] + "";
            }
        }

        //Response.Write(ToJson(temp_dt));
        //Response.End();

        game_levels.DataSource = temp_dt;
        game_levels.DataBind();

    }
}
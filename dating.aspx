<%@ Page Title="" Language="C#" MasterPageFile="~/top.master" AutoEventWireup="true" CodeFile="dating.aspx.cs" Inherits="order_list" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="Server">
    <%--    <script>
        $(function () {
            popTitle('买币大厅', '<a style="position: absolute; right: 0; height: 100%; width: 50px; top: 5px; display: flex; align-items: center; justify-content: center;" href="buy_orderList.aspx"><svg t="1694286034294" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="11523" width="25" height="25">                    <path d="M853.333333 146.773333H170.666667c-20.48 0-34.133333 17.066667-34.133334 34.133334v682.666666c0 20.48 17.066667 34.133333 34.133334 34.133334h682.666666c20.48 0 34.133333-13.653333 34.133334-34.133334v-682.666666c0-20.48-17.066667-34.133333-34.133334-34.133334zM512 754.346667c0 6.826667-6.826667 13.653333-13.653333 13.653333H252.586667c-6.826667 0-13.653333-6.826667-13.653334-13.653333v-23.893334c0-6.826667 6.826667-13.653333 13.653334-13.653333h245.76c6.826667 0 13.653333 6.826667 13.653333 13.653333v23.893334z m102.4-221.866667c0 6.826667-6.826667 13.653333-13.653333 13.653333H252.586667c-6.826667 0-13.653333-6.826667-13.653334-13.653333v-23.893333c0-6.826667 6.826667-13.653333 13.653334-13.653334h348.16c6.826667 0 13.653333 6.826667 13.653333 13.653334v23.893333z m170.666667-221.866667c0 6.826667-6.826667 13.653333-13.653334 13.653334H252.586667c-6.826667 0-13.653333-6.826667-13.653334-13.653334v-23.893333c0-6.826667 6.826667-13.653333 13.653334-13.653333h518.826666c6.826667 0 13.653333 6.826667 13.653334 13.653333v23.893333z" fill="#708BB1" p-id="11524"></path></svg></a>');


        })
    </script>--%>
    <style>
        .top-title .pptitle {
            font-size: 22px;
            font-weight: 500;
        }

        body {
            background: #F7F6FB;
        }

        .top-title {
            background: none;
        }

        .ppreturn {
            display: none!important;
        }

        .ulk_menus {
            display: block!important;
        }

        #lists {
            padding: 18px;
            /*height: calc(100vh - 320px);*/
            overflow-y: auto;
        }

        .ulk_block {
            display: none;
        }
    </style>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="Server">

    <%--<div style="display: flex; font-weight: bold; margin-bottom: 20px; padding: 0 12px;">
    </div>--%>

    <style>
        .paytype_list {
            display: flex;
            border-bottom: 1px solid #eee;
        }

            .paytype_list > div {
                width: 25%;
                text-align: center;
                font-size: 14px;
                padding: 15px 0;
                font-weight: bold;
                color: #878B90!important;
                position: relative;
                cursor: pointer;
            }

                .paytype_list > div.paytype_active {
                    color: #0066EF!important;
                }

                    .paytype_list > div.paytype_active::after {
                        content: "";
                        position: absolute;
                        bottom: 0px;
                        left: 35%;
                        width: 30%;
                        height: 2px;
                        background-color: #0066EF;
                        border-radius: 18px;
                    }
    </style>

    <div style="position: fixed; display: flex; top: 0; width: 100%; flex-direction: column; height: 100%; z-index: 9; max-width: 600px; left: 50%; transform: translate(-50%, 0);">

        <div style="height: 100%; display: flex; flex-direction: column; padding: 10px;">



            <div style="margin-top: 10px;">
                <a href="gateway_list.aspx">
                    <img src="../static/images/pay/pay.jpg" style="width: 100%;" /></a>
            </div>





            <div class="paytype_list" style="background: #fff;">
                <%--<div class="paytype_active">全部</div>--%>
                <div class="paytype_active">银行卡</div>
                <div>支付宝</div>
                <div>USDT</div>
                <%--<div>银商专员</div>--%>
                <%--<div>网银</div>--%>
            </div>
            <div style="padding: 18px; background: #fff; flex-grow: 1;" id="lists">
            </div>



            <div style="height: 80px; flex-shrink: 0;"></div>



        </div>

    </div>

    <script>
        $('.paytype_list>div').on('click', function () {
            dating_updateTime = "";
            $(this).addClass("paytype_active").siblings().removeClass("paytype_active");
            var name = $(this).text();
            if (name == "全部") {
                name = "";
            }
            $('#lists').html('');


            $('#usdt_rate').hide();
            switch (name) {
                case "银商专员":
                    name = "银行卡";
                    break;
                case "银行卡":
                    name = "网银";
                    break;
                case "USDT":
                    $('#usdt_rate').show();
                    break;
                default:
                    break;

            }

            show_list(0, name);
        })
    </script>


    <script>
        var dating_updateTime = "";
        var show_list = function (state, payment_type) {
            console.log('payment_type', payment_type);

            v3api("lists", { error: 1, data: { page: 'buy_list', p: 0, limit: 200, state: state, payment_type: payment_type, dating_updateTime: dating_updateTime } }, function (e) {

                if (e.code != 1) {
                    return;
                }

                dating_updateTime = e.dating_updateTime;

                $('#lists').html('');

                for (var i = 0; i < e.data.list.length; i++) {
                    var obj = e.data.list[i];


                    console.log('obj', i, obj);

                    if (obj.payment_type == "银行卡") {


                        var yajin = "";
                        try {
                            yajin = obj.payment_bankid.split('-')[1];

                            if (typeof (yajin) == "undefined") {
                                yajin = "";
                            }
                        } catch (e) {
                            console.log('押金错误', yajin, obj.payment_bankid, e);
                        }

                        console.log('进入银行', yajin);

                        if (yajin != "") {

                            $('#lists').append('<div style="border-bottom: 1px solid #f5f5f5;padding-bottom: 16px;margin-bottom: 12px;">            <div style="display: flex; align-items: center;">                    <div style="font-size: 12px; color: #7d8b9d;">                      <div style="display: flex;margin-bottom: 10px;align-items: center;">                    <div style="    display: flex;    font-weight: bold;    color: #9b7230;"><svg t="*************" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="14022" width="16" height="16"><path d="M69.888 78.4384m312.832 0l259.328 0q312.832 0 312.832 312.832l0 259.328q0 312.832-312.832 312.832l-259.328 0q-312.832 0-312.832-312.832l0-259.328q0-312.832 312.832-312.832Z" fill="#231F18" p-id="14023"></path><path d="M791.5008 480.512l-49.8688-49.8688V360.0896a64.3584 64.3584 0 0 0-64.3584-64.3584H606.72L556.8 245.76A64.3072 64.3072 0 0 0 465.92 245.76l-49.8688 49.8688h-70.656a64.3584 64.3584 0 0 0-64.3584 64.4608v70.5536l-49.92 49.8688a64.4608 64.4608 0 0 0 0 91.0336l49.92 49.8688v70.5536a64.3584 64.3584 0 0 0 64.3584 64.3584h70.5536l49.9712 49.8688a64.3072 64.3072 0 0 0 90.9824 0l49.92-49.8688h70.5536a64.3584 64.3584 0 0 0 64.3584-64.3584v-70.5536l49.8688-49.8688a64.3584 64.3584 0 0 0-0.1024-91.0336z m-181.504 11.4688l-84.8896 110.1824a23.3984 23.3984 0 0 1-37.5808-0.768L412.0576 494.7456a23.3472 23.3472 0 0 1 32.3584-32.6656l48.64 33.7408a23.5008 23.5008 0 0 0 25.6 0.512l60.0576-38.2976a23.3472 23.3472 0 0 1 31.2832 33.9456z" fill="#F6CB86" p-id="14024"></path></svg>&nbsp;已认证</div>                    <div style="color: #000; font-size: 15px; margin-left: 5px; font-weight: bold; display: flex; align-items: center;">                        ' + obj.payment_name + '&nbsp;<svg t="1694280548370" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="38741" width="15" height="15"><path d="M625.5104 560.1792m-281.4464 0a281.4464 281.4464 0 1 0 562.8928 0 281.4464 281.4464 0 1 0-562.8928 0Z" fill="#FED1B0" p-id="38742"></path><path d="M514.8672 934.8096c-34.6624 0-67.1744-13.6192-91.4432-38.3488l-324.4544-330.3424C39.2704 505.344 31.0272 410.3168 79.2576 340.1728L203.264 159.8464a178.21696 178.21696 0 0 1 146.8928-77.2608h324.1472c57.8048 0 112.2304 28.16 145.6128 75.3664l128.2048 181.4528c49.7664 70.4512 42.0352 166.2464-18.432 227.7888l-323.328 329.216c-24.32 24.7808-56.832 38.4-91.4944 38.4zM350.1568 144.0256c-38.4512 0-74.4448 18.944-96.256 50.6368L129.8944 374.9888c-31.6416 45.9776-26.1632 108.2368 12.9024 148.0704l324.4544 330.3424c12.6464 12.9024 29.5424 19.968 47.616 19.968 18.0736 0 34.9696-7.1168 47.616-19.968l323.328-329.216a116.77696 116.77696 0 0 0 12.0832-149.2992l-128.2048-181.4528a117.03808 117.03808 0 0 0-95.3856-49.408H350.1568zM104.5504 357.5808z" fill="#4F4F4F" p-id="38743"></path><path d="M515.8912 646.0928c-17.2032 0-32.8192-8.7552-41.7792-23.5008L340.3264 402.688a30.70976 30.70976 0 0 1 52.48-31.8976l122.9312 202.0864 119.8592-201.8304a30.71488 30.71488 0 1 1 52.8384 31.3344l-130.5088 219.8016a48.52736 48.52736 0 0 1-41.7792 23.9104h-0.256z" fill="#4F4F4F" p-id="38744"></path></svg>                    </div>                    <div style="margin-left: auto; font-size: 12px; color: #7d8b9d; display: flex; align-items: center; color: darkgreen;display:none;">                        <span style="background: #eee; display: inline-block; padding: 1px 11px; border-radius: 5px; color: #555;">不可拆单</span>                    </div>                </div><div style="    color: #000;    margin-bottom: 10px;">已押' + yajin + '元</div>  <div>收款方式：银行卡</div>                                         </div>                    <div style="margin-left: auto; text-align: right;">                        <div style="font-size: 12px; color: #7d8b9d;">金额</div>                        <div style="font-size: 20px; color: #0066EF;">' + obj.payment_bankid + '<img src="/static/images/coin.png" alt="" height="15" width="15" style="margin-left: 5px;">                        </div>                    </div>                </div><div style="display: flex; margin-top: 10px; align-items: center;">                    <div style="    display: flex;">            <svg t="*************" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="39928" width="16" height="16">                            <path d="M829.64898 805.093878H194.35102c-43.885714 0-79.412245-35.526531-79.412244-79.412245V298.840816C114.938776 254.955102 150.465306 219.428571 194.35102 219.428571h635.29796c43.885714 0 79.412245 35.526531 79.412244 79.412245v426.840817c0 43.363265-35.526531 79.412245-79.412244 79.412245z" fill="#F2CB51" p-id="39929"></path><path d="M114.938776 347.95102h794.122448v89.338776H114.938776z" fill="#DBAD2C" p-id="39930"></path><path d="M234.057143 699.559184c-10.971429 0-19.853061-8.881633-19.853061-19.853062v-148.897959c0-10.971429 8.881633-19.853061 19.853061-19.853061 10.971429 0 19.853061 8.881633 19.853061 19.853061v148.897959c0 10.971429-8.881633 19.853061-19.853061 19.853062zM333.322449 699.559184c-10.971429 0-19.853061-8.881633-19.853061-19.853062v-148.897959c0-10.971429 8.881633-19.853061 19.853061-19.853061 10.971429 0 19.853061 8.881633 19.853061 19.853061v148.897959c0 10.971429-8.881633 19.853061-19.853061 19.853062zM432.587755 699.559184c-10.971429 0-19.853061-8.881633-19.853061-19.853062v-148.897959c0-10.971429 8.881633-19.853061 19.853061-19.853061 10.971429 0 19.853061 8.881633 19.853061 19.853061v148.897959c0 10.971429-8.881633 19.853061-19.853061 19.853062z" fill="#E5404F" p-id="39931"></path></svg>                 <div style="    font-size: 12px;    margin-left: 8px;">' + obj.payment_bankname + '</div></div>                    <div style="margin-left: auto;">                        <a style="border-radius: 6px; padding: 5px 31px; font-size: 14px; background: #0066EF; color: #eee; display: flex; cursor: pointer; font-weight: 600;    text-decoration: none;" href="buy_operator.aspx?id=' + obj.orderId + '">&nbsp;购买</a>                    </div>                </div>       </div>');

                        }

                    } else {


                        switch (obj.payment_type) {
                            case "网银":
                                obj.payment_type = "银行卡";
                                break;
                            default:
                                break;

                        }

                        $('#lists').append('<div style="border-bottom: 1px solid #f5f5f5;padding-bottom: 16px;margin-bottom: 12px;">                         ' + '<div style="margin-left: auto; font-size: 12px; color: #7d8b9d; display: flex; align-items: center; color: darkgreen;display:none;">                        <span style="background: #eee; display: inline-block; padding: 1px 11px; border-radius: 5px; color: #555;">不可拆单</span>                   ' + '               </div>                <div style="display: flex; align-items: center;">                    <div style="font-size: 12px; color: #7d8b9d;">                      <div style="display: flex; margin-bottom: 10px;">                    <div style="background: #0066EF; color: #fff; width: 22px; height: 22px; font-size: 14px; border-radius: 50%; text-align: center; line-height: 22px;position:relative;">                        官                        <span style="background: #21d5b5;display:inline-block;width: 7px;height: 7px;border-radius: 50%;position: absolute;bottom: -2px;right: -4px;border: 1px solid #fff;"></span>                                       </div>                    <div style="color: #000; font-size: 15px; margin-left: 5px; font-weight: bold; display: flex; align-items: center;">                        官方自营店铺&nbsp;<svg t="1694280548370" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="38741" width="15" height="15"><path d="M625.5104 560.1792m-281.4464 0a281.4464 281.4464 0 1 0 562.8928 0 281.4464 281.4464 0 1 0-562.8928 0Z" fill="#FED1B0" p-id="38742"></path><path d="M514.8672 934.8096c-34.6624 0-67.1744-13.6192-91.4432-38.3488l-324.4544-330.3424C39.2704 505.344 31.0272 410.3168 79.2576 340.1728L203.264 159.8464a178.21696 178.21696 0 0 1 146.8928-77.2608h324.1472c57.8048 0 112.2304 28.16 145.6128 75.3664l128.2048 181.4528c49.7664 70.4512 42.0352 166.2464-18.432 227.7888l-323.328 329.216c-24.32 24.7808-56.832 38.4-91.4944 38.4zM350.1568 144.0256c-38.4512 0-74.4448 18.944-96.256 50.6368L129.8944 374.9888c-31.6416 45.9776-26.1632 108.2368 12.9024 148.0704l324.4544 330.3424c12.6464 12.9024 29.5424 19.968 47.616 19.968 18.0736 0 34.9696-7.1168 47.616-19.968l323.328-329.216a116.77696 116.77696 0 0 0 12.0832-149.2992l-128.2048-181.4528a117.03808 117.03808 0 0 0-95.3856-49.408H350.1568zM104.5504 357.5808z" fill="#4F4F4F" p-id="38743"></path><path d="M515.8912 646.0928c-17.2032 0-32.8192-8.7552-41.7792-23.5008L340.3264 402.688a30.70976 30.70976 0 0 1 52.48-31.8976l122.9312 202.0864 119.8592-201.8304a30.71488 30.71488 0 1 1 52.8384 31.3344l-130.5088 219.8016a48.52736 48.52736 0 0 1-41.7792 23.9104h-0.256z" fill="#4F4F4F" p-id="38744"></path></svg>                    </div>                    <div style="margin-left: auto; font-size: 12px; color: #7d8b9d; display: flex; align-items: center; color: darkgreen;display:none;">                        <span style="background: #eee; display: inline-block; padding: 1px 11px; border-radius: 5px; color: #555;">不可拆单</span>                    </div>                </div>  <div>收款方式：' + obj.payment_type.replace(/支付宝/g, '支付宝转银行卡') + '</div>                        ' + '<div style="margin-top: 5px;display:none;">                            限额 100-800,999                        </div>  ' + '                  </div>                    <div style="margin-left: auto; text-align: right;">                        ' + (obj.p1 != "" ? '<div style="background: linear-gradient(133deg, #b1331b, #bf2e28);color: #ffbf3a;border: 2px solid #f5a558;border-radius: 10px;font-size: 15px;font-weight: bold;margin-bottom: 4px;text-align: center;">送' + obj.p1 + '%</div>' : '<div style="font-size: 12px; color: #7d8b9d;">                            总额                        </div> ') + '                       <div style="font-size: 20px; color: #0066EF;">                            ' + obj.amount + (obj.payment_type == 'USDT' ? '<img src="/static/images/coin.png" alt="" height="15" width="15" style="margin-left: 5px;">' : 'CY') +'                        </div>       ' + (obj.payment_type != 'USDT' ? '<div style="font-size: 16px;font-weight: bold;">≈ ' + usd(obj.amount) + ' <img src="/static/images/coin.png" alt="" height="15" width="15" style="margin-left: 5px;"></div>' : '') + '             </div>                </div>                <div style="display: flex; margin-top: 10px; align-items: center;">                    <div>           ' + (obj.payment_type == "USDT" ? '    <div style="display: flex; font-size: 12px;">        <svg t="1694773671778" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="5529" width="16" height="16">            <path d="M1023.082985 511.821692c0 281.370746-228.08199 509.452736-509.452736 509.452736-281.360557 0-509.452736-228.08199-509.452737-509.452736 0-281.365652 228.092179-509.452736 509.452737-509.452737 281.370746 0 509.452736 228.087085 509.452736 509.452737" fill="#1BA27A" p-id="5530"></path><path d="M752.731701 259.265592h-482.400796v116.460896h182.969951v171.176119h116.460895v-171.176119h182.96995z" fill="#FFFFFF" p-id="5531"></path><path d="M512.636816 565.13592c-151.358408 0-274.070289-23.954468-274.070289-53.50782 0-29.548259 122.706786-53.507821 274.070289-53.507821 151.358408 0 274.065194 23.959562 274.065194 53.507821 0 29.553353-122.706786 53.507821-274.065194 53.50782m307.734925-44.587303c0-38.107065-137.776398-68.995184-307.734925-68.995184-169.953433 0-307.74002 30.888119-307.74002 68.995184 0 33.557652 106.837333 61.516418 248.409154 67.711363v245.729433h116.450707v-245.632637c142.66205-6.001353 250.615085-34.077294 250.615084-67.808159" fill="#FFFFFF" p-id="5532"></path></svg>        <div style="margin-left: 7px; color: #0066EF; font-weight: bold;">            1 USDT        </div>        <div style="padding: 0 3px; color: gray;">≈</div>        <div style="color: #000; font-weight: bold;">            ' + usdt.prices.app + '        </div>        <img src="/static/images/coin.png" alt="" height="15" width="15" style="margin-left: 5px;">    </div>' : ' <svg t="*************" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="39928" width="16" height="16">                            <path d="M829.64898 805.093878H194.35102c-43.885714 0-79.412245-35.526531-79.412244-79.412245V298.840816C114.938776 254.955102 150.465306 219.428571 194.35102 219.428571h635.29796c43.885714 0 79.412245 35.526531 79.412244 79.412245v426.840817c0 43.363265-35.526531 79.412245-79.412244 79.412245z" fill="#F2CB51" p-id="39929"></path><path d="M114.938776 347.95102h794.122448v89.338776H114.938776z" fill="#DBAD2C" p-id="39930"></path><path d="M234.057143 699.559184c-10.971429 0-19.853061-8.881633-19.853061-19.853062v-148.897959c0-10.971429 8.881633-19.853061 19.853061-19.853061 10.971429 0 19.853061 8.881633 19.853061 19.853061v148.897959c0 10.971429-8.881633 19.853061-19.853061 19.853062zM333.322449 699.559184c-10.971429 0-19.853061-8.881633-19.853061-19.853062v-148.897959c0-10.971429 8.881633-19.853061 19.853061-19.853061 10.971429 0 19.853061 8.881633 19.853061 19.853061v148.897959c0 10.971429-8.881633 19.853061-19.853061 19.853062zM432.587755 699.559184c-10.971429 0-19.853061-8.881633-19.853061-19.853062v-148.897959c0-10.971429 8.881633-19.853061 19.853061-19.853061 10.971429 0 19.853061 8.881633 19.853061 19.853061v148.897959c0 10.971429-8.881633 19.853061-19.853061 19.853062z" fill="#E5404F" p-id="39931"></path></svg>') + '              ' + '' + '                    </div>                    <div style="margin-left: auto;">                        <a style="border-radius: 6px; padding: 5px 31px; font-size: 14px; background: #0066EF; color: #eee; display: flex; cursor: pointer; font-weight: 600;    text-decoration: none;" href="order_buy.aspx?id=' + obj.orderId + '">&nbsp;购买</a>                    </div>                </div>            </div>');

                    }


                }

            })
        };

        show_list(0, '网银');

        setInterval(function () {
            var name = $('.paytype_active').text()
            switch (name) {
                case "银商专员":
                    name = "银行卡";
                    break;
                case "银行卡":
                    name = "网银";
                    break;
                default:
                    break;

            }

            show_list(0, name);
        }, 2000);


        var confirm_transaction = function (id) {
            v3api("confirm_transaction", { data: { id: id } }, function (e) {
                tp(e.msg);
                var ls = $('[listid=' + id + ']');
                ls.remove();
                //ls.find(".statedata").html('<span style="border-radius:18px;padding:2px 8px;font-size:12px;background:#2A3EE8;color:#fff;">已完成</span>');
            })

        }

        //监听
        subscribeToCustomEvent(function (e) {
            if (e.detail.last_path == "/order_buy.aspx" && e.detail.new_path == location.pathname) {
                dating_updateTime = "";
                console.log('页面发生变化');
                $('#lists').html('');


                var name = $('.paytype_active').text()
                switch (name) {
                    case "银商专员":
                        name = "银行卡";
                        break;
                    case "银行卡":
                        name = "网银";
                        break;
                    default:
                        break;

                }

                show_list(0, name);
            }
        });

    </script>







</asp:Content>


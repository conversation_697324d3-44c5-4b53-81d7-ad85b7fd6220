
<!DOCTYPE html>
<html lang="zh-cn">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>001</title>
    <!-- Elegant Line Icons CSS -->
    <link rel="stylesheet" href="../static/css/elegant-line-icons.css">
    <!-- Bootstrap CSS -->
    <link rel="stylesheet" href="../static/css/bootstrap.min.css">

    <!-- Responsive CSS -->
    <link rel="stylesheet" href="../static/css/index_all.css">

    <!-- jQuery Lib -->
    <script src="../static/js/jquery.min.js"></script>
    <!-- js -->
    <!-- Wow JS -->
    <script src="../static/js//wow.min.js"></script>




</head>
<body>
    <div class="site-preloader-wrap">
        <div class="spinner"></div>
    </div><!-- Preloader -->



    <style>
        .modal.fade .modal-dialog {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            ;
        }

        .h5-btn {
            font-size: 16px;
            border-radius: 5px;
            line-height: 50px;
            font-weight: 600;
            display: block;
            padding: 0 25px;
            letter-spacing: -0.02em;
            border: 1px solid #1151d3;
        }
    </style>


    <section class="promo-section padding">
        <div class="container">
            <div class="row">
                <div class="col-md-4 col-sm-6 sm-padding">
                    <div class="promo-content text-center">
                        <i class="icon-shield icon"></i>
                        <h3>安全</h3>
                        <p>
                            多重保护技术保障您的数字资产安全
                            放心管理您的钱包.
                        </p>
                    </div>
                </div><!-- Promo-1 -->
                <div class="col-md-4 col-sm-6 sm-padding">
                    <div class="promo-content text-center">
                        <i class="icon-layers icon"></i>
                        <h3>省心</h3>
                        <p>
                            严格的风控和24小时人工服务
                            为您打造无忧OTC交易环境.
                        </p>
                    </div>
                </div><!-- Promo-1 -->
                <div class="col-md-4 col-sm-6 sm-padding">
                    <div class="promo-content text-center">
                        <i class="icon-bargraph icon"></i>
                        <h3>极速</h3>
                        <p>
                            全新模式与强大生态伙伴
                            为您带来未曾有过的交易速度.
                        </p>
                    </div>
                </div><!-- Promo-3 -->
            </div>
        </div>
    </section><!-- Promo Section -->

    <section class="content-section bg-grey padding">
        <div class="container">
            <div class="row">
                <div class="col-lg-6">
                    <div class="content-mockups">
                        <!-- <div class="mockup-back"></div> -->
                        <div class="mockup-front"></div>
                    </div>
                </div>
                <div class="col-lg-6">
                    <div class="section-heading mb-40">
                        <h2>关于808币</h2>
                        <p>使用808管理、世界通用的、恒定人民币汇率数字资产 </p>
                    </div>
                    <div class="content-lists">
                        <div class="list-item">
                            <i class="icon-tools"></i>
                            <div class="list-content">
                                <h3>恒定1:1人民币汇率</h3>
                                <p>与其他数字货币不同，808币承诺永久无涨跌、也不加入任何第三方交易所。无论何时，都可以1:1在人民币和808币之间自由兑换 </p>
                            </div>
                        </div>
                        <div class="list-item">
                            <i class="icon-pictures"></i>
                            <div class="list-content">
                                <h3>稳定、安全、100%兑换</h3>
                                <p>全球数十万人使用，秒级交易匹配。808官方联合全球多个跨国集团共同保底，无需担心持有的808币无法兑换</p>
                            </div>
                        </div>
                        <div class="list-item">
                            <i class="icon-genius"></i>
                            <div class="list-content">
                                <h3>全球通用、自由跨境</h3>
                                <p>808币使用区块链技术具有高度匿名的特性，交易无迹可寻。世界范围内已有上千家大型商户支持808币，全球支付无障碍</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section><!-- Content Section -->






    <div class="scrollsidebar" id="scrollsidebar">
        <a class="item" href="order_new.aspx">
            <img src="../static/images/download.png" class="img-responsive">
        </a>
        <a class="item" href="online_service.aspx">
            <img src="../static/images/kefu.png" class="img-responsive">
        </a>
    </div>

    <!-- Footer End -->
    <!-- Main JS -->
    <script>
        /* Axuta Theme Scripts */

        (function ($) {
            "use strict";

            $(window).on('load', function () {
                $('body').addClass('loaded');
            });



            /*=========================================================================
                WOW Active
            =========================================================================*/
            new WOW().init();


        })(jQuery);


        function download() {
            if (device.ios() || device.android()) {
                $('#lineModal').modal('show');
            } else {
                window.location.href = window.location.protocol + '//' + window.location.host + '/#download'
            }
        }

        /*
         * @Author: ecitlm
         * @Date: 2017-08-04 23:18:26
         * @Last Modified by:   ecitlm
         * @Last Modified time: 2017-08-04 23:18:26
         */

        !(function () {
            var serviceOnline = (function () {
                var sideContent = document.querySelector(".side_content");
                var timer = null;

                //悬浮QQ匀速移动
                var startMove = function (argument) {
                    var scrollsidebar = document.getElementById("scrollsidebar");
                    clearInterval(timer);
                    timer = setInterval(function () {
                        var speed = (argument - scrollsidebar.offsetTop) / 4;
                        speed = speed > 0 ? Math.ceil(speed) : Math.floor(speed);
                        if (argument == scrollsidebar.offsetTop) {
                            clearInterval(timer);
                        } else {
                            scrollsidebar.style.top = (scrollsidebar.offsetTop + speed + 20) + "px";
                        }
                    }, 20);
                };

                //鼠标移动
                var scrollMove = function () {
                    window.onscroll = window.onload = function () {
                        var scrollsidebar = document.getElementById("scrollsidebar");
                        var scrolltop = document.body.scrollTop || document.documentElement.scrollTop;
                        if (!scrolltop) {
                            scrolltop = 0;
                        }
                        if (!scrollsidebar.offsetHeight) {
                            scrollsidebar.offsetHeight = 0
                        }
                        startMove(
                            parseInt(
                                (document.documentElement.clientHeight -
                                    scrollsidebar.offsetHeight) / 2 + scrolltop
                            )
                        );
                    };
                };

                //返回出来的方法
                return {
                    init: function () {
                        scrollMove();
                    }
                };
            })();

            //初始化
            serviceOnline.init();
        })();
    </script>
    <div class="hide">
    </div>


</body>

</html>
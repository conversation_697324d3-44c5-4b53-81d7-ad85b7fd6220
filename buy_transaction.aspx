<%@ Page Title="" Language="C#" MasterPageFile="~/top.master" AutoEventWireup="true" CodeFile="buy_transaction.aspx.cs" Inherits="order_list" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="Server">
    <script>
        $(function () {
            popTitle('订单详情', '');
        })
    </script>

    <style>
        .top-title {
            background: none;
        }
    </style>
    <style>
        .main-container {
            height: calc(100vh - 57px);
            overflow-y: auto;
        }

        body {
            background: linear-gradient(45deg, #F0F4F9, #F5F5F8);
            background-repeat: no-repeat;
            min-height: 100vh;
        }

        .select_tab {
            color: #000;
            box-sizing: border-box;
            padding: 12px 20px;
            text-align: center;
            font-weight: bold;
            font-size: 13px;
            background: #eee;
            border-radius: 12px;
            width: 50%;
            margin: 0 10px;
        }

            .select_tab.activity {
                background: #3c3351;
                color: #fff;
                transition: all 0.2s;
            }

        .theme-green .back-bar .selected-bar {
            background-color: #3838f5!important;
            background-image: linear-gradient(to bottom, #4747ff, #3838f5)!important;
        }

        .slider-container {
            margin-bottom: 38px;
            margin-top: 52px;
        }
    </style>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="Server">

    <style>
        #countdown {
            margin-left: 10px;
        }

        .countdown-item {
            background: #685EF4;
            color: #fff;
            display: inline-block;
            border-radius: 3px;
            width: 20px;
            height: 20px;
            text-align: center;
            line-height: 20px;
            font-size: 12px;
        }

        @keyframes rotate360 {
            from {
                transform: rotate(0deg);
            }

            to {
                transform: rotate(360deg);
            }
        }
        /* 应用旋转动画到元素 */
        .rotate-element .icon {
            animation: rotate360 5s linear infinite; /* 旋转2秒，线性速度，无限循环 */
            margin-right: 5px;
        }
    </style>





    <%if (uConfig.gd(userdt, "state") == "1001")
      {
    %>

    <div style="font-size: 22px; font-weight: 500; color: #954525; display: flex; align-items: center;" class="rotate-element">

        <svg t="1694330145365" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="14297" width="22" height="22">
            <path d="M511.49 294.67c-16.57 0-30-13.43-30-30V129.21c0-16.57 13.43-30 30-30s30 13.43 30 30v135.46c0 16.57-13.44 30-30 30zM510.45 927.13c-16.57 0-30-13.43-30-30V761.67c0-16.57 13.43-30 30-30s30 13.43 30 30v135.46c0 16.57-13.43 30-30 30zM265.66 540.28H130.2c-16.57 0-30-13.43-30-30s13.43-30 30-30h135.46c16.57 0 30 13.43 30 30s-13.43 30-30 30zM892.81 540.28H757.35c-16.57 0-30-13.43-30-30s13.43-30 30-30h135.46c16.57 0 30 13.43 30 30s-13.43 30-30 30zM263.14 809.9c-7.68 0-15.36-2.93-21.21-8.79-11.72-11.72-11.72-30.71 0-42.43l95.79-95.79c11.72-11.72 30.71-11.72 42.43 0 11.72 11.72 11.72 30.71 0 42.43l-95.79 95.79c-5.86 5.86-13.54 8.79-21.22 8.79zM706.6 366.44c-7.68 0-15.36-2.93-21.21-8.79-11.72-11.72-11.72-30.71 0-42.43l95.79-95.79c11.71-11.71 30.71-11.71 42.43 0 11.72 11.72 11.72 30.71 0 42.43l-95.79 95.79c-5.86 5.86-13.54 8.79-21.22 8.79zM781.13 809.9c-7.68 0-15.35-2.93-21.21-8.79l-95.79-95.79c-11.72-11.72-11.72-30.71 0-42.43 11.72-11.72 30.71-11.71 42.43 0l95.79 95.79c11.72 11.72 11.72 30.71 0 42.43a29.95 29.95 0 0 1-21.22 8.79zM337.67 366.44c-7.68 0-15.35-2.93-21.21-8.79l-95.79-95.79c-11.72-11.72-11.72-30.71 0-42.43 11.72-11.72 30.71-11.71 42.43 0l95.79 95.79c11.72 11.72 11.72 30.71 0 42.43a29.933 29.933 0 0 1-21.22 8.79z" p-id="14298" fill="#954525"></path></svg>

        商家交易确认中
    </div>



    <%}
      else if (uConfig.gd(userdt, "state") == "1")
      {
    %>

    <div style="font-size: 22px; font-weight: 500; color: #4faf6e; display: flex; align-items: center;">

        <svg t="1694340902993" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="4037" width="22" height="22" style="margin-right: 5px;">
            <path d="M512 0a512 512 0 1 0 512 512A512 512 0 0 0 512 0z m232.777143 382.72l-310.857143 310.857143a18.285714 18.285714 0 0 1-25.965714 0l-129.28-129.28a18.285714 18.285714 0 0 1 0-25.965714L305.188571 512a18.285714 18.285714 0 0 1 25.782858 0l85.394285 85.394286a7.314286 7.314286 0 0 0 10.24 0L693.028571 330.971429a18.285714 18.285714 0 0 1 25.782858 0l25.965714 25.782857a18.285714 18.285714 0 0 1 0 25.965714z" fill="#4faf6e" p-id="4038"></path></svg>

        交易完成
    </div>

    <%}
      else if (uConfig.gd(userdt, "state") == "1000")
      {
    %>

    <div style="font-size: 22px; font-weight: 500; color: #222; display: flex; align-items: center;" class="rotate-element">

        <svg t="1694330145365" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="14297" width="22" height="22">
            <path d="M511.49 294.67c-16.57 0-30-13.43-30-30V129.21c0-16.57 13.43-30 30-30s30 13.43 30 30v135.46c0 16.57-13.44 30-30 30zM510.45 927.13c-16.57 0-30-13.43-30-30V761.67c0-16.57 13.43-30 30-30s30 13.43 30 30v135.46c0 16.57-13.43 30-30 30zM265.66 540.28H130.2c-16.57 0-30-13.43-30-30s13.43-30 30-30h135.46c16.57 0 30 13.43 30 30s-13.43 30-30 30zM892.81 540.28H757.35c-16.57 0-30-13.43-30-30s13.43-30 30-30h135.46c16.57 0 30 13.43 30 30s-13.43 30-30 30zM263.14 809.9c-7.68 0-15.36-2.93-21.21-8.79-11.72-11.72-11.72-30.71 0-42.43l95.79-95.79c11.72-11.72 30.71-11.72 42.43 0 11.72 11.72 11.72 30.71 0 42.43l-95.79 95.79c-5.86 5.86-13.54 8.79-21.22 8.79zM706.6 366.44c-7.68 0-15.36-2.93-21.21-8.79-11.72-11.72-11.72-30.71 0-42.43l95.79-95.79c11.71-11.71 30.71-11.71 42.43 0 11.72 11.72 11.72 30.71 0 42.43l-95.79 95.79c-5.86 5.86-13.54 8.79-21.22 8.79zM781.13 809.9c-7.68 0-15.35-2.93-21.21-8.79l-95.79-95.79c-11.72-11.72-11.72-30.71 0-42.43 11.72-11.72 30.71-11.71 42.43 0l95.79 95.79c11.72 11.72 11.72 30.71 0 42.43a29.95 29.95 0 0 1-21.22 8.79zM337.67 366.44c-7.68 0-15.35-2.93-21.21-8.79l-95.79-95.79c-11.72-11.72-11.72-30.71 0-42.43 11.72-11.72 30.71-11.71 42.43 0l95.79 95.79c11.72 11.72 11.72 30.71 0 42.43a29.933 29.933 0 0 1-21.22 8.79z" p-id="14298" fill="#000"></path></svg>

        等待买家付款<div class="countdown_code"></div>
    </div>

    <%
      }
      else if (uConfig.gd(userdt, "state") == "-1")
      {
    %>

    <div style="font-size: 22px; font-weight: 500; color: #bb1818; display: flex; align-items: center;">

        <svg t="1694854732874" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="8010" width="22" height="22" style="margin-right: 5px;">
            <path d="M379.2 396.8c-17.6 0-28.8 11.2-28.8 28.8v51.2c0 17.6 11.2 28.8 28.8 28.8 11.2 0 28.8-11.2 28.8-28.8v-51.2c0-17.6-11.2-28.8-28.8-28.8zM673.6 476.8v-51.2c0-17.6-11.2-28.8-28.8-28.8s-28.8 11.2-28.8 28.8v51.2c0 17.6 11.2 28.8 28.8 28.8s28.8-11.2 28.8-28.8zM379.2 620.8c-11.2 11.2-11.2 28.8 0 40 6.4 6.4 11.2 6.4 17.6 6.4 6.4 0 11.2 0 17.6-6.4 51.2-51.2 144-57.6 195.2 0 11.2 11.2 28.8 11.2 40 0s11.2-28.8 0-40c-35.2-35.2-86.4-57.6-137.6-57.6s-104 22.4-132.8 57.6z" fill="currentColor" p-id="8011"></path><path d="M512 152C313.6 152 152 313.6 152 512c0 8 4.8 16 12.8 20.8 8 4.8 16 4.8 24 0l80-48c11.2-6.4 14.4-20.8 8-33.6s-20.8-14.4-33.6-8l-40 24C224 316.8 355.2 200 512 200c172.8 0 312 139.2 312 312S684.8 824 512 824c-99.2 0-193.6-48-252.8-129.6-8-11.2-22.4-12.8-33.6-4.8-11.2 8-12.8 22.4-4.8 33.6 67.2 92.8 176 148.8 291.2 148.8 198.4 0 360-161.6 360-360S710.4 152 512 152z" fill="currentColor" p-id="8012"></path></svg>

        交易超时<div class="countdown_code"></div>
    </div>

    <%
      }
      else
      {
    %>

    <div style="font-size: 22px; font-weight: 500; color: gray; display: flex; align-items: center;" class="rotate-element">
        交易已取消
    </div>

    <%
      } %>







    <div style="margin-top: 5px; margin-bottom: 10px; color: gray; font-size: 14px; display: flex;">
        <%=uConfig.gd(userdt, "state") == "1000" ? "请尽快向卖家付款" :uConfig.gd(userdt, "state") == "1" ? "本次交易已完成" :uConfig.gd(userdt, "state") == "1001" ? "请耐心等待商家确认，通常几分钟内响应":uConfig.gd(userdt, "state") == "-1" ?"如订单已支付请联系客服":"如果疑问请联系客服" %>


        <%if (uConfig.gd(userdt, "state") == "1000" || uConfig.gd(userdt, "state") == "1001")
          {
        %>

        <%--<div id="countdown">
            <span id="minutes1" class="countdown-item">0</span>
            <span id="minutes2" class="countdown-item">0</span>
            <span class="divider">:</span>
            <span id="seconds1" class="countdown-item">0</span>
            <span id="seconds2" class="countdown-item">0</span>
        </div>--%>

        <script>
            $('.countdown_code').append('<div id="countdown">            <span id="minutes1" class="countdown-item">0</span>            <span id="minutes2" class="countdown-item">0</span>            <span class="divider">:</span>            <span id="seconds1" class="countdown-item">0</span>            <span id="seconds2" class="countdown-item">0</span>        </div>');
        </script>

        <%
          } %>

        <script>
            // 获取倒计时容器和显示分钟、秒的元素
            var countdownContainer = document.getElementById("countdown");
            var minutes1Element = document.getElementById("minutes1");
            var minutes2Element = document.getElementById("minutes2");
            var seconds1Element = document.getElementById("seconds1");
            var seconds2Element = document.getElementById("seconds2");

            // 获取当前时间和传入的时间
            var currentTime = new Date().getTime();
            var targetTime = new Date("<%=Convert.ToDateTime(uConfig.gd(userdt,"update_time")==""?"1990-01-01 00:00:00":uConfig.gd(userdt,"update_time")).AddMinutes(20).ToString("yyyy-MM-dd HH:mm:ss") %>").getTime(); // 传入的时间（示例为2023年12月31日 23:59:59）

            // 计算时间差（毫秒）
            var timeDifference = targetTime - currentTime;

            console.log('timeDifference', timeDifference);

            // 更新倒计时的显示
            function updateCountdown() {
                if (timeDifference <= 0) {
                    clearInterval(countdownInterval);
                    // 在这里执行倒计时结束后的操作
                    // 例如显示提示消息或触发其他事件
                    countdownContainer.textContent = "已超时";
                    return;
                }

                var minutes = Math.floor(timeDifference / 60000); // 1分钟 = 60000毫秒
                var seconds = Math.floor((timeDifference % 60000) / 1000); // 1秒 = 1000毫秒

                //console.log('minutes,s', minutes, seconds);

                // 将分钟和秒数格式化为两位数
                minutes = minutes < 10 ? "0" + minutes : minutes.toString();
                seconds = seconds < 10 ? "0" + seconds : seconds.toString();


                if (minutes > 60) {
                    minutes = '60';
                    seconds = '00';
                }

                // 更新到页面中
                minutes1Element.textContent = minutes.charAt(0);
                minutes2Element.textContent = minutes.charAt(1);
                seconds1Element.textContent = seconds.charAt(0);
                seconds2Element.textContent = seconds.charAt(1);

                // 减少剩余时间
                timeDifference -= 1000;
            }

            // 初始更新倒计时
            updateCountdown();

            // 每一秒更新倒计时
            var countdownInterval = setInterval(updateCountdown, 1000);


        </script>
    </div>



    <%--<div style="margin-top: 5px; margin-bottom: 20px; color: #333; font-size: 14px; display: flex;">
        交易编码：<%=uConfig.gd(userdt, "orderId") %><svg t="*************" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="19366" width="18" height="18" style="padding: 0 8px; cursor: pointer;" onclick="textCopy('<%=uConfig.gd(userdt,"orderId") %>')">
            <path d="M866.133333 919.466667h-405.333333c-29.866667 0-53.333333-23.466667-53.333333-53.333334v-618.666666c0-29.866667 23.466667-53.333333 53.333333-53.333334h405.333333c29.866667 0 53.333333 23.466667 53.333334 53.333334v618.666666c0 29.866667-23.466667 53.333333-53.333334 53.333334z" fill="#69C0FF" p-id="19367"></path><path d="M770.133333 428.8h-213.333333c-12.8 0-21.333333-8.533333-21.333333-21.333333s8.533333-21.333333 21.333333-21.333334h213.333333c12.8 0 21.333333 8.533333 21.333334 21.333334s-10.666667 21.333333-21.333334 21.333333zM770.133333 556.8h-213.333333c-12.8 0-21.333333-8.533333-21.333333-21.333333s8.533333-21.333333 21.333333-21.333334h213.333333c12.8 0 21.333333 8.533333 21.333334 21.333334s-10.666667 21.333333-21.333334 21.333333zM770.133333 684.8h-213.333333c-12.8 0-21.333333-8.533333-21.333333-21.333333s8.533333-21.333333 21.333333-21.333334h213.333333c12.8 0 21.333333 8.533333 21.333334 21.333334s-10.666667 21.333333-21.333334 21.333333z" fill="#FFFFFF" p-id="19368"></path><path d="M567.466667 834.133333h-405.333334c-29.866667 0-53.333333-23.466667-53.333333-53.333333v-618.666667c0-29.866667 23.466667-53.333333 53.333333-53.333333h405.333334c29.866667 0 53.333333 23.466667 53.333333 53.333333v618.666667c0 29.866667-23.466667 53.333333-53.333333 53.333333z" fill="#1890FF" p-id="19369"></path><path d="M471.466667 343.466667h-213.333334c-12.8 0-21.333333-8.533333-21.333333-21.333334s8.533333-21.333333 21.333333-21.333333h213.333334c12.8 0 21.333333 8.533333 21.333333 21.333333s-10.666667 21.333333-21.333333 21.333334zM471.466667 471.466667h-213.333334c-12.8 0-21.333333-8.533333-21.333333-21.333334s8.533333-21.333333 21.333333-21.333333h213.333334c12.8 0 21.333333 8.533333 21.333333 21.333333s-10.666667 21.333333-21.333333 21.333334zM471.466667 599.466667h-213.333334c-12.8 0-21.333333-8.533333-21.333333-21.333334s8.533333-21.333333 21.333333-21.333333h213.333334c12.8 0 21.333333 8.533333 21.333333 21.333333s-10.666667 21.333333-21.333333 21.333334z" fill="#FFFFFF" p-id="19370"></path>
        </svg>
    </div>--%>

    <div>

        <div style="display: flex; margin-bottom: 30px;">

            <div style="width: 50%;">
                <span style="font-size: 14px; color: #7a7b7c;">总价</span>
                <div style="font-size: 22px; color: #00B595; margin-top: 5px;">
                    <%=uConfig.gd(userdt, "amount") %><%=(uConfig.gd(userdt, "payment_type") == "USDT" ? "<img src='/static/images/coin.png' alt='' height='15' width='15' style='margin-left: 5px;'>" : "CY") %>

                    <%if (uConfig.gd(userdt, "payment_type") != "USDT")
                      {
                    %>

                    <div style="font-size: 18px;color: #000;font-weight: bold;">
                        ≈ <%=tousd(uConfig.gd(userdt, "amount") ) %> <img src="/static/images/coin.png" alt="" height="15" width="15" style="margin-left: 5px;">
                    </div>

                    <%
                      } %>
                </div>



                <%if (uConfig.gd(userdt, "p1") != "")
                  {
                %>

                <div style="background: linear-gradient(133deg, #b1331b, #bf2e28); color: #ffbf3a; border: 2px solid #f5a558; border-radius: 10px; font-size: 15px; font-weight: bold; margin-top: 4px; text-align: center; display: inline-block; padding: 0 9px;">送<%=uConfig.gd(userdt, "p1")  %>%</div>


                <%
                  } %>
            </div>

            <div style="width: 50%;">
                <span style="font-size: 14px; color: #7a7b7c;">收款方式</span>
                <div style="font-size: 22px; color: gray; margin-top: 5px;">
                    <%=uConfig.gd(userdt, "payment_type").Replace("网银", "银行卡") %>
                </div>
            </div>

        </div>

    </div>




    <div style="display: flex; align-items: center; margin-bottom: 15px; color: #1A1F33;">
        <span style="display: inline-block; border: 2px solid #685EF4; border-radius: 50%; padding: 2px; width: 0px; height: 0px; margin-right: 8px;"></span>
        <%=uConfig.gd(userdt, "state") == "1000" ? "请按以下交易信息，付款给对方" : "商家收款信息" %>
    </div>


    <div style="flex-shrink: 0; display: flex; flex-direction: column; justify-content: center; line-height: 21px; color: #2020f4; font-weight: bold; font-size: 14px; padding: 12px; background: #FDFC08; border-radius: 3px; margin-bottom: 18px;">
        <div>
            <svg t="1697555172443" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="1624" width="12" height="12" style="margin-right: 5px;">
                <path d="M560 800l-10.464-416h-75.072L464 800h96z m-14.144-493.984c9.44-9.312 14.144-20.672 14.144-34.016 0-13.6-4.704-24.992-14.144-34.208A46.784 46.784 0 0 0 512 224c-13.12 0-24.448 4.608-33.856 13.792A45.856 45.856 0 0 0 464 272c0 13.344 4.704 24.704 14.144 34.016 9.408 9.312 20.704 13.984 33.856 13.984 13.12 0 24.448-4.672 33.856-13.984zM512 32C246.912 32 32 246.912 32 512c0 265.088 214.912 480 480 480 265.088 0 480-214.912 480-480 0-265.088-214.912-480-480-480z m0 64c229.76 0 416 186.24 416 416s-186.24 416-416 416S96 741.76 96 512 282.24 96 512 96z" fill="blue" p-id="1625"></path></svg><span style="display: inline-block;">超时转账，损失自行承担
                   
        </span>
        </div>

    </div>



    <%if (uConfig.gd(userdt, "payment_type") == "USDT")
      {
    %>


    <div style="background: #fff; border-radius: 8px; padding: 18px; font-size: 14px; margin-bottom: 30px;">

        <div style="display: flex; margin-bottom: 25px;">
            <div style="color: gray;">充币网络</div>
            <div style="margin-left: auto; color: #3a3b3c; font-weight: bold; display: flex;">
                USDT-TRC20<svg t="1694774682876" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="6524" width="18" height="18" style="padding: 0 8px;"><path d="M512 128c-36.192 0-71.216 5.008-104.416 14.368a673.968 673.968 0 0 1 114.144 85.616 64.064 64.064 0 0 0-40.864 49.472l4.24 4a609.184 609.184 0 0 0-154.464-108.016l-0.368 0.192a385.84 385.84 0 0 0-133.664 119.248 593.44 593.44 0 0 0 89.36 125.712 64.032 64.032 0 0 0-44.208 46.416 657.728 657.728 0 0 1-81.648-106.976l-1.936 4.496A382.784 382.784 0 0 0 128 512c0 68.128 17.76 132.128 48.864 187.6A651.712 651.712 0 0 1 250.56 515.2a63.92 63.92 0 0 0 57.44 28.656l4.688-7.712a588.288 588.288 0 0 0-83.792 235.296C299.12 848.016 399.952 896 512 896c50 0 97.76-9.552 141.584-26.944a608.72 608.72 0 0 0 17.92-173.92c9.52 5.632 20.64 8.864 32.496 8.864 11.488 0 22.272-3.04 31.584-8.32a676.672 676.672 0 0 1-8.592 134.56 385.44 385.44 0 0 0 141.44-175.12l-3.472 0.8c-34.032 7.68-69.072 12.72-104.88 14.88 5.056-9.12 7.92-19.632 7.92-30.8 0-12.192-3.408-23.584-9.328-33.28a587.84 587.84 0 0 0 130.704-23.296c4.352-23.168 6.624-47.04 6.624-71.424a382.56 382.56 0 0 0-97.536-255.728l-3.328 0.096c-65.76 2.272-128.8 15.264-187.408 37.312a63.92 63.92 0 0 0-27.104-58.16c48.016-18.496 98.768-31.472 151.488-38.208A382.176 382.176 0 0 0 512 128z m139.088 476.032l-0.784 1.12A63.696 63.696 0 0 0 640 640c0 10 2.288 19.456 6.384 27.888a652.32 652.32 0 0 1-320.752-127.616 64.144 64.144 0 0 0 41.376-49.008 588.544 588.544 0 0 0 284.08 112.768z m69.648-27.152l0.288 1.408a64.064 64.064 0 0 0-62.016 16.208 604.896 604.896 0 0 0-111.072-242.624 64 64 0 0 0 53.424-35.456 668.992 668.992 0 0 1 119.36 260.48z m-240.64-292.464a63.952 63.952 0 0 0 29.024 57.248 594.496 594.496 0 0 0-143.28 121.84 64.08 64.08 0 0 0-45.36-45.36 658.576 658.576 0 0 1 155.2-131.072zM257.328 143.376l0.512-0.352 3.696-2.528-1.472 1.008 3.904-2.64 3.728-2.448a435.232 435.232 0 0 1 33.376-19.744l1.92-1.024a446.432 446.432 0 0 1 16.672-8.368l1.808-0.864c2.4-1.12 4.8-2.24 7.232-3.328l1.056-0.48A446.448 446.448 0 0 1 512 64c129.76 0 246.64 55.168 328.448 143.328l0.96 1.056c2.256 2.432 4.48 4.896 6.688 7.392l0.752 0.848c2 2.288 3.984 4.592 5.936 6.912l0.992 1.184c2.192 2.608 4.352 5.248 6.48 7.92l0.752 0.96a448.512 448.512 0 0 1 13.488 17.872A445.968 445.968 0 0 1 960 512c0 22.752-1.696 45.12-4.96 66.96-0.16 0.864-0.272 1.728-0.416 2.576l-0.528 3.376-0.56 3.36-0.544 3.056a443.776 443.776 0 0 1-6.96 32.08c-32.512 127.072-119.36 232.384-234.336 289.728l-4.528 2.24-0.32 0.16c-5.808 2.8-11.68 5.472-17.6 8.032l-1.552 0.656c-2.88 1.232-5.776 2.448-8.688 3.616l-0.96 0.384c-2.768 1.104-5.536 2.176-8.32 3.216l-0.688 0.272-2.176 0.8c-4.384 1.616-8.8 3.168-13.264 4.64l-2.88 0.96-2.4 0.768c-2.8 0.896-5.632 1.76-8.464 2.608l-2 0.592a444.352 444.352 0 0 1-8.656 2.432C591.84 954.608 552.56 960 512 960a446.128 446.128 0 0 1-281.92-99.808l-6.032-4.96-1.056-0.896a450.576 450.576 0 0 1-38.48-36.624l-1.184-1.28a450.64 450.64 0 0 1-14.752-16.736A446.192 446.192 0 0 1 64 512a445.92 445.92 0 0 1 76.56-250.56l0.48-0.72a447.568 447.568 0 0 1 19.36-26.4 450.624 450.624 0 0 1 89.136-85.44l7.792-5.504z" fill="#17191C" p-id="6525"></path><path d="M704 560a80 80 0 0 1 3.472 159.92L704 720a80 80 0 0 1-3.472-159.92L704 560z m0 48a32 32 0 1 0 0 64 32 32 0 0 0 0-64zM304 400a80 80 0 0 1 3.472 159.92L304 560a80 80 0 0 1-3.472-159.92L304 400z m0 48a32 32 0 1 0 0 64 32 32 0 0 0 0-64z m240-240a80 80 0 0 1 3.472 159.92L544 368a80 80 0 0 1-3.472-159.92L544 208z m0 48a32 32 0 1 0 0 64 32 32 0 0 0 0-64z" fill="#1062FE" p-id="6526"></path></svg>
            </div>
        </div>

        <div style="display: flex; margin-bottom: 25px;">
            <div style="color: gray;">买币地址</div>
            <div style="margin-left: auto; color: #0066EF; font-weight: bold;">
                <%=uConfig.gd(userdt,"payment_bankid") %>
                <svg t="*************" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="19366" width="18" height="18" style="padding: 0 8px; cursor: pointer;" onclick="textCopy('<%=uConfig.gd(userdt,"payment_bankid") %>')">
                    <path d="M866.133333 919.466667h-405.333333c-29.866667 0-53.333333-23.466667-53.333333-53.333334v-618.666666c0-29.866667 23.466667-53.333333 53.333333-53.333334h405.333333c29.866667 0 53.333333 23.466667 53.333334 53.333334v618.666666c0 29.866667-23.466667 53.333333-53.333334 53.333334z" fill="#69C0FF" p-id="19367"></path><path d="M770.133333 428.8h-213.333333c-12.8 0-21.333333-8.533333-21.333333-21.333333s8.533333-21.333333 21.333333-21.333334h213.333333c12.8 0 21.333333 8.533333 21.333334 21.333334s-10.666667 21.333333-21.333334 21.333333zM770.133333 556.8h-213.333333c-12.8 0-21.333333-8.533333-21.333333-21.333333s8.533333-21.333333 21.333333-21.333334h213.333333c12.8 0 21.333333 8.533333 21.333334 21.333334s-10.666667 21.333333-21.333334 21.333333zM770.133333 684.8h-213.333333c-12.8 0-21.333333-8.533333-21.333333-21.333333s8.533333-21.333333 21.333333-21.333334h213.333333c12.8 0 21.333333 8.533333 21.333334 21.333334s-10.666667 21.333333-21.333334 21.333333z" fill="#FFFFFF" p-id="19368"></path><path d="M567.466667 834.133333h-405.333334c-29.866667 0-53.333333-23.466667-53.333333-53.333333v-618.666667c0-29.866667 23.466667-53.333333 53.333333-53.333333h405.333334c29.866667 0 53.333333 23.466667 53.333333 53.333333v618.666667c0 29.866667-23.466667 53.333333-53.333333 53.333333z" fill="#1890FF" p-id="19369"></path><path d="M471.466667 343.466667h-213.333334c-12.8 0-21.333333-8.533333-21.333333-21.333334s8.533333-21.333333 21.333333-21.333333h213.333334c12.8 0 21.333333 8.533333 21.333333 21.333333s-10.666667 21.333333-21.333333 21.333334zM471.466667 471.466667h-213.333334c-12.8 0-21.333333-8.533333-21.333333-21.333334s8.533333-21.333333 21.333333-21.333333h213.333334c12.8 0 21.333333 8.533333 21.333333 21.333333s-10.666667 21.333333-21.333333 21.333334zM471.466667 599.466667h-213.333334c-12.8 0-21.333333-8.533333-21.333333-21.333334s8.533333-21.333333 21.333333-21.333333h213.333334c12.8 0 21.333333 8.533333 21.333333 21.333333s-10.666667 21.333333-21.333333 21.333334z" fill="#FFFFFF" p-id="19370"></path>
                </svg>
            </div>
        </div>

        <div style="display: flex; margin-bottom: 25px;">
            <div style="color: gray;">USDT转账金额</div>
            <div style="margin-left: auto; color: #0066EF; font-weight: bold;">
                <%=tousd(uConfig.gd(userdt,"amount")) %>
                <svg t="*************" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="19366" width="18" height="18" style="padding: 0 8px; cursor: pointer;" onclick="textCopy('<%=tousd(uConfig.gd(userdt,"amount")) %>')">
                    <path d="M866.133333 919.466667h-405.333333c-29.866667 0-53.333333-23.466667-53.333333-53.333334v-618.666666c0-29.866667 23.466667-53.333333 53.333333-53.333334h405.333333c29.866667 0 53.333333 23.466667 53.333334 53.333334v618.666666c0 29.866667-23.466667 53.333333-53.333334 53.333334z" fill="#69C0FF" p-id="19367"></path><path d="M770.133333 428.8h-213.333333c-12.8 0-21.333333-8.533333-21.333333-21.333333s8.533333-21.333333 21.333333-21.333334h213.333333c12.8 0 21.333333 8.533333 21.333334 21.333334s-10.666667 21.333333-21.333334 21.333333zM770.133333 556.8h-213.333333c-12.8 0-21.333333-8.533333-21.333333-21.333333s8.533333-21.333333 21.333333-21.333334h213.333333c12.8 0 21.333333 8.533333 21.333334 21.333334s-10.666667 21.333333-21.333334 21.333333zM770.133333 684.8h-213.333333c-12.8 0-21.333333-8.533333-21.333333-21.333333s8.533333-21.333333 21.333333-21.333334h213.333333c12.8 0 21.333333 8.533333 21.333334 21.333334s-10.666667 21.333333-21.333334 21.333333z" fill="#FFFFFF" p-id="19368"></path><path d="M567.466667 834.133333h-405.333334c-29.866667 0-53.333333-23.466667-53.333333-53.333333v-618.666667c0-29.866667 23.466667-53.333333 53.333333-53.333333h405.333334c29.866667 0 53.333333 23.466667 53.333333 53.333333v618.666667c0 29.866667-23.466667 53.333333-53.333333 53.333333z" fill="#1890FF" p-id="19369"></path><path d="M471.466667 343.466667h-213.333334c-12.8 0-21.333333-8.533333-21.333333-21.333334s8.533333-21.333333 21.333333-21.333333h213.333334c12.8 0 21.333333 8.533333 21.333333 21.333333s-10.666667 21.333333-21.333333 21.333334zM471.466667 471.466667h-213.333334c-12.8 0-21.333333-8.533333-21.333333-21.333334s8.533333-21.333333 21.333333-21.333333h213.333334c12.8 0 21.333333 8.533333 21.333333 21.333333s-10.666667 21.333333-21.333333 21.333334zM471.466667 599.466667h-213.333334c-12.8 0-21.333333-8.533333-21.333333-21.333334s8.533333-21.333333 21.333333-21.333333h213.333334c12.8 0 21.333333 8.533333 21.333333 21.333333s-10.666667 21.333333-21.333333 21.333334z" fill="#FFFFFF" p-id="19370"></path>
                </svg>
            </div>
        </div>

        <div style="display: flex;">
            <div style="color: gray;">财务专员密信ID：</div>
            <div style="margin-left: auto; color: #0066EF; font-weight: bold;">
                <%=uConfig.gd(userdt,"payment_name") %>
                <svg t="*************" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="19366" width="18" height="18" style="padding: 0 8px; cursor: pointer;" onclick="textCopy('<%=uConfig.gd(userdt,"payment_name") %>')">
                    <path d="M866.133333 919.466667h-405.333333c-29.866667 0-53.333333-23.466667-53.333333-53.333334v-618.666666c0-29.866667 23.466667-53.333333 53.333333-53.333334h405.333333c29.866667 0 53.333333 23.466667 53.333334 53.333334v618.666666c0 29.866667-23.466667 53.333333-53.333334 53.333334z" fill="#69C0FF" p-id="19367"></path><path d="M770.133333 428.8h-213.333333c-12.8 0-21.333333-8.533333-21.333333-21.333333s8.533333-21.333333 21.333333-21.333334h213.333333c12.8 0 21.333333 8.533333 21.333334 21.333334s-10.666667 21.333333-21.333334 21.333333zM770.133333 556.8h-213.333333c-12.8 0-21.333333-8.533333-21.333333-21.333333s8.533333-21.333333 21.333333-21.333334h213.333333c12.8 0 21.333333 8.533333 21.333334 21.333334s-10.666667 21.333333-21.333334 21.333333zM770.133333 684.8h-213.333333c-12.8 0-21.333333-8.533333-21.333333-21.333333s8.533333-21.333333 21.333333-21.333334h213.333333c12.8 0 21.333333 8.533333 21.333334 21.333334s-10.666667 21.333333-21.333334 21.333333z" fill="#FFFFFF" p-id="19368"></path><path d="M567.466667 834.133333h-405.333334c-29.866667 0-53.333333-23.466667-53.333333-53.333333v-618.666667c0-29.866667 23.466667-53.333333 53.333333-53.333333h405.333334c29.866667 0 53.333333 23.466667 53.333333 53.333333v618.666667c0 29.866667-23.466667 53.333333-53.333333 53.333333z" fill="#1890FF" p-id="19369"></path><path d="M471.466667 343.466667h-213.333334c-12.8 0-21.333333-8.533333-21.333333-21.333334s8.533333-21.333333 21.333333-21.333333h213.333334c12.8 0 21.333333 8.533333 21.333333 21.333333s-10.666667 21.333333-21.333333 21.333334zM471.466667 471.466667h-213.333334c-12.8 0-21.333333-8.533333-21.333333-21.333334s8.533333-21.333333 21.333333-21.333333h213.333334c12.8 0 21.333333 8.533333 21.333333 21.333333s-10.666667 21.333333-21.333333 21.333334zM471.466667 599.466667h-213.333334c-12.8 0-21.333333-8.533333-21.333333-21.333334s8.533333-21.333333 21.333333-21.333333h213.333334c12.8 0 21.333333 8.533333 21.333333 21.333333s-10.666667 21.333333-21.333333 21.333334z" fill="#FFFFFF" p-id="19370"></path>
                </svg>
            </div>
        </div>
    </div>


    <%
      }
      else
      {
    %>

    <div style="background: #fff; border-radius: 8px; padding: 18px; font-size: 14px; margin-bottom: 30px;">

        <div style="display: flex; margin-bottom: 25px;">
            <div style="color: gray;"><%=uConfig.gd(userdt, "payment_type") != "支付宝" ? "银行卡号" : "支付宝账号" %></div>
            <div style="margin-left: auto; color: #0066EF; font-weight: bold;">
                <%=uConfig.gd(userdt,"payment_bankid") %>
                <svg t="*************" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="19366" width="18" height="18" style="padding: 0 8px; cursor: pointer;" onclick="textCopy('<%=uConfig.gd(userdt,"payment_bankid") %>')">
                    <path d="M866.133333 919.466667h-405.333333c-29.866667 0-53.333333-23.466667-53.333333-53.333334v-618.666666c0-29.866667 23.466667-53.333333 53.333333-53.333334h405.333333c29.866667 0 53.333333 23.466667 53.333334 53.333334v618.666666c0 29.866667-23.466667 53.333333-53.333334 53.333334z" fill="#69C0FF" p-id="19367"></path><path d="M770.133333 428.8h-213.333333c-12.8 0-21.333333-8.533333-21.333333-21.333333s8.533333-21.333333 21.333333-21.333334h213.333333c12.8 0 21.333333 8.533333 21.333334 21.333334s-10.666667 21.333333-21.333334 21.333333zM770.133333 556.8h-213.333333c-12.8 0-21.333333-8.533333-21.333333-21.333333s8.533333-21.333333 21.333333-21.333334h213.333333c12.8 0 21.333333 8.533333 21.333334 21.333334s-10.666667 21.333333-21.333334 21.333333zM770.133333 684.8h-213.333333c-12.8 0-21.333333-8.533333-21.333333-21.333333s8.533333-21.333333 21.333333-21.333334h213.333333c12.8 0 21.333333 8.533333 21.333334 21.333334s-10.666667 21.333333-21.333334 21.333333z" fill="#FFFFFF" p-id="19368"></path><path d="M567.466667 834.133333h-405.333334c-29.866667 0-53.333333-23.466667-53.333333-53.333333v-618.666667c0-29.866667 23.466667-53.333333 53.333333-53.333333h405.333334c29.866667 0 53.333333 23.466667 53.333333 53.333333v618.666667c0 29.866667-23.466667 53.333333-53.333333 53.333333z" fill="#1890FF" p-id="19369"></path><path d="M471.466667 343.466667h-213.333334c-12.8 0-21.333333-8.533333-21.333333-21.333334s8.533333-21.333333 21.333333-21.333333h213.333334c12.8 0 21.333333 8.533333 21.333333 21.333333s-10.666667 21.333333-21.333333 21.333334zM471.466667 471.466667h-213.333334c-12.8 0-21.333333-8.533333-21.333333-21.333334s8.533333-21.333333 21.333333-21.333333h213.333334c12.8 0 21.333333 8.533333 21.333333 21.333333s-10.666667 21.333333-21.333333 21.333334zM471.466667 599.466667h-213.333334c-12.8 0-21.333333-8.533333-21.333333-21.333334s8.533333-21.333333 21.333333-21.333333h213.333334c12.8 0 21.333333 8.533333 21.333333 21.333333s-10.666667 21.333333-21.333333 21.333334z" fill="#FFFFFF" p-id="19370"></path>
                </svg>
            </div>
        </div>

        <div style="display: flex; margin-bottom: 25px;">
            <div style="color: #5a5b5c;"><%=uConfig.gd(userdt, "payment_type") != "支付宝" ? "开户姓名" : "收款人姓名" %></div>
            <div style="margin-left: auto; color: #0066EF; font-weight: bold;">
                <%=uConfig.gd(userdt,"payment_name") %>
                <svg t="*************" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="19366" width="18" height="18" style="padding: 0 8px; cursor: pointer;" onclick="textCopy('<%=uConfig.gd(userdt,"payment_name") %>')">
                    <path d="M866.133333 919.466667h-405.333333c-29.866667 0-53.333333-23.466667-53.333333-53.333334v-618.666666c0-29.866667 23.466667-53.333333 53.333333-53.333334h405.333333c29.866667 0 53.333333 23.466667 53.333334 53.333334v618.666666c0 29.866667-23.466667 53.333333-53.333334 53.333334z" fill="#69C0FF" p-id="19367"></path><path d="M770.133333 428.8h-213.333333c-12.8 0-21.333333-8.533333-21.333333-21.333333s8.533333-21.333333 21.333333-21.333334h213.333333c12.8 0 21.333333 8.533333 21.333334 21.333334s-10.666667 21.333333-21.333334 21.333333zM770.133333 556.8h-213.333333c-12.8 0-21.333333-8.533333-21.333333-21.333333s8.533333-21.333333 21.333333-21.333334h213.333333c12.8 0 21.333333 8.533333 21.333334 21.333334s-10.666667 21.333333-21.333334 21.333333zM770.133333 684.8h-213.333333c-12.8 0-21.333333-8.533333-21.333333-21.333333s8.533333-21.333333 21.333333-21.333334h213.333333c12.8 0 21.333333 8.533333 21.333334 21.333334s-10.666667 21.333333-21.333334 21.333333z" fill="#FFFFFF" p-id="19368"></path><path d="M567.466667 834.133333h-405.333334c-29.866667 0-53.333333-23.466667-53.333333-53.333333v-618.666667c0-29.866667 23.466667-53.333333 53.333333-53.333333h405.333334c29.866667 0 53.333333 23.466667 53.333333 53.333333v618.666667c0 29.866667-23.466667 53.333333-53.333333 53.333333z" fill="#1890FF" p-id="19369"></path><path d="M471.466667 343.466667h-213.333334c-12.8 0-21.333333-8.533333-21.333333-21.333334s8.533333-21.333333 21.333333-21.333333h213.333334c12.8 0 21.333333 8.533333 21.333333 21.333333s-10.666667 21.333333-21.333333 21.333334zM471.466667 471.466667h-213.333334c-12.8 0-21.333333-8.533333-21.333333-21.333334s8.533333-21.333333 21.333333-21.333333h213.333334c12.8 0 21.333333 8.533333 21.333333 21.333333s-10.666667 21.333333-21.333333 21.333334zM471.466667 599.466667h-213.333334c-12.8 0-21.333333-8.533333-21.333333-21.333334s8.533333-21.333333 21.333333-21.333333h213.333334c12.8 0 21.333333 8.533333 21.333333 21.333333s-10.666667 21.333333-21.333333 21.333334z" fill="#FFFFFF" p-id="19370"></path>
                </svg>
            </div>
        </div>
        <div style="display: flex; ;<%=uConfig.gd(userdt, "payment_type") != "支付宝" ? "margin-bottom: 25px" : "" %>">
            <div style="color: #c73939;">确认姓名</div>
            <div style="margin-left: auto; color: #0066EF; font-weight: bold;">
                <%=uConfig.gd(userdt,"payment_name") %>
                <svg t="*************" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="19366" width="18" height="18" style="padding: 0 8px; cursor: pointer;" onclick="textCopy('<%=uConfig.gd(userdt,"payment_name") %>')">
                    <path d="M866.133333 919.466667h-405.333333c-29.866667 0-53.333333-23.466667-53.333333-53.333334v-618.666666c0-29.866667 23.466667-53.333333 53.333333-53.333334h405.333333c29.866667 0 53.333333 23.466667 53.333334 53.333334v618.666666c0 29.866667-23.466667 53.333333-53.333334 53.333334z" fill="#69C0FF" p-id="19367"></path><path d="M770.133333 428.8h-213.333333c-12.8 0-21.333333-8.533333-21.333333-21.333333s8.533333-21.333333 21.333333-21.333334h213.333333c12.8 0 21.333333 8.533333 21.333334 21.333334s-10.666667 21.333333-21.333334 21.333333zM770.133333 556.8h-213.333333c-12.8 0-21.333333-8.533333-21.333333-21.333333s8.533333-21.333333 21.333333-21.333334h213.333333c12.8 0 21.333333 8.533333 21.333334 21.333334s-10.666667 21.333333-21.333334 21.333333zM770.133333 684.8h-213.333333c-12.8 0-21.333333-8.533333-21.333333-21.333333s8.533333-21.333333 21.333333-21.333334h213.333333c12.8 0 21.333333 8.533333 21.333334 21.333334s-10.666667 21.333333-21.333334 21.333333z" fill="#FFFFFF" p-id="19368"></path><path d="M567.466667 834.133333h-405.333334c-29.866667 0-53.333333-23.466667-53.333333-53.333333v-618.666667c0-29.866667 23.466667-53.333333 53.333333-53.333333h405.333334c29.866667 0 53.333333 23.466667 53.333333 53.333333v618.666667c0 29.866667-23.466667 53.333333-53.333333 53.333333z" fill="#1890FF" p-id="19369"></path><path d="M471.466667 343.466667h-213.333334c-12.8 0-21.333333-8.533333-21.333333-21.333334s8.533333-21.333333 21.333333-21.333333h213.333334c12.8 0 21.333333 8.533333 21.333333 21.333333s-10.666667 21.333333-21.333333 21.333334zM471.466667 471.466667h-213.333334c-12.8 0-21.333333-8.533333-21.333333-21.333334s8.533333-21.333333 21.333333-21.333333h213.333334c12.8 0 21.333333 8.533333 21.333333 21.333333s-10.666667 21.333333-21.333333 21.333334zM471.466667 599.466667h-213.333334c-12.8 0-21.333333-8.533333-21.333333-21.333334s8.533333-21.333333 21.333333-21.333333h213.333334c12.8 0 21.333333 8.533333 21.333333 21.333333s-10.666667 21.333333-21.333333 21.333334z" fill="#FFFFFF" p-id="19370"></path>
                </svg>
            </div>
        </div>

        <%if (uConfig.gd(userdt, "payment_type") != "支付宝")
          {
              
        %>



        <div style="display: flex; margin-bottom: 25px;">
            <div style="color: gray;">开户银行</div>
            <div style="margin-left: auto; color: #0066EF; font-weight: bold;">
                <%=uConfig.gd(userdt,"payment_bankname") %>
                <svg t="*************" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="19366" width="18" height="18" style="padding: 0 8px; cursor: pointer;" onclick="textCopy('<%=uConfig.gd(userdt,"payment_bankname") %>')">
                    <path d="M866.133333 919.466667h-405.333333c-29.866667 0-53.333333-23.466667-53.333333-53.333334v-618.666666c0-29.866667 23.466667-53.333333 53.333333-53.333334h405.333333c29.866667 0 53.333333 23.466667 53.333334 53.333334v618.666666c0 29.866667-23.466667 53.333333-53.333334 53.333334z" fill="#69C0FF" p-id="19367"></path><path d="M770.133333 428.8h-213.333333c-12.8 0-21.333333-8.533333-21.333333-21.333333s8.533333-21.333333 21.333333-21.333334h213.333333c12.8 0 21.333333 8.533333 21.333334 21.333334s-10.666667 21.333333-21.333334 21.333333zM770.133333 556.8h-213.333333c-12.8 0-21.333333-8.533333-21.333333-21.333333s8.533333-21.333333 21.333333-21.333334h213.333333c12.8 0 21.333333 8.533333 21.333334 21.333334s-10.666667 21.333333-21.333334 21.333333zM770.133333 684.8h-213.333333c-12.8 0-21.333333-8.533333-21.333333-21.333333s8.533333-21.333333 21.333333-21.333334h213.333333c12.8 0 21.333333 8.533333 21.333334 21.333334s-10.666667 21.333333-21.333334 21.333333z" fill="#FFFFFF" p-id="19368"></path><path d="M567.466667 834.133333h-405.333334c-29.866667 0-53.333333-23.466667-53.333333-53.333333v-618.666667c0-29.866667 23.466667-53.333333 53.333333-53.333333h405.333334c29.866667 0 53.333333 23.466667 53.333333 53.333333v618.666667c0 29.866667-23.466667 53.333333-53.333333 53.333333z" fill="#1890FF" p-id="19369"></path><path d="M471.466667 343.466667h-213.333334c-12.8 0-21.333333-8.533333-21.333333-21.333334s8.533333-21.333333 21.333333-21.333333h213.333334c12.8 0 21.333333 8.533333 21.333333 21.333333s-10.666667 21.333333-21.333333 21.333334zM471.466667 471.466667h-213.333334c-12.8 0-21.333333-8.533333-21.333333-21.333334s8.533333-21.333333 21.333333-21.333333h213.333334c12.8 0 21.333333 8.533333 21.333333 21.333333s-10.666667 21.333333-21.333333 21.333334zM471.466667 599.466667h-213.333334c-12.8 0-21.333333-8.533333-21.333333-21.333334s8.533333-21.333333 21.333333-21.333333h213.333334c12.8 0 21.333333 8.533333 21.333333 21.333333s-10.666667 21.333333-21.333333 21.333334z" fill="#FFFFFF" p-id="19370"></path>
                </svg>
            </div>
        </div>

        <div style="display: flex;">
            <div style="color: gray;">开户城市</div>
            <div style="margin-left: auto; color: #0066EF; font-weight: bold;">
                <%=uConfig.gd(userdt,"payment_city") %>
                <svg t="*************" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="19366" width="18" height="18" style="padding: 0 8px; cursor: pointer;" onclick="textCopy('<%=uConfig.gd(userdt,"payment_city") %>')">
                    <path d="M866.133333 919.466667h-405.333333c-29.866667 0-53.333333-23.466667-53.333333-53.333334v-618.666666c0-29.866667 23.466667-53.333333 53.333333-53.333334h405.333333c29.866667 0 53.333333 23.466667 53.333334 53.333334v618.666666c0 29.866667-23.466667 53.333333-53.333334 53.333334z" fill="#69C0FF" p-id="19367"></path><path d="M770.133333 428.8h-213.333333c-12.8 0-21.333333-8.533333-21.333333-21.333333s8.533333-21.333333 21.333333-21.333334h213.333333c12.8 0 21.333333 8.533333 21.333334 21.333334s-10.666667 21.333333-21.333334 21.333333zM770.133333 556.8h-213.333333c-12.8 0-21.333333-8.533333-21.333333-21.333333s8.533333-21.333333 21.333333-21.333334h213.333333c12.8 0 21.333333 8.533333 21.333334 21.333334s-10.666667 21.333333-21.333334 21.333333zM770.133333 684.8h-213.333333c-12.8 0-21.333333-8.533333-21.333333-21.333333s8.533333-21.333333 21.333333-21.333334h213.333333c12.8 0 21.333333 8.533333 21.333334 21.333334s-10.666667 21.333333-21.333334 21.333333z" fill="#FFFFFF" p-id="19368"></path><path d="M567.466667 834.133333h-405.333334c-29.866667 0-53.333333-23.466667-53.333333-53.333333v-618.666667c0-29.866667 23.466667-53.333333 53.333333-53.333333h405.333334c29.866667 0 53.333333 23.466667 53.333333 53.333333v618.666667c0 29.866667-23.466667 53.333333-53.333333 53.333333z" fill="#1890FF" p-id="19369"></path><path d="M471.466667 343.466667h-213.333334c-12.8 0-21.333333-8.533333-21.333333-21.333334s8.533333-21.333333 21.333333-21.333333h213.333334c12.8 0 21.333333 8.533333 21.333333 21.333333s-10.666667 21.333333-21.333333 21.333334zM471.466667 471.466667h-213.333334c-12.8 0-21.333333-8.533333-21.333333-21.333334s8.533333-21.333333 21.333333-21.333333h213.333334c12.8 0 21.333333 8.533333 21.333333 21.333333s-10.666667 21.333333-21.333333 21.333334zM471.466667 599.466667h-213.333334c-12.8 0-21.333333-8.533333-21.333333-21.333334s8.533333-21.333333 21.333333-21.333333h213.333334c12.8 0 21.333333 8.533333 21.333333 21.333333s-10.666667 21.333333-21.333333 21.333334z" fill="#FFFFFF" p-id="19370"></path>
                </svg>
            </div>
        </div>


        <%

          } %>
    </div>


    <%
      } %>





    <div style="display: flex; align-items: center; margin-bottom: 15px; color: #1A1F33;">
        <span style="display: inline-block; border: 2px solid #685EF4; border-radius: 50%; padding: 2px; width: 0px; height: 0px; margin-right: 8px;"></span><%=uConfig.gd(userdt, "state") == "1000" ? "请上传付款" : "付款" %><%=pmlist["upload_type"] + "" == "video" ? "视频" : "截图" %>
    </div>
    <%=uConfig.gd(userdt, "state") == "1000" && pmlist["upload_type"] + "" == "video" ? "<div style='font-weight: bold; color: #bb5314; line-height: 28px; font-size: 13px;'>录屏规则：【百度搜索：北京时间】应用商店搜索【银行】并在应用商店打开进入您的银行APP转出和转入也一起录制<a style='color: #4f46cf; margin-left: 5px;'>查看示例</a></div>" : "" %>




    <div style="border-radius: 8px; padding: 18px; font-size: 14px; margin-top: 20px; display: flex; justify-content: center;">

        <%if (uConfig.gd(userdt, "state") == "1000")
          {
        %>


        <div id="payimg" style="background: #eee; min-height: 150px; cursor: pointer; display: flex; align-items: center; justify-content: center; color: #aaa; font-size: 17px; max-width: 150px; width: 100%;" onclick="upload_images('<%=pmlist["upload_type"] + "" == "video" ? "video/*" : "" %>')">
            <%=pmlist["upload_type"] + "" == "video" ? "<svg t='' class='icon' viewBox='0 0 1024 1024' version='1.1' xmlns='http://www.w3.org/2000/svg' p-id='5621' width='32' height='32'><path d='M927.5 540.4c13.3 0 24-10.7 24-24v-283c0-64.2-52.2-116.3-116.3-116.3H188.7c-64.2 0-116.4 52.2-116.4 116.3v554.1c0 64.2 52.2 116.3 116.4 116.3h554.1c115.1 0 208.7-93.6 208.7-208.7V625c0-29.4-15.9-55-44.7-72.4-20.9-12.6-47.7-20-71.7-20-13.3 0-24 10.7-24 24s10.7 24 24 24c11.5 0 30.8 3.5 47 13.2 14.2 8.5 21.4 19 21.4 31.2v70.1c0 88.6-72.1 160.7-160.7 160.7H188.7c-37.7 0-68.4-30.7-68.4-68.3V233.4c0-37.7 30.7-68.3 68.4-68.3h646.4c37.7 0 68.3 30.7 68.3 68.3v283c0.1 13.3 10.8 24 24.1 24z' fill='#aaaaaa' p-id='5622'></path><path d='M667.8 517.9l-9.1 5.9c-11.1 7.3-14.2 22.1-7 33.2 7.3 11.1 22.1 14.2 33.2 7l9.1-5.9c16.2-10.6 25.9-28.4 25.9-47.6 0-19.2-9.7-37-25.9-47.6L462.1 311.2c-17.9-11.7-40.8-12.7-59.6-2.5-18.5 10-30 29.2-30 50.1v303.4c0 20.9 11.5 40.1 30 50.1 8.7 4.7 18.2 7 27.8 7 11.1 0 22.2-3.2 31.9-9.5l154.5-101.1c11.1-7.3 14.2-22.1 7-33.2-7.3-11.1-22.1-14.2-33.2-7L435.8 669.6c-4.6 3-8.9 1.3-10.5 0.4-1.8-1-4.9-3.3-4.9-7.9V358.8c0-4.6 3-6.9 4.9-7.9 1.6-0.9 5.9-2.6 10.5 0.4l232 151.7c3.6 2.4 4.2 5.7 4.2 7.5-0.1 1.8-0.6 5.1-4.2 7.4z' fill='#aaaaaa' p-id='5623'></path></svg>" : "<svg t='' class='icon' viewBox='0 0 1024 1024' version='1.1' xmlns='http://www.w3.org/2000/svg' p-id='7508' width='32' height='32'><path d='M896 160H128v704h768z m-704 64h640v423.68L706.08 496l-157.12 157.12L352 358.24l-160 240z m0 576v-86.24l160-240 187.04 280.48L701.92 592 832 747.52V800z' fill='#aaaaaa' p-id='7509'></path><path d='M672 448a96 96 0 1 0-96-96 96 96 0 0 0 96 96z m0-128a32 32 0 1 1-32 32 32 32 0 0 1 32-32z' fill='#aaaaaa' p-id='7510'></path></svg>" %>

            <b style="margin-left: 5px;">上传<%=pmlist["upload_type"] + "" == "video" ? "视频" : "截图" %></b>
        </div>



        <%}
          else
          {
        %>



        <div style="background: #eee; min-height: 150px; cursor: pointer; display: flex; align-items: center; justify-content: center; color: #aaa; font-size: 17px; max-width: 150px; width: 100%;" onclick="toggleImgFullScreen('<%=uConfig.gd(userdt, "payimg") %>')">

            <%if (pmlist["upload_type"] + "" == "video")
              {
            %>
            <b v-url="<%=uConfig.gd(userdt, "payimg") %>">视频已上传</b>
            <%

              }
              else
              {
            %>
            <img style="width: 100%; max-height: 100%;" src="<%=uConfig.gd(userdt, "payimg") %>">
            <%
                
              } %>
        </div>



        <%
          } %>
    </div>

    <%if (uConfig.gd(userdt, "state") == "1000" && uConfig.gd(userdt, "error_reason") != "")
      {
    %>
    <div style="color: #ff0018; text-align: center; margin-bottom: 22px;">退回原因：<%=uConfig.gd(userdt, "error_reason") %></div>
    <%
      } %>


    <%if (uConfig.gd(userdt, "state") == "1000")
      {
    %>

    <div style="z-index: -1; background: #e8e8f8; color: #485280; line-height: 24px; font-size: 13px; padding: 8px 12px; border-radius: 8px; font-weight: bold; margin: 10px 0; display: flex; align-items: center;">
        <svg t="1692474480503" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="54328" width="13" height="13">
            <path d="M512 0C229.665391 0 0 229.665391 0 512 0 614.578087 30.230261 713.594435 87.462957 798.274783 97.792 813.568 118.53913 817.574957 133.832348 807.268174 149.103304 796.93913 153.132522 776.169739 142.803478 760.898783 93.072696 687.282087 66.782609 601.221565 66.782609 512 66.782609 266.50713 266.50713 66.782609 512 66.782609 757.49287 66.782609 957.217391 266.50713 957.217391 512 957.217391 757.49287 757.49287 957.217391 512 957.217391 420.685913 957.217391 332.933565 929.792 258.248348 877.879652 243.044174 867.350261 222.274783 871.067826 211.767652 886.227478 201.238261 901.36487 204.978087 922.178783 220.115478 932.685913 306.064696 992.434087 406.995478 1024 512 1024 794.334609 1024 1024 794.334609 1024 512 1024 229.665391 794.334609 0 512 0ZM512.004452 237.895235C475.118191 237.895235 445.221843 267.791583 445.221843 304.677843 445.221843 341.564104 475.118191 371.460452 512.004452 371.460452 548.890713 371.460452 578.787061 341.564104 578.787061 304.677843 578.787061 267.791583 548.890713 237.895235 512.004452 237.895235ZM512 429.935304C481.257739 429.935304 456.347826 454.845217 456.347826 485.587478L456.347826 752.717913C456.347826 783.460174 481.257739 808.370087 512 808.370087 542.742261 808.370087 567.652174 783.460174 567.652174 752.717913L567.652174 485.587478C567.652174 454.845217 542.742261 429.935304 512 429.935304Z" fill="#3d50df" p-id="54329"></path></svg>&nbsp;&nbsp;转账完成后请务必点击下方按钮，通知卖家收款
   
    </div>


    <a style="display: block; background: #685EF4; color: #fff; width: 100%; padding: 16px; box-sizing: border-box; border-radius: 10px; cursor: pointer; text-align: center; font-size: 14px; margin-top: 28px;" onclick="update_pay()">我已完成付款，下一步</a>
    <div style="text-align: center; margin-top: 10px;">
        <a style="color: #4f46cf; font-size: 14px; padding: 10px 12px; display: inline-block; margin-top: 10px; cursor: pointer;" onclick="cancen_pay()">取消订单</a>
    </div>

    <%}
      else
      {
    %>

    <div style="display: flex;">
        <div style="padding: 10px; width: 100%;">
            <a style="display: block; background: #dce7fd; color: #3a95e7; width: 100%; padding: 16px; box-sizing: border-box; border-radius: 10px; cursor: pointer; text-align: center; font-size: 14px; margin-top: 28px; text-decoration: none;" href="index.aspx">返回首页</a>
        </div>

        <div style="padding: 10px; width: 100%;">
            <a style="display: block; background: #3C8BF4; color: #fff; width: 100%; padding: 16px; box-sizing: border-box; border-radius: 10px; cursor: pointer; text-align: center; font-size: 14px; margin-top: 28px; text-decoration: none;" href="account.aspx">查看余额</a>
        </div>

    </div>


    <%
      } %>



    <div style="height: 20px;"></div>


    <script>

        var upload_images = function (accept) {
            file_upload(function (e) {
                if (e.success) {
                    if (accept == "video/*") {
                        $('#payimg').html('<b v-url="' + e.imgurl + '">视频已上传</b>')
                    } else {
                        $('#payimg').html('<img style="width:100%;max-height:100%;" src="' + e.imgurl + '">')
                    }
                }

            }, accept)
        }
        var upload_type = '<%=pmlist["upload_type"] + ""%>';
        var update_pay = function () {
            //security_password(function (e) {

            var payimg = $('#payimg img').attr('src');
            if (upload_type == "video") {
                payimg = $('#payimg b').attr('v-url');
            }

            v3api("upload_buyOrder", {
                data: {
                    //paypwd: e.password,
                    upload_type: upload_type,
                    payimg: payimg,
                    id: '<%=uConfig.gd(userdt,"id") %>'
                }
            }, function () {
                location.href = location.href;
            })
            //})
        }

        var cancen_pay = function () {
            security_password(function (e) {

                v3api("cancel_buyOrder", {
                    data: {
                        paypwd: e.password,
                        payimg: $('#payimg img').attr('src'),
                        id: '<%=uConfig.gd(userdt,"id") %>'
                    }
                }, function () {
                    location.href = "dating.aspx";
                })
            }, '<div style="color: red; padding: 0 30px; margin-top: 20px; font-weight: bold;text-align:center;" class="animated-text">取消订单后无法撤销，请谨慎操作！</div>')
        }

    </script>

</asp:Content>


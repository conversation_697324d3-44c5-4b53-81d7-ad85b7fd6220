using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

public partial class order_list : baseClass
{
    public DataTable userdt = new DataTable();
    protected void Page_Load(object sender, EventArgs e)
    {
        List<SqlParameter> pams = new List<SqlParameter>();
        dbClass db = new dbClass();



        if (uConfig.stcdata("dating_mode") == "api")
        {
            try
            {
                string temp = datingApi("operator_data", "id=" + Request.QueryString["id"]); 
                userdt = TransToDatatable(temp);
            }
            catch (Exception)
            {

            }
        }
        else
        {
            pams.Add(new SqlParameter("@id", Request.QueryString["id"] + ""));
            userdt = db.getDataTable("select * from buy_list with(nolock) where orderId=@id and payment_type='银行卡' ", pams.ToArray());
        }
    }
}
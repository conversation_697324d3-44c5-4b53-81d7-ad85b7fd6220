<%@ Page Title="" Language="C#" MasterPageFile="~/top.master" AutoEventWireup="true" CodeFile="pay2mch.aspx.cs" Inherits="order_list" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="Server">
    <%--   <script>
        $(function () {
            popTitle('当前余额：<span id="user_amount"><%=Convert.ToDouble(uConfig.gd(userdt,"amount")).ToString("0.00") %></span><img src="/static/images/coin.png" alt="" height="12" width="12" style="margin-left: 5px;">', '', 'gateway_list.aspx');
        })
    </script>--%>

    <style>
        .top-title {
            background: none;
        }

        .ulk_menus {
            display: none;
        }
    </style>
    <style>
        body {
            background: linear-gradient(45deg, #F0F4F9, #F5F5F8);
            background-repeat: no-repeat;
            height: 100vh;
        }

        .main-container {
            padding-top: 0px;
        }

        .select_tab {
            color: #000;
            box-sizing: border-box;
            padding: 12px 20px;
            text-align: center;
            font-weight: bold;
            font-size: 13px;
            background: #eee;
            border-radius: 12px;
            width: 50%;
            margin: 0 10px;
        }

            .select_tab.activity {
                background: #3c3351;
                color: #fff;
                transition: all 0.2s;
            }

        .theme-green .back-bar .selected-bar {
            background-color: #3838f5!important;
            background-image: linear-gradient(to bottom, #4747ff, #3838f5)!important;
        }

        .slider-container {
            margin-bottom: 38px;
            margin-top: 52px;
        }


        .paying {
            background: #8c8c9d!important;
        }

        @keyframes rotate360 {
            from {
                transform: rotate(0deg);
            }

            to {
                transform: rotate(360deg);
            }
        }
        /* 应用旋转动画到元素 */
        .rotate-element {
            animation: rotate360 5s linear infinite; /* 旋转2秒，线性速度，无限循环 */
            margin-right: 5px;
            width: 14px;
            height: 14px;
        }

            .rotate-element path {
                fill: #fff;
            }

        input[disabled] {
            color: #000;
            font-weight: bold;
        }
    </style>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="Server">




    <div style="position: fixed; display: flex; top: 0; width: 100%; flex-direction: column; height: 100%; z-index: 9; max-width: 600px; left: 50%; transform: translate(-50%, 0); padding: 10px; box-sizing: border-box;">

        <div class="top-title" style="display: block; position: relative; height: 39px; line-height: 39px; padding: 0;">

            <a class="ppreturn" style="position: absolute; left: 0; height: 100%; width: 50px; top: 0; display: flex; align-items: center; justify-content: center;" onclick="javascript:history.go(-1);">
                <svg t="" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="27857" width="16" height="16">
                    <path d="M716.8 972.8a51.2 51.2 0 0 1-36.352-14.848l-409.6-409.6a51.2 51.2 0 0 1 0-72.192l409.6-409.6a51.2 51.2 0 0 1 72.192 72.192L379.392 512l373.248 373.248A51.2 51.2 0 0 1 716.8 972.8z" fill="#000" p-id="27858"></path></svg></a>

            <b class="pptitle">当前余额：<span id="user_amount"><%=Convert.ToDouble(uConfig.gd(userdt,"amount","0.00")).ToString("0.00") %></span><img src="/static/images/coin.png" alt="" height="12" width="12" style="margin-left: 5px;"></b>

            <div class="prhtml"></div>
        </div>




        <div id="rechargeTop" style="background: #fff; border-radius: 8px; padding: 18px; font-size: 14px; margin-top: 18px;">

            <%if (!uConfig.isLogin)
              {
            %>



            <div style="text-align: left; margin-bottom: 14px; font-size: 17px; display: flex; align-items: center;">充值账号</div>


            <div style="position: relative; /* background: #f5f5f5; */border-radius: 8px; margin-bottom: 30px; display: ;">

                <input style="border: 0px; background: none; font-size: 22px; padding: 13px 0; outline: none; width: 100%; box-sizing: border-box; text-align: left;" id="recharge_user" value="" placeholder="请输入充值的账号">
                <a style="position: absolute; top: 8px; right: 5px; color: #fff; display: flex; align-items: center; /* width: 83px; */height: 37px; border-radius: 38px; justify-content: center; cursor: pointer;"></a>
            </div>




            <%--        <div style="text-align: left; margin-bottom: 14px; font-size: 17px; display: flex; align-items: center;">通道ID（channel_id）</div>


        <div style="position: relative; /* background: #f5f5f5; */border-radius: 8px; margin-bottom: 30px; display: ;">

            <input style="border: 0px; background: none; font-size: 22px; padding: 13px 0; outline: none; width: 100%; box-sizing: border-box; text-align: left;" id="channel_id" value="" placeholder="请输入通道ID">
            <a style="position: absolute; top: 8px; right: 5px; color: #fff; display: flex; align-items: center; /* width: 83px; */height: 37px; border-radius: 38px; justify-content: center; cursor: pointer;"></a>
        </div>--%>



            <%
              } %>


            <div style="text-align: left; margin-bottom: 10px; font-size: 17px; display: flex; align-items: center;">
                充值金额
            </div>


            <div style="position: relative; border-radius: 8px; margin-bottom: 2px; display: ;">

                <input style="border: 0px; background: none; font-size: 22px; outline: none; width: 100%; box-sizing: border-box; text-align: left;" id="sell_number" value="100" <%=Request.QueryString["paytype"] + "" == "收银台" || Request.QueryString["paytype"] + "" == "支付宝转账" ? "disabled=\"disabled\"" : "" %>>
                <a style="position: absolute; top: 8px; right: 5px; color: #fff; display: flex; align-items: center; /* width: 83px; */height: 37px; border-radius: 38px; justify-content: center; cursor: pointer;">
                    <img src="/static/images/coin.png" alt="" height="15" width="15" style="margin-left: 5px;">
                </a>
            </div>





            <div style="display: flex;">
                <div style="color: gray;">到账金额为提交订单的金额</div>
                <div style="margin-left: auto; color: #000; font-weight: bold;"></div>
            </div>

        </div>





        <div id="moneysPage" style="background: #fff; border-radius: 8px; padding: 18px; font-size: 14px; margin-top: 6px;">


            <style>
                #regular_money_list {
                    position: relative;
                    border-radius: 8px;
                    margin-bottom: 12px;
                    display: flex;
                    padding: 0 5px;
                    flex-wrap: wrap;
                    /*max-height: 200px;
                overflow-y: auto;*/
                }

                .regular_money {
                    width: 25%;
                    margin: 5px 0;
                }

                    .regular_money .money_item {
                        background: #fff;
                        border: 1px solid #eee;
                        text-align: center;
                        margin: 0 3px;
                        padding: 10px 0;
                        cursor: pointer;
                        border-radius: 5px;
                    }

                    .regular_money .money_value {
                        font-weight: bold;
                        color: #5a5b5c;
                    }

                    .regular_money .money_desc {
                        font-size: 12px;
                        color: gray;
                    }


                    .regular_money .money_item.active {
                        border: 1px solid #6a6ae5;
                        background: #6a6ae5;
                        color: #fff;
                    }

                        .regular_money .money_item.active .money_value {
                            color: #fff;
                        }

                        .regular_money .money_item.active .money_desc {
                            color: #eee;
                        }
            </style>

            <div id="regular_money_list">
            </div>

        </div>

        <div id="recpage" style="background: rgb(255, 255, 255); border-radius: 8px; margin-top: 6px; padding-top: 8px; display: none;">

            <div style="background: #FEF0F0; border: 1px solid #FEE2E8; color: #E21631; border-radius: 8px; margin: 0 18px; padding: 11px; text-align: center; font-weight: bold; display: flex; align-items: center; justify-content: center; margin-bottom: 10px;">
                <svg t="" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="10992" width="22" height="22" style="margin-right: 7px;">
                    <path d="M512 64C262.4 64 64 262.4 64 512s198.4 448 448 448 448-198.4 448-448-198.4-448-448-448z m51.2 716.8c0 32-19.2 51.2-51.2 51.2s-51.2-19.2-51.2-51.2V486.4c0-32 19.2-51.2 51.2-51.2s51.2 19.2 51.2 51.2v294.4z m25.6-486.4c0 44.8-38.4 76.8-76.8 76.8s-76.8-32-76.8-76.8 32-83.2 76.8-83.2 76.8 38.4 76.8 83.2z" fill="#E21631" p-id="10993"></path></svg>
                请选择以下金额！进行充值！
            </div>

            <%--<div style="display: flex; padding: 18px;">
            <input id="money_keyword" style="width: 100%; border: 1px solid #ddd; outline: none; padding: 0 12px;"><a style="width: 41px; height: 38px; background: #108DE9; display: flex; align-items: center; justify-content: center; cursor: pointer;" onclick="searchMoneys()"><svg t="" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="9660" width="14" height="14"><path d="M716.29895 806.808621C641.509798 862.822176 548.629446 896 448 896 200.576432 896 0 695.423565 0 448 0 200.576432 200.576432 0 448 0 695.423565 0 896 200.576432 896 448 896 548.629446 862.822176 641.509798 806.808621 716.29895L946.011456 855.501786C970.889978 880.380314 970.970976 920.635366 945.803174 945.803174 920.809619 970.79673 879.927322 970.436992 855.501786 946.011456L716.29895 806.808621ZM448 768C624.73112 768 768 624.73112 768 448 768 271.26888 624.73112 128 448 128 271.26888 128 128 271.26888 128 448 128 624.73112 271.26888 768 448 768Z" fill="#ffffff" p-id="9661"></path></svg></a>
        </div>--%>


            <table class="tb2" style="width: 100%;">
                <thead>
                    <tr>
                        <th>推荐金额</th>
                        <th>收款方式</th>
                        <th>操作</th>
                    </tr>
                </thead>
            </table>
            <div style="max-height: calc(100vh - 180px); overflow-y: auto;">

                <table class="tb2" id="reclist" style="width: 100%;">
                    <tbody>
                        <tr style="border-bottom: 1px solid #eee;">
                            <td style="color: rgb(255 103 0); font-size: 22px; font-weight: 500;">2600</td>
                            <td>
                                <img src="/static/images/pay/ali.png" />
                                <img src="../static/images/pay/wxp.png">
                                <img src="../static/images/pay/bank.png">
                                <img src="../static/images/pay/eny.png">
                            </td>
                            <td><a style="color: rgb(255 145 71 ); border: 1px solid rgb(255 145 71 ); border-radius: 0.25rem; padding: 0.25rem 0.5rem; font-size: 14px; cursor: pointer;" onclick="javascript:$(this).addClass('active').closest('.regular_money').siblings().find('.money_item').removeClass('active');$('#sell_number').val('2600');">确认</a>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
        <style>
            .tb2 {
                border-collapse: collapse; /* 合并边框 */
                table-layout: fixed;
            }

                .tb2 img {
                    height: 18px;
                    margin: 0 1px;
                }

                .tb2 th {
                    background: #f3f3f3;
                    font-weight: 100;
                    font-size: 13px;
                    padding: 9px 0;
                    color: #000;
                }

                .tb2 td {
                    text-align: center;
                    padding: 8px 0;
                }
        </style>


        <script>
            $(function () {
                var regular_money_list = '<%=uConfig.stcdata("regular_money_list").Replace("\n", ",") %>';
                regular_money_list = regular_money_list.split(',');
                console.log('regular_money_list', regular_money_list);

                if ($('.money_item').length == 0) {


                    $('#regular_money_list').html('');

                    for (var i = 0; i < regular_money_list.length; i++) {
                        $('#regular_money_list').append('<div class="regular_money">                <div class="money_item" onclick="javascript:$(this).addClass(\'active\').closest(\'.regular_money\').siblings().find(\'.money_item\').removeClass(\'active\');$(\'#sell_number\').val(\'' + regular_money_list[i] + '\');">                    <div class="money_value">' + regular_money_list[i] + '</div>                                   </div>            </div>');

                        if (i == 0) {
                            $('#sell_number').val(regular_money_list[i]);
                        }
                    }

                }
            })
        </script>


        <style>
            .create_button {
                display: flex;
                background: #3838f5;
                color: #fff;
                width: 100%;
                padding: 16px;
                box-sizing: border-box;
                border-radius: 10px;
                cursor: pointer;
                text-align: center;
                font-size: 14px;
                margin-top: 13px;
                text-decoration: none;
                align-items: center;
                justify-content: center;
            }

                .create_button .icon {
                    display: none;
                }


            .paying .icon {
                display: block;
            }



            .paytype_list {
                background: #fff;
                width: 100%;
                display: flex;
                padding: 15px 25px;
                box-sizing: border-box;
                align-items: center;
                border-radius: 3px;
                margin-bottom: 2px;
                font-size: 13px;
            }
        </style>







        <div id="paytype_select" style="margin-top: 18px;">
            <div class="paytype_list" onclick="select_pay(this,'美宜佳')">
                <div style="margin-right: 10px;">
                    <svg t="1695126716921" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="9034" width="32" height="32">
                        <path d="M894.509511 249.605689H330.752a37.660444 37.660444 0 0 0-37.546667 37.762844v342.448356a37.660444 37.660444 0 0 0 37.546667 37.762844h563.757511a37.660444 37.660444 0 0 0 37.558045-37.762844V287.368533a37.660444 37.660444 0 0 0-37.558045-37.762844z" fill="#CCCCCC" p-id="9035"></path><path d="M293.216711 333.585067H932.067556v97.655466H293.216711z" fill="#4D4D4D" p-id="9036"></path><path d="M688.685511 388.278044H124.928a37.660444 37.660444 0 0 0-37.546667 37.762845v342.448355a37.660444 37.660444 0 0 0 37.546667 37.762845h563.757511a37.660444 37.660444 0 0 0 37.546667-37.762845V426.040889a37.660444 37.660444 0 0 0-37.546667-37.762845z" fill="#FFCA6C" p-id="9037"></path><path d="M87.381333 472.257422h638.850845v97.655467H87.381333z" fill="#4D4D4D" p-id="9038"></path><path d="M213.595022 692.974933a58.595556 58.254222 90 1 0 116.508445 0 58.595556 58.254222 90 1 0-116.508445 0Z" fill="#47A7DD" p-id="9039"></path><path d="M155.3408 692.974933a58.595556 58.254222 90 1 0 116.508444 0 58.595556 58.254222 90 1 0-116.508444 0Z" fill="#FC583D" p-id="9040"></path><path d="M894.509511 234.951111H720.406756c-8.044089 0-14.563556 6.5536-14.563556 14.6432s6.519467 14.654578 14.563556 14.654578h174.102755c12.686222 0 22.994489 10.376533 22.994489 23.131022v31.561956H307.768889V287.379911c0-12.754489 10.308267-23.131022 22.994489-23.131022H671.857778c8.044089 0 14.552178-6.564978 14.552178-14.654578S679.913244 234.951111 671.869156 234.951111h-341.105778c-28.740267 0-52.1216 23.517867-52.1216 52.417422v86.254934H124.928c-28.728889 0-52.110222 23.517867-52.110222 52.417422V663.665778c0 8.100978 6.519467 14.654578 14.563555 14.654578 8.044089 0 14.563556-6.564978 14.563556-14.654578v-79.086934h609.723733v183.9104c0 12.743111-10.308267 23.108267-22.983111 23.108267H124.928a23.074133 23.074133 0 0 1-22.983111-23.108267v-55.990044c0-8.0896-6.519467-14.6432-14.563556-14.6432-8.044089 0-14.563556 6.5536-14.563555 14.6432v55.990044c0 28.899556 23.381333 52.406044 52.110222 52.406045h563.757511c28.728889 0 52.110222-23.506489 52.110222-52.406045V426.040889c0-28.899556-23.381333-52.417422-52.110222-52.417422H307.780267v-25.383823h609.735111v68.357689H772.846933c-8.044089 0-14.563556 6.5536-14.563555 14.6432s6.519467 14.654578 14.563555 14.654578h144.668445v183.9104a23.096889 23.096889 0 0 1-22.994489 23.131022H774.781156c-8.044089 0-14.552178 6.5536-14.552178 14.6432s6.508089 14.6432 14.552178 14.6432h119.728355c28.728889 0 52.1216-23.506489 52.1216-52.417422V287.379911C946.631111 258.468978 923.249778 234.951111 894.509511 234.951111z m-182.840889 191.089778v31.573333H178.642489c-8.044089 0-14.563556 6.5536-14.563556 14.6432s6.519467 14.654578 14.563556 14.654578h533.026133v68.357689H101.944889v-68.357689h28.16c8.044089 0 14.563556-6.564978 14.563555-14.654578s-6.519467-14.6432-14.563555-14.6432H101.944889v-31.573333c0-12.743111 10.308267-23.119644 22.983111-23.119645h563.757511a23.096889 23.096889 0 0 1 22.983111 23.119645z" fill="" p-id="9041"></path><path d="M242.744889 760.069689a72.100978 72.100978 0 0 0 29.104355 6.155378c40.152178 0 72.817778-32.8704 72.817778-73.250134 0-40.402489-32.6656-73.250133-72.817778-73.250133-10.069333 0-19.979378 2.127644-29.104355 6.132622a72.078222 72.078222 0 0 0-29.149867-6.132622c-40.152178 0-72.817778 32.847644-72.817778 73.250133 0 40.379733 32.6656 73.250133 72.817778 73.250134 10.365156 0 20.218311-2.218667 29.149867-6.155378z m72.795022-67.094756c0 24.223289-19.603911 43.9296-43.690667 43.9296h-0.034133a73.056711 73.056711 0 0 0 14.609067-43.9296 73.079467 73.079467 0 0 0-14.609067-43.952355h0.034133c24.098133 0 43.690667 19.706311 43.690667 43.952355z m-145.624178 0c0-24.246044 19.592533-43.952356 43.690667-43.952355 24.086756 0 43.690667 19.706311 43.690667 43.952355 0 24.223289-19.603911 43.9296-43.690667 43.9296-24.098133 0.011378-43.690667-19.706311-43.690667-43.9296zM655.633067 647.5776c8.032711 0 14.563556-6.5536 14.563555-14.6432s-6.530844-14.6432-14.563555-14.6432H440.103822c-8.044089 0-14.563556 6.5536-14.563555 14.6432s6.519467 14.6432 14.563555 14.6432h215.529245z" fill="" p-id="9042"></path></svg>
                </div>
                <span>支付</span>
                <div class="select_icon" style="margin-left: auto;">
                    <svg t="1695127337616" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="11440" width="25" height="25">
                        <path d="M218.47269380045435 793.6915279012346c-9.468623156464195 9.468623156464195-9.468623156464195 23.67155659662222 0 33.14017975308642 82.85044938271605 78.11613780448396 189.37245536205432 123.09209585588148 302.9959293560098 123.09209585588148 241.4498814280691 0 437.92380351020245-196.47392337667162 437.92380351020245-437.92380351020245S762.9185032899951 74.07619648979755 521.4686231564642 74.07619648979755 83.54481835172345 270.55011857193097 83.54481835172345 512c0 82.85044938271605 23.67155659662222 165.7008987654321 68.647515942558 236.71556984983704 7.101466720079011 11.83577829831111 21.304401454775306 14.202934734696296 33.14017975308642 7.101466720079011 11.83577829831111-7.101466720079011 14.202934734696296-21.304401454775306 7.101466720079011-33.14017975308642-40.24164647316543-61.54604792794074-61.54604792794074-134.92787415419258-61.54604792794074-210.6768568168296 0-215.41116839506174 175.16952192189632-390.580690316958 390.580690316958-390.580690316958S912.0493121788841 296.58883160493826 912.0493121788841 512s-175.16952192189632 390.580690316958-390.58068902241973 390.580690316958c-101.78769440110615 0-196.47392337667162-37.87449133131852-269.8557496029234-108.88916241572346-9.468623156464195-9.468623156464195-26.038713033007408-9.468623156464195-33.14017975308642 0z" fill="" p-id="11441"></path><path d="M481.22697538876054 677.700898765432c7.101466720079011 0 11.83577829831111-2.3671551418469137 16.57008987654321-7.101466720079011l248.55134814814815-248.55134814814815c9.468623156464195-9.468623156464195 9.468623156464195-23.67155659662222 0-33.14017975308642s-23.67155659662222-9.468623156464195-33.14017975308642 0l-231.98125827160493 231.98125827160493-153.8651191725827-153.865120467121c-9.468623156464195-9.468623156464195-23.67155659662222-9.468623156464195-33.14017975308642 0s-9.468623156464195 23.67155659662222 0 33.14017975308642l170.43520904912594 170.43521034366415c4.734311578232098 4.734311578232098 11.83577829831111 7.101466720079011 16.57008987654321 7.101466720079011z" fill="" p-id="11442"></path></svg>
                </div>
            </div>
        </div>



        <script id="channel_list" type="text/javascript"><%=ToJson(chelper.gdt("channel_list"))  %></script>
        <script>


            var current_channelId = "<%=Request.QueryString["channel_id"] %>";
            var current_paytype = "<%=Request.QueryString["paytype"] %>";
            var current_rules = "<%=Request.QueryString["rules"] %>";

            var select_pay = function (obj, name, rules, moneys) {

                if (name.indexOf("channel_") != -1) {
                    current_channelId = name.replace(/channel_/g, "");
                    var lst = current_channelId.split('_');

                    current_paytype = lst[0];
                    current_channelId = lst[1];

                } else {
                    current_paytype = name;
                }

                if (rules != "") {
                    current_rules = rules;
                }
                if (moneys != "") {

                    temp_array = moneys.split(',');
                    $('#regular_money_list').html('');
                    console.log('list', temp_array);
                    for (var i = 0; i < temp_array.length; i++) {
                        $('#regular_money_list').append('<div class="regular_money">                <div class="money_item" onclick="javascript:$(this).addClass(\'active\').closest(\'.regular_money\').siblings().find(\'.money_item\').removeClass(\'active\');$(\'#sell_number\').val(\'' + temp_array[i] + '\');">                    <div class="money_value">' + temp_array[i] + '</div>                                   </div>            </div>');


                        if (i == 0) {
                            $('#sell_number').val(temp_array[i]);
                        }
                    }
                }


                $(obj).siblings().find(".select_icon").html(unselicon);
                $(obj).find(".select_icon").html(selicon);

            }

            $('#paytype_select').html('');
            var lst = JSON.parse($('#channel_list').html());
            var selicon = '<svg t="" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="5118" width="50" height="50"><path d="M435.2 691.2c-20.48 0-38.4-7.68-53.76-23.04l-102.4-102.4c-30.72-30.72-30.72-79.36 0-107.52 30.72-30.72 79.36-30.72 107.52 0l53.76 53.76 202.24-161.28c33.28-25.6 81.92-20.48 107.52 12.8s20.48 81.92-12.8 107.52l-256 204.8c-12.8 10.24-28.16 15.36-46.08 15.36" fill="red" p-id="5119"></path></svg>';
            var unselicon = '<svg t="" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="5118" width="50" height="50"><path d="M435.2 691.2c-20.48 0-38.4-7.68-53.76-23.04l-102.4-102.4c-30.72-30.72-30.72-79.36 0-107.52 30.72-30.72 79.36-30.72 107.52 0l53.76 53.76 202.24-161.28c33.28-25.6 81.92-20.48 107.52 12.8s20.48 81.92-12.8 107.52l-256 204.8c-12.8 10.24-28.16 15.36-46.08 15.36" fill="#dddddd" p-id="5119"></path></svg>';
            var isSelect = false;
            for (var i = 0; i < lst.length; i++) {
                var temp_lst = lst[i];
                console.log('temp_lst', temp_lst);
                console.log(i, temp_lst, temp_lst.paytype.toLowerCase(), current_paytype.toLowerCase())
                if (temp_lst.paytype.toLowerCase() == current_paytype.toLowerCase()) {
                    var __icon = unselicon;
                    if (isSelect == false) {
                        isSelect = true;
                        __icon = selicon;
                        select_pay(null, 'channel_' + temp_lst.paytype + '_' + temp_lst.code, temp_lst.moneyLimits, temp_lst.defaultMoneys);
                        console.log('icon', 'channel_' + temp_lst.paytype + '_' + temp_lst.code, temp_lst.moneyLimits, temp_lst.defaultMoneys);
                    }


                    $('#paytype_select').append('<div class="paytype_list" onclick="select_pay(this,\'channel_' + temp_lst.paytype + '_' + temp_lst.code + '\',\'' + temp_lst.moneyLimits + '\',\'' + temp_lst.defaultMoneys + '\')">                <div style="margin-right: 10px;">                    <img src="../static/images/pay/' + temp_lst.paytype + '.png?r=1" width="25" height="25">                </div>                <span>' + temp_lst.name + '</span>     ' + '<span style="color:gray;max-width: 118px;text-wrap: nowrap;overflow: hidden;text-overflow: ellipsis;">（' + temp_lst.recharge_reamrk + '）</span>' + '           <div class="select_icon" style="margin-left: auto;">                    ' + __icon + '               </div>            </div>');

                }
            }
        </script>




        <a onclick="new_advert()" class="create_button">
            <svg t="1694330145365" class="icon rotate-element" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="14297" width="22" height="22">
                <path d="M511.49 294.67c-16.57 0-30-13.43-30-30V129.21c0-16.57 13.43-30 30-30s30 13.43 30 30v135.46c0 16.57-13.44 30-30 30zM510.45 927.13c-16.57 0-30-13.43-30-30V761.67c0-16.57 13.43-30 30-30s30 13.43 30 30v135.46c0 16.57-13.43 30-30 30zM265.66 540.28H130.2c-16.57 0-30-13.43-30-30s13.43-30 30-30h135.46c16.57 0 30 13.43 30 30s-13.43 30-30 30zM892.81 540.28H757.35c-16.57 0-30-13.43-30-30s13.43-30 30-30h135.46c16.57 0 30 13.43 30 30s-13.43 30-30 30zM263.14 809.9c-7.68 0-15.36-2.93-21.21-8.79-11.72-11.72-11.72-30.71 0-42.43l95.79-95.79c11.72-11.72 30.71-11.72 42.43 0 11.72 11.72 11.72 30.71 0 42.43l-95.79 95.79c-5.86 5.86-13.54 8.79-21.22 8.79zM706.6 366.44c-7.68 0-15.36-2.93-21.21-8.79-11.72-11.72-11.72-30.71 0-42.43l95.79-95.79c11.71-11.71 30.71-11.71 42.43 0 11.72 11.72 11.72 30.71 0 42.43l-95.79 95.79c-5.86 5.86-13.54 8.79-21.22 8.79zM781.13 809.9c-7.68 0-15.35-2.93-21.21-8.79l-95.79-95.79c-11.72-11.72-11.72-30.71 0-42.43 11.72-11.72 30.71-11.71 42.43 0l95.79 95.79c11.72 11.72 11.72 30.71 0 42.43a29.95 29.95 0 0 1-21.22 8.79zM337.67 366.44c-7.68 0-15.35-2.93-21.21-8.79l-95.79-95.79c-11.72-11.72-11.72-30.71 0-42.43 11.72-11.72 30.71-11.71 42.43 0l95.79 95.79c11.72 11.72 11.72 30.71 0 42.43a29.933 29.933 0 0 1-21.22 8.79z" p-id="14298" fill="#000"></path></svg>
            <span>立即充值</span></a>





        <div class="demo-popup-page payway-select" style="display: none;">
            <div style="padding: 18px; font-size: 13px;">

                <h4 style="color: #3C54D1; margin-bottom: 22px; font-size: 16px; display: flex;">选择收款账户
                <div style="margin-left: auto;" onclick="location.href='payway_add.aspx'">
                    <svg t="1692428311309" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="8668" width="22" height="22">
                        <path d="M642.8 548.2H383c-19.3 0-35-15.7-35-35s15.7-35 35-35h259.8c19.3 0 35 15.7 35 35s-15.7 35-35 35z" fill="#4195F9" p-id="8669"></path><path d="M512.9 678.1c-19.3 0-35-15.7-35-35V383.3c0-19.3 15.7-35 35-35s35 15.7 35 35v259.8c0 19.3-15.6 35-35 35z" fill="#4195F9" p-id="8670"></path><path d="M512.9 902.7c-52.6 0-103.6-10.3-151.6-30.6-46.4-19.6-88-47.7-123.8-83.5s-63.8-77.4-83.5-123.8c-20.3-48-30.6-99.1-30.6-151.6 0-52.6 10.3-103.6 30.6-151.6 19.6-46.4 47.7-88 83.5-123.8s77.4-63.8 123.8-83.5c48-20.3 99.1-30.6 151.6-30.6s103.6 10.3 151.6 30.6c46.4 19.6 88 47.7 123.8 83.5s63.8 77.4 83.5 123.8c20.3 48 30.6 99.1 30.6 151.6 0 52.6-10.3 103.6-30.6 151.6-19.6 46.4-47.7 88-83.5 123.8s-77.4 63.8-123.8 83.5c-48 20.3-99 30.6-151.6 30.6z m0-709c-176.2 0-319.5 143.3-319.5 319.5s143.3 319.5 319.5 319.5 319.5-143.3 319.5-319.5c0-176.1-143.3-319.5-319.5-319.5z" p-id="8671"></path></svg>
                </div>
                </h4>

                <div id="lists" style="max-height: 500px; overflow-y: auto;"></div>

            </div>
        </div>


    </div>


    <script>
        var rec_amounts = [];
        var new_advert = function (first) {
            //security_password(function (e) {


            if (current_paytype == "") {
                tp('请选择支付方式');
                return;
            }
            var _amount = parseFloat($('#sell_number').val());

            if (current_rules.indexOf('~') != -1) {

                var tmp = current_rules.split('~');
                var min = parseFloat(tmp[0]);
                var max = parseFloat(tmp[1]);

                if (_amount < min) {
                    tp('金额不在限额范围内[' + current_rules + ']');
                    return;
                }
                if (_amount > max) {
                    tp('金额不在限额范围内[' + current_rules + ']');
                    return;
                }

            } else {

                var tmp = current_rules.split(',');

                if (!tmp.includes(_amount.toString())) {
                    tp('金额不在限额范围内[' + current_rules + ']');
                    return;
                }

            }




            if ($('.create_button').hasClass("paying")) {
                return;
            }

            $('.create_button').addClass("paying");
            $('.create_button').find('span').html('<span>请求支付中</span>');


            v3api("new_gatewayOrder", {
                error: 1,
                data: {
                    recharge_user: $('#recharge_user').val(),
                    channel_id: current_channelId,
                    //paypwd: e.password,
                    amount: _amount,
                    gateway_network: current_paytype
                }
            }, function (e) {

                setTimeout(function () {
                    $('.create_button').removeClass("paying");
                    $('.create_button').find('span').html('立即充值');
                }, 2000)

                if (e.code != 1) {
                    var recommend_url = "";
                    var available_amounts = "";
                    rec_amounts = [];
                    try {
                        if (e.recommend_url && e.recommend_url != "") {
                            recommend_url = e.recommend_url;
                        }
                        if (e.available_amounts && e.available_amounts != "") {
                            available_amounts = e.available_amounts;
                        }
                        if (e.amounts) {
                            rec_amounts = e.amounts;
                        }
                    } catch (e) {

                    }
                    if (search_amount('')) {
                        tp('请选择充值金额');
                        return;
                    }

                    if (available_amounts != "") {
                        $('#regular_money_list').html('');
                        available_amounts = JSON.parse(available_amounts);
                        for (var i = 0; i < available_amounts.length; i++) {
                            $('#regular_money_list').append('<div class="regular_money">                <div class="money_item" onclick="javascript:$(this).addClass(\'active\').closest(\'.regular_money\').siblings().find(\'.money_item\').removeClass(\'active\');$(\'#sell_number\').val(\'' + available_amounts[i] + '\');">                    <div class="money_value">' + available_amounts[i] + '</div>                                   </div>            </div>');
                            if (i == 0) {
                                $('#sell_number').val(available_amounts[i]);
                            }
                        }
                        tp('请选择充值金额');
                        return;

                    }

                    if (recommend_url != "") {
                        location.href = recommend_url;
                        return;
                    }

                    if (e.msg.indexOf("波币余额不足") != -1) {
                        show_success();
                    } else {
                        tp(e.msg);
                    }
                    return;
                }
                if (e.payurl != "" && first != "1") {
                    setTimeout(function () {
                        success_pay(e.orderId);
                    }, 5000);
                    //location.href = e.payurl;
                    //openNewWindow(e.payurl);
                    location.href = e.payurl;

                } else {
                    location.href = 'gateway_details.aspx?orderId=' + e.orderId;
                }
            })
            //})
        }

        var searchMoneys = function () {
            search_amount($('#money_keyword').val());
        }

        var search_amount = function (ss_money) {
            var amounts = rec_amounts;
            if (amounts.length > 0) {
                if (!isNaN(ss_money) && ss_money != "") {
                    var newSort = [];
                    var new_amount = [];
                    for (var i = 0; i < amounts.length; i++) {
                        var money = amounts[i].money;
                        var _diff = Math.abs(parseFloat(ss_money) - parseFloat(money));
                        var spliceSuccess = false;
                        for (var n = 0; n < newSort.length; n++) {
                            if (_diff < newSort[n].diff) {
                                newSort.splice(n, 0, { index: i, money: money, diff: _diff });
                                spliceSuccess = true;
                                break;
                            }
                        }
                        if (!spliceSuccess) {
                            newSort.push({ index: i, money: money, diff: _diff });
                        }
                    }

                    for (var i = 0; i < newSort.length; i++) {
                        new_amount.push(amounts[newSort[i].index]);
                    }
                    amounts = new_amount;
                    //console.log('new', new_amount, newSort);
                }

                $('#moneysPage').hide();
                $('#recpage').show();
                $('#paytype_select').hide();
                $('.create_button').hide();
                $('#rechargeTop').hide();


                $('#reclist tbody').html('');
                var isfirst = true;
                for (var i = 0; i < amounts.length; i++) {
                    var imgs = '';
                    var money = amounts[i].money;
                    var types = amounts[i].type.split(',');
                    var show_money = false;
                    for (var t = 0; t < types.length; t++) {
                        switch (types[t]) {
                            //case "1":
                            //    show_money = true;
                            //    imgs += '<img src="/static/images/pay/wechat.png" />';
                            //    break;
                            case "2":
                                show_money = true;
                                imgs += '<img src="/static/images/pay/ali.png" />';
                                break;
                            case "3":
                                show_money = true;
                                imgs += '<img src="/static/images/pay/bank.png" />';
                                break;
                                //case "4":
                                //    imgs += '<img src="/static/images/pay/cny.png" />';
                                //    break;
                            default:
                                break;
                        }
                    }
                    if (show_money) {
                        if (parseFloat(money) >= 100) {

                            $('#reclist tbody').append(' <tr style="border-bottom: 1px solid #eee;">                    <td style="color: rgb(255 103 0); font-size: 22px; font-weight: 500;">' + money + '</td>                    <td>                       ' + imgs + '                    </td>                    <td><a style="color: rgb(255 145 71 ); border: 1px solid rgb(255 145 71 ); border-radius: 0.25rem; padding: 0.25rem 0.5rem; font-size: 14px;cursor:pointer;" onclick="javascript:$(this).addClass(\'active\').closest(\'.regular_money\').siblings().find(\'.money_item\').removeClass(\'active\');$(\'#sell_number\').val(\'' + money + '\');new_advert();">立即充值</a>                    </td>                </tr>');

                            if (isfirst) {
                                isfirst = false;
                                $('#sell_number').val(money);
                            }
                        }

                    }

                }
                return true;

            } else {
                $('#moneysPage').show();
                $('#recpage').hide();
                $('.create_button').show();
                $('#rechargeTop').show();
                return false;
            }
        }

        var success_pay = function (orderId) {
            //security_password(function (e) {

            v3api("check_gatewayPay", {
                error: 1,
                data: {
                    //paypwd: e.password,
                    orderId: orderId
                }
            }, function (e) {
                if (e.msg == "未支付") {
                    setTimeout(function () {
                        success_pay(orderId);
                    }, 5000);
                } else if (e.msg == "支付成功") {
                    tp(e.msg);
                    location.href = 'gateway_details.aspx?orderId=' + orderId;
                } else {
                    tp(e.msg);
                }
            })
            //})
        }

        <%if (Request.QueryString["paytype"] + "" == "收银台" || Request.QueryString["paytype"] + "" == "支付宝转账")
          {
              %>
        new_advert(1);
        <%
          } %>
    </script>










    <div class=" pg-tips" style="display: none;">
        <div style="width: 100vw; height: 100vh; position: fixed; top: 0; left: 0; background: #000000b5; z-index: 9991;"></div>

        <div class="password-content" style="background: linear-gradient(187deg, #D0EBF9, #ffffff); padding: 8px; box-sizing: border-box; width: 95%; max-width: 380px; position: fixed; top: 50%;">

            <h3 style="text-align: center; margin: 0; padding: 13px; font-size: 26px; color: #222; text-shadow: 2px 3px 10px #dfdbdb;">温馨提示</h3>


            <div class="tip-content" style="color: #2a2b2c; padding: 30px 18px; text-align: center;">
                波币余额不足，是否前往购买？
            </div>

            <div class="button_list">
                <div style="display: flex; padding: 16px 22px;">

                    <div style="width: 50%; text-align: center;">
                        <a style="color: #605D54; border: 1px solid #ababab; display: inline-block; width: 130px; height: 39px; line-height: 39px; border-radius: 24px; cursor: pointer; font-weight: bold;" onclick="javascript:$('.pg-tips').hide();">取消</a>
                    </div>
                    <div style="width: 50%; text-align: center;">
                        <a class="confirm_password_button" onclick="tip_success()" style="background: #2E3847; color: #d5c7c2; font-weight: bold; border-radius: 24px;">确定</a>
                    </div>

                </div>
            </div>

        </div>


    </div>

    <script>
        var show_success = function () {
            $('.pg-tips').show();
        }
        var tip_success = function () {
            $('.pg-tips').hide();

            v3api("get_bobi_transaction", {
                data: {
                    recharge_user: $('#recharge_user').val()
                }
            }, function (e) {
                location.href = e.login_url;
            })

        }
    </script>

</asp:Content>


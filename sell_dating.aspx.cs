using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

public partial class order_list : baseClass
{
    public DataTable userdt = new DataTable();
    public Dictionary<string, object> pmlist = new Dictionary<string, object>();
    protected void Page_Load(object sender, EventArgs e)
    {
        List<SqlParameter> pams = new List<SqlParameter>();
        dbClass db = new dbClass();
        DataTable dt = new DataTable();
        pams.Add(new SqlParameter("@userid", uConfig.p_uid));

        string sql = string.Empty;


        string[] g, g2;


        sql += @" 



select * from accounts with(nolock) where id=@userid

select top 1 id,(case when DATEDIFF(DAY,order_time,GETDATE())=0 then number else 0 end) as payment_number,(case when DATEDIFF(DAY,order_time,GETDATE())=0 then amount else 0 end) as payment_amount,(case when DATEDIFF(DAY,order_time,GETDATE())=0 then amount else 0 end) as payment_amount,* from payment_list with(nolock) where userid=@userid and state=1 

";
        DataSet ds = db.getDataSet(sql, pams.ToArray());

        userdt = ds.Tables[0];


        Dictionary<string, object> temp_dic = chelper.getUserParames(uConfig.gd(userdt, "usertype"), uConfig.gd(userdt, "groupid"));
        pmlist["show_type"] = temp_dic["show_type"];
        pmlist["limit_number"] = temp_dic["limit_number"];
        pmlist["limit_amount"] = temp_dic["limit_amount"];
        pmlist["order_number"] = temp_dic["order_number"];
        pmlist["usertype"] = temp_dic["usertype"];

        pmlist["tp"] = Convert.ToBase64String(EncryptAesEcb(temp_dic["usertype"] + "", md5("userenc_" + uConfig.p_uid)));


        //新（根据用户层级）
        pmlist["number"] = pmlist["limit_number"];
        pmlist["amount"] = pmlist["limit_amount"];


        //减去已用次数（额度）
        dt = ds.Tables[1];
        pmlist["payment_id"] = "";

        pmlist["payment_type"] = "";
        pmlist["payment_name"] = "";
        pmlist["payment_bankid"] = "";
        pmlist["payment_bankname"] = "";
        pmlist["payment_bankbranch"] = "";
        if (dt.Rows.Count > 0)
        {
            pmlist["payment_id"] = dt.Rows[0]["id"] + "";
            pmlist["number"] = Convert.ToInt32(pmlist["number"]) - Convert.ToInt32(dt.Rows[0]["payment_number"]);
            pmlist["amount"] = Convert.ToInt32(pmlist["amount"]) - Convert.ToInt32(dt.Rows[0]["payment_amount"]);


            pmlist["payment_type"] = dt.Rows[0]["type"] + "";
            pmlist["payment_name"] = dt.Rows[0]["name"] + "";
            pmlist["payment_bankid"] = dt.Rows[0]["bankid"] + "";
            pmlist["payment_bankname"] = dt.Rows[0]["bankname"] + "";
            pmlist["payment_bankbranch"] = "";

            g = (pmlist["payment_bankname"] + "").Split(' ');
            if (g.Length > 0)
            {
                pmlist["payment_bankname"] = g[0];
            }
            if (g.Length > 1)
            {
                pmlist["payment_bankbranch"] = g[1];
            }

        }


        //手加次数（额度）
        //dt = ds.Tables[2];
        //if (dt.Rows.Count > 0)
        //{
        //    pmlist["number"] = Convert.ToInt32(pmlist["number"]) + Convert.ToInt32(dt.Rows[0]["daily_number"]);
        //    pmlist["amount"] = Convert.ToInt32(pmlist["amount"]) + Convert.ToInt32(dt.Rows[0]["daily_amount"]);
        //}


        ////有效邀请人数
        //dt = ds.Tables[3];
        //pmlist["valid_user_number"] = "0";
        //if (dt.Rows.Count > 0)
        //{
        //    pmlist["valid_user_number"] = dt.Rows[0]["number"] + "";
        //}

        //dt = ds.Tables[4];
        //pmlist["is_double"] = "0";
        //if (dt.Rows.Count > 0)
        //{
        //    pmlist["is_double"] = "1";
        //}



        //DataTable temp_dt = SortDataTable(chelper.gdt("levels_list"), "minNumber ASC"); ;
        //try
        //{
        //    temp_dt = selectDateTable(temp_dt, "id=" + uConfig.gd(userdt, "levelid"));
        //}
        //catch (Exception)
        //{
        //    temp_dt = new DataTable();
        //}
        //if (temp_dt.Rows.Count > 0)
        //{
        //    if (Convert.ToInt16(temp_dt.Rows[0]["minNumber"]) > Convert.ToInt16(pmlist["valid_user_number"]))
        //    {
        //        pmlist["valid_user_number"] = temp_dt.Rows[0]["minNumber"] + "";
        //    }

        //}










        ////classList.DataSource = SortDataTable(selectDateTable(chelper.gdt("item_classList"), "state=1"), "sort_index ASC");
        ////classList.DataBind();

        //levels_list.DataSource = SortDataTable(chelper.gdt("levels_list"), "minNumber ASC");
        //levels_list.DataBind();

        //levels_list_gd.DataSource = SortDataTable(chelper.gdt("levels_list"), "minNumber ASC");
        //levels_list_gd.DataBind();
    }


    public object positive_number(object numb)
    {
        object result = numb;

        try
        {
            if (Convert.ToDouble(result) < 0)
            {
                result = "0";
            }
        }
        catch (Exception)
        {
        }

        return result;
    }

}
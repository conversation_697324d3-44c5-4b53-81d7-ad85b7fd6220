<%@ Page Title="" Language="C#" MasterPageFile="~/top.master" AutoEventWireup="true" CodeFile="rob_list.aspx.cs" Inherits="order_list" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="Server">
    <script>
        $(function () {
            popTitle('任务记录 <div style="color: #555; font-weight: 100; font-size: 12px; margin-top: 6px;">本数据由商家官方提供</div>', '<div style="color: #454242; display: flex; flex-direction: column; align-items: center;"><div style="font-weight: bold; font-size: 22px;"><%=uConfig.gnumber(userdt,"amount") %></div><div style="font-size: 12px; display: flex; color: #555; margin-top: 5px;">可用余额<img src="/static/images/coin.png" alt="" height="15" width="15" style="margin-left: 5px;"></div></div>');
            try {
                lastData();
            } catch (e) {

            }
        })
    </script>
    <style>
        body {
            background: linear-gradient(183deg, #eef1e4, transparent);
            background-repeat: no-repeat;
        }

        .top-title .pptitle {
            font-size: 25px;
            width: 100%;
        }

        .top-title {
            display: flex;
        }



        .top-title {
            background: none;
        }

        .gtab {
            width: 25%;
            margin: 0px;
            color: #000;
            padding: 8px 0px;
            font-weight: 100;
        }

            .gtab.act {
                color: #fafdde;
                background: #35313163;
            }

        .tablist {
            margin-top: 30px;
            position: fixed;
    left: 0;
    padding: 0 10px;
    box-sizing: border-box;
        }

        #lists {
            height: calc(100vh - 172px);
            overflow-y: auto;
            margin-top: 10px;
            margin-top: 80px;
        }
    </style>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="Server">






    <%--<div style="display: flex;">

        <div style="margin-left: auto; color: #454242; display: flex; flex-direction: column; align-items: center; margin-top: 22px;">
            <div style="font-weight: bold; font-size: 23px;">108899.66</div>
            <div style="font-size: 12px; display: flex; color: #555; margin-top: 5px;">剩余可用资产<img src="/static/images/coin.png" alt="" height="15" width="15" style="margin-left: 5px;"></div>
        </div>

    </div>--%>

    <div class="tablist">
        <div class="gtab act">全部</div>
        <div class="gtab">待处理</div>
        <div class="gtab">已完成</div>
        <div class="gtab">冻结中</div>
    </div>

    <div id="lists">
    </div>






    <script>


        var show_list = function (type) {
            v3api("lists", { data: { page: 'order_list', p: 0, limit: 30, state: type } }, function (e) {
                for (var i = 0; i < e.data.list.length; i++) {
                    var obj = e.data.list[i];
                    $('#lists').append('<div listid="' + obj.orderId + '" style="background: #fff; border-radius: 5px; padding: 5px 9px; margin: 10px 0;    border-bottom: 2px solid #ddd;">            <div style="background: #f8f8f8; padding: 10px 16px; border-radius: 8px;position: relative;">      <img src="/static/images/' + (obj.state == "0" || obj.state == "1001" ? "dongjie" : (obj.state == "1" || obj.state == "2") ? "succ" : "dai") + '.png" style="position: absolute; right: 0; width: 80px;">          <div style="color: #b1b1b1; font-size: 12px;">                    <div>任务时间：' + obj.create_time + '</div>                    <div style="">任务编号：' + obj.origin_orderId + '</div>                </div>                <div style="display: flex; background: #edededf0; margin: 10px 0;">                    <div>                        <img src="' + obj.item_imgurl + '" alt="" width="90" height="90">                    </div>                    <div style="padding: 8px; font-size: 13px; color: #555;">                        <div>' + obj.item_name + '</div>                        <div style="margin-top: 5px;">' + parseFloat(obj.amount).toFixed(2) + '<img src="/static/images/coin.png" alt="" height="15" width="15" style="margin-left: 5px;"></div>                    </div>                </div>                <div style="font-size: 14px;">                    <div style="display: flex; margin: 10px 0;">                        <div>                            订单总额                                                          </div>                        <div style="margin-left: auto; color: #222;">                            <span class="total_amount">' + parseFloat(obj.amount).toFixed(2) + '</span><img src="/static/images/coin.png" alt="" height="15" width="15" style="margin-left: 5px;">                        </div>                    </div>                    <div style="display: flex; margin: 10px 0;">                        <div>佣金</div>                        <div style="margin-left: auto; color: #222;">                            <span class="award_amount">' + parseFloat(obj.award_amount).toFixed(2) + '</span><img src="/static/images/coin.png" alt="" height="15" width="15" style="margin-left: 5px;">                        </div>                    </div>                    <div style="display: flex; margin: 10px 0;">                        <div>预计返还</div>                        <div style="margin-left: auto; color: #FAA751; font-size: 24px;"><span class="award_total_amount">' + (parseFloat(obj.amount) + parseFloat(obj.award_amount)).toFixed(2) + '</span><img src="/static/images/coin.png" alt="" height="15" width="15" style="margin-left: 5px;"></div>                    </div>                </div>    ' + (obj.state == 1000 ? ' <div style="margin-top: 20px;display: flex;justify-content: center;">    <a style="border-radius:18px;padding: 12px 26px;font-size:12px;background: #e3e3e3;color: #333;display: flex;cursor:pointer;border: 2px solid #9d9d9d;" onclick="confirm_transaction(\'' + obj.orderId + '\')"><svg t="1693040838464" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="4309" width="16" height="16"><path d="M770.56 928.4608H388.4032c-16.9472 0-30.72-13.7728-30.72-30.72s13.7728-30.72 30.72-30.72h382.1056c15.616 0 28.8768-11.2128 32.3584-27.2384l72.2944-335.1552c3.4304-15.7696-0.3584-31.9488-10.3424-44.3392-9.472-11.7248-22.7328-18.176-37.4272-18.176h-239.104a30.72 30.72 0 0 1-28.16-43.008c62.1056-142.2848 40.3456-201.1136 28.1088-219.8016-13.7216-20.9408-33.792-24.1152-44.4928-24.1152-25.8048 0-35.9936 25.088-47.8208 77.7216-1.5872 6.9632-3.0208 13.4656-4.5568 19.3536-42.1888 161.5872-149.3504 219.136-235.6224 219.136H192.2048c-17.8688 0-32.4096 15.36-32.4096 34.2016v327.2192c0 18.8416 14.5408 34.2016 32.4096 34.2016h58.9312c16.9472 0 30.72 13.7728 30.72 30.72s-13.7728 30.72-30.72 30.72H192.2048c-51.7632 0-93.8496-42.9056-93.8496-95.6416V505.6c0-52.736 42.0864-95.6416 93.8496-95.6416h63.5392c30.72 0 134.1952-12.4928 176.128-173.1584 1.3824-5.2736 2.6624-11.1104 4.096-17.3568 9.8816-43.9296 28.3136-125.696 107.776-125.696 39.3728 0 74.3424 18.8928 95.8976 51.8656 24.2688 37.0688 41.1136 107.1616-5.888 235.008h193.6384c33.1264 0 64.2048 14.9504 85.248 41.0112 21.7088 26.88 29.952 61.8496 22.6304 95.8464l-72.2944 335.1552c-9.472 43.9808-48.3328 75.8272-92.416 75.8272z" fill="#363F5B" p-id="4310"></path><path d="M269.6192 804.2496c-16.9472 0-30.72-13.7728-30.72-30.72v-193.0752c0-16.9472 13.7728-30.72 30.72-30.72s30.72 13.7728 30.72 30.72v193.0752c0 16.9472-13.7728 30.72-30.72 30.72z" fill="#B11EFF" p-id="4311"></path></svg>&nbsp;确认收款</a></div>' : '') + '       </div>        </div>');
                }

            })
        }

        var type = '<%=Request.QueryString["type"] %>';





        var confirm_transaction = function (id) {
            event.stopPropagation();
            v3api("confirm_order", { data: { id: id } }, function (e) {
                tp('确认成功');
                var ls = $('[listid=' + id + ']');
                ls.remove();
                //ls.find(".statedata").html('<span style="border-radius:18px;padding:2px 8px;font-size:12px;background:#2A3EE8;color:#fff;">已完成</span>');
            })

        }


        var lastData = function () {
            try {
                if (type != "") {
                    $('.tablist').hide();
                    //$('.pptitle').html(type + '记录');
                }
            } catch (e) {

            }
        }
        if (type != "") {
            show_list(type);
            lastData();
        } else {
            show_list('');
        }



        initTabs(function (e) {
            console.log('select', e);
            $('#lists').html('');

            switch (e) {
                case "全部":
                    e = "";
                    break;
                case "待处理":
                    e = "1000";
                    break;
                case "已完成":
                    e = "1,2";
                    break;
                case "冻结中":
                    e = "0";
                    break;
                default:
                    break;
            }

            show_list(e);
        })
    </script>
</asp:Content>


<%@ Page Title="" Language="C#" MasterPageFile="~/top.master" AutoEventWireup="true" CodeFile="chat.aspx.cs" Inherits="order_list" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="Server">
    <style>
        /*导航图标*/
        .NMH-g-navicon {
            position: fixed;
            top: 50%;
            right: 020px;
            width: 100px;
            height: 100px;
            pointer-events: none;
            z-index:1;
        }

            .NMH-g-navicon.Jnmh-onleft {
                right: auto;
                left: 020px;
            }
            /*导航图标logo按钮*/
            .NMH-g-navicon .Jnmh-btnlogo {
                position: absolute;
                display: block;
                top: 50%;
                right: -6px;
                /* margin-top: -50px; */
                border: 0;
                background: #fff;
                background-size: 95% 95%;
                border: 1px solid rgba(0, 0, 0, 0.1);
                box-shadow: rgba(0, 0, 0, 0.12) 0px 6px 10px 0px;
                outline: none;
                border-radius: 50%;
                z-index: 1;
                width: 60px;
                height: 60px;
                margin-top: -75px;
                pointer-events: auto;
            }

        .Jnmh-btnlogo-def {
            opacity: 0.9;
            color: #fff;
            font-size: 16px;
            background: rgba(216, 52, 66, .8)!important;
            border: 4px solid rgba(245, 113, 12)!important;
            box-shadow: rgb(216 52 66 / 36%) 0px 6px 10px 0px!important;
        }

        .NMH-g-navicon .Jnmh-btnlogohover {
            position: absolute;
            display: block;
            width: 100px;
            height: 100px;
            top: 50%;
            right: 0;
            margin: 0;
            padding: 0;
            margin-top: -50px;
            border: 0;
            overflow: hidden;
            /*background-color: red;*/
        }

        /*导航图标logo按钮-鼠标经过*/
        .NMH-g-navicon.Jnmh-open .Jnmh-btnlogohover {
            margin-top: -150px;
            width: 200px;
            height: 300px;
            border-radius: 150px 0 0 150px;
        }

        .NMH-g-navicon.Jnmh-onleft .Jnmh-btnlogohover {
            left: 0;
            right: auto;
            border-radius: 0 150px 150px 0;
        }
        /*导航图标菜单子容器*/
        .NMH-g-navicon .Jnmh-m-submenu {
            position: absolute;
            background-color: transparent;
            list-style: none;
            margin: 0;
            padding: 0;
            top: -35px;
            bottom: 56px;
            left: -10px;
            right: -68px;
        }

            .NMH-g-navicon .Jnmh-m-submenu .Jnmh-subli {
                position: absolute;
                width: 100%;
                height: 100%;
                transform: rotate(0deg);
                -webkit-transform: rotate(0deg);
                transition: all 0.1s ease-in-out;
                pointer-events: none;
            }

        .Jnmh-m-submenu .Jnmh-subdl {
            position: absolute;
            left: 50%;
            bottom: 100%;
            width: 0;
            height: 0;
            line-height: 1px;
            margin-left: 0;
            background: #fff;
            border-radius: 50%;
            text-align: center;
            font-size: 1px;
            overflow: hidden;
            cursor: pointer;
            box-shadow: none;
            transition: all 0.1s ease-in-out, color 0.1s, background 0.1s;
            pointer-events: auto;
        }
        /*导航图标-展开菜单时*/
        .NMH-g-navicon.Jnmh-open .Jnmh-m-submenu .Jnmh-subdl {
            /* margin-left: -40px; */
            box-shadow: 0 3px 3px rgba(0, 0, 0, 0.1);
            font-size: 12px;
            width: 50px;
            height: 50px;
            line-height: 50px;
            background: rgba(198, 44, 65, .9);
            color: #fff;
            margin-left: -25px;
        }
        /*导航图标-三级菜单容器*/
        .NMH-g-navicon.Jnmh-open .Jnmh-m-submenu .Jnmh-subdd {
            position: absolute;
            line-height: normal;
        }
    </style>

    <style>
        .nshow {
            display: none;
        }

        .atstate {
            color: #261bc7;
        }

        .usernoteButton {
            cursor: pointer;
        }

            .usernoteButton:hover, .usernoteButton:active, usernoteButton:focus {
                transition: all 0.2s;
                opacity: 1!important;
            }
    </style>
    <script>
        var chatData = {
            uuid: '<%=uConfig.gd(userdt,"parent_code") %>'
        }
        var tiptt = '<div class="tiptt" style="color: #c93434;display: inline-block;margin-right: 3px;font-size: 12px;font-weight: bold;">[提到了你]</div>';

        $(function () {
            popTitle('聊天室', '<a style="position: absolute;right: 10px;top: 8px;display: flex;align-items: center;justify-content: center;" href="index.aspx"><svg t="1696446227993" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="16184" width="12" height="12"><path d="M576 512l277.333333 277.333333-64 64-277.333333-277.333333L234.666667 853.333333 170.666667 789.333333l277.333333-277.333333L170.666667 234.666667 234.666667 170.666667l277.333333 277.333333L789.333333 170.666667 853.333333 234.666667 576 512z" fill="#000" p-id="16185"></path></svg></a>');
        })
    </script>

    <%--<div style="background: linear-gradient(143deg, #31325fd6, #363561); padding: 8px 20px; border-radius: 18px; word-wrap: break-word; display: inline-block; text-align: left; font-size: 12px;">
        <div style="display: flex; font-size: 13px; font-weight: bold; align-items: center; justify-content: center; color: #aebfe5;">
            <img src="images/jrzj.webp" style="width: 31px;"><div>今日战绩</div>
        </div>
        <div style="color: #a0a4e1; font-size: 12px;">
            投注<span style="color: #E5DB91; font-size: 15px; font-weight: bold; margin: 0 3px;">99999</span>元 中奖<span style="color: #E5DB91; font-size: 15px; font-weight: bold; margin: 0 3px;">99999</span>元
        </div>
        <div style="font-size: 15px; font-weight: bold; text-align: center; margin-top: 10px; background: #ffffff1a; border-radius: 10px; padding: 8px; margin-bottom: 6px; color: #ebe1e1;">
            盈亏<span style="color: #cfabab; margin: 0 2px; font-size: 16px;">99999</span>元
        </div>
    </div>--%>


    <%--<div style="background: #FFEB31;color: #756938;padding: 16px 20px;border-radius: 18px;word-wrap: break-word;text-align: left;font-size: 12px;display: flex;align-items: center;">

        <div>


            <div style="font-size: 12px;">
                <svg t="" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="31266" width="20" height="20" style="margin-right: 5px;">
                    <path d="M1017.6 204.8V32c-300.8 89.6-454.4 281.6-441.6 576v384h435.2V550.4h-224c-6.4-147.2 70.4-256 230.4-345.6z m-563.2 0V32C147.2 121.6 0 313.6 6.4 608v384h435.2V550.4H217.6c-6.4-147.2 70.4-256 236.8-345.6z" fill="#87857b8c" p-id="31267"></path></svg>

                投注<span style="font-size: 15px; font-weight: bold; margin: 0 3px;">99999</span>元 中奖<span style="font-size: 15px; font-weight: bold; margin: 0 3px;">99999</span>元

   

                <svg t="" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="31800" width="20" height="20" style="margin-left: 5px; position: relative;">
                    <path d="M1024 107.789474v378.610526c-21.557895 238.484211-144.168421 385.347368-367.831579 441.936842V759.915789c35.031579-21.557895 63.326316-41.768421 83.536842-63.326315 35.031579-48.505263 52.547368-80.842105 52.547369-94.31579v-83.536842H623.831579V107.789474H1024zM413.642105 107.789474v378.610526c-13.473684 253.305263-137.431579 400.168421-367.831579 441.936842V759.915789c70.063158-21.557895 115.873684-74.105263 137.431579-157.642105v-83.536842H13.473684V107.789474h400.168421z" fill="#87857b8c" p-id="31801"></path></svg>
            </div>

            <div style="font-size: 22px;color: #2a2b2c;margin-top: 10px;">
                <span style="font-size: 12px;">￥</span><span style="margin: 0 2px; font-weight: bold;">99999</span>
                <span style="font-size: 12px; background: #7d9ebf; color: #f3eded; padding: 2px 5px; border-radius: 3px; position: relative; top: -2px;">今日盈亏</span>
            </div>


        </div>



        <div style="margin-left: 15px;">
            <svg t="" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="21607" width="32" height="32">
                <path d="M512 10.24C789.11488 10.24 1013.76 234.88512 1013.76 512S789.11488 1013.76 512 1013.76 10.24 789.11488 10.24 512 234.88512 10.24 512 10.24z m-163.84 573.44h-40.96a51.2 51.2 0 0 0-51.2 51.2v81.92a51.2 51.2 0 0 0 51.2 51.2h40.96a51.2 51.2 0 0 0 51.2-51.2v-81.92a51.2 51.2 0 0 0-51.2-51.2z m184.32-61.44h-40.96a51.2 51.2 0 0 0-51.2 51.2v143.36a51.2 51.2 0 0 0 51.2 51.2h40.96a51.2 51.2 0 0 0 51.2-51.2v-143.36a51.2 51.2 0 0 0-51.2-51.2z m184.32-61.44h-40.96a51.2 51.2 0 0 0-51.2 51.2v204.8a51.2 51.2 0 0 0 51.2 51.2h40.96a51.2 51.2 0 0 0 51.2-51.2V512a51.2 51.2 0 0 0-51.2-51.2z m-97.21856-183.88992a30.72 30.72 0 0 0-34.816 22.2208l-0.65536 2.88768a30.72 30.72 0 0 0 17.2032 32.9728L541.184 389.12h-96.19456l-116.75648 99.49184-2.29376 2.17088a30.72 30.72 0 0 0-3.072 38.64576l1.90464 2.49856 2.17088 2.29376a30.72 30.72 0 0 0 38.64576 3.072l2.49856-1.90464L467.59936 450.56h97.1776l80.32256-72.33536 0.02048 10.89536 0.14336 2.94912A30.72 30.72 0 0 0 706.56 389.12v-88.6784l-84.09088-22.87616z" fill="#222222" p-id="21608"></path></svg>
        </div>
    </div>--%>



    <style>
        .base_page {
            background: linear-gradient(178deg, #E8EBF4, #FAF9FE);
        }

            .base_page:not(#index_menu) {
                max-width: 600px;
                display: flex;
                width: 100%;
                height: 100%;
                flex-direction: column;
                position: fixed;
            }

        .flex_grow {
            flex-grow: 1;
            overflow-y: auto;
        }

        .main-container {
            padding: 0px;
        }

        .menu {
            background: #ffffff70;
            margin-top: 0;
            border-radius: 0;
            border-bottom: 1px solid #f5f5f5;
        }

            .menu.istop {
                background: #fff9f1;
            }

        .menu-list div {
            margin-left: 0;
            font-weight: 100;
            text-align: left;
        }


        body, .top-title {
            background: #EDEDED;
        }

        .top-title {
            display: none!important;
        }

        .wxlist {
            display: flex;
            position: fixed;
            bottom: 0;
            left: 0;
            width: 100%;
            box-sizing: border-box;
            background: #ffffff61;
        }

            .wxlist .icon_item {
                width: 33.33%;
                text-align: center;
                font-size: 12px;
                padding: 10px 0;
            }

                .wxlist .icon_item:not(.default_color) svg path {
                    fill: currentColor;
                }


            .wxlist .icon_item {
                color: #333C4F;
            }


                .wxlist .icon_item.active {
                    color: #07C062;
                }
        /*.wxlist .icon_item.active  svg path  {
                    fill: currentColor;
                }*/

        #chat_message {
            display: flex;
            flex-direction: column;
            height: 100%;
            position: fixed;
            width: 100%;
        }

            #chat_message .wxlist {
                position: relative!important;
            }

        div {
            box-sizing: border-box;
        }
    </style>

    <link href="../slayer/layer.css" rel="stylesheet" />
    <script src="../slayer/layer.js"></script>



    <script>
        // 写入数据
        function writeToLocalStorage(key, value) {
            localStorage.setItem(key, value);
            //console.log('Stored ' + key + ' with value ' + value + ' in localStorage.');
        }
        // 读取数据
        function readFromLocalStorage(key, def) {
            var value = localStorage.getItem(key);
            if (value !== null) {
                console.log('Read ' + key + ' with value ' + value + ' from localStorage.');
            } else {
                value = def
                console.log(key + ' not found in localStorage.');
            }
            return value;
        }
    </script>

</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="Server">

    <div id="index_message_data" class="base_page" style="padding-bottom: 61px; display: ;">
        <div style="display: flex; padding: 16px 10px;">

            <div style="font-size: 18px;" id="message_number">消息（0）</div>
            <%if (Request.QueryString["test"] + "" == "roomdata")
              {
            %>

            <div style="font-size: 12px; color: red; display: flex; align-items: center;"><%=uConfig.p_tid %>,<%=uConfig.gd(selectDateTable(chelper.gdt("top_rooms"), "userid=" + uConfig.p_tid),"roomNumber") %></div>
            <%
              } %>

                        <%if (uConfig.gd(userdt, "chatad") == "1")
              {
            %>
<%--            <div style="display: flex; margin-left: auto; font-size: 14px; align-items: center;" onclick="pop_allsend()">
                <svg t="" class="icon" viewBox="0 0 1051 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="" width="18" height="18">
                    <path d="M740.894118 574.973262m-196.038503 0a196.038503 196.038503 0 1 0 392.077005 0 196.038503 196.038503 0 1 0-392.077005 0Z" fill="#8873B3" p-id="16268"></path><path d="M294.605348 906.26738c-4.380749 0-9.309091-1.095187-13.68984-2.737968-13.68984-5.475936-22.451337-17.522995-23.546524-32.308022l-6.023529-90.352941h-32.855615c-61.330481 0-111.161497-49.831016-111.161498-111.161497V256.82139c0-61.330481 49.831016-111.161497 111.161498-111.161497h108.423529c15.33262 0 27.927273 12.594652 27.927273 27.927273s-12.594652 27.927273-27.927273 27.927273H219.037433c-30.117647 0-54.759358 24.641711-54.759358 54.759358v413.980748c0 30.117647 24.641711 54.759358 54.759358 54.759359h54.759358c16.975401 0 31.212834 13.142246 32.308022 30.117647l4.380748 71.187166 93.09091-89.257754c8.213904-7.66631 19.165775-12.047059 30.117647-12.047059H832.342246c30.117647 0 54.759358-24.641711 54.759358-54.759359V256.82139c0-30.117647-24.641711-54.759358-54.759358-54.759358H441.908021c-15.33262 0-27.927273-12.594652-27.927272-27.927273s12.594652-27.927273 27.927272-27.927272H832.342246c61.330481 0 111.161497 49.831016 111.161497 111.161497v413.980749c0 61.330481-49.831016 111.161497-111.161497 111.161497H439.170053l-119.375401 114.447059c-6.571123 6.023529-15.880214 9.309091-25.189304 9.309091z" fill="#331F0B" p-id="16269"></path><path d="M591.40107 389.339037H291.86738c-11.499465 0-21.35615-9.309091-21.35615-21.356149v-8.761498c0-11.499465 9.309091-21.35615 21.35615-21.356149H591.40107c11.499465 0 21.35615 9.309091 21.356149 21.356149v8.761498c0 12.047059-9.309091 21.35615-21.356149 21.356149zM493.381818 574.973262H291.86738c-11.499465 0-21.35615-9.309091-21.35615-21.35615v-8.761497c0-11.499465 9.309091-21.35615 21.35615-21.35615h201.514438c11.499465 0 21.35615 9.309091 21.35615 21.35615v8.761497c0 12.047059-9.856684 21.35615-21.35615 21.35615z" fill="#331F0B" p-id="16270"></path></svg>&nbsp;消息群发
            </div>--%>

            <%} %>

            <%--<div style="margin-left: auto;">

                <a>

                    <svg t="1696432594931" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="12435" width="22" height="22">
                        <path d="M934.242232 884.637975 738.287281 695.561689l1.836835-2.302439c52.767807-65.338101 81.829703-147.602709 81.829703-231.593636 0-203.716728-165.742858-369.473913-369.411491-369.473913-203.731054 0-369.505635 165.757185-369.505635 369.473913 0 203.715705 165.774581 369.45754 369.505635 369.45754 89.26607 0 175.511339-32.450063 242.841817-91.288123l2.209318-1.929955 195.485253 186.963159 1.959631 0 0.031722 1.61887c4.947685 3.483334 10.517541 5.319146 16.5837 5.319146 16.180518 0 29.308513-13.192464 29.308513-29.310559C940.96126 896.276037 938.72329 890.239554 934.242232 884.637975zM452.541305 772.413008c-171.358763 0-310.74637-139.388631-310.74637-310.74637 0-171.34239 139.387607-310.700321 310.74637-310.700321 171.309644 0 310.700321 139.357931 310.700321 310.700321C763.241626 633.024377 623.851972 772.413008 452.541305 772.413008z" fill="#333C4F" p-id="12436"></path></svg>


                </a>

                <a style="margin-left: 20px;" onclick="javascript:$('.base_page').hide();$('#search_friends').show();">

                    <svg t="1696432613450" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="13485" width="22" height="22">
                        <path d="M512 992C246.912 992 32 777.088 32 512 32 246.912 246.912 32 512 32c265.088 0 480 214.912 480 480 0 265.088-214.912 480-480 480z m0-64c229.76 0 416-186.24 416-416S741.76 96 512 96 96 282.24 96 512s186.24 416 416 416z" fill="#333C4F" p-id="13486"></path><path d="M256 544a32 32 0 0 1 0-64h512a32 32 0 0 1 0 64H256z" fill="#333C4F" p-id="13487"></path><path d="M480 256a32 32 0 0 1 64 0v512a32 32 0 0 1-64 0V256z" fill="#333C4F" p-id="13488"></path></svg>


                </a>

            </div>--%>

            <script>
                var update_message_number = function (n) {
                    var number = $('#message_number').data("number");
                    if (isNaN(number)) {
                        number = "0";
                    }
                    //console.log('【NUB更新】n,number', n, number);

                    number = parseInt(number);
                    number += parseInt(n);
                    if (number < 0) {
                        number = 0;
                    }

                    $('#message_number').data("number", number);
                    $('#message_number').html('消息（' + number + '）');
                }
            </script>
        </div>





        <div style="display: flex; align-items: center; padding: 8px; padding-top: 0;">

            <div style="position: relative; width: 100%;">

                <div style="position: absolute; top: 9px; left: 10px;">
                    <svg t="1696520315313" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2610" width="16" height="16">
                        <path d="M680.728354 651.326209c121.274064-137.208988 116.320239-346.908988-14.939247-478.166427-136.444579-136.433322-357.662913-136.433322-494.094188 0-136.443555 136.442532-136.443555 357.661889 0 494.105445 131.241067 131.253346 340.927763 136.204102 478.149031 14.942317l265.63187 265.63187 30.889521-30.877241L680.728354 651.326209zM649.273968 622.002346l-28.658713 28.668946c-120.345925 105.622596-303.678394 101.031021-418.524049-13.812587-119.651101-119.651101-119.651101-313.648466 0-433.299567C321.742307 83.909062 515.740696 83.909062 635.39282 203.569372 750.211868 318.380234 754.826979 501.656421 649.273968 622.002346z" fill="#2c2c2c" p-id="2611"></path></svg>

                </div>

                <input class="search_content" sid="msg" style="border: 0; background: #fff; width: 100%; padding: 9px; box-sizing: border-box; padding-left: 32px;" placeholder="搜索">
            </div>


            <div class="cancel_search_button" sid="msg" style="flex-shrink: 0; display: none; padding: 0 10px; font-size: 13px;">
                取消
           
            </div>



        </div>

        <div id="chat-list" class="flex_grow">
        </div>
    </div>



    
    <div id="chat_message" class="base_page" style="display: none; z-index: 1;">


        <div class="group_item" style="display: none;">
            <div style="position: fixed; right: 0; top: 110px; display: flex; flex-direction: column; align-items: end;">

 <%--               <a style="margin-top: 12px; padding: 3px; text-align: right; display: inline-block; width: 100%;" id="luckwheel" href="luckwheel.aspx">
                    <img src="zhuanpan/img/幸运转盘.png" style="width: 59px;"></a>

                <a style="margin-top: 12px; padding: 3px; text-align: right; display: inline-block; width: 100%; text-decoration: none;" href="<%=uConfig.stcdata("kf_online") %>">
                    <img src="static/images/lxwm.png" style="width: 60px; margin-bottom: -6px;">
                    <div style="font-size: 12px; text-align: center; color: #2a2b2c;">
                        <b>在线客服</b>
                    </div>
                </a>--%>

                <%--            <a style="background: #FFC107; font-size: 13px; color: #fff; padding: 5px 10px; padding-left: 13px; border-top-left-radius: 50px; border-bottom-left-radius: 50px; text-align: right; box-shadow: 5px 5px 5px #b1880f8c;" onclick="share_reward()">晒出今日奖励</a>

                <a style="background: #FFC107; font-size: 13px; color: #fff; padding: 5px 10px; padding-left: 13px; border-top-left-radius: 50px; border-bottom-left-radius: 50px; margin-top: 12px; text-align: right; box-shadow: 5px 5px 5px #b1880f8c;" onclick="get_ranking_list()">今日业绩排行榜</a>


           <a style="margin-top: 12px; background: #00000038; padding: 3px; margin-right: 10px; border-radius: 7px; display: none;" id="get_pool_redbag">
                    <svg class="icon redbag-icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="12026" width="39" height="39">
                        <path d="M900.408 899.088c0 48.744-39.536 88.224-88.248 88.224H239.656c-48.712 0-88.192-39.488-88.192-88.224V108.48c0-48.744 39.48-88.256 88.192-88.256h572.504c48.712 0 88.248 39.512 88.248 88.256v790.608z" fill="#F6B246" p-id="12027"></path><path d="M239.712 20.224c-48.712 0-88.256 39.512-88.256 88.256v790.608c0 48.744 39.536 88.224 88.256 88.224h572.504" fill="#F5A517" p-id="12028"></path><path d="M525.728 343.256c179.504 0 330.52-85.608 374.68-201.824V108.48c0-48.744-39.536-88.256-88.248-88.256H239.656c-48.712 0-88.192 39.512-88.192 88.256v34.368c44.832 115.48 195.376 200.408 374.264 200.408zM618.272 598.376H433.592a30.104 30.104 0 1 1 0-60.216h184.68c16.64 0 30.104 13.464 30.104 30.104s-13.464 30.112-30.104 30.112zM618.272 698.208H433.592a30.104 30.104 0 1 1 0-60.216h184.68a30.088 30.088 0 0 1 30.104 30.104 30.088 30.088 0 0 1-30.104 30.112z" fill="#E6246B" p-id="12029"></path><path d="M525.936 862.08a30.096 30.096 0 0 1-30.104-30.112V569.68a30.096 30.096 0 0 1 30.104-30.104 30.08 30.08 0 0 1 30.104 30.104v262.288a30.096 30.096 0 0 1-30.104 30.112z" fill="#E6246B" p-id="12030"></path><path d="M503.152 592.792a30.08 30.08 0 0 1-25.872-14.672l-46.48-77.792a30.104 30.104 0 0 1 10.408-41.272 30.064 30.064 0 0 1 41.272 10.408l46.48 77.792a30.088 30.088 0 0 1-25.808 45.536z" fill="#E6246B" p-id="12031"></path><path d="M548.776 592.792a30.096 30.096 0 0 1-25.808-45.536l46.48-77.792a30.064 30.064 0 0 1 41.272-10.408 30.088 30.088 0 0 1 10.408 41.272l-46.48 77.792a30.088 30.088 0 0 1-25.872 14.672z" fill="#E6246B" p-id="12032"></path></svg></a>
                    

                <a style="margin-top: 12px; padding: 3px; text-align: right; display: inline-block; background: #cdb9b938; text-decoration: none; text-align: center; margin-right: 6px; border-radius: 7px;" id="A1" href="user_task.aspx?type=xrhl&back=1">
                    <svg t="1699544873152" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="3728" width="38" height="38">
                        <path d="M102.4 409.6h819.2v409.6a102.4 102.4 0 0 1-102.4 102.4H204.8a102.4 102.4 0 0 1-102.4-102.4V409.6zM102.4 409.6h819.2L579.4816 110.1824a102.4 102.4 0 0 0-134.9632 0z" fill="#36C196" p-id="3729"></path><path d="M204.8 204.8h614.4a51.2 51.2 0 0 1 51.2 51.2v563.2H153.6V256a51.2 51.2 0 0 1 51.2-51.2z" fill="#FFBA40" p-id="3730"></path><path d="M327.68 447.5904a20.48 20.48 0 0 0-4.7104-14.0288A43.3152 43.3152 0 0 0 307.2 423.936a173.056 173.056 0 0 1-19.5584-8.8064 60.6208 60.6208 0 0 1-14.0288-10.24 39.0144 39.0144 0 0 1-9.1136-13.312 44.4416 44.4416 0 0 1-3.2768-18.1248 40.96 40.96 0 0 1 11.1616-29.7984 46.6944 46.6944 0 0 1 29.7984-13.6192V307.2h16.5888v23.3472a43.1104 43.1104 0 0 1 28.7744 15.872 53.9648 53.9648 0 0 1 10.24 34.2016H327.68a30.72 30.72 0 0 0-5.12-19.3536 16.7936 16.7936 0 0 0-13.7216-6.3488 17.1008 17.1008 0 0 0-13.2096 5.0176 19.456 19.456 0 0 0-4.608 13.824 18.432 18.432 0 0 0 4.5056 13.1072 51.2 51.2 0 0 0 16.9984 10.24 194.56 194.56 0 0 1 20.48 9.728 61.44 61.44 0 0 1 13.5168 10.24 42.1888 42.1888 0 0 1 8.4992 13.0048 46.592 46.592 0 0 1 3.3792 17.3056 40.96 40.96 0 0 1-10.9568 29.696 47.7184 47.7184 0 0 1-30.72 13.5168V512h-16.5888v-21.2992a49.664 49.664 0 0 1-32.9728-15.6672A51.9168 51.9168 0 0 1 256 439.6032h30.0032a29.2864 29.2864 0 0 0 5.8368 19.6608 20.48 20.48 0 0 0 16.896 6.8608 20.48 20.48 0 0 0 13.9264-5.3248 17.7152 17.7152 0 0 0 5.0176-13.2096z" fill="#FFFFFF" p-id="3731"></path><path d="M460.288 307.2m25.6 0l256 0q25.6 0 25.6 25.6l0 0q0 25.6-25.6 25.6l-256 0q-25.6 0-25.6-25.6l0 0q0-25.6 25.6-25.6Z" fill="#FFFFFF" p-id="3732"></path><path d="M613.888 409.6m25.6 0l102.4 0q25.6 0 25.6 25.6l0 0q0 25.6-25.6 25.6l-102.4 0q-25.6 0-25.6-25.6l0 0q0-25.6 25.6-25.6Z" fill="#FFFFFF" p-id="3733"></path><path d="M102.4 409.6v409.6a102.4 102.4 0 0 0 102.4 102.4h563.2z" fill="#2A44A1" p-id="3734"></path><path d="M921.6 409.6v409.6a102.4 102.4 0 0 1-102.4 102.4H256z" fill="#3D60F6" p-id="3735"></path></svg>
                    <div style="color: #2a44a1; text-decoration: none; outline: none; font-size: 11px; font-weight: bold;">
                        新人豪礼
                    </div>

                </a>


                <a style="margin-top: 12px; padding: 3px; text-align: right; display: inline-block; background: #cdb9b938; text-decoration: none; text-align: center; margin-right: 6px; border-radius: 7px;" id="A2" href="user_task.aspx?type=yrsw&back=1">
                    <svg t="1699545174919" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="10161" width="38" height="38">
                        <path d="M819.2 870.4H204.8a102.4 102.4 0 0 1-102.4-102.4V256h819.2v512a102.4 102.4 0 0 1-102.4 102.4z" fill="#3D60F6" p-id="10162"></path><path d="M819.2 358.4H102.4V256a102.4 102.4 0 0 1 102.4-102.4h614.4a102.4 102.4 0 0 1 102.4 102.4 102.4 102.4 0 0 1-102.4 102.4z" fill="#2A44A1" p-id="10163"></path><path d="M819.2 819.2m-153.6 0a153.6 153.6 0 1 0 307.2 0 153.6 153.6 0 1 0-307.2 0Z" fill="#FFBA40" p-id="10164"></path><path d="M804.2496 865.6896a10.24 10.24 0 0 1-7.2704-3.072l-36.2496-36.1472a10.24 10.24 0 0 1 14.5408-14.5408l28.9792 28.9792 65.1264-65.1264a10.24 10.24 0 0 1 14.4384 14.4384l-72.3968 72.3968a10.24 10.24 0 0 1-7.168 3.072z" fill="#FFFFFF" p-id="10165"></path><path d="M665.6 358.4L409.6 102.4 153.6 358.4h512z" fill="#36C196" p-id="10166"></path><path d="M768 358.4L512 102.4 256 358.4h512z" fill="#FFBA40" p-id="10167"></path><path d="M475.7504 241.0496a51.2 51.2 0 0 0 72.4992 0L665.6 358.4H358.4z" fill="#FFFFFF" p-id="10168"></path><path d="M409.6 665.6H102.4V460.8h307.2a102.4 102.4 0 0 1 102.4 102.4 102.4 102.4 0 0 1-102.4 102.4z" fill="#36C196" p-id="10169"></path><path d="M409.6 563.2m-51.2 0a51.2 51.2 0 1 0 102.4 0 51.2 51.2 0 1 0-102.4 0Z" fill="#FFFFFF" p-id="10170"></path></svg>
                    <div style="color: #2a44a1; text-decoration: none; outline: none; font-size: 11px; font-weight: bold;">
                        推荐有好礼
                    </div>

                </a>

                <a style="margin-top: 12px; padding: 3px; text-align: right; display: inline-block; background: #d5d5d538; text-decoration: none; text-align: center; margin-right: 6px; border-radius: 7px;" id="A3" href="partners_manage.aspx">
                    <svg t="1699545505093" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="20731" width="38" height="38">
                        <path d="M224 640V224a96 96 0 0 1 96-96h384a96 96 0 0 1 96 96V640z" fill="#D0E2FE" p-id="20732"></path><path d="M800 896H224a96 96 0 0 1-96-96V496a32 32 0 0 1 44.3-30l302.8 126.6a95.4 95.4 0 0 0 73.8 0l302.8-126.1a32 32 0 0 1 44.3 30V800a96 96 0 0 1-96 96zM608 256H416a32 32 0 0 0 0 64h192a32 32 0 1 0 0-64zM544 384H480a32 32 0 0 0 0 64h64a32 32 0 1 0 0-64z" fill="#3F86FF" p-id="20733"></path><path d="M512 720m-48 0a48 48 0 1 0 96 0 48 48 0 1 0-96 0Z" fill="#FFFFFF" p-id="20734"></path></svg>
                    <div style="color: #2a44a1; text-decoration: none; outline: none; font-size: 11px; font-weight: bold;">领取佣金</div>

                </a>--%>

                <%--<a style="margin-top: 12px; padding: 3px; text-align: right; display: inline-block; background: #d5d5d538; text-decoration: none; text-align: center; margin-right: 6px; border-radius: 7px;" id="A4" href="lend_list.aspx">
                    <svg t="1701314425537" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="15742" width="30" height="30">
                        <path d="M969.142857 621.714286a36.571429 36.571429 0 0 1 0 73.142857h-182.857143a36.571429 36.571429 0 0 1 0-73.142857h182.857143zM969.142857 731.428571a36.571429 36.571429 0 0 1 0 73.142858h-182.857143a36.571429 36.571429 0 0 1 0-73.142858h182.857143zM969.142857 841.142857a36.571429 36.571429 0 0 1 0 73.142857h-182.857143a36.571429 36.571429 0 0 1 0-73.142857h182.857143z" fill="#333333" p-id="15743"></path><path d="M18.285714 256h950.857143v73.142857H18.285714z" fill="#333333" opacity=".396" p-id="15744"></path><path d="M838.546286 73.142857c70.326857 0 126.829714 57.782857 130.413714 129.536l0.182857 7.204572V512a36.571429 36.571429 0 0 1-72.886857 4.278857L896 512V209.92c0-33.792-23.588571-60.672-52.589714-63.414857L838.582857 146.285714H148.845714c-29.549714 0-54.674286 25.124571-57.234285 58.038857L91.428571 209.92v567.661714c0 33.755429 23.588571 60.635429 52.589715 63.378286L148.845714 841.142857H640a36.571429 36.571429 0 0 1 4.278857 72.886857L640 914.285714H148.882286C78.555429 914.285714 22.052571 856.502857 18.468571 784.749714L18.285714 777.545143V209.883429c0-72.594286 54.272-132.754286 123.611429-136.557715L148.882286 73.142857H838.582857z" fill="#333333" p-id="15745"></path><path d="M676.571429 438.857143a40.155429 40.155429 0 0 1 6.070857 0.475428l0.658285 0.146286a35.84 35.84 0 0 1 28.452572 25.892572 35.913143 35.913143 0 0 1-9.325714 35.913142l-175.542858 175.542858a36.571429 36.571429 0 0 1-48.274285 3.035428l-3.437715-3.035428-61.915428-61.878858-105.801143 105.764572a36.571429 36.571429 0 0 1-48.274286 3.035428l-3.437714-3.035428a36.571429 36.571429 0 0 1-3.035429-48.274286l3.035429-3.437714 131.657143-131.657143a36.571429 36.571429 0 0 1 48.274286-3.035429l3.437714 3.035429 61.915428 61.878857 57.965715-57.965714-39.936-39.972572c-22.052571-22.016-8.045714-59.026286 21.686857-62.208L544.914286 438.857143z" fill="#E50738" p-id="15746"></path></svg>
                    <div style="color: #2a44a1; text-decoration: none; outline: none; font-size: 11px; font-weight: bold;">我要赚币</div>

                </a>--%>
            </div>
        </div>





        <script>
            //晒出今日奖励
            var share_reward = function () {

                v3api("send_message", {
                    data: {
                        msgtype: 'card',
                        card_type: 'share_reward',
                        touser: -1,
                        from_type: chatData.type,
                        chatid: chatData.id
                    }
                }, function (e) {
                    console.log('share_reward', e);
                })


            }

            //今日盈利排行榜
            var open_ranking = function () {

                v3api("daily_ranking", {
                    data: {
                        from_type: chatData.type,
                        chatid: chatData.id
                    }
                }, function (e) {
                    console.log('daily_ranking', e);
                })
            }



            //晒出今日奖励
            var share_gamedata = function () {

                v3api("send_message", {
                    data: {
                        msgtype: 'card',
                        card_type: 'game_data',
                        touser: -1,
                        from_type: chatData.type,
                        chatid: chatData.id
                    }
                }, function (e) {
                    console.log('share_gamedata', e);
                })


            }


            //晒出团队信息
            var share_teamdata = function () {

                v3api("send_message", {
                    data: {
                        msgtype: 'card',
                        card_type: 'team_data',
                        touser: -1,
                        from_type: chatData.type,
                        chatid: chatData.id
                    }
                }, function (e) {
                    console.log('share_teamdata', e);
                })


            }
        </script>



        <div style="display: flex; align-items: center; padding: 12px;">


            <svg class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="27857" width="16" height="16" onclick="back_list()">
                <path d="M716.8 972.8a51.2 51.2 0 0 1-36.352-14.848l-409.6-409.6a51.2 51.2 0 0 1 0-72.192l409.6-409.6a51.2 51.2 0 0 1 72.192 72.192L379.392 512l373.248 373.248A51.2 51.2 0 0 1 716.8 972.8z" fill="#333C4F" p-id="27858"></path></svg>

            <div style="font-size: 20px; margin-left: 10px;" id="contact_username">
            </div>

            <div style="margin-left: auto;">
                <svg t="1696445912731" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="14940" width="16" height="16">
                    <path d="M150.528 431.104q37.888 0 58.368 24.064t20.48 51.712l0 11.264q0 34.816-17.92 58.88t-59.904 24.064l-7.168 0q-38.912 0-61.952-21.504t-23.04-59.392l0-14.336q0-13.312 5.632-26.624t15.872-24.064 25.6-17.408 33.792-6.656l10.24 0zM519.168 431.104q37.888 0 58.368 24.064t20.48 51.712l0 11.264q0 34.816-17.92 58.88t-59.904 24.064l-7.168 0q-38.912 0-61.952-21.504t-23.04-59.392l0-14.336q0-13.312 5.632-26.624t15.872-24.064 25.6-17.408 33.792-6.656l10.24 0zM887.808 431.104q37.888 0 58.368 24.064t20.48 51.712l0 11.264q0 34.816-17.92 58.88t-59.904 24.064l-7.168 0q-38.912 0-61.952-21.504t-23.04-59.392l0-14.336q0-13.312 5.632-26.624t15.872-24.064 25.6-17.408 33.792-6.656l10.24 0z" p-id="14941" fill="#333C4F"></path></svg>
            </div>

        </div>


        <!--顶部功能 -->
        <div style="background: rgb(82, 112, 216); padding: 15px; display: none;" id="redbag_poll">

            <div style="display: flex;">
                <div style="color: #fff;" id="redbag_poll_text">
                    <%=uConfig.stcdata("redbag_tips") %>
                </div>

                <div class="d-flex align-center" style="margin-left: auto;">
                    <span class="openlobby-countdown-tips" style="font-size: 12px; color: #fff;">下一波倒计时</span><div class="countdown-container d-flex align-center">
                        <div class="countdown-item-box d-flex align-center justify-center" style="color: rgb(82, 112, 216);" id="redbag_hour">00</div>
                        <div class="countdown-item-box d-flex align-center justify-center" style="color: rgb(82, 112, 216);" id="redbag_minute">00</div>
                        <div class="countdown-item-box d-flex align-center justify-center" style="color: rgb(82, 112, 216);" id="redbag_second">00</div>
                    </div>
                </div>
            </div>

        </div>

        <!--聊天内容 -->
        <div id="chat_box" style="padding: 16px; overflow-y: auto; flex-grow: 1" onclick="close_openBox()">
        </div>

        
        <!--群发消息 -->
        <div id="allsend_box" style="padding: 16px; overflow-y: auto; flex-grow: 1; background: #EDEDED; display: none;" onclick="close_openBox()">
            <h2 style="text-align: center; color: #888;">将给以下<span class="allsend_num"></span>位用户群发消息</h2>
            <div id="allsend_user">
            </div>
        </div>

        <div id="anonyNoSpeak" style="background: rgb(249, 249, 249); text-align: center; padding: 18px; font-weight: bold; border-top: 1px solid rgb(221, 221, 221); display: none;" onclick="javascript:location.href='login.aspx';">
            游客禁止发言
        </div>

        <div id="nospeak" style="background: #f9f9f9; text-align: center; padding: 18px; border-top: 1px solid #ddd;">
            全体禁言中...
        </div>

        <div id="reply_data" style="margin-bottom: -1px; z-index: 99999; background: #F7F7F7; padding: 8px; padding-bottom: 0; font-size: 13px; display: none;">
            <div style="display: flex;">
                <div style="background: #ebebeb; padding: 10px; border-radius: 8px; width: 100%;">
                    <div>
                        <b style="color: #3e56cf;" id="reply_name">回复：</b>
                    </div>
                    <div style="color: #2A2B2C; margin-top: 3px;" id="reply_content">
                    </div>
                </div>
                <div style="display: flex; align-items: center; padding: 0 8px; cursor: pointer;" onclick="close_reply()">
                    <svg t="" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="3732" width="18" height="18">
                        <path d="M0 0h1024v1024H0z" fill="#bfbfbf" fill-opacity="0" p-id="3733"></path><path d="M240.448 168l2.346667 2.154667 289.92 289.941333 279.253333-279.253333a42.666667 42.666667 0 0 1 62.506667 58.026666l-2.133334 2.346667-279.296 279.210667 279.274667 279.253333a42.666667 42.666667 0 0 1-58.005333 62.528l-2.346667-2.176-279.253333-279.253333-289.92 289.962666a42.666667 42.666667 0 0 1-62.506667-58.005333l2.154667-2.346667 289.941333-289.962666-289.92-289.92a42.666667 42.666667 0 0 1 57.984-62.506667z" fill="#bfbfbf" p-id="3734"></path></svg>
                </div>
            </div>
        </div>

        <div id="message_sendbox" class="wxlist noclick" style="background: #F7F7F7; border-top: 1px solid #ddd; align-items: center; box-sizing: border-box; display: block;">

            <div class="noclick" style="display: flex; width: 100%; align-items: center; padding: 13px; box-sizing: border-box;">
                <svg t="1696449836998" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="8294" width="38" height="38">
                    <path d="M512 23.272727c269.917091 0 488.727273 218.810182 488.727273 488.727273s-218.810182 488.727273-488.727273 488.727273S23.272727 781.917091 23.272727 512 242.082909 23.272727 512 23.272727z m0 69.818182C281.018182 93.090909 93.090909 281.018182 93.090909 512s187.927273 418.909091 418.909091 418.909091 418.909091-187.927273 418.909091-418.909091S742.981818 93.090909 512 93.090909zM209.454545 418.909091h93.09091v-93.090909H209.454545v93.090909z m170.65891 0h93.090909v-93.090909h-93.090909v93.090909z m170.682181 0h93.090909v-93.090909h-93.090909v93.090909zM721.454545 418.909091h93.09091v-93.090909h-93.09091v93.090909zM209.454545 605.090909h93.09091v-93.090909H209.454545v93.090909z m523.636364 162.909091h-442.181818a34.909091 34.909091 0 0 1 0-69.818182h442.181818a34.909091 34.909091 0 0 1 0 69.818182m-352.977454-162.909091h93.090909v-93.090909h-93.090909v93.090909z m170.682181 0h93.090909v-93.090909h-93.090909v93.090909zM721.454545 605.090909h93.09091v-93.090909h-93.09091v93.090909z" fill="#000000" p-id="8295"></path></svg>
                <div style="width: 100%; padding: 0 10px;">
                    <textarea style="background: #fff; width: 100%; border: 0; box-sizing: border-box; height: 33px; padding: 7px; font-size: 16px; outline: none; max-height: 100px; resize: none;" id="send_message"></textarea>
                    <%--<div contenteditable="true" style="background: #fff;width: 100%;border: 0;box-sizing: border-box;padding: 7px;font-size: 16px;outline: none;max-height: 100px;resize: none;overflow-y: auto;" id="send_message"></div>--%>
                </div>
                <div style="flex-shrink: 0; display: flex;">
                    <svg t="1696450156970" class="icon" viewBox="0 0 1025 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="9483" width="28" height="28" style="margin-right: 9px;" onclick="toggle_emoji()">
                        <path d="M512 1024c-137.309091 0-265.309091-53.527273-363.054545-148.945455C53.527273 777.309091 0 649.309091 0 512S53.527273 246.690909 148.945455 148.945455C246.690909 53.527273 374.690909 0 512 0s265.309091 53.527273 363.054545 148.945455c200.145455 200.145455 200.145455 523.636364 0 723.781818C777.309091 970.472727 649.309091 1024 512 1024m0-956.509091c-118.690909 0-230.4 46.545455-314.181818 130.327273-83.781818 83.781818-130.327273 195.490909-130.327273 314.181818s46.545455 230.4 130.327273 314.181818c83.781818 83.781818 195.490909 130.327273 314.181818 130.327273s230.4-46.545455 314.181818-130.327273C1000.727273 651.636364 1000.727273 370.036364 826.181818 195.490909 742.4 114.036364 630.690909 67.490909 512 67.490909M330.472727 651.636364s60.509091 95.418182 181.527273 95.418181c121.018182 0 202.472727-95.418182 202.472727-95.418181s44.218182 0 44.218182 48.872727c0 0-79.127273 111.709091-246.690909 111.709091-167.563636 0-223.418182-111.709091-223.418182-111.709091s-2.327273-48.872727 41.890909-48.872727m32.581818-304.872728c-34.909091 0-62.836364 27.927273-62.836363 62.836364 0 34.909091 27.927273 62.836364 62.836363 62.836364 34.909091 0 62.836364-27.927273 62.836364-62.836364 0-34.909091-27.927273-62.836364-62.836364-62.836364m321.163637 0c-34.909091 0-62.836364 27.927273-62.836364 62.836364 0 34.909091 27.927273 62.836364 62.836364 62.836364 34.909091 0 62.836364-27.927273 62.836363-62.836364 0-34.909091-27.927273-62.836364-62.836363-62.836364m0 0z" p-id="9484" fill="#000000"></path></svg>

                    <svg t="1696450258494" class="icon" viewBox="0 0 1025 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="13501" width="28" height="28" onclick="toggle_other()" id="other_button">
                        <path d="M852.812 166.713c-92.715-89.28-212.9-140.789-339.954-140.789s-250.672 51.509-339.953 140.79c-188.863 188.862-188.863 491.043 0 679.906 89.28 89.28 212.9 140.789 339.953 140.789S763.531 935.9 852.812 846.62c188.863-188.863 188.863-491.044 0-679.907m-48.074 631.833c-78.98 78.979-181.996 120.185-291.88 120.185s-212.9-41.206-291.879-120.185c-161.392-161.392-161.392-422.367 0-583.759 78.98-78.979 181.996-120.185 291.88-120.185s212.9 41.206 291.879 120.185c161.392 161.392 161.392 422.367 0 583.759" fill="#000000" p-id="13502"></path><path d="M547.197 472.328V297.2c0-20.603-13.735-34.338-34.339-34.338S478.52 276.597 478.52 297.2v175.128H303.392c-20.603 0-34.339 13.735-34.339 34.339 0 10.301 3.434 17.169 10.302 24.037s13.736 10.301 24.037 10.301H478.52v175.128c0 10.302 3.434 17.17 10.301 24.037 6.868 6.868 13.736 10.302 24.037 10.302 20.604 0 34.34-13.736 34.34-34.34V541.006h175.127c20.603 0 34.338-13.735 34.338-34.338s-13.735-34.34-34.338-34.34H547.197z" fill="#000000" p-id="13503"></path></svg>

                    <a style="background: #07C062; color: #fff; padding: 2px 8px; display: inline-block; width: 40px; text-align: center; cursor: pointer; display: none;" id="send_button">发送</a>



                </div>

            </div>


            <%--表情菜单--%>
            <div id="emoji_list" style="display: none;">
                <div style="background: #ECECEC; padding: 13px; display: flex; flex-wrap: wrap; max-height: 200px; overflow-y: auto;">
                    <%for (int i = 0; i < 130; i++)
                      {
                    %>
                    <div style="width: 12.5%; text-align: center; padding: 10px 0;">
                        <img src="kindeditor/plugins/emoticons/images/<%=i %>.gif" style="width: 30px; height: 30px;" onclick="get_emoji('<%=get_emoji_data(i.ToString()) %>')">
                    </div>
                    <%
                      } %>
                </div>
            </div>




            <%--图片与红包--%>
            <div id="other_list" style="display: none; padding-bottom: 20px;">
                <div style="background: #F7F7F7; display: flex; flex-wrap: wrap; max-height: 200px; overflow-y: auto;">
                    <div style="width: 25%; text-align: center; font-size: 12px; cursor: pointer;" id="sendimg">



                        <div style="display: inline-block; border-radius: 8px; text-align: center; padding: 13px 0;" onclick="upload_images()">

                            <svg t="" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="1443" width="28" height="28" style="background: #fff; padding: 10px; border-radius: 8px; margin-bottom: 5px;">
                                <path d="M0.020978 127.521349l0 831.492568 1023.372713 0L1023.393691 127.521349 0.020978 127.521349 0.020978 127.521349zM959.433984 895.053186 63.980685 895.053186 63.980685 191.482079l895.453299 0L959.433984 895.053186 959.433984 895.053186zM703.585945 351.386976c0 52.969399 42.943045 95.937003 95.944166 95.937003s95.944166-42.968627 95.944166-95.937003c0-53.000098-42.943045-95.944166-95.944166-95.944166S703.585945 298.391994 703.585945 351.386976L703.585945 351.386976zM895.473253 831.094502 127.939369 831.094502l191.883215-511.691985L575.664483 639.21231l127.921461-95.944166L895.473253 831.094502 895.473253 831.094502zM895.473253 831.094502" fill="#272636" p-id="1444"></path></svg>
                            <div>发送图片</div>

                        </div>
                    </div>






                    <div style="width: 25%; text-align: center; font-size: 12px; cursor: pointer; padding: 13px 0;" onclick="get_play_records()">
                        <div style="display: block;">

                            <svg t="" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="149946" width="28" height="28" style="background: #fff; padding: 10px; border-radius: 8px; margin-bottom: 5px;">
                                <path d="M622.6944 247.3472H410.9312c-30.8224 0-55.808-24.9856-55.808-55.808v-48.0768c0-30.8224 24.9856-55.808 55.808-55.808h211.7632c30.8224 0 55.808 24.9856 55.808 55.808v48.0768c-0.0512 30.8736-25.0368 55.808-55.808 55.808z" fill="#FF9552" p-id="149947"></path><path d="M762.112 143.7696h-35.9936c1.5872 7.9872 2.4576 16.1792 2.4576 24.6272 0 68.4544-55.5008 123.9552-123.9552 123.9552H428.9024c-68.4544 0-123.9552-55.5008-123.9552-123.9552 0-8.448 0.8704-16.6912 2.4576-24.6272h-35.9936c-62.8224 0-113.7664 50.944-113.7664 113.7664v547.1232c0 62.8224 50.944 113.7664 113.7664 113.7664h490.7008c62.8224 0 113.7664-50.944 113.7664-113.7664V257.536c0.0512-62.8224-50.8928-113.7664-113.7664-113.7664zM518.0416 773.12H317.5936c-21.8624 0-39.5776-17.7152-39.5776-39.5776 0-21.8624 17.7152-39.5776 39.5776-39.5776h200.448c21.8624 0 39.5776 17.7152 39.5776 39.5776 0 21.8624-17.7152 39.5776-39.5776 39.5776z m203.5712-142.7968H317.5936c-21.8624 0-39.5776-17.7152-39.5776-39.5776 0-21.8624 17.7152-39.5776 39.5776-39.5776h404.0192c21.8624 0 39.5776 17.7152 39.5776 39.5776 0 21.8624-17.7152 39.5776-39.5776 39.5776z m0-156.3648H317.5936c-21.8624 0-39.5776-17.7152-39.5776-39.5776s17.7152-39.5776 39.5776-39.5776h404.0192c21.8624 0 39.5776 17.7152 39.5776 39.5776s-17.7152 39.5776-39.5776 39.5776z" fill="#FC7032" p-id="149948"></path><path d="M875.8784 491.2128V257.536c0-62.8224-50.944-113.7664-113.7664-113.7664h-35.9936c1.5872 7.9872 2.4576 16.1792 2.4576 24.6272 0 68.4544-55.5008 123.9552-123.9552 123.9552H428.9024c-68.4544 0-123.9552-55.5008-123.9552-123.9552 0-8.448 0.8704-16.6912 2.4576-24.6272h-35.9936c-62.8224 0-113.7664 50.944-113.7664 113.7664v547.1232c0 53.504 36.9664 98.2528 86.7328 110.3872 8.3456 0.3072 16.7424 0.512 25.1392 0.512 278.8352 0.0512 516.2496-176.7424 606.3616-424.3456zM317.5936 394.8544h404.0192c21.8624 0 39.5776 17.7152 39.5776 39.5776 0 21.8624-17.7152 39.5776-39.5776 39.5776H317.5936a39.57248 39.57248 0 1 1 0-79.1552zM518.0416 773.12H317.5936c-21.8624 0-39.5776-17.7152-39.5776-39.5776s17.7152-39.5776 39.5776-39.5776h200.448c21.8624 0 39.5776 17.7152 39.5776 39.5776s-17.7152 39.5776-39.5776 39.5776z m-200.448-142.7968c-21.8624 0-39.5776-17.7152-39.5776-39.5776 0-21.8624 17.7152-39.5776 39.5776-39.5776h404.0192c21.8624 0 39.5776 17.7152 39.5776 39.5776 0 21.8624-17.7152 39.5776-39.5776 39.5776H317.5936z" fill="#FF7C33" p-id="149949"></path><path d="M356.8128 630.3232h-39.2192c-21.8624 0-39.5776-17.7152-39.5776-39.5776 0-21.8624 17.7152-39.5776 39.5776-39.5776H488.448c29.952-23.296 57.8048-49.1008 83.2512-77.2096H317.5936c-21.8624 0-39.5776-17.7152-39.5776-39.5776 0-21.8624 17.7152-39.5776 39.5776-39.5776h315.2384c31.8464-48.5376 57.344-101.6832 75.264-158.1568-22.1696 33.536-60.2112 55.7056-103.424 55.7056H428.9024c-68.4544 0-123.9552-55.5008-123.9552-123.9552 0-8.448 0.8704-16.6912 2.4576-24.6272h-35.9936c-62.8224 0-113.7664 50.944-113.7664 113.7664v425.7792c70.3488-6.9632 137.3184-25.2416 199.168-52.992zM728.0128 156.2112c0.7168-4.1472 1.4336-8.2944 2.0992-12.4416h-3.9424c0.8192 4.096 1.4336 8.2432 1.8432 12.4416z" fill="#FF9552" p-id="149950"></path><path d="M375.7568 280.32c-41.8304-19.9168-70.8096-62.464-70.8096-111.872 0-8.448 0.8704-16.6912 2.4576-24.6272h-35.9936c-62.8224 0-113.7664 50.944-113.7664 113.7664V417.28a645.46304 645.46304 0 0 0 218.112-136.96zM514.7648 87.7056H434.944c-44.0832 0-79.8208 35.7376-79.8208 79.8208 0 35.7888 23.6032 66.0992 56.064 76.2368 41.6768-46.4384 76.6464-98.9696 103.5776-156.0576z" fill="#FFA56A" p-id="149951"></path></svg>
                            <div>分享注单</div>

                        </div>
                    </div>


                    <div style="width: 25%; text-align: center; font-size: 12px; cursor: pointer; padding: 13px 0;" onclick="share_gamedata()">
                        <div style="display: block;">

                            <svg t="1709760910204" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="47817" width="25" height="25" style="background: #fff; padding: 10px; border-radius: 8px; margin-bottom: 5px;">
                                <path d="M236.31872 894.55616h588.8768l-41.23136-461.8496-197.77536 219.06432-124.61056-105.72288-224.78848 216.09472-13.9776 19.27168z" fill="#BAD4FF" p-id="47818"></path><path d="M779.99104 431.54432h48.44032v462.94528h-48.44032V431.54432zM586.1888 650.03008h48.44032v244.45952h-48.44032v-244.45952zM387.27168 619.95008h48.4352v274.53952h-48.4352v-274.53952zM188.35456 762.14272h48.4352v132.34688h-48.4352v-132.34688zM829.7472 141.73184l-195.58912 45.65504 63.36512 62.01344-175.30368 191.04256-87.99744-86.72768-249.10336 249.10336 34.24768 34.2528 215.10656-215.10656 89.4976 88.20736 208.19968-226.87744 64.27136 62.88896z" fill="#4E8CEE" p-id="47819"></path></svg>
                            <div>分享业绩</div>

                        </div>
                    </div>


                    <%--<div style="width: 25%; text-align: center; font-size: 12px; cursor: pointer; padding: 13px 0;" onclick="share_teamdata()">
                        <div style="display: block;">

                            <svg t="" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="47817" width="25" height="25" style="background: #fff; padding: 10px; border-radius: 8px; margin-bottom: 5px;">
                                <path d="M593.9 619.6l-45-45c-17.7 7.1-33.7 15-48.2 23.6v233.4h54.6l38.6-212z" fill="#FFDB69" p-id="3677"></path><path d="M629.3 352.9m-147.8 0a147.8 147.8 0 1 0 295.6 0 147.8 147.8 0 1 0-295.6 0Z" fill="#FFECB6" p-id="3678"></path><path d="M709.6 574.7l-45 45 38.6 212.1H896c0-0.1 6.4-180-186.4-257.1z" fill="#FFDB69" p-id="3679"></path><path d="M629.3 619.6l-32.9-32.9h65.8zM629.3 619.6l-32.2 154.3 32.2 57.8 32.1-57.8zM603.6 510.4c-88.6 0-160.7-72.1-160.7-160.7 0-44.5 14.3-66.4 21.9-78.1l1.6-2.5c2.8-4.5 8.8-5.8 13.3-3s5.8 8.8 3 13.3l-1.7 2.7c-7 10.8-18.8 28.8-18.8 67.6 0 78 63.4 141.4 141.4 141.4S745 427.7 745 349.7s-63.4-141.4-141.4-141.4c-44.8 0-68.5 13.8-90.2 34.4-3.9 3.7-10 3.5-13.6-0.4-3.7-3.9-3.5-10 0.4-13.6 21.7-20.6 49.3-39.7 103.5-39.7 88.6 0 160.7 72.1 160.7 160.7s-72.2 160.7-160.8 160.7z" fill="#F7931E" p-id="3680"></path><path d="M870.3 834.9c-5.3 0-9.6-4.3-9.6-9.6 0-141.7-115.3-257.1-257.1-257.1-39.1 0-72.3 5.7-101.7 17.6-5 2-10.6-0.4-12.5-5.3-2-4.9 0.4-10.6 5.3-12.5 31.7-12.8 67.3-19 108.9-19 152.4 0 276.3 124 276.3 276.3 0 5.3-4.3 9.6-9.6 9.6z" fill="#F7931E" p-id="3681"></path><path d="M430 352.9m-147.8 0a147.8 147.8 0 1 0 295.6 0 147.8 147.8 0 1 0-295.6 0Z" fill="#FFECB6" p-id="3682"></path><path d="M349.7 574.7l45 45-38.6 212.1H163.3c0-0.1-6.4-180 186.4-257.1zM510.4 574.7l-45 45 38.6 212h192.8s6.4-179.9-186.4-257z" fill="#F4BA64" p-id="3683"></path><path d="M430 619.6l-32.9-32.9H463zM430 619.6l-32.1 154.3 32.1 57.8 32.2-57.8z" fill="#F7931E" p-id="3684"></path><path d="M404.3 510.4c-88.6 0-160.7-72.1-160.7-160.7 0-44.5 14.3-66.4 21.9-78.1l1.6-2.5c2.9-4.5 8.8-5.8 13.3-3s5.8 8.8 3 13.3l-1.7 2.7c-7 10.8-18.8 28.8-18.8 67.6 0 78 63.4 141.4 141.4 141.4s141.4-63.4 141.4-141.4-63.4-141.4-141.4-141.4c-44.8 0-68.5 13.8-90.2 34.4-3.9 3.7-10 3.5-13.6-0.4-3.7-3.9-3.5-10 0.4-13.6 21.7-20.6 49.3-39.7 103.5-39.7 88.6 0 160.7 72.1 160.7 160.7s-72.2 160.7-160.8 160.7zM671 834.9c-5.3 0-9.6-4.3-9.6-9.6 0-141.7-115.3-257.1-257.1-257.1-68.4 0-121.2 18.1-161.5 55.3-3.9 3.6-10 3.4-13.6-0.5-3.6-3.9-3.4-10 0.5-13.6 44-40.7 101.1-60.4 174.6-60.4 152.4 0 276.3 124 276.3 276.3 0.1 5.3-4.2 9.6-9.6 9.6z" fill="" p-id="3685"></path><path d="M137.6 834.9c-5.3 0-9.6-4.3-9.6-9.6 0-74.6 19.3-129.1 62.6-176.7 3.6-3.9 9.7-4.2 13.6-0.7 3.9 3.6 4.2 9.7 0.7 13.6-40.4 44.5-57.6 93.4-57.6 163.8 0 5.3-4.3 9.6-9.7 9.6z" fill="" p-id="3686"></path>

                            </svg>
                            <div>分享团队信息</div>
                        </div>
                    </div>--%>
                </div>
            </div>


        </div>


    </div>












    <!--今日盈利排行榜 -->
    <div id="daily_award_liist" class="base_page" style="display: none;">

        <div style="display: flex; align-items: center; padding: 18px 12px; background: #FEC009; color: #fff;">




            <div style="font-size: 16px; margin-left: 10px; width: 100%; text-align: center;" id="ranking_toptit">
                当前排行榜【每15分钟更新排名】
            </div>






            <svg t="1696778758025" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="3945" width="16" height="16" onclick="show_page('#chat_message');">
                <path d="M453.44 512L161.472 220.032a41.408 41.408 0 0 1 58.56-58.56L512 453.44 803.968 161.472a41.408 41.408 0 0 1 58.56 58.56L570.56 512l291.968 291.968a41.408 41.408 0 0 1-58.56 58.56L512 570.56 220.032 862.528a41.408 41.408 0 0 1-58.56-58.56L453.44 512z" fill="#ffffff" p-id="3946"></path></svg>


        </div>


        <div style="text-align: center; font-weight: bold; padding: 10px 0;">
            本群奖励活动【每日<%=Convert.ToDateTime("2030-01-01 " + uConfig.stcdata("ranking_stop_time")).ToString("HH:mm") %>分截止统计】开始领取奖励
        </div>

        <ul id="jsfoot02" class="noticTipTxt">
            <li>排名每15分钟更新一次</li>
            <li>每日<%=Convert.ToDateTime("2030-01-01 " + uConfig.stcdata("ranking_stop_time")).ToString("HH:mm") %>分截止统计领取奖励</li>
        </ul>





        <!-- 消息滚动 -->
        <style>
            .noticTipTxt {
                color: #8500ff;
                height: 22px;
                min-height: 22px;
                line-height: 22px;
                overflow: hidden;
                list-style: none;
                padding: 0;
                font-size: 13px;
                /*background: #2222220f;*/
                display: inline-block;
                border-radius: 50px;
                margin: 10px;
                margin-top: 0;
            }

                .noticTipTxt li {
                    height: 22px;
                    line-height: 22px;
                    text-align: center;
                }

                .noticTipTxt a {
                    color: #222;
                    font-size: 14px;
                    text-decoration: none;
                }

                    .noticTipTxt a:hover {
                        color: #ff7300;
                        text-decoration: underline;
                    }


                .noticTipTxt.list_notice {
                    height: 300px;
                    line-height: 300px;
                }

                    .noticTipTxt.list_notice li {
                        height: 30px;
                        line-height: 30px;
                    }

                    .noticTipTxt.list_notice a {
                        color: #000;
                        font-size: 12px;
                    }
        </style>



        <script src="../static/js/scrolltext.js"></script>


        <%-- #8500ff--%>





        <div id="ranklist" class="flex_grow" style="background: #fff;">

            <%--<div style="display: flex; border-bottom: 1px solid #f1f1f1; padding: 18px 10px; color: #5c1cf7;">
                <span style="display: flex; align-items: center; position: relative; left: -3px;">



                    <img src="/static/images/r1.png" alt="" width="50" style="margin-right: 10px;">
                    <img src="static/images/avatar-1.jpg" width="60" height="60" style="margin-right: 10px;">

                    <div>
                        <b>29***34</b>
                        <div style="color: #5a5b5c; font-size: 14px; margin-top: 10px;">
                            盈利金额：<b style="color: #F4941D;">188.00</b>
                        </div>
                        <b></b>
                    </div>
                </span>

            </div>--%>
        </div>


        <script>
            document.getElementById('send_message').addEventListener('keydown', function (e) {
                if (e.key === 'Enter' && e.ctrlKey) {
                    // 用户按下 Ctrl + Enter 键
                    // 执行换行操作，例如：
                    this.value += '\n';

                    //event.preventDefault(); // 阻止默认的换行行为
                    //var div = event.target;
                    //var sel = window.getSelection();
                    //var range = sel.getRangeAt(0);

                    //// 获取光标所在的节点和偏移量
                    //var node = range.startContainer;
                    //var offset = range.startOffset;

                    //// 在光标位置插入换行符
                    //var br = document.createElement("br");
                    //node.parentNode.insertBefore(br, node);

                    //// 在换行符后插入空白字符
                    //var textNode = document.createTextNode("\u00a0"); // 添加一个空白字符
                    //node.parentNode.insertBefore(textNode, br.nextSibling);

                    //// 创建一个新的范围，将光标移动到新插入的文本节点的开头
                    //var newRange = document.createRange();
                    //newRange.setStart(textNode, 0);
                    //newRange.setEnd(textNode, 0);
                    //sel.removeAllRanges();
                    //sel.addRange(newRange);


                } else if (e.key === 'Enter') {
                    // 用户按下 Enter 键
                    e.preventDefault(); // 阻止默认的换行行为
                    // 执行您的指定命令，例如：
                    document.getElementById('send_button').click();
                }
            });

            var scrollup;

            var rank_reward_list = '<%=uConfig.stcdata("share_ranking_list").Replace("\n", "##") %>'.split('##');

            var get_ranking_list = function () {
                show_page('#daily_award_liist');

                if (!show_groupList(chatData.id)) {
                    v3api("ranking_list", {
                    }, function (e) {
                        $('#ranklist').html('');

                        try {
                            scrollup.Stop();
                        } catch (e) {

                        }
                        $('#jsfoot02').html('');
                        if (e.draw_time < 0) {
                            $('#jsfoot02').append('<li>排名每15分钟更新一次</li>');
                            $('#jsfoot02').append(' <li>每日<%=Convert.ToDateTime("2030-01-01 " + uConfig.stcdata("ranking_stop_time")).ToString("HH:mm") %>分截止统计领取奖励</li>');
                            $('#jsfoot02').append('<li>当日分享排名才能上榜</li>');
                        }

                        $('#ranking_toptit').html('您当前排名【' + (e.ranking == '' ? '未晒单' : '第' + e.ranking + '名') + '】可领取奖励【' + e.ranking_reward + '】');


                        for (var i = 0; i < e.list.length; i++) {
                            var obj = e.list[i];

                            var receive_money = "";
                            if (rank_reward_list.length >= i + 1) {
                                receive_money = rank_reward_list[i];
                            }

                            var receive_button = '<div style="margin-left: auto; text-align: center;">            <div style="color: #5a5b5c; margin-bottom: 10px; font-weight: bold; font-size: 14px;">奖励' + receive_money + '元</div>            <div>                <a style="background: #eee; color: #aaa; font-weight: bold; padding: 3px 15px; border-radius: 50px; font-size: 15px;cursor:pointer;" onclick="receive_reward(' + obj.ranking_number + ',0.2)">立即领取</a>            </div>        </div>';


                            if (e.draw_time >= 0) {
                                receive_button = ' <div style="margin-left: auto; text-align: center;">            <div style="color: red; margin-bottom: 10px; font-weight: bold; font-size: 14px;">奖励' + receive_money + '元</div>            <div>                <a style="background: #f32121; color: #ffff2f; font-weight: bold; padding: 3px 15px; border-radius: 50px; font-size: 15px;cursor:pointer;" onclick="receive_reward(' + obj.ranking_number + ',1.5)">立即领取</a>            </div>        </div>';
                            }


                            if (receive_money == "") {
                                receive_button = "";
                            } else {
                                if (e.draw_time >= 0) {
                                    $('#jsfoot02').append('<li>恭喜 ' + obj.name + ' 获得' + receive_money + '元奖励</li>');
                                }
                            }

                            $('#ranklist').append('<div style="display: flex; border-bottom: 1px solid #f1f1f1; padding: 18px 10px; color: #5c1cf7;">                <span style="display: flex; align-items: center; position: relative; left: -3px;">                  ' + (obj.ranking_number > 3 ? "<div style='color:#EEC994;font-size: 28px;text-shadow: 3px 3px 7px #EEC994;font-weight: bold;width:50px;text-align:center;margin-right:10px;'>" + obj.ranking_number + "</div>" : '<img src="/static/images/r' + obj.ranking_number + '.png" alt="" width="50" style="margin-right: 10px;">') + '                      <img src="' + obj.avatar + '" width="60" height="60" style="margin-right: 10px;">                    <div>                        <b style="white-space: nowrap;overflow: hidden;text-overflow: ellipsis;width: 100px;display: inline-block;font-size: 12px;">' + (obj.isme == 1 ? '<span style="font-size: 12px; background: #181717c4; color: #fff; padding: 2px 10px; border-radius: 50px; margin-right: 5px;">我</span>' : '') + obj.name + '</b>                        <div style="color: #5a5b5c; font-size: 14px; margin-top: 10px;">                            盈利金额：<b style="color: #F4941D;">' + parseFloat(obj.daily_award_amount).toFixed(2) + '</b>                        </div>                        <b></b>                    </div>                </span>     ' + receive_button + '       </div>');



                        }


                        scrollup = new ScrollText("jsfoot02");
                        scrollup.LineHeight = 22;        //单排文字滚动的高度
                        scrollup.Amount = 1;            //注意:子模块(LineHeight)一定要能整除Amount.
                        scrollup.Delay = 1;           //延时
                        scrollup.Timeout = 3000;
                        scrollup.Start();             //文字自动滚动
                        scrollup.Direction = "up";   //默认设置为文字向上滚动
                    })
                }
            }


            var receive_reward = function (rank, t) {


                layer.open({
                    type: 2,
                    content: '奖励领取中',
                    time: t,
                    shadeClose: false
                })

                setTimeout(function () {

                    v3api("receive_reward", {
                        data: {
                            rank: rank
                        }
                    }, function (e) {
                        //tp(e.msg);
                        show_page('#ranking_reward_result');
                        $('.redpacketsinfo-title-name').html('佣金排名奖励');
                        $('.redpacketsinfo-subtitle span').html('第' + e.ranking_number + '名奖励');
                        $('#ranking_reward_amount').html(e.receive_amount);
                    })


                }, t * 1000);





            }
        </script>




    </div>






    <!--佣金排名领取 -->
    <div id="ranking_reward_result" class="base_page" style="display: none; position: fixed; left: 0; width: 100%;">
        <div class="redpacketsinfo-overlay van-popup van-popup--center" style="animation-duration: 0s; z-index: 2011;">
            <header class="app-bar d-flex justify-space-between redpacketsinfo-header" style="height: 52px; z-index: 9999;">
                <div class="app-bar-left-box d-flex align-center">
                    <button class="app-bar-back-btn van-button van-button--default van-button--normal" type="button" onclick="show_page('#daily_award_liist');">
                        <div class="van-button__content">
                            <span class="van-button__text">
                                <svg class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="27857" width="16" height="16">
                                    <path d="M716.8 972.8a51.2 51.2 0 0 1-36.352-14.848l-409.6-409.6a51.2 51.2 0 0 1 0-72.192l409.6-409.6a51.2 51.2 0 0 1 72.192 72.192L379.392 512l373.248 373.248A51.2 51.2 0 0 1 716.8 972.8z" fill="#ffffff" p-id="27858"></path></svg>

                            </span>

                        </div>
                    </button>
                    <h2 class="app-bar-title van-ellipsis"></h2>
                    <h2 class="app-bar-title-sub van-ellipsis"></h2>
                </div>
            </header>
            <div class="redpacketsinfo-scroll">
                <div class="redpacketsinfo-info-box">
                    <div class="redpacketsinfo-title-bar d-flex align-center justify-center">
                        <img src="static/images/avatar-1.jpg" loading="lazy" class="auto-domain-image redpacketsinfo-title-headicon" style="width: 26px; height: 26px;"><div class="redpacketsinfo-title-name">佣金排名奖励</div>
                        <!---->
                    </div>
                    <div class="redpacketsinfo-subtitle d-flex align-center justify-center"><span style="text-align: center;">恭喜发财，大吉大利</span></div>


                    <div id="ranking_success_tip" style="font-size: 39px; text-align: center; font-weight: bold; color: #BF9F6F; padding-bottom: 22px;">
                        <span id="ranking_reward_amount">0.00</span><span style="margin-left: 7px; font-size: 12px; font-weight: 500;">元</span>

                        <div style="font-size: 12px; font-weight: 100; color: #b19770; margin-top: 8px;">
                            已转入您的账户余额
                        </div>
                    </div>

                    <div id="ranking_error_tip" style="font-size: 22px; font-weight: 100; color: rgb(115,111,105); margin-top: 8px; font-weight: bold; text-align: center; padding-bottom: 22px; display: none;">很遗憾，您手慢了！</div>


                </div>
                <div class="redpacketsinfo-division van-hairline--top van-hairline--bottom"></div>

            </div>
        </div>

    </div>













    <!--搜索好友 -->
    <%--<div id="search_friends" class="base_page" style="display: none;">

        <div style="display: flex; align-items: center; padding: 2px 10px;">

            <div style="position: relative; width: 100%;">

                <div style="position: absolute; top: 9px; left: 10px;">
                    <svg t="1696520315313" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2610" width="16" height="16">
                        <path d="M680.728354 651.326209c121.274064-137.208988 116.320239-346.908988-14.939247-478.166427-136.444579-136.433322-357.662913-136.433322-494.094188 0-136.443555 136.442532-136.443555 357.661889 0 494.105445 131.241067 131.253346 340.927763 136.204102 478.149031 14.942317l265.63187 265.63187 30.889521-30.877241L680.728354 651.326209zM649.273968 622.002346l-28.658713 28.668946c-120.345925 105.622596-303.678394 101.031021-418.524049-13.812587-119.651101-119.651101-119.651101-313.648466 0-433.299567C321.742307 83.909062 515.740696 83.909062 635.39282 203.569372 750.211868 318.380234 754.826979 501.656421 649.273968 622.002346z" fill="#2c2c2c" p-id="2611"></path></svg>

                </div>

                <input id="search_content" style="border: 0; background: #fff; width: 100%; padding: 9px; box-sizing: border-box; padding-left: 32px;" placeholder="会员账号/手机号码">
            </div>

            <div style="flex-shrink: 0; display: inline-block; padding: 0 10px; font-size: 13px;" onclick="back_list()">
                取消
            </div>

        </div>


        <div style="display: none;" class="search_page">
            <div style="display: flex; background: #fff; padding: 12px; margin-top: 10px; align-items: center;" onclick="search_user()">

                <div style="background: #07C062; width: 43px; height: 43px; text-align: center; line-height: 53px; border-radius: 3px;">
                    <svg t="1696520488433" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="9099" width="22" height="22">
                        <path d="M502.178909 38.632727c-131.072 0-237.521455 104.168727-237.521454 232.727273s106.402909 232.727273 237.521454 232.727273c131.165091 0 237.568-104.168727 237.568-232.727273s-106.309818-232.727273-237.568-232.727273z m0 0c-131.072 0-237.521455 104.168727-237.521454 232.727273s106.402909 232.727273 237.521454 232.727273c131.165091 0 237.568-104.168727 237.568-232.727273s-106.309818-232.727273-237.568-232.727273zM413.184 581.678545c-169.472 0-306.874182 134.609455-306.874182 300.590546v19.316364c0 67.909818 137.402182 67.956364 306.874182 67.956363h197.957818c169.425455 0 306.781091-2.513455 306.781091-67.956363v-19.316364c0-165.981091-137.355636-300.590545-306.781091-300.590546H413.184z m0 0" p-id="9100" fill="#ffffff"></path></svg>
                </div>

                <div style="margin-left: 13px;">
                    搜索：<span style="color: #07C062;" id="search_account">qq</span>
                </div>


            </div>
        </div>


        <div style="background: #fff; padding: 39px 12px; margin-top: 10px; font-size: 13px; text-align: center; color: #5a5b5c; display: none;" class="search_not_result">
            该用户不存在
        </div>
    </div>--%>



    <!--发红包 -->
    <div id="send_redbag" class="base_page" style="display: none;">

        <div style="display: flex; align-items: center; padding: 12px;">


            <svg class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="27857" width="16" height="16" onclick="show_page('#chat_message'); ">
                <path d="M716.8 972.8a51.2 51.2 0 0 1-36.352-14.848l-409.6-409.6a51.2 51.2 0 0 1 0-72.192l409.6-409.6a51.2 51.2 0 0 1 72.192 72.192L379.392 512l373.248 373.248A51.2 51.2 0 0 1 716.8 972.8z" fill="#333C4F" p-id="27858"></path></svg>

            <div style="font-size: 20px; margin-left: 10px;">
                发红包           
           
           
            </div>


        </div>

        <div style="text-align: right; font-size: 12px; color: gray; padding: 0 10px;">
            账户余额：<%=uConfig.gnumber(userdt,"amount") %>
        </div>

        <div style="padding: 10px;">
            <div style="display: flex; background: #fff; padding: 10px; align-items: center;">

                <span style="color: #555; flex-shrink: 0;">金额</span>
                <input style="width: 100%; text-align: right; border: 0; padding: 5px; padding-right: 10px; font-size: 16px;" id="redbag_amount">
                <span style="flex-shrink: 0;">元</span>
            </div>
        </div>


        <div class="redbag_group_item" style="margin-top: 10px; padding: 10px; color: #b77f2b; font-size: 12px; display: flex; align-items: center;">
            <span style="margin-right: 3px;" id="redbag_typename" onclick="toggle_redbagtype()">拼手气红包</span>
            <svg t="1696688656330" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="3971" width="12" height="12" onclick="toggle_redbagtype()">
                <path d="M139.636364 581.818182l721.454545 0.186182L954.181818 674.909091H372.363636l162.909091 186.181818h-139.636363L139.636364 581.818182z m744.727272-116.363637l-721.454545-0.186181L69.818182 372.363636h581.818182l-162.909091-186.181818h139.636363l256 279.272727z" fill="#b77f2b" p-id="3972"></path></svg>

        </div>


        <div class="redbag_group_item" id="redbag_inputnumber" style="margin-top: 10px; padding: 10px;">
            <div style="display: flex; background: #fff; padding: 10px; align-items: center;">

                <span style="color: #555; flex-shrink: 0;">红包个数</span>
                <input style="width: 100%; text-align: right; border: 0; padding: 5px; padding-right: 10px; font-size: 16px;" id="redbag_number">
                <span style="flex-shrink: 0;">个</span>
            </div>
        </div>



        <div class="redbag_group_item" id="redbag_selectuser" style="margin-top: 10px; padding: 10px;" onclick="open_groupUserSelect();">
            <div style="display: flex; background: #fff; padding: 13.5px  10px; align-items: center;">

                <span style="color: #555; flex-shrink: 0;">选择成员</span>
                <div style="width: 100%; text-align: right; padding-right: 10px; font-size: 13px; color: #3c3c87;" id="select_group_user"></div>
                <span style="flex-shrink: 0; padding: 0 10px;" onclick="select_group()">
                    <svg t="1696665535704" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="3720" width="12" height="12">
                        <path d="M312.888889 995.555556c-17.066667 0-28.444444-5.688889-39.822222-17.066667-22.755556-22.755556-17.066667-56.888889 5.688889-79.644445l364.088888-329.955555c11.377778-11.377778 17.066667-22.755556 17.066667-34.133333 0-11.377778-5.688889-22.755556-17.066667-34.133334L273.066667 187.733333c-22.755556-22.755556-28.444444-56.888889-5.688889-79.644444 22.755556-22.755556 56.888889-28.444444 79.644444-5.688889l364.088889 312.888889c34.133333 28.444444 56.888889 73.955556 56.888889 119.466667s-17.066667 85.333333-51.2 119.466666l-364.088889 329.955556c-11.377778 5.688889-28.444444 11.377778-39.822222 11.377778z" fill="#2c2c2c" p-id="3721"></path></svg></span>
            </div>
        </div>

        <div style="margin-top: 10px; padding: 10px;">

            <textarea style="background: #fff; padding: 10px; width: 100%; box-sizing: border-box; border: 0; resize: none; height: 88px; font-size: 18px;" placeholder="恭喜发财，大吉大利" id="redbag_text"></textarea>

        </div>


        <div style="color: #000; font-size: 32px; font-weight: bold; text-align: center; margin: 20px 0;" id="total_amount_text">
            ￥ 1.00
        </div>


        <div style="text-align: center;">
            <a style="display: inline-block; cursor: pointer; background: gray; color: #fff; width: 196px; text-align: center; border-radius: 4px; padding: 12px; box-sizing: border-box;" onclick="send_bag()" id="send_redbag_button">塞钱进红包</a>
        </div>

        <script>
            ////测试数据
            //chatData.id = 'gmqun';

            var open_groupUserSelect = function () {
                //chatData.select_user = [];
                //$('#select_tip').html('请选择用户');

                show_page('#select_groupUsers');

                if (!show_groupList(chatData.id)) {
                    v3api("group_userlist", {
                        data: {
                            chatid: chatData.id,
                        }
                    }, function (e) {
                        console.log('group_userlist', e);
                        chatData['gp_list_' + chatData.id] = e.list;
                        show_groupList(chatData.id);
                    })
                }
            }

            var show_groupList = function (chatid, filter) {
                var list = chatData['gp_list_' + chatid];
                if (!list) {
                    return false;
                }
                $('#group_user_list').html('');
                for (var i = 0; i < list.length; i++) {
                    var object = list[i];
                    var is_exists = chatData.select_user.indexOf(object.parent_code) !== -1;

                    if (filter && object.parent_code.indexOf(filter) == -1) {
                        continue;
                    }



                    $('#group_user_list').append('<div class="group_user_item ' + (is_exists ? 'active' : '') + '" onclick="select_user(\'' + object.parent_code + '\')" uid="' + object.parent_code + '">                <div style="text-align: center; border-radius: 3px;">                    <img src="static/images/avatar-1.jpg" width="38" height="38">                </div>                <div style="margin-left: 13px;display: flex;flex-wrap: wrap;align-items: center;">                    ' + object.name + '                               </div>                <div style="margin-left: auto;">                    <svg t="1696676296018" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="3026" width="16" height="16">                        <path d="M512.056889 942.828575a430.828575 430.828575 0 0 0 0-861.543384 430.828575 430.828575 0 0 0 0 861.543384z m0 81.171425a512 512 0 1 1 0-1024 512 512 0 0 1 0 1024z" fill="#e6e6e6" p-id="3027"></path><path d="M726.845468 408.13243a45.562937 45.562937 0 0 0-58.02022 1.194534L467.631603 593.455838 357.051889 477.358516c-14.505055-15.073881-40.500389-17.178536-57.849572-4.664371-17.292301 12.62793-19.738251 35.267193-5.346961 50.397956l139.931118 146.757027a43.287635 43.287635 0 0 0 30.54594 12.798578h3.185424a43.799578 43.799578 0 0 0 27.872458-11.035218L728.153767 458.644151a32.536829 32.536829 0 0 0-1.365182-50.511721z" fill="#e6e6e6" p-id="3028"></path></svg>                </div>            </div>');
                }
                return true;
            }

            var toggle_redbagtype = function () {
                $('#redbag_inputnumber').toggle();
                $('#redbag_selectuser').toggle();

                if ($('#redbag_selectuser').css('display') == "block") {
                    $('#redbag_typename').html('专属红包');
                } else {
                    $('#redbag_typename').html('拼手气红包');
                }
            }
        </script>
    </div>



    <!--红包-搜索成员 -->
    <div id="select_groupUsers" class="base_page" style="display: none;">

        <div style="display: flex; align-items: center; padding: 20px 12px">

            <svg class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="27857" width="16" height="16" onclick="show_page('#send_redbag');">
                <path d="M716.8 972.8a51.2 51.2 0 0 1-36.352-14.848l-409.6-409.6a51.2 51.2 0 0 1 0-72.192l409.6-409.6a51.2 51.2 0 0 1 72.192 72.192L379.392 512l373.248 373.248A51.2 51.2 0 0 1 716.8 972.8z" fill="#333C4F" p-id="27858"></path></svg>

            <div style="font-size: 20px; margin-left: 10px;">
                选择成员
            </div>


        </div>

        <div style="display: flex; align-items: center; padding: 2px 10px;">

            <div style="position: relative; width: 100%;">

                <div style="position: absolute; top: 9px; left: 10px;">
                    <svg t="1696520315313" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2610" width="16" height="16">
                        <path d="M680.728354 651.326209c121.274064-137.208988 116.320239-346.908988-14.939247-478.166427-136.444579-136.433322-357.662913-136.433322-494.094188 0-136.443555 136.442532-136.443555 357.661889 0 494.105445 131.241067 131.253346 340.927763 136.204102 478.149031 14.942317l265.63187 265.63187 30.889521-30.877241L680.728354 651.326209zM649.273968 622.002346l-28.658713 28.668946c-120.345925 105.622596-303.678394 101.031021-418.524049-13.812587-119.651101-119.651101-119.651101-313.648466 0-433.299567C321.742307 83.909062 515.740696 83.909062 635.39282 203.569372 750.211868 318.380234 754.826979 501.656421 649.273968 622.002346z" fill="#2c2c2c" p-id="2611"></path></svg>

                </div>

                <input id="group_user_search" style="border: 0; background: #fff; width: 100%; padding: 9px; box-sizing: border-box; padding-left: 32px;" placeholder="搜索">
            </div>

        </div>

        <style>
            .group_user_item {
                display: flex;
                background: #fff;
                padding: 12px;
                align-items: center;
            }

                .group_user_item svg {
                    color: #aaa;
                }

                .group_user_item.active svg {
                    color: green;
                }

                .group_user_item svg path {
                    fill: currentColor;
                }
        </style>




        <div style="padding: 10px; font-size: 12px; color: gray;" id="select_tip">
            请选择用户
        </div>


        <div id="group_user_list" class="flex_grow" style="background: #fff;">
        </div>

        <div style="background: #eee; display: flex; padding: 14px;">
            <a style="background: #49C265; color: #fff; margin-left: auto; padding: 5px 18px; border-radius: 5px;" onclick="confirm_select_group_user()" id="confirm_select_group_user">完成</a>
        </div>

        <script>
            chatData.select_user = [];
            var select_user = function (uid) {
                $('[uid="' + uid + '"]').toggleClass("active");

                if ($('[uid="' + uid + '"]').hasClass("active")) {
                    chatData.select_user.push(uid);
                } else {
                    chatData.select_user = chatData.select_user.filter(function (item) {
                        return item !== uid;
                    });
                }
                //已选择65***88、99***00等10位用户
                var tip_text = "请选择用户";
                if (chatData.select_user.length > 0) {
                    var firstFive = chatData.select_user.slice(0, 2);
                    for (var i = 0; i < firstFive.length; i++) {
                        firstFive[i] = firstFive[i].slice(0, 2) + "***" + firstFive[i].slice(-2);
                    }
                    tip_text = '已选择' + firstFive.join('、') + '等' + chatData.select_user.length + '位用户';

                    chatData.select_user_tip = '已选择' + firstFive.join('、') + '等' + chatData.select_user.length + '位';

                    $('#confirm_select_group_user').html('完成 (' + chatData.select_user.length + ')');
                    $('#confirm_select_group_user').css('background', '#49C265');
                    $('#confirm_select_group_user').css('color', '#ffffff');
                } else {

                    $('#confirm_select_group_user').html('完成');
                    $('#confirm_select_group_user').css('background', '#E9E9E9');
                    $('#confirm_select_group_user').css('color', '#BDBDBD');
                }


                $('#select_tip').html(tip_text)


            }

            var confirm_select_group_user = function () {
                if (chatData.select_user.length > 0) {
                    show_page('#send_redbag');
                    $('#select_group_user').html(chatData.select_user_tip)
                    $('#select_group_user').data('users', chatData.select_user.join(','))

                    $('#redbag_number').val(chatData.select_user.length);
                }
            }

            $('#group_user_search').on('keyup', function () {
                show_groupList(chatData.id, $(this).val())
            })
        </script>

    </div>


    <!--通讯录 -->
    <div id="friend_book" class="base_page" style="padding-bottom: 61px; display: none;">

        <div style="display: flex; padding: 16px 10px;">

            <div style="font-size: 18px;">通讯录</div>


        </div>


        <div style="display: ;" class="search_page">
            <div style="display: flex; background: #ffffff70; padding: 12px; align-items: center;" onclick="show_page('#friend_list');get_user('我的代理');">

                <div style="background: #2892B5; width: 43px; height: 43px; text-align: center; line-height: 53px; border-radius: 3px;">
                    <svg t="1696559870801" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="41083" width="22" height="22">
                        <path d="M796.113455 345.367273l-67.304728 67.304727A42.682182 42.682182 0 1 1 668.392727 352.349091l136.657455-136.657455a42.682182 42.682182 0 0 1 23.877818-15.36 42.635636 42.635636 0 0 1 52.503273 48.686546v533.271273a42.682182 42.682182 0 0 1-85.364364 0V345.367273zM184.552727 253.998545h369.803637a42.682182 42.682182 0 0 1 0 85.364364H184.552727a42.682182 42.682182 0 0 1 0-85.364364z m0 227.607273h369.803637a42.682182 42.682182 0 1 1 0 85.317818H184.552727a42.682182 42.682182 0 1 1 0-85.364363z m0 227.514182h369.803637a42.682182 42.682182 0 1 1 0 85.364364H184.552727a42.682182 42.682182 0 0 1 0-85.364364z" fill="#ffffff" p-id="41084"></path></svg>
                </div>

                <div style="margin-left: 13px;">
                    我的代理
               
                </div>

            </div>

            <div style="display: flex; background: #ffffff70; padding: 12px; align-items: center;" onclick="show_page('#friend_list');get_user('我的下级');">

                <div style="background: #2196F3; width: 43px; height: 43px; text-align: center; line-height: 53px; border-radius: 3px;">
                    <svg t="1696559785110" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="36599" width="22" height="22">
                        <path d="M1024 797v131c0 53-43 96-96 96h-96c-53 0-96-43-96-96V797c0-26.5 10.7-50.5 28.1-67.9 17.4-17.4 41.4-28.1 67.9-28.1 8.8 0 16-7.2 16-16v-77c0-17.7-14.3-32-32-32H560c-8.8 0-16 7.2-16 16v94c0 8.3 6.7 15 15 15h1c53 0 96 43 96 96v131c0 53-43 96-96 96h-96c-53 0-96-43-96-96V797c0-53 43-96 96-96h1c8.3 0 15-6.7 15-15v-94c0-8.8-7.2-16-16-16H208c-17.7 0-32 14.3-32 32v77c0 4.4 1.8 8.4 4.7 11.3 2.9 2.9 6.9 4.7 11.3 4.7 53 0 96 43 96 96v131c0 53-43 96-96 96H96c-53 0-96-43-96-96V797c0-26.5 10.7-50.5 28.1-67.9C45.5 711.7 69.5 701 96 701c8.8 0 16-7.2 16-16v-77c0-53 43-96 96-96h256c8.8 0 16-7.2 16-16v-96c0-8.8-7.2-16-16-16h-48c-53 0-96-43-96-96V96c0-53 43-96 96-96h192c53 0 96 43 96 96v192c0 53-43 96-96 96h-48c-8.8 0-16 7.2-16 16v96c0 8.8 7.2 16 16 16h256c53 0 96 43 96 96v77c0 8.8 7.2 16 16 16 53 0 96 43 96 96z" p-id="36600" fill="#ffffff"></path></svg>
                </div>

                <div style="margin-left: 13px;">
                    我的下级
               
                </div>

            </div>
        </div>

    </div>




    <!--游戏大厅 -->
    <div id="game_dating" class="base_page" style="padding-bottom: 61px; display: none;">

        <div style="display: flex; padding: 16px 10px;">

            <div style="font-size: 18px; display: flex;">
                <svg t="" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="1653" width="22" height="22" style="margin-right: 5px;">
                    <path d="M679.408 689.376c-24.032-28.336-89.52-94.8-175.104-94.8-67.008 0-115.28 38.064-142.48 67.312a581.184 581.184 0 0 1-24.976 31.824l-0.752 1.216-1.76 1.744c-46.96 54.784-100.112 93.6-145.152 86.48-103.04-16.32-92.4-222.112-48.8-358.8s99.296-179.84 184.496-166.352c4.768 0.752 9.456 1.76 14.064 2.992 68.992 10.224 86.304 27.712 165.36 27.712 77.488 0 150.832-22.48 176.88-28.048 4.128-1.088 8.336-1.984 12.624-2.656 85.216-13.488 140.896 29.648 184.496 166.352 43.6 136.704 54.24 342.48-48.8 358.8-43.072 6.816-92.608-27.808-136.896-78.112-1.072-1.024-2.16-2.048-3.232-3.104 0 0-1.632-2.288-4.752-6.208a525.808 525.808 0 0 1-5.216-6.352zM297.52 513.312a78.64 78.64 0 1 0 0-157.28 78.64 78.64 0 0 0 0 157.28z" fill="#95EAFF" p-id="1654"></path><path d="M314.112 170.912h-68.816c-36.208 0-67.632 20.288-67.632 49.376v44.496h32v-44.496c0-7.744 14.928-17.376 35.632-17.376h68.816c20.72 0 35.632 9.648 35.632 17.376v44.496h32v-44.496c0-29.056-31.392-49.376-67.632-49.376zM674.272 220.24c0-7.76 14.928-17.408 35.632-17.408h68.816c20.72 0 35.632 9.648 35.632 17.408v44.528h32v-44.528c0-29.072-31.392-49.408-67.632-49.408h-68.816c-36.208 0-67.632 20.304-67.632 49.408v44.528h32v-44.528z" fill="#49A3FE" p-id="1655"></path><path d="M752 384m-32 0a32 32 0 1 0 64 0 32 32 0 1 0-64 0Z" fill="#49A3FE" p-id="1656"></path><path d="M688 448m-32 0a32 32 0 1 0 64 0 32 32 0 1 0-64 0Z" fill="#49A3FE" p-id="1657"></path><path d="M816 448m-32 0a32 32 0 1 0 64 0 32 32 0 1 0-64 0Z" fill="#49A3FE" p-id="1658"></path><path d="M752 512m-32 0a32 32 0 1 0 64 0 32 32 0 1 0-64 0Z" fill="#49A3FE" p-id="1659"></path><path d="M589.824 544h-52.768a16 16 0 0 0 0 32h52.768a16 16 0 0 0 0-32zM468.928 544H416a16 16 0 0 0 0 32h52.928a16 16 0 0 0 0-32z" fill="#49A3FE" p-id="1660"></path><path d="M937.2 423.568c-49.6-155.488-116.8-214.112-224.224-197.104-5.312 0.848-10.544 1.936-15.712 3.296-2.864 0.592-45.072 11.072-62.304 14.848-46.848 10.24-89.888 16.016-131.568 16.016-40.24 0-65.04-3.536-99.536-12.416-3.088-0.8-20.128-5.296-25.312-6.608a549.52 549.52 0 0 0-57.696-11.776 170.336 170.336 0 0 0-15.76-3.36c-107.408-17.008-174.624 41.6-224.224 197.104C17.952 620.832 31.92 827.344 148.24 845.76c53.808 8.528 115.744-30.608 177.088-102.16a18.24 18.24 0 0 0 3.584-4.208l-0.032-0.016a666.384 666.384 0 0 0 27.168-34.608c39.824-42.64 89.184-69.824 147.344-69.824 71.088 0 134.8 42.704 183.76 100.432 1.76 2.176 3.76 4.608 5.776 7.008 2.192 2.752 3.808 4.912 4.544 5.952l1.904 2.208 3.76 3.616c56.752 64.56 114.992 99.792 166.704 91.6 116.32-18.416 130.288-224.928 67.36-422.192z m-72.352 390.592c-37.856 6-87.488-24.016-138.688-82.192a356.768 356.768 0 0 0-3.568-3.488 194.144 194.144 0 0 0-4.88-6.336c-2.128-2.56-4.032-4.848-5.92-7.184-54.688-64.48-125.584-112.016-208.4-112.016-68.528 0-125.76 31.616-171.168 80.432a717.28 717.28 0 0 1-28.464 36.176l-1.168 1.584-0.624 0.608c-55.696 64.88-109.168 98.672-148.736 92.4-86.544-13.712-99.2-201.12-41.872-380.864 45.312-142.048 99.536-189.344 188.72-175.216 4.752 0.752 9.456 1.76 14.08 3.008 21.12 3.232 37.232 6.624 56.464 11.536 5.008 1.28 22 5.76 25.232 6.592 36.96 9.52 64.384 13.44 107.52 13.44 44.32 0 89.504-6.08 138.4-16.768 17.84-3.904 59.968-14.352 62.896-14.992 4.848-1.264 9.04-2.144 13.28-2.816 89.184-14.128 143.424 33.168 188.72 175.216 57.376 179.76 44.704 367.168-41.824 380.88z" fill="#0F13AE" p-id="1661"></path><path d="M863.616 728.88a44.16 44.16 0 0 1-15.648 5.536 50.08 50.08 0 0 1-20.976-1.488c-23.376-6.24-50.88-26.848-78.704-58.448-1.248-1.248-2-1.968-2.64-2.592a146.368 146.368 0 0 0-3.536-4.576l-4.432-5.376a16 16 0 1 0-24.848 20.144c1.504 1.856 3.008 3.68 4.512 5.472 1.52 1.92 2.688 3.456 3.2 4.176l1.904 2.208c0.944 0.928 1.904 1.824 2.848 2.72 30.592 34.864 62.816 58.976 93.456 67.168a82.128 82.128 0 0 0 34.24 2.176 75.904 75.904 0 0 0 26.896-9.568 16 16 0 1 0-16.272-27.552zM282.672 666.064c-0.432 0.576-0.32 0.448-0.448 0.576a351.184 351.184 0 0 1-33.504 34.368c-28.416 24.992-54.112 36.528-73.872 33.392a43.632 43.632 0 0 1-22.416-10.432 16 16 0 0 0-21.056 24.096 75.648 75.648 0 0 0 38.448 17.936c31.168 4.944 65.152-10.304 100.016-40.976a364.992 364.992 0 0 0 35.712-36.544 23.84 23.84 0 0 0 2.784-3.328 16 16 0 1 0-25.664-19.088z" fill="#0F13AE" p-id="1662"></path><path d="M689.28 641.232m-17.232 0a17.232 17.232 0 1 0 34.464 0 17.232 17.232 0 1 0-34.464 0Z" fill="#0F13AE" p-id="1663"></path><path d="M113.28 689.232m-17.232 0a17.232 17.232 0 1 0 34.464 0 17.232 17.232 0 1 0-34.464 0Z" fill="#0F13AE" p-id="1664"></path><path d="M294.4 419.2V400a16 16 0 0 0-32 0v19.2H240a16 16 0 0 0 0 32h22.4V480a16 16 0 0 0 32 0v-28.8H320a16 16 0 0 0 0-32h-25.6z" fill="#0F13AE" p-id="1665"></path><path d="M280 336a104 104 0 1 0 0 208 104 104 0 0 0 0-208z m0 176a72 72 0 1 1 0-144 72 72 0 0 1 0 144z" fill="#0F13AE" p-id="1666"></path></svg>热门游戏
            </div>


        </div>




        <div id="game-list" class="flex_grow">
        </div>



    </div>


    <!--好友列表 -->
    <%--<div id="friend_list" class="base_page" style="display: none;">

        <div style="display: flex; align-items: center; padding: 20px 12px">


            <svg class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="27857" width="16" height="16" onclick="show_page('#friend_book');$('#index_menu').show();">
                <path d="M716.8 972.8a51.2 51.2 0 0 1-36.352-14.848l-409.6-409.6a51.2 51.2 0 0 1 0-72.192l409.6-409.6a51.2 51.2 0 0 1 72.192 72.192L379.392 512l373.248 373.248A51.2 51.2 0 0 1 716.8 972.8z" fill="#333C4F" p-id="27858"></path></svg>

            <div style="font-size: 20px; margin-left: 10px;" id="friend_class_name">
                我的下级
            </div>


        </div>

        <div style="display: flex; align-items: center; padding: 2px 10px;">

            <div style="position: relative; width: 100%;">

                <div style="position: absolute; top: 9px; left: 10px;">
                    <svg t="1696520315313" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2610" width="16" height="16">
                        <path d="M680.728354 651.326209c121.274064-137.208988 116.320239-346.908988-14.939247-478.166427-136.444579-136.433322-357.662913-136.433322-494.094188 0-136.443555 136.442532-136.443555 357.661889 0 494.105445 131.241067 131.253346 340.927763 136.204102 478.149031 14.942317l265.63187 265.63187 30.889521-30.877241L680.728354 651.326209zM649.273968 622.002346l-28.658713 28.668946c-120.345925 105.622596-303.678394 101.031021-418.524049-13.812587-119.651101-119.651101-119.651101-313.648466 0-433.299567C321.742307 83.909062 515.740696 83.909062 635.39282 203.569372 750.211868 318.380234 754.826979 501.656421 649.273968 622.002346z" fill="#2c2c2c" p-id="2611"></path></svg>

                </div>

                <input id="search_friend" style="border: 0; background: #fff; width: 100%; padding: 9px; box-sizing: border-box; padding-left: 32px;" placeholder="搜索">
            </div>

        </div>

        <div id="user_list" class="flex_grow" style="padding-top: 20px;">

            <div style="display: flex; background: #fff; padding: 12px; align-items: center;" onclick="get_chat('@friend_2');">

                <div style="text-align: center; border-radius: 3px;">
                    <img src="static/images/avatar-1.jpg" width="38" height="38">
                </div>

                <div style="margin-left: 13px;">
                    我的代理
                </div>

            </div>


        </div>


    </div>--%>

    <div id="friend_list" class="base_page" style="padding-bottom: 61px; display: none;">

        <div style="display: flex; padding: 16px 10px;">

            <div style="font-size: 20px; margin-left: 10px;" id="friend_class_name">
                好友列表
            </div>
        </div>


        <div style="display: flex; align-items: center; padding: 8px; padding-top: 0;">

            <div style="position: relative; width: 100%;">

                <div style="position: absolute; top: 9px; left: 10px;">
                    <svg t="1696520315313" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2610" width="16" height="16">
                        <path d="M680.728354 651.326209c121.274064-137.208988 116.320239-346.908988-14.939247-478.166427-136.444579-136.433322-357.662913-136.433322-494.094188 0-136.443555 136.442532-136.443555 357.661889 0 494.105445 131.241067 131.253346 340.927763 136.204102 478.149031 14.942317l265.63187 265.63187 30.889521-30.877241L680.728354 651.326209zM649.273968 622.002346l-28.658713 28.668946c-120.345925 105.622596-303.678394 101.031021-418.524049-13.812587-119.651101-119.651101-119.651101-313.648466 0-433.299567C321.742307 83.909062 515.740696 83.909062 635.39282 203.569372 750.211868 318.380234 754.826979 501.656421 649.273968 622.002346z" fill="#2c2c2c" p-id="2611"></path></svg>

                </div>

                <input class="search_content" sid="friend" style="border: 0; background: #fff; width: 100%; padding: 9px; box-sizing: border-box; padding-left: 32px;" placeholder="搜索">
            </div>


            <div class="cancel_search_button" sid="friend" style="flex-shrink: 0; display: none; padding: 0 10px; font-size: 13px;">
                取消
            </div>



        </div>


        <div id="user_list" class="flex_grow" style="padding-top: 0px;">
        </div>


    </div>


    <%--新的好友--%>
    <div id="newfriend_detail" class="base_page" style="padding-bottom: 61px; display: none;">

        <div style="display: flex; padding: 16px 10px; align-items: center;">

            <svg class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="27857" width="16" height="16" onclick="javascript:$('#newfriend_detail').hide();$('#index_menu').show();">
                <path d="M716.8 972.8a51.2 51.2 0 0 1-36.352-14.848l-409.6-409.6a51.2 51.2 0 0 1 0-72.192l409.6-409.6a51.2 51.2 0 0 1 72.192 72.192L379.392 512l373.248 373.248A51.2 51.2 0 0 1 716.8 972.8z" fill="#333C4F" p-id="27858"></path></svg>
            <div style="font-size: 20px; margin-left: 10px;" id="Div3">
                新的好友
           
            </div>
        </div>





        <div id="newfriend_list" class="flex_grow" style="padding-top: 0px;">
        </div>


    </div>


    
    <%--群发消息--%>

    <style>
        .wxbtn {
            background: #E1E1E1;
            color: #b9b4b4;
            font-size: 13px;
            padding: 5px 15px;
            border-radius: 5px;
            display: inline-block;
        }

            .wxbtn.active {
                background: #07BE62;
                color: #fff;
            }
    </style>
    <script>
        var allsend_list = [];
        var pop_allsend = function () {
            show_page('#allsend_message');
            get_user('好友列表');
            $('#index_menu').show();
        }
        var to_allsend = function (obj) {
            if (!$(obj).hasClass('active')) {
                return;
            }
            $('.allsend_num').html('');
            $('#allsend_user').html('');

            allsend_list = [];
            $('.q_sel').each(function () {
                var item = $(this).closest('div[user_uuid]');
                var __img = item.find('img').attr('src');
                var uuid = item.attr('user_uuid');
                allsend_list.push({
                    uuid: uuid,
                    img: __img
                })

                $('#allsend_user').append('<img src="' + __img + '" style="border-radius: 3px;margin:0 5px;" width="38" height="38">');
            })
            $('.allsend_num').html(allsend_list.length);
            console.log('allsend_list', allsend_list);


            $('#contact_username').html('群发助手');
            chatData.type = 'allsend';
            $('#chat_message').show();
            $('#chat_box').hide();
            $('#allsend_box').show();
            $('#nospeak').hide();
            $('#message_sendbox').show();


        }
        var cancel_allsend = function () {
            show_page('#index_message_data');
            $('#index_menu').show();
        }
        var selclick = function (obj) {

            var _selIcon = $(obj).closest('div[user_uuid]').find('.q_button');
            console.log('q_unsel', _selIcon.attr('class'), _selIcon.hasClass("q_unsel"));

            if (_selIcon.attr('class').indexOf('q_unsel') != -1) {
                console.log('sel button');
                _selIcon.replaceWith(selbtn);
            } else {
                console.log('unsel button');
                _selIcon.replaceWith(unselbtn);
            }

            var _total = $('.q_sel').length;
            if (_total > 0) {
                $('#allsend_button').addClass('active');
                $('#allsend_button').html('完成 (' + _total + ')');
            } else {
                $('#allsend_button').removeClass('active');
                $('#allsend_button').html('完成');
            }
        }


        //$(function () {
        //    setTimeout(function () {
        //        pop_allsend();
        //    }, 100)
        //})

    </script>
    <div id="allsend_message" class="base_page" style="padding-bottom: 61px; display: none;">




        <div style="display: flex; justify-content: space-between; align-items: center; padding: 16px 10px;">
            <div style="flex: 1;" onclick="cancel_allsend()">取消</div>
            <div style="flex: 1; text-align: center; font-size: 16px; font-weight: bold;">选择好友</div>
            <div style="flex: 1; text-align: right;">

                <span class="wxbtn" onclick="to_allsend(this)" onclick="to_allsend()" id="allsend_button">完成</span>
            </div>
        </div>


        <div style="display: flex; align-items: center; padding: 8px; padding-top: 0;">

            <div style="position: relative; width: 100%;">

                <div style="position: absolute; top: 9px; left: 10px;">
                    <svg t="1696520315313" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2610" width="16" height="16">
                        <path d="M680.728354 651.326209c121.274064-137.208988 116.320239-346.908988-14.939247-478.166427-136.444579-136.433322-357.662913-136.433322-494.094188 0-136.443555 136.442532-136.443555 357.661889 0 494.105445 131.241067 131.253346 340.927763 136.204102 478.149031 14.942317l265.63187 265.63187 30.889521-30.877241L680.728354 651.326209zM649.273968 622.002346l-28.658713 28.668946c-120.345925 105.622596-303.678394 101.031021-418.524049-13.812587-119.651101-119.651101-119.651101-313.648466 0-433.299567C321.742307 83.909062 515.740696 83.909062 635.39282 203.569372 750.211868 318.380234 754.826979 501.656421 649.273968 622.002346z" fill="#2c2c2c" p-id="2611"></path></svg>

                </div>

                <input class="search_content" sid="allsend" style="border: 0; background: #fff; width: 100%; padding: 9px; box-sizing: border-box; padding-left: 32px;" placeholder="搜索">
            </div>


            <div class="cancel_search_button" sid="allsend" style="flex-shrink: 0; display: none; padding: 0 10px; font-size: 13px;">
                取消
            </div>



        </div>


        <div id="msg_friends" class="flex_grow" style="padding-top: 0px;">
        </div>


    </div>



    <div id="user_card" class="base_page" style="display: none;">

        <div style="display: flex; align-items: center; padding: 20px 12px">


            <svg class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="27857" width="16" height="16" onclick="$('#user_card').hide();">
                <path d="M716.8 972.8a51.2 51.2 0 0 1-36.352-14.848l-409.6-409.6a51.2 51.2 0 0 1 0-72.192l409.6-409.6a51.2 51.2 0 0 1 72.192 72.192L379.392 512l373.248 373.248A51.2 51.2 0 0 1 716.8 972.8z" fill="#333C4F" p-id="27858"></path></svg>
            
            <div style="font-size: 20px; margin-left: 10px;">
                用户信息
           
            </div>


        </div>

        <div style="padding: 18px;">
            <div style="display: flex;">

                <div>
                    <img id="card_avatar" src="../images/avatar/user12.png" style="width: 60px; height: 60px;">
                </div>

                <div style="margin-left: 8px;">
                    <div style="margin: 0; font-size: 19px; font-weight: bold; color: #2a2b2c;" id="card_username">
                        -
                    </div>
                    <div style="font-size: 13px; color: gray; margin-top: 4px;">备注：<span id="card_remark">-</span></div>
                </div>

            </div>

            <div style="margin-top: 18px;">

                <div>
                    <div style="font-size: 15px; color: #5a5b5c; padding-left: 8px;">备注</div>
                    <div style="margin-top: 8px;">
                        <input style="background: #e7e7e7; border: 0; width: 100%; padding: 12px; box-sizing: border-box; border-radius: 6px; font-size: 15px;" placeholder="请输入备注名称" id="user_remark">
                    </div>
                </div>

            </div>


            <div style="margin-top: 30px; display: flex; justify-content: center;">

                <a style="background: #dbdbdb; color: #2a2b2c; padding: 10px 10px; border-radius: 3px; width: 80px; text-align: center; margin-right: 18px; cursor: pointer; display: flex; align-items: center; justify-content: center;" onclick="user_ichat()">
                    <svg t="" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="" width="22" height="22">
                        <path d="M631.17 935.09H178.5c-50.28 0-91.18-40.9-91.18-91.18V391.24c0-81.6 31.78-158.32 89.48-216.02s134.42-89.48 216.02-89.48h238.34c76.76 0 150.14 28.58 206.63 80.48 12.68 11.65 13.52 31.38 1.87 44.06-11.65 12.68-31.38 13.52-44.06 1.87-44.95-41.3-103.35-64.04-164.44-64.04H392.83c-64.95 0-126 25.29-171.93 71.21-45.92 45.92-71.21 106.98-71.21 171.93v452.67c0 15.89 12.93 28.82 28.82 28.82h452.67c64.95 0 126-25.29 171.93-71.21 45.92-45.92 71.22-106.98 71.22-171.93V391.24c0-22.22-2.99-44.23-8.89-65.41-4.62-16.59 5.08-33.78 21.67-38.4 16.59-4.62 33.78 5.08 38.4 21.67 7.42 26.63 11.18 54.27 11.18 82.14v238.34c0 81.6-31.78 158.32-89.48 216.02-57.72 57.71-134.43 89.49-216.04 89.49z" fill="#3E3A39" p-id="4927"></path><path d="M712.44 400.24H309.55c-17.22 0-31.18-13.96-31.18-31.18s13.96-31.18 31.18-31.18h402.89c17.22 0 31.18 13.96 31.18 31.18s-13.96 31.18-31.18 31.18zM581.93 543.18H309.55c-17.22 0-31.18-13.96-31.18-31.18s13.96-31.18 31.18-31.18h272.38c17.22 0 31.18 13.96 31.18 31.18s-13.96 31.18-31.18 31.18z" fill="#3E3A39" p-id="4928"></path></svg><span style="margin-left: 5px">聊天</span></a>

                <a style="display: inline-block; background: #11b511; color: #fff; padding: 10px 10px; border-radius: 3px; width: 100px; text-align: center; margin-right: 18px; cursor: pointer;" onclick="usercard_submit()">确认修改</a>
                <a style="display: inline-block; background: #db2929; color: #fff; padding: 10px 10px; border-radius: 3px; width: 100px; text-align: center; cursor: pointer;" id="user_speak_button" onclick="user_nospeak()">用户禁言</a>
            </div>


        </div>


    </div>






    <!--红包领取结果 -->
    <div id="redbag_detail" class="base_page" style="display: none; position: fixed; left: 0; width: 100%;">

        <style>
            .openredpackets-overlay {
                overflow: visible;
                background-color: transparent;
            }

            .openredpackets-overlay-pop {
                background-color: hsla(0,0%,100%,.7);
            }

            .openredpackets-main {
                overflow: hidden;
                position: relative;
                height: 380px;
                width: 260px;
                border-radius: 12px;
                background-color: #f45540;
            }

                .openredpackets-main:after {
                    content: "";
                    position: absolute;
                    top: -400px;
                    left: 50%;
                    margin-left: -350px;
                    width: 700px;
                    height: 700px;
                    border-radius: 50%;
                    background-color: #f36050;
                    box-shadow: 0 3px 1px -2px rgba(0,0,0,.2),0 2px 2px 0 rgba(0,0,0,.14),0 1px 5px 0 rgba(0,0,0,.12);
                    transition: all .6s;
                }

            .openredpackets-content {
                z-index: 10;
                position: relative;
                margin-top: 80px;
            }

            .openredpackets-content-normal {
                box-sizing: border-box;
                padding: 0 16px;
                max-width: 100%;
                color: #ebcd99;
            }

            .openredpackets-content-title {
                margin-bottom: 10px;
            }

            .openredpackets-content-headicon {
                margin-right: 10px;
            }

            .openredpackets-content-intro {
                overflow: auto;
                font-size: 20px;
                max-height: 160px;
            }

                .openredpackets-content-intro::-webkit-scrollbar {
                    height: 10px;
                    width: 5px;
                    background: #f36050;
                }

                .openredpackets-content-intro::-webkit-scrollbar-thumb {
                    background: #ebcd99;
                    border-radius: 6px;
                }

            .openredpackets-main-btn {
                z-index: 10;
                position: absolute;
                top: 262px;
                left: 50%;
                margin-left: -36px;
                width: 72px;
                height: 72px;
                font-size: 28px;
                font-weight: 700;
                font-family: SimSun;
                border-radius: 50%;
                background-color: #ebcd99;
                box-shadow: 0 3px 1px -2px rgba(0,0,0,.2),0 2px 2px 0 rgba(0,0,0,.14),0 1px 5px 0 rgba(0,0,0,.12);
            }

            .openredpackets-main-btn-loading {
                position: absolute;
            }

            .openredpackets-main-bot {
                position: absolute;
                left: 0;
                right: 0;
                bottom: 0;
                padding: 10px;
                font-size: 12px;
                color: #ebcd99;
            }

            .openredpackets-main-btn-loading {
                -webkit-animation: flip-vertical-right .8s both;
                animation: flip-vertical-right .8s both;
            }

            .openredpackets-main-noredpackets-tips {
                z-index: 10;
                position: absolute;
                top: 250px;
                left: 0;
                right: 0;
                text-align: center;
                color: #ebcd99;
            }

            .openredpackets-main-open {
                width: 100vw;
                height: 100vh;
                background-color: #fff;
                border-radius: 0;
                transition: all .6s;
            }

                .openredpackets-main-open .openredpackets-content-normal, .openredpackets-main-open .openredpackets-main-bot {
                    transform: scale(0);
                    transition: all .6s;
                }

                .openredpackets-main-open .openredpackets-main-btn {
                    top: calc(-400vw + 80px);
                    margin-left: -200vw;
                    width: 400vw;
                    height: 400vw;
                    background-color: #f36050;
                    transition: all .6s;
                    -webkit-animation: flip-scale-up-diag-1 .6s cubic-bezier(.39,.575,.565,1) both;
                    animation: flip-scale-up-diag-1 .6s cubic-bezier(.39,.575,.565,1) both;
                }

                .openredpackets-main-open .openredpackets-main-btn-text {
                    transform: scale(0);
                    transition: all .6s;
                }

                .openredpackets-main-open:after {
                    opacity: 0;
                    transform: translateY(-300px);
                }

            .redpacketsinfo-overlay-pop {
                background: transparent;
            }

            .redpacketsinfo-overlay {
                width: 100%;
                height: 100%;
                overflow: visible;
            }

                .redpacketsinfo-overlay:after {
                    content: "";
                    box-sizing: border-box;
                    left: 50%;
                    top: calc(-400vw + 90px);
                    margin-left: -200vw;
                    width: 400vw;
                    height: 400vw;
                    border-radius: 50%;
                    border: 2px solid #ebcd99;
                    z-index: -1;
                }

                .redpacketsinfo-header, .redpacketsinfo-overlay:after {
                    position: absolute;
                    background-color: #f36050;
                }

                    .redpacketsinfo-header .app-bar-back-btn-icon {
                        color: #fff;
                    }

            .redpacketsinfo-scroll {
                box-sizing: border-box;
                height: 100%;
                /*overflow-y: auto;*/
                padding-top: 80px;
                top: 0;
                left: 0;
                position: fixed;
                width: 100%;
                display: flex;
                flex-direction: column;
            }

            .redpacketsinfo-info-box {
                padding: 10px 0 10px 0;
            }

            .redpacketsinfo-info-loading {
                box-sizing: border-box;
                padding: 10px 0 10px 0;
                min-height: 183px;
            }

            .redpacketsinfo-title-bar {
                margin: 10px 0;
            }

            .redpacketsinfo-title-headicon {
                margin-right: 10px;
            }

            .redpacketsinfo-title-name {
                margin-right: 10px;
                font-size: 18px;
            }

                .redpacketsinfo-title-name em {
                    color: red;
                }

            .redpacketsinfo-title-tips-icon {
                width: 22px;
                height: 22px;
                font-size: 14px;
                color: #ffc619;
                border-radius: 4px;
                border: 1px solid #ffc619;
                transform: scale(.7);
            }

            .redpacketsinfo-subtitle {
                margin: 20px 0;
                padding: 0 16px;
                font-size: 14px;
                color: #999;
            }

            .redpacketsinfo-amount {
                margin: 20px 0;
                color: #c0a03e;
                text-align: center;
            }

            .redpacketsinfo-amount-num {
                margin-right: 10px;
                line-height: 48px;
                font-size: 48px;
                font-weight: 700;
                vertical-align: -2px;
            }

            .redpacketsinfo-amount-company {
                font-size: 14px;
                line-height: 14px;
            }

            .redpacketsinfo-division {
                height: 8px;
                background-color: #ededed;
            }

                .redpacketsinfo-division:after {
                    border-color: #ccc;
                }

            .redpacketsinfo-info-tips {
                margin-left: 16px;
                padding: 10px 16px 10px 0;
                font-size: 12px;
                color: #999;
            }

            .redpacketsinfo-page-item {
                padding: 0 16px;
            }

            .redpacketsinfo-page-item-left {
                margin-right: 16px;
                padding: 10px 0;
            }

            .redpacketsinfo-page-item-right {
                padding: 10px 0;
                font-size: 14px;
            }

            .redpacketsinfo-page-item-name {
                margin-bottom: 6px;
            }

            .redpacketsinfo-page-item-time {
                font-size: 12px;
                color: #999;
            }

            .redpacketsinfo-page-item-amount {
                margin-bottom: 6px;
            }

            .redpacketsinfo-page-item-tips {
                color: #fd9b40;
            }

            .redpacketsinfo-page-item-crown {
                margin-right: 4px;
                height: 14px;
            }

            .redpacketsinfo-page-morebtn {
                min-height: 42px;
                font-size: 12px;
                color: #999;
            }

            .redpacketslist-overlay-pop {
                background: transparent;
            }

            .redpacketslist-overlay {
                width: 100%;
                height: 100%;
                overflow: visible;
            }

            .redpacketslist-selecttype-overlay {
                border-radius: 8px 8px 0 0;
            }

            .redpacketslist-selecttype-btn {
                height: 46px;
            }

            .redpacketslist-selecttype-btn-division {
                height: 8px;
                background-color: #ededed;
            }

            .redpacketslist-selectyear-overlay .redpacketslist-datetime-picker .van-picker-column:nth-child(2) {
                display: none;
            }

            .redpacketslist-header {
                background-color: #f36050;
            }

                .redpacketslist-header .app-bar-back-btn-icon, .redpacketslist-header .app-bar-title {
                    color: #ffe9a6;
                }

            .redpacketslist-scroll {
                box-sizing: border-box;
                height: 100%;
                overflow-y: auto;
                padding-top: 52px;
            }

            .redpacketslist-info-box {
                padding: 20px 16px;
                background-color: #ededed;
            }

            .redpacketslist-year-select {
                font-size: 16px;
                color: #c0a03e;
            }

            .redpacketslist-tips {
                margin: 12px 0;
                font-size: 16px;
            }

            .redpacketslist-amount {
                margin: 12px 0;
                font-size: 48px;
                font-weight: 700;
            }

            .redpacketslist-details {
                width: 100%;
                max-width: 600px;
            }

            .redpacketslist-details-item {
                color: #666;
            }

            .redpacketslist-details-item-number {
                font-size: 36px;
            }

            .redpacketslist-details-item-tips {
                font-size: 14px;
            }

            .redpacketslist-total-box {
                margin-top: 12px;
                margin-bottom: 36px;
                font-size: 18px;
                color: #666;
            }

            .redpacketslist-total-strong {
                color: #c0a03e;
            }

            .redpacketslist-page-item {
                padding: 0 16px;
            }

            .redpacketslist-page-item-left {
                margin-right: 16px;
                padding: 10px 0;
            }

            .redpacketslist-page-item-right {
                padding: 10px 0;
                font-size: 14px;
            }

            .redpacketslist-page-itemd-name-box {
                margin-bottom: 4px;
            }

            .redpacketslist-page-item-name {
                margin-right: 4px;
            }

            .redpacketslist-page-item-tips-icon {
                width: 20px;
                height: 20px;
                font-size: 14px;
                color: #fff;
                border-radius: 4px;
                border: 1px solid #ffc619;
                background-color: #ffc619;
                transform: scale(.65);
            }

            .redpacketslist-page-item-time {
                font-size: 12px;
                color: #999;
            }

            .redpacketslist-page-item-amount {
                margin-bottom: 6px;
            }

            .redpacketslist-page-item-total {
                font-size: 12px;
                color: #999;
            }

            .redpacketslist-page-item-tips {
                color: #fd9b40;
            }

            .redpacketslist-page-item-crown {
                margin-right: 4px;
                height: 14px;
            }

            .redpacketslist-page-morebtn {
                min-height: 42px;
                font-size: 12px;
                color: #999;
            }

            .align-center {
                align-items: center!important;
            }

            .justify-center {
                justify-content: center!important;
            }

            .d-flex {
                display: flex!important;
            }

            .redpacketsinfo-division {
                height: 8px;
                background-color: #ededed;
            }

            .app-bar .app-bar-back-btn {
                width: 52px;
                height: 100%;
                margin-right: -26px;
                margin-left: -6px;
                border-style: none;
                border-radius: 0;
                background: transparent;
                font-weight: 700;
            }
        </style>

        <%--<div style="position:fixed;left:0;top:0;">
            <svg class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="27857" width="16" height="16" onclick="javascript:$('.base_page').hide();$('#friend_book').show();$('#index_menu').show();">
                <path d="M716.8 972.8a51.2 51.2 0 0 1-36.352-14.848l-409.6-409.6a51.2 51.2 0 0 1 0-72.192l409.6-409.6a51.2 51.2 0 0 1 72.192 72.192L379.392 512l373.248 373.248A51.2 51.2 0 0 1 716.8 972.8z" fill="#333C4F" p-id="27858"></path></svg>
        </div>--%>
        <div class="redpacketsinfo-overlay van-popup van-popup--center" style="animation-duration: 0s; z-index: 2011;">
            <header class="app-bar d-flex justify-space-between redpacketsinfo-header" style="height: 52px; z-index: 9999;">
                <div class="app-bar-left-box d-flex align-center">
                    <button class="app-bar-back-btn van-button van-button--default van-button--normal" type="button" onclick="show_page('#chat_message');">
                        <div class="van-button__content">
                            <span class="van-button__text">
                                <svg class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="27857" width="16" height="16">
                                    <path d="M716.8 972.8a51.2 51.2 0 0 1-36.352-14.848l-409.6-409.6a51.2 51.2 0 0 1 0-72.192l409.6-409.6a51.2 51.2 0 0 1 72.192 72.192L379.392 512l373.248 373.248A51.2 51.2 0 0 1 716.8 972.8z" fill="#ffffff" p-id="27858"></path></svg>

                            </span>

                        </div>
                    </button>
                    <h2 class="app-bar-title van-ellipsis"></h2>
                    <h2 class="app-bar-title-sub van-ellipsis"></h2>
                </div>
                <%--<div class="app-bar-right-box">
                    <button class="icon-btn van-button van-button--default van-button--normal" style="height: 52px; width: 52px;">
                        <div class="van-button__content"><span class="van-button__text"><i class="van-icon van-icon-bars" style="color: rgb(255, 255, 255); font-size: 20px;">
                            <!---->
                        </i></span></div>
                    </button>
                </div>--%>
            </header>
            <div class="redpacketsinfo-scroll">
                <div class="redpacketsinfo-info-box">
                    <div class="redpacketsinfo-title-bar d-flex align-center justify-center">
                        <img src="static/images/avatar-1.jpg" loading="lazy" class="auto-domain-image redpacketsinfo-title-headicon" style="width: 26px; height: 26px;"><div class="redpacketsinfo-title-name">桓易槐vC的红包</div>
                        <!---->
                    </div>
                    <div class="redpacketsinfo-subtitle d-flex align-center justify-center"><span style="text-align: center;">恭喜发财，大吉大利</span></div>


                    <div id="user_rec_amount" style="font-size: 39px; text-align: center; font-weight: bold; color: #BF9F6F; padding-bottom: 22px;">
                        <span id="receive_amount">0.99</span><span style="margin-left: 7px; font-size: 12px; font-weight: 500;">元</span>

                        <div style="font-size: 12px; font-weight: 100; color: #b19770; margin-top: 8px;">
                            已转入您的账户余额
                        </div>
                    </div>

                    <div id="rec_error_tip" style="font-size: 22px; font-weight: 100; color: rgb(115,111,105); margin-top: 8px; font-weight: bold; text-align: center; padding-bottom: 22px; display: none;">很遗憾，您手慢了！</div>


                </div>
                <div class="redpacketsinfo-division van-hairline--top van-hairline--bottom"></div>

                <div class="redpacketsinfo-info-tips van-hairline--bottom"><span><span>红包金额0.00，等待领取</span></span></div>


                <div id="rec_list" class="flex_grow">
                </div>



            </div>
        </div>

    </div>




    <!--记录列表 -->
    <div id="play_records" class="base_page" style="display: none;">

        <div style="display: flex; align-items: center; padding: 18px 12px; background: #EDEDED; color: #000;">


            <div style="font-size: 16px; margin-left: 10px; width: 100%; text-align: center;">
                游戏记录/分享
            </div>



            <svg t="" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="3945" width="16" height="16" onclick="show_page('#chat_message');">
                <path d="M453.44 512L161.472 220.032a41.408 41.408 0 0 1 58.56-58.56L512 453.44 803.968 161.472a41.408 41.408 0 0 1 58.56 58.56L570.56 512l291.968 291.968a41.408 41.408 0 0 1-58.56 58.56L512 570.56 220.032 862.528a41.408 41.408 0 0 1-58.56-58.56L453.44 512z" fill="#000000" p-id="3946"></path></svg>


        </div>



        <div id="lists" class="flex_grow" style="background: #fff; padding: 15px;">

            <%--<div style="display: flex; border-bottom: 1px solid #f1f1f1; padding: 18px 10px; color: #5c1cf7;">
                <span style="display: flex; align-items: center; position: relative; left: -3px;">



                    <img src="/static/images/r1.png" alt="" width="50" style="margin-right: 10px;">
                    <img src="static/images/avatar-1.jpg" width="60" height="60" style="margin-right: 10px;">

                    <div>
                        <b>29***34</b>
                        <div style="color: #5a5b5c; font-size: 14px; margin-top: 10px;">
                            盈利金额：<b style="color: #F4941D;">188.00</b>
                        </div>
                        <b></b>
                    </div>
                </span>

            </div>--%>
        </div>


        <script>
            var more_list = function (index) {
                v3api("lists", { data: { page: 'play_records', p: index, limit: 10 } }, function (e) {

                    for (var i = 0; i < e.data.list.length; i++) {
                        var obj = e.data.list[i];
                        if (obj.state == 0) {

                            $('#lists').append('<div style="padding-bottom: 18px; border-bottom: 1px solid #f2f2f2; margin-bottom: 18px;"><div style="color: #20a349;display:flex;"><span>' + obj.gametitle + '（' + obj.expect + '&#26399）</span><a style="margin-left:auto;background: #07C160;color: #fff;font-size: 12px;width: 55px;text-align: center;height: 28px;line-height: 28px;border-radius: 2px;" onclick="share_play(\'' + obj.token + '\')">分享</a></div><div style="margin-top: 10px; line-height: 22px; font-size: 12px; color: #2a2b2c;"><div>&#35746&#21333&#21495：' + obj.trano + '</div><div>&#29609&#27861：' + obj.playtitle + '</div><div>&#25237&#27880&#20869&#23481：' + obj.playcode + '</div>' + (obj.state == 0 ? '' : '<div>&#24320&#22870&#21495&#30721：' + obj.resultcode + '</div>') + '<div>&#19979&#27880&#37329&#39069：' + obj.amount + '</div></div><div style="font-size: 12px; margin-top: 8px; color: gray; display: flex;"><div>' + timestampToDateTimeString(obj.time) + '</div><div style="margin-left: auto;">' + (obj.state == 0 ? '&#26410&#24320&#22870' : obj.state == -1 ? '<span style="color:green;">&#26410&#20013&#22870</span>' : obj.state == 1 ? '<span style="color:#dd2727;">&#20013;' + obj.okamount + '<img src="/static/images/coin.png" alt="" height="12" width="12" style="margin-left: 5px;position: relative;top: 1px;"></span>' : '&#24322&#24120') + '</div></div></div>');

                        }
                    }



                    if (e.data.list.length == 10) {
                        $('#lists').append('<div id="load_more" next_id="' + (parseInt(index) + 1) + '"></div>');
                    }
                    finish_trigger();

                })
            }


            var get_play_records = function () {
                show_page('#play_records');

                trigger_check = 0;
                more_list(0);

            }

            function timestampToDateTimeString(timestamp) {
                // 将时间戳乘以1000以转换为13位时间戳（Date对象使用13位时间戳）
                var date = new Date(timestamp * 1000);

                // 获取年、月、日、时、分、秒
                var year = date.getFullYear();
                var month = String(date.getMonth() + 1).padStart(2, '0'); // 月份从0开始，需要加1
                var day = String(date.getDate()).padStart(2, '0');
                var hours = String(date.getHours()).padStart(2, '0');
                var minutes = String(date.getMinutes()).padStart(2, '0');
                var seconds = String(date.getSeconds()).padStart(2, '0');

                // 格式化日期时间字符串
                var formattedDateTimeString = year + '-' + month + '-' + day + ' ' + hours + ':' + minutes + ':' + seconds;

                return formattedDateTimeString;
            }



            $(document).ready(function () {
                console.log("准备就绪");
                $("#lists").scroll(function () {
                    var element = $(this);

                    if (element.scrollTop() + element.innerHeight() >= element[0].scrollHeight) {
                        console.log("已滚动到底部");
                        trigger_bottom();
                    }

                });

            });

            var trigger_check = 0;
            var trigger_bottom = function () {
                if (trigger_check != 0) {
                    return;
                }
                trigger_check = 1;
                console.log("滚动条已经到达底部为" + $(document).scrollTop());

                if (typeof (more_list)) {
                    var m = $('#lists').find("#load_more");
                    if (m.html() != undefined) {
                        m.remove();
                        more_list(m.attr("next_id"));
                    }
                }
            }

            var finish_trigger = function () {
                trigger_check = 0;
            }



            var receive_reward = function (rank, t) {


                layer.open({
                    type: 2,
                    content: '奖励领取中',
                    time: t,
                    shadeClose: false
                })

                setTimeout(function () {

                    v3api("receive_reward", {
                        data: {
                            rank: rank
                        }
                    }, function (e) {
                        //tp(e.msg);
                        show_page('#ranking_reward_result');
                        $('.redpacketsinfo-title-name').html('佣金排名奖励');
                        $('.redpacketsinfo-subtitle span').html('第' + e.ranking_number + '名奖励');
                        $('#ranking_reward_amount').html(e.receive_amount);
                    })


                }, t * 1000);





            }
        </script>




    </div>


    <style>
        .countdown-container {
            display: inline-block;
        }

            .countdown-container .countdown-item-box {
                position: relative;
                box-sizing: border-box;
                margin: 0 4px;
                padding: 0 4px;
                height: 24px;
                min-width: 28px;
                border-radius: 4px;
                font-size: 12px;
                color: #2e4496;
                background: #fff;
                font-family: clock;
                box-shadow: 0 1px 1px 1px rgba(0,0,0,.2);
                transform: scaleY(1.25);
            }

                .countdown-container .countdown-item-box:before {
                    content: "";
                    position: absolute;
                    top: 50%;
                    right: 100%;
                    width: 2px;
                    height: 4px;
                    margin-top: -2px;
                    background: #fff;
                }

                .countdown-container .countdown-item-box:after {
                    content: "";
                    position: absolute;
                    top: 50%;
                    left: 100%;
                    width: 2px;
                    height: 4px;
                    margin-top: -2px;
                    background: #fff;
                }
    </style>


    <script>
        var selbtn = '<svg t="" class="icon q_button q_sel" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="" width="30" height="30"><path d="M512.2 65.4c-247.1 0-447.5 200.4-447.5 447.5s200.4 447.5 447.5 447.5S959.7 760 959.7 512.9 759.3 65.4 512.2 65.4z m220 348L488.8 656.9c-8.1 8.1-18.8 12.2-29.4 12.2-10.7 0-21.3-4.1-29.4-12.2L292.2 519.2c-6.3-6.3-9.7-14.6-9.7-23.5s3.5-17.2 9.7-23.5c13-13 34.1-13 47 0l120.1 120.1 225.9-225.9c13-13 34-13 47 0 6.3 6.3 9.7 14.6 9.7 23.5 0.1 8.9-3.4 17.2-9.7 23.5z" fill="#05C160" p-id="3571"></path></svg>';
        var unselbtn = '<svg t="" class="icon q_button q_unsel" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="" width="30" height="30"><path d="M512 950.857143a438.857143 438.857143 0 1 1 438.857143-438.857143 438.857143 438.857143 0 0 1-438.857143 438.857143z m0-804.571429a365.714286 365.714286 0 1 0 365.714286 365.714286A365.714286 365.714286 0 0 0 512 146.285714z" p-id="4280" fill="#cdcdcd"></path></svg>';



        var show_page = function (id) {
            $('body').css("background", "#EDEDED");
            $('.base_page').hide();
            $(id).show();
        }


        var __page = get_param('page');
        if (__page != "") {
            show_page('#' + __page);
        }

        var show_newfriend = function () {
            $('#newfriend_detail').show();
            $('#index_menu').hide();
            get_newfriend();
        }

        var get_user = function (type) {
            console.log('获取好友', type);
            $('#friend_class_name').html(type);

            $('#msg_friends').html('');

            v3api("friend_list", {
                data: {
                    from_type: type,
                }
            }, function (e) {
                console.log('friend_list', e);

                $('#user_list').html('<div style="display: flex;background: rgb(255, 255, 255);padding: 12px;align-items: center;margin-bottom: 5px;color: #2a2b2c;" friendchat="" onclick="show_newfriend()">                <div style="text-align: center;border-radius: 3px;display: flex;">                    <svg t="" class="icon" viewBox="0 0 1026 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="" width="38" height="38"><path d="M0 511.385846A511.385846 511.385846 0 1 0 511.385846 0 510.976409 510.976409 0 0 0 0 511.385846z m0 0" fill="#d81e06" p-id="33712"></path><path d="M496.236705 525.306677a160.089564 160.089564 0 1 1 113.413835-47.085166 159.270692 159.270692 0 0 1-113.413835 47.085166z m0-273.093962a112.594962 112.594962 0 1 0 112.594962 112.594962 112.594962 112.594962 0 0 0-112.594962-112.594962z m0 0" fill="#FFFFFF" p-id="33713"></path><path d="M245.661735 816.82527a23.747301 23.747301 0 0 1-23.337865-20.471811 279.235506 279.235506 0 0 1-2.866053-40.943623 275.960016 275.960016 0 0 1 468.395042-198.986006q8.598161 8.188725 16.377449 16.786886a23.747301 23.747301 0 0 1-35.620952 31.526589l-13.511395-13.920832a228.465414 228.465414 0 0 0-386.917233 163.774491 231.740904 231.740904 0 0 0 2.456617 33.57377 23.747301 23.747301 0 0 1-20.062375 27.022791z m0 0" fill="#FFFFFF" p-id="33714"></path><path d="M550.691723 818.872451H250.165534a23.747301 23.747301 0 1 1 0-47.494602h300.526189a23.747301 23.747301 0 1 1 0 47.494602zM672.294282 810.27429l2.456618-149.444222a23.747301 23.747301 0 1 1 47.494602 0l-2.456618 149.444222a23.747301 23.747301 0 1 1-47.494602 0z" fill="#FFFFFF" p-id="33715"></path><path d="M771.787285 752.953219H622.343063a23.747301 23.747301 0 1 1 0-47.494602h149.444222a23.747301 23.747301 0 0 1 0 47.494602z" fill="#FFFFFF" p-id="33716"></path></svg>                </div>                <div style="margin-left: 10px;">新的好友</div><div style="margin-left:auto;"><div style="background: red;color: rgb(255, 255, 255);width: 19px;height: 19px;line-height: 18px;border-radius: 50%;text-align: center;font-size: 12px;display: none;" class="new_friend_number">0</div></div>            </div>');

                chatData.friend_list = e.list;

                for (var i = 0; i < e.list.length; i++) {
                    var object = e.list[i]

                    var user_remark = "";
                    try {
                        if (object.user_remark != "") {
                            user_remark = "<span rk='" + object.uuid + "' style='color: rebeccapurple;font-weight: bold;'>(" + object.user_remark + ")</span> ";
                        }
                    } catch (e) {

                    }

                    //+ (object.username != '' ? '<span style="color:gray;font-size: 12px;">（' + object.username + '）</span>' : '')
                    object.chatid = object.chatid.replace(/friend/g, "newchat")
                    var user_click = 'onclick="get_chat(\'@' + object.chatid + '\',' + '\'' + object.name + '\',null,' + '\'' + object.uuid + '\'' + ')"';
                    var _fhtml = '<div style="display: flex; background: #fff; padding: 12px; align-items: center;" friendChat="' + ('@' + object.chatid) + '" user_uuid="' + object.uuid + '" {click} >        {selbtn}        <div style="text-align: center; border-radius: 3px;">                    <img src="' + object.avatar + '" style="    border-radius: 3px;" width="38" height="38" >                </div>                <div style="margin-left: 10px;" class="username_text">                    ' + object.name + user_remark + '                </div>   ' + xhtml.replace(/{uuid}/g, object.uuid) + '         </div>';
                    $('#user_list').append(_fhtml.replace(/{selbtn}/g, '').replace(/{click}/g, user_click));
                    $('#msg_friends').append(_fhtml.replace(/{selbtn}/g, '<div style="padding-right: 9px;">' + unselbtn + '</div>').replace(/{click}/g, 'onclick="selclick(this)"'));

                }
            })
        }

        var get_newfriend = function (type) {
            v3api("newfriend_list", {
                data: {
                }
            }, function (e) {
                console.log('newfriend_list', e);

                $('#newfriend_list').html('');

                chatData.friend_list = e.list;

                for (var i = 0; i < e.list.length; i++) {
                    var object = e.list[i]

                    var user_remark = "";
                    try {
                        if (object.user_remark != "") {
                            user_remark = "<span rk='" + object.uuid + "' style='color: rebeccapurple;font-weight: bold;'>(" + object.user_remark + ")</span> ";
                        }
                    } catch (e) {

                    }

                    object.chatid = object.chatid.replace(/friend/g, "newchat")

                    //formatDate
                    // $('#newfriend_list').append('<div style="display: flex; background: #fff; padding: 12px; align-items: center;" friendChat="' + ('@' + object.chatid) + '" onclick="get_chat(\'@' + object.chatid + '\',' + '\'' + object.name + '\',null,' + '\'' + object.uuid + '\'' + ')" >                <div style="text-align: center; border-radius: 3px;">                    <img src="' + object.avatar + '" width="38" height="38" >                </div>                <div style="margin-left: 10px;" class="username_text">                    ' + object.name + user_remark + '                </div>   ' + xhtml.replace(/{uuid}/g, object.uuid) + '         </div>');

                    var date = formatDate(object.addtime);
                    $('#newfriend_list').append('<div style="background: #fff; padding: 8px;  font-size: 16px; color: gray;" adddate="' + (date == "近三天" || date == "三天前" ? date : '') + '">                ' + date + '            </div>            <div style="display: flex; background: #fff; padding: 5px 12px; align-items: center;" friendchat="' + ('@' + object.chatid) + '" onclick="get_chat(\'@' + object.chatid + '\',' + '\'' + object.name + '\',null,' + '\'' + object.uuid + '\'' + ')">                <div style="text-align: center; border-radius: 3px;">                    <img src="' + object.avatar + '" width="38" height="38">                </div>                <div style="margin-left: 10px;">                    <span class="username_text">' + object.name + user_remark + '</span>         ' + xhtml.replace(/{uuid}/g, object.uuid) + '           <div style="color: gray; font-size: 12px; margin-top: 1px;">我们已经是好友了！</div>                </div>                <a style="margin-left: auto; background: #03B803; color: #fff; border-radius: 3px; padding: 6px 10px; font-size: 13px;">查看</a>            </div>');
                }

                $('[adddate="近三天"]').not(':first').remove();
                $('[adddate="三天前"]').not(':first').remove();
            })
        }

        function formatDate(dateString) {
            var inputDate = new Date(dateString);
            var currentDate = new Date();

            var differenceInTime = currentDate - inputDate;
            var differenceInDays = differenceInTime / (1000 * 3600 * 24);

            if (differenceInDays < 1) {
                // 当天
                return inputDate.toTimeString().slice(0, 5);
            } else if (differenceInDays < 3) {
                // 三天内
                return '近三天';
            } else {
                // 三天前
                return '三天前';
            }
        }



        var upload_images = function () {
            file_upload(function (e) {
                if (e.success) {
                    var replyid = $('#reply_data').attr('replyid');
                    close_reply();

                    if (chatData.type == 'allsend') {
                        if ($('#send_button').hasClass('unsend')) {
                            return;
                        }
                        $('#send_button').addClass('unsend');
                        $('#send_button').css('background', '#ddd');
                        for (var i = 0; i < allsend_list.length; i++) {
                            sendUserList.push(allsend_list[i].uuid);
                        }

                        __sendText = '[图片=' + e.imgurl + ']';
                        __msgtype = 'img';

                        allsend_poll();
                        return;
                    }

                    v3api("send_message", {
                        data: {
                            from_type: chatData.type,
                            chatid: chatData.id,
                            touser: chatData.userid,
                            replyid: replyid,
                            msgtype: 'img',
                            msg_text: '[图片=' + e.imgurl + ']'
                        }
                    }, function (e) {

                    })

                }



            })
        }


        $('#redbag_amount').on("keyup", function () {
            var v = $(this).val();
            if (v != "" && parseFloat(v) > 0) {
                $('#total_amount_text').html('￥' + parseFloat(v).toFixed(2));
                $('#send_redbag_button').css('background', "#07C160");
            } else {
                $('#total_amount_text').html('￥0.00');
                $('#send_redbag_button').css('background', "gray");
            }
        })





        //发送红包
        var popup_redbag = function () {
            chatData.select_user = [];
            show_page('#send_redbag');

            $('#select_tip').html('请选择用户')
            $('#select_group_user').html('');

            $('#redbag_amount').val('');
            $('#redbag_number').val('');
            $('#redbag_text').val('');
            $('#selct_group_user').val('');
            $('#selct_group_user').data('suid', '');

            if (chatData.type == "group") {
                $('.redbag_group_item').show();

                $('#redbag_typename').html('拼手气红包');
                $('#redbag_selectuser').hide();
            } else {
                $('#redbag_number').val('1');
                $('.redbag_group_item').hide();
            }

        }
        var send_bag = function () {


            security_password(function (e) {
                v3api("send_message", {
                    data: {
                        paypwd: e.password,
                        from_type: chatData.type,
                        chatid: chatData.id,
                        touser: chatData.userid,
                        msgtype: 'redbag',
                        redbag_amount: $('#redbag_amount').val(),
                        redbag_number: $('#redbag_number').val(),
                        redbag_text: $('#redbag_text').val(),
                        redbag_typename: $('#redbag_typename').html(),
                        redbag_users: $('#select_group_user').data('users')
                    }
                }, function (e) {

                    $(".base_page").hide();
                    $("#chat_message").show();

                })
            })
        }

        // 打开/查看红包详情
        function randomSort(a, b) {
            return Math.random() - 0.5;
        }
        var open_redbag = function (id, name, text) {
            $('.redbag_text1').html(name + '的红包');
            $('.redbag_text2').html(text);
            $('#redbag_circle_open').data("readbagId", id);
            $('#redbag_circle_open').removeClass("rotate");
            $('.open_redbox').show();
            return;
        }

        var open_redbag_detail = function (id) {
            $('.open_redbox').hide();

            v3api("redbag_detail", {
                data: {
                    id: id
                }
            }, function (e) {
                localStorage.setItem("<%=uConfig.p_uid %>_" + id, '1');
                $('#redbagk_' + id).css({ "background": "#edc9a8" });

                $('.redpacketsinfo-title-name').html(e.name + '的红包');
                $('.redpacketsinfo-subtitle span').html(e.text.replace(/\n/g, "<br>"));
                $('.redpacketsinfo-info-tips span').html('红包金额' + parseFloat(e.amount).toFixed(2) + ',等待领取');
                $('.redpacketsinfo-info-tips span').html('已领取' + e.receive_number + '/' + e.number + '个，共' + parseFloat(e.receive_amount).toFixed(2) + '/' + parseFloat(e.amount).toFixed(2) + '元');

                $('#user_rec_amount').hide();
                $('#rec_error_tip').hide();
                if (e.user_amount_receive != -1) {
                    $('#receive_amount').html(e.user_amount_receive);
                    $('#user_rec_amount').show();
                } else {
                    $('#rec_error_tip').html('很遗憾，您手慢了！');
                    $('#rec_error_tip').show();
                }

                $('#rec_list').html('');
                e.list.sort(randomSort);
                for (var i = 0; i < e.list.length; i++) {
                    var object = e.list[i];

                    $('#rec_list').append('<div style="display: flex; background: #fff; padding: 12px; align-items: center;">                            <div style="text-align: center; border-radius: 3px;">                                <img src="static/images/avatar-1.jpg" width="38" height="38">                            </div>                            <div style="margin-left: 13px; display: flex; width: 100%; border-bottom: 1px solid #eee; padding-bottom: 14px; font-size: 14px;">                                <div style="">                                    <div>' + object.name + '      </div>                                    <div style="font-size: 12px; margin-top: 5px; color: #aaa;">                                        ' + object.time + '                                    </div>                                </div>                                <div style="margin-left: auto; color: #000;">                                    ' + object.amount + '元                                </div>                            </div>                        </div>');
                }


                $(".base_page").hide();
                $("#redbag_detail").show();
                $('body').css("background", "#fff");

            })
        }


        $('#search_content').on("keyup", function () {
            var v = $(this).val();
            $(".search_not_result").hide();
            if (v != "") {
                $('.search_page').show();
                $('#search_account').html(v);
            } else {
                $('.search_page').hide();
            }
        })

        var search_user = function () {
            $(".search_page").hide();
            v3api("search_user", {
                error: 1,
                data: {
                    search_content: $('#search_content').val()
                }
            }, function (e) {
                console.log('search_user', e);
                if (e.code != 1) {
                    $(".search_not_result").show();
                    return;
                }

                $("#user_info").show();

            })
        }





        var toggle_emoji = function () {
            $('#other_list').hide();
            $('#emoji_list').toggle();

            to_chatBottom();
        }


        var toggle_other = function () {
            $('#emoji_list').hide();
            $('#other_list').toggle();

            to_chatBottom();
        }

        $('.noclick').on('click', function () {
            event.stopPropagation();
        })

        var close_openBox = function () {
            $('#emoji_list').hide();
            $('#other_list').hide();

        }

        var get_emoji = function (new_text) {
            var text = $('#send_message').val();
            $('#send_message').val(text + new_text);
            autoExpand(document.getElementById('send_message'));


            text_change();
        }

        document.addEventListener("input", function (e) {
            if (e.target.tagName.toLowerCase() === "textarea") {
                autoExpand(e.target);
            }
        });

        function autoExpand(textarea) {
            //textarea.style.height = "auto";
            textarea.style.height = textarea.scrollHeight + "px";
        }



        var back_list = function () {
            chatData.id = '';
            chatData.type = '';
            chatData.chatid = '';
            chatData.userid = -1;

            if ($('#allsend_box').css('display') == 'block') {
                $('#chat_message').hide();
                return;
            }

            if (anonyVisitor) {
                history.go(-1);
                return;
            }

            $('.icon_item[aid=""]').click();
        }

        function newchatid() {
            var template = 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx';
            var guid = template.replace(/[xy]/g, function (c) {
                var r = Math.random() * 16 | 0;
                var v = c === 'x' ? r : (r & 0x3 | 0x8);
                return v.toString(16);
            }).replace(/-/g, '');
            return guid;
        }

        var anonyVisitor = false;//是否游客
        var anonyChatid = localStorage.getItem("chatid");
        if (anonyChatid == null || anonyChatid == "") {
            anonyChatid = newchatid();
            localStorage.setItem("chatid", anonyChatid);
        }

        var lastMessage = true;
        var chat_list = function () {
            v3api("chat_list", {
                data: {
                    target: window.location.hash.replace(/#/g, ''),
                    anonyChatid: anonyChatid
                }
            }, function (e) {
                $('#chat-list').html('');
                chatData.list = e.list;
                if (e.msg == 'anony') {
                    anonyVisitor = true;
                }
                for (var i = 0; i < e.auth_list.length; i++) {
                    var object = e.auth_list[i];
                    if (anonyVisitor) {
                        object.url = '../login.aspx';
                    }

                    var __html = '<div class="menu menu-list" onclick="open_auth(\'' + object.url + '\')">                <div style="display: flex; align-items: center;">                    <div style=" position: relative; ">                        <div style="    width: 60px;    height: 60px;    display: flex;    align-items: center;    justify-content: center;    background: #c7d8ff47;    border-radius: 50%;">  <img src="' + object.imgurl + '" style="width: 100%;height: 100%;border-radius: 12px;">  </div>                    </div>                    <div style="padding-left: 18px;">                        <div style="color: #000; font-weight: 500; font-size: 16px;">' + object.name + '</div>                        <div style="font-size: 12px; margin-top: 4px; max-width: 159px; overflow: hidden; white-space: nowrap; text-overflow: ellipsis;" class="msg_text">' + object.text_details + '</div>                    </div>                    <div style="margin-left: auto; font-size: 12px; color: gray;" class="">    <a style="background: #64e964;color: #127512;font-weight: bold;display: inline-block;padding: 8px 15px;border-radius: 3px;">立即进入</a></div>                </div>            </div>';
                    $('#chat-list').append(__html);
                    //$('#game-list').append(__html);

                    $('.Jnmh-m-submenu').append(' <li class="Jnmh-subli">                <dl class="Jnmh-subdl" gameId="' + object.id + '">                    <dt class="NMH-subdt">' + object.sample_name + '</dt>                    <dd class="NMH-subdd"></dd>                </dl>            </li>');

                }


                for (var i = 0; i < e.list.length; i++) {
                    var object = e.list[i];

                    if (anonyVisitor) {
                        var read_num = 0;
                        try {
                            read_num = parseInt(readFromLocalStorage('chat_gmqun', '0'));
                        } catch (e) {

                        }
                        object.unread_number = parseInt(object.unread_number) - read_num;
                        if (object.unread_number < 0) {
                            object.unread_number = 0;
                        }
                    }

                    update_message_number(object.unread_number);
                    new_windows_chat(object);

                    if (anonyVisitor && i == 0) {
                        get_chat('@' + object.type + '_' + object.chatid, object.userid, null, object.uuid);
                    }
                }

                lastMessage = false;
            })
        }
        chat_list();

        var close_auth = function () {
            v3api("close_auth", {
                error: 1,
                data: {}
            }, function (e) {
            })
        }
        close_auth();



        var open_auth = function (url) {
            location.href = 'select_rooms.aspx?u=' + encodeURIComponent(url) + "&from=game";
        }

        var new_windows_chat = function (object, istop) {
            if ($('[chat="' + ('@' + object.type + '_' + object.chatid) + '"]').length > 0) {
                return;
            }
            //wait_update

            var user_remark = "";

            try {
                if (object.user_remark != "") {
                    user_remark = "<span rk='" + object.uuid + "' style='color: rebeccapurple;font-weight: bold;'>(" + object.user_remark + ")</span> ";
                } else {
                    user_remark = "<span rk='" + object.uuid + "' style='color: rebeccapurple;font-weight: bold;'></span> ";
                }
            } catch (e) {

            }

            var __style = "";
            var __class = "";
            try {
                if (object.type == "group") {
                    __class = ' istop';
                    istop = true;
                }
                if (object.issend == '-1') {
                    __class = ' istop';
                }
                if (lastMessage == false) {
                    __class = ' istop';
                }
            } catch (e) {

            }

            var text = '<div chattype="' + object.type + '" chat="' + ('@' + object.type + '_' + object.chatid) + '" class="menu menu-list ' + __class + '" onclick="get_chat(\'@' + object.type + '_' + object.chatid + '\',' + object.userid + ',null,\'' + object.uuid + '\')" style="' + __style + '">                <div style="display: flex; align-items: center;">                    <div style=" position: relative; ">                        <img src="' + (object.avatar == '' ? 'static/images/avatar-' + (object.type == "group" ? "1" : "2") + '.jpg' : object.avatar) + '" width="60" height="60">     ' + (object.type == 'group' ? ' <span style="position: absolute; bottom: 0; left: 0; width: 28px; background: #fdfd46; color: #000; font-weight: 500; display: inline-block; text-align: center; font-size: 12px; padding: 2px 3px; border-top-right-radius: 8px;">群聊</span>' : '') + '     <div class="unread_number" style="background:red;color:#fff;width: 18px;height: 18px;line-height: 18px;position: absolute;border-radius: 50%;text-align: center;top: -6px;right: -6px;font-size: 12px;' + (object.unread_number == 0 ? "display:none;" : "") + '">' + object.unread_number + '</div>          </div>                    <div style="padding-left: 18px;">                        <div  class="username_text" style="color: #000; font-weight: 500; font-size: 16px;">                            ' + object.name + user_remark + (object.type == 'friend' ? xhtml.replace(/{uuid}/g, object.uuid) : '') + '                        </div>                        <div style="font-size: 12px; margin-top: 4px; max-width: 159px; overflow: hidden; white-space: nowrap; text-overflow: ellipsis;" class="msg_text">                            ' + (object.at_number > 0 ? tiptt : '') + object.last_message + '                        </div>                    </div>                    <div style="margin-left: auto; font-size: 12px; color: gray;" class="msg_time">' + object.show_time + '</div>                </div>            </div>';
            if (istop) {
                $('#chat-list').prepend(text);
            } else {
                $('#chat-list').append(text);
            }
        }

        var resetTopWindows = function () {
            var msgWins = new Array();
            $('.istop').each(function () {
                msgWins.push($(this));
            })

            for (var i = msgWins.length - 1; i >= 0; i--) {
                msgWins[i].prependTo($('#chat-list'));
            }
        }

        var getTime = function () {
            return Math.floor(Date.now() / 1000);
        }

        var stateMessage = 0;
        var lastTimeMessage = getTime();
        var get_chat_message = function () {
            if (stateMessage == 1 && getTime() - lastTimeMessage < 20) {
                return;
            }
            lastTimeMessage = getTime();
            stateMessage = 1;
            v3api("get_message", {
                error: 1,
                data: {
                    from_type: 'list',
                    msgid: chatData.msgid,
                    chatid: chatData.id,
                    type: chatData.type
                }
            }, function (e) {
                stateMessage = 0;
                //setTimeout(function () {
                //    get_chat_message();
                //}, 2000);
                if (e.code != 1) {
                    tp(e.msg);
                    return;
                }

                try {

                    $('.new_friend_number').html(e.new_friend_number);
                    if (e.new_friend_number > 0) {
                        $('.new_friend_number').show();
                    } else {
                        $('.new_friend_number').hide();
                    }
                } catch (e) {

                }


                e.list.reverse();

                var is_update = false;
                if (chatData.msgid != 0) {
                    is_update = true;
                } else {
                    chatData.msgid = -1;
                }

                for (var i = 0; i < e.nclient.length; i++) {
                    var ab = e.nclient[i];
                    if (ab.name == "delete") {
                        $('[msgid="' + ab.msgid + '"]').remove()
                    }
                }

                for (var i = 0; i < e.list.length; i++) {
                    var object = e.list[i];
                    //console.log('消息遍历', i + "/" + e.list.length, object.text);
                    object.msgid = parseInt(object.msgid);
                    if (object.msgid > chatData.msgid) {
                        chatData.msgid = object.msgid;
                    }

                    var messageWindow = $('[chat="' + object.chatid + '"]');

                    if (is_update) {
                        var msg_text = object.text;
                        switch (object.msgtype) {
                            case "img":
                                msg_text = "[图片]";
                                break;
                            case "redbag":
                                msg_text = "[红包]" + msg_text.split('$$')[1];
                                break;
                            case "card":
                                msg_text = "[晒出奖励]";
                                break;
                            case "share":
                                msg_text = "[分享]";
                                break;
                            default:
                                break;

                        }

                        if (object.chatid == "@group_all_gouprs") {
                            messageWindow = $('[chattype="group"]');
                        }



                        if (messageWindow.length == 0) {
                            //console.log("进入{0}")
                            var is_added = false;
                            for (var dda = 0; dda < chatData.list.length; dda++) {
                                var tp = chatData.list[dda];
                                if (tp.chatid == object.chatid.split('_')[1] && tp.type == object.type) {
                                    is_added = true;
                                    object.name = tp.name;
                                    break;
                                }
                            }
                            if (!is_added) {
                                chatData.list.push({
                                    type: object.type,
                                    chatid: object.chatid.split('_')[1],
                                    name: object.name,
                                })
                            }

                            console.log('新窗口', object);
                            new_windows_chat({
                                user_remark: object.user_remark,
                                uuid: object.uuid,
                                type: object.type,
                                chatid: object.chatid.split('_')[1],
                                userid: object.userid,
                                unread_number: 0,
                                name: (object.type == 'group' ? object.group_name : object.name),
                                last_message: '',
                                show_time: '00:00',
                                avatar: (object.type == 'group' ? object.group_avatar : object.avatar)
                            })
                            messageWindow = $('[chat="' + object.chatid + '"]');
                        }




                        messageWindow.find(".msg_text").html((messageWindow.find(".tiptt").length > 0 ? tiptt : '') + msg_text);
                        messageWindow.find(".msg_time").html(object.create_time);

                        if (object.ta == 1) {
                            //console.log('get_message【数量更新】');
                            messageWindow.each(function () {
                                update_message_number(1);
                                $(this).find(".unread_number").html(parseInt($(this).find(".unread_number").html()) + 1).show();

                                //@跟回复事件
                                if (msg_text.indexOf("uuid='" + chatData.uuid + "'") != -1) {
                                    messageWindow.find(".tiptt").remove();
                                    messageWindow.find(".msg_text").prepend(tiptt);
                                    console.log('[提到了你]');
                                }
                            })
                        }
                        messageWindow.prependTo($('#chat-list'));
                        if (object.ta == 0) {
                            messageWindow.removeClass("istop")
                        }
                        resetTopWindows();

                        if (object.chatid == chatData.chatid || (chatData.type == "group" && object.chatid == "@group_all_gouprs")) {
                            new_windows_msg({
                                type: object.msgtype,
                                object: object
                            })

                        }
                    }
                }


                //if (e.redbag_poll) {
                //    if (chatData.type == 'group') {
                //        $('#redbag_poll').show();
                //        countdownToTime(e.redbag_poll.split('~')[1]);
                //    } else {
                //        $('#redbag_poll').hide();
                //    }
                //} else {
                //    $('#redbag_poll').hide();
                //}


                if (e.redbag_poll_token) {
                    $('#get_pool_redbag').data("tokenid", e.redbag_poll_token);
                    $('#get_pool_redbag').show();
                } else {
                    $('#get_pool_redbag').hide();
                }
                if (chatData.type == 'group') {

                    $('#redbag_poll').show();
                    if (e.next_time != "") {
                        //console.log('e.poll_text', e.poll_text);
                        if (e.poll_text != "") {
                            $('#redbag_poll_text').html(e.poll_text);
                        }
                        if (chatData.red_pool_next_time != e.next_time.split('~')[1]) {
                            clearInterval(countdownInterval);
                            countdownInterval = -1;
                        }
                        countdownToTime(e.next_time.split('~')[1]);
                    } else {
                        $('#redbag_poll_text').html("今日红包雨已结束");
                        $('#redbag_hour').html("--");
                        $('#redbag_minute').html("--");
                        $('#redbag_second').html("--");

                        //$('#redbag_poll').hide();
                    }

                } else {
                    $('#redbag_poll').hide();
                }



            })
        }
        chatData.msgid = 0;
        get_chat_message();

        setInterval(function () {
            get_chat_message();
        }, 2000);

        $('#get_pool_redbag').on('click', function () {
            open_redbag($(this).data("tokenid"), '-', '-');
        })


        var countdownInterval = -1;
        var countdownToTime = function (time) {
            if (countdownInterval != -1) {
                return;
            }


            chatData.red_pool_next_time = time;
            // 获取倒计时容器和显示小时、分钟、秒的元素
            var hourElement = document.getElementById("redbag_hour");
            var minuteElement = document.getElementById("redbag_minute");
            var secondElement = document.getElementById("redbag_second");

            // 将传入的时间字符串解析为 Date 对象
            var targetTime = new Date();
            var timeParts = time.split(":");
            if (timeParts.length === 3) {
                targetTime.setHours(parseInt(timeParts[0], 10));
                targetTime.setMinutes(parseInt(timeParts[1], 10));
                targetTime.setSeconds(parseInt(timeParts[2], 10));
            } else {
                // 处理传入时间格式错误的情况
                console.error("传入时间格式不正确，应为hh:mm:ss");
                return;
            }

            // 计算时间差（毫秒）
            var currentTime = new Date();
            var timeDifference = targetTime - currentTime;

            // 更新倒计时的显示
            function updateCountdown() {
                if (timeDifference <= 0) {
                    clearInterval(countdownInterval);
                    countdownInterval = -1;
                    // 在这里执行倒计时结束后的操作
                    // 例如显示提示消息或触发其他事件
                    console.log("已超时");


                    $('#redbag_poll').hide();

                    return;
                }

                var hours = Math.floor(timeDifference / 3600000); // 1小时 = 3600000毫秒
                var minutes = Math.floor((timeDifference % 3600000) / 60000); // 1分钟 = 60000毫秒
                var seconds = Math.floor((timeDifference % 60000) / 1000); // 1秒 = 1000毫秒

                // 更新到页面中
                hourElement.textContent = hours.toString().padStart(2, "0");
                minuteElement.textContent = minutes.toString().padStart(2, "0");
                secondElement.textContent = seconds.toString().padStart(2, "0");

                // 减少剩余时间
                timeDifference -= 1000;


                //console.log('倒计时计算', countdownInterval);
            }

            // 初始更新倒计时
            updateCountdown();

            // 每一秒更新倒计时
            countdownInterval = setInterval(updateCountdown, 1000);
        };








        function getTextBetweenStrings(inputString, startText, endText) {
            var results = [];
            var startIndex = 0;

            while (startIndex < inputString.length) {
                var startIdx = inputString.indexOf(startText, startIndex);
                if (startIdx === -1) {
                    break; // 如果找不到起始文本，结束循环
                }

                var endIdx = inputString.indexOf(endText, startIdx + startText.length);
                if (endIdx === -1) {
                    break; // 如果找不到结束文本，结束循环
                }

                var textBetween = inputString.substring(startIdx + startText.length, endIdx);
                results.push(textBetween);

                // 更新 startIndex，继续搜索下一个匹配
                startIndex = endIdx + endText.length;
            }

            return results;
        }



        var replace_emoji = function (text) {

            text = text.replace(/\n/g, "<br>")



            // 使用正则表达式匹配
            var regex = /\<div reply.*?<\/div>/g;
            var matches = [];
            var match;

            while ((match = regex.exec(text)) !== null) {
                //console.log(text, match);
                matches.push(match[0]);
            }

            var tempText = text.replace(regex, '')
            var img_list = getTextBetweenStrings(tempText, "[图片=", "]");


            if (matches.length > 0) {
                console.log('源数据', text);
                console.log('matches', matches, matches.join(''));
            }

            if (img_list.length > 0) {
                //console.log('matches', matches, "[开头数据] " + text.replace(/\[图片=.*?\]/g, ''), "[转换] " + text + ' == >> ' + tempText);

                var __html = "<img src='" + img_list[0] + "' style='max-width:180px;' onclick='toggleImgFullScreen(\"" + img_list[0] + "\")'>";
                if (img_list[0].indexOf(',') != -1) {
                    var kk = img_list[0].split(',');
                    //img_list[0] = kk[0];

                    __html = "";
                    for (var i = 0; i < kk.length; i++) {
                        __html += "<div><img src='" + kk[i] + "' style='width:80px;' onclick='toggleImgFullScreen(\"" + kk[i] + "\")'></div>";
                    }
                }
                return matches.join('') + __html;
            }



            var replace_list = getTextBetweenStrings(text, "/::[", "]");

            if (replace_list.length == 0) {
                return text;
            }

            var emoji_data = "/::[微笑]/::[撇嘴]/::[色]/::[发呆]/::[得意]/::[流泪]/::[害羞]/::[闭嘴]/::[睡]/::[大哭]/::[尴尬]/::[发怒]/::[调皮]/::[呲牙]/::[惊讶]/::[难过]/::[囧]/::[抓狂]/::[吐]/::[偷笑]/::[愉快]/::[白眼]/::[傲慢]/::[困]/::[惊恐]/::[流汗]/::[憨笑]/::[悠闲]/::[奋斗]/::[咒骂]/::[疑问]/::[嘘]/::[晕]/::[衰]/::[骷髅]/::[敲打]/::[再见]/::[擦汗]/::[抠鼻]/::[鼓掌]/::[坏笑]/::[左哼哼]/::[右哼哼]/::[哈欠]/::[鄙视]/::[委屈]/::[快哭了]/::[阴险]/::[亲亲]/::[可怜]/::[菜刀]/::[西瓜]/::[啤酒]/::[咖啡]/::[猪头]/::[玫瑰]/::[凋谢]/::[嘴唇]/::[爱心]/::[心碎]/::[蛋糕]/::[炸弹]/::[便便]/::[月亮]/::[太阳]/::[拥抱]/::[强]/::[弱]/::[握手]/::[胜利]/::[抱拳]/::[勾引]/::[拳头]/::[OK]/::[跳跳]/::[发抖]/::[怄火]/::[转圈]/::[";


            var g = (']' + emoji_data + "/::").split(']/::[');

            //console.log('g1', g);

            for (var i = 0; i < replace_list.length; i++) {

                var position = g.indexOf(replace_list[i]);
                if (position < 0) {
                    position = parseInt(replace_list[i]) + 1;
                }
                console.log('aa', position, replace_list[i]);
                //if (position !== -1) {
                //    console.log(replace_list[i] + " 在数组中的位置是：" + position);
                //} else {
                //    console.log(replace_list[i] + " 未在数组中找到。");
                //}

                try {
                    var pattern = new RegExp("/::\\[" + replace_list[i] + "\\]", "g");
                    text = text.replace(pattern, '<img src="kindeditor/plugins/emoticons/images/' + (position - 1) + '.gif" style="width: 18px; height: 18px;margin-right:3px;">')
                } catch (e) {
                    console.log('表情解析出错', e, replace_list[i])
                }
            }


            return text;
        }

        var to_chatBottom = function (nocheck) {

            //console.log("回到底部");

            var distanceToBottom = 0;
            if (typeof (nocheck) == "undefined") {

                // 获取滚动区域的高度
                var containerHeight = $('#chat_box').height();

                // 获取内容的总高度
                var contentHeight = $('#chat_box')[0].scrollHeight;

                // 获取滚动条的当前位置
                var scrollPosition = $('#chat_box').scrollTop();

                // 计算滚动条距离底部的距离
                distanceToBottom = contentHeight - (scrollPosition + containerHeight);

                // 输出距离底部的距离
                //console.log("滚动条距离底部的距离：" + distanceToBottom + " 像素");
            }

            if (distanceToBottom < 500) {

                $('#chat_box').scrollTop(9999999999);
                setTimeout(function () {
                    $('#chat_box').scrollTop(9999999999);
                }, 100);
                setTimeout(function () {
                    $('#chat_box').scrollTop(9999999999);
                }, 500)

            }
        }
        var uuid = '<%=pmlist["uuid"] %>';

        var xhtml = '';
        var acthtml = '';

        <%if (uConfig.gd(userdt, "chatad") == "1")
          {
              %>
        //background: #373232;color: #dbdb8e;
        xhtml = '<a class="usernoteButton" style="background: #d5d5d5;color: #2a2b2c;padding: 3px 5px;border-radius: 3px;font-size: 13px;margin: 0 5px;font-weight: bold;display: inline-block;opacity: 0.5;" onclick="usercard(event,\'{uuid}\');">设置备注</a>';
        acthtml = '<a class="usernoteButton" style="background: #d5d5d5;color: #2a2b2c;padding: 3px 5px;border-radius: 3px;font-size: 13px;margin: 0 5px;font-weight: bold;display: inline-block;opacity: 0.5;" onclick="cancel_message(event,\'{uuid}\',\'{msgid}\');">消息撤销</a>';
        acthtml += '<a class="usernoteButton" style="background: #ef6545;color: #fff;padding: 3px 5px;border-radius: 3px;font-size: 13px;margin: 0 5px;display: inline-block;" onclick="chat_user_nospeak(event,\'{uuid}\');">禁言</a>';
        var usercard = function (event, __uuid) {
            event.stopPropagation();
            userCardData.uuid = __uuid;
            card_userinfo();
            $('#user_card').show();
        }

        var cancel_message = function (event, __uuid, msgid) {
            event.stopPropagation();


            layer.open({
                content: '<div style="color:red;font-weight: bold;font-size: 18px;">是否撤销以下内容</div><div style="text-align:center;border-bottom: 1px  dashed gray;margin: 16px 22px;"></div><div style="display: flex;background: #eee;padding: 8px;">' + $('[msgid="' + msgid + '"]').html() + '</div>'
                , btn: ['确认', '取消']
                , yes: function (index) {
                    layer.close(index);

                    v3api("cancel_message", {
                        data: {
                            msgid: msgid
                        }
                    }, function (e) {
                        tp(e.msg);
                    })
                }
            })
        }


        var chat_user_nospeak = function (event, __uuid) {
            event.stopPropagation();

            v3api("user_nospeak", {
                data: {
                    uuid: __uuid,
                    text: '用户禁言'
                }
            }, function (e) {
                tp(e.msg);
            })
        }



        var userCardData = {
            chatid: '',
            uuid: ''
        }
        var card_userinfo = function () {
            userCardData.chatid = '';
            $('#card_avatar').html('-');
            $('#card_username').html('');
            $('#card_remark').html('');
            $('#user_remark').val('');


            v3api("card_userinfo", {
                data: {
                    uuid: userCardData.uuid
                }
            }, function (e) {
                $('#card_avatar').attr('src', e.avatar);
                $('#card_username').html(e.username);
                $('#card_remark').html(e.remark);
                $('#user_speak_button').text(e.chat_nospeak == 1 ? '解除禁言' : '用户禁言');

                userCardData.chatid = e.chatid;
                userCardData.uuid = e.uuid;
            })
        }
        var user_ichat = function () {
            get_chat(userCardData.chatid, userCardData.uuid, null, userCardData.uuid)
        }

        var usercard_submit = function () {
            v3api("user_remark", {
                data: {
                    uuid: userCardData.uuid,
                    user_remark: $('#user_remark').val()
                }
            }, function (e) {
                tp(e.msg);
                if (e.code == 1) {
                    $('#card_remark').html($('#user_remark').val());
                    $('[rk="' + userCardData.uuid + '"]').html("(" + $('#user_remark').val() + ")");
                }
            })
        }
        var user_nospeak = function () {
            var text = $('#user_speak_button').text();
            v3api("user_nospeak", {
                data: {
                    uuid: userCardData.uuid,
                    text: text
                }
            }, function (e) {
                tp(e.msg);
                if (e.code == 1) {
                    $('#user_speak_button').text(text == '用户禁言' ? '解除禁言' : '用户禁言');
                }
            })
        }











        <%
          } %>


        var new_windows_msg = function (pms) {
            var type = pms.type;
            var isLast = false;
            try {
                if (pms.last) {
                    isLast = true;
                }
            } catch (e) {

            }
            pms = pms.object;
            var isupdate = false;
            if ($('[msgid="' + pms.msgid + '"]').length > 0) {

                switch (type) {
                    case "text":
                    case "img":
                        isupdate = true;
                        break;
                    default:
                        break;

                }

                if (isupdate == false) {
                    return;
                }
            }
            //console.log('新消息', pms);
            var actionHtml = acthtml.replace(/{uuid}/g, pms.uuid).replace(/{msgid}/g, pms.msgid);
            switch (type) {
                case "text":
                case "img":
                    pms.text = replace_emoji(pms.text);

                    if (pms.userid == "-100") {
                        pms.avatar = chatData.group_avatar;
                        pms.name = "<span style='color: #cd1717;font-weight: bold;'>系统消息</span>";
                    }


                    var __html = '';
                    if (pms.ta == 1) {


                        var user_remark = "";
                        try {
                            if (pms.user_remark != "") {
                                user_remark = "<span rk='" + pms.uuid + "' style='color: rebeccapurple;font-weight: bold;'>(" + pms.user_remark + ")</span> ";
                            }
                        } catch (e) {

                        }

                        __html = '<div msgid="' + pms.msgid + '"  style="display: flex; margin-bottom: 18px;">                <div>                    <img src="' + (pms.avatar == '' ? 'static/images/avatar-1.jpg' : pms.avatar) + '" onclick="atuser(\'' + pms.uuid + '\',\'' + pms.name + '\')" width="50" height="50" style="border-radius: 3px;">                </div>                <div style="margin-left: 10px;text-align: left;">                    <div style="font-size: 12px; color: gray; margin-bottom: 5px;">' + '<span class="user_name">' + (pms.type == "group" ? pms.name + " " : "") + '</span>' + user_remark + pms.create_time + xhtml.replace(/{uuid}/g, pms.uuid) + actionHtml + '</div>                    <div style="background: #fff; padding: 8px 12px; border-radius: 3px; word-wrap: break-word; max-width: calc(100vw - 180px);display: inline-block;" class="message_details">                        ' + pms.text + '                                   </div>                </div>            </div>';



                    } else {

                        __html = '<div msgid="' + pms.msgid + '"  style="display: flex; margin-bottom: 18px;" pm2="' + pms.pm2 + '">                <div style="margin-left: auto;text-align: right;">                    <div style="font-size: 12px; color: gray; margin-bottom: 5px; text-align: right;">' + pms.create_time + actionHtml + '</div>                    <div style="background: #95EC69;padding: 8px 12px;border-radius: 3px;word-wrap: break-word;max-width: calc(100vw - 180px);display: inline-block;text-align: left;" class="message_details">                       ' + pms.text + '                                      </div>                </div>                <div style="margin-left: 10px;">                    <img src="' + (pms.avatar == '' ? 'static/images/avatar-2.jpg' : pms.avatar) + '" onclick="atuser(\'' + pms.uuid + '\',\'' + pms.name + '\')" width="50" height="50" style="border-radius: 3px;">                </div>            </div>';

                    }
                    if (isupdate) {
                        //console.log('update_meesage', pms.msgid);
                        $('[msgid="' + pms.msgid + '"]').replaceWith(__html);
                    } else {
                        //console.log('newmsg', pms.text);
                        if (isLast) {
                            $('#chat_box').prepend(__html);
                        } else {
                            $('#chat_box').append(__html);
                        }
                    }


                    break;
                case "html":
                    var __html = '<div msgid="' + pms.msgid + '" style="margin-bottom: 18px;text-align: center;font-size: 12px;color: #6d6c75;font-weight: bold;padding: 10px 0;text-shadow: 5px 5px 5px #332e2e38;">            ' + pms.text + '            </div>';
                    if (isLast) {
                        $('#chat_box').prepend(__html);
                    } else {
                        $('#chat_box').append(__html);
                    }
                    break;
                case "redbag":
                    var show_text = "[当前版本不支持]";
                    var redbag_list = getTextBetweenStrings(pms.text, "[红包=", "]");
                    if (redbag_list.length > 0) {
                        var redbag = redbag_list[0].split('$$');
                        //console.log('redbag', redbag);
                        if (redbag.length == 3) {
                            var redbag_key = localStorage.getItem('<%=uConfig.p_uid %>_' + redbag[0]);

                            show_text = '<div id="redbagk_' + redbag[0] + '" style="background: ' + (redbag_key == "1" ? "#edc9a8" : "#fd9b40") + ';padding: 8px 12px;border-radius: 3px;word-wrap: break-word;max-width: calc(100vw - 100px);display: inline-block;text-align: left;width: 250px;cursor:pointer;" onclick="open_redbag(\'' + redbag[0] + '\',\'' + pms.name + '\',\'' + redbag[1].replace(/\n/g, "<br>").replace(/\r/g, "") + '\')">     <div style="display:flex;color: #fff;align-items: center;">    <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" t="1608100632456" class="icon" viewBox="0 0 1024 1024" version="1.1" p-id="14864" width="46" height="46"><defs><style type="text/css"></style></defs><path d="M57.046912 0m76.8 0l716.8 0q76.8 0 76.8 76.8l0 870.4q0 76.8-76.8 76.8l-716.8 0q-76.8 0-76.8-76.8l0-870.4q0-76.8 76.8-76.8Z" fill="#D73C1E" p-id="14865"></path><path d="M850.646912 0a76.8 76.8 0 0 1 76.8 76.8l0.0256 275.8144c-122.2912 51.968-272.64 82.5856-435.2 82.5856-162.5856 0-312.96-30.6432-435.2512-82.5856L57.046912 76.8a76.8 76.8 0 0 1 76.8-76.8h716.8z" fill="#F14C2E" p-id="14866"></path><path d="M517.846912 409.6m-153.6 0a153.6 153.6 0 1 0 307.2 0 153.6 153.6 0 1 0-307.2 0Z" fill="#F7D49A" p-id="14867"></path><path d="M528.906112 512h-21.9136v-51.2h-65.7664v-20.48h65.7664V409.6h-65.7664v-20.48h53.504L441.046912 318.3104l18.816-11.1104 54.3232 71.68h12.4672l48.5632-67.84 18.2272 11.392-47.7696 66.688H594.646912v20.48h-63.6416l-2.0992 2.944V440.32H594.646912v20.48h-65.7408v51.2z" fill="#E98337" p-id="14868"></path></svg>        <div style="    margin-left: 10px;">        ' + redbag[1].replace(/\n/g, "<br>") + '        </div></div><div style="    color: #eeeeeec4;    border-top: 1px solid hsl(0deg 0% 100% / 32%);    margin-top: 10px;    padding-top: 5px;    font-size: 12px;">    ' + redbag[2] + '</div></div>';
                        }
                    }


                    if (pms.ta == 1) {


                        $('#chat_box').append('<div msgid="' + pms.msgid + '" style="display: flex; margin-bottom: 18px;">                <div>                    <img src="' + (pms.avatar == '' ? chatData.group_avatar : pms.avatar) + '" width="50" height="50" style="border-radius: 3px;">                </div>                <div style="margin-left: 10px;text-align: left;">                    <div style="font-size: 12px; color: gray; margin-bottom: 5px;">' + (pms.type == "group" ? pms.name + " " : "") + pms.create_time + '</div>                                        ' + show_text + '                                                </div>            </div>');


                    } else {

                        $('#chat_box').append('<div msgid="' + pms.msgid + '" style="display: flex; margin-bottom: 18px;">                <div style="margin-left: auto;text-align: right;">                    <div style="font-size: 12px; color: gray; margin-bottom: 5px; text-align: right;">' + pms.create_time + '</div>                                          ' + show_text + '                                                 </div>                <div style="margin-left: 10px;">                    <img src="' + (pms.avatar == '' ? 'static/images/avatar-2.jpg' : pms.avatar) + '" width="50" height="50" style="border-radius: 3px;">                </div>            </div>');
                    }

                    break;
                case "card":

                    var show_text = "[当前版本不支持]";
                    //var redbag_list = getTextBetweenStrings(pms.text, "[红包=", "]");
                    //if (redbag_list.length > 0) {
                    //    var redbag = redbag_list[0].split('$$');
                    //    //console.log('redbag', redbag);
                    //    if (redbag.length == 3) {

                    //    }
                    //}

                    var card_list = getTextBetweenStrings(pms.text, "[卡片=", "]");
                    if (card_list.length > 0) {
                        var cdata = {};
                        //console.log('clist', card_list);
                        try {
                            cdata = JSON.parse(card_list[0]);
                        } catch (e) {

                        }
                        if (cdata != {}) {

                            if (pms.name == "") {
                                pms.name = cdata.name;
                            }

                            show_text = '<div style="background: #fff; border: 1px solid #bbb; border-radius: 12px;">            <div style="background: #FFC107; padding: 10px; text-align: center; font-size: 13px; color: #000; border-top-left-radius: 12px; border-top-right-radius: 12px;">                ' + cdata.name + ' 当前排名：' + cdata.ranking_number + '                   </div>            <div style="padding-top: 11px;font-weight: bold;">                <div style="font-size: 13px; padding: 6px 10px; display: flex; color: #5b5c5e;">                    <div style="width: 130px;text-align: left;font-weight: bold;">                        日期                    </div>                    <div>' + cdata.date + '</div>                </div>                <div style="font-size: 13px; padding: 6px 10px; display: flex; color: #5b5c5e;">                    <div style="width: 130px;text-align: left;font-weight: bold;">                        今日任务总额                    </div>                    <div>' + parseFloat(cdata.daily_amount).toFixed(0) + '元</div>                </div>                <div style="font-size: 13px; padding: 6px 10px; display: flex; color: #5b5c5e;">                    <div style="width: 130px;text-align: left;font-weight: bold;">                        今日任务次数                    </div>                    <div>' + cdata.daily_count + '次</div>                </div>                <div style="font-size: 13px; padding: 6px 10px; display: flex; color: #5b5c5e;">                    <div style="width: 130px;text-align: left;font-weight: bold;">                        今日任务佣金                    </div>                    <div>' + parseFloat(cdata.daily_award_amount).toFixed(0) + '元</div>                </div>        <div style="font-size: 13px; padding: 6px 10px; display: flex; color: #5b5c5e;">                    <div style="width: 130px;text-align: left;font-weight: bold;">                        历史任务佣金                    </div>                    <div>' + parseFloat(cdata.reward_amount).toFixed(0) + '元</div>                </div>        <a style="border-top: 1px solid #ddd;padding: 10px;font-size: 12px;text-align: center;color: #2663ab;font-weight: bold;margin-top: 13px;display: inline-block;width: 100%;outline: none;text-decoration: none;box-sizing: border-box;" onclick="share_reward()" >                    <span>我要分享</span>                </a>            </div>        </div>';

                        }


                    }


                    card_list = getTextBetweenStrings(pms.text, "[PLAY=", "]");
                    if (card_list.length > 0) {
                        var cdata = {};
                        //console.log('clist', card_list);
                        try {
                            cdata = JSON.parse(card_list[0]);
                        } catch (e) {

                        }
                        if (cdata != {}) {

                            if (pms.name == "") {
                                pms.name = cdata.name;
                            }

                            show_text = '<div style="background: #fff;border-radius: 12px;">            <div style="background: linear-gradient(178deg, #9bf1be, #f5f5f5);padding: 10px;text-align: center;font-size: 13px;color: #000;border-radius: 12px;">    <div style="    display: flex;    align-items: center;"><div><img src="' + cdata.imgurl + '" style="width: 28px;height: 28px;border-radius: 3px;border: 2px solid #fff;"></div><div style="    margin-left: 8px;    text-align: left;">    <div style="font-weight: bold;">' + cdata.gamename + '</div><div style="    font-size: 12px;    color: #656363;">' + cdata.expect + '期</div></div><div style="margin-left:auto;padding-left: 19px;">    <a style="font-weight: bold;display: flex;flex-direction: column;align-items: center;font-size: 12px;padding: 2px 10px;border-radius: 8px;color: #fff;background: #000;" onclick="follow_click(\'' + cdata.token + '\')">跟投</a></div></div><div style="margin-top:10px;">    <div style="text-align: left;border-radius: 9px;padding: 5px;font-weight: bold;color: #000;">        <svg t="1707125312312" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="59692" width="12" height="12" style="    margin-right: 5px;"><path d="M821.4016 276.8896s-149.9136-116.6848-341.8112-71.424c-222.208 52.3776-265.3696 234.1888-265.3696 319.7952 0 168.9088 76.4928 337.5104 284.5184 379.648 142.9504 17.0496 318.1568-125.1328 318.1568-125.1328l-240.2304-265.8816 244.736-237.0048z" fill="#FF94A4" p-id="59693"></path><path d="M821.4016 525.2608m-64.4096 0a64.4096 64.4096 0 1 0 128.8192 0 64.4096 64.4096 0 1 0-128.8192 0Z" fill="#FF94A4" p-id="59694"></path><path d="M619.4176 515.2256c31.7952-30.5664 65.8432-62.3104 98.9696-93.184 50.8416-47.4112 98.8672-92.2112 138.0352-131.7888 10.9568-11.1104 11.8784-28.672 2.0992-40.8576-80.7936-100.6592-201.0112-158.3616-329.9328-158.3616-233.1648 0-422.8608 189.696-422.8608 422.8608s189.696 422.8608 422.8608 422.8608c117.6576 0 230.9632-49.6128 310.8352-136.1408 10.752-11.6224 10.8544-29.5424 0.3072-41.3184-62.1568-69.4784-146.5856-162.9184-220.3136-244.0704z m-90.7776 360.0384c-199.2704 0-361.4208-162.1504-361.4208-361.4208s162.1504-361.4208 361.4208-361.4208c100.9664 0 195.7376 41.4208 263.9872 114.5856-34.304 33.6896-74.2912 70.9632-116.1728 110.08-40.9088 38.144-83.1488 77.568-121.2928 114.7904a30.69952 30.69952 0 0 0-1.2288 42.6496c70.8096 77.824 155.0336 170.9568 220.672 244.0704-66.56 61.7472-154.7264 96.6656-245.9648 96.6656z" fill="#5D5D66" p-id="59695"></path><path d="M441.4464 356.608h-39.936v-39.936c0-16.9472-13.7728-30.72-30.72-30.72s-30.72 13.7728-30.72 30.72v39.936h-39.936c-16.9472 0-30.72 13.7728-30.72 30.72s13.7728 30.72 30.72 30.72h39.936v39.936c0 16.9472 13.7728 30.72 30.72 30.72s30.72-13.7728 30.72-30.72v-39.936h39.936c16.9472 0 30.72-13.7728 30.72-30.72s-13.7728-30.72-30.72-30.72zM821.4016 430.1312c-52.4288 0-95.1296 42.6496-95.1296 95.1296 0 52.4288 42.6496 95.1296 95.1296 95.1296 52.4288 0 95.1296-42.6496 95.1296-95.1296 0-52.4288-42.7008-95.1296-95.1296-95.1296z m0 128.8192c-18.5856 0-33.6896-15.104-33.6896-33.6896s15.104-33.6896 33.6896-33.6896 33.6896 15.104 33.6896 33.6896-15.104 33.6896-33.6896 33.6896z" fill="#5D5D66" p-id="59696"></path></svg>玩法：' + cdata.playtitle + ',下注金额：' + cdata.amount + '<div style="color: #737237;font-weight: 500;font-size: 14px;margin-top: 5px;display: flex;align-items: center;justify-content: center;">我下注内容是<span style="    font-size: 22px;    font-weight: bold;    margin-left: 5px;    color: #09c282;    text-shadow: 5px 5px 5px #dd956b8a;">' + cdata.number + '</span></div></div></div></div>                    </div>';



                        }


                    }


                    card_list = getTextBetweenStrings(pms.text, "[GAMEDATA=", "]");
                    if (card_list.length > 0) {
                        var cdata = {};
                        try {
                            cdata = JSON.parse(card_list[0]);
                        } catch (e) {

                        }
                        if (cdata != {}) {

                            if (pms.name == "") {
                                pms.name = cdata.name;
                            }

                            show_text = '<div style="background: #FFEB31;color: #756938;padding: 16px 20px;border-radius: 18px;word-wrap: break-word;text-align: left;font-size: 12px;display: flex;align-items: center;">        <div>            <div style="font-size: 12px;">                <svg t="" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="31266" width="20" height="20" style="margin-right: 5px;">                    <path d="M1017.6 204.8V32c-300.8 89.6-454.4 281.6-441.6 576v384h435.2V550.4h-224c-6.4-147.2 70.4-256 230.4-345.6z m-563.2 0V32C147.2 121.6 0 313.6 6.4 608v384h435.2V550.4H217.6c-6.4-147.2 70.4-256 236.8-345.6z" fill="#87857b8c" p-id="31267"></path></svg>                投注<span style="font-size: 15px; font-weight: bold; margin: 0 3px;">' + cdata.play + '</span>元 中奖<span style="font-size: 15px; font-weight: bold; margin: 0 3px;">' + cdata.win + '</span>元                   <svg t="" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="31800" width="20" height="20" style="margin-left: 5px; position: relative;">                    <path d="M1024 107.789474v378.610526c-21.557895 238.484211-144.168421 385.347368-367.831579 441.936842V759.915789c35.031579-21.557895 63.326316-41.768421 83.536842-63.326315 35.031579-48.505263 52.547368-80.842105 52.547369-94.31579v-83.536842H623.831579V107.789474H1024zM413.642105 107.789474v378.610526c-13.473684 253.305263-137.431579 400.168421-367.831579 441.936842V759.915789c70.063158-21.557895 115.873684-74.105263 137.431579-157.642105v-83.536842H13.473684V107.789474h400.168421z" fill="#87857b8c" p-id="31801"></path></svg>            </div>            <div style="font-size: 22px;color: #2a2b2c;margin-top: 10px;">                <span style="font-size: 13px;background: red;color: yellow;padding: 4px 10px;border-radius: 8px;position: relative;top: -2px;margin-right: 7px;font-weight: bold;">今日盈利</span><span style="margin: 0 2px; font-weight: bold;">' + cdata.amount + '</span>                     </div>        </div>        <div style="margin-left: 15px;">            <svg t="" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="21607" width="32" height="32">                <path d="M512 10.24C789.11488 10.24 1013.76 234.88512 1013.76 512S789.11488 1013.76 512 1013.76 10.24 789.11488 10.24 512 234.88512 10.24 512 10.24z m-163.84 573.44h-40.96a51.2 51.2 0 0 0-51.2 51.2v81.92a51.2 51.2 0 0 0 51.2 51.2h40.96a51.2 51.2 0 0 0 51.2-51.2v-81.92a51.2 51.2 0 0 0-51.2-51.2z m184.32-61.44h-40.96a51.2 51.2 0 0 0-51.2 51.2v143.36a51.2 51.2 0 0 0 51.2 51.2h40.96a51.2 51.2 0 0 0 51.2-51.2v-143.36a51.2 51.2 0 0 0-51.2-51.2z m184.32-61.44h-40.96a51.2 51.2 0 0 0-51.2 51.2v204.8a51.2 51.2 0 0 0 51.2 51.2h40.96a51.2 51.2 0 0 0 51.2-51.2V512a51.2 51.2 0 0 0-51.2-51.2z m-97.21856-183.88992a30.72 30.72 0 0 0-34.816 22.2208l-0.65536 2.88768a30.72 30.72 0 0 0 17.2032 32.9728L541.184 389.12h-96.19456l-116.75648 99.49184-2.29376 2.17088a30.72 30.72 0 0 0-3.072 38.64576l1.90464 2.49856 2.17088 2.29376a30.72 30.72 0 0 0 38.64576 3.072l2.49856-1.90464L467.59936 450.56h97.1776l80.32256-72.33536 0.02048 10.89536 0.14336 2.94912A30.72 30.72 0 0 0 706.56 389.12v-88.6784l-84.09088-22.87616z" fill="#222222" p-id="21608"></path></svg>        </div>    </div>';



                        }


                    }



                    card_list = getTextBetweenStrings(pms.text, "[TEAMDATA=", "]");
                    if (card_list.length > 0) {
                        var cdata = {};
                        try {
                            cdata = JSON.parse(card_list[0]);
                        } catch (e) {

                        }
                        if (cdata != {}) {

                            if (pms.name == "") {
                                pms.name = cdata.name;
                            }

                            //show_text = '<div style="background: linear-gradient(282deg, #e1e1e1, #f5f3d5); color: #756938; padding: 16px 20px; border-radius: 18px; word-wrap: break-word; text-align: left; font-size: 12px; display: flex; align-items: center;">            <div>                <div style="font-size: 12px;">                    <svg t="" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="31266" width="20" height="20" style="margin-right: 5px;">                        <path d="M1017.6 204.8V32c-300.8 89.6-454.4 281.6-441.6 576v384h435.2V550.4h-224c-6.4-147.2 70.4-256 230.4-345.6z m-563.2 0V32C147.2 121.6 0 313.6 6.4 608v384h435.2V550.4H217.6c-6.4-147.2 70.4-256 236.8-345.6z" fill="#87857b8c" p-id="31267"></path></svg>团队昨日人数：<span style="font-size: 15px; font-weight: bold; margin: 0 3px;">' + cdata.teamnum + '</span>人<br> 团队昨日总收入：<span style="font-size: 15px; font-weight: bold; margin: 0 3px;">' + parseNumber(cdata.teamincome) + '</span>元<br> 直属下级人数：<span style="font-size: 15px; font-weight: bold; margin: 0 3px;">' + cdata.invitenum + '</span>人<br> 直属昨日一键任务佣金：<span style="font-size: 15px; font-weight: bold; margin: 0 3px;">' + parseNumber(cdata.agentbork) + '</span>元<svg t="" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="31800" width="20" height="20" style="margin-left: 5px; position: relative;">                            <path d="M1024 107.789474v378.610526c-21.557895 238.484211-144.168421 385.347368-367.831579 441.936842V759.915789c35.031579-21.557895 63.326316-41.768421 83.536842-63.326315 35.031579-48.505263 52.547368-80.842105 52.547369-94.31579v-83.536842H623.831579V107.789474H1024zM413.642105 107.789474v378.610526c-13.473684 253.305263-137.431579 400.168421-367.831579 441.936842V759.915789c70.063158-21.557895 115.873684-74.105263 137.431579-157.642105v-83.536842H13.473684V107.789474h400.168421z" fill="#87857b8c" p-id="31801"></path></svg>                </div>          <span style="color: gray;display:inline-block;margin-top:12px;">直属日工资：<span style="font-size: 12px; margin: 0 2px; color: #2a2b2c;">' + parseNumber(cdata.agent_salary) + '</span><br><p style="margin-top: 3px;margin-bottom: 0;">合伙人日工资：<span style="font-size: 12px; margin: 0 2px; color: #2a2b2c;">' + parseNumber(cdata.team_salary) + '</span></p></span>       <div style="font-size: 22px; color: #2a2b2c; margin-top: 10px;"><span style="font-size: 13px; background: red; color: yellow; padding: 4px 10px; border-radius: 8px; position: relative; top: -2px; margin-right: 7px; font-weight: bold;">可领日工资</span><span style="margin: 0 2px; font-weight: bold;">' + parseNumber(cdata.total_salary) + '</span>                     </div>                           </div>            <div style="margin-left: 15px;">                <svg t="" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="" width="32" height="32">                    <path d="M859.2 352c40 0 72-32 72-72 0 39.76-32.24 72-72 72zM787.2 280c0 40 32 72 72 72-39.76 0-72-32.24-72-72zM424.592 500.864c16.976 25.056 44.448 42.56 76.608 45.792-31.856-3.232-59.504-20.64-76.608-45.792zM859.2 208c39.76 0 72 32.24 72 72 0-40-32-72-72-72z" fill="#E8E8E8" p-id="3896"></path><path d="M859.2 406.4c81.632 0 149.792 66.464 162.512 153.28 1.456-15.696 2.288-31.584 2.288-47.68 0-88.08-22.256-170.976-61.44-243.36 0.4 3.728 0.64 7.52 0.64 11.36 0 57.6-46.4 104-104 104s-104-46.4-104-104 46.4-104 104-104c17.984 0 34.864 4.528 49.584 12.496C814.912 73.472 672.048 0 512 0S209.088 73.472 115.216 188.496C129.936 180.528 146.816 176 164.8 176c57.6 0 104 46.4 104 104s-46.4 104-104 104-104-46.4-104-104c0-3.84 0.24-7.632 0.64-11.36C22.256 341.024 0 423.92 0 512c0 16.096 0.832 31.984 2.288 47.68 12.72-86.816 80.88-153.28 162.512-153.28 89.6 0 161.6 80 163.2 179.2H5.344C41.04 833.488 254.24 1024 512 1024s470.96-190.512 506.656-438.4H694.4c1.6-99.2 75.2-179.2 164.8-179.2z m-347.2-104c76.8 0 137.6 62.4 137.6 137.6s-62.4 137.6-137.6 137.6-137.6-62.4-137.6-137.6 60.8-137.6 137.6-137.6zM731.2 848H292.8C296 715.2 392 609.6 512 609.6S728 715.2 731.2 848z" fill="#E8E8E8" p-id="3897"></path><path d="M522.8 546.656c32.144-3.232 59.632-20.736 76.608-45.792-17.104 25.152-44.752 42.56-76.608 45.792zM859.2 208c-40 0-72 32-72 72 0-39.76 32.24-72 72-72zM452.656 354.256c-25.008 16.976-42.48 44.432-45.712 76.528 3.232-31.792 20.592-59.424 45.712-76.528z" fill="#E8E8E8" p-id="3898"></path><path d="M419.088 491.808c0.448 0.832 0.992 1.6 1.456 2.432 1.248 2.16 2.496 4.336 3.888 6.4 0.048 0.08 0.112 0.144 0.176 0.224 17.088 25.152 44.752 42.56 76.608 45.792 3.536 0.352 7.136 0.544 10.784 0.544s7.248-0.192 10.8-0.544c31.856-3.232 59.504-20.64 76.608-45.792 0.048-0.08 0.112-0.144 0.176-0.224 1.392-2.064 2.64-4.24 3.888-6.4 0.464-0.816 1.008-1.6 1.456-2.432 0.848-1.584 1.568-3.248 2.336-4.864 0.704-1.472 1.472-2.912 2.112-4.416 0.496-1.168 0.864-2.4 1.312-3.6 0.768-2.032 1.568-4.032 2.224-6.112 0.288-0.912 0.464-1.872 0.72-2.8 0.672-2.4 1.344-4.8 1.856-7.264 0.256-1.216 0.368-2.48 0.576-3.728 0.368-2.224 0.784-4.416 1.008-6.672 0.352-3.52 0.544-7.104 0.544-10.72 0-57.6-48-105.6-105.6-105.6-3.616 0-7.184 0.192-10.72 0.544-2.272 0.224-4.496 0.64-6.72 1.024-1.216 0.208-2.48 0.32-3.68 0.56-2.496 0.512-4.912 1.2-7.344 1.872-0.896 0.256-1.84 0.432-2.72 0.704-2.112 0.656-4.16 1.472-6.224 2.256-1.168 0.448-2.368 0.8-3.504 1.28-1.536 0.64-2.976 1.424-4.48 2.128-1.616 0.768-3.248 1.472-4.816 2.32-0.848 0.448-1.632 0.992-2.448 1.472-2.16 1.248-4.304 2.48-6.368 3.872-0.112 0.08-0.208 0.16-0.32 0.24-25.104 17.088-42.48 44.72-45.712 76.528-0.368 3.52-0.56 7.12-0.56 10.768 0 3.616 0.192 7.2 0.544 10.72 0.224 2.256 0.64 4.464 1.008 6.672 0.208 1.232 0.32 2.496 0.576 3.728 0.496 2.464 1.184 4.864 1.856 7.264 0.256 0.928 0.432 1.888 0.72 2.8 0.64 2.08 1.456 4.096 2.224 6.112 0.448 1.2 0.832 2.432 1.312 3.6 0.64 1.504 1.408 2.944 2.112 4.416 0.768 1.648 1.472 3.312 2.336 4.896z" fill="#FAD97F" p-id="3899"></path><path d="M512 577.6c75.2 0 137.6-62.4 137.6-137.6s-60.8-137.6-137.6-137.6-137.6 62.4-137.6 137.6 62.4 137.6 137.6 137.6z m-98.688-98.688c-0.768-2.032-1.568-4.032-2.224-6.112-0.288-0.912-0.464-1.872-0.72-2.8-0.672-2.4-1.344-4.8-1.856-7.264-0.256-1.216-0.368-2.48-0.576-3.728-0.368-2.224-0.784-4.416-1.008-6.672a107.818 107.818 0 0 1-0.544-10.72c0-3.648 0.192-7.248 0.544-10.8 3.232-32.112 20.704-59.552 45.712-76.528 0.112-0.08 0.208-0.16 0.32-0.24 2.048-1.392 4.208-2.624 6.368-3.872 0.832-0.48 1.616-1.024 2.448-1.472 1.568-0.848 3.216-1.552 4.816-2.32 1.488-0.704 2.944-1.488 4.48-2.128 1.136-0.48 2.336-0.848 3.504-1.28 2.064-0.784 4.096-1.6 6.224-2.256 0.896-0.272 1.824-0.448 2.72-0.704 2.432-0.672 4.864-1.36 7.344-1.872 1.216-0.24 2.464-0.352 3.68-0.56 2.224-0.368 4.448-0.784 6.72-1.024 3.552-0.368 7.12-0.56 10.736-0.56 57.6 0 105.6 48 105.6 105.6 0 3.616-0.192 7.2-0.544 10.72-0.224 2.256-0.64 4.464-1.008 6.672-0.208 1.232-0.32 2.496-0.576 3.728-0.496 2.464-1.184 4.864-1.856 7.264-0.256 0.928-0.432 1.888-0.72 2.8-0.64 2.08-1.456 4.096-2.224 6.112-0.448 1.2-0.832 2.432-1.312 3.6-0.64 1.504-1.408 2.944-2.112 4.416-0.768 1.632-1.488 3.296-2.336 4.864-0.448 0.832-0.992 1.6-1.456 2.432-1.248 2.16-2.496 4.336-3.888 6.4-0.048 0.08-0.112 0.144-0.176 0.224-16.976 25.056-44.448 42.56-76.608 45.792-3.536 0.384-7.136 0.576-10.784 0.576s-7.248-0.192-10.8-0.544c-32.144-3.232-59.632-20.736-76.608-45.792-0.048-0.08-0.112-0.144-0.176-0.224-1.392-2.064-2.64-4.24-3.888-6.4-0.464-0.816-1.008-1.6-1.456-2.432-0.848-1.584-1.568-3.248-2.336-4.864-0.704-1.472-1.472-2.912-2.112-4.416-0.48-1.184-0.864-2.416-1.312-3.616z" fill="" p-id="3900"></path><path d="M512 641.6c-91.2 0-168 75.2-184 174.4h368c-16-99.2-92.8-174.4-184-174.4z" fill="#FAD97F" p-id="3901"></path><path d="M512 609.6c-120 0-216 105.6-219.2 238.4h438.4C728 715.2 632 609.6 512 609.6zM328 816c16-99.2 92.8-174.4 184-174.4s168 75.2 184 174.4H328z" fill="" p-id="3902"></path><path d="M164.8 208c-40 0-72 32-72 72s32 72 72 72 72-32 72-72-32-72-72-72z" fill="#FFFFFF" p-id="3903"></path><path d="M164.8 384c57.6 0 104-46.4 104-104s-46.4-104-104-104c-17.984 0-34.864 4.528-49.584 12.496a103.69 103.69 0 0 0-53.76 80.144A102.408 102.408 0 0 0 60.8 280c0 57.6 46.4 104 104 104z m0-32c-40 0-72-32-72-72s32-72 72-72 72 32 72 72-32 72-72 72z" fill="" p-id="3904"></path><path d="M164.8 438.4c-62.4 0-115.2 49.6-129.6 115.2h257.6c-14.4-65.6-65.6-115.2-128-115.2z" fill="#FFFFFF" p-id="3905"></path><path d="M164.8 406.4c-81.632 0-149.792 66.464-162.512 153.28C1.04 568.16 0.144 576.768 0 585.6h328c-1.6-99.2-73.6-179.2-163.2-179.2z m0 32c62.4 0 113.6 49.6 128 115.2H35.2c14.4-65.6 67.2-115.2 129.6-115.2z" fill="" p-id="3906"></path><path d="M859.2 280m-72 0a72 72 0 1 0 144 0 72 72 0 1 0-144 0Z" fill="#FFFFFF" p-id="3907"></path><path d="M755.2 280c0 57.6 46.4 104 104 104s104-46.4 104-104c0-3.84-0.24-7.632-0.64-11.36-3.744-34.752-24.464-64.288-53.76-80.144C894.064 180.528 877.184 176 859.2 176c-57.6 0-104 46.4-104 104z m104 72c-40 0-72-32-72-72s32-72 72-72 72 32 72 72-32 72-72 72z" fill="" p-id="3908"></path><path d="M860 435.648c-0.288 0-0.56-0.048-0.832-0.048-63.744 0-119.104 52-134.208 120.784h268.416a180.48 180.48 0 0 0-3.264-12.64c-18.48-62.112-69.136-107.68-130.112-108.096z" fill="#FFFFFF" p-id="3909"></path><path d="M1021.712 559.68c-12.72-86.816-80.88-153.28-162.512-153.28-89.6 0-163.2 80-164.8 179.2H1024c-0.144-8.832-1.04-17.44-2.288-25.92z m-295.088-3.28h-1.664c15.104-68.784 70.464-120.784 134.208-120.784 0.288 0 0.56 0.048 0.832 0.048 60.976 0.416 111.632 45.984 130.112 108.112a180.48 180.48 0 0 1 3.264 12.64H726.624v-0.016z" fill="" p-id="3910"></path></svg>            </div>        </div>';


                            var html_obj = $('<div style="background: linear-gradient(282deg, #e1e1e1, #f5f3d5);color: #756938;padding: 16px 20px;border-radius: 18px;word-wrap: break-word;text-align: left;font-size: 12px;display: flex;align-items: center;padding-top: 10px;">            <div>       <div style="text-align: center;/* margin-bottom: 12px; */">                <svg t="" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="" width="52" height="52">                    <path d="M859.2 352c40 0 72-32 72-72 0 39.76-32.24 72-72 72zM787.2 280c0 40 32 72 72 72-39.76 0-72-32.24-72-72zM424.592 500.864c16.976 25.056 44.448 42.56 76.608 45.792-31.856-3.232-59.504-20.64-76.608-45.792zM859.2 208c39.76 0 72 32.24 72 72 0-40-32-72-72-72z" fill="#E8E8E8" p-id="3896"></path><path d="M859.2 406.4c81.632 0 149.792 66.464 162.512 153.28 1.456-15.696 2.288-31.584 2.288-47.68 0-88.08-22.256-170.976-61.44-243.36 0.4 3.728 0.64 7.52 0.64 11.36 0 57.6-46.4 104-104 104s-104-46.4-104-104 46.4-104 104-104c17.984 0 34.864 4.528 49.584 12.496C814.912 73.472 672.048 0 512 0S209.088 73.472 115.216 188.496C129.936 180.528 146.816 176 164.8 176c57.6 0 104 46.4 104 104s-46.4 104-104 104-104-46.4-104-104c0-3.84 0.24-7.632 0.64-11.36C22.256 341.024 0 423.92 0 512c0 16.096 0.832 31.984 2.288 47.68 12.72-86.816 80.88-153.28 162.512-153.28 89.6 0 161.6 80 163.2 179.2H5.344C41.04 833.488 254.24 1024 512 1024s470.96-190.512 506.656-438.4H694.4c1.6-99.2 75.2-179.2 164.8-179.2z m-347.2-104c76.8 0 137.6 62.4 137.6 137.6s-62.4 137.6-137.6 137.6-137.6-62.4-137.6-137.6 60.8-137.6 137.6-137.6zM731.2 848H292.8C296 715.2 392 609.6 512 609.6S728 715.2 731.2 848z" fill="#E8E8E8" p-id="3897"></path><path d="M522.8 546.656c32.144-3.232 59.632-20.736 76.608-45.792-17.104 25.152-44.752 42.56-76.608 45.792zM859.2 208c-40 0-72 32-72 72 0-39.76 32.24-72 72-72zM452.656 354.256c-25.008 16.976-42.48 44.432-45.712 76.528 3.232-31.792 20.592-59.424 45.712-76.528z" fill="#E8E8E8" p-id="3898"></path><path d="M419.088 491.808c0.448 0.832 0.992 1.6 1.456 2.432 1.248 2.16 2.496 4.336 3.888 6.4 0.048 0.08 0.112 0.144 0.176 0.224 17.088 25.152 44.752 42.56 76.608 45.792 3.536 0.352 7.136 0.544 10.784 0.544s7.248-0.192 10.8-0.544c31.856-3.232 59.504-20.64 76.608-45.792 0.048-0.08 0.112-0.144 0.176-0.224 1.392-2.064 2.64-4.24 3.888-6.4 0.464-0.816 1.008-1.6 1.456-2.432 0.848-1.584 1.568-3.248 2.336-4.864 0.704-1.472 1.472-2.912 2.112-4.416 0.496-1.168 0.864-2.4 1.312-3.6 0.768-2.032 1.568-4.032 2.224-6.112 0.288-0.912 0.464-1.872 0.72-2.8 0.672-2.4 1.344-4.8 1.856-7.264 0.256-1.216 0.368-2.48 0.576-3.728 0.368-2.224 0.784-4.416 1.008-6.672 0.352-3.52 0.544-7.104 0.544-10.72 0-57.6-48-105.6-105.6-105.6-3.616 0-7.184 0.192-10.72 0.544-2.272 0.224-4.496 0.64-6.72 1.024-1.216 0.208-2.48 0.32-3.68 0.56-2.496 0.512-4.912 1.2-7.344 1.872-0.896 0.256-1.84 0.432-2.72 0.704-2.112 0.656-4.16 1.472-6.224 2.256-1.168 0.448-2.368 0.8-3.504 1.28-1.536 0.64-2.976 1.424-4.48 2.128-1.616 0.768-3.248 1.472-4.816 2.32-0.848 0.448-1.632 0.992-2.448 1.472-2.16 1.248-4.304 2.48-6.368 3.872-0.112 0.08-0.208 0.16-0.32 0.24-25.104 17.088-42.48 44.72-45.712 76.528-0.368 3.52-0.56 7.12-0.56 10.768 0 3.616 0.192 7.2 0.544 10.72 0.224 2.256 0.64 4.464 1.008 6.672 0.208 1.232 0.32 2.496 0.576 3.728 0.496 2.464 1.184 4.864 1.856 7.264 0.256 0.928 0.432 1.888 0.72 2.8 0.64 2.08 1.456 4.096 2.224 6.112 0.448 1.2 0.832 2.432 1.312 3.6 0.64 1.504 1.408 2.944 2.112 4.416 0.768 1.648 1.472 3.312 2.336 4.896z" fill="#FAD97F" p-id="3899"></path><path d="M512 577.6c75.2 0 137.6-62.4 137.6-137.6s-60.8-137.6-137.6-137.6-137.6 62.4-137.6 137.6 62.4 137.6 137.6 137.6z m-98.688-98.688c-0.768-2.032-1.568-4.032-2.224-6.112-0.288-0.912-0.464-1.872-0.72-2.8-0.672-2.4-1.344-4.8-1.856-7.264-0.256-1.216-0.368-2.48-0.576-3.728-0.368-2.224-0.784-4.416-1.008-6.672a107.818 107.818 0 0 1-0.544-10.72c0-3.648 0.192-7.248 0.544-10.8 3.232-32.112 20.704-59.552 45.712-76.528 0.112-0.08 0.208-0.16 0.32-0.24 2.048-1.392 4.208-2.624 6.368-3.872 0.832-0.48 1.616-1.024 2.448-1.472 1.568-0.848 3.216-1.552 4.816-2.32 1.488-0.704 2.944-1.488 4.48-2.128 1.136-0.48 2.336-0.848 3.504-1.28 2.064-0.784 4.096-1.6 6.224-2.256 0.896-0.272 1.824-0.448 2.72-0.704 2.432-0.672 4.864-1.36 7.344-1.872 1.216-0.24 2.464-0.352 3.68-0.56 2.224-0.368 4.448-0.784 6.72-1.024 3.552-0.368 7.12-0.56 10.736-0.56 57.6 0 105.6 48 105.6 105.6 0 3.616-0.192 7.2-0.544 10.72-0.224 2.256-0.64 4.464-1.008 6.672-0.208 1.232-0.32 2.496-0.576 3.728-0.496 2.464-1.184 4.864-1.856 7.264-0.256 0.928-0.432 1.888-0.72 2.8-0.64 2.08-1.456 4.096-2.224 6.112-0.448 1.2-0.832 2.432-1.312 3.6-0.64 1.504-1.408 2.944-2.112 4.416-0.768 1.632-1.488 3.296-2.336 4.864-0.448 0.832-0.992 1.6-1.456 2.432-1.248 2.16-2.496 4.336-3.888 6.4-0.048 0.08-0.112 0.144-0.176 0.224-16.976 25.056-44.448 42.56-76.608 45.792-3.536 0.384-7.136 0.576-10.784 0.576s-7.248-0.192-10.8-0.544c-32.144-3.232-59.632-20.736-76.608-45.792-0.048-0.08-0.112-0.144-0.176-0.224-1.392-2.064-2.64-4.24-3.888-6.4-0.464-0.816-1.008-1.6-1.456-2.432-0.848-1.584-1.568-3.248-2.336-4.864-0.704-1.472-1.472-2.912-2.112-4.416-0.48-1.184-0.864-2.416-1.312-3.616z" fill="" p-id="3900"></path><path d="M512 641.6c-91.2 0-168 75.2-184 174.4h368c-16-99.2-92.8-174.4-184-174.4z" fill="#FAD97F" p-id="3901"></path><path d="M512 609.6c-120 0-216 105.6-219.2 238.4h438.4C728 715.2 632 609.6 512 609.6zM328 816c16-99.2 92.8-174.4 184-174.4s168 75.2 184 174.4H328z" fill="" p-id="3902"></path><path d="M164.8 208c-40 0-72 32-72 72s32 72 72 72 72-32 72-72-32-72-72-72z" fill="#FFFFFF" p-id="3903"></path><path d="M164.8 384c57.6 0 104-46.4 104-104s-46.4-104-104-104c-17.984 0-34.864 4.528-49.584 12.496a103.69 103.69 0 0 0-53.76 80.144A102.408 102.408 0 0 0 60.8 280c0 57.6 46.4 104 104 104z m0-32c-40 0-72-32-72-72s32-72 72-72 72 32 72 72-32 72-72 72z" fill="" p-id="3904"></path><path d="M164.8 438.4c-62.4 0-115.2 49.6-129.6 115.2h257.6c-14.4-65.6-65.6-115.2-128-115.2z" fill="#FFFFFF" p-id="3905"></path><path d="M164.8 406.4c-81.632 0-149.792 66.464-162.512 153.28C1.04 568.16 0.144 576.768 0 585.6h328c-1.6-99.2-73.6-179.2-163.2-179.2z m0 32c62.4 0 113.6 49.6 128 115.2H35.2c14.4-65.6 67.2-115.2 129.6-115.2z" fill="" p-id="3906"></path><path d="M859.2 280m-72 0a72 72 0 1 0 144 0 72 72 0 1 0-144 0Z" fill="#FFFFFF" p-id="3907"></path><path d="M755.2 280c0 57.6 46.4 104 104 104s104-46.4 104-104c0-3.84-0.24-7.632-0.64-11.36-3.744-34.752-24.464-64.288-53.76-80.144C894.064 180.528 877.184 176 859.2 176c-57.6 0-104 46.4-104 104z m104 72c-40 0-72-32-72-72s32-72 72-72 72 32 72 72-32 72-72 72z" fill="" p-id="3908"></path><path d="M860 435.648c-0.288 0-0.56-0.048-0.832-0.048-63.744 0-119.104 52-134.208 120.784h268.416a180.48 180.48 0 0 0-3.264-12.64c-18.48-62.112-69.136-107.68-130.112-108.096z" fill="#FFFFFF" p-id="3909"></path><path d="M1021.712 559.68c-12.72-86.816-80.88-153.28-162.512-153.28-89.6 0-163.2 80-164.8 179.2H1024c-0.144-8.832-1.04-17.44-2.288-25.92z m-295.088-3.28h-1.664c15.104-68.784 70.464-120.784 134.208-120.784 0.288 0 0.56 0.048 0.832 0.048 60.976 0.416 111.632 45.984 130.112 108.112a180.48 180.48 0 0 1 3.264 12.64H726.624v-0.016z" fill="" p-id="3910"></path></svg>            </div>         <div style="font-size: 12px;font-weight: bold;">                    团队昨日人数：<span style="font-size: 15px; font-weight: bold; margin: 0 3px;" class="teamnum">-</span>人<br> 团队昨日总收入：<span style="font-size: 15px; font-weight: bold; margin: 0 3px;" class="teamincome">-</span>元<br> 直属下级人数：<span style="font-size: 15px; font-weight: bold; margin: 0 3px;" class="invitenum">-</span>人<br> 直属昨日一键任务佣金：<span style="font-size: 15px; font-weight: bold; margin: 0 3px;" class="agentbork">-</span>元                </div>          <span style="color: #2a2b2c;font-weight: bold;display:inline-block;margin-top: 3px;">直属日工资：<span style="font-size: 12px;margin: 0 2px;" class="agent_salary">-</span><br><p style="margin-top: 3px;margin-bottom: 0;">合伙人日工资：<span style="font-size: 12px;margin: 0 2px;" class="team_salary">-</span></p></span>       <div style="font-size: 22px;color: #2a2b2c;/* margin-top: 10px; */"><span style="font-size: 13px; background: red; color: yellow; padding: 4px 10px; border-radius: 8px; position: relative; top: -2px; margin-right: 7px; font-weight: bold;">可领日工资</span><span style="margin: 0 2px; font-weight: bold;" class="total_salary">-</span>                     </div>                           </div>                    </div>');
                            html_obj.find('.teamnum').html(cdata.teamnum);
                            html_obj.find('.teamincome').html(cdata.teamincome);
                            html_obj.find('.invitenum').html(cdata.invitenum);
                            html_obj.find('.agentbork').html(cdata.agentbork);
                            html_obj.find('.agent_salary').html(cdata.agent_salary);
                            html_obj.find('.team_salary').html(cdata.team_salary);
                            html_obj.find('.total_salary').html(cdata.total_salary);


                            show_text = html_obj[0].outerHTML;



                        }


                    }




                    if (show_text == "[当前版本不支持]") {
                        break;
                    }





                    if (pms.ta == 1) {


                        $('#chat_box').append('<div msgid="' + pms.msgid + '" style="display: flex; margin-bottom: 18px;">                <div>                    <img src="' + (pms.avatar == '' ? 'static/images/avatar-1.jpg' : pms.avatar) + '"  onclick="atuser(\'' + pms.uuid + '\',\'' + pms.name + '\')" width="50" height="50" style="border-radius: 3px;">                </div>                <div style="margin-left: 10px;text-align: left;">                    <div style="font-size: 12px; color: gray; margin-bottom: 5px;">' + (pms.type == "group" ? pms.name + " " : "") + pms.create_time + actionHtml + '</div>                                        ' + show_text + '                                                </div>            </div>');


                    } else {

                        $('#chat_box').append('<div msgid="' + pms.msgid + '" style="display: flex; margin-bottom: 18px;">                <div style="margin-left: auto;text-align: right;">                    <div style="font-size: 12px; color: gray; margin-bottom: 5px; text-align: right;">' + pms.create_time + actionHtml + '</div>                                          ' + show_text + '                                                 </div>                <div style="margin-left: 10px;">                    <img src="' + (pms.avatar == '' ? 'static/images/avatar-2.jpg' : pms.avatar) + '" width="50" height="50" style="border-radius: 3px;">                </div>            </div>');
                    }

                    break;
                default:
                    break;

            }

            if (!isLast) {
                if (pms.ta == 1) {
                    to_chatBottom();
                } else {
                    to_chatBottom(true);
                }
            }


            $('[uuid="' + uuid + '"]').html('你');

            reply_create();
        }


        // 获取指定的元素
        var chatElement = document.getElementById("chat_box"); // 替换为你的元素

        // 添加滚动事件监听器
        chatElement.addEventListener("scroll", function () {
            // 在滚动事件触发时执行你的操作
            console.log("发生了滑动事件1");

            if ($('[chat="' + chatData.chatid + '"]').length > 0) {

                var unread_number = $('[chat="' + chatData.chatid + '"]').find(".unread_number").html()
                if (unread_number != "0") {
                    //console.log('滑动【数量更新】');
                    update_message_number(parseInt($('[chat="' + chatData.chatid + '"]').find(".unread_number").html()) * -1);
                    $('[chat="' + chatData.chatid + '"]').find(".unread_number").html('0').hide();

                    //console.log("api_read");



                    v3api("read_message", {
                        data: {
                            from_type: chatData.type,
                            chatid: chatData.id
                        }
                    }, function (e) {
                        console.log('read_message', e);
                        if (e.msg == "check") {
                            writeToLocalStorage("chat_" + chatData.id, e.total_number);
                        }
                    })




                }

            }

            var element = this;
            if (element.scrollTop === 0) {
                msgid = $(this).find("[msgid]").eq(0).attr('msgid');
                console.log('msgid', msgid);

                get_message_later(msgid);
            }

        });

        var get_message_state = 0;
        var get_message_later = function (msgid) {
            if (get_message_state == 1) {
                return;
            }
            if (chatData.type == 'group') {
                return;
            }
            get_message_state = 1;

            //加载更多消息
            v3api("get_message_later", {
                error: 1,
                data: {
                    from_type: chatData.type,
                    chatid: chatData.id,
                    msgid: msgid
                }
            }, function (e) {
                get_message_state = 0;
                if (e.code != 1) {
                    return;
                }
                console.log('chat_message', chatData.type, e);


                //e.list.reverse();


                // 记录当前的滚动高度
                var element = document.getElementById('chat_box');
                var currentScrollHeight = element.scrollHeight;

                for (var i = 0; i < e.list.length; i++) {
                    var object = e.list[i];

                    new_windows_msg({
                        last: true,
                        type: object.msgtype,
                        object: object
                    })

                }


                // 计算添加新元素后的滚动高度差
                var newScrollHeight = element.scrollHeight;
                var scrollDifference = newScrollHeight - currentScrollHeight;


                console.log('data', currentScrollHeight, scrollDifference);
                // 恢复滚动位置
                $('#chat_box').scrollTop(scrollDifference);


            })
        }

        var get_chat = function (cid, userid, name, user_uuid) {
            var array = cid.replace(/@/g, "").split('_');
            chatData.type = array[0];
            chatData.id = array[1];
            chatData.chatid = cid;
            chatData.userid = userid;
            chatData.user_uuid = user_uuid;

            if ($('[chat="' + cid + '"]').length > 0) {
                //console.log('get_chat【数量更新】', cid);
                update_message_number(parseInt($('[chat="' + cid + '"]').find(".unread_number").html()) * -1);
                $('[chat="' + cid + '"]').find(".unread_number").html('0').hide();
            }

            console.log('cid,userid,name,user_uuid', cid, userid, name, user_uuid);
            if (!name) {
                console.log('遍历用户名');
                chatData.name = '';
                for (var i = 0; i < chatData.list.length; i++) {
                    var object = chatData.list[i];
                    if (object.chatid == chatData.id && object.type == chatData.type) {
                        chatData.name = object.name;
                        break;
                    }
                }
            } else {
                console.log('指定名');
                chatData.name = name;
            }

            if (chatData.name == "-1") {
                chatData.name = "福利官" + '<svg t="" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="" width="22" height="22" style="position:relative;top: 4px;margin-left: 5px;"><path d="M956.672 459.776L862.72 397.568c-16.64-11.008-24.832-30.976-20.992-50.688l22.272-110.592c4.096-20.736-2.304-42.24-17.152-57.344-15.104-15.104-36.352-21.504-57.344-17.152l-110.592 22.272c-19.712 4.096-39.424-4.352-50.688-20.992L565.76 69.12c-11.776-17.664-31.488-28.16-52.736-28.16s-40.96 10.496-52.736 28.16l-62.464 93.952c-11.008 16.64-30.976 24.832-50.688 20.992l-110.592-22.272c-20.736-4.096-42.24 2.304-57.344 17.152-15.104 15.104-21.504 36.352-17.152 57.344l22.272 110.592c3.84 19.712-4.352 39.424-20.992 50.688l-93.952 62.464c-17.664 11.776-28.16 31.488-28.16 52.736s10.496 40.96 28.16 52.736l93.952 62.464c16.64 11.008 24.832 30.976 20.992 50.688l-22.272 110.592c-4.096 20.736 2.304 42.24 17.152 57.344 15.104 15.104 36.352 21.504 57.344 17.152l110.592-22.272c19.712-4.096 39.424 4.352 50.688 20.992l62.464 93.952c11.776 17.664 31.488 28.16 52.736 28.16s40.96-10.496 52.736-28.16l62.464-93.952c11.008-16.64 30.976-24.832 50.688-20.992l110.592 22.272c20.736 4.096 42.24-2.304 57.344-17.152 15.104-15.104 21.504-36.352 17.152-57.344l-22.272-110.592c-3.84-19.712 4.352-39.424 20.992-50.688l93.952-62.464c17.664-11.776 28.16-31.488 28.16-52.736s-10.496-41.216-28.16-52.992z m-249.344-22.016l-211.456 215.04c-5.888 6.144-13.824 9.216-22.016 9.216-7.168 0-14.592-2.56-20.224-7.68l-138.24-122.112c-12.8-11.264-13.824-30.72-2.816-43.264 11.264-12.8 30.72-13.824 43.264-2.816l116.48 102.912 190.976-194.304c11.776-12.032 31.232-12.288 43.52-0.256 12.288 11.52 12.288 30.976 0.512 43.264z" fill="#5396FF" p-id="3602"></path></svg>';
            }

            $('#contact_username').html(chatData.name + xhtml.replace(/{uuid}/g, chatData.user_uuid));
            $('#nospeak').hide();
            $('#message_sendbox').show();

            $('#chat_box').show();
            $('#allsend_box').hide();

            v3api("get_message", {
                data: {
                    from_type: chatData.type,
                    chatid: chatData.id
                }
            }, function (e) {
                console.log('chat_message', chatData.type, e);


                chatData.group_avatar = '';
                chatData.group_name = '';
                if (chatData.type == 'group') {

                    if (e.poll_text != "") {
                        $('#redbag_poll_text').html(e.poll_text);
                    }


                    chatData.group_avatar = e.group_avatar;
                    chatData.group_name = e.group_name;
                    if (chatData.group_avatar == '') {
                        chatData.group_avatar = 'static/images/avatar-1.jpg';
                    }
                }

                if (chatData.type == 'newchat' || chatData.type == 'fw') {


                    chatData.list.push({
                        type: e.chatid.split('_')[0].replace(/@/g, ""),
                        chatid: e.chatid.split('_')[1],
                        name: userid,
                    })

                    get_chat(e.chatid, e.userid, userid, chatData.user_uuid);
                    return;
                }

                $('#chat_box').html('');

                if (anonyVisitor) {
                    //$('#anonyNoSpeak').show();
                    //$('#nospeak').hide();
                    //$('#message_sendbox').hide();

                    $('#luckwheel').hide();
                    $('#sendimg').show().siblings().hide();

                } else {
                    if (e.chat_nospeak == 1) {
                        $('#nospeak').show();
                        $('#message_sendbox').hide();
                    }

                }


                e.list.reverse();

                for (var i = 0; i < e.list.length; i++) {
                    var object = e.list[i];


                    new_windows_msg({
                        type: object.msgtype,
                        object: object
                    })




                }

                $('#get_pool_redbag').hide();
                $('#redbag_poll').hide();
                $('#other_list').hide();
                $('#emoji_list').hide();

                chatData.group_avatar = '';
                chatData.group_name = '';
                if (chatData.type == 'group') {
                    chatData.group_avatar = e.group_avatar;
                    chatData.group_name = e.group_name;
                    $('.group_item').show();
                }

                show_page('#chat_message');

                to_chatBottom();


            })

        }

        function getFormattedCSTDate() {
            var now = new Date();
            var offset = 8; // 北京时间与UTC的时差为+8小时
            var utc = now.getTime() + now.getTimezoneOffset() * 60000;
            var cst = new Date(utc + (3600000 * offset));

            var year = cst.getFullYear();
            var month = String(cst.getMonth() + 1).padStart(2, '0'); // 月份从0开始，需要加1
            var day = String(cst.getDate()).padStart(2, '0');
            var hours = String(cst.getHours()).padStart(2, '0');
            var minutes = String(cst.getMinutes()).padStart(2, '0');
            var seconds = String(cst.getSeconds()).padStart(2, '0');

            //return year + '-' + month + '-' + day + ' ' + hours + ':' + minutes + ':' + seconds;
            return hours + ':' + minutes;
        }

        var formattedCSTDate = getFormattedCSTDate();


        var __replyid = '';
        var __sendText = '';
        var __msgtype = '';
        var __sourceText = '';
        var sendUserList = [];

        $('#send_button').on('click', function () {
            var replyid = $('#reply_data').attr('replyid');
            if (typeof (replyid) == 'undefined') {
                replyid = '';
            }
            close_reply();


            var msg_text = $('#send_message').val();
            __sourceText = msg_text;
            //@消息处理
            for (var k in atList) {
                console.log('k', k, atList[k]);
                try {
                    msg_text = msg_text.replace(new RegExp('\@' + k + ' ', 'g'), '@' + atList[k] + ' ');
                } catch (e) {

                }
            }

            __msgtype = 'text';
            __replyid = replyid;
            __sendText = msg_text;



            //show_page('#allsend_message');

            if (chatData.type == 'allsend') {
                if ($('#send_button').hasClass('unsend')) {
                    return;
                }
                $('#send_button').addClass('unsend');
                $('#send_button').css('background', '#ddd');
                for (var i = 0; i < allsend_list.length; i++) {
                    sendUserList.push(allsend_list[i].uuid);
                }
                allsend_poll();
            } else {
                send_text_chat();

            }

        })


        var allsend_poll = function () {
            console.log('sendUserList', sendUserList);
            $('#send_button').text((allsend_list.length - sendUserList.length) + '/' + allsend_list.length);
            if (sendUserList.length == 0) {
                $('#send_button').text('发送');
                $('#send_button').css('background', 'rgb(7, 192, 98)');
                $('#send_button').removeClass('unsend');

                tp('消息群发已完成')
                return;
            }
            var uuid = sendUserList.shift();
            chatData.userid = uuid;
            send_text_chat();

            setTimeout(function () {
                allsend_poll();
            }, 200)
        }

        var send_text_chat = function () {
            var __data = {
                from_type: chatData.type,
                chatid: chatData.id,
                touser: chatData.userid,
                replyid: __replyid,
                msgtype: __msgtype,
                msg_text: __sendText //$('#send_message').html().replace(/<br>/g, "\n")
            };

            if (chatData.type == 'allsend') {
                __data = {
                    from_type: 'friend',
                    allsendId: chatData.userid,
                    msgtype: __msgtype,
                    msg_text: __sendText //$('#send_message').html().replace(/<br>/g, "\n")
                };
            }

            v3api("send_message", {
                data: __data
            }, function (e) {
                $('#send_message').val('');
                $('#send_message').css('height', '33px');

                if (chatData.type == 'allsend') {
                    return;
                }

                new_windows_msg({
                    type: 'text',
                    object: {
                        userid: e.userid,
                        msgid: e.msgid,
                        text: __sourceText,//$('#send_message').html().replace(/<br>/g, "\n"),
                        ta: 0,
                        create_time: getFormattedCSTDate(),
                        avatar: '<%=uConfig.gd(userdt,"avatar") %>',
                        pm2: (__replyid != "" ? 'wait_update' : '')
                    }

                })


                //$('#send_message').html('');


                text_change();
                to_chatBottom();

            })
        }


        var text_change = function () {
            if ($('#send_message').val() != "") {
                $('#other_button').hide();
                $('#send_button').show();
            } else {
                $('#send_button').hide();
                $('#other_button').show();
            }
        }

        $('#send_message').on("keyup", function () {
            text_change()
        })
    </script>
    <style>
        .ppreturn {
            display: none!Important;
        }

        .top-title .pptitle {
            font-size: 12px;
            font-weight: 500;
        }

        .top-title {
            text-align: left;
            padding-top: 5px;
            height: 38px!important;
        }


        input, textarea {
            outline: none;
        }
    </style>


    <!--底部菜单 -->
    <div id="index_menu" class="base_page">


        <div class="wxlist">


            <div class="icon_item" onclick="location.href='index.aspx'">
                <div>
                    <svg t="" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="" width="22" height="22">
                        <path d="M675.328 117.717333A425.429333 425.429333 0 0 0 512 85.333333C276.352 85.333333 85.333333 276.352 85.333333 512s191.018667 426.666667 426.666667 426.666667 426.666667-191.018667 426.666667-426.666667c0-56.746667-11.093333-112-32.384-163.328a21.333333 21.333333 0 0 0-39.402667 16.341333A382.762667 382.762667 0 0 1 896 512c0 212.074667-171.925333 384-384 384S128 724.074667 128 512 299.925333 128 512 128c51.114667 0 100.8 9.984 146.986667 29.12a21.333333 21.333333 0 0 0 16.341333-39.402667z m-324.693333 373.013334l174.464-174.485334a21.141333 21.141333 0 0 0-0.192-29.973333 21.141333 21.141333 0 0 0-29.973334-0.192l-208.256 208.256a20.821333 20.821333 0 0 0-6.122666 14.976c0.042667 5.418667 2.133333 10.837333 6.314666 14.997333l211.178667 211.2a21.141333 21.141333 0 0 0 29.973333 0.213334 21.141333 21.141333 0 0 0-0.213333-29.973334l-172.629333-172.629333 374.997333 2.602667a20.693333 20.693333 0 0 0 21.034667-21.034667 21.482667 21.482667 0 0 0-21.333334-21.333333l-379.242666-2.624z" fill="#333C4F" p-id="27768"></path></svg>
                </div>
                <span>首页</span>
            </div>
            
            
            <div class="icon_item default_color" onclick="location.href='dating.aspx'">
                    <div>
                        <svg t="" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="1678" width="22" height="22">
<path class="light_point" d="M700.16 661.333333m-181.333333 0a181.333333 181.333333 0 1 0 362.666666 0 181.333333 181.333333 0 1 0-362.666666 0Z" fill="#FFCA5F" p-id="1679"></path><path d="M158.506667 232.106667m-42.666667 0a42.666667 42.666667 0 1 0 85.333333 0 42.666667 42.666667 0 1 0-85.333333 0Z" fill="#FFCA5F" p-id="1680"></path><path d="M518.826667 654.08a152.746667 152.746667 0 1 1 152.746666-152.746667 152.96 152.96 0 0 1-152.746666 152.746667z m0-241.493333a88.746667 88.746667 0 1 0 88.746666 88.746666 88.746667 88.746667 0 0 0-88.746666-88.746666z" fill="#5C1CF7" p-id="1681"></path><path d="M310.186667 533.333333H157.866667v-32a360.96 360.96 0 0 1 700.373333-122.666666l-60.16 21.333333A296.96 296.96 0 0 0 223.573333 469.333333h86.613334zM518.826667 862.08A362.666667 362.666667 0 0 1 179.413333 624.213333l60.16-21.333333A296.96 296.96 0 0 0 813.866667 533.333333h-86.613334v-64h152.32v32a361.173333 361.173333 0 0 1-360.746666 360.746667z" fill="#5C1CF7" p-id="1682"></path></svg>
                    </div>
                   <span>买币大厅</span>
            </div>
            <div class="icon_item default_color" onclick="location.href='order_new.aspx'">
                        <div>
                        <svg t="" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="6390" width="22" height="22">
<path class="light_point" d="M636.373333 663.68m-202.666666 0a202.666667 202.666667 0 1 0 405.333333 0 202.666667 202.666667 0 1 0-405.333333 0Z" fill="#FFCA5F" p-id="6391"></path>
<path d="M232.533333 222.506667m-47.786666 0a47.786667 47.786667 0 1 0 95.573333 0 47.786667 47.786667 0 1 0-95.573333 0Z" fill="#FFCA5F" p-id="6392"></path><path d="M789.333333 740.053333H215.466667a46.933333 46.933333 0 0 1-37.333334-75.946666 181.333333 181.333333 0 0 0 38.186667-111.36V411.733333a289.066667 289.066667 0 0 1 11.093333-78.933333l61.44 17.706667a226.986667 226.986667 0 0 0-8.533333 61.226666v141.013334a245.12 245.12 0 0 1-33.28 123.306666h510.72A247.466667 247.466667 0 0 1 725.333333 552.746667V411.733333a222.08 222.08 0 0 0-370.133333-165.546666l-42.666667-47.573334A286.08 286.08 0 0 1 789.333333 411.733333v141.013334a182.826667 182.826667 0 0 0 38.186667 111.36A46.933333 46.933333 0 0 1 789.333333 740.053333zM502.4 881.28a102.613333 102.613333 0 0 1-102.4-102.613333h64a38.613333 38.613333 0 1 0 77.013333 0h64a102.613333 102.613333 0 0 1-102.613333 102.613333z" fill="#5C1CF7" p-id="6393"></path></svg>
                   </div>
                   <span>开始搬砖</span>
            </div>
            <div class="icon_item default_color active" aid="">
                <div>
                     <svg t="" class="icon" viewBox="0 0 1601 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="31008" width="22" height="22"><path d="M186.6945686 138.23074871s3.51008995-0.36948349 5.35750608 1.84741491c1.84741615 2.40164076 4.24905691 34.91616207 12.7471707 43.78375935 8.49811381 8.86759729 43.04479239 3.87957344 43.04479238 15.51829492 0 9.23707953-31.96029671 10.34552996-42.86005123 19.21312604-10.89975455 8.86759729-4.61854037 46.55488355-17.91993449 45.07695087-10.34552996-1.10844917-3.1406077-31.03658864-15.88777838-43.04479361-12.7471707-12.00820375-43.78375933-9.60656301-43.78375812-20.50631755 0-10.89975455 31.77555558-4.24905691 42.6753089-16.62674414 10.7150122-12.19294611 6.09647306-45.26169202 16.62674416-45.26169079z" fill="#FFBB5A" p-id="31009"></path><path d="M1350.56664679-124.47180614s2.40164076-0.18474111 3.69483107 1.29319035c1.29319156 1.47793268 2.95586533 23.4621829 8.68285615 29.55865594 5.72698959 6.09647306 29.00443136 2.58638188 29.00443015 10.53027108 0 6.28121418-21.61476676 7.02018113-29.00443015 12.93191306-7.38966338 6.09647306-3.1406077 31.40607213-12.1929461 30.48236405-7.02018113-0.73896697-2.03215729-20.87580105-10.71501222-29.00443136-8.68285493-8.12863033-29.55865596-6.46595652-29.55865593-13.85561994s21.43002563-2.95586533 28.81968899-11.269238 4.24905691-30.66710516 11.26923804-30.66710518z" fill="#E94151" p-id="31010"></path><path d="M1531.61341363 278.44962278s3.51008995-0.36948349 5.54224844 2.03215729 4.24905691 35.47038666 12.93191184 44.52272508c8.68285493 9.0523384 43.599017 3.87957344 43.59901704 15.70303605 0 9.42182189-32.51452133 10.53027107-43.59901704 19.58260947-11.08449569 9.0523384-4.80328148 47.29384929-18.28941795 45.81591663-10.53027107-1.10844917-3.1406077-31.40607213-16.0725195-43.59901702-12.93191183-12.19294611-44.52272507-9.79130535-44.52272508-20.87580101 0-11.08449569 32.3297802-4.24905691 43.22953352-16.99622761 11.26923805-12.7471707 6.65069766-46.1854001 17.18096873-46.18539888z" fill="#FF0000" p-id="31011"></path><path d="M367.0023697-124.47180614z m-36.57883709 0c0 20.13683407 16.44200178 36.57883709 36.57883708 36.57883587s36.57883709-16.44200178 36.5788371-36.57883587S387.13920378-161.05064323 367.0023697-161.05064323c-20.32157643 0-36.57883709 16.44200178-36.57883709 36.57883709M1351.12087139 201.04289173z m-36.02461248 0c0 19.95209295 16.25726065 36.0246125 36.02461248 36.02461246 19.95209295 0 36.20935361-16.25726065 36.20935361-36.02461246 0-19.95209295-16.25726065-36.20935361-36.20935361-36.20935366-19.95209295 0.18474111-36.0246125 16.25726065-36.02461248 36.20935366" fill="#FFEB00" p-id="31012"></path><path d="M349.08243523 179.24338381c0 139.11042487 207.46481633 251.80280027 463.33193117 251.8028003s463.33193237-112.6923754 463.33193241-251.8028003S1068.4662236-72.37467533 812.59910876-72.37467533C556.54725159-72.37467533 349.08243523 40.31770005 349.08243523 179.24338381z" fill="#DB1818" p-id="31013"></path><path d="M1065.8798417 222.47291735z m-168.11485624 0c0 92.92502477 75.37457259 168.11485625 168.11485624 168.11485624 92.92502477 0 168.11485625-75.37457259 168.11485502-168.11485624s-75.37457259-168.11485625-168.11485502-168.11485502c-92.92502477 0-168.11485625 75.18983146-168.11485624 168.11485502" fill="#F4AD51" p-id="31014"></path><path d="M1065.8798417 222.47291735z m-140.77309987 0c0 50.24971466 26.78753175 96.80459822 70.38654995 121.92945615 43.599017 25.12485795 97.1740817 25.12485795 140.77309865 0s70.38654996-71.67974029 70.38654996-121.92945615c0-50.24971466-26.78753175-96.80459822-70.38654996-121.92945493a140.87840167 140.87840167 0 0 0-140.77309865 0c-43.599017 25.12485795-70.38654996 71.67974029-70.38654995 121.92945493" fill="#F4E476" p-id="31015"></path><path d="M1121.85654595 240.94707762h-41.9363432v-29.1891725h41.9363432c7.75914687 0 13.85561991-6.28121418 13.8556199-13.8556199 0-7.75914687-6.28121418-13.85561991-13.8556199-13.85561989h-24.01640754l15.3335526-26.4180495c3.69483231-6.65069766 1.29319156-14.96406911-5.17276496-18.65890138s-14.96406911-1.6626738-18.84364378 4.80328147L1065.8798417 183.4924395l-22.90795831-39.90418468c-3.87957344-6.46595652-12.37768722-8.68285493-18.84364376-4.80328151-6.46595652 3.69483231-8.86759729 12.00820375-5.17276495 18.65890141l15.14881143 26.41804825h-24.01640747c-7.75914687 0-13.85561991 6.28121418-13.85561995 13.85561994 0 7.75914687 6.28121418 13.85561991 13.85561995 13.85561987h41.93634319v29.1891737h-41.93634319c-7.75914687 0-13.85561991 6.28121418-13.85561995 13.85561991 0 7.75914687 6.28121418 13.85561991 13.85561995 13.85561989h41.93634319v25.49434022c0 7.75914687 6.28121418 13.85561991 13.85561987 13.85561989 7.75914687 0 13.85561991-6.28121418 13.85561993-13.85561989v-25.49434022h41.93634319c4.98802263 0 9.60656301-2.58638188 12.00820374-7.02018106 2.40164076-4.24905691 2.40164076-9.60656301 0-13.85561995-2.40164076-3.87957344-6.83543878-6.65069766-11.82346261-6.65069765zM559.13363345 222.47291735z m-168.11485622 0c0 92.92502477 75.37457259 168.11485625 168.11485622 168.11485624 92.92502477 0 168.11485625-75.37457259 168.11485624-168.11485624s-75.37457259-168.11485625-168.11485624-168.11485502-168.11485625 75.18983146-168.11485622 168.11485502" fill="#F4AD51" p-id="31016"></path><path d="M559.13363345 222.47291735z m-140.77309869 0c0 50.24971466 26.78753175 96.80459822 70.38654874 121.92945615 43.599017 25.12485795 97.1740817 25.12485795 140.77309992 0s70.38654996-71.67974029 70.38654873-121.92945615c0-77.77621333-62.99688537-140.77309869-140.7730987-140.7730987s-140.77309869 62.99688537-140.77309866 140.7730987" fill="#F4E476" p-id="31017"></path><path d="M615.11033767 240.94707762h-41.93634316v-29.1891725H615.11033767c7.75914687 0 13.85561991-6.28121418 13.85561992-13.8556199 0-7.75914687-6.28121418-13.85561991-13.85561992-13.85561989h-24.01640751l15.3335526-26.4180495c3.69483231-6.65069766 1.29319156-14.96406911-5.17276498-18.65890138s-14.96406911-1.6626738-18.84364255 4.80328147l-22.9079583 39.90418592-22.9079583-39.90418592c-3.87957344-6.46595652-12.37768722-8.68285493-18.84364377-4.80328147-6.46595652 3.69483231-8.86759729 12.00820375-5.17276498 18.65890138l15.3335538 26.4180495h-24.01640871c-7.75914687 0-13.85561991 6.28121418-13.85561991 13.85561989 0 7.75914687 6.28121418 13.85561991 13.85561991 13.8556199h41.9363432v29.1891725h-41.9363432c-7.75914687 0-13.85561991 6.28121418-13.85561991 13.85561992 0 7.75914687 6.28121418 13.85561991 13.8556199 13.85561989h41.93634321v25.49434139c0 7.75914687 6.28121418 13.85561991 13.85561988 13.85561993 7.75914687 0 13.85561991-6.28121418 13.85561991-13.85561993v-25.49434139H615.11033767c4.98802263 0 9.60656301-2.58638188 12.00820499-7.02018113 2.40164076-4.24905691 2.40164076-9.60656301 0-13.85561989-2.40164076-4.06431575-7.02018113-6.83543878-12.00820499-6.83543879z" fill="#F4AD51" p-id="31018"></path><path d="M573.35873686 151.53214278c0 132.09024375 107.15012817 239.0556296 239.2403719 239.05563084s239.24037191-106.9653858 239.2403707-239.05563084-107.15012817-239.0556296-239.24037071-239.05562955-239.24037191 106.9653858-239.24037189 239.05562955z" fill="#FFBB5A" p-id="31019"></path><path d="M812.59910876 151.53214278z m-200.25989409 0c0 71.49499917 38.24151088 137.6324922 100.12994705 173.28762122 61.88843615 35.83987014 138.18671682 35.83987014 200.25989284 0 61.88843615-35.83987014 100.12994706-101.79262084 100.12994708-173.28762122 0-110.475477-89.59967595-200.25989408-200.25989289-200.25989287-110.6602181 0-200.25989408 89.59967595-200.25989408 200.25989287" fill="#FFF48D" p-id="31020"></path><path d="M892.03799591 177.95019226h-59.67153653V136.38333254h59.67153653c10.89975455 0 19.7673506-8.86759729 19.7673518-19.76735181 0-10.89975455-8.86759729-19.7673506-19.7673518-19.76735062h-34.17719637l21.61476798-37.50254517c4.98802263-9.42182189 1.6626738-21.06054215-7.5744057-26.41804827-9.23707953-5.3575061-21.06054215-2.40164076-26.6027906 6.65069768l-32.69926246 56.71567116-32.69926368-56.71567116c-3.51008995-6.09647306-9.97604647-9.97604647-17.1809687-9.97604651-7.02018113 0-13.67087877 3.69483231-17.18096876 9.97604651-3.51008995 6.09647306-3.51008995 13.67087877 0 19.76735059l21.61476675 37.50254517h-34.17719509c-10.89975455 0-19.7673506 8.86759729-19.76735183 19.76735062 0 10.89975455 8.86759729 19.7673506 19.76735183 19.76735181h59.67153651v41.56685972h-59.67153651c-10.89975455 0-19.7673506 8.86759729-19.76735183 19.76735065 0 10.89975455 8.86759729 19.7673506 19.76735183 19.7673518h59.67153651v36.2093536c0 10.89975455 8.86759729 19.7673506 19.76735061 19.76735063 10.89975455 0 19.7673506-8.86759729 19.76735183-19.76735063v-36.2093536h59.67153656c7.02018113 0 13.67087877-3.69483231 17.1809687-9.97604648 3.51008995-6.09647306 3.51008995-13.67087877 0-19.76735181-3.32534883-6.09647306-9.97604647-9.79130535-16.99622758-9.79130416z" fill="#FFBB5A" p-id="31021"></path><path d="M813.70755795 270.13625013c-188.6211738 0-356.18180422-41.93634318-463.33193237-106.96538584v824.50175926c0 51.91238966 42.12108432 94.03347399 94.03347401 94.03347396h738.59691555c51.91238966 0 94.03347399-42.12108432 94.03347397-94.03347396V162.98612194c-107.15012817 65.21378498-274.71075858 107.15012817-463.33193116 107.15012819z" fill="#FF4545" p-id="31022"></path><path d="M760.31723561 800.34464127h-117.12617465v-52.28187313h117.12617465v-36.39409473h-117.12617465v-52.28187316h94.77244095l-140.77309868-224.64578509h127.47170336l46.1854001 101.9773632c23.09269943 50.43445699 28.45020675 63.7358523 43.04479239 95.88089016h3.6948323c15.14881146-32.14503783 21.7995091-48.03281626 43.0447924-95.88089016l44.89220855-101.9773632h125.07006383l-140.77309989 224.64578509H984.59353723v52.28187313h-116.01772426v36.39409476H984.59353723v52.28187313h-116.01772426v88.67596788h-108.07383624v-88.67596788z" fill="#FFFFFF" p-id="31023"></path></svg>
                </div>
                <span style="position: relative;
    color: red;
    font-weight: bold;
">抢红包
                    <div style="background: rgba(251, 24, 24, 0.87); color: rgb(255, 255, 255); width: 21px; height: 18px; line-height: 18px; position: absolute; border-radius: 50%; text-align: center; top: -29px; right: -10px; font-size: 12px; display: none;" id="chat_unread_number">0</div>
                </span>

            </div>
            <%--<div class="icon_item default_color" aid="game">
                <div>
                    <svg t="" class="icon" viewBox="0 0 1601 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="31008" width="22" height="22"><path d="M186.6945686 138.23074871s3.51008995-0.36948349 5.35750608 1.84741491c1.84741615 2.40164076 4.24905691 34.91616207 12.7471707 43.78375935 8.49811381 8.86759729 43.04479239 3.87957344 43.04479238 15.51829492 0 9.23707953-31.96029671 10.34552996-42.86005123 19.21312604-10.89975455 8.86759729-4.61854037 46.55488355-17.91993449 45.07695087-10.34552996-1.10844917-3.1406077-31.03658864-15.88777838-43.04479361-12.7471707-12.00820375-43.78375933-9.60656301-43.78375812-20.50631755 0-10.89975455 31.77555558-4.24905691 42.6753089-16.62674414 10.7150122-12.19294611 6.09647306-45.26169202 16.62674416-45.26169079z" fill="#FFBB5A" p-id="31009"></path><path d="M1350.56664679-124.47180614s2.40164076-0.18474111 3.69483107 1.29319035c1.29319156 1.47793268 2.95586533 23.4621829 8.68285615 29.55865594 5.72698959 6.09647306 29.00443136 2.58638188 29.00443015 10.53027108 0 6.28121418-21.61476676 7.02018113-29.00443015 12.93191306-7.38966338 6.09647306-3.1406077 31.40607213-12.1929461 30.48236405-7.02018113-0.73896697-2.03215729-20.87580105-10.71501222-29.00443136-8.68285493-8.12863033-29.55865596-6.46595652-29.55865593-13.85561994s21.43002563-2.95586533 28.81968899-11.269238 4.24905691-30.66710516 11.26923804-30.66710518z" fill="#E94151" p-id="31010"></path><path d="M1531.61341363 278.44962278s3.51008995-0.36948349 5.54224844 2.03215729 4.24905691 35.47038666 12.93191184 44.52272508c8.68285493 9.0523384 43.599017 3.87957344 43.59901704 15.70303605 0 9.42182189-32.51452133 10.53027107-43.59901704 19.58260947-11.08449569 9.0523384-4.80328148 47.29384929-18.28941795 45.81591663-10.53027107-1.10844917-3.1406077-31.40607213-16.0725195-43.59901702-12.93191183-12.19294611-44.52272507-9.79130535-44.52272508-20.87580101 0-11.08449569 32.3297802-4.24905691 43.22953352-16.99622761 11.26923805-12.7471707 6.65069766-46.1854001 17.18096873-46.18539888z" fill="#FF0000" p-id="31011"></path><path d="M367.0023697-124.47180614z m-36.57883709 0c0 20.13683407 16.44200178 36.57883709 36.57883708 36.57883587s36.57883709-16.44200178 36.5788371-36.57883587S387.13920378-161.05064323 367.0023697-161.05064323c-20.32157643 0-36.57883709 16.44200178-36.57883709 36.57883709M1351.12087139 201.04289173z m-36.02461248 0c0 19.95209295 16.25726065 36.0246125 36.02461248 36.02461246 19.95209295 0 36.20935361-16.25726065 36.20935361-36.02461246 0-19.95209295-16.25726065-36.20935361-36.20935361-36.20935366-19.95209295 0.18474111-36.0246125 16.25726065-36.02461248 36.20935366" fill="#FFEB00" p-id="31012"></path><path d="M349.08243523 179.24338381c0 139.11042487 207.46481633 251.80280027 463.33193117 251.8028003s463.33193237-112.6923754 463.33193241-251.8028003S1068.4662236-72.37467533 812.59910876-72.37467533C556.54725159-72.37467533 349.08243523 40.31770005 349.08243523 179.24338381z" fill="#DB1818" p-id="31013"></path><path d="M1065.8798417 222.47291735z m-168.11485624 0c0 92.92502477 75.37457259 168.11485625 168.11485624 168.11485624 92.92502477 0 168.11485625-75.37457259 168.11485502-168.11485624s-75.37457259-168.11485625-168.11485502-168.11485502c-92.92502477 0-168.11485625 75.18983146-168.11485624 168.11485502" fill="#F4AD51" p-id="31014"></path><path d="M1065.8798417 222.47291735z m-140.77309987 0c0 50.24971466 26.78753175 96.80459822 70.38654995 121.92945615 43.599017 25.12485795 97.1740817 25.12485795 140.77309865 0s70.38654996-71.67974029 70.38654996-121.92945615c0-50.24971466-26.78753175-96.80459822-70.38654996-121.92945493a140.87840167 140.87840167 0 0 0-140.77309865 0c-43.599017 25.12485795-70.38654996 71.67974029-70.38654995 121.92945493" fill="#F4E476" p-id="31015"></path><path d="M1121.85654595 240.94707762h-41.9363432v-29.1891725h41.9363432c7.75914687 0 13.85561991-6.28121418 13.8556199-13.8556199 0-7.75914687-6.28121418-13.85561991-13.8556199-13.85561989h-24.01640754l15.3335526-26.4180495c3.69483231-6.65069766 1.29319156-14.96406911-5.17276496-18.65890138s-14.96406911-1.6626738-18.84364378 4.80328147L1065.8798417 183.4924395l-22.90795831-39.90418468c-3.87957344-6.46595652-12.37768722-8.68285493-18.84364376-4.80328151-6.46595652 3.69483231-8.86759729 12.00820375-5.17276495 18.65890141l15.14881143 26.41804825h-24.01640747c-7.75914687 0-13.85561991 6.28121418-13.85561995 13.85561994 0 7.75914687 6.28121418 13.85561991 13.85561995 13.85561987h41.93634319v29.1891737h-41.93634319c-7.75914687 0-13.85561991 6.28121418-13.85561995 13.85561991 0 7.75914687 6.28121418 13.85561991 13.85561995 13.85561989h41.93634319v25.49434022c0 7.75914687 6.28121418 13.85561991 13.85561987 13.85561989 7.75914687 0 13.85561991-6.28121418 13.85561993-13.85561989v-25.49434022h41.93634319c4.98802263 0 9.60656301-2.58638188 12.00820374-7.02018106 2.40164076-4.24905691 2.40164076-9.60656301 0-13.85561995-2.40164076-3.87957344-6.83543878-6.65069766-11.82346261-6.65069765zM559.13363345 222.47291735z m-168.11485622 0c0 92.92502477 75.37457259 168.11485625 168.11485622 168.11485624 92.92502477 0 168.11485625-75.37457259 168.11485624-168.11485624s-75.37457259-168.11485625-168.11485624-168.11485502-168.11485625 75.18983146-168.11485622 168.11485502" fill="#F4AD51" p-id="31016"></path><path d="M559.13363345 222.47291735z m-140.77309869 0c0 50.24971466 26.78753175 96.80459822 70.38654874 121.92945615 43.599017 25.12485795 97.1740817 25.12485795 140.77309992 0s70.38654996-71.67974029 70.38654873-121.92945615c0-77.77621333-62.99688537-140.77309869-140.7730987-140.7730987s-140.77309869 62.99688537-140.77309866 140.7730987" fill="#F4E476" p-id="31017"></path><path d="M615.11033767 240.94707762h-41.93634316v-29.1891725H615.11033767c7.75914687 0 13.85561991-6.28121418 13.85561992-13.8556199 0-7.75914687-6.28121418-13.85561991-13.85561992-13.85561989h-24.01640751l15.3335526-26.4180495c3.69483231-6.65069766 1.29319156-14.96406911-5.17276498-18.65890138s-14.96406911-1.6626738-18.84364255 4.80328147l-22.9079583 39.90418592-22.9079583-39.90418592c-3.87957344-6.46595652-12.37768722-8.68285493-18.84364377-4.80328147-6.46595652 3.69483231-8.86759729 12.00820375-5.17276498 18.65890138l15.3335538 26.4180495h-24.01640871c-7.75914687 0-13.85561991 6.28121418-13.85561991 13.85561989 0 7.75914687 6.28121418 13.85561991 13.85561991 13.8556199h41.9363432v29.1891725h-41.9363432c-7.75914687 0-13.85561991 6.28121418-13.85561991 13.85561992 0 7.75914687 6.28121418 13.85561991 13.8556199 13.85561989h41.93634321v25.49434139c0 7.75914687 6.28121418 13.85561991 13.85561988 13.85561993 7.75914687 0 13.85561991-6.28121418 13.85561991-13.85561993v-25.49434139H615.11033767c4.98802263 0 9.60656301-2.58638188 12.00820499-7.02018113 2.40164076-4.24905691 2.40164076-9.60656301 0-13.85561989-2.40164076-4.06431575-7.02018113-6.83543878-12.00820499-6.83543879z" fill="#F4AD51" p-id="31018"></path><path d="M573.35873686 151.53214278c0 132.09024375 107.15012817 239.0556296 239.2403719 239.05563084s239.24037191-106.9653858 239.2403707-239.05563084-107.15012817-239.0556296-239.24037071-239.05562955-239.24037191 106.9653858-239.24037189 239.05562955z" fill="#FFBB5A" p-id="31019"></path><path d="M812.59910876 151.53214278z m-200.25989409 0c0 71.49499917 38.24151088 137.6324922 100.12994705 173.28762122 61.88843615 35.83987014 138.18671682 35.83987014 200.25989284 0 61.88843615-35.83987014 100.12994706-101.79262084 100.12994708-173.28762122 0-110.475477-89.59967595-200.25989408-200.25989289-200.25989287-110.6602181 0-200.25989408 89.59967595-200.25989408 200.25989287" fill="#FFF48D" p-id="31020"></path><path d="M892.03799591 177.95019226h-59.67153653V136.38333254h59.67153653c10.89975455 0 19.7673506-8.86759729 19.7673518-19.76735181 0-10.89975455-8.86759729-19.7673506-19.7673518-19.76735062h-34.17719637l21.61476798-37.50254517c4.98802263-9.42182189 1.6626738-21.06054215-7.5744057-26.41804827-9.23707953-5.3575061-21.06054215-2.40164076-26.6027906 6.65069768l-32.69926246 56.71567116-32.69926368-56.71567116c-3.51008995-6.09647306-9.97604647-9.97604647-17.1809687-9.97604651-7.02018113 0-13.67087877 3.69483231-17.18096876 9.97604651-3.51008995 6.09647306-3.51008995 13.67087877 0 19.76735059l21.61476675 37.50254517h-34.17719509c-10.89975455 0-19.7673506 8.86759729-19.76735183 19.76735062 0 10.89975455 8.86759729 19.7673506 19.76735183 19.76735181h59.67153651v41.56685972h-59.67153651c-10.89975455 0-19.7673506 8.86759729-19.76735183 19.76735065 0 10.89975455 8.86759729 19.7673506 19.76735183 19.7673518h59.67153651v36.2093536c0 10.89975455 8.86759729 19.7673506 19.76735061 19.76735063 10.89975455 0 19.7673506-8.86759729 19.76735183-19.76735063v-36.2093536h59.67153656c7.02018113 0 13.67087877-3.69483231 17.1809687-9.97604648 3.51008995-6.09647306 3.51008995-13.67087877 0-19.76735181-3.32534883-6.09647306-9.97604647-9.79130535-16.99622758-9.79130416z" fill="#FFBB5A" p-id="31021"></path><path d="M813.70755795 270.13625013c-188.6211738 0-356.18180422-41.93634318-463.33193237-106.96538584v824.50175926c0 51.91238966 42.12108432 94.03347399 94.03347401 94.03347396h738.59691555c51.91238966 0 94.03347399-42.12108432 94.03347397-94.03347396V162.98612194c-107.15012817 65.21378498-274.71075858 107.15012817-463.33193116 107.15012819z" fill="#FF4545" p-id="31022"></path><path d="M760.31723561 800.34464127h-117.12617465v-52.28187313h117.12617465v-36.39409473h-117.12617465v-52.28187316h94.77244095l-140.77309868-224.64578509h127.47170336l46.1854001 101.9773632c23.09269943 50.43445699 28.45020675 63.7358523 43.04479239 95.88089016h3.6948323c15.14881146-32.14503783 21.7995091-48.03281626 43.0447924-95.88089016l44.89220855-101.9773632h125.07006383l-140.77309989 224.64578509H984.59353723v52.28187313h-116.01772426v36.39409476H984.59353723v52.28187313h-116.01772426v88.67596788h-108.07383624v-88.67596788z" fill="#FFFFFF" p-id="31023"></path></svg>
                </div>
                        <span style="
    color: red;
    font-weight: bold;
">抢红包</span>
            </div>--%>
            <%--<div class="icon_item" aid="friend_book">
                <div>
                    <svg t="" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="" width="22" height="22" style="">
                        <path d="M512 85.333333c128.085333 0 230.656 105.6 230.656 257.28 0 58.154667-34.986667 142.122667-77.013333 203.989334-24.746667 30.805333-13.994667 57.728 4.010666 67.328 3.754667 2.133333 77.994667 43.093333 100.949334 55.808l2.432 1.365333c39.125333 21.76 67.84 37.973333 87.04 49.365333 50.645333 33.877333 74.837333 59.008 78.592 103.253334V853.333333a85.333333 85.333333 0 0 1-85.333334 85.333334H170.666667a85.333333 85.333333 0 0 1-85.333334-85.333334v-29.610666c0-41.813333 24.064-67.2 77.610667-102.954667 36.650667-22.613333 143.786667-81.706667 189.098667-105.728 18.176-10.453333 28.629333-36.138667 1.962666-70.101333-39.253333-58.069333-73.813333-143.104-73.813333-202.368C280.192 189.525333 381.866667 85.333333 512 85.333333z m0 85.333334c-84.181333 0-146.474667 67.498667-146.474667 171.946666 0 36.266667 25.173333 103.466667 57.856 152.533334 51.669333 68.693333 39.637333 154.538667-31.402666 195.328-53.077333 28.117333-153.685333 84.096-181.632 101.248-34.261333 22.869333-39.168 27.861333-39.68 31.36V853.333333h682.666666v-24.874666c-1.493333-8.490667-7.765333-14.933333-38.784-35.797334a3817.770667 3817.770667 0 0 0-82.944-46.933333l-2.432-1.365333-47.018666-26.026667a7185.066667 7185.066667 0 0 1-52.565334-29.098667c-69.461333-36.949333-84.352-125.44-33.066666-192.768 35.242667-52.608 60.8-118.869333 60.8-153.898666C657.322667 239.573333 594.048 170.666667 512 170.666667z m426.709333 384c21.845333 0 39.893333 16.341333 42.325334 37.674666L981.333333 597.333333c0 23.552-18.901333 42.666667-42.624 42.666667h-42.752A42.538667 42.538667 0 0 1 853.333333 597.333333c0-23.552 18.901333-42.666667 42.624-42.666666h42.752z m-0.426666-170.666667c22.058667 0 40.277333 16.341333 42.752 37.674667L981.333333 426.666667a42.666667 42.666667 0 0 1-43.093333 42.666666h-84.48a42.666667 42.666667 0 1 1 0-85.333333h84.48zM938.666667 213.333333c21.845333 0 39.893333 16.341333 42.325333 37.674667L981.333333 256c0 23.552-18.944 42.666667-42.666666 42.666667h-128c-23.552 0-42.666667-18.944-42.666667-42.666667 0-23.552 18.944-42.666667 42.666667-42.666667h128z" fill="#333C4F" p-id="11199"></path></svg>
                </div>
                <span style="position: relative;">通讯录
                    <div style="background: red; color: rgb(255, 255, 255); width: 18px; height: 18px; line-height: 18px; position: absolute; border-radius: 50%; text-align: center; top: -29px; right: -3px; font-size: 12px; display: none;" class="new_friend_number">0</div>
                </span>
            </div>--%>
            <div class="icon_item" onclick="location.href='account.aspx'">
                <div>
                    <svg t="" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="" width="22" height="22">
                        <path class="light_point" d="M627.2 690.56m-181.333333 0a181.333333 181.333333 0 1 0 362.666666 0 181.333333 181.333333 0 1 0-362.666666 0Z" fill="#FFCA5F" p-id="2715"></path><path d="M125.866667 274.56m-42.666667 0a42.666667 42.666667 0 1 0 85.333333 0 42.666667 42.666667 0 1 0-85.333333 0Z" fill="#FFCA5F" p-id="2716"></path><path d="M524.8 894.506667a34.56 34.56 0 0 1-21.333333-7.466667l-5.12-3.84c-113.28-89.386667-183.466667-144.853333-202.88-160.64-90.453333-73.173333-142.933333-144-165.12-222.72a292.266667 292.266667 0 0 1-8.746667-114.56l64 7.893333a229.973333 229.973333 0 0 0 6.4 89.386667c18.346667 65.28 64 125.866667 143.786667 190.293333 13.013333 10.666667 56.533333 45.226667 189.013333 149.333334 132.48-104.32 176-138.88 189.013333-149.333334 79.786667-64 125.44-125.013333 143.786667-190.293333s7.68-164.053333-62.506667-213.333333a132.906667 132.906667 0 0 0-94.933333-21.333334c-70.826667 9.386667-115.84 34.986667-145.92 82.986667a34.56 34.56 0 0 1-58.666667 0c-30.293333-48.213333-75.306667-73.813333-146.133333-83.2a132.906667 132.906667 0 0 0-94.933333 21.333333l-36.906667-52.48a196.48 196.48 0 0 1 140.373333-32.426666 261.973333 261.973333 0 0 1 166.826667 79.573333 261.973333 261.973333 0 0 1 166.826667-79.573333A196.48 196.48 0 0 1 832 216.533333c96.213333 68.053333 113.066667 192 87.253333 283.306667-21.333333 78.72-74.453333 149.333333-165.12 222.72-19.413333 15.786667-89.6 71.253333-202.88 160.64l-5.12 3.84a34.56 34.56 0 0 1-21.333333 7.466667z" fill="#5C1CF7" p-id="2717"></path></svg>
                </div>
                <span>我的</span>
            </div>

        </div>

    </div>

    <script>
        $('.icon_item').on("click", function () {

            $(this).addClass("active").siblings().removeClass("active");
            window.location.hash = $('.icon_item.active').attr('aid');

            var text = $(this).find('span').text();
            var aid = $(this).attr('aid');
            switch (aid) {
                case "friend_book":
                    if (anonyVisitor) {
                        location.href = 'login.aspx';
                        return;
                    }
                    //show_page('#friend_book');
                    show_page('#friend_list');
                    get_user('好友列表');
                    $('#index_menu').show();

                    break;
                case "game":
                    show_page('#game_dating');
                    $('#index_menu').show();
                    break;
                default:
                    show_page('#index_message_data');
                    $('#index_menu').show();
                    break;

            }
        });


        $(function () {
            //var __from = get_param('from');
            var __from = window.location.hash.replace(/#/g, '');
            if (__from.indexOf('chat-') == 0) {
                //get_chat('@fw_gmqun', 1, null, '返现福利官');
                return;
            }

            $('.icon_item[aid="' + __from + '"]').click();
        });


        var lastnumber = -1;
        setInterval(function () {
            //同步消息数量
            var n = parseNumber($('#message_number').data("number"));
            if (n == lastnumber) {
                return;
            }
            lastnumber = n;
            $('#chat_unread_number').html(n);
            if (n > 0) {
                $('#chat_unread_number').show();
            } else {
                $('#chat_unread_number').hide()
            }
        }, 200)
    </script>












    <style type="text/css">
        .open_redbox {
            display: none;
        }

        .redbag_box {
            margin: 0 auto;
            width: 307px;
            height: 508px;
            border-radius: 10px;
            background-color: rgb(255,68,53);
            overflow: hidden;
            /* position: relative; */
            position: absolute;
            top: 50%;
            left: 50%;
            margin-left: -153px;
            margin-top: -254px;
            z-index: 10001;
        }

        .circle_fm {
            border-radius: 50%;
            height: 700px;
            width: 700px;
            background-color: rgb(255, 79, 66);
            position: absolute;
            top: -292px;
            left: -198px;
            border: 2px solid rgb(237,61,49);
            z－index: -1;
        }

        .redbag_text1 {
            position: absolute;
            z－index: 10001;
            height: 25px;
            width: 209px;
            font-weight: bold;
            font-size: 17px;
            color: rgb(254,199,155);
            top: 130px;
            left: 135px;
            letter-spacing: 1px;
        }

        .redbag_head {
            position: absolute;
            z－index: 10001;
            height: 29px;
            width: 28px;
            border-radius: 5px;
            top: 125px;
            left: 95px;
        }

        .redbag_text2 {
            position: absolute;
            z－index: 10001;
            height: 25px;
            width: 209px;
            font-weight: bold;
            font-size: 21px;
            color: rgb(254,199,155);
            top: 163px;
            left: 55px;
            letter-spacing: 1px;
        }

        .redbag_circle_button {
            border: 0;
            background-color: none;
            height: 97px;
            width: 97px;
            border-radius: 50%;
            background-color: rgb(241,204,146);
            margin: auto;
            line-height: 97px;
            text-align: center;
            font-size: 35px;
            font-weight: bold;
            color: rgb(71,68,63);
            position: absolute;
            z－index: 10000;
            top: 70%;
            left: 34%;
            cursor: pointer;
        }

        /* 旋转动画 */
        .redbag-rotate-button.rotate {
            animation: rotateAnimation 1s linear infinite;
        }

        @keyframes rotateAnimation {
            from {
                transform: rotateY(0deg);
            }

            to {
                transform: rotateY(360deg);
            }
        }





        .redbag-icon {
            position: relative;
            left: 20px;
            animation: redbag_swing 0.1s infinite alternate;
        }

        @keyframes redbag_swing {
            0%,20% {
                transform: translateX(-50%) rotate(-2deg);
            }

            50% {
                transform: translateX(-50%) rotate(0deg);
            }

            80%,100% {
                transform: translateX(-50%) rotate(2deg);
            }
        }
    </style>




    <div class="open_redbox redbag_box_cover" style="position: fixed; top: 0; left: 0; z-index: 9999; background: #ffffff9e; width: 100%; height: 100%;" onclick="$('.open_redbox').hide();">
    </div>
    <div class="open_redbox redbag_box">
        <div class="circle_fm"></div>
        <%--        <img src="static/images/avatar-1.jpg" class="redbag_head">
        <div class="redbag_text1">张三的红包</div>
        <div class="redbag_text2">恭喜发财， 大吉大利</div>--%>

        <div class="redbag_circle_button redbag-rotate-button" id="redbag_circle_open">
            開
        </div>

    </div>

    <script>
        $('#redbag_circle_open').on('click', function () {
            if (!$(this).hasClass("rotate")) {
                $(this).addClass("rotate");
                setTimeout(function () {
                    open_redbag_detail($('#redbag_circle_open').data("readbagId"));
                }, 1000);
            }
        })
    </script>












    <style>
        .pop-cpt {
            z-index: 999999999;
            position: relative;
            display: none;
        }

        .pop-cpt2 {
            display: flex;
            justify-content: center;
            align-items: center;
            position: fixed;
            left: 0;
            top: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,.7);
        }

        .pop-cpt a {
            text-decoration: none;
            color: inherit;
            display: block;
        }

            .pop-cpt a:hover {
                text-decoration: none;
            }

        .pop-cpt .pop-cpt-bd {
            line-height: 22px;
            width: 330px;
            /*padding: 18px 18px;*/
            box-sizing: border-box;
            border-radius: 16px;
            background: #fff;
            background: radial-gradient(117.33% 20.82% at 50% 0,rgb(255 0 0 / 12%) 0,hsla(0,0%,100%,0) 100%),#fff;
            font-size: 14px;
            color: #333;
            font-family: "微软雅黑";
            position: relative;
        }

        .pop-cpt .pop-cpt-close {
            display: inline-block;
            height: 36px;
            line-height: 36px;
            padding: 0 15px;
            color: #fff;
            position: absolute;
            top: 0;
            right: 0;
        }

        .pop-cpt .pop-cpt-tit {
            height: 52px;
            line-height: 52px;
            color: #000;
            text-align: center;
            font-size: 18px;
            font-weight: bold;
            color: #f5ecdc;
            text-shadow: 2px 3px 2px #7d5909;
        }

            .pop-cpt .pop-cpt-tit span {
                display: inline-block;
                padding: 0 10px;
                position: absolute;
                top: 0;
                right: 0;
            }

        .pop-cpt .pop-cpt-con {
            padding: 0 6px 3px 6px;
            border-radius: 15px;
            background: none;
        }

        .pop-cpt .pop-cpt-con1 {
            text-align: center;
            color: #000;
        }

        .pop-cpt .pop-cpt-con2 {
            padding-top: 10px;
        }

        .pop-cpt .pop-cpt-con2-tit {
            display: flex;
            justify-content: center;
            padding-bottom: 5px;
        }

            .pop-cpt .pop-cpt-con2-tit span {
                display: inline-block;
                width: 26px;
                line-height: 26px;
                margin-left: -3px;
                text-align: center;
                color: #FFF200;
                font-size: 16px;
                font-weight: bold;
                border-radius: 50%;
                background: #ED1B20;
            }

        .pop-cpt .pop-cpt-con2-logo {
            display: flex;
            justify-content: center;
            height: 60px;
        }

        .pop-cpt .pop-cpt-con2-logol {
            color: #fb2725;
            font-size: 54px;
            line-height: 50px;
            font-weight: bold;
            font-family: Tahoma;
        }

        .pop-cpt .pop-cpt-con2-logor {
            width: 70px;
            position: relative;
        }

        .pop-cpt .pop-cpt-con2-logor-txt1 {
        }

            .pop-cpt .pop-cpt-con2-logor-txt1 span {
                display: inline-block;
                width: 60px;
                height: 22px;
                line-height: 22px;
                text-align: center;
                color: #fff;
                border-radius: 5px;
                background: #0082f3;
                position: absolute;
                left: 8px;
                top: 2px;
            }

        .pop-cpt .pop-cpt-con2-logor-txt2 {
            line-height: 32px;
            color: #333;
            font-size: 30px;
            font-family: impact,arial;
            position: absolute;
            top: 24px;
            left: 0;
        }

        .pop-cpt .pop-cpt-con3 {
            padding: 0 2px;
        }


        .pop-cpt .pop-cpt-con4 {
            text-align: center;
            margin-bottom: 10px;
        }

            .pop-cpt .pop-cpt-con4 span {
                display: inline-block;
                width: 200px;
                height: 30px;
                line-height: 30px;
                font-size: 20px;
                font-weight: bold;
                color: #ff0;
                border-radius: 10px;
                background: #fb2725;
            }

        .pop-cpt .pop-cpt-footer {
            padding: 10px 0;
            color: #fff;
            text-align: center;
        }

        .pop-cpt .pop-cpt-footer1 {
            font-size: 16px;
        }

        .pop-cpt .pop-cpt-footer2 {
            padding-top: 5px;
            font-weight: bold;
            color: #ff0;
        }
    </style>

    <div id="pop-tip" class="pop-cpt" onclick="$('#pop-tip').hide();">
        <div class="pop-cpt2">
            <div class="pop-cpt-bd" style="background: radial-gradient(117.33% 20.82% at 50% 0,rgb(0 170 255 / 12%) 0,hsla(0,0%,100%,0) 100%),#fff; border-radius: 2px;">

                <div style="font-size: 15px; text-align: center; display: flex; align-items: center; padding: 8px 12px; background: #EF0926; color: #fff;">
                    <div style="width: 100%;">
                        <span class="gamename">-</span>
                    </div>
                    <svg t="" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="3945" width="16" height="16" onclick="$('#pop-tip').hide();">
                        <path d="M453.44 512L161.472 220.032a41.408 41.408 0 0 1 58.56-58.56L512 453.44 803.968 161.472a41.408 41.408 0 0 1 58.56 58.56L570.56 512l291.968 291.968a41.408 41.408 0 0 1-58.56 58.56L512 570.56 220.032 862.528a41.408 41.408 0 0 1-58.56-58.56L453.44 512z" fill="#ffffff" p-id="3946"></path></svg>
                </div>




                <div style="display: flex; background: #FEF4F5; padding: 6px 10px; font-size: 12px;">
                    <div>玩法：<span class="playtitle">-</span></div>
                    <div style="margin-left: auto;">
                        期号：<span class="expect">-</span>
                    </div>
                </div>
                <div style="padding: 6px 10px; height: 80px;">
                    投注内容：<span class="number">-</span>
                </div>

                <div style="display: flex; background: #FEF4F5; padding: 6px 10px; font-size: 12px;">
                    <div style="color: #B04F62; font-weight: bold; font-size: 13px;">
                        总金额：<span id="amount_text">0元</span>
                    </div>
                    <div style="margin-left: auto; font-size: 12px; color: gray;">
                        账户余额：<span class="user_amount">-</span>
                    </div>
                </div>
                <div style="display: flex; background: #FEF4F5; padding: 6px 10px; font-size: 12px;">
                    <div style="display: flex;">
                        单注金额
                        <input style="border: 0; border-bottom: 1px solid #a50f22; width: 28px; background: none; margin: 0 5px; text-align: center;" id="play_money">
                        <div style="background: #E70827; color: #fff; width: 27px; height: 22px; text-align: center; font-size: 12px;">
                            元
                        </div>
                    </div>
                    <div style="display: none; margin-left: auto; align-items: center;">
                        <svg t="1707166900208" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="1647" width="12" height="12">
                            <path d="M85.33 469.33h853.33v85.33H85.33z" p-id="1648" fill="#cdcdcd"></path></svg>
                        <input style="border: 0; border-bottom: 1px solid #a50f22; width: 16px; background: none; margin: 0 5px; text-align: center;"
                            value="1" id="play_beishu"><svg t="" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="1243" width="12" height="12"><path d="M938.67 554.63l-0.01-85.33-384 0.03-0.03-384-85.33 0.01 0.03 384-384 0.03 0.01 85.33 384-0.03 0.03 384 85.33-0.01-0.03-384z" p-id="1244"></path></svg>
                        <div style="font-size: 12px; margin-left: 5px;">
                            倍
                        </div>
                    </div>
                </div>
                <div class="pop-cpt-footer" style="padding: 10px; padding-top: 2px;">
                    <a style="border: 1px solid #eee; display: inline-block; padding: 6px 65px; border-radius: 5px; background: #EF0926; color: #eee; cursor: pointer; width: 100%; box-sizing: border-box;" onclick="follow_play()">确定</a>

                </div>

            </div>
        </div>
    </div>

    <script>
        //通用用户搜索
        var showSearchData = function (sid) {
            var text = $('.search_content[sid="' + sid + '"]').val();
            if (text != "") {
                $('.cancel_search_button[sid="' + sid + '"]').show();
            } else {
                $('.cancel_search_button[sid="' + sid + '"]').hide();
            }

            var obj;
            switch (sid) {
                case "allsend"://群发搜索
                    obj = $('#msg_friends').find("div[user_uuid]");
                    break;
                case "friend"://好友搜索
                    obj = $('#user_list').find("div[friendchat]");
                    break;
                default://聊天界面搜索
                    obj = $('#chat-list').find("div[chat]");
                    break;

            }
            //console.log('objhtml',sid, obj.html());

            if (text == '') {
                obj.show();
                return;
            }

            obj.hide();
            obj.each(function () {
                var username_text = $(this).find('.username_text').text();
                //console.log('username_text', username_text);
                if (username_text.indexOf(text) != -1) {
                    $(this).show();
                }
            })
        }
        $('.search_content[sid]').on('keyup', function () {
            var sid = $(this).attr('sid');
            console.log('搜索', sid);
            showSearchData(sid);
        })
        $('.cancel_search_button[sid]').on('click', function () {
            var sid = $(this).attr('sid');
            $('.search_content[sid="' + sid + '"]').val('');
            showSearchData(sid);
        })


        //分享游戏
        var share_play = function (text) {

            v3api("send_message", {
                data: {
                    msgtype: 'card',
                    card_type: 'play',
                    touser: -1,
                    from_type: chatData.type,
                    chatid: chatData.id,
                    text: text
                }
            }, function (e) {
                console.log('card_play', e);
                show_page('#chat_message');
            })


        }

        $(".pop-cpt-bd").on("click", function (event) {
            event.stopPropagation(); // 阻止事件冒泡
        });

        var play_id;
        var follow_click = function (id) {
            v3api("play_data", {
                data: {
                    id: id
                }
            }, function (e) {
                play_id = id;
                console.log('play_data', e);


                $('.gamename').html(e.gamename);
                $('.expect').html(e.expect);
                $('.playtitle').html(e.playtitle);
                $('.number').html(e.number);
                $('#amount_text').html('5元');
                $('.user_amount').html(e.user_amount);
                $('#play_money').val('5');
                $('#play_beishu').val('1');

                $('#pop-tip').show();
            })
        }
        var follow_play = function () {
            $('#pop-tip').hide();
            v3api("follow_play", {
                data: {
                    id: play_id,
                    chatid: chatData.id,
                    amount: $('#play_money').val(),
                    zhushu: $('#play_beishu').val()
                }
            }, function (e) {
                tp(e.msg);
            })
        }

        $('#play_money').on('keyup', function () {
            var n = $(this).val();
            if (isNaN('9') || n == "") {
                n = "0";
            }
            $('#amount_text').html(n + '元')
            ;
        })



        //回复消息
        var reply_msg = function (msgid) {
            console.log('reply', msgid);
            var hh = $('[msgid="' + msgid + '"] .message_details');
            var user_name = $('[msgid="' + msgid + '"] .user_name').html();
            if (typeof (user_name) == "undefined") {
                user_name = "";
            }

            $('#reply_data').attr('replyid', msgid);
            $('#reply_name').html('回复：' + user_name);
            $('#reply_content').html(hh.html());


            $('#reply_content').find('img').css("max-height", "30px");

            $('#reply_data').show();

            to_chatBottom();

        }
        var close_reply = function () {
            $('#reply_data').hide();
            $('#reply_data').attr('replyid', '');
        }

        var reply_create = function () {

            $('div[reply]').each(function () {

                try {
                    var json = JSON.parse($(this).html());

                    $(this).insertBefore($(this).closest(".message_details"));
                    $(this).html('<div style="background: #e4e9e7;padding: 10px;border-radius: 8px;width: 100%;margin-bottom: 4px;font-size: 13px;">                    <div style="">    <div style=" text-align: left;">                        <b style="color: #3e56cf;" id="reply_name">回复：' + json.name + ' </b>                    </div>                    <div style="color: #2A2B2C;margin-top: 3px;text-align: left;" class="reply_from_message">                        ' + replace_emoji(decodeMessage(json.data)) + '                                   </div></div>                                    </div>');
                    $(this).find('.reply_from_message img').css("max-height", "30px");
                    $(this).attr('reply_msgid', $(this).attr('reply')).show();
                    $(this).removeAttr('reply');
                } catch (e) {

                }
            })
        }

        $(document).on("contextmenu", ".message_details", function (event) {
            event.preventDefault();
            reply_msg($(event.target).closest('div[msgid]').attr('msgid'));
        });

        // 监听 touchstart 事件
        $(document).on("touchstart", ".message_details", function (event) {
            console.log("touchstart");
            // 设置长按触发时间为1秒
            var pressTimer = setTimeout(function () {
                // 在这里执行长按事件的操作
                reply_msg($(event.target).closest('div[msgid]').attr('msgid'));
                console.log("长按事件触发");
            }, 1000);

            // 监听 touchend 事件
            $(document).on("touchend", ".message_details", function (event) {
                // 清除长按事件的计时器
                clearTimeout(pressTimer);
            });
        });

        var atList = {};
        var atuser = function (uuid, name) {
            $('#send_message').val($('#send_message').val() + '@' + name + ' ');
            atList[name] = uuid;
        }


        function decodeMessage(encodedMessage) {
            // 创建一个临时的div元素
            var tempDiv = document.createElement('div');

            // 将HTML实体解码并放入临时div中
            tempDiv.innerHTML = encodedMessage;

            // 返回解码后的文本内容
            return tempDiv.textContent || tempDiv.innerText || "";
        }
    </script>


    <div id="nmh-navicon" class="NMH-g-plugin NMH-g-navicon">
        <div style="background: rgb(201, 201, 201); height: 230px; width: 230px; border-radius: 50%; position: relative; top: -110px; right: 40px; opacity: 0.5; display: none;" id="Jnmh-shadow"></div>
        <button class="Jnmh-btnlogo Jnmh-btnlogo-def " type="button">游戏</button>
        <ins class="Jnmh-btnlogohover"></ins>
        <ul class="Jnmh-m-submenu">
            <%-- <li class="Jnmh-subli">
                <dl class="Jnmh-subdl" gameId="1">
                    <dt class="NMH-subdt">电商平台</dt>
                    <dd class="NMH-subdd"></dd>
                </dl>
            </li>
            <li class="Jnmh-subli">
                <dl class="Jnmh-subdl" gameId="2">
                    <dt class="NMH-subdt">选品平台</dt>
                    <dd class="NMH-subdd"></dd>
                </dl>
            </li>
            <li class="Jnmh-subli">
                <dl class="Jnmh-subdl" gameId="3">
                    <dt class="NMH-subdt">会员升级</dt>
                    <dd class="NMH-subdd"></dd>
                </dl>
            </li>
            <li class="Jnmh-subli">
                <dl class="Jnmh-subdl" gameId="4">
                    <dt class="NMH-subdt">产品操作</dt>
                    <dd class="NMH-subdd"></dd>
                </dl>
            </li>
            <li class="Jnmh-subli">
                <dl class="Jnmh-subdl" gameId="5">
                    <dt class="NMH-subdt">个人中心</dt>
                    <dd class="NMH-subdd"></dd>
                </dl>
            </li>--%>
        </ul>
    </div>
    <script type="text/javascript">
        // 绑定 .Jnmh-subdl 的点击事件
        //$('.Jnmh-subdl').click(function (event) {
        //    //event.stopPropagation(); // 防止事件冒泡到其他元素
        //    open_auth('../games/authorize.aspx?gameId=' + $(this).attr("gameId"));
        //});
        $('.Jnmh-m-submenu').on('click', '.Jnmh-subdl', function (event) {
            //event.stopPropagation(); // 防止事件冒泡到其他元素
            open_auth('../games/authorize.aspx?gameId=' + $(this).attr("gameId"));
        });


        $('#nmh-navicon').on('click', function () {
            $('#nmh-navicon').toggleClass('Jnmh-open');
            GtoggleNavlogo();
        })

        var GtoggleNavlogo = function () {
            var li = $('#nmh-navicon').find('.Jnmh-subli');
            var lilen = li.length;
            var avgDeg = 180 / (lilen - 1);// 平均角度
            var initDeg = 0;// 起始方向角度
            if ($('#nmh-navicon').hasClass('Jnmh-onleft')) {
                // 如果悬浮球被拖拽到左边，则二级菜单则显示右侧
                li.css({ transform: 'rotate(0deg)' });
                initDeg = 180;
            } else {
                // 默认悬浮球在右边，二级菜单显示在左侧
                li.css({ transform: 'rotate(-360deg)' });
            }
            for (var i = 0, j = lilen - 1; i < lilen; i++, j--) {
                var d = initDeg - (i * avgDeg);
                var z = initDeg ? j : i;
                // console.log(d);
                $('#nmh-navicon').hasClass('Jnmh-open') ? GrotateNavlogo(li[z], d) : GrotateNavlogo(li[z], 0);
            }

            if ($('#nmh-navicon').hasClass('Jnmh-open')) {
                $('#Jnmh-shadow').show();
                $('.Jnmh-btnlogo').removeClass("Jnmh-btnlogo-def");
                $('.Jnmh-btnlogo').html('<svg t="" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="" width="22" height="22"><path d="M962.56 926.72L586.845091 552.96l375.528727-373.666909c21.224727-21.224727 21.224727-55.668364 0-74.193455-21.131636-21.224727-55.482182-21.224727-74.007273 0L512.930909 478.766545 140.008727 102.4a59.764364 59.764364 0 0 0-76.706909 0c-21.131636 18.618182-18.525091 55.668364 0 76.893091L438.830545 552.96 63.301818 926.72c-21.131636 21.131636-21.131636 55.575273 0 74.193455 21.224727 21.131636 55.575273 21.131636 74.100364 0L512.930909 627.153455l372.922182 376.366545c21.131636 21.224727 55.482182 21.224727 74.007273 0a57.157818 57.157818 0 0 0 2.606545-76.8z" fill="#B7B7B7" p-id="6379"></path></svg>');
            } else {
                $('#Jnmh-shadow').hide()
                $('.Jnmh-btnlogo').addClass("Jnmh-btnlogo-def");
                $('.Jnmh-btnlogo').text('游戏');
            }


        };
        var GrotateNavlogo = function (dom, deg) {
            $({ a: 0 }).animate({ a: deg }, {
                step: function (now, fx) {
                    $(dom).css({ transform: 'rotate(' + now + 'deg)' });
                    $(dom).children().css({ transform: 'rotate(' + (-now) + 'deg)' });
                }, duration: 0
            });
        }

    </script>
</asp:Content>


<%@ Page Title="" Language="C#" MasterPageFile="~/top.master" AutoEventWireup="true" CodeFile="play_records.aspx.cs" Inherits="order_list" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="Server">
    <script>
        $(function () {
            popTitle('游戏记录', '');
        })
    </script>
    <style>
        .top-title {
            background: #EDEDED;
        }

        body {
            background: #fff;
        }

        .top-title .pptitle {
            font-weight: 100;
        }
    </style>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="Server">



    <div id="lists">
    </div>



    <script>

        var more_list = function (index) {
            show_list(index);
        }

        var show_list = function (index) {
            if (!index) {
                index = 0;
            }


            v3api("lists", { data: { page: 'play_records', p: index, limit: 10 } }, function (e) {
                for (var i = 0; i < e.data.list.length; i++) {
                    var obj = e.data.list[i];
                    $('#lists').append('<div style="padding-bottom: 18px; border-bottom: 1px solid #f2f2f2; margin-bottom: 18px;"><div style="color: #20a349;">' + obj.gametitle + '（' + obj.expect + '&#26399）</div><div style="margin-top: 10px; line-height: 22px; font-size: 12px; color: #2a2b2c;"><div>&#35746&#21333&#21495：' + obj.trano + '</div><div>&#29609&#27861：' + obj.playtitle + '</div><div>&#25237&#27880&#20869&#23481：' + obj.playcode + '</div>' + (obj.state == 0 ? '' : '<div>&#24320&#22870&#21495&#30721：' + obj.resultcode + '</div>') + '<div>&#19979&#27880&#37329&#39069：' + obj.amount + '</div></div><div style="font-size: 12px; margin-top: 8px; color: gray; display: flex;"><div>' + timestampToDateTimeString(obj.time) + '</div><div style="margin-left: auto;">' + (obj.state == 0 ? '&#26410&#24320&#22870' : obj.state == -1 ? '<span style="color:green;">&#26410&#20013&#22870</span>' : obj.state == 1 ? '<span style="color:#dd2727;">&#20013;' + obj.okamount + '<img src="/static/images/coin.png" alt="" height="12" width="12" style="margin-left: 5px;position: relative;top: 1px;"></span>' : '&#24322&#24120') + '</div></div></div>');
                }



                if (e.data.list.length == 10) {
                    $('#lists').append('<div id="load_more" next_id="' + (parseInt(index) + 1) + '"></div>');
                }
                finish_trigger();

            })
        }

        more_list(0);


        function timestampToDateTimeString(timestamp) {
            // 将时间戳乘以1000以转换为13位时间戳（Date对象使用13位时间戳）
            var date = new Date(timestamp * 1000);

            // 获取年、月、日、时、分、秒
            var year = date.getFullYear();
            var month = String(date.getMonth() + 1).padStart(2, '0'); // 月份从0开始，需要加1
            var day = String(date.getDate()).padStart(2, '0');
            var hours = String(date.getHours()).padStart(2, '0');
            var minutes = String(date.getMinutes()).padStart(2, '0');
            var seconds = String(date.getSeconds()).padStart(2, '0');

            // 格式化日期时间字符串
            var formattedDateTimeString = year + '-' + month + '-' + day + ' ' + hours + ':' + minutes + ':' + seconds;

            return formattedDateTimeString;
        }
    </script>


    <script>
        $(document).ready(function () {
            $(window).scroll(function () {
                if ($(document).scrollTop() <= 0) {
                    console.log("滚动条已经到达顶部为0");
                }
                if ($(document).scrollTop() + 100 >= $(document).height() - $(window).height()) {
                    trigger_bottom();
                }

            });

        });

        var trigger_check = 0;
        var trigger_bottom = function () {
            if (trigger_check != 0) {
                return;
            }
            trigger_check = 1;
            console.log("滚动条已经到达底部为" + $(document).scrollTop());

            if (typeof (more_list)) {
                var m = $('#lists').find("#load_more");
                if (m.html() != undefined) {
                    m.remove();
                    more_list(m.attr("next_id"));
                }
            }
        }

        var finish_trigger = function () {
            trigger_check = 0;
        }

    </script>

</asp:Content>


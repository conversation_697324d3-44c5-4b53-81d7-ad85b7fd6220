@charset "utf-8";
body { background: #fff;}
.lr-box { width: 100%; min-height: 600px; overflow: auto; padding-bottom: 10px;}
.lr-box .login { margin: 71px auto 0 auto;-moz-box-shadow:1px 1px 6px 0 rgba(0,0,0,.2); box-shadow: 1px 1px 6px 0 rgba(0,0,0,.2); box-shadow: 1px 1px 6px 0 rgba(0,0,0,.2);}
.lr-box.login-bg { background:url(../images/login-reg/login-bg.jpg) center top no-repeat;}
/*.lr-box.reg-bg { background:url(../images/login-reg/reg-bg.jpg) center top no-repeat;}*/
/* 注册 */
.reg { width: 500px; padding-bottom: 50px; background: #fff; overflow: auto; position: relative; margin: 50px auto 0 auto; background-color: #fff;-moz-box-shadow:1px 1px 12px 3px rgba(0,0,0,.07); box-shadow: 1px 1px 12px 3px rgba(0,0,0,.07); box-shadow: 1px 1px 12px 3px rgba(0,0,0,.07);}
.reg-top { width: 320px; margin: 31px auto 25px auto;}
.reg-top h2 { overflow: hidden;}
.reg-top h2 span { float: left; font-size: 24px; color: #333; font-family: "microsoft yahei";}
.reg-top h2 em { float: right; margin-top: 13px; color: #aaa;}
.reg-top h2 em a { margin-top: 13px; color: #ff6700;}
.reg-top h2 em a:hover { color: #333;}
.reg-con {width: 320px; margin: 0 auto 0 auto;}
.reg-con .reg-form-item {display:block; position: relative; margin-bottom: 34px;}
.reg-con .nc-container.nc-scrape .nc-toolbar{line-height: 20px;font-size: 12px;color: #999;height: 25px;}
.reg-con .nc-container.nc-scrape .nc-toolbar .nc-btns{height: 24px;}
.reg-con .reg-form-item em.iconfont { position: absolute; left: 1px; top:1px; display:block; width: 40px; height: 42px; line-height: 42px; text-align: center; color: #aaa; font-size: 20px;}
.reg-con .reg-form-item i.iconfont {  position: absolute; right: 1px; top:1px; display:none; width: 40px; height: 42px; line-height: 42px; text-align: center; color: #aaa; font-size: 20px; cursor: pointer;}
.reg-con .reg-form-item i.right-icon { color: #22ac38;}
.reg-con .common-input { width: 243px; padding:0 40px; height: 42px; line-height: 42px\9; font-size: 14px; font-family:"microsoft yahei";}
.reg-con .reg-form-item.reg-form-yzm .common-input{ padding:0 15px; width: 88px; float: left;}
.reg-con .reg-form-item.reg-form-yzm .codeimg-box { float: left; width: 80px; height: 44px; margin-left: 14px;}
.reg-con .reg-form-item.reg-form-yzm .codeimg-box img { cursor: pointer;}
.reg-con .reg-form-item.reg-form-yzm .code-change { float: left; margin-left: 14px; margin-top: 13px;}
.reg-con .reg-form-item.reg-form-yzm .code-change a { color: #0675ff;}
.reg-con .reg-form-item.reg-form-yzm .code-change a:hover { color: #ff6700;}
.reg-con .reg-form-item.reg-form-yzm .clear-btn { left: 80px;}
.reg-con .login-error { position: absolute; left:50px; top:278px; width: 320px; text-align: center; color: #ff6700;}
.reg-con .login-error.Validform_checktip { padding-left: 0;}
.reg-con .reg-form-item.phone-code .common-input{ width: 90px; float: left;}
.reg-con .reg-form-item.phone-code label em.iconfont {top:3px;}
.reg-con .reg-form-item.phone-code .phone-code-btn { float: left; width: 130px; height: 44px; margin-left: 14px;}
.reg-con .reg-form-item.phone-code .phone-code-btn .com-btn-01 { width: 128px; height: 36px; line-height: 35px; margin-top: 4px; font-size: 12px; font-family: \5b8b\4f53; color: #666; border-radius: 0; cursor: pointer;}
.reg-con .reg-form-item.phone-code .clear-btn { left: 132px;}
.reg-con .reg-btn { margin-top: 40px;}
.reg-bot { margin-top: 66px; text-align: center; color: #999;}
.reg-bot .reg-btn { width: 320px; margin: 0 auto 26px auto;}
.reg-bot .reg-btn .com-btn-01 { width: 100%; font-size: 20px;}
.reg-bot p.reg-tips { color: #aaa;}
.reg-bot p.reg-tips a { color: #888;}
.reg-bot p.reg-tips a:hover{ color: #ff6700;}
.reg-con .reg-tips.Validform_checktip { position: absolute; color: #999; left: 1px; bottom: -25px; padding-left: 0;}
.reg-con .reg-tips.Validform_checktip a { color: #0675ff;}
.reg-con .reg-tips.Validform_checktip.Validform_wrong { color: #ff6700;}

/*上传身份证*/
.upload-box{width: 105%;position: relative;}
.upload-box .upload-item{width: 97px;height: 97px;float: left;margin-right: 16px;}
.upload-box .upload-item.mr0{margin-right: 0;}
.upload-box .upload-item .upload-con{width: 97px;height: 97px;}
.upload-box .upload-item .id-card-z{background: url(../images/login-reg/id-card-z.png) no-repeat;background-size: 100% 100%;}
.upload-box .upload-item .id-card-f{background: url(../images/login-reg/id-card-f.png) no-repeat;background-size: 100% 100%;}
.upload-box .upload-item .id-card-s{background: url(../images/login-reg/id-card-s.png) no-repeat;background-size: 100% 100%;margin-right: 0;}
.upload-box .auth-upload-con .upload-img{width: 97px;height: 97px;position: relative;border: 1px dashed #cccccc;}
.upload-box .auth-upload-con .upload-img img{width: 97px;height: 97px;}
.upload-box .auth-upload-con .upload-img .info{display: none;}
.upload-box .auth-upload-con .webuploader-container{position: absolute;top:0;left: 0;}
.upload-box .auth-upload-con .webuploader-pick{width: 97px;height: 97px;overflow: hidden;line-height: 9999px;}
.upload-box .upload-tips{position: absolute;bottom: -25px;left: 1px;color: #999;}
.upload-box .upload-tips.error{color: #ff6700;}
.reg-bot{margin-top: 37px;}







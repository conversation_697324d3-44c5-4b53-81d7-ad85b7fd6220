/*body, button, input, select, span, textarea, h1, h2, h3, h4, h5, h6 {
	font-family: "Hiragino Sans GB","Microsoft YaHei", '宋体', Tahoma, Helvetica, Arial, "\5b8b\4f53", sans-serif;
}
body,td,th {
	font-family: "Hiragino Sans GB","Microsoft YaHei", "宋体", Tahoma, Helvetica, Arial, \5b8b\4f53, sans-serif;
}*/
.news-my{padding-bottom: 10px;border-bottom: 1px solid #e6e6e6;    margin-bottom: 10px;}
.news-my li{font-size:18px;margin-bottom: 10px;}
.news-my li span{margin-right: 5px; font-size: 16px;color: #E65858;}
.news-my div{color:#B3B3B3;  line-height: 24px;}
.vertical-center{
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
    }
	.center-block {
    margin-bottom: 10px;
}
.checkbox { color:#333 !important;}
.treeview img{  height: 20px;margin-right: 20px;  vertical-align: sub;}
.treeview-menu li{    margin-left: 38px !important;}

.text-danger {
    color: #848484 !important;
}
.label-danger {background-color: #219E8B !important;}

ul,li,dl,dd,p,img,h1,h2,h3,h4,h5,h6,from,span,strong{border:0;list-style-type:none;margin:0;padding:0;}
input{margin:0;padding:0;}

.skin-blue .main-header .navbar {
	background-color: #F5F5F5;
    border-bottom:1px solid #ddd;
}
.skin-blue .main-header .navbar .nav>li>a {
	color: #999999;    line-height: 50px;
}
.skin-blue .main-header .navbar .nav>li>a:hover, .skin-blue .main-header .navbar .nav>li>a:active, .skin-blue .main-header .navbar .nav>li>a:focus, .skin-blue .main-header .navbar .nav .open>a, .skin-blue .main-header .navbar .nav .open>a:hover, .skin-blue .main-header .navbar .nav .open>a:focus, .skin-blue .main-header .navbar .nav>.active>a {
	background: rgba(0,0,0,0.1);
	color: #999999
}

.skin-blue .main-header .navbar .nav>li>a.closebtn:hover{
    background:inherit;
    cursor:pointer;
}

.skin-blue .main-header .navbar .sidebar-toggle {
	color: #fff
}
.skin-blue .main-header .navbar .sidebar-toggle {
	color: #999999;
	line-height: 50px;
	font-size: 22px;
    padding:0 15px;
}
.skin-blue .main-header .navbar .sidebar-toggle:hover {    
	color: #999999;
	background-color: #f0f0f0;
	line-height: 50px;
}
@media (max-width:767px) {
.skin-blue .main-header .navbar .dropdown-menu li.divider {
	background-color: rgba(255,255,255,0.1)
}
.skin-blue .main-header .navbar .dropdown-menu li a {
	color: #fff
}
.skin-blue .main-header .navbar .dropdown-menu li a:hover {
	background: #404040
}
}
.skin-blue .main-header .logo {
	background-color:#222;
	color: #fff;
	border-bottom: 1px solid #000;
    border-right: 1px solid #000;
}
.skin-blue .main-header .logo:hover {
	background-color: #000;
}
.skin-blue .main-header li.user-header {
	background-color: #404040
}
.skin-blue .content-header {
	background: transparent
}
.skin-blue .wrapper, .skin-blue .main-sidebar, .skin-blue .left-side {
	background-color: #3E3838;
}
.skin-blue .user-panel>.info, .skin-blue .user-panel>.info>a {
	color: #fff
}
.skin-blue .sidebar-menu>li.header {
	color: #4b646f;
	background: #1a2226
}
.skin-blue .sidebar-menu>li>a {
    color: #9C9C9F;
	border-left: 3px solid transparent
}
.skin-blue .sidebar-menu>li:hover>a {
    color: #7B797B;
    background: #393333;
    /*border-left-color: #3c8dbc;*/
}

.skin-blue .sidebar-menu > li.active > a {
    color: #F04F43;
    /*color: #00a65a ;*/    
    /*color: #00a65a ;*/
    background: #2F2A2A;
}


.skin-blue .sidebar-menu>li>.treeview-menu {
	margin: 0 1px;
	background: #2c3b41
}
.skin-blue .sidebar a {
	color: #b8c7ce
}
.skin-blue .sidebar a:hover {
	text-decoration: none
}
.skin-blue .treeview-menu>li>a {
	color: #8aa4af
}
.skin-blue .treeview-menu>li.active>a, .skin-blue .treeview-menu>li>a:hover {
	color: #fff
}
.skin-blue .sidebar-form {
	border-radius: 3px;
	border: 1px solid #374850;
	margin: 10px 10px
}
.skin-blue .sidebar-form input[type="text"], .skin-blue .sidebar-form .btn {
	box-shadow: none;
	background-color: #374850;
	border: 1px solid transparent;
	height: 35px;
	-webkit-transition: all .3s ease-in-out;
	-o-transition: all .3s ease-in-out;
	transition: all .3s ease-in-out
}
.skin-blue .sidebar-form input[type="text"] {
	color: #666;
	border-top-left-radius: 2px;
	border-top-right-radius: 0;
	border-bottom-right-radius: 0;
	border-bottom-left-radius: 2px
}
.skin-blue .sidebar-form input[type="text"]:focus, .skin-blue .sidebar-form input[type="text"]:focus+.input-group-btn .btn {
	background-color: #fff;
	color: #666
}
.skin-blue .sidebar-form input[type="text"]:focus+.input-group-btn .btn {
	border-left-color: #fff
}
.skin-blue .sidebar-form .btn {
	color: #999;
	border-top-left-radius: 0;
	border-top-right-radius: 2px;
	border-bottom-right-radius: 2px;
	border-bottom-left-radius: 0
}
.skin-blue.layout-top-nav .main-header>.logo {
	background-color: #676767;
	color: #fff;
	border-bottom: 0 solid transparent
}
.skin-blue.layout-top-nav .main-header>.logo:hover {
	background-color: #3b8ab8
}

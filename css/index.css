@charset "utf-8";
body { background: #fff;}
.top-common-box {min-width: 1300px;}
.top-common { width: 1300px;}
.header-box { min-width: 1300px; border-bottom: none;}
.header { width: 1300px; }
.main-search-box { margin-right: 40px;}
.index-wrap { min-width: 1300px;}
/* 微效果 */
.totop {-webkit-transition: all .2s ease; -moz-transition: all .2s ease; -ms-transition: all .2s ease; transition: all .2s ease; }
.totop:hover { -webkit-transform: translateY(-3px); -moz-transform: translateY(-3px); -ms-transform: translateY(-3px); transform: translateY(-3px);}
/* 微效果 */
/* index-nav Start */
.newnav { height: 42px; margin-top: 0; min-width: 1300px; background: none!important;}
.newnav ul { width: 1300px;}
.newnav ul li { height: 42px; width: 126px;}
.newnav ul li a { height: 42px; color: #333; font-size: 16px; }
.navlong { width: 243px; height: 42px; background: url(../images/index/navlong-bg.png) no-repeat;}
.newnav ul li.navlong { margin-right: 20px;}
.newnav ul li.navlong a { color: #fff; margin-left: 26px;}
.newnav ul li img { top: 4px; right: 12px;}
.newnav ul li .steam img { right: -8px; top: -7px; z-index: 9; }
/* index-nav End */
/* main-area-01 Start */
.main-area-01 { width: 100%; min-width: 1300px; height: 508px; margin: 0 auto; overflow: hidden; background: #f8f8f8 url(../images/index/banner-bg.jpg) center top no-repeat; position: relative;}
.main-area-01 .w1300 { position: relative; z-index: 9;}
.subnav-box { position: relative; z-index: 8;height: 478px; width: 240px; background:#312b30; float: left;}
.subnav-box .subnav-hotgame { padding-top: 20px; padding-bottom: 6px; margin-bottom: 9px; margin-left: 24px; width: 194px; border-bottom: #383838 1px solid;}
.subnav-box .subnav-hotgame ul { overflow: hidden; width:240px;}
.subnav-box .subnav-hotgame ul li { float: left; margin-right:14px; margin-bottom: 15px;}
.subnav-box .subnav-hotgame ul li a { display: block; width: 88px; height: 30px; line-height: 30px; color: #fff; text-align: center; font-size: 14px; font-family: "microsoft yahei"; border: #555 1px solid; border-radius: 32px;}
.subnav-box .subnav-hotgame ul li a:hover { color: #ff6700; border: #ff6700 1px solid;}
.sub-nav { width: 240px;}
.sub-nav a { display: block; height: 36px; padding: 11px 0 11px 30px; -webkit-transition: all .2s ease-in-out; -moz-transition: all .2s ease-in-out;-ms-transition: all .2s ease-in-out;transition: all .2s ease-in-out;}
.sub-nav dl{ overflow: hidden;}
.sub-nav dt { float: left; width: 42px; height: 36px; margin-right: 20px;}
.sub-nav dd { float: left; margin-top: -1px; max-width: 130px; white-space: nowrap; overflow: hidden; text-overflow: ellipsis;  word-wrap: normal;}
.sub-nav dd h5{ font-weight: normal; color: #fff; font-family: "microsoft yahei"; font-size: 14px;}
.sub-nav dd p{ color: #555; font-family: arial;}
.sub-nav a.active { padding-left: 35px; background-color: #f6f8fc; background-repeat: repeat-y; background-image: -moz-linear-gradient(left,#f6f8fc,#fff); background-image: -webkit-linear-gradient(left,#f6f8fc,#fff);background-image: -o-linear-gradient(left,#f6f8fc,#fff); background-image: linear-gradient(left,#f6f8fc,#fff);}
.sub-nav a.active dl dd h5 { color: #ff6700;}
.sub-nav a.active dl dd p { color: #bbbbbb;}
.sub-menu { width: 786px; height: 477px; border-top: #ff6700 1px solid; visibility: hidden; opacity: 0; background: #ffffff; position: absolute; overflow: hidden; left: 240px; top:0; z-index: 9; -webkit-transition: all .3s ease-in-out;-moz-transition: all .3s ease-in-out;-ms-transition: all .3s ease-in-out;}
.sub-menu.active { visibility: visible; opacity: 1;}
.sub-menu .sub-menu-item { display: none;}
.sub-menu-item ul { overflow: hidden; padding-left: 25px; padding-top: 35px;}
.sub-menu-item ul li { float: left; position: relative; width: 165px; margin-left: 15px; margin-bottom: 35px;}
.sub-menu-item ul li a img { display: inline-block; vertical-align: middle;}
.sub-menu-item ul li a span { font-size: 14px; color: #333; font-family: "microsoft yahei"; margin-left: 6px; display: inline-block; vertical-align: middle; max-width: 114px; white-space: nowrap; overflow: hidden;text-overflow: ellipsis;word-wrap: normal; }
.sub-menu-item ul li a i { display: inline-block; position: absolute; left: 42px; top:-14px;}
.sub-menu-item ul li a:hover span { color: #ff6700;}
.index-banner { float: left; margin-left: 14px; margin-top: 14px; overflow: hidden;width: 772px;}
.banner-box { width: 772px; height:310px; float: left; margin-bottom: 14px;overflow: hidden; position:relative;}
.banner-box .hd{ width: 772px; height: 36px; position:absolute; bottom:0px; z-index:1; }
.banner-box .hd li{float:left;height:36px;line-height:36px;overflow:hidden; color:#fff;text-align:center; cursor:pointer;position: relative;font-family: "microsoft yahei";color: #ccc;}
.banner-box .hd li p {white-space: nowrap;overflow: hidden;text-overflow: ellipsis;word-wrap: normal; padding: 0 10px;}
.banner-box .hd li.on{color:#FFF;background:#000;text-decoration:none;}
.banner-box .hd li.on h5 { position: absolute;left: 0; bottom: 0px; width: 100%; height: 2px; background: #93c9ff;}
.banner-box .hd li.on h5 em {position: absolute;left: 0; bottom: 0px; width: 0px; height: 2px; background: #0d7bea;}
.banner-box .bd li{ width:772px;height:310px; overflow:hidden; }
.banner-box .bd li img{ width:772px;height:310px; }
.banner-box .bd{position: relative; height: 100%;z-index: 0;}
.banner-box .txtBg{ position:absolute; width:100%; height:36px; bottom:0px; background:#000; filter:alpha(opacity=66);opacity:0.66;}
.recommend-box:after{content:".";display:block;height:0;clear:both;visibility:hidden;}
.recommend-box a { display: block; float: left;}
.recommend-box a.first { margin-right: 14px;}
.quk-area { float: right; width: 260px; height: 464px; background: #fff; margin-top: 14px;}
.index-login { height: 120px; text-align: center; width: 260px; padding-top: 18px; margin-bottom: 2px;}
.index-login dl { margin-bottom: 12px;}
.index-login dl dt { display: inline-block; width: 50px; height: 50px; margin-bottom: 9px;}
.index-login dl dd { color: #888;}
.index-login .login-btn a { display: inline-block; width: 90px; height: 28px; margin: 0 5px; border-radius: 28px; background: #fff; border: #ff6700 1px solid; color: #ff6700; line-height: 28px; text-align: center; font-size: 14px; font-family: "microsoft yahei";
-webkit-transition: all .4s ease-in-out;
-moz-transition: all .4s ease-in-out;
-ms-transition: all .4s ease-in-out;
transition: all .4s ease-in-out;
}
.index-login .login-btn a:hover { background: #ff6700; border: #ff6700 1px solid; color: #fff;}
.index-login p { width: 220px; text-align: center; margin: 0 auto; margin-top: 16px; white-space: nowrap;overflow: hidden;text-overflow: ellipsis;word-wrap: normal;}
.index-login p span { color: #ff6700; font-size: 16px; font-family: "microsoft yahei"; vertical-align: middle; display: inline-block; max-width: 160px; white-space: nowrap; overflow: hidden; text-overflow: ellipsis; word-wrap: normal;}
.index-login p a { vertical-align: middle; color: #999; margin-left: 10px;}
.index-tab-box { width: 220px; margin:0 auto; margin-bottom: 16px; position: relative; padding-bottom: 15px; border-bottom: #e5e5e5 1px dashed;}
.index-tab-box .slider-line { position: absolute; left: 0; top:41px; width: 110px; height: 1px; background: #f56500;}
.index-tab-top { overflow: hidden; border-bottom: #eee 1px solid; padding-top: 3px; margin-bottom: 19px;}
.index-tab-top li { float: left; width: 110px; text-align: center; padding: 10px 0; cursor: pointer;}
.index-tab-top li.on { color: #ff6700;}
.index-tab-bot { height: 146px;}
.index-tab-item { display: none;}
.index-tab-item ul li { width: 220px; margin-bottom: 13px; white-space: nowrap;overflow: hidden;text-overflow: ellipsis;word-wrap: normal;}
.index-tab-item ul li a { color: #666;}
.index-tab-item ul li a:hover { color: #ff6700;}
.down-app { width: 220px; margin: 0 auto;}
.down-app .down-btn { width: 220px; height: 46px; line-height: 44px; background-color: #ff9600;background-repeat: repeat-y; margin-bottom: 8px;display: inline-block; text-align: center; color: #fff; border-radius: 4px; position: relative;
    background-image: -moz-linear-gradient(left,#ff9600,#ff6700);
    background-image: -webkit-linear-gradient(left,#ff9600,#ff6700);
    background-image: -o-linear-gradient(left,#ff9600,#ff6700);
    background-image: linear-gradient(left,#ff9600,#ff6700);
}
.down-app .down-btn i { display: inline-block; width: 20px; height: 20px; margin-right: 12px; vertical-align: middle; background: url(../images/index/down-icon.png) no-repeat;}
.down-app .down-btn span { font-size: 16px; font-family: "microsoft yahei"; vertical-align: middle;}
.down-app .down-btn { -webkit-transition: all .3s ease-in-out; -moz-transition: all .3s ease-in-out; -ms-transition: all .3s ease-in-out; transition: all .3s ease-in-out}
.down-app .down-btn:hover { box-shadow: 0 5px 10px rgba(255,103,0,.4);-webkit-transform: translateY(-3px);-moz-transform: translateY(-3px); -ms-transform: translateY(-3px); transform: translateY(-3px); background-color: #ff6700; background-repeat: repeat-y;
    background-image: -moz-linear-gradient(left,#ff6700,#ff9600);
    background-image: -webkit-linear-gradient(left,#ff6700,#ff9600);
    background-image: -o-linear-gradient(left,#ff6700,#ff9600);
    background-image: linear-gradient(left,#ff6700,#ff9600)
}
.down-app p { text-align: center; color: #d7d7d7;}
.main-area-01 .wave-roll-area { position: absolute; z-index: 1; bottom: 0px; left: 0;}
.main-area-01 .wave-roll-area ul { position: absolute; left: 0; bottom: 0; width: 6000px;}
.main-area-01 .wave-roll-area ul .wave-box { float: left; width: 3000px; height: 120px;}
.main-area-01 .wave-roll-area ul .wave-box .wave-bg1 { width: 100%; height: 120px; position: absolute; bottom: 0; z-index: 1; background: url(../images/index/A1.png);}
.main-area-01 .wave-roll-area ul .wave-box .wave-bg2 { width: 100%; height: 120px; position: absolute; bottom: 0; z-index: 3; background: url(../images/index/A2.png);}
.main-area-01 .wave-roll-area ul .wave-box .wave-bg3 { width: 100%; height: 120px; position: absolute; bottom: 0; z-index: 2; background: url(../images/index/A3.png);}

/* main-area-01 End */
/* main-area-02 Start */
.main-area-02 { width: 1300px; margin: 0 auto; padding-top: 42px; margin-bottom: 33px;}
.main-area-02:after{content:".";display:block;height:0;clear:both;visibility:hidden;}
.tabsList-box {overflow:hidden; zoom:1; width:1020px; float: left;}
.tabsList{overflow:hidden; zoom:1; width:1190px; float: left;}
.tabsList li.item{float:left; display:inline; padding-right: 1px; height:350px; width: 230px; overflow: hidden; position: relative; -webkit-transition: all .3s ease-in-out; -moz-transition: all .3s ease-in-out; -ms-transition: all .3s ease-in-out; transition: all .3s ease-in-out}
.tabsList li.item.fs { width: 559px;}
.tabsList li.item h4{width:230px;height:350px;float:left;overflow:hidden; position: absolute;left: 0;top:0;}
.tabsList li.item h4 span { position: absolute; bottom: 0; left: 0; width: 100%; text-align: center;}
.tabsList li.item.one h4 { background: url(../images/index/playing-bg-01.png) no-repeat;}
.tabsList li.item.two h4 { background: url(../images/index/playing-bg-02.png) no-repeat;}
.tabsList li.item.last h4 { background: url(../images/index/playing-bg-03.png) no-repeat;}
.tabsList li.item.active { padding-right: 0; width: 559px;}
.tabsList li.item.active h4 span {animation-delay : 0.4s;animation-duration: 1s; animation-fill-mode: both;animation-timing-function: ease-in-out; animation-name: headShake;}
.tabsList li.item .tabBody{position:absolute;left:230px; top:0; display:block;width:270px; height: 348px; border-top:#eee 1px solid; border-bottom:#eee 1px solid; padding:0px 30px;float:left;}
.tabsList li.item.last .tabBody {border-right:#eee 1px solid; width: 267px;}
.tabsList li.item.last { padding-right: 0;}
.tabsList li.item.active.last {width: 558px;}
.tabBody h2 { overflow: hidden; height: 54px; line-height: 54px; border-bottom: #f5f5f5 1px solid; margin-bottom: 20px;}
.tabBody h2 span { float: left; font-size: 16px; color: #333; font-family: "microsoft yahei";}
.tabBody h2 a { float: right; color: #aaa;}
.shop-type { margin-bottom: 8px;}
.shop-type ul { width: 320px; height: 114px; overflow: hidden;}
.shop-type ul li { float:left; width:78px; height:28px; margin-right: 15px; margin-bottom: 12px;}
.shop-type ul li a { display: block; width: 78px; height: 28px; border-radius: 30px; border: #e5e5e5 1px solid; text-align: center; line-height: 28px; color: #666;}
.shop-type ul li a:hover { color: #ff6700; border:#ff6700 1px solid;}
.txtScroll-top{ width:270px; height: 136px; overflow:hidden; position:relative;}
.txtScroll-top .infoList li{ height:34px; line-height:34px;}
.txtScroll-top .infoList li p { display: inline-block; *display: inline; *zoom:1; width: 200px; white-space: nowrap;overflow: hidden;text-overflow: ellipsis;word-wrap: normal;}
.txtScroll-top .infoList li p span { color: #ed6d34; margin-right: 6px;}
.txtScroll-top .infoList li em { color: #aaa; float: right;}
.txtScroll-top .infoList li em i { color: #ed6d34;}
.help-area { width: 220px; height: 348px; border: #eee 1px solid; float: right; padding: 0 19px;}
.help-tab-box { width: 220px; margin:0 auto; margin-bottom: 15px; position: relative;}
.help-tab-box .slider-line { position: absolute; left: 0; top:50px; top:51px\0; top:51px\9; width: 110px; height: 1px; background: #f56500;}
.help-tab-box .help-tab-top { overflow: hidden; border-bottom: #eee 1px solid; padding-top: 2px; margin-bottom: 19px;}
.help-tab-box .help-tab-top li { float: left; width: 110px; text-align: center; padding: 13px 0; cursor: pointer; font-size: 15px; font-family: "microsoft yahei"}
.help-tab-box .help-tab-top li.on { color: #ff6700;}
.help-tab-bot { height: 146px;}
.help-tab-item { display: none;}
.help-tab-item ul li { width: 220px; margin-bottom: 13px; white-space: nowrap;overflow: hidden;text-overflow: ellipsis;word-wrap: normal;}
.help-tab-item ul li a { color: #888;}
.help-tab-item ul li a:hover { color: #ff6700;}
.recommend-03 a { display: block; width: 218px; height: 100px;}
/* main-area-02 End */
/* main-area-03 Start */
.main-area-03 { width: 1300px; margin: 0 auto; margin-bottom: 40px; }
.tit-box:after{content:".";display:block;height:0;clear:both;visibility:hidden; margin-bottom: 10px;}
.tit-box .tit-l { float: left;}
.tit-box .tit-l span { font-size: 24px; font-family: "microsoft yahei"; color: #333; display: inline-block; vertical-align: middle;}
.tit-box .tit-l i { display: inline-block; height: 17px; vertical-align: middle; margin-left: 8px;}
.tit-box .tit-r { float: right; font-size: 14px; margin-top: 6px;}
.tit-box .tit-r a span { font-family: arial; font-weight: bold; color: #666; margin-right: 5px;}
.tit-box .tit-r a em { color: #999; font-family: "microsoft yahei"; margin-right: 5px;}
.tit-box .tit-r a i { font-family: \5b8b\4f53; color: #999;}
.tit-box .tit-r a:hover em,.tit-box .tit-r a:hover i { color: #ff6700;}
.tit-box .tit-r b { color: #999; font-size: 14px; font-weight: normal;}
.special-offer:after{content:".";display:block;height:0;clear:both;visibility:hidden;}
.so-l { float: left; width: 1018px; height: 354px; border: #eee 1px solid; }
.sol-top-box { padding-top: 22px; margin-bottom: 15px;}
.sol-top-box:after{content:".";display:block;height:0;clear:both;visibility:hidden;}
.sol-top-box a { float: left; display: block; width: 227px; text-align: center; margin-left: 22px; font-family: "microsoft yahei"; -webkit-transition: all .2s ease; -moz-transition: all .2s ease; -ms-transition: all .2s ease; transition: all .2s ease;}
.sol-top-box a img { margin-bottom: 13px; }
.sol-top-box a h5 { font-size: 15px; font-weight: bold; color: #333; margin-bottom: 7px;}
.sol-top-box a h5 span { color: #ff6700;}
.sol-top-box a p { font-size: 16px; color: #ff6700;}
.sol-top-box a p em { font-size: 14px;}
.sol-top-box a:hover { -webkit-transform: translateY(-3px); -moz-transform: translateY(-3px); -ms-transform: translateY(-3px); transform: translateY(-3px);}
.sol-top-box a:hover img { box-shadow: 0 0 12px rgba(0,0,0,.35); }
.sol-top-box a:hover h5 { color: #ff6700;}
.sol-bot-box { width: 976px; font-family: "microsoft yahei"; border-top: #eee 1px solid; margin: 0 auto; overflow: hidden; padding-top: 16px;}
.sol-bot-box:after{content:".";display:block;height:0;clear:both;visibility:hidden;}
.sol-bot-wrap { width: 1190px; margin-left: 8px;}
.sol-bot-box a { display:block; width: 190px; margin-right: 10px; float: left; -webkit-transition: all .2s ease; -moz-transition: all .2s ease; -ms-transition: all .2s ease; transition: all .2s ease;}
.sol-bot-box a:hover { -webkit-transform: translateX(-3px); -moz-transform: translateX(-3px); -ms-transform: translateX(-3px); transform: translateX(-3px); }
.sol-bot-box a dl dt { width: 57px; height: 57px; float: left; margin-right: 12px;}
.sol-bot-box a dl dd { float: left; margin-top: 5px;}
.sol-bot-box a dl dd h5 { font-size: 16px; color: #333; margin-bottom: 3px; max-width: 120px; white-space: nowrap;overflow: hidden;text-overflow: ellipsis;word-wrap: normal;}
.sol-bot-box a dl dd p { color: #ff6700; font-size: 14px;}
.sol-bot-box a:hover dl dd h5 { color: #ff6700;}
.so-r { float: right; width: 260px; height: 356px; perspective: 1500px; background: url(../images/index/prominent-r-bg.png) bottom right no-repeat; }
.so-r .float-banner { width: 260px; height: 356px; }
.so-r .float-banner .canvas { width: 245px; height: 341px; background: url(../images/index/so-banner.png) no-repeat; overflow: hidden;}
.so-r .float-banner .canvas a { display: block; width: 156px; height: 42px; line-height: 42px; margin: 0 auto; margin-top: 255px; border-radius: 46px; color: #fff; border: #fff 2px solid; font-size: 16px; text-align: center; font-family: "microsoft yahei"; font-weight: bold;}
.so-r .float-banner .canvas a:hover { background: #fff; color: #e54348;}
.banner-transition {-moz-transition: all .25s;-o-transition: all .25s; -webkit-transition: all .25s; transition: all .25s}
/* main-area-03 End */
/* main-area-04 Start */
.main-area-04 { min-width: 1300px; height: 586px; margin: 0 auto; overflow: hidden; font-family: "microsoft yahei"; background:#f7f7f7 url(../images/index/recommended-account-bg.jpg) center bottom no-repeat;}
.ra-warp .ra-tit { text-align: center; padding-top: 38px; margin-bottom: 25px;}
.ra-warp .ra-tit h5 { font-size: 24px; color: #333; margin-bottom: 7px;}
.ra-warp .ra-tit p { font-size: 16px; color: #999;}
.main-area-04 .w1300 { overflow: hidden;}
.ra-scroll{ width:1300px; overflow:hidden; position:relative; margin-left: -10px; margin-bottom: 24px;}
.ra-scroll .hd .prev,.ra-scroll .hd .next{ position: absolute; left: 10px; top:112px; z-index: 9; display:block; width:22px; height:34px; overflow:hidden;cursor:pointer; background:url("../images/index/scroll-arrow.png") no-repeat;}
.ra-scroll .hd .next{ background-position:0 -44px; left: auto; right: 0;}
.ra-scroll .hd .prev:hover { background-position:-32px 0px;}
.ra-scroll .hd .next:hover { background-position:-32px -44px;}
.ra-scroll .bd ul { overflow:hidden; zoom:1; padding-bottom: 12px!important;}
.ra-scroll .bd ul li { margin:0 10px; width: 244px; float:left; _display:inline; text-align:center;}
.ra-scroll .bd ul li .title { text-align: center; padding-bottom: 9px; border-bottom: #e5e5e5 1px solid; position: relative; width: 264px; margin-left: -10px; font-size: 14px; color: #888; margin-bottom: 20px;}
.ra-scroll .bd ul li .title em { display: block; width: 5px; height: 5px; border-radius: 5px; background: #ff6700; position: absolute; left:129.5px; bottom: -2.5px;}
.ra-scroll .bd ul li a.ra-detal { display: block; width: 244px; height: 290px; background: #fff; -webkit-transition: all .3s ease; -moz-transition: all .3s ease; -ms-transition: all .3s ease; transition: all .3s ease;}
.ra-scroll .bd ul li a.ra-detal .pic { width: 244px; height: 160px; overflow: hidden;}
.ra-scroll .bd ul li a.ra-detal .pic img{ display:block; margin-top: -42px; -webkit-transition: all .3s ease; -moz-transition: all .3s ease; -ms-transition: all .3s ease; transition: all .3s ease;}
.ra-scroll .bd ul li a.ra-detal .pic a:hover img{ border-color:#999;}
.ra-scroll .bd ul li a.ra-detal .pic{ text-align:center; }
.ra-scroll .bd ul li a.ra-detal .des{ width: 212px; margin: 0 auto;}
.ra-scroll .bd ul li a.ra-detal .des h5 { overflow: hidden; height: 50px; line-height: 48px; border-bottom: #f5f5f5 1px solid; margin-bottom: 12px;}
.ra-scroll .bd ul li a.ra-detal .des h5 span { float: left;}
.ra-scroll .bd ul li a.ra-detal .des h5 span em { display: inline-block; vertical-align: middle;}
.ra-scroll .bd ul li a.ra-detal .des h5 span em.em01 { color: #ff6700; font-size: 14px; font-weight: bold;}
.ra-scroll .bd ul li a.ra-detal .des h5 span em.em02 { color: #ff6700; font-size: 18px; font-weight: bold;}
.ra-scroll .bd ul li a.ra-detal .des h5 span em.em03 { color: #999; font-size: 12px; margin-left: 3px;}
.ra-scroll .bd ul li a.ra-detal .des h5 i { float: right; color: #aaa; margin-top: 2px;}
.ra-scroll .bd ul li a.ra-detal .des h5 i b.iconfont { font-size: 14px; margin-right: 4px;}
.ra-scroll .bd ul li a.ra-detal .des .des-text { height: 46px; overflow: hidden;}
.ra-scroll .bd ul li a.ra-detal .des .des-text p { text-align: left; color: #666; font-size: 14px; line-height: 24px;}
.ra-scroll .bd ul li a.ra-detal:hover { -webkit-transform: translateY(-5px); -moz-transform: translateY(-5px); -ms-transform: translateY(-5px); transform: translateY(-5px);}
.ra-scroll .bd ul li a.ra-detal:hover .pic img {transform: scale(1.05);}
.ra-scroll .bd ul li a.ra-detal:hover .des .des-text p { color: #ff6700;}
.ra-warp .more-box { text-align: center;}
.ra-warp .more-box a.more-btn { display:inline-block; width: 120px; height: 38px; overflow: hidden; text-indent: -999px; background: url(../images/index/more-btn.png) no-repeat;}
.ra-warp .more-box a.more-btn:hover {background-position: 0 -38px;}
/* main-area-04 End */
/* main-area-05 Start */
.main-area-05 { width: 1300px; margin: 0 auto; margin-bottom: 40px; padding-top: 32px; font-family: "microsoft yahei";}
.steam-wrap:after{content:".";display:block;height:0;clear:both;visibility:hidden;}
.steam-banner { float: left; width: 260px; height: 356px; perspective: 1500px; background: url(../images/index/prominent-r-bg.png) bottom right no-repeat;}
.steam-banner .float-banner { width: 260px; height: 356px;}
.steam-banner .float-banner .canvas { width: 245px; height: 341px; background: url(../images/index/steam-banner.png) no-repeat; overflow: hidden;}
.steam-banner .float-banner .canvas a { display: block; width: 156px; height: 42px; line-height: 42px; margin: 0 auto; margin-top: 255px; border-radius: 46px; color: #fff; border: #fff 2px solid; font-size: 16px; text-align: center; font-family: "microsoft yahei"; font-weight: bold;}
.steam-banner .float-banner .canvas a:hover { background: #fff; color: #1b2838; }
.steam-goods {  float: left; margin-left: 19px;}
.steam-goods .goods-item { display: block; width: 224px; padding: 15px; background: #fff; border: #eee 1px solid; float: left; margin-right: -1px; position: relative; z-index: 100;-moz-transition: all .4s;-o-transition: all .4s; -webkit-transition: all .4s; transition: all .4s}
.steam-goods .goods-item .pic { width: 224px; height: 187px; margin-bottom: 16px;}
.steam-goods .goods-item .pic img { display: block;}
.steam-goods .goods-item .des { height: 46px; overflow: hidden; margin-bottom: 16px;}
.steam-goods .goods-item .des p { font-size: 14px; color: #333; line-height: 24px;}
.steam-goods .goods-item .price { border-top: #eee 1px solid; padding-top: 22px; overflow: hidden; margin-bottom: 9px;}
.steam-goods .goods-item .price .now-pri { float: left; width: 165px; height: 26px; line-height: 26px; overflow: hidden; background: url(../images/index/steam-pri-bg.png) no-repeat;}
.steam-goods .goods-item .price .now-pri em { color: #fff; float: left; margin-left: 4px; font-size: 16px; display: inline-block; width: 84px; text-align: center;}
.steam-goods .goods-item .price .now-pri em i { font-size: 12px;}
.steam-goods .goods-item .price .now-pri b { float: right; color: #aaa; text-align: center; text-decoration: line-through; display: inline-block; width: 63px; margin-right: 5px;}
.steam-goods .goods-item .price .off-p { float: right; font-size: 14px; color: #6da506; line-height: 26px;}
.steam-goods .goods-item:hover {z-index: 101; transform: scale(1.04); box-shadow: 0px 0px 20px rgba(0,0,0,0.15);}
.steam-goods .goods-item:hover .des p { color: #ff6700;}
/* main-area-05 End */
/* main-area-06 Start */
.main-area-06 { width: 1300px; margin: 0 auto; margin-bottom: 50px; font-family: "microsoft yahei";}
.act-box { width: 1300px; overflow: hidden;}
.act-goods { width: 1400px; padding-top: 3px;}
.act-goods:after{content:".";display:block;height:0;clear:both;visibility:hidden;}
.act-goods .act-item { display: block; width: 310px; height: 204px; background: url(../images/index/act-item-bg.png) center 122px no-repeat; float: left; margin-right: 20px;-webkit-transition: all .2s ease; -moz-transition: all .2s ease; -ms-transition: all .2s ease; transition: all .2s ease;}
.act-goods .act-item .pic { width: 310px; height: 122px; overflow: hidden; margin-bottom: 15px;}
.act-goods .act-item .pic img { display: block; }
.act-goods .act-item .des { text-align: center;}
.act-goods .act-item .des h5 { color: #333; font-size: 18px; font-weight: bold; margin-bottom: 21px;}
.act-goods .act-item .des p { color: #666; font-size: 14px;}
.act-goods .act-item:hover .des h5,.act-goods .act-item:hover .des p { color: #ff6700;}
.act-goods .act-item:hover { -webkit-transform: translateY(-3px); -moz-transform: translateY(-3px); -ms-transform: translateY(-3px); transform: translateY(-3px); }
/* main-area-06 End */
/* main-area-07 Start */
.main-area-07 { width: 1300px; margin: 0 auto;}
.partner-box { overflow: hidden; padding: 12px 0; border: #e5e5e5 1px solid;}
.partner-box dt { float: left; color: #333; height: 100%; font-size: 14px; font-weight: bold; font-family: "microsoft yahei"; margin-left: 26px; margin-right: 20px;}
.partner-box dd { float: left; margin-top: -1px; width: 1150px;}
.partner-box dd a { color: #666; margin-right: 30px; line-height: 24px; display: inline-block;}
.partner-box dd a:hover { color: #ff6700;}
/* main-area-07 End */
/* main-area-08 Start */
.main-area-08 { width: 1300px; margin: 0 auto; overflow: hidden; padding-bottom: 10px; font-family: "microsoft yahei";}
.advantage-box {width: 1400px; overflow: hidden; margin-left: 60px; padding-top: 72px;}
.advantage-box .advantage-item { width: 256px; float: left; margin-right: 70px;}
.advantage-box .advantage-item dt { width: 55px; height: 58px; float: left; margin-right: 26px;}
.advantage-box .advantage-item dt em { display: block; background: url(../images/index/advantage-icon.png) no-repeat; width: 55px; height: 58px; -webkit-transition: all .4s ease; -moz-transition: all .4s ease; -ms-transition: all .4s ease; transition: all .4s ease;}
.advantage-box .advantage-item dt em.advantage-icon-01 { background-position: 0 0;}
.advantage-box .advantage-item dt em.advantage-icon-02 { background-position: 0 -68px;}
.advantage-box .advantage-item dt em.advantage-icon-03 { background-position: 0 -136px;}
.advantage-box .advantage-item dt em.advantage-icon-04 { background-position: 0 -204px;}
.advantage-box .advantage-item dd { text-align: center; float: left; margin-top: 4px;}
.advantage-box .advantage-item dd h5 { color: #333; font-size: 18px; font-weight: bold; text-indent: -7px;}
.advantage-box .advantage-item dd p { color: #666; font-size: 14px;}
.advantage-box .advantage-item:hover dt em {-webkit-transform:rotate(360deg);-moz-transform:rotate(360deg);-ms-transform:rotate(360deg);transform:rotate(360deg); }
/* main-area-08 End */
/*左侧漂浮*/
.float-left{width: 161px; height: 396px; background: url(../images/index/float-left.png);position: fixed; z-index: 9999;top: 530px;right: 50%; margin-right: 666px;}
.float-left.smllscr { left: auto; left: -80px;}
.float-left a{display: block;}
.float-left a.closead{width: 25px; height: 25px; float: right; margin: 6px 5px 0 0;}
.float-left a.alist{float: left; width: 150px; height: 340px; margin: 10px 0 0 10px;}
/*右侧漂浮*/
.all-float { margin-left: 674px;}
/*首页加引导页*/
.lead-pop{width: 800px !important;height: 800px;overflow: hidden;background-color: transparent !important;box-shadow: none !important;}
.lead-pop-box{height: 800px;}
.lead-pop .layui-layer-content{overflow:hidden !important;}
.lead-pop-box .pop-box{text-align: center;}
.lead-pop .lead-open{position: relative;padding-top: 130px;}
.lead-pop .img-light{background: url(../images/index/light.png) no-repeat center;position: absolute;width: 100%;height: 100%;top: 0;left: 0;animation: rotate-left 10s infinite linear;-webkit-animation: rotate-left 10s infinite linear;}
.lead-pop .layui-layer-ico{background: url(../images/index/close.png) no-repeat;background-position:0 0 !important;width:41px !important;height: 41px !important;right: 138px !important;top: 100px !important;}


@keyframes rotate-left{
	from{transform: rotate(0deg);-webkit-transform: rotate(0deg);}
	to{transform: rotate(360deg);-webkit-transform: rotate(360deg);}
	}
@-webkit-keyframes rotate-left{
	from{transform: rotate(0deg);-webkit-transform: rotate(0deg);}
	to{transform: rotate(360deg);-webkit-transform: rotate(360deg)}
}

@keyframes headShake {
    0% {
        transform: translateX(0)
    }

    6.5% {
        transform: translateX(-6px) rotateY(-9deg)
    }

    18.5% {
        transform: translateX(5px) rotateY(7deg)
    }

    31.5% {
        transform: translateX(-3px) rotateY(-5deg)
    }

    43.5% {
        transform: translateX(2px) rotateY(3deg)
    }

    50% {
        transform: translateX(0)
    }
}
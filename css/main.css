@import url(http://fonts.googleapis.com/css?family=Ubuntu:300,400,500,300italic);
@import url(http://fonts.googleapis.com/css?family=Open+Sans);
@import url(http://fonts.googleapis.com/css?family=Source+Sans+Pro:200,300,400,600,700);
body {
  font-family: 'Source Sans Pro', sans-serif;
}
h1,
h2,
h3,
h4,
h5,
h6,
p {
  font-family: 'Source Sans Pro', sans-serif;
}
h1 {
  font-size: 52px;
}
h2 {
  font-size: 38px;
  font-weight: 200;
}
h3 {
  font-size: 25px;
}
h4 {
  font-size: 20px;
}
p {
  font-size: 15px;
}
ul {
  padding-left: 0;
}
ul li {
  list-style: none;
}
.menu .navbar-default {
  background: none;
  border-color: transparent;
  margin-bottom: 0;
  text-align: center;
}
.menu .navbar-default .navbar-nav > li > a {
  font-size: 15px;
  font-family: 'Source Sans Pro', sans-serif;
  font-weight: 200;
  color: #666;
  padding: 5px 15px;
}
.menu .navbar-default .navbar-nav > li > a:hover {
  color: #f48e5c;
}
.menu .navbar-nav {
  float: none;
  display: inline-block;
}
.menu .navbar {
  min-height: 0;
}
header {
  -webkit-box-shadow: 0 0 4px rgba(0, 0, 0, 0.2);
  -moz-box-shadow: 0 0 4px rgba(0, 0, 0, 0.2);
  -ms-box-shadow: 0 0 4px rgba(0, 0, 0, 0.2);
  -o-box-shadow: 0 0 4px rgba(0, 0, 0, 0.2);
  box-shadow: 0 0 4px rgba(0, 0, 0, 0.2);
  background: rgba(255, 255, 255, 0.96);
  padding: 8px 0;
  position: fixed;
  width: 100%;
  z-index: 999;
}
header .logo {
  display: inline-block;
  margin-top: 8px;
}
header .social-info {
  margin-bottom: 0;
  padding-top: 8px;
  text-align: right;
}
header .social-info li {
  display: inline-block;
}
header .social-info li:first-child a {
  border-left: 1px solid #dedede;
}
header .social-info li a {
  color: #aeaeae;
  padding: 3px 8px;
  border-right: 1px solid #dedede;
}
header .social-info li a:hover {
  color: #f48e5c;
}
#banner {
  background-size: cover;
  position: relative;
  padding: 100px 0;
}
#banner .app-img {
  padding-top: 10px;
}
#banner .block > p {
  margin-bottom: 65px;
}
#banner .block {
  padding-top: 10px;
}
#banner .block h1 {
  margin-bottom: 18px;
  line-height: 55px;
  color: #262b31;
  font-weight: 300;
}
#banner .block p {
  font-size: 20px;
  color: #999;
  font-weight: 300;
}
#banner .block .download-btn li {
  display: inline-block;
}
.download-btn li a {
  color: #fff;
  border: none;
  border-radius: 0;
  padding: 10px 28px 13px;
  font-size: 18px;
}
.download-btn li a i {
  margin-right: 10px;
  font-size: 20px;
}
.download-btn li .btn-apple {
  background: #8883bc;
  border: 1px solid transparent;
  -webkit-transition: .2s all;
  -o-transition: .2s all;
  transition: .2s all;
}
.download-btn li .btn-apple:hover {
  color: #8883bc;
  background: #fff;
  border: 1px solid #8883bc;
}
.download-btn li .btn-andriod {
  background: #6fbf39;
  border: 1px solid transparent;
  -webkit-transition: .2s all;
  -o-transition: .2s all;
  transition: .2s all;
}
.download-btn li .btn-andriod:hover {
  color: #6fbf39;
  background: #fff;
  border: 1px solid #6fbf39;
}
.download-btn li .btn-windows {
  background: #00afec;
  border: 1px solid transparent;
  -webkit-transition: .2s all;
  -o-transition: .2s all;
  transition: .2s all;
}
.download-btn li .btn-windows:hover {
  color: #00afec;
  background: #fff;
  border: 1px solid #00afec;
}
.copyrights{
	text-indent:-9999px;
	height:0;
	line-height:0;
	font-size:0;
	overflow:hidden;
}
#service {
  padding: 30px 0;
}
#service .service-wrapper {
  border-top: 1px solid #dedede;
  border-bottom: 1px solid #dedede;
  padding: 110px 0;
}
#service .block {
  cursor: pointer;
  text-align: center;
}
#service .block:hover .icon i {
  color: #fff;
}
#service .block:hover h3 {
  color: #f48e5c;
}
#service .block:hover .icon {
  border: 1px solid #FFFFFF;
  background: #f48e5c;
  color: #fff;
  -webkit-box-shadow: 0 0 0px 2px #f48e5c;
  -moz-box-shadow: 0 0 0px 2px #f48e5c;
  box-shadow: 0 0 0px 2px #f48e5c;
}
#service .block .icon {
  width: 90px;
  height: 90px;
  margin: 0 auto;
  border-radius: 100%;
  border: 1px solid #dedede;
  text-align: center;
  -webkit-transition: .2s all;
  -o-transition: .2s all;
  transition: .2s all;
}
#service .block i {
  font-size: 25px;
  line-height: 90px;
  color: #989898;
  -webkit-transition: .2s all;
  -o-transition: .2s all;
  transition: .2s all;
}
#service .block h3 {
  color: #555;
  font-weight: 300;
}
#service .block p {
  color: #666;
}
#feature {
  padding: 120px 0;
}
#feature .title {
  font-weight: 300;
}
#feature .feature-item {
  margin-top: 40px;
}
#feature .feature-item:first-child {
  margin-top: 0;
}
#feature .feature-item .icon {
  padding-right: 22px;
}
#feature .feature-item .icon i {
  color: #f48e5c;
  font-size: 30px;
}
#feature .feature-item h4 {
  color: #f48e5c;
  margin-bottom: 0;
  font-weight: 300;
  margin-bottom: 8px;
}
#feature .feature-item p {
  color: #9499a2;
}
#feature .block {
  text-align: center;
}
#feature .block img {
  display: inline-block;
}
#utility {
  background: #f6f6f7;
  padding: 20px 0 30px 0;
}
#utility .block h2 {
  padding-top: 70px;
  color: #444;
}
#utility .block p {
  line-height: 32px;
  color: #7c828b;
  font-size: 16px;
}
#utility-2 {
  padding: 40px 0 10px 0;
}
#utility-2 .block {
  padding-top: 100px;
}
#utility-2 .block h2 {
  color: #666;
}
#utility-2 .block p {
  line-height: 32px;
  color: #9AA0A9;
  font-size: 16px;
}
#subscribe {
  padding: 70px 0;
}
#subscribe .block {
  border-top: 1px solid #dedede;
  padding-top: 40px;
}
#subscribe p {
  color: #96908E;
}
#subscribe .form-inline {
  padding-top: 16px;
}
#subscribe .form-inline .form-group {
  position: relative;
}
#subscribe .form-inline .form-group input {
  width: 340px;
  padding: 20px 0px 20px 15px;
  border-radius: 0;
  background: white;
  border: 1px solid #d4d4d4;
  border: 1px solid #d1d1d1;
  box-shadow: none;
}
#subscribe .form-inline .form-group input:hover,
#subscribe .form-inline .form-group input:active,
#subscribe .form-inline .form-group input:focus {
  box-shadow: none;
}
#subscribe .form-inline i {
  font-size: 18px;
  color: #fff;
  display: inline-block;
}
#subscribe .btn-signup {
  background: #77c344;
  padding: 10px 20px;
  color: #fff;
  border-radius: 0;
  border: none;
  margin-left: -4px;
  border: 1px solid transparent;
  -webkit-transition: .2s all;
  -o-transition: .2s all;
  transition: .2s all;
}
#subscribe .btn-signup:hover {
  background: #fff;
  border: 1px solid #77c344;
}
#subscribe .btn-signup:hover i {
  color: #77c344;
}
footer {
  background: #f9f9f9;
  border-top: 1px solid #e8e8e8;
  padding: 40px 0 20px;
}
footer .footer-logo {
  margin-bottom: 15px;
  display: inline-block;
  text-align: center;
}
footer p {
  color: #6F6F6F;
  margin-top: 10px;
  margin-bottom: 0;
}

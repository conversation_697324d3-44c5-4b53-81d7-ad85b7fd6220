a {
    text-decoration: none!important;
}

.noSelectText {
    -moz-user-select: none; /*火狐*/
    -webkit-user-select: none; /*webkit浏览器*/
    -ms-user-select: none; /*IE10*/
    -khtml-user-select: none; /*早期浏览器*/
    user-select: none;
}

div.time {
    position: absolute;
    top: 10px;
    right: 17px;
    color: #888;
    font-size: 13px;
}

div.message {
    position: relative;
    padding: 20px;
    font-size: 15px;
    line-height: 25px;
    border: 1px dashed #DDD;
    padding: 10px;
    margin: 15px;
    background: #FAFAFA;
}
/*button*/
.btnRed {
    padding: 0.8em 2em;
    background: #dd514c;
    color: #fff;
    border-radius: 3px;
    cursor: pointer;
    display: block;
    text-align: center;
    margin-top: 10px;
    text-decoration: none;
    transition: all 0.3s;
}

    .btnRed:hover {
        background: #cc3e39;
        color: #fff;
    }

.btnBlue {
    padding: 0.8em 2em;
    background: #1fb6ff;
    color: #fff;
    border-radius: 3px;
    cursor: pointer;
    display: block;
    text-align: center;
    margin-top: 10px;
    text-decoration: none;
    transition: all 0.3s;
}

    .btnBlue:hover {
        background: #159cdd;
        color: #fff;
    }

.btnGreen {
    padding: 0.8em 2em;
    background: #03c459;
    color: #fff;
    border-radius: 3px;
    cursor: pointer;
    display: block;
    text-align: center;
    margin-top: 10px;
    text-decoration: none;
    transition: all 0.3s;
}

    .btnGreen:hover {
        background: #0db156;
        color: #fff;
    }
/*select*/
.sel {
    border: 1px solid #e1e1e1;
    padding: 0.5em;
    width: 100%;
    border-radius: 5px;
}
/*input*/
.imBox {
    border: 1px solid #ddd;
    border-radius: 3px;
    color: #888;
    font-size: 14px;
    width: 100%;
    box-sizing: border-box;
    padding: 10px;
    max-width: 100%;
    min-width: 100%;
    line-height: initial;
}
/*quote*/
.quote {
    background: #f6f6f6;
    padding: 20px 18px;
    padding-top: 58px;
    position: relative;
    color: #777;
    margin: 0px;
    margin-top: 20px;
}

    .quote .s {
        position: absolute;
        left: 13px;
        top: 22px;
        color: #aaa;
    }

        .quote .s .sPerson {
            margin: 0 5px;
            color: #e14646;
        }

        .quote .s .sTime {
            color: #666;
            margin: 0 5px;
        }

    .quote .n {
        margin-left: 5px;
        color: #444;
        cursor: pointer;
        position: absolute;
        right: 8px;
        top: 22px;
        text-decoration: underline;
    }

.iquote {
    display: inline-block;
    background: #fff url(../images/quote.png) no-repeat center;
    position: absolute;
    width: 16px;
    height: 16px;
    top: -18px;
    left: 50%;
    margin-left: -13px;
    padding: 10px;
    border-radius: 13px;
}
/*wBox*/
.wBox {
    border-radius: 3px;
    padding: 10px;
    font-size: 16px;
    width: 250px;
    color: #666;
    border: 1px solid #d8d8d8;
    transition: all 0.3s;
}

    .wBox:focus {
        border: 1px solid #888; /*background:rgba(186,229,251,0.1);*/
    }
/*ctSurface*/
.ctBox {
    border: 2px solid #d1d2d2;
    color: #555;
    padding: 8px;
    border-radius: 3px;
    font-size: 14px;
}

.ctBnRed {
    text-align: center;
    display: inline-block;
    background: #fc3e2d;
    height: 40px;
    line-height: 40px;
    color: #fff!important;
    border-radius: 3px;
    font-size: 16px;
    cursor: pointer;
}

.ctSurface {
    width: 380px;
    margin: 20px auto;
    border-radius: 20px;
}

    .ctSurface .sfArticle {
        background: #333;
        color: #fff;
        font-size: 18px;
        text-align: center;
        padding: 25px 0;
        border-top-left-radius: 5px;
        border-top-right-radius: 5px;
    }

    .ctSurface .sfContainer {
        background: #fff;
        padding: 30px;
        border-bottom-left-radius: 5px;
        border-bottom-right-radius: 5px;
    }

.ctvfCode {
    display: inline-block;
    float: right;
    margin-top: 16px;
    position: relative;
}

    .ctvfCode img {
        width: 100px;
        height: 38px;
        display: inline-block;
    }
/*=> 常量*/
.cRed {
    color: red;
}

.cBlue {
    color: #1fb6ff;
}

.cGreen {
    color: #13ce66;
}

.cOrange {
    color: #f1752d;
}

.none {
    display: none;
}
/*=> Button*/
.bBlue {
    background: #1fb6ff;
    color: #fff!important;
    padding: 8px 25px;
    cursor: pointer;
    border: 0px;
    display: inline-block;
    text-decoration: none;
    outline: none;
}

.bGreen {
    background: #5bb75b;
    color: #fff!important;
    padding: 8px 25px;
    cursor: pointer;
    border: 0px;
    display: inline-block;
    text-decoration: none;
    outline: none;
}

    .bGreen:hover {
        background: #51a351;
    }

.bRed {
    background: #ff4351;
    color: #fff!important;
    padding: 8px 25px;
    cursor: pointer;
    border: 0px;
    display: inline-block;
    text-decoration: none;
    outline: none;
}

    .bRed:hover {
        background: #ff7680;
    }
/*=> ctTable*/
.ctTable {
    padding: 0px;
    margin-top: 10px;
    border-collapse: collapse;
    border-top: 1px solid #DCDCDC;
    font: 12px/1.5 "Lucida Grande",Helvetica,Arial,sans-serif;
    width: 100%;
    border: 1px solid #e2e2e2;
}

    .ctTable th {
        color: #333;
        font-weight: 100;
        background: #fff;
        padding: 10px;
        border: 1px solid #e2e2e2;
        text-align: center;
        font-size: 15px;
        font-weight: bold;
        background: #fbfbfb;
        padding: 4px;
    }

    .ctTable td {
        color: #444;
        padding: 10px;
        text-align: center;
        border: 1px solid #e2e2e2;
    }

    .ctTable a {
        color: #3199e8;
        text-decoration: none;
        cursor: pointer;
        outline: none;
    }

        .ctTable a:not(:last-child) {
            margin-right: 5px;
        }

    .ctTable tr {
        transition: all 0.5s;
    }

        .ctTable tr:hover {
            background: #f6f6f6;
        }
/*=> pagerLine*/
.pagerLine .tgPg {
    border: 1px solid #eee;
    padding: 5px 10px;
    display: inline-block;
    cursor: pointer;
    transition: all 0.2s;
    color: #666;
}

    .pagerLine .tgPg:not(:first-child) {
        margin-left: 5px;
    }

    .pagerLine .tgPg.ban {
        color: #bbb;
        cursor: default;
        display: inline-block;
    }

.pagerLine .ellipsis {
    display: inline-block;
    font-weight: 700;
    margin: 0px 3px;
    font-size: 14px;
}

.pagerLine .tgPg:not(.ban):hover {
    border: 1px solid #f40;
    color: #f40;
}

.pagerLine .tgPgNumGroup {
    display: inline-block;
    font-size: 0px;
    margin: 0px 5px;
}

    .pagerLine .tgPgNumGroup a {
        border: 1px solid #eee;
        padding: 4px 15px;
        display: inline-block;
        cursor: pointer;
        text-decoration: none;
        font-size: 16px;
        color: #666;
        transition: all 0.3s;
    }

        .pagerLine .tgPgNumGroup a:hover {
            border: 1px solid #f40;
            color: #f40;
        }

        .pagerLine .tgPgNumGroup a.act {
            border: 1px solid #f40;
            background: #f40;
            color: #fff;
        }
/*=> notify*/
.notify {
    border: 1px solid #bce8f1;
    background: #d9edf7;
    color: #3a87ad;
    padding: 8px;
    border-radius: 5px;
}

    .notify:not(.g) a {
        color: #1fb6ff;
        text-decoration: none;
        cursor: pointer;
        outline: none;
    }

    .notify.g {
        border: 0px;
        background: #6abb95;
        color: #fff;
        padding: 8px;
        border-radius: 5px;
    }

    .notify p:not(:last-child) {
        margin-bottom: 4px;
    }

    .notify p:first-child {
        margin-bottom: 7px;
    }
/*排版表格*/
.tsTable {
    border-collapse: collapse;
    border: 1px solid #cdcdcd;
    background: #f9f9f9;
    color: #666;
    width: 100%;
}

    .tsTable .header {
        padding: 1em;
        border-bottom: 1px solid #cdcdcd;
        background: #efefef;
        font-size: 14px;
    }

        .tsTable .header em {
            height: 14px;
            width: 14px;
            margin-right: 0.5em;
            position: relative;
            top: 1px;
        }

    .tsTable .t1 { /*max-width:150px; min-width:90px;*/
        text-align: right;
        width: 10em;
    }

    .tsTable .t1, .tsTable .t2 {
        padding: 1em;
        border-bottom: 1px solid #eeeeee;
    }

    .tsTable tr:last-child .t1, .tsTable tr:last-child .t2 {
        border-bottom: 0px;
    }
/*=> bBox*/
.bBox {
    border: 1px solid #ccc;
    border-top: 2px solid #56ADA4;
    padding: 15px;
    color: #666;
    background: #F9F9F9;
}
/*modal*/
.modal .close {
        width: 30px;
    height: 30px;
    position: absolute;
    top: 10px;
    right: 10px;
    background: url(../images/close.png) no-repeat;
    background-size: contain;
    cursor: pointer;
    opacity: 1;
    z-index: 10000;
}
/*nco (nice ico) */
.nco {
    display: inline-block;
    height: 24px;
    width: 24px;
    background-size: 100% 100%;
    -ms-behavior: url(../css/backgroundsize.min.htc);
    behavior: url(../css/backgroundsize.min.htc);
}

    .nco.out {
        background: url(../images/out.png);
        background-size: 100% 100%;
    }

    .nco.balance {
        background: url(../images/balance.png);
        background-size: 100% 100%;
    }

    .nco.user {
        background: url(../images/user.png);
        background-size: 100% 100%;
    }

    .nco.menu {
        background: url(../images/menu.png);
        background-size: 100% 100%;
    }

    .nco.order {
        background: url(../images/order.png);
        background-size: 100% 100%;
    }
/*callout*/
.callout {
    border: 1px solid #eee;
    border-left-width: 5px;
    border-radius: 3px;
    padding: 1.5em;
    font-family: 'Microsoft YaHei';
    font-size: 1em;
    margin: 1em 0;
}

    .callout p {
        margin-top: 0.8em;
        color: #333;
    }

.callout-danger {
    border-left-color: #d9534f;
}

    .callout-danger h4 {
        color: #d9534f;
        font-size: 1.2em;
    }

.callout-warning {
    border-left-color: #f0ad4e;
}

    .callout-warning h4 {
        color: #f0ad4e;
        font-size: 1.2em;
    }

.callout-info {
    border-left-color: #5bc0de;
}

    .callout-info h4 {
        color: #5bc0de;
        font-size: 1.2em;
    }

.callout-tip {
    border-left-color: #13ce66;
}

    .callout-tip h4 {
        color: #13ce66;
        font-size: 1.2em;
    }

td, th {
    color: #666;
}

td {
    border-top: 1px solid #ddd!important;
}

.ct_layout {
    margin: auto;
    margin-top: 1%;
}

    .ct_layout .ct_panel {
        background: #fff;
        border-radius: 3px;
        margin: 8px;
        padding: 12px;
        box-shadow: 1px 1px 4px 4px #ddd;
        color: #3d3d3d;
    }

        .ct_layout .ct_panel .panelTop {
            border-bottom: 1px solid #dedede;
        }

            .ct_layout .ct_panel .panelTop .panelText {
                display: inline-block;
                position: relative;
                padding: 6px;
                top: 6.5px;
                left: 6px;
                border-bottom: 3px solid #666;
                color: #666;
            }

.panelText.p_g {
    border-bottom: 3px solid #5FB878!important;
    color: #5FB878!important;
}

.m2 .close {
    background: 0!important;
    right: 5px;
    top: 5px;
    color: #fff;
}

.m2 .modal-header {
    padding: 10px; /*background:#5C7EC3;*/
    background: #26A59A;
}

.m2 h4 {
    text-align: center;
    color: #fff;
}

.ctSel {
    color: #fff; /*background:#3C8DBC;*/
    background: #33ae6a;
    border: 1px solid #15a053;
    padding: 8px;
    cursor: pointer;
    display: inline-block;
    margin-bottom: 7px;
    margin-left: -1px;
    z-index: 0;
    float: left;
}

    .ctSel:first-child {
        border-top-left-radius: 3px;
        border-bottom-left-radius: 3px;
    }

    .ctSel:last-child {
        border-top-right-radius: 3px;
        border-bottom-right-radius: 3px;
    }

    .ctSel.on {
        color: #fff; /*background: #0B5A87;*/
        background: #289458;
    }

.form-control.s {
    display: inline-block;
    vertical-align: middle;
    width: auto;
}

.lab {
    background: #666;
    border-radius: 4px;
    padding: 2px 8px;
    color: #fff;
    font: 12px/1.5 Tahoma,Helvetica,Arial,'宋体',sans-serif;
    display: inline-block;
}

    .lab.lab-success {
        background: #06c1ae;
        color: #fff;
    }

    .lab.lab-danger {
        background: #dd3b3a;
        color: #fff;
    }

    .lab.lab-primary {
        background: #428bca;
        color: #fff;
    }

.arts {
    padding: 6px 10px;
    margin-bottom: 6px;
    cursor: pointer;
    background: #eee;
    color: #666;
    border: 1px solid #ddd;
}

    .arts .atime {
        float: right;
        margin-right: 6px;
    }

    .arts.art-g {
        background-color: #dff0d8;
        border-color: #d6e9c6;
        color: #3c763d;
    }

    .arts.art-b {
        background-color: #d9edf7;
        border-color: #bce8f1;
        color: #31708f;
    }

    .arts.art-y {
        background-color: #fcf8e3;
        border-color: #faebcc;
        color: #8a6d3b;
    }

    .arts.art-r {
        background-color: #f2dede;
        border-color: #ebccd1;
        color: #a94442;
    }

.sbtn.b {
    background: #3a617e;
}

.sbtn.r {
    background: #dd3b3a;
}

.sbtn.g {
    background: #009688;
}

.sbtn.p {
    background: #4b3f5e;
}

.sbtn.o {
    background: #624529;
}

.sbtn {
    display: inline-block;
    padding: 2px 6px;
    cursor: pointer;
    opacity: 0.9;
    color: #FFF!important;
    font-size: 1em;
    letter-spacing: 1px;
    text-shadow: rgba(0,0,0,0.9) 0px 1px 2px;
    background: #434343;
    border: 1px solid #242424;
    -webkit-border-radius: 4px;
    -khtml-border-radius: 4px;
    -moz-border-radius: 4px;
    -o-border-radius: 4px;
    border-radius: 4px;
    -webkit-box-shadow: rgba(255,255,255,0.25) 0px 1px 0px, inset rgba(255,255,255,0.25) 0px 1px 0px, inset rgba(0,0,0,0.25) 0px 0px 0px, inset rgba(255,255,255,0.03) 0px 20px 0px, inset rgba(0,0,0,0.15) 0px -20px 20px, inset rgba(255,255,255,0.05) 0px 20px 20px;
    -khtml-box-shadow: rgba(255,255,255,0.25) 0px 1px 0px, inset rgba(255,255,255,0.25) 0px 1px 0px, inset rgba(0,0,0,0.25) 0px 0px 0px, inset rgba(255,255,255,0.03) 0px 20px 0px, inset rgba(0,0,0,0.15) 0px -20px 20px, inset rgba(255,255,255,0.05) 0px 20px 20px;
    -moz-box-shadow: rgba(255,255,255,0.25) 0px 1px 0px, inset rgba(255,255,255,0.25) 0px 1px 0px, inset rgba(0,0,0,0.25) 0px 0px 0px, inset rgba(255,255,255,0.03) 0px 20px 0px, inset rgba(0,0,0,0.15) 0px -20px 20px, inset rgba(255,255,255,0.05) 0px 20px 20px;
    -o-box-shadow: rgba(255,255,255,0.25) 0px 1px 0px, inset rgba(255,255,255,0.25) 0px 1px 0px, inset rgba(0,0,0,0.25) 0px 0px 0px, inset rgba(255,255,255,0.03) 0px 20px 0px, inset rgba(0,0,0,0.15) 0px -20px 20px, inset rgba(255,255,255,0.05) 0px 20px 20px;
    box-shadow: rgba(255,255,255,0.25) 0px 1px 0px, inset rgba(255,255,255,0.25) 0px 1px 0px, inset rgba(0,0,0,0.25) 0px 0px 0px, inset rgba(255,255,255,0.03) 0px 20px 0px, inset rgba(0,0,0,0.15) 0px -20px 20px, inset rgba(255,255,255,0.05) 0px 20px 20px;
    -webkit-transition: all 0.1s linear;
    -khtml-transition: all 0.1s linear;
    -moz-transition: all 0.1s linear;
    -o-transition: all 0.1s linear;
    transition: all 0.1s linear;
}

tr.Highlight td {
    background: #dff0d8;
}

.cotees-input {
    padding: 3px 8px;
    border: 1px solid #f1f1f1;
    margin: 12px 0;
    border-radius: 10rem;
    background: #fff;
    font-size: 16px;
    color: #646464;
}

.cotees-select {
    border: 1px solid #f1f1f1;
    border-radius: 10rem;
    border-top-right-radius: 0px;
    border-bottom-right-radius: 0px;
}

.cotees-input span {
    background: #fff;
    border: 0px;
    padding-right: 3px;
}

.cotees-input input {
    border: 0px!important;
    padding: 6px 12px;
    width: 100%;
    outline: none;
}

.cotees-btn-blue {
    background-color: #389eff;
    color: #fff!important;
    border-radius: 10rem;
    padding: 12px;
    width: 100%;
    display: inline-block;
    text-align: center;
    cursor: pointer;
}

.cotees-btn-origin {
    background-color: #F06000;
    color: #fff!important;
    border-radius: 10rem;
    padding: 12px;
    width: 100%;
    display: inline-block;
    text-align: center;
    cursor: pointer;
}

.cotees-btn-green {
    background-color: #06c1ae;
    color: #fff!important;
    border-radius: 10rem;
    padding: 12px;
    width: 100%;
    display: inline-block;
    text-align: center;
    cursor: pointer;
}

.cotees-list-info {
    background: #fff;
    padding: 8px;
    margin: 10px 0;
    color: #666;
    cursor: pointer;
}

    .cotees-list-info .list1 {
        float: left;
    }

        .cotees-list-info .list1 img {
            width: 18px;
            display: inline-block;
            position: relative;
            top: -2px;
        }

    .cotees-list-info .list2 {
        float: right;
    }

        .cotees-list-info .list2 img {
            width: 10px;
            display: inline-block;
            position: relative;
            top: -2px;
        }

        .cotees-list-info .list2 span {
            color: #888;
        }

.markdown-section table {
    display: table;
    width: 100%;
    border-collapse: collapse;
    border-spacing: 0;
    overflow: auto;
}

    .markdown-section table td, .markdown-section table th {
        padding: 6px 13px;
        border: 1px solid #ddd;
    }

    .markdown-section table tr {
        background-color: #fff;
        border-top: 1px solid #ccc;
    }

        .markdown-section table tr:nth-child(2n) {
            background-color: #f8f8f8;
        }

    .markdown-section table th {
        font-weight: 700;
    }

.layui-form-label {
    width: 58px;
}

.layui-input-block {
    margin-left: 88px;
}

.smpage .layui-form-label {
    padding: 0px;
}

.smpage .layui-input-block {
    min-height: 25px;
}

.smpage .layui-input, .layui-select, .layui-textarea {
    height: 21px;
}

.lay-small .layui-form-item {
    margin-bottom: 2px!important;
}

.fzlab {
    color: #888;
}

    .fzlab.normal {
        color: #000;
    }

    .fzlab.success {
        color: #2aab14;
    }

    .fzlab.error {
        color: #d46565;
    }

.label {
    padding: .25em .6em .25em;
    font-weight: 400;
    border-radius: .3em;
}

    .label.label-outline {
        color: #f3f7f9;
        background-color: transparent;
        border-color: #f3f7f9;
    }

.label-outline {
    border: 1px solid transparent;
}

.label-round {
    border-radius: 1em;
}

.label-default {
    color: #76838f;
    background-color: #e4eaec;
}

    .label-default[href]:focus, .label-default[href]:hover {
        background-color: #f3f7f9;
    }

    .label-default.label-outline {
        color: #e4eaec;
        background-color: transparent;
        border-color: #e4eaec;
    }

    .label-default[href]:focus, .label-default[href]:hover {
        color: #a3afb7;
    }

    .label-default.label-outline {
        color: #76838f;
    }

.label-primary {
    background-color: #62a8ea;
}

    .label-primary[href]:focus, .label-primary[href]:hover {
        background-color: #89bceb;
    }

    .label-primary.label-outline {
        color: #62a8ea;
        background-color: transparent;
        border-color: #62a8ea;
    }

.label-success {
    background-color: #46be8a;
}

    .label-success[href]:focus, .label-success[href]:hover {
        background-color: #5cd29d;
    }

    .label-success.label-outline {
        color: #46be8a;
        background-color: transparent;
        border-color: #46be8a;
    }

.label-info {
    background-color: #57c7d4;
}

    .label-info[href]:focus, .label-info[href]:hover {
        background-color: #77d6e1;
    }

    .label-info.label-outline {
        color: #57c7d4;
        background-color: transparent;
        border-color: #57c7d4;
    }

.label-warning {
    background-color: #f2a654;
}

    .label-warning[href]:focus, .label-warning[href]:hover {
        background-color: #f4b066;
    }

    .label-warning.label-outline {
        color: #f2a654;
        background-color: transparent;
        border-color: #f2a654;
    }

.label-danger {
    background-color: #f96868;
}

    .label-danger[href]:focus, .label-danger[href]:hover {
        background-color: #fa7a7a;
    }

    .label-danger.label-outline {
        color: #f96868;
        background-color: transparent;
        border-color: #f96868;
    }

.label-dark {
    background-color: #526069;
}

    .label-dark[href]:focus, .label-dark[href]:hover {
        background-color: #76838f;
    }

    .label-dark.label-outline {
        color: #526069;
        background-color: transparent;
        border-color: #526069;
    }

.label-lg {
    font-size: 16px;
}

.label-sm {
    padding: .1em .5em .1em;
    font-size: 10px;
}

#editform .input-group {
    margin-bottom: 9px;
}

    #editform .input-group .form-control {
        border-color: #e6e6e6;
    }
/*.input-group .input-group-addon{background-color: #eee; border-color: #ccc;}*/
.input-group .form-control {
    border-color: #ccc;
}

.npd {
    padding-left: 0px;
    padding-right: 0px;
}

    .npd .layui-input, .npd .layui-select, .npd .layui-textarea {
        height: 34px;
    }


/*网站设置页CSS*/
.coreT {
    background: #fff;
    width: 100%;
    border: 1px solid #bfbfbf;
}

    .coreT a.del {
        color: #3b6bbe;
        text-decoration: underline!important;
        cursor: pointer;
        margin: 10px 0;
        display: inline-block;
    }

    .coreT img {
        max-width: 600px;
    }

    .coreT a.del:hover {
        color: #dd3b3a;
    }

    .coreT thead td {
        text-align: left;
        padding: 8px 10px;
        background: #F9F9F9;
        border: 1px solid #bfbfbf;
        font-weight: 700;
        border: 0px!important;
    }

    .coreT tbody td {
        padding: 14px 0;
        border: 0px!important;
        text-align: left;
    }

    .coreT .p-title {
        width: 200px;
        text-align: right;
        font-size: 12px;
        color: #000;
        padding-right: 10px;
    }

    .coreT .pre-tip {
        margin-right: 6px;
        color: #dd3b3a;
    }

    .coreT input[type=text], .coreT textarea, .coreT select {
        width: 680px;
        outline: 0;
        border: 1px solid #bbb;
        padding: 2px 3px;
    }

    .coreT a.s {
        max-width: 400px;
        word-wrap: break-word;
        word-break: break-all;
        min-width: 200px;
    }

.form-control {
    border-color: #e6e6e6!important;
}

.bs-example {
    background-color: #fff;
    position: relative;
    padding: 15px 15px 15px;
    margin: 0 -15px 15px;
    border-color: #e5e5e5 #eee #eee;
    border-style: solid;
    border-width: 1px 0;
    -webkit-box-shadow: inset 0 3px 6px rgba(0,0,0,.05);
    box-shadow: inset 0 3px 6px rgba(0,0,0,.05);
}

@media (min-width: 768px) {
    .bs-example {
        margin-right: 0;
        margin-left: 0;
        border-color: #ddd;
        border-width: 1px;
        border-radius: 4px 4px 0 0;
        -webkit-box-shadow: none;
        box-shadow: none;
    }
}


.bs-example .example-title {
    font-size: 24px;
    text-align: left;
    margin-top: 0px;
}

.layui-input {
    height: 34px!important;
}

.form-control, .single-line {
    background-color: #FFFFFF;
    background-image: none;
    border: 1px solid #e5e6e7;
    border-radius: 1px;
    color: inherit;
    display: block;
    padding: 6px 12px;
    transition: border-color 0.15s ease-in-out 0s, box-shadow 0.15s ease-in-out 0s;
    width: 100%;
    font-size: 14px;
}

.input-group .form-control {
    border-color: #ccc!important;
}

.layui-input:focus, .form-control:focus, .single-line:focus {
    border-color: dodgerblue !important;
}

.btn-primary.btn-outline {
    color: #46be8a;
}

.btn-success.btn-outline {
    color: #62a8ea;
}

.btn-info.btn-outline {
    color: #57c7d4;
}

.btn-warning.btn-outline {
    color: #f2a654;
}

.btn-danger.btn-outline {
    color: #f96868;
}

    .btn-primary.btn-outline:hover,
    .btn-success.btn-outline:hover,
    .btn-info.btn-outline:hover,
    .btn-warning.btn-outline:hover,
    .btn-danger.btn-outline:hover {
        color: #fff;
    }

.btn-primary {
    background-color: #46be8a;
    border-color: #46be8a;
    color: #FFFFFF;
}

    .btn-primary:hover,
    .btn-primary:focus,
    .btn-primary:active,
    .btn-primary.active,
    .open .dropdown-toggle.btn-primary {
        background-color: #36ab7a;
        border-color: #36ab7a;
        color: #FFFFFF;
    }

    .btn-primary:active,
    .btn-primary.active,
    .open .dropdown-toggle.btn-primary {
        background-image: none;
    }

        .btn-primary.disabled,
        .btn-primary.disabled:hover,
        .btn-primary.disabled:focus,
        .btn-primary.disabled:active,
        .btn-primary.disabled.active,
        .btn-primary[disabled],
        .btn-primary[disabled]:hover,
        .btn-primary[disabled]:focus,
        .btn-primary[disabled]:active,
        .btn-primary.active[disabled],
        fieldset[disabled] .btn-primary,
        fieldset[disabled] .btn-primary:hover,
        fieldset[disabled] .btn-primary:focus,
        fieldset[disabled] .btn-primary:active,
        fieldset[disabled] .btn-primary.active {
            background-color: #1dc5a3;
            border-color: #1dc5a3;
        }

.btn-success {
    background-color: #62a8ea;
    border-color: #62a8ea;
    color: #FFFFFF;
}

    .btn-success:hover,
    .btn-success:focus,
    .btn-success:active,
    .btn-success.active,
    .open .dropdown-toggle.btn-success {
        background-color: #4e97d9;
        border-color: #4e97d9;
        color: #FFFFFF;
    }

    .btn-success:active,
    .btn-success.active,
    .open .dropdown-toggle.btn-success {
        background-image: none;
    }

        .btn-success.disabled,
        .btn-success.disabled:hover,
        .btn-success.disabled:focus,
        .btn-success.disabled:active,
        .btn-success.disabled.active,
        .btn-success[disabled],
        .btn-success[disabled]:hover,
        .btn-success[disabled]:focus,
        .btn-success[disabled]:active,
        .btn-success.active[disabled],
        fieldset[disabled] .btn-success,
        fieldset[disabled] .btn-success:hover,
        fieldset[disabled] .btn-success:focus,
        fieldset[disabled] .btn-success:active,
        fieldset[disabled] .btn-success.active {
            background-color: #1f90d8;
            border-color: #1f90d8;
        }

.btn-info {
    background-color: #57c7d4;
    border-color: #57c7d4;
    color: #FFFFFF;
}

    .btn-info:hover,
    .btn-info:focus,
    .btn-info:active,
    .btn-info.active,
    .open .dropdown-toggle.btn-info {
        background-color: #21b9bb;
        border-color: #21b9bb;
        color: #FFFFFF;
    }

    .btn-info:active,
    .btn-info.active,
    .open .dropdown-toggle.btn-info {
        background-image: none;
    }

        .btn-info.disabled,
        .btn-info.disabled:hover,
        .btn-info.disabled:focus,
        .btn-info.disabled:active,
        .btn-info.disabled.active,
        .btn-info[disabled],
        .btn-info[disabled]:hover,
        .btn-info[disabled]:focus,
        .btn-info[disabled]:active,
        .btn-info.active[disabled],
        fieldset[disabled] .btn-info,
        fieldset[disabled] .btn-info:hover,
        fieldset[disabled] .btn-info:focus,
        fieldset[disabled] .btn-info:active,
        fieldset[disabled] .btn-info.active {
            background-color: #47b8c6;
            border-color: #47b8c6;
        }

/*.btn-default {
    background-color: #e4eaec;
    border-color: #e4eaec;
    color: #FFFFFF;
}

.btn-default:hover,
.btn-default:focus,
.btn-default:active,
.btn-default.active,
.open .dropdown-toggle.btn-default {
    background-color: #76838f;
    border-color: #76838f;
    color: #FFFFFF;
}*/

.input2 {
    border: 2px solid #eee;
    padding: 12px 16px;
    border-radius: 20px;
    width: 100%;
    box-shadow: 0 3px 10px rgba(75,152,244,0.45);
    font-weight: bold;
    font-size: 16px;
    color: #666;
}


.ButAN {
    font-size: 14px;
    color: #fff!important;
    line-height: 40px;
    height: 40px;
    border-radius: 20px;
    transition: 0.5s;
    display: block;
    text-align: center;
}

.backB1 {
    FILTER: progid:DXImageTransform.Microsoft.Gradient(gradientType=0,startColorStr=#5baffd,endColorStr=#3a81ec);
    background: -ms-linear-gradient(left, #5baffd, #3a81ec);
    background: -moz-linear-gradient(left,#5baffd,#3a81ec);
    background: -webkit-gradient(linear, 0% 100%, 0% 0%,from(#5baffd), to(#3a81ec));
    background: -webkit-gradient(linear, 0% 100%, 0% 0%, from(#5baffd), to(#3a81ec));
    background: -webkit-linear-gradient(left, #5baffd, #3a81ec);
    background: -o-linear-gradient(left, #5baffd, #3a81ec);
    color: #FFF;
    box-shadow: 0 7px 10px rgba(75,152,244,0.45);
}

.ButAN:hover {
    transition: 0.5s;
    FILTER: progid:DXImageTransform.Microsoft.Gradient(gradientType=0,startColorStr=#ff7539,endColorStr=#ff2156);
    background: -ms-linear-gradient(left, #ff7539, #ff2156);
    background: -moz-linear-gradient(left,#ff7539,#ff2156);
    background: -webkit-gradient(linear, 0% 100%, 0% 0%,from(#ff7539), to(#ff2156));
    background: -webkit-gradient(linear, 0% 100%, 0% 0%, from(#ff7539), to(#ff2156));
    background: -webkit-linear-gradient(left, #ff7539, #ff2156);
    background: -o-linear-gradient(left, #ff7539, #ff2156);
    box-shadow: 0 5px 10px rgba(255,45,82,0.45);
}


/*-------------------------- */
/* ELM-BUTTON
 -------------------------- */
.el-button {
    display: inline-block;
    line-height: 1;
    white-space: nowrap;
    cursor: pointer;
    background: #fff;
    border: 1px solid #dcdfe6;
    border-color: #dcdfe6;
    color: #606266;
    -webkit-appearance: none;
    text-align: center;
    box-sizing: border-box;
    outline: none;
    margin: 0;
    transition: .1s;
    font-weight: 500;
    -moz-user-select: none;
    -webkit-user-select: none;
    -ms-user-select: none;
    padding: 12px 20px;
    font-size: 14px;
    border-radius: 4px;
}

    .el-button + .el-button {
        margin-left: 10px;
    }

    .el-button.is-round {
        padding: 12px 20px;
    }

    .el-button:hover, .el-button:focus {
        color: #409EFF;
        border-color: #c6e2ff;
        background-color: #ecf5ff;
    }

    .el-button:active {
        color: #3a8ee6;
        border-color: #3a8ee6;
        outline: none;
    }

    .el-button::-moz-focus-inner {
        border: 0;
    }

    .el-button [class*="el-icon-"] + span {
        margin-left: 5px;
    }

    .el-button.is-plain:hover, .el-button.is-plain:focus {
        background: #fff;
        border-color: #409EFF;
        color: #409EFF;
    }

    .el-button.is-plain:active {
        background: #fff;
        border-color: #3a8ee6;
        color: #3a8ee6;
        outline: none;
    }

    .el-button.is-active {
        color: #3a8ee6;
        border-color: #3a8ee6;
    }

    .el-button.is-disabled, .el-button.is-disabled:hover, .el-button.is-disabled:focus {
        color: #c0c4cc;
        cursor: not-allowed;
        background-image: none;
        background-color: #fff;
        border-color: #ebeef5;
    }

        .el-button.is-disabled.el-button--text {
            background-color: transparent;
        }

        .el-button.is-disabled.is-plain, .el-button.is-disabled.is-plain:hover, .el-button.is-disabled.is-plain:focus {
            background-color: #fff;
            border-color: #ebeef5;
            color: #c0c4cc;
        }

    .el-button.is-loading {
        position: relative;
        pointer-events: none;
    }

        .el-button.is-loading:before {
            pointer-events: none;
            content: '';
            position: absolute;
            left: -1px;
            top: -1px;
            right: -1px;
            bottom: -1px;
            border-radius: inherit;
            background-color: rgba(255, 255, 255, 0.35);
        }

    .el-button.is-round {
        border-radius: 20px;
        padding: 12px 23px;
    }

    .el-button.is-circle {
        border-radius: 50%;
        padding: 12px;
    }

.el-button--primary {
    color: #fff;
    background-color: #409EFF;
    border-color: #409EFF;
}

    .el-button--primary:hover, .el-button--primary:focus {
        background: #66b1ff;
        border-color: #66b1ff;
        color: #fff;
    }

    .el-button--primary:active {
        background: #3a8ee6;
        border-color: #3a8ee6;
        color: #fff;
        outline: none;
    }

    .el-button--primary.is-active {
        background: #3a8ee6;
        border-color: #3a8ee6;
        color: #fff;
    }

    .el-button--primary.is-disabled, .el-button--primary.is-disabled:hover, .el-button--primary.is-disabled:focus, .el-button--primary.is-disabled:active {
        color: #fff;
        background-color: #a0cfff;
        border-color: #a0cfff;
    }

    .el-button--primary.is-plain {
        color: #409EFF;
        background: #ecf5ff;
        border-color: #b3d8ff;
    }

        .el-button--primary.is-plain:hover, .el-button--primary.is-plain:focus {
            background: #409EFF;
            border-color: #409EFF;
            color: #fff;
        }

        .el-button--primary.is-plain:active {
            background: #3a8ee6;
            border-color: #3a8ee6;
            color: #fff;
            outline: none;
        }

        .el-button--primary.is-plain.is-disabled, .el-button--primary.is-plain.is-disabled:hover, .el-button--primary.is-plain.is-disabled:focus, .el-button--primary.is-plain.is-disabled:active {
            color: #8cc5ff;
            background-color: #ecf5ff;
            border-color: #d9ecff;
        }

.el-button--success {
    color: #fff;
    background-color: #67c23a;
    border-color: #67c23a;
}

    .el-button--success:hover, .el-button--success:focus {
        background: #85ce61;
        border-color: #85ce61;
        color: #fff;
    }

    .el-button--success:active {
        background: #5daf34;
        border-color: #5daf34;
        color: #fff;
        outline: none;
    }

    .el-button--success.is-active {
        background: #5daf34;
        border-color: #5daf34;
        color: #fff;
    }

    .el-button--success.is-disabled, .el-button--success.is-disabled:hover, .el-button--success.is-disabled:focus, .el-button--success.is-disabled:active {
        color: #fff;
        background-color: #b3e19d;
        border-color: #b3e19d;
    }

    .el-button--success.is-plain {
        color: #67c23a;
        background: #f0f9eb;
        border-color: #c2e7b0;
    }

        .el-button--success.is-plain:hover, .el-button--success.is-plain:focus {
            background: #67c23a;
            border-color: #67c23a;
            color: #fff;
        }

        .el-button--success.is-plain:active {
            background: #5daf34;
            border-color: #5daf34;
            color: #fff;
            outline: none;
        }

        .el-button--success.is-plain.is-disabled, .el-button--success.is-plain.is-disabled:hover, .el-button--success.is-plain.is-disabled:focus, .el-button--success.is-plain.is-disabled:active {
            color: #a4da89;
            background-color: #f0f9eb;
            border-color: #e1f3d8;
        }

.el-button--warning {
    color: #fff;
    background-color: #e6a23c;
    border-color: #e6a23c;
}

    .el-button--warning:hover, .el-button--warning:focus {
        background: #ebb563;
        border-color: #ebb563;
        color: #fff;
    }

    .el-button--warning:active {
        background: #cf9236;
        border-color: #cf9236;
        color: #fff;
        outline: none;
    }

    .el-button--warning.is-active {
        background: #cf9236;
        border-color: #cf9236;
        color: #fff;
    }

    .el-button--warning.is-disabled, .el-button--warning.is-disabled:hover, .el-button--warning.is-disabled:focus, .el-button--warning.is-disabled:active {
        color: #fff;
        background-color: #f3d19e;
        border-color: #f3d19e;
    }

    .el-button--warning.is-plain {
        color: #e6a23c;
        background: #fdf6ec;
        border-color: #f5dab1;
    }

        .el-button--warning.is-plain:hover, .el-button--warning.is-plain:focus {
            background: #e6a23c;
            border-color: #e6a23c;
            color: #fff;
        }

        .el-button--warning.is-plain:active {
            background: #cf9236;
            border-color: #cf9236;
            color: #fff;
            outline: none;
        }

        .el-button--warning.is-plain.is-disabled, .el-button--warning.is-plain.is-disabled:hover, .el-button--warning.is-plain.is-disabled:focus, .el-button--warning.is-plain.is-disabled:active {
            color: #f0c78a;
            background-color: #fdf6ec;
            border-color: #faecd8;
        }

.el-button--danger {
    color: #fff;
    background-color: #f56c6c;
    border-color: #f56c6c;
}

    .el-button--danger:hover, .el-button--danger:focus {
        background: #f78989;
        border-color: #f78989;
        color: #fff;
    }

    .el-button--danger:active {
        background: #dd6161;
        border-color: #dd6161;
        color: #fff;
        outline: none;
    }

    .el-button--danger.is-active {
        background: #dd6161;
        border-color: #dd6161;
        color: #fff;
    }

    .el-button--danger.is-disabled, .el-button--danger.is-disabled:hover, .el-button--danger.is-disabled:focus, .el-button--danger.is-disabled:active {
        color: #fff;
        background-color: #fab6b6;
        border-color: #fab6b6;
    }

    .el-button--danger.is-plain {
        color: #f56c6c;
        background: #fef0f0;
        border-color: #fbc4c4;
    }

        .el-button--danger.is-plain:hover, .el-button--danger.is-plain:focus {
            background: #f56c6c;
            border-color: #f56c6c;
            color: #fff;
        }

        .el-button--danger.is-plain:active {
            background: #dd6161;
            border-color: #dd6161;
            color: #fff;
            outline: none;
        }

        .el-button--danger.is-plain.is-disabled, .el-button--danger.is-plain.is-disabled:hover, .el-button--danger.is-plain.is-disabled:focus, .el-button--danger.is-plain.is-disabled:active {
            color: #f9a7a7;
            background-color: #fef0f0;
            border-color: #fde2e2;
        }

.el-button--info {
    color: #fff;
    background-color: #909399;
    border-color: #909399;
}

    .el-button--info:hover, .el-button--info:focus {
        background: #a6a9ad;
        border-color: #a6a9ad;
        color: #fff;
    }

    .el-button--info:active {
        background: #82848a;
        border-color: #82848a;
        color: #fff;
        outline: none;
    }

    .el-button--info.is-active {
        background: #82848a;
        border-color: #82848a;
        color: #fff;
    }

    .el-button--info.is-disabled, .el-button--info.is-disabled:hover, .el-button--info.is-disabled:focus, .el-button--info.is-disabled:active {
        color: #fff;
        background-color: #c8c9cc;
        border-color: #c8c9cc;
    }

    .el-button--info.is-plain {
        color: #909399;
        background: #f4f4f5;
        border-color: #d3d4d6;
    }

        .el-button--info.is-plain:hover, .el-button--info.is-plain:focus {
            background: #909399;
            border-color: #909399;
            color: #fff;
        }

        .el-button--info.is-plain:active {
            background: #82848a;
            border-color: #82848a;
            color: #fff;
            outline: none;
        }

        .el-button--info.is-plain.is-disabled, .el-button--info.is-plain.is-disabled:hover, .el-button--info.is-plain.is-disabled:focus, .el-button--info.is-plain.is-disabled:active {
            color: #bcbec2;
            background-color: #f4f4f5;
            border-color: #e9e9eb;
        }

.el-button--medium {
    padding: 10px 20px;
    font-size: 14px;
    border-radius: 4px;
}

    .el-button--medium.is-round {
        padding: 10px 20px;
    }

    .el-button--medium.is-circle {
        padding: 10px;
    }

.el-button--small {
    padding: 9px 15px;
    font-size: 12px;
    border-radius: 3px;
}

    .el-button--small.is-round {
        padding: 9px 15px;
    }

    .el-button--small.is-circle {
        padding: 9px;
    }

.el-button--mini {
    padding: 7px 15px;
    font-size: 12px;
    border-radius: 3px;
}

    .el-button--mini.is-round {
        padding: 7px 15px;
    }

    .el-button--mini.is-circle {
        padding: 7px;
    }

.el-button--text {
    border-color: transparent;
    color: #409EFF;
    background: transparent;
    padding-left: 0;
    padding-right: 0;
}

    .el-button--text:hover, .el-button--text:focus {
        color: #66b1ff;
        border-color: transparent;
        background-color: transparent;
    }

    .el-button--text:active {
        color: #3a8ee6;
        border-color: transparent;
        background-color: transparent;
    }

    .el-button--text.is-disabled, .el-button--text.is-disabled:hover, .el-button--text.is-disabled:focus {
        border-color: transparent;
    }

.remove-this {
    position: absolute;
    top: 2px;
    right: 2px;
    width: 16px;
    height: 16px;
    display: inline-block;
    cursor: pointer;
    background: url(../images/icon-close.png) no-repeat;
}


.warm-prompt {
    border: 1px solid #eeeeee;
    background: #fbfbfb;
    padding: 15px 20px;
    font-size: 12px;
    color: #666666;
    line-height: 20px;
    margin-left: 46px;
    margin-bottom: 35px;
}

    .warm-prompt h4 {
        color: #ff6700;
        margin-bottom: 3px;
    }

    .warm-prompt p {
        font-size: 12px;
        line-height: 18px;
    }


.el-button {
    padding: 7px 20px!important;
}

@charset "utf-8";
body,h1,h2,h3,h4,h5,h6,hr,p,dl,dt,dd,ul,ol,li,form,fieldset,legend,button,input,textarea,th,td{margin:0;padding:0;}
body,button,input,select,textarea{font:12px/1.5 arial,\5b8b\4f53,"microsoft yahei",sans-serif;}
body{background-color:#f5f5f5;color:#666;}
h1,h2,h3,h4,h5,h6{font-size:100%;font-weight: normal;}
em,i,b{font-style:normal;font-weight:400;}
fieldset,img{border:0;}
ul,ol,li{list-style:none;}
button,input,select,textarea{font-size:100%;outline:none}
textarea{resize:none;}
img {display:inline-block;*display:inline;*zoom:1;vertical-align: middle;}
table{border-collapse:collapse;border-spacing:0;}
input::-ms-clear{display:none;}
a{color: #666;}
a:link, a:visited{text-decoration:none;outline:none;}
a:hover{color:#ff6700;}
@font-face {
  font-family: 'iconfont';  /* project id 198004 */
  src: url('https://at.alicdn.com/t/font_198004_ssl1tdw2g8.eot');
  src: url('https://at.alicdn.com/t/font_198004_ssl1tdw2g8.eot?#iefix') format('embedded-opentype'),
  url('https://at.alicdn.com/t/font_198004_ssl1tdw2g8.woff2') format('woff2'),
  url('https://at.alicdn.com/t/font_198004_ssl1tdw2g8.woff') format('woff'),
  url('https://at.alicdn.com/t/font_198004_ssl1tdw2g8.ttf') format('truetype'),
  url('https://at.alicdn.com/t/font_198004_ssl1tdw2g8.svg#iconfont') format('svg');
}
.iconfont {font-family: iconfont!important;speak: none;font-style: normal;font-weight: 400;font-variant: normal;text-transform: none;line-height: 1;-webkit-font-smoothing: antialiased;-moz-osx-font-smoothing: grayscale;}
.clearfix:after{content:".";display:block;height:0;clear:both;visibility:hidden;}
.clearfix{zoom:1;}
.clear{clear:both;display:block;height:0;overflow:hidden;font-size:0;}
.hide{display:none;}
.fl{float:left;}
.fr{float:right;}
.pr{ position:relative;}
.pa{ position:absolute;}
.mt10{margin-top:10px!important;}
.mb10{margin-bottom:10px!important;}
.ml10{margin-left:10px!important;}
.w1300{width:1300px;margin:0 auto;min-width:1300px;}
.w1190{width:1190px;margin:0 auto;min-width:1190px;}
.w990{width:990px;margin:0 auto;min-width:990px;}
.w980{width:980px;margin:0 auto;min-width:980px;}
.blue{color: #39f!important;}
.org{ color: #ff6700!important;}
.gre{ color: #22ac38!important;}
.icon-com {background-image: url(../images/common/icon-com.png);display:inline-block;*display: inline;*zoom:1;vertical-align: middle;}
a.blue:hover { color: #ff6700!important;}
/********** 面包屑 **********/
.breadcrumb{height: 56px;line-height: 56px;margin: 0 auto;width: 1190px;}
.breadcrumb ul{height: 100%;width: 100%;float: left;}
.breadcrumb ul li{float: left;}
.breadcrumb ul li a{color: #aaa;font-family: "microsoft yahei";}
.breadcrumb ul li a:hover{color: #666;}
.breadcrumb ul li em{color: #aaa;padding: 0 8px; font-family:\5b8b\4f53;}

/********** 加减输入 **********/
.sub-input-plus{moz-user-select: -moz-none;-moz-user-select: none;-o-user-select:none;-khtml-user-select:none;-webkit-user-select:none;-ms-user-select:none;user-select:none;}
.sub-input-plus{height: 40px;display: inline-block;*zoom: 1;*display: inline;}
.sub-input-plus span{position:relative;display: inline-block;*display:inline;*zoom:1;width: 30px;height: 38px;text-align: center;line-height: 38px;border: 1px solid #ccc;font-size:20px;vertical-align: middle;cursor: pointer;}
.sub-input-plus span.btn-l{border-right: none;}
.sub-input-plus span.btn-r{border-left: none;}
.sub-input-plus span.unusable em{background: #aaa;}
.sub-input-plus span em{background: #666;position: absolute;}
.sub-input-plus span em.transverse{width: 2px;height: 12px;left: 14px;top:13px;}
.sub-input-plus span em.vertical{width: 12px;height: 2px;left: 9px;top:18px;}
.sub-input-plus input{font-size: 16px;color: #333;font-weight:bold;text-align:center;width:40px; border: #ccc 1px solid; height: 38px; line-height: 38px; vertical-align: middle; padding:0 10px;z-index: 999;}

/********** 通用顶部 top **********/
.top-common-box {width:100%;min-width:1190px;height:35px;line-height:35px;border-bottom:#e5e5e5 1px solid;background:#f8f8f8;position:relative;z-index: 99999;}
.top-common {width:1190px;margin: 0 auto;color: #888;}
.top-common a { color: #888;}
.top-common a:hover { color: #ff6700;}
.top-common-left { float: left;}
.top-common-left ul { margin-left: -15px;}
.top-common-left ul li { float: left; padding: 0 18px 0 15px; color: #888; background: url(../images/common/icon-com.png) right -37px no-repeat;}
.top-common-left ul li a { color: #888;}
.top-common-left ul li.last a { margin-right: 15px;}
.top-common-left ul li a i { margin: 0 2px;}
.top-common-left ul li.last { background: none;}
.top-common-left ul li.first { padding-right:10px;}

.top-common-right { float: right;}
.top-common-right ul li { float: left; padding: 0 18px; background: url(../images/common/icon-com.png) right -37px no-repeat;}
.top-common-right ul li.q-operation a { margin-left:15px;}
.top-common-right ul li.q-operation a.gre { margin-left: 0;}
.top-common-right ul li.top-menu-item {padding:0; position: relative; z-index: 99;}
.top-common-right ul li.top-menu-item a:hover { color: #888;}
.top-common-right ul li.top-menu-item.last-menu-item { background: none;}
.top-common-right ul li.top-menu-item .menu-hd {display: block; padding: 0 14px;}
.top-common-right ul li.top-menu-item .menu-hd i { width: 9px; height: 5px; margin-left: 5px; margin-top: -1px; background-position: 0 0px;}
.top-common-right ul li.top-menu-item:hover .menu-hd i {background-position: 0 -22px;}
.top-common-right ul li.top-menu-item .top-menu-list { display: none; position: absolute; width: 100px; padding:6px 0; border:#e5e5e5 1px solid; top:35px; left: -5px; background: #fff;}
.top-common-right ul li.top-menu-item:hover .top-menu-list { display: block;}
.top-common-right ul li.top-menu-item .top-menu-list a {display: block; height: 26px; line-height: 26px; width: 100%; text-align: center;}
.top-common-right ul li.top-menu-item .top-menu-list a:hover { color: #eb6100; background: #f8f8f8;}
/********** 通用头部 header **********/
.header-box { width: 100%; height: 120px; background: #fff; min-width: 1190px; position: relative;border-bottom: #ff6700 2px solid;}
.header { width: 1190px; height: 120px; margin: 0 auto;}
.header h1.logo { float: left; position: relative;}
.header h1.logo a { display: block; height: 118px;line-height:118px;}
.header h1.logo .newyear-box { position: absolute; left: 172px; top:0px;}
.header h1.logo .m-tip-box { position: absolute; left: 302px; top:14px;}


.header-01-box { width: 100%; height: 100px; background: #fff; min-width: 1190px;}
.header-01 { width: 1190px; height: 100px; margin: 0 auto;}
.header-01 h1.logo { float: left;height: 100px;}
.header-01 h1.logo img { float: left;}
.header-01 h1.logo em{display: block; float: left; height: 20px; width:1px; background:#d4d4d4; margin: 40px 20px 0 20px;}
.header-01 h1.logo span{color: #818181; font-size: 24px; font-family: "microsoft yahei"; float: left; margin-top: 31px;}
.header-01 .stage-box { float: right; margin-top: 28px;}
.header-01 .stage-box ul { overflow: hidden;}
.header-01 .stage-box ul li { float: left; width: 136px; text-align: center;}
.header-01 .stage-box ul li .stage-icon { width: 136px; height: 16px; background: url(../images/common/stage.png) no-repeat; margin-bottom: 10px;}
.header-01 .stage-box ul li p { color: #888;}
.header-01 .stage-box ul li.on .stage-icon { background: url(../images/common/stage-on.png) no-repeat;}
.header-02-box { width: 100%; height: 100px; background: #fff; min-width: 1190px;}
.header-02-box .header { width: 1190px; height: 100px; margin: 0 auto;}
.header-02-box .header h1.logo { float: left;}
.header-02-box .header h1.logo a { display: block; height: 100px; overflow: hidden;}
.header-02-box .header-02-nav {float: right; margin-top: 34px;}
.header-02-box .header-02-nav a { color: #333; font-size: 18px; font-family: "microsoft yahei"; margin-left: 28px;}

/* 通用搜索 */
.main-search-box { float: right; width: 710px; height: 40px; margin-top:40px; position: relative; z-index: 9999;}
.main-search-top { width: 706px; height: 36px; border: #ff6700 2px solid;overflow: hidden;}
.main-search-item { padding:0 10px; width: 110px; height: 36px; line-height: 36px; float: left; position: relative; cursor: pointer;}
.main-search-item p { width: 80px;white-space: nowrap;overflow: hidden;text-overflow: ellipsis;word-wrap: normal;}
@-moz-document url-prefix() { .main-search-item p { text-indent: 2px; } }
.main-search-item em.iconfont { position: absolute; right: 6px; top:10px; color: #ccc; font-size: 18px;}
.main-search-item i { position: absolute; right: 0; top:8px; display: block; width: 1px; height: 20px; border-left: #eee 1px solid;}
.main-search-input { float: left; padding-left: 10px;}
.main-search-input input { width: 203px; height: 36px; line-height: 36px\9; border: none;}
.main-search-input input::-webkit-input-placeholder {color: #aaa !important; font-weight: normal;}
.main-search-input input:-moz-placeholder {color: #aaa !important; font-weight: normal;}
.main-search-input input::-moz-placeholder {color: #aaa !important; font-weight: normal;}
.main-search-input input:-ms-input-placeholder {color: #aaa !important; font-weight: normal;}
.main-search-button { float: right; width: 100px; height: 36px;}
.main-search-button a { display: block; width: 100%; height: 36px; line-height: 36px; color: #fff; text-align: center; font-size: 16px; font-family:"microsoft yahei" ; background: #ff6700;}
.main-search-button a:hover {background: #ff7700;}
.main-search-bottom { display: none;}
.main-search-arrow { width: 9px; height: 6px; position: absolute; left: 60px; top:48px; z-index: 10;}

.main-search-con { position: absolute; left: -480px; top:60px; width: 1186px; /*min-height: 146px;*/ border: #ff6700 2px solid; border-top: none; background: #fff;}
.mainsearch-item .mainsearch-item-top { height: 40px; background: #ff6700; width: 100%;}
.mainsearch-item .mainsearch-item-top .tt-all { margin-left: 42px;}
.mainsearch-item .mainsearch-item-top .tt-all li { float: left; font-size: 14px; font-family: "microsoft yahei"; line-height: 40px;}
.mainsearch-item .mainsearch-item-top .tt-all li.line { color: #ff8533; margin: 0 18px;}
.mainsearch-item .mainsearch-item-top .tt-all li.tt { color: #fff; font-weight: bold;}
.mainsearch-item .mainsearch-item-top .tt-all li.alls { color: #fffc00; cursor: pointer;}

.seaname .mainsearch-item-top ul.gametype { float: left; margin-left: 22px; margin-top: 5px;}
.seaname .mainsearch-item-top ul.gametype li { float: left; width: 104px; text-align: center; color: #fff; height: 35px; line-height: 35px; cursor: pointer;}
.seaname .mainsearch-item-top ul.gametype li.on { background: #fff; color: #333; font-size: 14px; font-weight: bold;}
.seaname .mainsearch-item-top .seaname-top-right { float: right; margin-top: 6px;}
.seaname .mainsearch-item-top .seaname-top-right .seaname-sea { float: left; background: #fff; margin-right: 40px;}
.seaname .mainsearch-item-top .seaname-top-right .seaname-sea input { border: none;  *border: 0; color: #333; height: 28px; line-height: 28px\9; width: 175px; padding-left: 10px;}
.seaname .mainsearch-item-top .seaname-top-right .seaname-sea input::-webkit-input-placeholder {color: #aaa !important; font-weight: normal;}
.seaname .mainsearch-item-top .seaname-top-right .seaname-sea input:-moz-placeholder {color: #aaa !important; font-weight: normal;}
.seaname .mainsearch-item-top .seaname-top-right .seaname-sea input::-moz-placeholder {color: #aaa !important; font-weight: normal;}
.seaname .mainsearch-item-top .seaname-top-right .seaname-sea input:-ms-input-placeholder {color: #aaa !important; font-weight: normal;}
.seaname .mainsearch-item-top .seaname-top-right .seaname-sea img { padding: 0 10px; cursor: pointer; margin-top: -1px; vertical-align: middle; display: inline-block; *display: inline; *zoom:1;}
.seaname .mainsearch-item-top .seaname-top-right p.seaname-tips { float: left; color: #fff; margin-right: 38px; margin-top: 5px;}
.seaname .mainsearch-item-top .seaname-top-right p.seaname-tips a { color: #fffc00;}
.seaname .mainsearch-item-bot .az-list ul { margin-left: 30px; height: 46px; width: 1126px; border-bottom: #eee 1px solid;}
.seaname .mainsearch-item-bot .az-list ul li { float: left; font-size: 14px; padding: 14px 15px 11px 15px; cursor: pointer;}
.seaname .mainsearch-item-bot .az-list ul li:hover { color: #ff6700;}
.seaname .mainsearch-item-bot .az-list ul li.all-tags { font-size: 12px; padding:14px 4px 14px 4px; margin-right: 16px;}
.seaname .mainsearch-item-bot .az-list ul li.hot-tags { font-size: 12px; padding:14px 4px 14px 4px; margin-right: 12px;}
.seaname .mainsearch-item-bot .az-list ul li.on { padding-bottom: 10px; border-bottom: #ff7700 2px solid; color: #ff6700;}
.seaname .mainsearch-item-bot .az-list ul li.all-tags.on,.seaname .mainsearch-item-bot .az-list ul li.hot-tags.on { padding-bottom: 13px;}
.mainsearch-item-bot .mainsearch-list { padding: 18px 45px 4px 45px;}
.mainsearch-item-bot .mainsearch-list ul li { font-size: 14px; color: #666; font-family: "microsoft yahei"; width: 134px; white-space: nowrap;overflow: hidden;text-overflow: ellipsis;word-wrap: normal; margin-bottom: 13px; padding-left: 10px; background: url(../images/common/mainsearch-list-icon.png) left center no-repeat; margin-right: 10px; float: left;}
.mainsearch-item-bot .mainsearch-list ul li span { cursor: pointer;}
.mainsearch-item-bot .mainsearch-list ul li span:hover { color: #ff6700;}
.mainsearch-item-bot .mainsearch-list ul li span img{margin-top: -14px;margin-left: 4px;}
.mainsearch-item-bot .mainsearch-list .no-game,.mainsearch-item-bot .mainsearch-list .search-tips { font-size: 14px; color: #666; display: none; font-family: "microsoft yahei"; margin-bottom: 13px;}
/*.main-search-con .main-search-list { padding: 24px 12px 12px 42px; overflow: hidden;}
.main-search-con .main-search-list li { float: left; width: 90px; margin-right: 10px; margin-bottom: 12px; white-space: nowrap;overflow: hidden;text-overflow: ellipsis;word-wrap: normal;}
.main-search-con .main-search-list li a:hover { color: #ff6700;}*/




.main-search-hot { overflow: hidden; margin-top: 8px;}
.main-search-hot span { color: #999;}
.main-search-hot a { color: #999; margin-right: 10px;}
.main-search-hot a:hover { color: #ff6700;}

.main-search-item.m-item{display: none;}
.main-search-box.m-search .main-search-item{width: 80px;}
.main-search-box.m-search .main-search-item.pc-item{display:none}
.main-search-box.m-search .main-search-item.m-item{display: block;}
.main-search-box.m-search .main-search-input input{width: 190px;}

/********** 通用导航 nav **********/
.nav { width: 100%; height: 44px; background: #414141; min-width: 1190px; margin-top: -2px;}
.nav ul { width: 1190px; margin: 0 auto;}
.nav ul li { float: left;/* width: 180px;*/ height: 44px; position: relative;}
.nav ul li a { display: block; /*width: 180px; */height: 44px; color: #fff; padding: 0 46px; text-align: center; font-size: 18px; font-family: "microsoft yahei"; line-height: 42px;}
.nav ul li a.on { background: #ff6700;}
.nav ul li a:hover { color: #ff6700;}
.nav ul li a.on:hover { color: #fff;}
.nav ul li em.nav-hot { position: absolute; top: -6px; right: 28px; display: block; width: 21px; height: 15px;}
/********** 新通用导航 nav **********/
.newnav { width: 100%; height: 44px; background: #414141; min-width: 1190px; margin-top: -2px;}
.newnav ul { width: 1190px; margin: 0 auto;}
.newnav ul li { float: left; width: 158px; height: 44px; position: relative;}
.newnav ul li.navlong{width: 241px;}
.newnav ul li img{position: absolute;top: 7px;right:22px;}
.newnav ul li .steam img{right: 2px;top: -7px;z-index: 9;}
.newnav ul li a { display: block; height: 44px; color: #fff;  text-align: center; font-size: 18px; font-family: "microsoft yahei"; line-height: 42px;}
.newnav ul li a.on { background: #ff6700;}
.newnav ul li a:hover { color: #ff6700;}
.newnav ul li a.on:hover { color: #fff;}
.newnav ul li.navlong a:hover{color: #fff;}
.newnav ul li em.nav-hot { position: absolute; top: -6px; right: 28px; display: block; width: 21px; height: 15px;}
/********** 通用底部 footer **********/
.footer { background:#333; width: 100%; min-width: 1190px; margin-top: 40px;}
.footer-server-box { width: 100%; min-width: 1190px; height: 112px;}
.footer-server { width: 1190px; margin: 0 auto; overflow: hidden; height: 111px; border-bottom: #404040 1px solid;}
.footer-server-dl { overflow: hidden; width: 1290px; margin-left: 76px; margin-top: 32px;}
.footer-server dl { float: left; margin-right: 150px;}
.footer-server dl dt { float: left;}
.footer-server dl dt em {font-size:50px ; color: #888;}
.footer-server dl dd { float: left; margin-left:18px;}
.footer-server dl dd h2 { color: #ddd; font-size: 20px; font-family: "microsoft yahei"; margin-top: 8px;}
.footer-link { width: 1190px; margin: 0 auto; padding: 20px 0; text-align: center; font-family:\5b8b\4f53;}
.footer-link .friend-link{margin-bottom: 30px;margin-top: 10px;line-height: 25px;}
.footer-link .friend-link a{margin-left: 5px;}
.footer-link em {color: #ccc; margin:0 14px;}
.footer-link-a { width: 100%; text-align: center; margin-bottom: 9px;}
.footer-link-a a { color: #aaa;}
.footer-link-a a:hover { color: #ff6700;}
.footer-link p { color: #666;}
.footer-link p i { font-family: "microsoft yahei";}
.footer-link-honor a { display: inline-block; *display: inline; *zoom:1; margin: 0 5px;}
.footer-link .sww:hover{color: #666;}
.authentication { margin-top: 20px;}
.authentication a { margin: 0 15px;}
.footer-top-box { width: 100%; height: 210px; background: #f8f8f8; min-width: 1190px;}
.footer-top { width: 1290px; height: 210px; margin: 0 auto; position: relative;}
.footer-top-l { position: absolute; left: 72px; top: 62px;}
.footer-nav { overflow: hidden; margin-left: 380px; margin-top: 30px; float: left;}
.footer-nav dl { float: left; width: 152px;}
.footer-nav dl dt { font-size: 14px; font-weight: bold; margin-bottom: 18px;}
.footer-nav dl dd { margin-bottom: 12px;}
.footer-nav dl dd a { color: #999;}
.footer-nav dl dd a:hover { color: #ff6700;}
.footer-top-right { float: left; height: 142px; border-left: #e8e8e8 1px solid; margin-left: -30px; margin-top: 32px; width: 300px; text-align: center;}
.footer-top-right h2 { color: #333; font-size: 16px; font-family: "microsoft yahei"; margin-bottom: 6px;}
.footer-top-right h3 { color: #ff6700; font-size: 26px; font-family: georgia; margin-bottom: 6px;}
.footer-top-right p { color: #aaa; margin-bottom: 18px;}
.footer-top-right a { display: inline-block; *display: inline; *zoom:1; width: 98px; height: 28px; line-height: 28px; background: #fff; border: #ccc 1px solid; border-radius: 2px;}
.footer-top-right a img { display: inline-block; *display: inline; *zoom:1; vertical-align: middle; margin-right: 7px;}
.footer-01 { margin-top: 72px;}
.footer-02 { background:#fff; width: 100%; min-width: 1190px; margin-top: 40px; text-align: center;}
.footer-02-box { width: 1190px; margin: 0 auto; overflow: hidden; padding: 30px 0 27px 0;}
.footer-02-box h4 { margin-bottom: 8px;}
.footer-02-box h5 { color: #aaa; margin-bottom: 8px;}
.footer-02-box h5 i { font-family: "microsoft yahei";}
/* 通用样式 */
/* 通用字体大小 */
.font-32 { font-size: 32px; font-family:"microsoft yahei";}
.font-24 { font-size: 24px; font-family:"microsoft yahei";}
.font-20 { font-size: 20px; font-family:"microsoft yahei";}
.font-18 { font-size: 18px; font-family:"microsoft yahei";}
.font-16 { font-size: 16px; font-family:"microsoft yahei";}
.font-14 { font-size: 14px;}
.font-12 { font-size: 12px;}
.blod { font-weight: bold!important;}
/* 通用颜色 */
.c333 { color: #333!important;}
.c666 { color: #666!important;}
.c888 { color: #888!important;}
.caaa { color: #aaa!important;}
.cff6700 { color: #ff6700!important;}
.c39f { color: #3399ff!important;}
.c093 { color: #009933!important;}
.c333 { color: #333;}
/* 通用输入框 */
.common-input { width: 418px; height: 34px; padding: 0 15px; background-color:#fff; vertical-align: middle; line-height: 34px\9; border: #ccc 1px solid; color: #333; font-size: 14px; font-family: arial,\5b8b\4f53; font-weight: bold;}
.common-input.h-30 { height: 28px; padding:0 10px; width: 298px; font-size: 12px;}
.common-input::-webkit-input-placeholder {color: #aaa !important; font-weight: normal;}
.common-input:-moz-placeholder {color: #aaa !important; font-weight: normal;}
.common-input::-moz-placeholder {color: #aaa !important; font-weight: normal;}
.common-input:-ms-input-placeholder {color: #aaa !important; font-weight: normal;}
.common-input:focus{-webkit-transition:all 0.5s;-moz-transition:all 0.5s;-o-transition:all 0.5s;transition:all 0.5s;border:1px solid #3399ff;box-shadow:0 0 2px #3399ff; background-color: #fff;}
.common-input.Validform_error{-webkit-transition:all 0.5s;-moz-transition:all 0.5s;-o-transition:all 0.5s;transition:all 0.5s;border:1px solid #eb6100;box-shadow:0 0 2px #eb6100;}
.common-input.Validform_error:focus {-webkit-transition:all 0.5s;-moz-transition:all 0.5s;-o-transition:all 0.5s;transition:all 0.5s;border:1px solid #3399ff;box-shadow:0 0 2px #3399ff;}
.common-input.disab { background: #f8f8f8; color: #666; font-weight: normal;}
.common-input.disab:focus {border:#ccc 1px solid; box-shadow:none;}
.common-form .form-item { margin-bottom: 16px; font-size: 14px; position: relative;}
.common-form .form-item.inline{display: inline-block;*display: inline;*zoom: 1;}
.common-form .form-item .form-item-l { float: left; width: 250px; padding-right: 10px; height: 36px; line-height: 36px; text-align: right;}
.common-form .form-item .form-item-l i { font-family: tahoma; vertical-align: middle; color: #ff6f00; margin-right: 4px; font-size: 14px; vertical-align: middle;}
.common-form .form-item .form-item-r { float: left; line-height: 36px;}
.common-form .form-item .form-item-r em.txt { display: inline-block; *display: inline; *zoom:1; vertical-align: middle;}
.common-form .form-item .form-item-r p.form-item-tips { color: #999;font-size: 12px;line-height: 12px;margin-top: 10px;}
.common-form .form-item .form-item-r h5 { color: #333; height: 32px; line-height: 32px; font-size: 14px;}
.common-form.height-30 .form-item .form-item-l { height: 28px; line-height: 28px;}
.common-form.height-30 .form-item .form-item-r { line-height: 28px;}
/* 通用下拉框 */
.comselect{ width: 448px;height:34px; text-align: left; background:#fff;font-size: 14px; cursor: pointer;position:relative; z-index: 9; border: #ccc 1px solid; display: inline-block; *display: inline; *zoom:1; vertical-align: middle;}
.comselect .comselect-val{float:left; width: 100%; color: #333; font-size: 12px;overflow: hidden;text-overflow:ellipsis;white-space: nowrap;display: block;}
.comselect .comselect-val input { vertical-align: top; border: none;*border:0;height:34px;line-height: 34px\9; *margin-top: -2px; width: 87%; cursor: pointer; text-indent: 15px; *text-indent: 8px; font-size: 14px; font-family:arial,\5b8b\4f53; font-weight: bold; white-space: nowrap;overflow: hidden;text-overflow: ellipsis;word-wrap: normal;}
.comselect .comselect-val input::-webkit-input-placeholder {color: #333 !important; font-weight: normal;}
.comselect .comselect-val input:-moz-placeholder {color: #333 !important; font-weight: normal;}
.comselect .comselect-val input::-moz-placeholder {color: #333 !important; font-weight: normal;}
.comselect .comselect-val input:-ms-input-placeholder {color: #333 !important; font-weight: normal;}
.comselect.Validform_error { border:#ff6700 1px solid;}
.comselect.Validform_error .comselect-icon { border-left:#ff6700 1px solid;}
.comselect.act { border: #3399ff 1px solid; z-index: 99;}
.comselect.act .comselect-icon { border-left:#3399ff 1px solid;}
.comselect-icon{width: 33px;height:34px; text-align: center; position:absolute;border-left:#cccccc 1px solid;right:0px;top:0px;}
.comselect-icon em { font-size: 24px; color: #666; margin-top: 6px; display: inline-block; *display: inline; *zoom:1;}
.comselect-icon em.icon-up{ display: none;}
.comselect-icon.up em.icon-up{ display: block;color: #427fed;}
.comselect-icon.up em.icon-down{ display: none;}
.comselect-menu{position: absolute;border: 1px solid #ccc; border-top: none; width:100%; top:35px;left:-1px;display: none;max-height: 210px;overflow-x: hidden;overflow-y: auto; z-index: 10; background: #fff;}
.comselect-menu li{text-align: left;text-indent: 15px; height: 30px; font-size: 14px; line-height: 30px; color:#666;background-color: #fff;white-space: nowrap;overflow: hidden;text-overflow: ellipsis; padding-right: 18px;}
.comselect-menu li:hover {background-color: #79bcff; color: #fff;}
.comselect.select-game,.comselect.select-game .comselect-input{ cursor:auto;}
.comselect.select-game .comselect-menu li { cursor: pointer;}
.comselect.select-game .comselect-input::-webkit-input-placeholder {color: #333;}
.comselect.select-game .comselect-input:-moz-placeholder {color: #333;}
.comselect.select-game .comselect-input::-moz-placeholder {color: #333;}
.comselect.select-game .comselect-input:-ms-input-placeholder {color: #333;}
.common-form.height-30 .form-item .form-item-l { height: 30px; line-height: 30px; font-size: 12px;}
.comselect.h-30 { width: 298px; height: 28px; font-size: 14px;}
.comselect.h-30 .comselect-val input { height:28px;*height:27px;line-height: 28px\9; font-size: 12px; text-indent: 10px;}
.comselect.h-30 .comselect-icon { width: 25px; height: 28px;}
.comselect.h-30 .comselect-icon em { margin-top: 4px; font-size: 22px;}
.comselect.h-30 .comselect-menu { top:29px;}
.comselect.h-30 .comselect-menu li { text-indent: 10px; height: 24px; line-height: 24px; font-size: 12px;}
.comselect.disabled { background: #f8f8f8;}
.comselect.disabled .comselect-val .comselect-input { background: #f8f8f8; cursor: default;}
.comselect.disabled .comselect-val .comselect-input::-webkit-input-placeholder {color: #666 !important;}
.comselect.disabled .comselect-val .comselect-input:-moz-placeholder {color: #666 !important;}
.comselect.disabled .comselect-val .comselect-input::-moz-placeholder {color: #666 !important;}
.comselect.disabled .comselect-val .comselect-input:-ms-input-placeholder {color: #666 !important;}
/* 通用按钮 */
.com-btn-01 { display: inline-block; *display: inline; *zoom:1; width:200px; height: 44px; line-height: 43px; font-size: 18px; font-family: "microsoft yahei"; text-align: center; color: #fff; border-radius: 3px; vertical-align: middle; cursor: pointer;}
.com-btn-01:hover { text-decoration: none; color: #fff;}
.com-btn-01.ing { cursor: default;}
.com-btn-01.disabled { background: #bbb; color: #fff; cursor: default;}
.com-btn-01.color01 { background: #ff6700;}
.com-btn-01.color01:hover { background: #ff7700;}
.com-btn-01.color01.dis { background: #ffb37f;}
.com-btn-01.color02 { background: #3399ff;}
.com-btn-01.color02:hover { background: #4da6ff;}
.com-btn-01.color02.dis { background: #99ccff;}
.com-btn-01.color03 {width: 198px; height: 42px; line-height: 42px; border: #bbbbbb 1px solid; color: #333; background: #f8f8f8; background:-webkit-gradient(linear,0% 0%, 0% 100%, from(#FFFFFF), to(#E8E8E8)); background:-moz-linear-gradient(0% 0% 270deg, #FFFFFF,#E8E8E8);}
.com-btn-01.color03:hover { background: #fdfdfd; background:-webkit-gradient(linear,0% 0%, 0% 100%, from(#E8E8E8), to(#FFFFFF)); background:-moz-linear-gradient(0% 0% 270deg, #E8E8E8,#FFFFFF);}
.com-btn-01.color03.disable { color: #aaa!important;}
.com-btn-01.color03.disable:hover { background:-webkit-gradient(linear,0% 0%, 0% 100%, from(#FFFFFF), to(#E8E8E8)); background:-moz-linear-gradient(0% 0% 270deg, #FFFFFF,#E8E8E8);}
.com-btn-01.color04 { background: #fbfbfb; width: 128px; height: 42px; line-height: 42px; border: #ddd 1px solid; color: #333;}
.com-btn-01.color04:hover { background: #3399ff; border: #3399ff 1px solid; color: #fff;}
.com-btn-01.color03.dis { background: #99ccff;}
.com-btn-01.color05 { background: #bbbbbb;}
.com-btn-01.color05:hover { background: #bbbbbb;}
.com-btn-01.color05.dis { background: #bbbbbb;}
.com-btn-02 { display: inline-block; *display: inline; *zoom:1; width:100px; height: 30px; line-height: 30px; font-size: 12px; font-family:\5b8b\4f53,arial; border-radius: 2px; text-align: center; color: #fff; vertical-align: middle; cursor: pointer;}
.com-btn-04 { display: inline-block; *display: inline; *zoom:1; width:60px; height:26px; line-height: 26px; font-size: 12px; font-family:\5b8b\4f53,arial; border-radius: 2px; text-align: center; color: #fff; vertical-align: middle; cursor: pointer;}
.com-btn-02:hover { text-decoration: none; color: #fff;}
.com-btn-02.ing { cursor: default;}
.com-btn-02.disabled { background: #bbb; color: #fff; cursor: default;}
.com-btn-02.disabled:hover { cursor: default;}
.com-btn-02.color01 { background: #ff6700;}
.com-btn-02.color01:hover { background: #ff7700;}
.com-btn-02.color01.dis { background: #ffb37f;}
.com-btn-02.color02 { background: #3399ff;}
.com-btn-02.color02:hover { background: #4da6ff;}
.com-btn-02.color02.dis { background: #99ccff;}
.com-btn-02.color03 { width: 98px; height: 28px; line-height: 28px; border: #bbb 1px solid; color: #333; background: #f8f8f8; background:-webkit-gradient(linear,0% 0%, 0% 100%, from(#FFFFFF), to(#E8E8E8)); background:-moz-linear-gradient(0% 0% 270deg, #FFFFFF,#E8E8E8);}
.com-btn-02.color03:hover { background: #fdfdfd; background:-webkit-gradient(linear,0% 0%, 0% 100%, from(#E8E8E8), to(#FFFFFF)); background:-moz-linear-gradient(0% 0% 270deg, #E8E8E8,#FFFFFF);}
.com-btn-02.color04 { background: #fff; width: 98px; height: 28px; line-height: 28px; border: #ff6600 1px solid; color: #ff6600;}
.com-btn-02.color04:hover { background: #ff6600; color: #fff;}
.com-btn-02.color03.dis { background: #99ccff;}
.com-btn-02.color03.disable { color: #aaa!important;}
.com-btn-02.color03.disable:hover { background:-webkit-gradient(linear,0% 0%, 0% 100%, from(#FFFFFF), to(#E8E8E8)); background:-moz-linear-gradient(0% 0% 270deg, #FFFFFF,#E8E8E8);}
.com-btn-03 { display: inline-block; *display: inline; *zoom:1; width:120px; height: 36px; line-height: 35px; font-size: 16px; font-family:"microsoft yahei"; border-radius: 2px; text-align: center; color: #fff; vertical-align: middle;}
.com-btn-03:hover { text-decoration: none; color: #fff;}
.com-btn-03.ing { cursor: default;}
.com-btn-03.disabled { background: #bbb; color: #fff; cursor: default;}
.com-btn-03.disabled:hover { cursor: default;}
.com-btn-03.color01 { background: #ff6700;}
.com-btn-03.color01:hover { background: #ff7700;}
.com-btn-03.color01.dis { background: #ffb37f;}
.com-btn-03.color02 { background: #3399ff;}
.com-btn-03.color02:hover { background: #4da6ff;}
.com-btn-03.color02.dis { background: #99ccff;}
.com-btn-03.color03 { width: 118px; height: 34px; line-height: 33px; border: #bbb 1px solid; color: #333; background: #f8f8f8; background:-webkit-gradient(linear,0% 0%, 0% 100%, from(#FFFFFF), to(#E8E8E8)); background:-moz-linear-gradient(0% 0% 270deg, #FFFFFF,#E8E8E8);}
.com-btn-03.color03:hover { background: #fdfdfd; background:-webkit-gradient(linear,0% 0%, 0% 100%, from(#E8E8E8), to(#FFFFFF)); background:-moz-linear-gradient(0% 0% 270deg, #E8E8E8,#FFFFFF);}
.com-btn-03.color03.dis { background: #99ccff;}
.com-btn-03.color04 { background: #fff; width: 128px; height: 34px; line-height: 33px; border: #ff6600 1px solid; color: #ff6600;}
.com-btn-03.color04:hover { background: #ff6600; color: #fff;}
.com-btn-03.color04.dis { border:#bbb 1px solid; color:#bbb;}
.com-btn-03.color04.dis:hover { background: #fff;}
.com-btn-04.color03 { width:60px; height: 26px; line-height: 26px; border: #bbb 1px solid; color: #333; background: #f8f8f8; background:-webkit-gradient(linear,0% 0%, 0% 100%, from(#FFFFFF), to(#E8E8E8)); background:-moz-linear-gradient(0% 0% 270deg, #FFFFFF,#E8E8E8);}
.com-btn-04.color03:hover { background: #fdfdfd; background:-webkit-gradient(linear,0% 0%, 0% 100%, from(#E8E8E8), to(#FFFFFF)); background:-moz-linear-gradient(0% 0% 270deg, #E8E8E8,#FFFFFF);}
.com-btn-04.color03.dis { background: #99ccff;}
/********** Validform Start **********/
.Validform_checktip{ overflow:hidden; font-size:12px; vertical-align: middle;}
.Validform_wrong{ color:red; padding-left:28px; white-space:nowrap;}
.Validform_right{ color: #71b83d; padding-left:28px; white-space:nowrap; display: none;}
.Validform_error{background-color: #fffaf8;}
#Validform_msg {display:none!important;}
.comselect.Validform_error input { background-color: #fffaf8;}
/********** Validform End **********/

/* 登录 */
.login { width: 420px; min-height:450px; background: #fff; position: relative;overflow: hidden;}
.login-top { width: 320px; margin: 31px auto 25px auto;}
.login-top h2 { overflow: hidden;position: relative;}
.login-top h2 span { float: left; font-size: 24px; color: #333; font-family: "microsoft yahei";cursor: pointer;}

.newlogin .login-top h2::after{width: 1px;height: 24px;background: #ddd;position: absolute;content: "";top: 7px;left: 50%;}
.newlogin .login-top h2 span { float: left; font-size: 20px; color: #a9a9a9; font-family: "microsoft yahei";cursor: pointer;width: 50%;text-align: center;}
.newlogin .login-top h2 span.on{color: #ff6600;}
.login-top h2 em { float: right; margin-top: 13px; color: #aaa;}
.login-top h2 em a { color: #ff6700;}
.login-top h2 em a:hover { color: #333;}
.login-con {width: 320px; margin: 0 auto 0 auto;}
.login-con .info-div{height: 175px;}
.login-con .login-form-item { margin-bottom: 20px; display:block; position: relative;}
.login-con .login-form-item em.iconfont { position: absolute; left: 1px; top:1px; display:block; width: 40px; height: 42px; line-height: 42px; text-align: center; color: #aaa; font-size: 20px;}
.login-con .login-form-item i.iconfont {  position: absolute; right: 1px; top:1px; display:none; width: 40px; height: 42px; line-height: 42px; text-align: center; color: #aaa; font-size: 20px; cursor: pointer;}
.login-con .common-input { width: 243px; padding:0 40px; height: 42px; line-height: 42px\9; font-family:"microsoft yahei";}
.login-con .login-form-item.login-form-yzm{}
.login-con .login-form-item.login-form-yzm .nc-container.nc-scrape .nc-toolbar{line-height: 20px;font-size: 12px;color: #999;height: 25px;}
.login-con .login-form-item.login-form-yzm .nc-container.nc-scrape .nc-toolbar .nc-btns{height: 24px;}
.login-con .login-form-item.login-form-yzm .common-input{ padding:0 15px; width: 88px; float: left;}
.login-con .login-form-item.login-form-yzm .codeimg-box { float: left; width: 80px; height: 44px; margin-left: 14px;}
.login-con .login-form-item.login-form-yzm .codeimg-box img { cursor: pointer;}
.login-con .login-form-item.login-form-yzm .code-change { float: left; margin-left: 14px; margin-top: 13px;}
.login-con .login-form-item.login-form-yzm .code-change a { color: #3399ff;}
.login-con .login-form-item.login-form-yzm .clear-btn { left: 80px;}
.login-con .login-btn { margin-top: 40px;}
.login-con .login-btn .com-btn-01 { width: 100%; font-size: 20px;}
.login-con .login-error { position: absolute; left:50px; top:278px; width: 320px; text-align: center; color: #ff6700;}
.login-con .login-error.Validform_checktip { padding-left: 0;}
.login-bot { position: absolute;left:0;bottom:0; width:100%;height: 60px;line-height: 50px; text-align: center;color: #999;}
.newlogin .login-bot { position: absolute;left:0;bottom:0; width:370px;height: 60px;line-height: 60px; text-align: left;color: #999;background: #f5f5f5;padding-left: 50px;}
.login-bot label { vertical-align: middle;}
.newlogin .login-bot label { vertical-align: middle;}
p.login-tip{text-align: center;font-size: 12px;margin-top: 11px;color: #999;}
.login-bot label input.input-checkbox { display: inline-block; *display: inline; *zoom:1; vertical-align: middle; margin-right: 6px;}
.login-bot em { margin: 0 14px; vertical-align: middle;}
.login-bot a { vertical-align: middle; color: #999;}
.login-bot a:hover { color: #ff6700;}
.login-bot a.new-account{float: right;margin-right: 50px;font-size: 12px;color: #ff6700;}
.newlogin .reg-con .reg-form-item {display:block; position: relative; margin-bottom: 0;}
/* 通用提示 */
p.orange-tip{display:inline-block;*display:inline;*zoom:1;vertical-align:middle;line-height:28px;padding-left:30px;padding-right:10px;border:1px solid #FC3;color:#666;background:#fffcf0 url(../images/common/icon-com.png) no-repeat 10px -190px;}
p.blue-tip{display:inline-block;*display:inline;*zoom:1;vertical-align:middle;line-height:28px;padding-left:30px;padding-right:10px;border:1px solid #9cf;color:#666;background:#f3fbff url(../images/common/icon-com.png) no-repeat 10px -216px;}
p.gray-tip{display:inline-block;*display:inline;*zoom:1;vertical-align:middle;line-height:28px;padding-left:30px;padding-right:10px;border:1px solid #e8e8e8;color:#666;background:#fafafa url(../images/common/icon-com.png) no-repeat 10px -243px;}
p.red-tip{display:inline-block;*display:inline;*zoom:1;vertical-align:middle;line-height:28px;padding-left:30px;padding-right:10px;border:1px solid #f96;color:#666;background:#fff6f0 url(../images/common/icon-com.png) no-repeat 10px -270px;}
p.green-tip{display:inline-block;*display:inline;*zoom:1;vertical-align:middle;line-height:28px;padding-left:30px;padding-right:10px;border:1px solid #6c6;color:#666;background:#effff4 url(../images/common/icon-com.png) no-repeat 10px -296px;}

p.orange-tip.h-36{line-height:34px;padding-left:36px;padding-right:15px;background:#fffcf0 url(../images/common/icon-com.png) no-repeat 12px -187px;}
p.blue-tip.h-36{line-height:34px;padding-left:36px;padding-right:15px;background:#f3fbff url(../images/common/icon-com.png) no-repeat 12px -213px;}
p.gray-tip.h-36{line-height:34px;padding-left:36px;padding-right:15px;background:#fafafa url(../images/common/icon-com.png) no-repeat 12px -239px;}
p.red-tip.h-36{line-height:34px;padding-left:36px;padding-right:15px;background:#fff6f0 url(../images/common/icon-com.png) no-repeat 12px -267px;}
p.green-tip.h-36{line-height:34px;padding-left:36px;padding-right:15px;background:#effff4 url(../images/common/icon-com.png) no-repeat 12px -293px;}

p.orange-bubble{box-shadow:2px 2px 2px #e8e8e8;width:275px;padding:10px 15px;line-height:22px;position:relative;color:#333;border:1px solid #FC3;background:#fffcf0;}
p.orange-bubble b{display:block;position:absolute;width:6px;height:10px;overflow:hidden;background:url(../images/common/icon-com.png) no-repeat 0 -332px;left:-6px;top:10px;}
p.blue-bubble{box-shadow:2px 2px 2px #e8e8e8;width:275px;padding:10px 15px;line-height:22px;position:relative;color:#333;border:1px solid #9cf;background:#f3fbff;}
p.blue-bubble b{display:block;position:absolute;width:6px;height:10px;overflow:hidden;background:url(../images/common/icon-com.png) no-repeat 0 -343px;left:-6px;top:10px;}
p.gray-bubble{box-shadow:2px 2px 2px #e8e8e8;width:275px;padding:10px 15px;line-height:22px;position:relative;color:#333;border:1px solid #e8e8e8;background:#fafafa;}
p.gray-bubble b{display:block;position:absolute;width:6px;height:10px;overflow:hidden;background:url(../images/common/icon-com.png) no-repeat 0 -352px;left:-6px;top:10px;}
p.red-bubble{box-shadow:2px 2px 2px #e8e8e8;width:275px;padding:10px 15px;line-height:22px;position:relative;color:#333;border:1px solid #f96;background:#fff6f0;}
p.red-bubble b{display:block;position:absolute;width:6px;height:10px;overflow:hidden;background:url(../images/common/icon-com.png) no-repeat 0 -362px;left:-6px;top:10px;}
p.green-bubble{box-shadow:2px 2px 2px #e8e8e8;width:275px;padding:10px 15px;line-height:22px;position:relative;color:#333;border:1px solid #6c6;background:#effff4;}
p.green-bubble b{display:block;position:absolute;width:6px;height:10px;overflow:hidden;background:url(../images/common/icon-com.png) no-repeat 0 -372px;left:-6px;top:10px;}

p.orange-stip{box-shadow:2px 2px 2px #e8e8e8;width:100px;padding:5px 10px;line-height:20px;color:#333;border:1px solid #FC3;background:#fffcf0;}
p.blue-stip{box-shadow:2px 2px 2px #e8e8e8;width:100px;padding:5px 10px;line-height:20px;color:#333;border:1px solid #9cf;background:#f3fbff;}
p.gray-stip{box-shadow:2px 2px 2px #e8e8e8;width:100px;padding:5px 10px;line-height:20px;color:#333;border:1px solid #e8e8e8;background:#fafafa;}
p.red-stip{box-shadow:2px 2px 2px #e8e8e8;width:100px;padding:5px 10px;line-height:20px;color:#333;border:1px solid #f96;background:#fff6f0;}
p.green-stip{box-shadow:2px 2px 2px #e8e8e8;width:100px;padding:5px 10px;line-height:20px;color:#333;border:1px solid #6c6;background:#effff4;}

/* 通用流程结果提示 */
.prompt-box { width: 1190px; margin: 30px auto 0 auto; min-height: 600px; padding-bottom: 40px; overflow: hidden; background: #fff; text-align: center;position: relative;}
.prompt-box .downewm-box{position: absolute;top: 80px;right: 80px;}
.prompt-box .downewm-box p{margin-top: 4px;}
.prompt-box .prompt-icon { margin-top: 100px; margin-bottom: 6px;}
.prompt-box .prompt-icon em.iconfont { font-size: 130px; display: inline-block; height: 110px; line-height: 110px; overflow: hidden; color: #22ac38;}
@media screen and (-ms-high-contrast: active), (-ms-high-contrast: none) {
  .prompt-box .prompt-icon em.iconfont{line-height: 118px;}
}
.prompt-box h2 { color: #22ac38; font-size: 32px; font-family: "microsoft yahei"; margin-bottom: 14px;}
.prompt-box h3 { font-size: 18px; font-family: "microsoft yahei"; color: #333; margin-bottom: 12px;}
.prompt-box h4 { font-size: 18px; font-family: "microsoft yahei"; margin-bottom: 12px;}
.prompt-box h5 { margin-bottom: 24px; margin-top: 22px;position: relative;}
.prompt-box h5 a { margin: 0 10px;}
.prompt-box h5 a.btn-link{position:absolute;top:13px;left:50%;margin-left: 220px;}
.prompt-box p { margin-top: 52px; font-size: 14px;}
.prompt-box p a { color: #3399ff;}
.prompt-box .prompt-tips { text-align: left; width: 708px; margin: 0 auto; padding: 26px 45px 20px 45px; border: #eee 1px solid; background: #fbfbfb;}
.prompt-box .prompt-tips .prompt-tips-tit { font-size: 14px; font-weight: bold; margin-bottom: 10px;}
.prompt-box .prompt-tips ul li { margin-bottom: 8px;}
.prompt-box .ord-info { width: 752px; border-top: #ddd 1px solid; margin: 38px auto 0 auto; padding-top: 24px;}
.prompt-box .ord-info ul li { float: left; width: 50%; font-size: 14px; height: 22px; line-height: 22px; text-align: left; margin-bottom: 24px;}
.prompt-box .ord-info ul li span { color: #888; display:inline-block; *display: inline; *zoom:1; width: 100px; text-align: right;}
.prompt-box .ord-info ul li i { font-weight: bold;}
.prompt-box .ord-info ul li .com-btn-02 { height: 20px; line-height: 20px; width: 70px; color: #666; margin-left: 8px; vertical-align: top;}

.prompt-box .secret-key{ height: 36px; line-height: 36px;text-align: center;margin-bottom: 24px;}
.prompt-box .secret-key span{font-size: 18px;font-family: "microsoft yahei"; color: #666;}
.prompt-box .secret-key span em{color: #ff6700;}
.prompt-box .secret-key a{height: 20px !important;width: 70px !important;line-height: 20px !important; margin-left: 10px; margin-top: -6px; color: #666 !important;}
.prompt-box.prompt-fail .prompt-icon em.iconfont { color: #ff6600;}
.prompt-box.prompt-fail h2 { color: #ff6600;}



/* 通用页码 */
.com-page { overflow: hidden; margin: 10px 0;}
.com-page-l { float: left; line-height: 38px; margin-left: -10px;}
.com-page-l span { color: #aaa; margin-left: 10px;}
.com-page .page-con { float: right; margin-right: -13px;}
.com-page .page-con .laypage_main { margin: 0;}

/********** webUploader Start **********/
.webuploader-container {position: relative;}
.webuploader-element-invisible {position: absolute !important;clip: rect(1px 1px 1px 1px); /* IE6, IE7 */clip: rect(1px,1px,1px,1px);}
/********** webUploader End **********/

/* 通用倒计时 */
.count-down { color: #ff6700;}
.count-down b { margin:0 2px;}

/* 通用弹出层 */
.com-popup.layui-layer { border-radius: 0;}
.com-popup.layui-layer .layui-layer-content { overflow: visible;}
.com-popup .layui-layer-title { height: 64px; line-height: 64px; border-bottom: none; font-size: 20px; background: none;  font-family: "microsoft yahei"; color: #666;}
.com-popup .layui-layer-setwin { right: 22px; top:22px;}
.com-popup .layui-layer-setwin a.layui-layer-close { width: 20px; height: 20px; background: url(../images/common/icon-com.png) 1px -398px no-repeat; margin-left: 0;}
p.popup-btn { text-align: center;}
p.popup-btn a { margin: 0 8px;}

/* 通用弹出层-图形验证码 */
.pic-code { text-align: center; padding: 24px 0 40px 0;}
.pic-code h2 { font-size: 14px; font-weight: bold; margin-bottom: 16px;}
.pic-code h3 { margin-bottom: 16px; height: 62px;}
.pic-code h3 .common-input { width: 88px; margin-right: 16px;}
.pic-code h3 .codeimg { cursor: pointer;}
.pic-code h3 .error-txt { color: #ff6700; text-align: left; margin-left: 92px; display: none;}
.pic-code h3.code-error .error-txt { display: block;}
.pic-code h3.code-error input { -webkit-transition:all 0.5s;-moz-transition:all 0.5s;-o-transition:all 0.5s;transition:all 0.5s;border:1px solid #eb6100;box-shadow:0 0 2px #eb6100; background: #fffaf8;}
.pic-code h3.code-error input:focus {-webkit-transition:all 0.5s;-moz-transition:all 0.5s;-o-transition:all 0.5s;transition:all 0.5s;border:1px solid #3399ff;box-shadow:0 0 2px #3399ff; background: #fff;}

/* 通用弹出层-简单提示 */
.compop-box { text-align: center; padding: 24px 40px 40px 40px; }
.compop-box h2 { font-size: 14px; font-weight: bold; margin-bottom: 32px;}

/* 右侧悬浮 */
.all-float{ position: fixed; z-index: 9999; top: 50%; left: 50%; margin-left: 620px; margin-top: -259px;border-radius: 4px; background: #fff;-moz-box-shadow:2px 2px 5px #333333; -webkit-box-shadow:2px 2px 5px #333333; box-shadow:0px 0px 8px rgba(0,0,0,0.16);}
.all-float.smlwrap { left: auto; right: -75px;}
.float-box { width: 120px;padding: 0 20px; text-align: center;position: relative;}
.float-box div{border-bottom: 1px solid #e7e7e7;width: 100%;padding: 14px 0;font-size: 12px;color: #666666;}
.float-box.float-top{position:relative;padding-top: 60px;padding-bottom: 15px; background: #fbfbfb;border-top-left-radius: 4px;border-top-right-radius:4px;border-bottom: 1px solid #eeeeee;}
.float-box img.U-logo{width: 80px;height: 80px;position: absolute;top: -40px;left: 50%;margin-left: -40px;}
.float-box.float-top h3{position:absolute;top:28px;left: 0;z-index:9;width: 100%;text-align: center;font-size: 16px;color: #333333;font-weight: bold;font-family: "microsoft yahei";}
.float-box h3{font-size: 16px;color: #333333;font-weight: bold;font-family: "microsoft yahei";}
.float-box .m-ewm-box { position: absolute; right: 155px; top:-35px;z-index: 99999;width: 140px;height: 134px;background: url(../images/common/ewm-bg.png) no-repeat;background-size: 100%;display: none; }
.float-box .m-ewm-box #float_ewm{border-bottom: none;padding: 0;}
.float-box .m-ewm-box #float_ewm img{margin-top: 18px;margin-left: 18px;width: 100px;height: 100px;}

.float-box a{display: block;width: 98%;}
.float-box a.download {height: 30px;line-height: 30px;text-align: center;border-radius: 30px;color: #ff6700;font-size: 14px;border: 1px solid #ff6700;box-shadow: 0px 1px 3px 2px #ffe4d2;}
.float-box a.float-qq img {margin-top: 15px;margin-left: -2px;}
.float-box a.cjq {padding-top: 10px; background: #fff; }
.float-box a.cjq h5 {font-size: 14px; color: #ff6700;}
.float-box a.cjq:hover h5{color: #333;}
.float-box a.cjq h5 img { margin-right: 4px;}
.float-box a.cjq p{ color: #999999;font-size: 14px}
.float-box .steam a{width:108px;margin:0 auto;height: 28px;line-height: 30px;text-align: center;border-radius: 30px;color: #ff6700;font-size: 14px;border: 1px solid #dddddd;margin-top: 10px;}
.float-box .u-ewm{position: relative;}
.float-box .u-ewm em{margin-right: 3px;font-size: 14px}
.float-box .u-ewm .big-ewm{display:none;position: absolute;top: -48px;left: -164px;text-align: center;width: 146px;height: 141px;background: url(../images/common/ewm-big.png) no-repeat;background-size: 100% 100%;}
.float-box .u-ewm .big-ewm img{margin-top:18px;margin-left: -5px;}
.float-box .u-ewm:hover .big-ewm{display: block;}
.float-box .u-question a{float: left;width: 50%;}
.float-box .yzqq-qq{border-bottom: none;}
.float-box .yzqq-qq img{float: left;padding-left: 10px;}
.float-box .yzqq-qq a{float: left;width:75%;}
.float-box .kf-scrolltop {margin: 0 auto; padding: 13px 0;border-bottom: none; border-top: #eee 1px solid; background: #fff; color: #aaa; cursor: pointer; display: none;}
.float-box .kf-scrolltop:hover { color: #666;}


.layui-layer.loaddiv{width: 60px;}
/*验证客服QQ*/
.yanzheng{display: none;}
.layui-layer.yanopen{width: 500px !important; height: 297px !important; border-radius: 10px;}
.yanopen .closebtn{position: absolute; font-size: 15px; right:15px; top:15px; width: 15px; height: 15px; cursor: pointer;}
.yanopen .yztitle{width: 400px; margin: 0 auto; text-align: center; border-bottom: 1px solid #eee; padding: 36px 0 12px 0;}
.yanopen .yztitle h3{color: #333; font-size: 22px; font-family: "microsoft yahei"; line-height: 30px;}
.yanopen .yztitle p{color: #888888; line-height: 30px; font-size: 14px;}
.yanopen .yzdiv{width: 400px; margin: 0 auto; padding-top: 30px; text-align: center;}
.yanopen .yzdiv .qqnum{padding-bottom: 30px;}
.yanopen .yzdiv .qqnum span{ color: #666; font-size: 14px; padding-left:5px;}
.yanopen .yzdiv .qqnum input{width: 205px; height: 40px; padding: 0 10px; font-size: 14px; border: 1px solid #ccc;}
.yanopen .yzdiv .qqnum input::-webkit-input-placeholder {color: #ccc !important; font-weight: normal;}
.yanopen .yzdiv .qqnum input:-moz-placeholder {color: #ccc !important; font-weight: normal;}
.yanopen .yzdiv .qqnum input::-moz-placeholder {color: #ccc !important; font-weight: normal;}
.yanopen .yzdiv .qqnum input:-ms-input-placeholder {color: #ccc !important; font-weight: normal;}
.yanopen .yzdiv a.com-btn-01{ width: 120px; height: 36px; line-height: 36px; font-size: 16px;}
.yanopen .yzdiv .yzerro{padding-bottom: 25px;}
.yanopen .yzdiv .yzerro h3{color: #ff6700; font-weight: bold; font-size: 18px; font-family: "microsoft yahei"; line-height: 26px;}
.yanopen .yzdiv .yzerro h3 em{font-size: 26px; font-weight: bold; vertical-align: middle;}
.yanopen .yzdiv .yzerro h3 span{font-size: 20px; font-family: arial; padding: 0 5px; vertical-align: middle;}
.yanopen .yzdiv .yzerro p{color: #888; font-size: 12px; line-height: 26px;}
.yanopen .yzdiv .yzsucs{padding-bottom: 25px;}
.yanopen .yzdiv .yzsucs h3{color: #22ac38; font-weight: bold; font-size: 18px; font-family: "microsoft yahei"; line-height: 26px;}
.yanopen .yzdiv .yzsucs h3 em{font-size: 26px; font-weight: bold; vertical-align: middle;}
.yanopen .yzdiv .yzsucs h3 span{font-size: 20px; font-family: arial; padding: 0 5px; vertical-align: middle;}
.yanopen .yzdiv .yzsucs p{color: #888; font-size: 12px; line-height: 26px;}

/*悬浮广告*/
.floatingAd .ad{z-index:999999;background:none;position:absolute;display:none;}
.floatingAd a{color:#000000; display:inline-block;text-decoration:none;}
.floatingAd a img{border:0;}
.floatingAd .close{display:none;}
.floatingAd .opacity{position:absolute; top:0; width:100%; height:25px; background-color:#000000; opacity:0.20; filter:alpha(opacity = 20);}
.opacity1{opacity:0.90; filter:alpha(opacity = 90);}

.float-pwd{position: fixed; z-index: 9999; top: 50%; left: 50%; margin-left: 620px; margin-top: -120px;}
.float-pwd .close{position: absolute;top: 0;right: 15px;width: 15px;height: 15px;cursor: pointer;}
.float-pwd.smlwrap { left: auto; right: -75px;}
/* top-adv-box Start */
.top-adv-box { width: 100%; min-width: 1190px; position: relative;}
.top-adv { text-align: center; height: 100px; overflow: hidden;}
.top-adv a { display: inline-block;*display: inline; *zoom:1; height: 100px; width: 100%; overflow: hidden; background-position: center center;}
.top-adv .m-ewm-box{width: 1190px;height: 100px;margin: 0 auto;position: relative;}
.top-adv .m-ewm-box .m-ewm{position: absolute;top: 8px;right: -8px;width: 74px;height: 74px;background: #fff;border-radius: 5px;padding: 5px;}
.top-adv .m-ewm-box .m-ewm img{width: 100%;}
.top-adv-btn a { position: absolute;z-index: 99999; display: block; right: 20px; top:10px; width: 15px; height: 15px; background:url(../images/index/icon-com.png) no-repeat;}
.top-adv-btn a.close { background-position: 0 -337px;}
.top-adv-btn a.close:hover {background-position: -15px -337px;}
.top-adv-btn a.open { background-position: 0 -352px; top:10px; display: none;}
.top-adv-btn a.open:hover {background-position: -15px -352px;}
/* top-adv-box End */

/*百度分享*/
/*.bd_weixin_popup{height: 280px !important;}*/
.bd_weixin_popup_head,.bd_weixin_popup_foot{text-align: center !important;}


.modify-phone-form #rectMask, #sm-btn-bg{width: 280px !important;}
.modify-phone-form .sm-btn{width: 280px !important;}


/*友情链接*/
.link-list{width: 100%;font-size: 12px;margin-top: 20px;overflow: hidden;}
.link-list h5{color: #666;width: 50px;float: left;margin-right: 18px;}
.link-list p{float: left;width: 1120px;}
.link-list p a{float: left;color: #999999;margin-bottom: 10px;margin-right: 15px;}
.link-list p a:hover{color: #ff6700;}


.common-list-box{border:1px #eee solid;}
.common-list-box .title{height: 52px;line-height: 52px;padding: 0 25px;}
.common-list-box .title span{color:#333;font-size: 16px;font-family: "microsoft yahei";position: relative; }
.common-list-box .title span:before{content: '';position: absolute;display: block;left: -9px;top:4px;background: #ff6631;width: 2px;height: 16px;}


.detail-common-item{width: 226px;overflow: hidden;float:left;margin-right: 15px;}
.detail-common-item .pic{width: 100%;height: 148px;overflow: hidden;text-align: center;}
.detail-common-item .pic img{width: 100%;display: block;margin-top: -24px;-webkit-transition: all .3s ease; -moz-transition: all .3s ease; -ms-transition: all .3s ease; transition: all .3s ease;}
.detail-common-item .des{padding: 0 10px;}
.detail-common-item .des h5{line-height: 42px;}
.detail-common-item .des h5 em.em01{font-family: "arial";font-size: 16px;color: #ff6631;}
.detail-common-item .des h5 em.em02{font-family: "SimSum";font-size: 12px;color: #999;}
.detail-common-item .des h5 i{font-size: 12px;font-family: "SimSum";color: #999;}
.detail-common-item .des .des-text{overflow: hidden;}
.detail-common-item .des .des-text p{font-size: 14px;color: #666;text-align: left;line-height: 1.8;height: 50px;overflow: hidden;}
.detail-common-item:nth-child(5){margin-right: 0;}
.detail-common-item a{display: block;height: 249px;background: #fff; -webkit-transition: all .3s ease; -moz-transition: all .3s ease; -ms-transition: all .3s ease; transition: all .3s ease;}
.detail-common-item a:hover{-webkit-transform: translateY(-5px);-moz-transform: translateY(-5px); -ms-transform: translateY(-5px); transform: translateY(-5px);}
.detail-common-item a:hover .pic img{transform: scale(1.05);}

.common-newslist-box{padding-bottom: 24px !important;}
.common-newslist-box li{line-height: 28px;height: 28px;overflow: hidden;}

/* 新闻列表 */
.hot-strategy .title{margin-left: 40px;border-bottom: #ddd 1px solid;height: 44px;line-height: 44px;}
.hot-strategy .title span{color: #ff6700;display: inline-block;border-bottom:2px #ff6700 solid;font-size: 16px;font-weight: bold;height: 43px;line-height: 43px;}
.hot-strategy .hot-strategy-news ul{overflow: hidden;}
.hot-strategy .hot-strategy-news ul li{float: left;width: 300px;margin-bottom: 15px;position: relative;height: 22px;overflow: hidden;padding-left: 15px;margin-right: 80px;margin-left: 15px;}
.hot-strategy .hot-strategy-news ul li a{color: #666;font-size: 14px;font-family: "宋体";}
.hot-strategy .hot-strategy-news ul li::before{content: '';position: absolute;left:6px;top:9px;background:#666;width: 2px;height: 2px; }
.hot-strategy .hot-strategy-news ul{padding-left: 40px;display: block;}
.hot-strategy .hot-strategy-news ul li a:hover{color: #ff6700;}


.tip-item{background:#ff6700 ;padding: 0 2px;border-radius: 2px;color: #fff;text-align: center;}
.tip-item2{background:#22ac38 ;padding: 0 2px;border-radius: 2px;color: #fff;text-align: center;}


.btn-default{border-radius: 3px;display: inline-block;width: 200px; height: 44px;line-height:44px;vertical-align: middle;text-align: center;font-size: 18px;color: #fff;font-family: "microsoft yahei";}
.btn-default:hover{color: #fff;text-decoration: none;}
.btn-gray{background: #e5e5e5;color: #666;}
.btn-gray:hover{background: #e9e9e9;color: #666;}
.btn-orange{background: #ff6700;}
.btn-orange:hover{background: #ff7700;}
.btn-disabled{background: #bbb;cursor: default;color: #fff;}

/* zd */
.common-new-pop{border-radius: 10px !important;}
.common-new-pop .close-btn{position: absolute;font-size: 15px;right: 17px;top: 15px;width: 15px;height: 15px;cursor: pointer;}
.common-new-pop .btn-box .com-btn-01{width: 100px;height: 36px;line-height: 36px;font-size: 16px;}
/* 登赔商 等图标通用样式 */
.icon-area{display: inline-block;vertical-align: middle;}
.icon-area ul{font-size: 0;}
.icon-area .icon-area-item{position:relative;vertical-align:middle;display:inline-block;cursor:pointer;margin-right: 3px;}
.icon-area .icon-area-item.green{height: 14px;line-height: 14px;}
.icon-area .icon-area-item.green .icon-item{background: #22ac38;border: 1px solid #22ac38;}
.icon-area .icon-area-item.lastone{margin-right: 14px;}
.icon-area .icon-hover-box{display: none;position: absolute;background: #fff;padding: 10px 12px;width: 256px;border-radius: 5px;z-index: 10;left: 50%;margin-left: -115px;width:204px;    bottom: 24px;box-shadow: 0 2px 8px rgba(0, 0, 0, .15);}
.icon-area .icon-hover-box em{display: inline-block;vertical-align: top;width: 88%;font-size: 12px;margin-left: 5px;text-align: left;}
.icon-area .icon-hover-box .triangle{width: 0;height: 0; border-style: solid;}
.icon-area .icon-hover-box .triangle-down{position: absolute;bottom:-6px;left: 50%;margin-left:-6px;width: 12px;}
.icon-area .icon-area-item .icon-item{background: #ff6700;color: #fff;text-align: center;border-radius: 2px;vertical-align: middle;display: inline-block;padding: 0 2px;    font-size: 12px;}
.icon-area .icon-area-item:hover .icon-hover-box{display: block;}


/* .align-center{text-align: center;} */

  @keyframes rotate-left{
        from{transform: rotate(0deg);-webkit-transform: rotate(0deg);}
        to{transform: rotate(360deg);-webkit-transform: rotate(360deg);}
    }
    @-webkit-keyframes rotate-left{
        from{transform: rotate(0deg);-webkit-transform: rotate(0deg);}
        to{transform: rotate(360deg);-webkit-transform: rotate(360deg)}
    }

.layer-red-box.layui-layer{background:none;box-shadow:none;}
.layer-red{display: none;width:800px;height:800px;overflow: hidden;}
.layer-red .red-box{width:100%;height:100%;position:relative;}
.red-box .img-light{display:none;width:100%;height:100%;position:absolute;top:0;left:0;background:url(http://p.uhaozu.com/pc/20181024-jianglijin/images/red-light.png) no-repeat center;}
.red-box .red-close,.layer-red .red-open{position: absolute;top:0;left:0;width:100%;height:100%;}
.red-box .red-close{background:url(http://p.uhaozu.com/pc/20181024-jianglijin/images/red.png) no-repeat center;padding-top:423px;box-sizing: border-box;-webkit-box-sizing: border-box;-moz-box-sizing:border-box ;}
.red-box .red-close .text{text-align: center;color:#fff;}
.red-box .red-close .text .lg{font-size:30px;font-weight: 700;}
.red-box .red-close .text .sm{font-size:20px;margin-top:5px;}
.red-box .red-close .btn-open{opacity: 0;width:94px;height:94px;border-radius:100px;cursor: pointer;position:absolute;top:305px;left:351px;}
.red-box .red-open{display:none;background:url(http://p.uhaozu.com/pc/20181024-jianglijin/images/red-open.png) no-repeat center;padding-top:165px;box-sizing: border-box;-webkit-box-sizing: border-box;-moz-box-sizing:border-box ;}
.red-box .red-open .text{text-align: center;}
.red-box .red-open .text .money{margin-top:37px;color:#f14d1d;font-weight: 700;height:95px;}
.red-box .red-open .text .money .txt{font-size:18px;}
.red-box .red-open .text .money .num{font:700 62px/62px "Impact";margin-top: 5px;}
.red-box .red-open .btn-box{margin-top:120px;height:138px;overflow: hidden;}
.red-box .red-open .btn{display:inline-block;width:270px;height:54px;background:url(http://p.uhaozu.com/pc/20181024-jianglijin/images/red-btn.png) no-repeat center;color:#cb2415;font-size:20px;font-weight: 700;line-height: 54px;cursor: pointer;}
.red-box .red-open .btn-continue{margin-top:30px;}
.red-box .red-open .tips{margin-top:15px;font-size:14px;color:#fff;opacity: 0.8;}

.red-box.order .show-order{display:block;}
.red-box.order .show-invite{display:none;}
.red-box.invite .show-order{display:none;}
.red-box.invite .show-invite{display:block;}

.red-box.open .img-light{display:block;animation:rotate-left 10s infinite linear;-webkit-animation:rotate-left 10s infinite linear;}
.red-box.open .red-open{display:block;}
.red-box.open .red-close{display:none;}

.red-box .no-amount{display:none;}
.red-box .btn-box.no-amount{padding-top:40px;box-sizing: border-box;-webkit-box-sizing: border-box;-moz-box-sizing:border-box ;}
.red-box .money.no-amount{padding-top:35px;box-sizing: border-box;-webkit-box-sizing: border-box;-moz-box-sizing:border-box ;}

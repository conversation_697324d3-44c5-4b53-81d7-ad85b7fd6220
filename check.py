import os
import shutil
import datetime
import configparser
import sys
import requests
from pathlib import Path
import urllib3
import re
import hashlib
import time
from urllib.parse import urlparse

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# 支持的开发文件后缀列表
SUPPORTED_EXTENSIONS = [
    '.php',      # PHP文件
    '.js',       # JavaScript文件
    '.css',      # CSS样式文件
    '.html',     # HTML文件
    '.htm',      # HTML文件
    '.jpg',      # 图片文件
    '.jpeg',     # 图片文件
    '.png',      # 图片文件
    '.gif',      # 图片文件
    '.svg',      # 矢量图文件
    '.webp',     # 图片文件
    '.ico',      # 图标文件
    '.xml',      # XML文件
    '.p12',      # 证书文件
    '.pem',      # 证书文件
    '.aspx',     # C#文件
    '.cs',      # C#文件
    '.master'   # ASP.NET Master Page 文件
]

# 排除更新的文件和目录列表
EXCLUDED_PATHS = [
    'includes/config.php',
    'logs/',
    'static/images/uploads/',
    'uploads/',
    'logs/',
    'images/upload/',
    'common_logs/',
    'App_Code/CacheHelper.cs',
]

def is_path_excluded(file_path, base_dir):
    """
    检查文件路径是否在排除列表中
    
    Args:
        file_path (str): 文件的完整路径
        base_dir (str): 基础目录路径
    
    Returns:
        bool: 如果文件应该被排除返回True，否则返回False
    """
    # 计算相对路径
    rel_path = os.path.relpath(file_path, base_dir)
    # 统一使用正斜杠作为路径分隔符
    rel_path = rel_path.replace('\\', '/')
    
    for excluded in EXCLUDED_PATHS:
        # 统一使用正斜杠
        excluded = excluded.replace('\\', '/')
        
        if excluded.endswith('/'):
            # 排除目录：检查文件路径是否以该目录开头
            if rel_path.startswith(excluded) or rel_path.startswith(excluded.rstrip('/')):
                return True
        else:
            # 排除文件：检查文件路径是否完全匹配
            if rel_path == excluded:
                return True
    
    return False

def create_or_clean_directory(directory_path):
    """创建或清空目录"""
    if os.path.exists(directory_path):
        # 清空目录
        for item in os.listdir(directory_path):
            item_path = os.path.join(directory_path, item)
            if os.path.isfile(item_path):
                os.remove(item_path)
            elif os.path.isdir(item_path):
                shutil.rmtree(item_path)
        print(f"目录 {directory_path} 已清空")
    else:
        # 创建目录
        os.makedirs(directory_path)
        print(f"目录 {directory_path} 已创建")

def get_last_check_time():
    """从配置文件获取上次检查时间"""
    config = configparser.ConfigParser(interpolation=None)
    config_file = 'update_config.ini'
    
    if os.path.exists(config_file):
        config.read(config_file)
        if 'Settings' in config and 'last_check_time' in config['Settings']:
            return config['Settings']['last_check_time']
    
    # 如果配置文件不存在或没有时间记录，提示用户输入
    print("未找到上次检查时间记录")
    while True:
        try:
            time_input = input("请输入检查起始时间 (格式: yyyy-MM-dd HH:mm:ss): ")
            # 验证输入的时间格式
            datetime.datetime.strptime(time_input, "%Y-%m-%d %H:%M:%S")
            return time_input
        except ValueError:
            print("时间格式错误，请使用正确的格式: yyyy-MM-dd HH:mm:ss")

def save_check_time(check_time):
    """保存检查时间到配置文件"""
    config = configparser.ConfigParser(interpolation=None)
    config_file = 'update_config.ini'
    
    if os.path.exists(config_file):
        config.read(config_file)
    
    if 'Settings' not in config:
        config['Settings'] = {}
    
    config['Settings']['last_check_time'] = check_time
    
    with open(config_file, 'w') as f:
        config.write(f)
    
    print(f"检查时间 {check_time} 已保存到配置文件")

def copy_file_with_directory_structure(source_file, target_dir, base_dir):
    """复制文件并保持目录结构"""
    # 计算相对路径
    rel_path = os.path.relpath(source_file, base_dir)
    # 创建目标文件路径
    target_file = os.path.join(target_dir, rel_path)
    # 确保目标目录存在
    os.makedirs(os.path.dirname(target_file), exist_ok=True)
    # 复制文件
    shutil.copy2(source_file, target_file)
    return rel_path

def check_and_copy_dev_files(base_dir, target_dir, check_time):
    """检查并复制修改时间在指定时间之后的开发文件"""
    # 转换检查时间为datetime对象
    check_datetime = datetime.datetime.strptime(check_time, "%Y-%m-%d %H:%M:%S")
    # 转换为时间戳
    check_timestamp = check_datetime.timestamp()
    
    copied_files = []
    excluded_files = []
    
    for root, dirs, files in os.walk(base_dir):
        for file in files:
            if file.endswith(tuple(SUPPORTED_EXTENSIONS)):
                file_path = os.path.join(root, file)
                
                # 检查文件是否在排除列表中
                if is_path_excluded(file_path, base_dir):
                    excluded_files.append(os.path.relpath(file_path, base_dir))
                    continue
                
                # 获取文件修改时间
                mod_time = os.path.getmtime(file_path)
                # 如果修改时间晚于检查时间
                if mod_time > check_timestamp:
                    # 复制文件
                    rel_path = copy_file_with_directory_structure(file_path, target_dir, base_dir)
                    copied_files.append(rel_path)
    
    # 输出排除的文件信息
    if excluded_files:
        print(f"排除了 {len(excluded_files)} 个文件/目录:")
        for file in excluded_files[:10]:  # 只显示前10个
            print(f"  - {file}")
        if len(excluded_files) > 10:
            print(f"  ... 和其他 {len(excluded_files) - 10} 个文件")
    
    return copied_files

def upload_file_to_baota(file_path, target_dir, server_url="", 
                        cookie="", cookie_token="", http_token="", root_path=""):
    """
    上传文件到宝塔面板
    
    Args:
        file_path (str): 要上传的文件路径
        target_dir (str): 目标目录（相对于root_path）
        server_url (str): 宝塔服务器地址
        cookie (str): 宝塔面板cookie
        cookie_token (str): x-cookie-token值（已弃用，保留为兼容性）
        http_token (str): x-http-token值
        root_path (str): 上传根目录路径
    
    Returns:
        bool: 上传是否成功
    """
    if not os.path.exists(file_path):
        print(f"错误：文件 {file_path} 不存在")
        return False
    
    # 获取文件信息
    file_name = os.path.basename(file_path)
    file_size = os.path.getsize(file_path)
    
    # 确保target_dir以/结尾
    if not target_dir.endswith('/'):
        target_dir += '/'
    
    # 构建完整的上传路径
    full_target_path = root_path + target_dir
    
    # 构建请求URL
    upload_url = f"{server_url}/files/upload"
    
    # 生成request_time和request_token
    # t = Date.now() (JavaScript时间戳，毫秒)
    request_time = int(time.time() * 1000)
    
    # e = http_token
    # request_token = md5(String(t).concat(md5(e)))
    http_token_md5 = hashlib.md5(http_token.encode()).hexdigest()
    request_token = hashlib.md5((str(request_time) + http_token_md5).encode()).hexdigest()
    
    # 构建请求头
    headers = {
        'Accept': 'application/json, text/plain, */*',
        'Accept-Language': 'zh-CN,zh;q=0.9',
        'Cookie': cookie,
        'Origin': server_url,
        'Proxy-Connection': 'keep-alive',
        'Referer': f"{server_url}/files",
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'x-http-token': http_token
    }
    
    try:
        # 创建session并禁用代理
        session = requests.Session()
        session.proxies = {}  # 清除所有代理设置
        session.trust_env = False  # 不使用环境变量中的代理设置
        
        # 读取文件内容
        with open(file_path, 'rb') as f:
            file_content = f.read()
        
        # 构建multipart/form-data
        files = {
            'path': (None, full_target_path),
            'filename': (None, file_name),
            'size': (None, str(file_size)),
            'start': (None, '0'),
            'blob': ('blob', file_content, 'application/octet-stream'),
            'force': (None, 'true'),
            'request_time': (None, str(request_time)),
            'request_token': (None, request_token)
        }
        
        print(f"正在上传文件: {file_name}")
        print(f"目标路径: {full_target_path}")
        # print(f"文件大小: {file_size} 字节")
        # print(f"Request Time: {request_time}")
        # print(f"Request Token: {request_token}")
        
        # 发送请求 - 禁用代理和SSL验证
        response = session.post(
            upload_url, 
            headers=headers, 
            files=files, 
            verify=False,  # 忽略SSL证书验证
            timeout=60     # 设置60秒超时
        )
        
        if response.status_code == 200:
            print(f"文件 {file_name} 上传成功")
            return True
        else:
            print(f"上传失败，HTTP状态码: {response.status_code}")
            print(f"响应内容: {response.text}")
            return False
            
    except Exception as e:
        print(f"上传文件时发生错误: {str(e)}")
        return False

def batch_upload_to_baota(files_dir, server_url="", 
                         cookie="", cookie_token="", http_token="", root_path=""):
    """
    批量上传目录中的文件到宝塔面板，保持文件目录结构
    
    Args:
        files_dir (str): 要上传的文件目录
        server_url (str): 宝塔服务器地址
        cookie (str): 宝塔面板cookie
        cookie_token (str): x-cookie-token值
        http_token (str): x-http-token值
        root_path (str): 上传根目录路径
    
    Returns:
        tuple: (成功数量, 失败数量)
    """
    if not os.path.exists(files_dir):
        print(f"错误：目录 {files_dir} 不存在")
        return 0, 0
    
    success_count = 0
    fail_count = 0
    
    print(f"开始批量上传目录: {files_dir}")
    print(f"将保持原有目录结构上传到: {root_path}")
    
    # 遍历目录中的所有文件
    for root, dirs, files in os.walk(files_dir):
        for file in files:
            if file.endswith(tuple(SUPPORTED_EXTENSIONS)):
                file_path = os.path.join(root, file)
                
                # 检查文件是否在排除列表中（基于files_dir作为基础目录）
                if is_path_excluded(file_path, files_dir):
                    print(f"  ⊘ {file} 已排除")
                    continue
                
                # 计算相对路径作为目标目录
                rel_path = os.path.relpath(root, files_dir)
                if rel_path == '.':
                    # 文件在根目录，直接上传到root_path
                    target_dir = ""
                else:
                    # 文件在子目录，保持目录结构
                    target_dir = rel_path.replace('\\', '/')
                
                # 上传文件
                if upload_file_to_baota(file_path, target_dir, server_url, 
                                       cookie, cookie_token, http_token, root_path):
                    success_count += 1
                    print(f"  ✓ {file} -> {target_dir if target_dir else '根目录'}")
                else:
                    fail_count += 1
                    print(f"  ✗ {file} 上传失败")
    
    print(f"批量上传完成 - 成功: {success_count}, 失败: {fail_count}")
    return success_count, fail_count

def check_baota_login_status(server_url, cookie, cookie_token, http_token):
    """
    检查宝塔面板登录状态

    Args:
        server_url (str): 宝塔服务器地址
        cookie (str): 宝塔面板cookie
        cookie_token (str): x-cookie-token值（已弃用，保留为兼容性）
        http_token (str): x-http-token值

    Returns:
        bool: 是否登录
    """
    check_url = f"{server_url}/system/GetServiceStatus"
    
    headers = {
        'Accept': 'application/json, text/plain, */*',
        'Accept-Language': 'zh-CN,zh;q=0.9',
        'Content-Type': 'application/x-www-form-urlencoded',
        'Cookie': cookie,
        'Origin': server_url,
        'Proxy-Connection': 'keep-alive',
        'Referer': f"{server_url}/site/php",
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'x-http-token': http_token
    }

    try:
        session = requests.Session()
        session.proxies = {}
        session.trust_env = False
        
        # POST request with service name data
        post_data = 'name=web'
        
        response = session.post(check_url, headers=headers, data=post_data, verify=False, timeout=10)
        
        if response.status_code == 200:
            try:
                response_data = response.json()
                if isinstance(response_data, dict) and response_data.get('status_msg') == 'running':
                    service_name = response_data.get('service_name', 'unknown')
                    version = response_data.get('version', 'unknown')
                    print(f"宝塔面板已登录，服务状态正常: {service_name} ({version})")
                    return True
                else:
                    print(f"宝塔面板登录状态检查失败，服务状态: {response_data.get('status_msg', 'unknown')}")
                    return False
            except (requests.exceptions.JSONDecodeError, ValueError):
                print(f"宝塔面板登录状态检查失败，无法解析JSON响应: {response.text}")
                return False
        else:
            print(f"宝塔面板登录状态检查失败，HTTP状态码: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"检查宝塔登录状态时发生错误: {e}")
        return False

def get_baota_config():
    """
    从配置文件获取宝塔相关配置信息
    
    Returns:
        dict: 包含宝塔配置的字典
    """
    config = configparser.ConfigParser(interpolation=None)
    config_file = 'update_config.ini'
    
    # 默认配置
    default_config = {
        'server_url': '',
        'cookie': '',
        'cookie_token': '',
        'http_token': '',
        'root_path': ''
    }
    
    if os.path.exists(config_file):
        config.read(config_file)
        if 'BaoTa' in config:
            for key in default_config.keys():
                if key in config['BaoTa']:
                    default_config[key] = config['BaoTa'][key]
    else:
        # 创建默认配置文件
        config['BaoTa'] = default_config
        with open(config_file, 'w') as f:
            config.write(f)
        print(f"已创建配置文件 {config_file}，请填写宝塔相关配置")
    
    return default_config

def save_baota_config(server_url, cookie, cookie_token, http_token, root_path):
    """
    保存宝塔配置到配置文件
    
    Args:
        server_url (str): 宝塔服务器地址
        cookie (str): 宝塔面板cookie
        cookie_token (str): x-cookie-token值
        http_token (str): x-http-token值
        root_path (str): 上传根目录路径
    """
    config = configparser.ConfigParser(interpolation=None)
    config_file = 'update_config.ini'
    
    if os.path.exists(config_file):
        config.read(config_file)
    
    if 'BaoTa' not in config:
        config['BaoTa'] = {}
    
    config['BaoTa']['server_url'] = server_url
    config['BaoTa']['cookie'] = cookie
    config['BaoTa']['cookie_token'] = cookie_token
    config['BaoTa']['http_token'] = http_token
    config['BaoTa']['root_path'] = root_path
    
    with open(config_file, 'w') as f:
        config.write(f)
    
    print("宝塔配置已保存到配置文件")

def main():
    # 定义更新文件目录
    updates_dir = "c:\\updates_aspx_files"
    
    # 创建或清空更新目录
    create_or_clean_directory(updates_dir)
    
    # 获取上次检查时间
    check_time = get_last_check_time()
    print(f"将检查修改时间在 {check_time} 之后的开发文件")
    print(f"支持的文件类型: {', '.join(SUPPORTED_EXTENSIONS)}")
    print(f"排除的路径: {', '.join(EXCLUDED_PATHS)}")
    
    # 获取当前脚本所在目录
    current_dir = os.path.dirname(os.path.abspath(__file__))
    
    # 检查并复制文件
    copied_files = check_and_copy_dev_files(current_dir, updates_dir, check_time)
    
    # 显示结果
    if copied_files:
        print(f"共复制了 {len(copied_files)} 个文件到 {updates_dir}:")
        for file in copied_files:
            print(f"  - {file}")
        
        # # 询问是否上传到宝塔
        # upload_choice = ""
        
        # if len(copied_files) > 10:
        #     upload_choice = input("\n是否要将文件上传到宝塔面板？(y/n): ").lower().strip()
        # else:
        #     upload_choice = 'y'

        # # 设置默认值为是
        # if not upload_choice:
        #     upload_choice = 'y'

        # if upload_choice in ['y', 'yes', '是']:
        #     upload_to_baota_panel(updates_dir)
    else:
        print(f"未找到在 {check_time} 之后修改的开发文件")
    
    # 保存当前时间为下次检查的时间点
    current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    save_check_time(current_time)

def upload_to_baota_panel(updates_dir):
    """
    上传文件到宝塔面板的交互式函数
    
    Args:
        updates_dir (str): 要上传的文件目录
    """
    print("\n=== 宝塔面板上传配置 ===")
    
    # 获取现有配置
    baota_config = get_baota_config()
    
    # 检查登录状态
    if not check_baota_login_status(
        baota_config['server_url'],
        baota_config['cookie'],
        baota_config['cookie_token'],
        baota_config['http_token']
    ):
        print("宝塔登录验证失败，请运行 'python checkupdate.py config' 更新配置后重试。")
        return
    
    target_dir = ""
    
    print(f"\n开始上传文件到宝塔面板...")
    print(f"服务器: {baota_config['server_url']}")
    print(f"目标路径: {baota_config['root_path']}{target_dir}")
    
    # 执行批量上传
    success, fail = batch_upload_to_baota(
        updates_dir,
        baota_config['server_url'],
        baota_config['cookie'],
        baota_config['cookie_token'],
        baota_config['http_token'],
        baota_config['root_path']
    )
    
    print(f"\n上传完成！成功: {success} 个文件，失败: {fail} 个文件")

def input_baota_config():
    """
    交互式输入宝塔配置信息
    
    Returns:
        dict: 宝塔配置字典
    """
    print("请粘贴从浏览器复制的cURL命令 (Windows CMD/PowerShell 格式)，输入完成后按两次回车:")
    
    lines = []
    while True:
        try:
            line = input()
            if line:
                lines.append(line)
            else:
                break
        except EOFError:
            break
            
    curl_command = " ".join(lines).replace('^', '').replace('\\\n', '')

    try:
        # Extract server_url from the main curl URL
        url_match = re.search(r'curl\s+[\'"](.*?)[\'"]', curl_command)
        if not url_match:
            print("错误：无法从cURL命令中解析出URL")
            return None
        
        full_url = url_match.group(1)
        parsed_url = urlparse(full_url)
        server_url = f"{parsed_url.scheme}://{parsed_url.netloc}"

        # Extract cookie
        cookie_match = re.search(r"-b\s+[\"'](.*?)[\"']", curl_command)
        cookie = cookie_match.group(1) if cookie_match else ""

        # Extract x-cookie-token (可选，新接口不需要)
        cookie_token_match = re.search(r"x-cookie-token:\s*([^\s\"']+)", curl_command)
        cookie_token = cookie_token_match.group(1) if cookie_token_match else ""

        # Extract x-http-token
        http_token_match = re.search(r"x-http-token:\s*([^\s\"']+)", curl_command)
        http_token = http_token_match.group(1) if http_token_match else ""
        
        if not (server_url and cookie and http_token):
            print("错误：无法从cURL命令中完整地解析出 URL, cookie 和 x-http-token。")
            print("请确保cURL命令格式正确。")
            return None
        
        # 获取现有配置以保留 root_path
        baota_config = get_baota_config()
        root_path = baota_config.get('root_path', '')

        # 保存新的配置
        save_baota_config(server_url, cookie, cookie_token, http_token, root_path)
        
        print("\n宝塔配置已成功更新！")
        
        return {
            'server_url': server_url,
            'cookie': cookie,
            'cookie_token': cookie_token,
            'http_token': http_token,
            'root_path': root_path
        }
    except Exception as e:
        print(f"解析cURL命令时发生错误: {e}")
        return None

if __name__ == "__main__":
    # 检查命令行参数
    if len(sys.argv) > 1:
        command = sys.argv[1].lower()
        
        if command == "upload" and len(sys.argv) >= 4:
            # 直接上传模式: python checkupdate.py upload <file_path> <target_dir>
            file_path = sys.argv[2]
            target_dir = sys.argv[3]
            
            print("=== 宝塔文件上传模式 ===")
            
            # 获取配置
            baota_config = get_baota_config()
            
            if not (baota_config['cookie'] and baota_config['http_token']):
                print("未找到宝塔配置，请先运行 python checkupdate.py config 配置宝塔信息")
                sys.exit(1)
            
            # 检查登录状态
            if not check_baota_login_status(
                baota_config['server_url'],
                baota_config['cookie'],
                baota_config['cookie_token'],
                baota_config['http_token']
            ):
                print("宝塔登录验证失败，请运行 'python checkupdate.py config' 更新配置后重试。")
                sys.exit(1)
            
            # 执行上传
            success = upload_file_to_baota(
                file_path,
                target_dir,
                baota_config['server_url'],
                baota_config['cookie'],
                baota_config['cookie_token'],
                baota_config['http_token'],
                baota_config['root_path']
            )
            
            if success:
                print("文件上传成功！")
            else:
                print("文件上传失败！")
                sys.exit(1)
                
        elif command == "config":
            # 配置模式: python checkupdate.py config
            print("=== 宝塔面板配置模式 ===")
            input_baota_config()
            print("配置完成！")
            
        elif command == "batch" and len(sys.argv) >= 3:
            # 批量上传模式: python checkupdate.py batch <dir_path>
            dir_path = sys.argv[2]
            
            print("=== 宝塔批量上传模式 ===")
            
            # 获取配置
            baota_config = get_baota_config()
            
            if not (baota_config['cookie'] and baota_config['http_token']):
                print("未找到宝塔配置，请先运行 python checkupdate.py config 配置宝塔信息")
                sys.exit(1)
            
            # 检查登录状态
            if not check_baota_login_status(
                baota_config['server_url'],
                baota_config['cookie'],
                baota_config['cookie_token'],
                baota_config['http_token']
            ):
                print("宝塔登录验证失败，请运行 'python checkupdate.py config' 更新配置后重试。")
                sys.exit(1)
            
            # 执行批量上传
            success, fail = batch_upload_to_baota(
                dir_path,
                baota_config['server_url'],
                baota_config['cookie'],
                baota_config['cookie_token'],
                baota_config['http_token'],
                baota_config['root_path']
            )
            
            print(f"批量上传完成！成功: {success} 个文件，失败: {fail} 个文件")
            
        else:
            print("用法:")
            print("  python checkupdate.py                    # 检查更新文件并可选上传")
            print("  python checkupdate.py config             # 配置宝塔面板信息")
            print("  python checkupdate.py upload <file> <dir> # 上传单个文件")
            print("  python checkupdate.py batch <dir>        # 批量上传目录（保持目录结构）")
    else:
        # 默认模式：检查更新文件
        main()

<%@ Page Title="" Language="C#" MasterPageFile="~/top.master" AutoEventWireup="true" CodeFile="wallet.aspx.cs" Inherits="order_list" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="Server">
    <style>
        .info_title {
            color: #000;
            font-size: 14px;
            font-weight: bold;
            display: flex;
            align-items: center;
            margin: 10px 0;
            margin-top: 18px;
        }

            .info_title i {
                display: inline-block;
                background: #2A3EE8;
                width: 6px;
                height: 6px;
                border-radius: 50%;
            }

        .main-container {
            padding: 0px;
        }
    </style>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="Server">

    
    <div style="padding: 18px">


        <div class="info_title">
            <i></i>&nbsp;&nbsp;资产列表
        </div>

        <div style="display: flex; font-size: 12px; background: #fff; padding: 18px 0; border-radius: 8px;">

            <div style="width: 33.333%; text-align: center;">
                <img src="/static/images/coin.png" alt="" height="60" width="60" style="margin-left: 5px;">
            </div>
            <div style="width: 33.333%; text-align: center;">
                <div style="color: gray;">
                    冻结
                </div>
                <div style="margin: 10px 0; font-size: 18px; color: #222; font-weight: bold;">
                    <%=uConfig.gnumber(userdt,"freeze_amount") %>
                </div>
            </div>
            <div style="width: 33.333%; text-align: center;">
                <div style="color: gray;">
                    可用
                </div>
                <div style="margin: 10px 0; font-size: 18px; color: #3E48D4; font-weight: bold;">
                    <%=uConfig.gnumber(userdt,"amount") %>
                </div>
            </div>
        </div>

    </div>


    <%--<div style="display: flex;font-size: 12px;background: #fff;border-radius: 8px;display: flex;justify-content: center; /* 水平居中 */align-items: center; /* 垂直居中 */width: 100%;height: 100vh; /* 可视窗口高度，实现垂直居中 */position: fixed;left: 0;">

            
            
            <div style="width: 33.333%; text-align: center;">
                <div style="color: #000;display:flex;justify-content: center;">
                    总余额<img src="/static/images/coin.png" alt="" height="15" width="15" style="margin-left: 5px;">
                </div>
                <div style="margin: 10px 0; font-size: 38px; color: #3E48D4; font-weight: bold;">
                     <%=uConfig.gnumber(userdt,"amount") %>
                </div>
                <div style="color: gray;">
                    不可用  <%=uConfig.gnumber(userdt,"freeze_amount") %> CNY
                </div>
            </div>
        </div>--%>
</asp:Content>


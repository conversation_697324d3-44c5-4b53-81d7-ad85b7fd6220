using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Data;
using System.Data.SqlClient;
using LitJson;

public partial class order_list : baseClass
{
    public DataTable userdt = new DataTable();
    public DataTable agents = new DataTable();
    public DataTable recList = new DataTable();
    public int total_number = 0;
    public Dictionary<string, object> pmlist = new Dictionary<string, object>();
    public List<string> msgs = new List<string>();

    protected void Page_Load(object sender, EventArgs e)
    {
        List<SqlParameter> pams = new List<SqlParameter>();
        dbClass db = new dbClass();
        pams.Add(new SqlParameter("@userid", uConfig.p_uid));



        DataTable tempdt = selectDateTable(chelper.gdt("task_list"), " from_type='" + Request.QueryString["type"] + "' and state=1 ");

        List<string> moneyList = new List<string>();
        int total_number = tempdt.Rows.Count;
        for (int i = 0; i < total_number; i++)
        {
            for (int t = 0; t < (total_number - i) * 5 - 4; t++)
            {
                moneyList.Add(tempdt.Rows[i]["reward_amount"] + "");
            }
        }


        Random rd = new Random();
        var bankid1 = "";
        var bankid2 = "";
        string money = "";

        if (moneyList.Count > 0)
        {

            for (int i = 0; i < 25; i++)
            {
                bankid1 = rd.Next(10, 99).ToString();
                bankid2 = rd.Next(10, 99).ToString();
                int v = rd.Next(0, moneyList.Count - 1);
                money = moneyList[v];

                msgs.Add("<li><a>恭喜" + bankid1 + "***" + bankid2 + "领取 " + (Request.QueryString["type"] + "" == "usdt" ? "【USDT】买币大礼包" : "任务奖励") + " <span style='color: #E47F5A'>" + money + "</span> <img src='/static/images/coin.png' alt='' height='15' width='15' style='margin-left: 5px;'></a></li>");

            }
        }


        string sql = string.Empty;
        sql = @"
select * from accounts with(nolock) where id=@userid

-- 查询下级有效人数
select id,trans_amount,isnull(onetouch_amount,0) as onetouch_amount,lend_amount,create_time,(case when MONTH(create_time) = MONTH(GETDATE()) AND YEAR(create_time) = YEAR(GETDATE()) then 1 else 0 end) as samemonth from accounts with(nolock) where parentid=@userid

-- 查询买币次数
select count(0) as number,isnull(sum(case when MONTH(create_time) = MONTH(GETDATE()) AND YEAR(create_time) = YEAR(GETDATE()) then 1 else 0 end),0) as number_month from [buy_list] with(nolock) where userid=@userid and state=1 and payment_type='USDT' and buy_time>='2023-12-14 11:00:00'

select trl.taskid,trl.create_time,trl.audit_time from task_receive_list trl with(nolock) left join task_list tl with(nolock) on trl.taskid=tl.id where userid=@userid and (isnull(tl.times,'once')='once' or (tl.times='month' and datediff(month,trl.create_time,getdate())=0))

";

        DataSet ds = new DataSet();
        ds = db.getDataSet(sql, pams.ToArray());
        userdt = ds.Tables[0];

        Dictionary<string, object> temp_dic = chelper.getUserParames(uConfig.gd(userdt, "usertype"), uConfig.gd(userdt, "groupid"));
        pmlist["usertype"] = temp_dic["usertype"];



        DataTable dt = new DataTable();
        agents = ds.Tables[1];
        pmlist["trans_amount"] = Convert.ToDouble(uConfig.gd(userdt, "trans_amount")) + Convert.ToDouble(uConfig.gnumber(userdt, "onetouch_amount"));



        dt = ds.Tables[2];
        pmlist["buy_number"] = "0";
        pmlist["buy_number_month"] = "0";
        if (dt.Rows.Count > 0)
        {
            pmlist["buy_number"] = dt.Rows[0]["number"] + "";
            pmlist["buy_number_month"] = dt.Rows[0]["number_month"] + "";
        }

        recList = ds.Tables[3];


        taskList.DataSource = selectDateTable(chelper.gdt("task_list"), " from_type='" + Request.QueryString["type"] + "' and state=1 ");
        taskList.DataBind();

    }

    public string calcNumber(string times, string type, string n1, string n2)
    {
        string pg = "0";
        DataTable tempdt = new DataTable();

        switch (type)
        {
            case "sd_money":
                switch (times)
                {
                    case "once":
                        pg = Convert.ToDouble(pmlist["trans_amount"] + "").ToString("0");
                        break;
                    case "month":
                        break;
                    default:
                        break;
                }
                break;
            case "agents_money":
                switch (times)
                {
                    case "once":
                        tempdt = selectDateTable(agents, " trans_amount>=" + n2 + " ");
                        pg = tempdt.Rows.Count.ToString();
                        break;
                    case "month":
                        tempdt = selectDateTable(agents, " samemonth=1 and trans_amount>=" + n2 + " ");
                        pg = tempdt.Rows.Count.ToString();
                        break;
                    default:
                        break;
                }
                break;
            case "buy_number":
                switch (times)
                {
                    case "once":
                        pg = Convert.ToDouble(pmlist["buy_number"] + "").ToString("0");
                        break;
                    case "month":
                        pg = Convert.ToDouble(pmlist["buy_number_month"] + "").ToString("0");
                        break;
                    default:
                        break;
                }
                break;
            default:
                break;
        }

        return pg;
    }
}
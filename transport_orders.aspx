<%@ Page Title="" Language="C#" MasterPageFile="~/top.master" AutoEventWireup="true" CodeFile="transport_orders.aspx.cs" Inherits="order_list" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="Server">
    <script>
        $(function () {
            popTitle('搬砖记录 <div style="color: #555; font-weight: 100; font-size: 12px; margin-top: 6px;">本数据由商家官方提供</div>', '<div style="color: #454242; display: flex; flex-direction: column; align-items: center;"><div style="font-weight: bold; font-size: 22px;"><%=uConfig.gnumber(userdt,"amount") %></div><div style="font-size: 12px; display: flex; color: #555; margin-top: 5px;">可用余额<img src="/static/images/coin.png" alt="" height="15" width="15" style="margin-left: 5px;"></div></div>');
            try {
                lastData();
            } catch (e) {

            }
        })
    </script>
    <style>
        body {
            background: linear-gradient(183deg, #eef1e4, transparent);
            background-repeat: no-repeat;
        }

        .top-title .pptitle {
            font-size: 25px;
            width: 100%;
        }

        .top-title {
            display: flex;
        }



        .top-title {
            background: none;
        }

        .gtab {
            width: 25%;
            margin: 0px;
            color: #000;
            padding: 8px 0px;
            font-weight: 100;
        }

            .gtab.act {
                color: #fafdde;
                background: #35313163;
            }

        .tablist {
            margin-top: 30px;
            position: fixed;
            left: 0;
            padding: 0 10px;
            box-sizing: border-box;
        }

        #lists {
            height: calc(100vh - 172px);
            overflow-y: auto;
            margin-top: 10px;
            margin-top: 80px;
        }
    </style>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="Server">

    <style>
        .star-icon path {
            fill: currentColor;
        }

        .star-icon {
            color: #ccc;
            cursor: pointer;
        }

            .star-icon.active {
                color: #FFB408;
            }
    </style>


    <%--<div style="display: flex;">

        <div style="margin-left: auto; color: #454242; display: flex; flex-direction: column; align-items: center; margin-top: 22px;">
            <div style="font-weight: bold; font-size: 23px;">108899.66</div>
            <div style="font-size: 12px; display: flex; color: #555; margin-top: 5px;">剩余可用资产<img src="/static/images/coin.png" alt="" height="15" width="15" style="margin-left: 5px;"></div>
        </div>

    </div>--%>

    <div class="tablist">
        <div class="gtab act">全部</div>
        <div class="gtab">失败</div>
        <div class="gtab">已完成</div>
        <div class="gtab">进行中</div>
    </div>

    <div id="lists">
    </div>






    <script>

        if (get_param('taskfrom') != "") {
            $('.tablist').hide();
            $('#lists').css("marginTop", "10px");
        }

        var timeout_second = '<%=uConfig.gd(userdt, "timeout_second") %>';

        var set_orderstate = function (id, state) {
            event.stopPropagation();

            security_password(function (e) {


                v3api("set_orderstate", {
                    data: {
                        paypwd: e.password,
                        id: id,
                        state: state
                    }
                }, function (e) {
                    tp(e.msg);
                    var ls = $('[listid=' + id + ']');
                    ls.remove();
                })



            }, { error_tip_top: '<div style="color: #2a2b2c;padding: 0 30px;margin-top: 20px;font-weight: bold;text-align:center;font-size: 14px;" class="animated-text"><div style="    margin-bottom: 10px;">确定收到款了吗？</div><div>为了保证您的资金安全，<span style="color:red;">请收到款再确认收货</span></div>    </div>' })

        }


        var show_list = function (type) {
            v3api("lists", { data: { page: 'transport_orders', p: 0, limit: 30, state: type, taskfrom: get_param('taskfrom') } }, function (e) {
                for (var i = 0; i < e.data.list.length; i++) {
                    var obj = e.data.list[i];


                    $('#lists').append('<div listid="' + obj.sd_orderNo + '" style="background: #fff; border-radius: 5px; padding: 5px 9px; margin: 10px 0;    border-bottom: 2px solid #ddd;">            <div style="background: #f8f8f8; padding: 10px 16px; border-radius: 8px;position: relative;">      <img src="/static/images/' + (obj.state == "1000" ? "order_tasking" : obj.state == "1" ? "order_succ" : (obj.state == "2" || obj.state == "20") ? "order_fail" : obj.state == "-1" ? "order_fail" : "") + '.png" style="position: absolute; right: 0; top: -8px; width: 80px;    transform: rotate(60deg);">          <div style="color: #b1b1b1; font-size: 12px;">                    <div>搬砖时间：' + obj.receive_time + '</div>                    <div style="">搬砖编号：' + obj.sd_orderNo + '</div>                </div>                               <div style="font-size: 14px;">                    <div style="display: flex; margin: 10px 0;">                        <div>                            订单总额                                                          </div>                        <div style="margin-left: auto; color: #222;">                            <span class="total_amount">' + parseFloat(obj.orderAmt).toFixed(2) + '</span><img src="/static/images/coin.png" alt="" height="15" width="15" style="margin-left: 5px;">                        </div>                    </div>                    <div style="display: flex; margin: 10px 0;">                        <div>佣金</div>                        <div style="margin-left: auto;color: #222;text-align: right;">                            <span class="award_amount" style="' + (obj.is_double == 1 ? 'color: #ee7d25; font-weight: bold; font-size: 18px;' : '') + '">' + parseFloat(obj.award_amount - (obj.serve_fee == '' || obj.serve_fee == 0 ? '' : parseFloat(obj.serve_fee)) + (obj.is_double == 1 ? obj.award_amount : 0)).toFixed(2) + '</span><img src="/static/images/coin.png" alt="" height="15" width="15" style="margin-left: 5px;">             ' + (obj.is_double == 1 ? '<div><span style="color: #ee7d25; font-weight: bold; background: #000000e3; font-size: 12px; padding: 2px 10px; border-radius: 5px; margin-left: 6px; box-shadow: 2px 2px 8px #ee7d25;">        <svg t="1701338602094" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="9074" width="12" height="12" style="margin-right: 3px;">            <path d="M859.995 460.963c-1.197 10.478-1.197 21.854 0 33.527v88.305c0 160.751-110.161 296.657-258.34 336.17v-52.982c0-49.693-40.112-89.805-89.505-89.805h-0.3c-49.394 0-89.805 40.112-89.805 89.805v52.982c-148.179-39.812-258.04-175.719-258.04-336.17V431.328c39.814 32.33 93.098 91.302 89.805 172.726 33.227-153.267 99.085-176.318 154.764-239.48 72.444-82.321 93.996-172.726 52.386-259.538C641.17 167.6 687.868 409.476 672.901 536.7c64.959-115.251 146.682-146.083 187.094-154.166v78.429z" fill="#FF7B15" p-id="9075"></path></svg>双倍收益</span></div>' : '') + '   <div style="    color: #000;    font-weight: bold;">佣金转入余额</div>        </div>                    </div>         ' + (obj.serve_fee == '' || obj.serve_fee == 0 ? '' : '<div style="display: flex; margin: 10px 0;">                        <div>                            房主佣金                                                          </div>                        <div style="margin-left: auto; color: #222;">                            <span class="serve_fee">' + parseFloat(obj.serve_fee).toFixed(2) + '</span><img src="/static/images/coin.png" alt="" height="15" width="15" style="margin-left: 5px;">                        </div>                    </div>')

                        + `<div style="display: flex; margin: 10px 0;">                            <div>当前汇率                                                          </div>                            <div style="margin-left: auto; color: #222;">                                <span class="total_amount">≈${usdt.prices.app}</span>                           </div>                        </div>`

                        + '           <div style="display: flex; margin: 10px 0;">                        <div>商家回款</div>                        <div style="margin-left: auto; color: #FAA751; font-size: 24px;"><span class="award_total_amount">' + cny((parseFloat(obj.orderAmt) - (obj.serve_fee == '' || obj.serve_fee == 0 ? '' : parseFloat(obj.serve_fee))).toFixed(2)) + '</span>CY</div>                    </div>                </div>    ' + (obj.state == 1000 ? ' <div style="margin-top: 20px;display: flex;justify-content: center;">    <a style="border-radius:18px;padding: 12px 26px;font-size:12px;background: #e3e3e3;color: #333;display: flex;cursor:pointer;border: 2px solid #9d9d9d;" onclick="set_orderstate(\'' + obj.sd_orderNo + '\',1)"><svg t="1693040838464" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="4309" width="16" height="16"><path d="M770.56 928.4608H388.4032c-16.9472 0-30.72-13.7728-30.72-30.72s13.7728-30.72 30.72-30.72h382.1056c15.616 0 28.8768-11.2128 32.3584-27.2384l72.2944-335.1552c3.4304-15.7696-0.3584-31.9488-10.3424-44.3392-9.472-11.7248-22.7328-18.176-37.4272-18.176h-239.104a30.72 30.72 0 0 1-28.16-43.008c62.1056-142.2848 40.3456-201.1136 28.1088-219.8016-13.7216-20.9408-33.792-24.1152-44.4928-24.1152-25.8048 0-35.9936 25.088-47.8208 77.7216-1.5872 6.9632-3.0208 13.4656-4.5568 19.3536-42.1888 161.5872-149.3504 219.136-235.6224 219.136H192.2048c-17.8688 0-32.4096 15.36-32.4096 34.2016v327.2192c0 18.8416 14.5408 34.2016 32.4096 34.2016h58.9312c16.9472 0 30.72 13.7728 30.72 30.72s-13.7728 30.72-30.72 30.72H192.2048c-51.7632 0-93.8496-42.9056-93.8496-95.6416V505.6c0-52.736 42.0864-95.6416 93.8496-95.6416h63.5392c30.72 0 134.1952-12.4928 176.128-173.1584 1.3824-5.2736 2.6624-11.1104 4.096-17.3568 9.8816-43.9296 28.3136-125.696 107.776-125.696 39.3728 0 74.3424 18.8928 95.8976 51.8656 24.2688 37.0688 41.1136 107.1616-5.888 235.008h193.6384c33.1264 0 64.2048 14.9504 85.248 41.0112 21.7088 26.88 29.952 61.8496 22.6304 95.8464l-72.2944 335.1552c-9.472 43.9808-48.3328 75.8272-92.416 75.8272z" fill="#363F5B" p-id="4310"></path><path d="M269.6192 804.2496c-16.9472 0-30.72-13.7728-30.72-30.72v-193.0752c0-16.9472 13.7728-30.72 30.72-30.72s30.72 13.7728 30.72 30.72v193.0752c0 16.9472-13.7728 30.72-30.72 30.72z" fill="#B11EFF" p-id="4311"></path></svg>&nbsp;确认收款</a></div>' : '') + (obj.state == 1 ? (typeof (obj.star_wlfw) != "number" ? ('<div style="border-top: 1px solid #eee; padding-top: 20px;" class="evaluate_list">        <div style="color: #5a5b5c; margin-bottom: 12px;">            给店铺评分        </div>        <div style="display: flex; align-items: center; padding: 2px 0;">            <div>物流服务</div>            <div style="margin-left: 20px;" class="star-itmes">                <svg class="star-icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="4009" width="30" height="30">                    <path d="M493.9 77c162.3-0.8 98.2 190.6 208.5 234.6 26.6 10.6 64.1 1 96.6 6.1 71.6 11.4 148.1 14.9 159.5 85.9 19 118.7-150.7 159.3-170.2 236.1-21.2 83.6 112.5 256.8-19.9 297.5-112.6 34.6-167.9-81.5-249.9-95.1-93.2-15.3-155.7 161.9-280.7 85.9-100.6-61.1 28.7-202.6 0-296-22.9-74.3-153.9-93-171.7-193.2-17.7-99.5 80.2-109.6 153.3-121.1 45.4-7.2 94.8 7.2 122.7-15.3 59.4-47.9 53-163.7 115-210.1 12.3-5.1 24.5-10.2 36.8-15.3z" p-id="4010" fill="#FFB408"></path></svg>                <svg class="star-icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="4009" width="30" height="30">                    <path d="M493.9 77c162.3-0.8 98.2 190.6 208.5 234.6 26.6 10.6 64.1 1 96.6 6.1 71.6 11.4 148.1 14.9 159.5 85.9 19 118.7-150.7 159.3-170.2 236.1-21.2 83.6 112.5 256.8-19.9 297.5-112.6 34.6-167.9-81.5-249.9-95.1-93.2-15.3-155.7 161.9-280.7 85.9-100.6-61.1 28.7-202.6 0-296-22.9-74.3-153.9-93-171.7-193.2-17.7-99.5 80.2-109.6 153.3-121.1 45.4-7.2 94.8 7.2 122.7-15.3 59.4-47.9 53-163.7 115-210.1 12.3-5.1 24.5-10.2 36.8-15.3z" p-id="4010" fill="#FFB408"></path></svg>                <svg class="star-icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="4009" width="30" height="30">                    <path d="M493.9 77c162.3-0.8 98.2 190.6 208.5 234.6 26.6 10.6 64.1 1 96.6 6.1 71.6 11.4 148.1 14.9 159.5 85.9 19 118.7-150.7 159.3-170.2 236.1-21.2 83.6 112.5 256.8-19.9 297.5-112.6 34.6-167.9-81.5-249.9-95.1-93.2-15.3-155.7 161.9-280.7 85.9-100.6-61.1 28.7-202.6 0-296-22.9-74.3-153.9-93-171.7-193.2-17.7-99.5 80.2-109.6 153.3-121.1 45.4-7.2 94.8 7.2 122.7-15.3 59.4-47.9 53-163.7 115-210.1 12.3-5.1 24.5-10.2 36.8-15.3z" p-id="4010" fill="#FFB408"></path></svg>                <svg class="star-icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="4009" width="30" height="30">                    <path d="M493.9 77c162.3-0.8 98.2 190.6 208.5 234.6 26.6 10.6 64.1 1 96.6 6.1 71.6 11.4 148.1 14.9 159.5 85.9 19 118.7-150.7 159.3-170.2 236.1-21.2 83.6 112.5 256.8-19.9 297.5-112.6 34.6-167.9-81.5-249.9-95.1-93.2-15.3-155.7 161.9-280.7 85.9-100.6-61.1 28.7-202.6 0-296-22.9-74.3-153.9-93-171.7-193.2-17.7-99.5 80.2-109.6 153.3-121.1 45.4-7.2 94.8 7.2 122.7-15.3 59.4-47.9 53-163.7 115-210.1 12.3-5.1 24.5-10.2 36.8-15.3z" p-id="4010" fill="#FFB408"></path></svg><svg class="star-icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="4009" width="30" height="30"><path d="M493.9 77c162.3-0.8 98.2 190.6 208.5 234.6 26.6 10.6 64.1 1 96.6 6.1 71.6 11.4 148.1 14.9 159.5 85.9 19 118.7-150.7 159.3-170.2 236.1-21.2 83.6 112.5 256.8-19.9 297.5-112.6 34.6-167.9-81.5-249.9-95.1-93.2-15.3-155.7 161.9-280.7 85.9-100.6-61.1 28.7-202.6 0-296-22.9-74.3-153.9-93-171.7-193.2-17.7-99.5 80.2-109.6 153.3-121.1 45.4-7.2 94.8 7.2 122.7-15.3 59.4-47.9 53-163.7 115-210.1 12.3-5.1 24.5-10.2 36.8-15.3z" p-id="4010" fill="#FFB408"></path></svg>            </div>        </div>        <div style="display: flex; align-items: center; padding: 2px 0;">            <div>发货速度</div>            <div style="margin-left: 20px;" class="star-itmes">                <svg class="star-icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="4009" width="30" height="30">                    <path d="M493.9 77c162.3-0.8 98.2 190.6 208.5 234.6 26.6 10.6 64.1 1 96.6 6.1 71.6 11.4 148.1 14.9 159.5 85.9 19 118.7-150.7 159.3-170.2 236.1-21.2 83.6 112.5 256.8-19.9 297.5-112.6 34.6-167.9-81.5-249.9-95.1-93.2-15.3-155.7 161.9-280.7 85.9-100.6-61.1 28.7-202.6 0-296-22.9-74.3-153.9-93-171.7-193.2-17.7-99.5 80.2-109.6 153.3-121.1 45.4-7.2 94.8 7.2 122.7-15.3 59.4-47.9 53-163.7 115-210.1 12.3-5.1 24.5-10.2 36.8-15.3z" p-id="4010" fill="#FFB408"></path></svg>                <svg class="star-icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="4009" width="30" height="30">                    <path d="M493.9 77c162.3-0.8 98.2 190.6 208.5 234.6 26.6 10.6 64.1 1 96.6 6.1 71.6 11.4 148.1 14.9 159.5 85.9 19 118.7-150.7 159.3-170.2 236.1-21.2 83.6 112.5 256.8-19.9 297.5-112.6 34.6-167.9-81.5-249.9-95.1-93.2-15.3-155.7 161.9-280.7 85.9-100.6-61.1 28.7-202.6 0-296-22.9-74.3-153.9-93-171.7-193.2-17.7-99.5 80.2-109.6 153.3-121.1 45.4-7.2 94.8 7.2 122.7-15.3 59.4-47.9 53-163.7 115-210.1 12.3-5.1 24.5-10.2 36.8-15.3z" p-id="4010" fill="#FFB408"></path></svg>                <svg class="star-icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="4009" width="30" height="30">                    <path d="M493.9 77c162.3-0.8 98.2 190.6 208.5 234.6 26.6 10.6 64.1 1 96.6 6.1 71.6 11.4 148.1 14.9 159.5 85.9 19 118.7-150.7 159.3-170.2 236.1-21.2 83.6 112.5 256.8-19.9 297.5-112.6 34.6-167.9-81.5-249.9-95.1-93.2-15.3-155.7 161.9-280.7 85.9-100.6-61.1 28.7-202.6 0-296-22.9-74.3-153.9-93-171.7-193.2-17.7-99.5 80.2-109.6 153.3-121.1 45.4-7.2 94.8 7.2 122.7-15.3 59.4-47.9 53-163.7 115-210.1 12.3-5.1 24.5-10.2 36.8-15.3z" p-id="4010" fill="#FFB408"></path></svg>                <svg class="star-icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="4009" width="30" height="30">                    <path d="M493.9 77c162.3-0.8 98.2 190.6 208.5 234.6 26.6 10.6 64.1 1 96.6 6.1 71.6 11.4 148.1 14.9 159.5 85.9 19 118.7-150.7 159.3-170.2 236.1-21.2 83.6 112.5 256.8-19.9 297.5-112.6 34.6-167.9-81.5-249.9-95.1-93.2-15.3-155.7 161.9-280.7 85.9-100.6-61.1 28.7-202.6 0-296-22.9-74.3-153.9-93-171.7-193.2-17.7-99.5 80.2-109.6 153.3-121.1 45.4-7.2 94.8 7.2 122.7-15.3 59.4-47.9 53-163.7 115-210.1 12.3-5.1 24.5-10.2 36.8-15.3z" p-id="4010" fill="#FFB408"></path></svg><svg class="star-icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="4009" width="30" height="30"><path d="M493.9 77c162.3-0.8 98.2 190.6 208.5 234.6 26.6 10.6 64.1 1 96.6 6.1 71.6 11.4 148.1 14.9 159.5 85.9 19 118.7-150.7 159.3-170.2 236.1-21.2 83.6 112.5 256.8-19.9 297.5-112.6 34.6-167.9-81.5-249.9-95.1-93.2-15.3-155.7 161.9-280.7 85.9-100.6-61.1 28.7-202.6 0-296-22.9-74.3-153.9-93-171.7-193.2-17.7-99.5 80.2-109.6 153.3-121.1 45.4-7.2 94.8 7.2 122.7-15.3 59.4-47.9 53-163.7 115-210.1 12.3-5.1 24.5-10.2 36.8-15.3z" p-id="4010" fill="#FFB408"></path></svg>            </div>        </div>        <div style="display: flex; align-items: center; padding: 2px 0;">            <div>服务态度</div>            <div style="margin-left: 20px;" class="star-itmes">                <svg class="star-icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="4009" width="30" height="30">                    <path d="M493.9 77c162.3-0.8 98.2 190.6 208.5 234.6 26.6 10.6 64.1 1 96.6 6.1 71.6 11.4 148.1 14.9 159.5 85.9 19 118.7-150.7 159.3-170.2 236.1-21.2 83.6 112.5 256.8-19.9 297.5-112.6 34.6-167.9-81.5-249.9-95.1-93.2-15.3-155.7 161.9-280.7 85.9-100.6-61.1 28.7-202.6 0-296-22.9-74.3-153.9-93-171.7-193.2-17.7-99.5 80.2-109.6 153.3-121.1 45.4-7.2 94.8 7.2 122.7-15.3 59.4-47.9 53-163.7 115-210.1 12.3-5.1 24.5-10.2 36.8-15.3z" p-id="4010" fill="#FFB408"></path></svg>                <svg class="star-icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="4009" width="30" height="30">                    <path d="M493.9 77c162.3-0.8 98.2 190.6 208.5 234.6 26.6 10.6 64.1 1 96.6 6.1 71.6 11.4 148.1 14.9 159.5 85.9 19 118.7-150.7 159.3-170.2 236.1-21.2 83.6 112.5 256.8-19.9 297.5-112.6 34.6-167.9-81.5-249.9-95.1-93.2-15.3-155.7 161.9-280.7 85.9-100.6-61.1 28.7-202.6 0-296-22.9-74.3-153.9-93-171.7-193.2-17.7-99.5 80.2-109.6 153.3-121.1 45.4-7.2 94.8 7.2 122.7-15.3 59.4-47.9 53-163.7 115-210.1 12.3-5.1 24.5-10.2 36.8-15.3z" p-id="4010" fill="#FFB408"></path></svg>                <svg class="star-icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="4009" width="30" height="30">                    <path d="M493.9 77c162.3-0.8 98.2 190.6 208.5 234.6 26.6 10.6 64.1 1 96.6 6.1 71.6 11.4 148.1 14.9 159.5 85.9 19 118.7-150.7 159.3-170.2 236.1-21.2 83.6 112.5 256.8-19.9 297.5-112.6 34.6-167.9-81.5-249.9-95.1-93.2-15.3-155.7 161.9-280.7 85.9-100.6-61.1 28.7-202.6 0-296-22.9-74.3-153.9-93-171.7-193.2-17.7-99.5 80.2-109.6 153.3-121.1 45.4-7.2 94.8 7.2 122.7-15.3 59.4-47.9 53-163.7 115-210.1 12.3-5.1 24.5-10.2 36.8-15.3z" p-id="4010" fill="#FFB408"></path></svg>                <svg class="star-icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="4009" width="30" height="30">                    <path d="M493.9 77c162.3-0.8 98.2 190.6 208.5 234.6 26.6 10.6 64.1 1 96.6 6.1 71.6 11.4 148.1 14.9 159.5 85.9 19 118.7-150.7 159.3-170.2 236.1-21.2 83.6 112.5 256.8-19.9 297.5-112.6 34.6-167.9-81.5-249.9-95.1-93.2-15.3-155.7 161.9-280.7 85.9-100.6-61.1 28.7-202.6 0-296-22.9-74.3-153.9-93-171.7-193.2-17.7-99.5 80.2-109.6 153.3-121.1 45.4-7.2 94.8 7.2 122.7-15.3 59.4-47.9 53-163.7 115-210.1 12.3-5.1 24.5-10.2 36.8-15.3z" p-id="4010" fill="#FFB408"></path></svg><svg class="star-icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="4009" width="30" height="30"><path d="M493.9 77c162.3-0.8 98.2 190.6 208.5 234.6 26.6 10.6 64.1 1 96.6 6.1 71.6 11.4 148.1 14.9 159.5 85.9 19 118.7-150.7 159.3-170.2 236.1-21.2 83.6 112.5 256.8-19.9 297.5-112.6 34.6-167.9-81.5-249.9-95.1-93.2-15.3-155.7 161.9-280.7 85.9-100.6-61.1 28.7-202.6 0-296-22.9-74.3-153.9-93-171.7-193.2-17.7-99.5 80.2-109.6 153.3-121.1 45.4-7.2 94.8 7.2 122.7-15.3 59.4-47.9 53-163.7 115-210.1 12.3-5.1 24.5-10.2 36.8-15.3z" p-id="4010" fill="#FFB408"></path></svg>            </div>        </div>        <div style="display: flex; margin-top: 10px; align-items: center;">            <div style="width: 100%;">                <textarea style="resize: none; width: 100%; border: 1px solid #eee; height: 80px; outline: none; background: #f9f9f9; padding: 8px; box-sizing: border-box; font-size: 18px; line-height: 23px;" class="evaluate_text"></textarea>            </div>            <div style="flex-shrink: 0; margin-left: 12px;">                <a style="display: inline-block; color: #ad5b1d; border: 1px solid #C3A187; padding: 3px 10px; border-radius: 50px;cursor:pointer;" onclick="share_evaluate(this,\'' + obj.sd_orderNo + '\')">写评价</a>            </div>        </div>    </div>') : ('<div style="color: #34488d;font-size: 14px;">物流&nbsp;' + get_star(obj.star_wlfw) + '</div>' + '<div style="color: #34488d;font-size: 14px;">发货&nbsp;' + get_star(obj.star_fhsd) + '</div>' + '<div style="color: #34488d;font-size: 14px;">服务&nbsp;' + get_star(obj.star_fwtd) + '</div>' + '<div style="color:gray;font-size: 12px;margin-top: 6px;" title="' + obj.evaluate_text + '">' + obj.evaluate_text + '</div>')) : '') + (obj.state == 20 && obj.release_order != 1 ? ' <div style="margin-top: 20px; display: flex; justify-content: center;padding-top: 0;" releaseId="' + obj.sd_orderNo + '">                       <div style="display: flex; justify-content: center;">                        <div style="display: inline-block; border: 1px solid blue; border-radius: 50%; width: 130px; height: 130px; display: flex; justify-content: center; align-items: center;" class="countdown-1" data-duration="' + obj.duration + '">                            <div style="color: #5a5b5c;">                                <div style="color: blue;font-size: 18px;margin-bottom: 5px;padding: 10px;text-align: center;" class="countdown-text" >联系密信专员</div></div>                        </div>                    </div>                              <div style="display: flex;align-items: center;margin-left: 8px;flex-direction: column;justify-content: center;">   <a style="border-radius: 18px;padding: 12px 26px;background: #ff0008;color: #fff;cursor: pointer;border: 2px solid #c72d2e;margin-left: auto;font-weight: bold;font-size: 30px;display: inline-block;height: 60px;line-height: 60px;width: 139px;text-align: center;" onclick="release_order(\'' + obj.sd_orderNo + '\',1)">申请解冻</a>      </div>     </div> ' : '') + '       </div>        </div>');
                }




                $('.star-icon').on('click', function () {
                    var index = $(this).index();
                    console.log('star-index', index);
                    var ss = $(this).closest(".star-itmes").find('.star-icon');
                    for (var i = 0; i < 5; i++) {
                        if (i > index) {
                            ss.eq(i).attr("class", "star-icon");
                        } else {
                            ss.eq(i).attr("class", "star-icon active");
                        }
                    }
                })





            })
        }


        var get_star = function (s) {
            var text = '';
            for (var i = 0; i < s; i++) {
                text += '<svg t="1697998729342" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="4009" width="12" height="12"><path d="M493.9 77c162.3-0.8 98.2 190.6 208.5 234.6 26.6 10.6 64.1 1 96.6 6.1 71.6 11.4 148.1 14.9 159.5 85.9 19 118.7-150.7 159.3-170.2 236.1-21.2 83.6 112.5 256.8-19.9 297.5-112.6 34.6-167.9-81.5-249.9-95.1-93.2-15.3-155.7 161.9-280.7 85.9-100.6-61.1 28.7-202.6 0-296-22.9-74.3-153.9-93-171.7-193.2-17.7-99.5 80.2-109.6 153.3-121.1 45.4-7.2 94.8 7.2 122.7-15.3 59.4-47.9 53-163.7 115-210.1 12.3-5.1 24.5-10.2 36.8-15.3z" p-id="4010" fill="#FFB408"></path></svg>';
            }
            return text;
        }

        var share_evaluate = function (e, orderNo) {
            var el = $(e).closest(".evaluate_list");
            var evaluate_text = el.find('.evaluate_text').val();
            var wlfw = el.find('.star-itmes').eq(0).find(".active").length;
            var fhsd = el.find('.star-itmes').eq(1).find(".active").length;
            var fwtd = el.find('.star-itmes').eq(2).find(".active").length;

            v3api("share_evaluate"
                , { data: { id: orderNo, evaluate_text: evaluate_text, wlfw: wlfw, fhsd: fhsd, fwtd: fwtd } }
                , function (e) {
                    //tp('感谢您的评价！');

                    el.html('<div style="display:flex;align-items:center;justify-content: center;"><svg t="1698035464313" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="8453" width="30" height="30"><path d="M512 0C228.430769 0 0 228.430769 0 512s228.430769 512 512 512 512-228.430769 512-512S795.569231 0 512 0z m256 413.538462l-271.753846 271.753846c-7.876923 7.876923-19.692308 11.815385-31.507692 11.815384-11.815385 0-23.630769-3.938462-31.507693-11.815384l-169.353846-169.353846c-15.753846-15.753846-15.753846-47.261538 0-63.015385 15.753846-15.753846 47.261538-15.753846 63.015385 0l137.846154 137.846154 240.246153-240.246154c15.753846-15.753846 47.261538-15.753846 63.015385 0 19.692308 15.753846 19.692308 47.261538 0 63.015385z" fill="#07a35a" p-id="8454"></path></svg><span style="text-shadow: 5px 5px 5px #28832724;color: #07a35a;margin-left: 6px;font-size: 22px;font-weight: bold;">感谢您的评价</span></div>')

                })
        }

        var release_order = function (orderNo) {
            v3api("release_order"
               , { data: { id: orderNo } }
               , function (e) {
                   //tp('感谢您的评价！');

                   $('[releaseId="' + orderNo + '"]').html('<div style="display:flex;align-items:center;justify-content: center;"><svg t="1698035464313" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="8453" width="30" height="30"><path d="M512 0C228.430769 0 0 228.430769 0 512s228.430769 512 512 512 512-228.430769 512-512S795.569231 0 512 0z m256 413.538462l-271.753846 271.753846c-7.876923 7.876923-19.692308 11.815385-31.507692 11.815384-11.815385 0-23.630769-3.938462-31.507693-11.815384l-169.353846-169.353846c-15.753846-15.753846-15.753846-47.261538 0-63.015385 15.753846-15.753846 47.261538-15.753846 63.015385 0l137.846154 137.846154 240.246153-240.246154c15.753846-15.753846 47.261538-15.753846 63.015385 0 19.692308 15.753846 19.692308 47.261538 0 63.015385z" fill="#07a35a" p-id="8454"></path></svg><span style="text-shadow: 5px 5px 5px #28832724;color: #07a35a;margin-left: 6px;font-size: 22px;font-weight: bold;">已提交申请</span></div>')

               })
        }


        var type = '<%=Request.QueryString["type"] %>';





        var confirm_transaction = function (id) {
            event.stopPropagation();
            v3api("confirm_order", { data: { id: id } }, function (e) {
                tp('确认成功');
                var ls = $('[listid=' + id + ']');
                ls.remove();
                //ls.find(".statedata").html('<span style="border-radius:18px;padding:2px 8px;font-size:12px;background:#2A3EE8;color:#fff;">已完成</span>');
            })

        }


        var lastData = function () {
            try {
                if (type != "") {
                    $('.tablist').hide();
                    //$('.pptitle').html(type + '记录');
                }
            } catch (e) {

            }
        }
        if (type != "") {
            show_list(type);
            lastData();
        } else {
            show_list('');
        }



        initTabs(function (e) {
            console.log('select', e);
            $('#lists').html('');

            switch (e) {
                case "全部":
                    e = "";
                    break;
                case "进行中":
                    e = "1000";
                    break;
                case "已完成":
                    e = "1";
                    break;
                case "失败":
                    e = "-1,2,20";
                    break;
                default:
                    break;
            }

            show_list(e);
        })
    </script>
</asp:Content>


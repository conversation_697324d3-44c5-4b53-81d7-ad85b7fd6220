body, html, form {
    margin: 0px;
    padding: 0px;
}

body {
    background: #f8f8f8;
    font: Helvetica Neue, Helvetica, PingFang SC, Tahoma, Arial, sans-serif;
    max-width: 600px;
    margin: 0 auto;
}

.ulk_menu {
    position: fixed;
    left: 0px;
    bottom: 0;
    display: flex;
    width: 100%;
    font-size: 14px;
    max-width: 600px;
    left: 50%;
    transform: translateX(-50%);
    background: url(data:image/png;base64,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);
    background-repeat: no-repeat;
    background-size: 100% auto;
    -moz-background-size: 100% auto;
    background-position: bottom;
    z-index: 999;
}

    .ulk_menu .main_button {
        background: #FBE64E;
        border-radius: 50%;
        width: 280px;
        position: relative;
        top: -8px;
    }


    .ulk_menu > div {
        width: 100%;
        text-align: center;
        padding: 5px 0;
        color: #222;
        transition: all 0.3s;
    }

    .ulk_menu svg {
        height: 28px;
        width: 28px;
    }

    .ulk_menu > div:not(.issel) svg path {
        fill: currentColor;
    }

    .ulk_menu > div:not(.issel) svg .light_point {
        display: none;
    }


    .ulk_menu > div.issel {
        color: black;
    }



.main-container {
    padding: 10px;
    box-sizing: border-box;
}

.top-title {
    background: #fff;
    text-align: center;
    padding: 18px 12px;
    position: relative;
    display: none;
    position: fixed;
    width: 100%;
    top: 0;
    left: 0;
    box-sizing: border-box;
    z-index: 9999999;
}

    .top-title .pptitle {
        margin: 0px;
        font-size: 16px;
    }





/* 弹出页面样式 */

.demo-popup-page {
    position: fixed;
    left: 0;
    bottom: 0;
    background: #fff;
    width: 100%;
    border-top: 1px solid #eee;
}

.popup-container {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    min-height: 200px;
    background-color: #fff;
    z-index: 9999;
    transition: transform 0.1s ease-in-out;
    display: none;
    left: 50%;
    max-width: 500px;
    transform: translate(-50%,100%);
    border-top-left-radius: 8px;
    border-top-right-radius: 8px;
}

.popup-show {
    transform: translate(-50%,0);
}

/* 蒙版样式 */
.popup-mask {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 9998;
    display: none;
}

/* 关闭按钮样式 */
.popup-close {
    position: absolute;
    top: 10px;
    right: 10px;
    font-size: 24px;
    cursor: pointer;
}



/* 可以添加其他样式，例如背景颜色、宽度等 */
.password-content, .poptip-content {
    background-color: #000000db;
    padding: 20px 20px;
    color: #fff;
    border-radius: 8px;
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    padding: 20px;
    z-index: 99999999; /* 可选：设置 z-index 来控制显示顺序 */
    opacity: 0;
    /*text-wrap: nowrap;*/
    pointer-events: none; /* 设置不捕获鼠标事件 */
    text-align: center;
}

.info_title {
    color: #000;
    font-size: 14px;
    font-weight: bold;
    display: flex;
    align-items: center;
    margin: 10px 0;
    margin-top: 18px;
}

    .info_title i {
        display: inline-block;
        background: #2A3EE8;
        width: 6px;
        height: 6px;
        border-radius: 50%;
    }

.select_tab {
    width: 50%;
    color: #000;
    box-sizing: border-box;
    padding: 10px 12px;
    text-align: center;
    font-weight: bold;
    font-size: 13px;
    cursor: pointer;
}

    .select_tab.activity {
        background: #2B3DE9;
        color: #fff;
        border-radius: 30px;
        transition: all 0.2s;
    }




/*tab插件*/

.tablist {
    width: 100%;
    overflow-x: auto;
    white-space: nowrap;
    display: flex;
    align-items: center; /* 垂直居中 */
    justify-content: flex-start; /* 初始位置 */
    flex-wrap: wrap;
}

.gtab {
    width: 33.33%;
    text-align: center;
    /*background-color: #ccc;*/
    text-align: center;
    /*margin: 0 10px;*/
    flex-shrink: 0; /* 防止元素缩小 */
    padding: 12px 0px;
    border-radius: 18px;
    box-sizing: border-box;
    color: #000;
    font-weight: bold;
    font-size: 15px;
    cursor: pointer;
}

    .gtab.act {
        background: #2D41E6;
        color: #fff;
    }

/* 隐藏水平滚动条 */
.tablist::-webkit-scrollbar {
    width: 0.5em;
}

.tablist::-webkit-scrollbar-track {
    background-color: transparent;
}

.tablist::-webkit-scrollbar-thumb {
    background-color: rgba(0, 0, 0, 0);
}


/*菜单*/
.menu {
    background: #fff;
    text-align: center;
    padding: 10px 12px;
    margin-top: 10px;
    border-radius: 15px;
    display: flex;
}

.menu-title {
    font-size: 20px;
    margin-bottom: 20px;
}

.menu-item {
    display: inline-block;
    width: 100%;
    text-align: center;
    cursor: pointer;
    color: #bbb!important;
    outline: none;
    text-decoration: none;
}

.menu-icon {
    width: 50px;
    height: 50px;
}


.menu-item div {
    color: #1e1a1a;
    margin-top: 3px;
}

.menu-list {
    display: block;
}

    .menu-list .menu-item {
        display: flex;
        padding: 18px 12px;
        box-sizing: border-box;
        align-items: center;
    }

    .menu-list div {
        margin-left: 10px;
        font-size: 15px;
        font-weight: bold;
    }

    .menu-list .right-icon {
        display: inline-block;
        margin-left: auto;
        width: 13px;
        height: 13px;
    }

        .menu-list .right-icon path {
            fill: currentColor;
        }


.code_send {
    background: none!important;
    color: #000!important;
}




.pg-password-input {
    display: none;
}


.password-content {
    opacity: 1;
    display: block;
    pointer-events: all; /* 设置不捕获鼠标事件 */
    background: #fff;
    padding: 0px;
    z-index: 99999991;
}

.password-input {
    background: #f1f1f1;
    width: 38px;
    height: 38px;
    border-radius: 3px;
    border: 1px solid #f1f1f1;
    text-align: center;
    line-height: 45px;
    color: #6868db;
    outline: none;
    margin: 0 2px;
    font-size: 20px;
    font-weight: bold;
}

    .password-input:focus, .password-input:hover {
        border: 1px solid blue;
        background: none;
    }


.loginpwd-input {
    background: #f1f1f1;
    width: 250px;
    padding: 10px;
    color: #3838f5;
    border: 1px solid #f1f1f1;
    outline: none;
    font-size: 20px;
}

    .loginpwd-input:focus, .loginpwd-input:hover {
        border: 1px solid blue!important;
        background: none;
    }

.confirm_password_button {
    background: #f1f1f1;
    color: #bbb;
    display: inline-block;
    width: 130px;
    height: 45px;
    line-height: 45px;
    border-radius: 12px;
    cursor: pointer;
}



        .design-button-full {
            background: #3255FF;color: #fff!important;padding: 3px 10px;border-radius: 3px;font-size: 12px;font-weight: bold;
        }
        .design-button-hollow {
            border: 1px solid #3255FF;color: #3255FF!important;padding: 3px 10px;border-radius: 3px;font-size: 12px;font-weight: bold;
        }
        .design-button-common {
            background: #E0E6FF;color: #556cdf!important;padding: 3px 10px;border-radius: 12px;font-size: 12px;font-weight: bold;
        }

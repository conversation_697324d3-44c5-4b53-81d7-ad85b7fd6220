/* Axuta By IconicThemes */

/*  ==========================================================================
    Table of Contets
    ==========================================================================
    
    1.0 Common Styles
    2.0 Header Section
    3.0 Hero Section
    4.0 About Section
    5.0 Feature Section
    6.0 Video Section
    7.0 Screenshot Section
    8.0 Pricing Section
    9.0 Counter Section
    10.0 Testimonial Section
    11.0 Blog Section
    12.0 Download Section
    13.0 Subscribe Section
    14.0 Footer Section
    15.0 Scrool To Top
    
    ==========================================================================
    Axuta App Landing Page Version 0.1
    ========================================================================== */

	@import url('https://fonts.googleapis.com/css?family=Arimo:400,700|Open+Sans');
	
/*  ==========================================================================
    Common Styles
    ========================================================================== */
*{
	padding: 0;
	margin: 0;
}
body{
    background-color: #fff;
    font-size: 17px;
    line-height: 27px;
    color: #777;
    font-weight: 400;
    letter-spacing: -0.2px;
    position: relative;
    overflow-x: hidden;
    margin:0 auto;
}
h1, h2, h3, h4, h5, h6{
    font-family: 'Arimo', sans-serif;
    -webkit-font-smoothing:antialiased;
	color: #333;
}
h1{
    font-size: 40px;
    font-weight: 700;
    line-height: 1.2;
    margin: 0 0 10px;
    color: #fff;
    letter-spacing: -0.05em;
}
h2{
    font-size: 42px;
    color: #333;
    margin: 0 0 10px;
    font-weight: 600;
    line-height: 1.2;
    letter-spacing: -0.04em;
}
h3,h4{
    margin: 0 0 10px;
    font-weight: 600;
    line-height: 1.7;
    color: #333;
    letter-spacing: -0.01em;
}
h3{
    font-size: 18px;
}
h4{
    font-size: 16px;
}
h5,h6{
    font-size: 14px;
    margin: 0 0 10px;
}
img{
    border: none;
    outline:none;
    max-width: 100%;
}
ul{
    display: block;
    list-style: none;
    padding: 0;
    margin: 0;
}
p, li, a, span{}
p{
    font-size: 15px;
    margin-bottom: 15px;
}
a, a:hover{
    text-decoration: none;
}
a:focus{
    outline: 0;
    text-decoration: none;
}
input::-webkit-input-placeholder,
textarea::-webkit-input-placeholder{
   color: #ddd !important;
}

input:-moz-placeholder,
textarea:-moz-placeholder{ /* Firefox 18- */
   color: #ddd !important;  
}

input::-moz-placeholder,
textarea::-moz-placeholder{  /* Firefox 19+ */
   color: #ddd !important;  
}

input:-ms-input-placeholder,
textarea:-ms-input-placeholder{  
   color: #ddd !important;  
}
button{
    border: none;
	background: none;
}
/* Helper Class */
.padding{
    padding: 100px 0;
}
.no-padding{
    padding: 0;
}
.padding-15{padding: 15px;}
.padding-20{padding: 20px;}

.bg-white{ background-color: #fff; }
.bg-grey{ background-color: #eff4ff;}
.bg-dark{ background-color: #232323;}
.bd-top{ border-top: 1px solid #eaeaea;}
.bd-bottom{ border-bottom: 1px solid #eaeaea;}

.mb-10{ margin-bottom: 10px; }
.mb-15{ margin-bottom: 15px; }
.mb-20{ margin-bottom: 20px; }
.mb-25{ margin-bottom: 25px; }
.mb-30{ margin-bottom: 30px; }
.mb-35{ margin-bottom: 35px; }
.mb-40{ margin-bottom: 40px; }
.mb-45{ margin-bottom: 45px; }
.mb-50{ margin-bottom: 50px; }
.mb-55{ margin-bottom: 55px; }
.mb-60{ margin-bottom: 60px; }
.ml-15{ margin-left: 15px; }
.ml-20{ margin-left: 20px; }
.ml-25{ margin-left: 25px; }
.ml-30{ margin-left: 30px; }
.ml-35{ margin-left: 35px; }
.fz-28{ font-size: 28px; }
.fz-24{ font-size: 24px; }
.fz-22{ font-size: 22px; }
.fz-20{ font-size: 20px; }
.fz-18{ font-size: 18px; }
.fz-16{ font-size: 16px; }
.text-black{
    color: #333;
}
.text-white{
    color: #ffffff;
}
.align-left{
    text-align: left;
}
.align-right{
    text-align: right;
}
.align-center{
    text-align: center;
}
.fl-right{
    float: right;
}
.fl-left{
    float: left;
}
.display-table{
    width: 100%;
    height: 100%;
    display: table;
}
.table-cell{
    display: table-cell;
    vertical-align: middle;
}

.overlay{
    width: 100%;
    position: relative;
    z-index: 1;
}
.overlay:before{
    content: '';
    width: 100%;
    height: 100%;
    position: absolute;
    left: 0;
    top: 0;
    z-index: -1;
}
.gradiant-bg{
    background: linear-gradient(90deg,#e91e63 0%,#ff5722 100%);
}
/* Preloader Styles */
.loaded .site-preloader-wrap {
    opacity: 0;
    visibility: hidden;
}
.site-preloader-wrap {
    position: fixed;
    z-index: 999;
    height: 100%;
    width: 100%;
    background: #272c30;
    top: 0;left: 0
}

.site-preloader-wrap .spinner {
    background-color: #f65a7d;
    position: absolute;
    left: 50%;
    top: 50%;
    margin-left: -20px;
    margin-top: -20px;
}

.spinner {
  width: 40px;
  height: 40px;

  border-radius: 100%;  
  -webkit-animation: sk-scaleout 1.0s infinite ease-in-out;
  animation: sk-scaleout 1.0s infinite ease-in-out;
}

@-webkit-keyframes sk-scaleout {
  0% { -webkit-transform: scale(0) }
  100% {
    -webkit-transform: scale(1.0);
    opacity: 0;
  }
}

@keyframes sk-scaleout {
  0% { 
    -webkit-transform: scale(0);
    transform: scale(0);
  } 100% {
    -webkit-transform: scale(1.0);
    transform: scale(1.0);
    opacity: 0;
  }
}

/* Button Style */
.btn-group{}
.btn-group a{
    margin: 5px;
}
.h5-btn {
  font-size: 16px;
  border-radius: 5px;
  line-height: 50px;
  font-weight: 600;
  display: inline-block;
  padding: 0 25px;
  letter-spacing: -0.02em;
}
.default-btn{
    background-color: #1151d3;
    text-transform: uppercase;
    color: #fff;
    font-size: 12px;
    border-radius: 5px;
    line-height: 50px;
    font-weight: 600;
    display: inline-block;
    padding: 0 25px;
    letter-spacing: -0.02em;
}
.default-btn:hover{
    background-color: #f65a7d;
    color: #fff;
}
.btn-group a img:hover{
    opacity: 0.9;
}
/* Transition Effect */
a,a:hover, .overlay, img, .form-control,  .form-control:hover, button{
    -webkit-transition: all 0.5s;
    -o-transition: all 0.5s;
    transition: all 0.5s;
}
/* ==========================================================================
   Header Section
   ========================================================================== */
.header-section{
    background-color: #fff;
    width: 100%;
    z-index: 99;
    position: fixed;
    left: 0;
    top: 0;
    padding: 3px 0;
}
.navbar-fixed-top.header-section{
    background-color: #fff;
    box-shadow: 0px 16px 28px 0px rgba(0, 0, 0, 0.05);
    border-bottom: 0;
    padding: 0;
}

.navbar-fixed-top ul.nav > li > a{
    color: #555;
}
.navbar-fixed-top ul.nav > li > a:hover,
.navbar-fixed-top ul.nav > li > a:focus,
.navbar-fixed-top ul.nav > li.active > a{
    color: #222;
}
.logo-light,
.navbar-fixed-top .logo-light,
.logo-dark,
.navbar-fixed-top .logo-dark,
.navbar-fixed-top.header-section,
.header-section{
    transition: all 0.3s ease-in-out;
}
.navbar{
    border: medium none;
    -moz-border-radius: 0;
    -webkit-border-radius: 0;
    -ms-border-radius: 0;
    border-radius: 0;
    margin: 0;
    position: relative;
    padding: 0;
}
.navbar .logo {
    height: 30px;
}
.navbar-header {
    padding: 17px 0;
}
.navbar-header .brand{
    font-size: 25px;
    color: #333;
    letter-spacing: -1px;
    font-weight: 600;
	margin-left: 13px;
}
.navbar-header .nav-btn{
    padding: 8px;
    border-radius: 1px;
}
.navbar-header .nav-btn .icon-bar{
    background-color: #fff;
    width: 18px;
}
.navbar-fixed-top .navbar-header .nav-btn .icon-bar{
    background-color: #777;
}
.navbar-header .nav-btn:hover,
.navbar-header .nav-btn:focus{
    opacity: 0.8;
}

/* Nav Menu */
#navbar{
    z-index: 999;
    padding: 0 15px;
}
ul.nav{}
ul.nav > li{}
ul.nav > li > a{
    color: #555;
    display: inline-block;
    vertical-align: middle;
    padding: 0 10px;
    letter-spacing: 1px;
    font-size: 14px;
    font-weight: 600;
    text-transform: uppercase;
    line-height: 60px;
    z-index: 1;
}
ul.nav > li > a:hover,
ul.nav > li > a:focus,
ul.nav > li > a.active{
    background: none;
    color: #555;
    opacity: 1;
}
.menu-btn{
    margin-left: 10px;
    margin-top: 10px;
}
.header-btn .default-btn{
    line-height: 40px;
    font-size: 12px;
    letter-spacing: 0;
}

.header-btn{
    line-height: 55px;
    margin-left: 10px;
    
}
/*Mobile Menu */
.menu-wrap{
    position: relative;
}
.slicknav_menu {
    background: none;
    padding: 4px 0;
    display:none;
    width: 100%;
    position: absolute;
    right: 0;
    top: 0;
}
.slicknav_nav {
    background-color: #fff;
}
.slicknav_btn{
    background-color: transparent;
    margin: 8px 5px 15px;
}
.header-section.navbar-fixed-top .slicknav_btn{
    margin: 8px 5px 12px;
}
.slicknav_nav .slicknav_row:hover,
.slicknav_nav .slicknav_row, 
.slicknav_nav a,
.slicknav_nav a:hover{
    -moz-border-radius: 0;
    -webkit-border-radius: 0;
    -o-border-radius: 0;
    border-radius: 0;
}
.slicknav_nav .slicknav_row, 
.slicknav_nav a {
    padding: 10px 15px;
    margin: 0;
    color: #777;
}
.slicknav_nav a .caret{
    display: none;
}
.slicknav_nav ul{
    margin: 0;
}
.slicknav_nav ul li a{
    padding-left: 30px;
    font-size: 12px;
}
.slicknav_nav .slicknav_row:hover,
.slicknav_nav a:hover{
    background-color: transparent;
    color: #333;
}

/* ==========================================================================
   Hero Section
   ========================================================================== */
.hero-section{
    position: relative;
    width: 100%;
    height: 100vh !important;
    display: flex;
    align-items: center;
    z-index: 1;
}
.hero-section:before{
    background-image: url(../img/hero-wave.png);
    background-repeat: no-repeat;
    background-position: right center;
    content: "";
    position: absolute;
    width: 100%;
    height: 100%;
    left: 0;
    top: 0;
    z-index: -1;
}
.hero-content{}
.hero-content h1{
    font-size: 46px;
    color: #222;
}
.hero-moc{
    background-image: url(../img/mockup-1.png);
    background-position: bottom right;
    background-repeat: no-repeat;
    content: "";
    position: absolute;
    width: 550px;
    height: 600px;
    vertical-align: middle;
    bottom: 0;
    right: 120px;
}
/* ==========================================================================
   Promo Section
   ========================================================================== */
.promo-section{}
.promo-content{
    padding: 0 15px;
}
.promo-content h3{
    margin-bottom: 5px;
    font-size: 22px;
}
.promo-content p{
    margin-bottom: 0;
}
.promo-content .icon{
    font-size: 50px;
}
.promo-content i{
    background-image: linear-gradient(-45deg, #2b18dd 0%, #1151d3 50%, #1151d3 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    font-weight: 700;
    margin-bottom: 10px;
}
/* ==========================================================================
   Content Section
   ========================================================================== */
.content-section{
    overflow: hidden;
}
.content-mockups{
    position: relative;
}
.mockup-front{
    background-image: url(../img/mockup-1.png);
    background-position: bottom right;
    background-repeat: no-repeat;
    content: "";
    position: absolute;
    width: 550px;
    height: 600px;
    left: 0;
    top: -100px;
}
/* .mockup-back{
    background-image: url(../img/mockup-2.png);
    background-repeat: no-repeat;
    background-position: center center;
    content: "";
    position: absolute;
    width: 500px;
    height: 500px;
    left: 80px;
    top: 0;
} */
.content-list{}
.list-item{
    display: flex;
    align-items: center;
}
.list-item:not(:last-of-type){
    margin-bottom: 35px;
}
.list-content{
    display: block;
    padding-left: 25px;
}
.list-content h3{
    font-size: 22px;
    margin-bottom: 0;
}
.list-content p{
    margin-bottom: 0;
}
.list-item i{
    background-image: linear-gradient(-45deg, #2b18dd 0%, #1151d3 50%, #1151d3 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    font-weight: 700;
    font-size: 40px;
}
/* ==========================================================================
   Feature Section
   ========================================================================== */
.features-section{}
.feature-wrap{
    margin-top: -15px;
    margin-bottom: -15px;
}
.feature-wrap .col-sm-6{
    padding: 10px;
}
.feature-content{
    background-color: #eff4ff;
    padding: 40px 30px;
    transition: all 0.2s ease-in-out;
}
.feature-content:hover{
    box-shadow: 0 5px 10px 0 rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease-in-out;
}
.feature-content i{
    background-image: linear-gradient(-45deg, #2b18dd 0%, #1151d3 50%, #1151d3 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    font-weight: 700;
    font-size: 40px;
    margin-bottom: 10px;
}
.feature-content h3{
    font-size: 22px;
    margin-bottom: 0;
}
/* ==========================================================================
   Screenshots Section
   ========================================================================== */
.screenshots-section{
    padding-bottom: 53px;
}
.swiper-container {
    padding: 3.58rem 0 7rem 0;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
}
.swiper-container .swiper-slide{
    transform: scale(.65);
    opacity: 0.2;
    -webkit-transition: all .3s ease;
    transition: all .3s ease
}
.swiper-container .swiper-slide-next, 
.swiper-container .swiper-slide-prev{
    transform: scale(.75);
    opacity: 0.85;
}
.swiper-container .swiper-slide-active{
    opacity: 1;
    transform: scale(1);
     z-index: 100;
}
.swiper-container .screen{
    width: 212px;
    height: 376px;
    margin: 0 auto;
    -webkit-box-shadow: 0 0 1px rgba(0, 0, 0, .1), 0 2px 6px rgba(0, 0, 0, .03);
    box-shadow: 0 0 1px rgba(0, 0, 0, .1), 0 2px 6px rgba(0, 0, 0, .03);
}
.swiper-container .mobile-mock {
    background: #fff;
    border: 1px solid #edf4f8;
    margin: 0 auto;
    position: absolute;
    top: 0;
    max-width: 240px;
    left: 0;
    right: 0;
    width: 240px;
    border-radius: 36px;
    padding: 55px 0;
    -webkit-box-shadow: inset 0 4px 10px 1px #fff, inset 0 0 6px 0 rgba(66, 77, 86, .5), 0 2px 0 #aeb5bb, 0 20px 50px -25px rgba(0, 0, 0, .5);
    box-shadow: inset 0 4px 10px 1px #fff, inset 0 0 6px 0 rgba(66, 77, 86, .5), 0 2px 0 #aeb5bb, 0 20px 50px -25px rgba(0, 0, 0, .5)
}
.swiper-container .mobile-mock:after,
.swiper-container .mobile-mock:before {
    content: '';
    position: absolute
}
.swiper-container .mobile-mock:before {
    width: 45px;
    height: 4px;
    background: #e3e8eb;
    -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, .05);
    box-shadow: inset 0 1px 1px rgba(0, 0, 0, .05);
    border-radius: 30px;
    left: 0;
    right: 0;
    margin: -25px auto 0
}
.swiper-container .mobile-mock:after {
    top: 30px;
    left: 50%;
    width: 6px;
    height: 6px;
    margin-left: -45px;
    background: #e3e8eb;
    -webkit-box-shadow: 0 0 0 2px rgba(0, 0, 0, .03);
    box-shadow: 0 0 0 2px rgba(0, 0, 0, .03);
    display: block;
    border-radius: 50%
}
.swiper-container .mobile-mock .screen {
    width: 90%;
    margin: 0 auto
}
.swiper-container .mobile-mock .circle {
    position: absolute;
    left: 50%;
    -webkit-box-shadow: 0 0 1px 2px #e3e8eb inset;
    box-shadow: 0 0 1px 2px #e3e8eb inset;
    border: 1px solid #edf4f8;
    border-radius: 50%;
    bottom: 1.75%;
    height: 0;
    margin-left: -18px;
    padding-top: 36px;
    width: 36px
}
.swiper-container .swiper-next,
.swiper-container .swiper-prev{
    width: 30px;
    height: 30px;
    line-height: 30px;
    color: #1151d3;
    font-size: 30px;
    opacity: 0.8;
    display: block;
    text-align: center;
    position: absolute;
    left: 0;
    top: calc(50% - 15px);
    cursor: pointer;
    transition: all 0.3s ease-in-out;
    z-index: 99;
}
.swiper-container .swiper-next{
    left: auto;
    right: 0;
}
.swiper-container .swiper-next:hover,
.swiper-container .swiper-prev:hover{
    opacity: 1;
    transition: all 0.3s ease-in-out;
}
/* ==========================================================================
   Download Section
   ========================================================================== */
.download-section{
    position: relative;
    padding: 60px 0;
}
.download-content{}
.download-content h2{
    font-weight: 700;
}
/* ==========================================================================
   Blog Section
   ========================================================================== */
.blog-section{}
.blog-box{}
.blog-thumb{
    position: relative;
}
.blog-thumb img{
    width: 100%;
}
.blog-content{
    padding: 40px 15px;
    padding-bottom: 0;
}
.blog-content a{
    font-size: 20px;
    font-weight: 600;
    color: #333;
    line-height: 24px;
    display: block;
    margin-bottom: 10px;
    text-transform: capitalize;
}
.blog-content a:hover{
    color: #1151d3;
}
.post-meta{
    text-align: center;
    position: absolute;
    left: 0;
    bottom: -20px;
    width: 100%;
    height: auto;
}
.post-meta div{
    background-color: #fff;
    box-shadow: 0 5px 10px 0 rgba(0, 0, 0, 0.1);
    padding: 10px 15px;
    display: inline-block;
}
.post-meta span {
    font-size: 14px;
    font-weight: 600;
    text-transform: capitalize;
    color: #555;
    margin-right: 10px;
}
.post-meta i{
    color: #1151d3;
    margin-right: 5px;
}
.blog-post{}
.blog-content .read-more{
    background-color: #1151d3;
    font-size: 14px;
    font-weight: 500;
    text-transform: capitalize;
    color: #fff;
    text-align: center;
    padding: 5px 20px;
    border-radius: 2px;
    display: inline-block;
    transition: all 0.2s ease-in-out;
    margin: 0;
}
.blog-content .read-more:hover{
    background-color: #f65a7d;
    color: #fff;
    box-shadow: 0 5px 10px 0 rgba(0, 0, 0, 0.1);
    -webkit-transform: translateY(1.1);
    transition: all 0.2s ease-in-out;
}
/* ==========================================================================
   Subscribe Section
   ========================================================================== */
.subscribe-section{
    background-color: #1151d3;
}
.subscribe-section .logo{
    margin-bottom: 30px;
}
.subscribe-content{
    display: block;
    margin-bottom: 40px;
}
.subscribe-content p{
    color: #fff;
}
.subscribe-wrap{
    display: block;
    text-align: center;
    margin-bottom: 25px;
}
.subscribe-form{
    display: block;
    text-align: center;
    width: 450px;
    margin: 0 auto;
    position: relative;
}
.subscribe-form .form_input{
    background-color: transparent;
    border: none;
    border-bottom: 1px solid #f65a7d;
    display: block;
    color: #fff;
    font-size: 14px;
    line-height: 58px;
    padding: 0 15px;
    float: left;
    width: 100%;
    outline: none;
    border-radius: 0;
    padding-right: 110px;
}
.subscribe-form .submit{
    position: absolute;
    right: -3px;
    top: 4px;
    height: 55px;
    width: 120px;
    text-transform: uppercase;
    font-size: 12px;
    font-weight: 600;
    background-color: #f65a7d;
    color: #fff;
}
.subscribe-form .submit:hover{
    cursor: pointer;
    color: #fff;
}
#subscribe-result{
    display: none;
    margin-top: 15px;
    padding: 10px;
    border: 1px solid #111;
}
#subscribe-result.subs-result{
    display: block;
}
.subscription-success,
.subscription-error{
    color: #fff;
}
.social-link{
    text-align: center;
    margin-top: 30px;
}
.social-link li{
    display: inline-block;
    margin-right: 20px;
}
.social-link li a{
    color: #fff;
    display: inline-block;
    font-size: 14px;
    height: 100%;
    width: auto;
}
.social-link li a:hover{
    cursor: pointer;
}
/* ==========================================================================
   Footer Section
   ========================================================================== */
.footer-section{
    background-color: #222;
    display: block;
    color: #fff;
    text-align: center;
    padding: 30px 0;
}
.footer-section p{
    text-transform: uppercase;
    font-size: 12px;
    margin: 0;
}  

/* ==========================================================================
   Scroll To Top
   ========================================================================== */
#scroll-to-top{
    background-color: #1151d3;
    display: none;
    width: 45px;
    height: 45px;
    text-align: center;
    font-size: 14px;
    border-radius: 50%;
    line-height: 45px;
    color: #fff;
    position: fixed;
    bottom: 50px;
    right: 50px;
    z-index: 999;
}
#scroll-to-top:hover{
    opacity: 0.8;
    color: #fff;
}
/*** Footer ***/
.footer {
    background: url(../img/footer.png) center center no-repeat;
    background-size: contain;
}

.footer .btn.btn-social {
    margin-right: 5px;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: rgba(255,255,255,.5);
    border: 1px solid rgba(256, 256, 256, .1);
    border-radius: 40px;
    transition: .3s;
}

.footer .btn.btn-social:hover {
    color: var(--primary);
}

.footer .btn.btn-link {
    display: block;
    margin-bottom: 10px;
    padding: 0;
    text-align: left;
    color: rgba(255,255,255,.5);
    font-weight: normal;
    transition: .3s;
    text-decoration: none;
}

.footer .btn.btn-link:hover {
    color: #FFFFFF;
}

.footer .btn.btn-link::before {
    position: relative;
    content: "\f105";
    font-family: "Font Awesome 5 Free";
    font-weight: 900;
    margin-right: 10px;
}

.footer .btn.btn-link:hover {
    letter-spacing: 1px;
    box-shadow: none;
}

.footer .copyright {
    padding: 25px 0;
    font-size: 14px;
    border-top: 1px solid rgba(256, 256, 256, .1);
}

.footer .copyright a {
    color: rgba(255,255,255,.5);
}

.footer .copyright a:hover {
    color: #FFFFFF;
}

.footer .footer-menu a {
    margin-right: 15px;
    padding-right: 15px;
    border-right: 1px solid rgba(255, 255, 255, .1);
}

.footer .footer-menu a:last-child {
    margin-right: 0;
    padding-right: 0;
    border-right: none;
}

.bg-dark {
    background-color: #14183e !important;
}

.hero-content .mobile {
    text-align: center;
    display: block;
}

.hero-content .mobile .default-btn {
    display: block;
    font-size: 16px;
}

.scrollsidebar {
    position: absolute;
    top: 480px;
    right: 0;
    border-radius: 10px;
    padding: 6px;
    z-index: 1000;
    /* background-color: #ffff; */
    width: 62px;
    height: 160px;
}

.scrollsidebar  .item {
    width: 52px;
    height: 58px;
    cursor: pointer;
    display: inline-block;
}

.down-btn {
    margin: 3px 0 5px 0;
    line-height: 20px;
    padding: 14px 10px;
    font-size: 16px;
    border-radius: 10px;
    color: #1590FF;
    border: 1px solid #1590FF;
    background: rgba(21, 144, 255, 0.10);
}

.down-btn a {
    vertical-align: middle;
}

.down-btn .icon {
    width: 16px;
    margin-left:5px;
    border-radius:3px;
}


/*
   Olio Theme Responsive Design
*/
@media (max-width: 1170px) {
    .hero-moc{
        right: 50px;
    }
    .swiper-container{
        width: 70%;
    }
}
@media (max-width: 1024px) {
    .hero-moc{
        right: 0;
    }
    .hero-section{
        height: 650px;
    }
    .hero-section:before{
        background-position: center center;
        background-size: cover;
    }
}

/* ---- Start of max-width 992px CSS ---- */
@media (max-width: 992px) {
    .sm-padding{ padding: 15px; }
    .feature_list,
    .about_content {
        padding: 0;
    } 
    .hero_mockup{
        background-position: 120% center;
    }
    ul.nav > li > a {
        padding: 0 5px;
        font-size: 10px;
    }
    .hero-moc{
        display: none;
    }
    .promo-content{
        padding: 0;
    }
    .content-mockups{ display: none; }
    .content-info{
        padding-bottom: 65px;
        padding-top: 0;
    }
    .download-btn a{
        margin-right: 0px;
        padding: 14px 10px;
        font-size: 10px;
    }
    .download-content h2{
        font-size: 24px;
    }
    .download-content{
        padding: 0;
    }
    .hero-section{
        height: 570px;
    }
    .swiper-container{
        width: 260px;
    }
    .swiper-container .swiper-slide{ text-align: center; }
    .swiper-container .swiper-slide img,
    .swiper-container .swiper-slide-active img{ width: 80%!important; }
    .swiper-container .swiper-next, 
    .swiper-container .swiper-prev{ display: none; }

}
/* ---- End of max-width 992px CSS ---- */


/* ---- Start of min-width 480px and max-width 768px CSS ---- */
@media all and (min-width: 480px) and (max-width: 768px) {
	
}
/* ---- End of min-width 480px and max-width 768px CSS ---- */


/* ---- Start of max-width 767px CSS ---- */
@media all and (max-width: 767px) {
    body, p{
        font-size: 12px;
        line-height: 22px;
    }
    h1{
        font-size: 30px!important;
    }
    h2{
        font-size: 20px;
    }
    h3{
        font-size: 16px;
    }
    .mock-hide,
    p br{
        display: none;
    }
    .xs-d-none{
        display: none;
    }
    .padding{
        padding: 60px 0;
    }
    .xs-padding{
    	padding: 15px;
    }
    .navbar-brand{
        padding: 11px 0;
    }
    .header-btn,
    .menu-wrap ul.nav{
        display: none;
    }
    .slicknav_menu {
        display:block;
    }
    .content-info{
        padding-bottom: 0;
    }
    .download-content{
        text-align: center;
    }
    
    
}
/* ---- End of max-width 767px CSS ---- */


/* ---- Start of max-width 520px CSS ---- */
@media all and (max-width: 520px) {
    .subscribe-form{ width: 100%; }
    .download-content h2{
        font-size: 20px;
    }
    .download-btn a:first-child{
        margin-bottom: 10px;
    }
    .social-link li{
        margin: 0;
    }
}
/* ---- End of max-width 520px CSS ---- */


/* ---- Start of max-width 420px CSS ---- */
@media all and (max-width: 420px) {
    
}
/* ---- End of max-width 420px CSS ---- */


/* ---- Start of max-width 380px CSS ---- */
@media all and (max-width: 380px) {
	
}
/* ---- End of max-width 380px CSS ---- */
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

public partial class order_list : globalClass
{
    public DataTable userdt = new DataTable();
    public DataTable payTotal = new DataTable();
    protected void Page_Load(object sender, EventArgs e)
    {
        List<SqlParameter> pams = new List<SqlParameter>();
        dbClass db = new dbClass();
        pams.Add(new SqlParameter("@id", Request.QueryString["id"] + ""));
        pams.Add(new SqlParameter("@userid", uConfig.p_uid));


        string sql = string.Empty;
        sql = @" 
select * from buy_list with(nolock) where orderId=@id 
 ";

        //SELECT channel_id,COUNT(0) as total_number FROM [api_orderList] with(nolock) where userid=@userid and gateway_network='美宜佳' and channel_id is not null and state=1 and datediff(day,create_time,getdate())=0 group by channel_id 

        DataSet ds = new DataSet();
        ds = db.getDataSet(sql, pams.ToArray());
        userdt = ds.Tables[0];
        //payTotal = ds.Tables[1];

    }
}
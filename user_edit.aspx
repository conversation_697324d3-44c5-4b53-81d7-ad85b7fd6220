<%@ Page Title="" Language="C#" MasterPageFile="~/top.master" AutoEventWireup="true" CodeFile="user_edit.aspx.cs" Inherits="order_list" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="Server">
    <script>
        $(function () {
            popTitle('用户信息', '');
        })
    </script>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="Server">

    <div class="menu menu-list" style="background:none;">
        <a class="menu-item" onclick="upload_avatar()">
            <%--<svg t="" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="3216" width="18" height="18" style="position: relative; top: 2px;">
                <path d="M873.856 65.984L360.3968 579.456a50.4576 50.4576 0 1 0 71.3472 71.3472L945.216 137.344a50.4576 50.4576 0 1 0-71.3472-71.3472z" fill="#455CED" p-id="3217"></path><path d="M615.8336 54.4H375.2064C198.0288 54.4 54.4 198.0288 54.4 375.2064v273.5872c0 177.1776 143.6288 320.8064 320.8064 320.8064h273.5872c177.1776 0 320.8064-143.6288 320.8064-320.8064V481.984a47.2192 47.2192 0 0 0-94.4384 0v166.8096c0 125.0176-101.3504 226.368-226.368 226.368H375.2064c-125.0176 0-226.368-101.3504-226.368-226.368V375.2064c0-125.0176 101.3504-226.368 226.368-226.368h240.64a47.2192 47.2192 0 0 0 0-94.4384z" fill="#1F1F1F" p-id="3218"></path></svg>--%>
            <div>头像</div>
            <div style="margin-left: auto; color: gray; font-size: 12px; font-weight: 100; margin-right: 5px;">
                <img id="avatar_1" src="<%=uConfig.gd(userdt,"avatar") %>" style="width: 22px; height: 22px;" />
            </div>
            <svg t="" class="right-icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="4792" width="25" height="25" style="margin-left: 0;">
                <path d="M368.576 116.672a16 16 0 0 0-22.656 0l-45.248 45.248a16 16 0 0 0 0 22.656l327.936 327.936-327.936 327.936a16 16 0 0 0 0 22.592l45.248 45.248a16 16 0 0 0 22.656 0l384.512-384.448a16 16 0 0 0 0-22.656L368.576 116.672z" fill="#2c2c2c" p-id="4793"></path></svg>
        </a>


        <a class="menu-item" href="username_manager.aspx">
            <%--<svg t="" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="3216" width="18" height="18" style="position: relative; top: 2px;">
                <path d="M873.856 65.984L360.3968 579.456a50.4576 50.4576 0 1 0 71.3472 71.3472L945.216 137.344a50.4576 50.4576 0 1 0-71.3472-71.3472z" fill="#455CED" p-id="3217"></path><path d="M615.8336 54.4H375.2064C198.0288 54.4 54.4 198.0288 54.4 375.2064v273.5872c0 177.1776 143.6288 320.8064 320.8064 320.8064h273.5872c177.1776 0 320.8064-143.6288 320.8064-320.8064V481.984a47.2192 47.2192 0 0 0-94.4384 0v166.8096c0 125.0176-101.3504 226.368-226.368 226.368H375.2064c-125.0176 0-226.368-101.3504-226.368-226.368V375.2064c0-125.0176 101.3504-226.368 226.368-226.368h240.64a47.2192 47.2192 0 0 0 0-94.4384z" fill="#1F1F1F" p-id="3218"></path></svg>--%>
            <div>用户名</div>
            <div style="margin-left: auto; color: gray; font-size: 12px; font-weight: 100; margin-right: 5px; position: relative; top: -1px;">
                <%=uConfig.gd(userdt,"username") %>
               
            </div>
            <svg t="" class="right-icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="4792" width="25" height="25" style="margin-left: 0;">
                <path d="M368.576 116.672a16 16 0 0 0-22.656 0l-45.248 45.248a16 16 0 0 0 0 22.656l327.936 327.936-327.936 327.936a16 16 0 0 0 0 22.592l45.248 45.248a16 16 0 0 0 22.656 0l384.512-384.448a16 16 0 0 0 0-22.656L368.576 116.672z" fill="#2c2c2c" p-id="4793"></path></svg>
        </a>

    </div>

    <script>
        var upload_avatar = function () {
            file_upload(function (e) {
                if (e.success) {
                    v3api("upload_avatar", { data: { imgurl: e.imgurl[0] } }, function () {
                        tp('上传成功');
                        $('#avatar_1').attr('src', e.imgurl[0])
                    })
                }

            }, "","avatar")
        }
    </script>

</asp:Content>


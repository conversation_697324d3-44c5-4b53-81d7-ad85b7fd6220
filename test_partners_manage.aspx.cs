using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

public partial class order_list : baseClass
{
    public DataTable userdt = new DataTable();
    public DataSet ds = new DataSet();
    public Dictionary<string, object> dic = new Dictionary<string, object>();
    public Dictionary<string, object> pmlist = new Dictionary<string, object>();
    protected void Page_Load(object sender, EventArgs e)
    {



        DataTable tempdt = selectDateTable(chelper.gdt("account_groups"), " name='★托号' ");
        pmlist["th_groupId"] = "-1";
        if (tempdt.Rows.Count > 0)
        {
            pmlist["th_groupId"] = tempdt.Rows[0]["id"] + "";
        }

        List<SqlParameter> pams = new List<SqlParameter>();
        dbClass db = new dbClass();
        pams.Add(new SqlParameter("@userid", uConfig.p_uid));
        pams.Add(new SqlParameter("@sd_valid_money", uConfig.stcdata("sd_valid_money")));
        pams.Add(new SqlParameter("@sd_valid_lendcount", uConfig.stcdata("sd_valid_lendcount")));


        //一级（直属）
        pmlist["agent_sql"] = "parentid=@userid";

        //三级
        pmlist["level_sql"] = "(CASE WHEN CHARINDEX(@sid, ','+ISNULL(relaids,'')) > 0 THEN LEN(SUBSTRING(','+ISNULL(relaids,''), CHARINDEX(@sid, ','+ISNULL(relaids,'')) + LEN(@sid), LEN(','+ISNULL(relaids,'')))) - LEN(REPLACE(SUBSTRING(','+ISNULL(relaids,''), CHARINDEX(@sid, ','+ISNULL(relaids,'')) + LEN(@sid), LEN(','+ISNULL(relaids,''))), ',', '')) ELSE 999 END)".Replace("@sid", "'," + uConfig.p_uid + ",'");


        pmlist["agent_sql"] = "  id<>" + uConfig.p_uid + " and " + pmlist["level_sql"] + "<4 and " + pmlist["level_sql"] + ">0 ";


        string sql = @"

select * from accounts with(nolock) where id=@userid

select sum(award_amount) as amount,rank from brok_list with(nolock) where userid=@userid and rank<>-1 group by rank

select sum(award_amount) as amount,rank from brok_list with(nolock) where userid=@userid and datediff(day,getdate(),create_time)=0 and rank<>-1 group by rank


select isnull(SUM((case when parentid=@userid then 1 else 0 end)),0) as agent_number,COUNT(0) as total_number FROM [accounts] with(nolock) where id>@userid and CHARINDEX(','+cast(@userid as varchar(10))+',',','+ISNULL(relaids,'')+',')>0


-- 查询下级有效人数
select count(0) as number from accounts with(nolock) where {agent_sql} and trans_amount>=@sd_valid_money


-- 查询一键任务佣金数据
select 
level
,SUM(today_amount) as today_amount,SUM(yesterday_amount) as yesterday_amount,SUM(total_amount) as total_amount
,SUM(case when today_number>0 then 1 else 0 end) as today_number,SUM(yesterday_number) as yesterday_number,count(0) as total_number

 from (
select t.*
,{level_sql} as level 

 from (select userid
  ,sum(case when state=1 and DATEDIFF(DAY,settle_time,GETDATE())=0 then award_amount-serve_amount else 0 end) as today_amount 
  ,sum(case when state=1 and  DATEDIFF(DAY,settle_time,GETDATE())=1 then award_amount-serve_amount else 0 end) as yesterday_amount 
  ,sum(case when state=1 then award_amount-serve_amount else 0 end) as total_amount 
  
  ,sum(case when DATEDIFF(DAY,settle_time,GETDATE())=0 then 1 else 0 end) as today_number
  ,sum(case when DATEDIFF(DAY,settle_time,GETDATE())=1 then 1 else 0 end) as yesterday_number
  ,COUNT(0) as total_number 
  
  from [task_onetouch_preset] with(nolock) where settle_time is not null group by userid)t 
  left join accounts a  with(nolock)  on t.userid=a.id
  where {agent_sql}
)b group by level

"
            .Replace("{agent_sql}", pmlist["agent_sql"] + "")
            .Replace("{level_sql}", pmlist["level_sql"] + "");


        //Response.ContentType = "text/plain";
        //Response.Write("sql\r\n");
        //Response.Write(sql);
        //Response.End();

        ds = db.getDataSet(sql, pams.ToArray());

        userdt = ds.Tables[0];

        DataTable dt = new DataTable();



        dic["today_user"] = 0;
        dic["yesterday_user"] = 0;
        dic["total_user"] = 0;

        dic["today_award_amount"] = 0.00;
        dic["yesterday_award_amount"] = 0.00;
        dic["total_award_amount"] = 0.00;

        dic["r1_today_award_amount"] = 0.00;
        dic["r1_yesterday_award_amount"] = 0.00;
        dic["r1_total_award_amount"] = 0.00;

        dic["r2_today_award_amount"] = 0.00;
        dic["r2_yesterday_award_amount"] = 0.00;
        dic["r2_total_award_amount"] = 0.00;

        dic["r3_today_award_amount"] = 0.00;
        dic["r3_yesterday_award_amount"] = 0.00;
        dic["r3_total_award_amount"] = 0.00;


        //历史佣金
        dt = ds.Tables[1];

        for (int i = 0; i < dt.Rows.Count; i++)
        {
            dic["total_award_amount"] = Convert.ToDouble(dic["total_award_amount"]) + Convert.ToDouble(dt.Rows[i]["amount"] + "");
            dic["r" + dt.Rows[i]["rank"] + "_total_award_amount"] = Convert.ToDouble(dic["r" + dt.Rows[i]["rank"] + "_total_award_amount"]) + Convert.ToDouble(dt.Rows[i]["amount"] + "");
        }



        //今日佣金
        dt = ds.Tables[2];

        for (int i = 0; i < dt.Rows.Count; i++)
        {
            dic["today_award_amount"] = Convert.ToDouble(dic["today_award_amount"]) + Convert.ToDouble(dt.Rows[i]["amount"] + "");
            dic["r" + dt.Rows[i]["rank"] + "_today_award_amount"] = Convert.ToDouble(dic["r" + dt.Rows[i]["rank"] + "_today_award_amount"]) + Convert.ToDouble(dt.Rows[i]["amount"] + "");
        }




        //今日/昨日/历史|三级 团队一键任务佣金（+人数）统计
        dt = ds.Tables[5];

        //Response.Write(ToJson(dt)); ;
        //Response.End();

        for (int i = 0; i < dt.Rows.Count; i++)
        {

            dic["today_award_amount"] = Convert.ToDouble(dic["today_award_amount"]) + Convert.ToDouble(dt.Rows[i]["today_amount"] + "");
            dic["yesterday_award_amount"] = Convert.ToDouble(dic["yesterday_award_amount"]) + Convert.ToDouble(dt.Rows[i]["yesterday_amount"] + "");
            dic["total_award_amount"] = Convert.ToDouble(dic["total_award_amount"]) + Convert.ToDouble(dt.Rows[i]["total_amount"] + "");



            dic["today_user"] = Convert.ToDouble(dic["today_user"]) + Convert.ToDouble(dt.Rows[i]["today_number"] + "");
            dic["yesterday_user"] = Convert.ToDouble(dic["yesterday_user"]) + Convert.ToDouble(dt.Rows[i]["yesterday_number"] + "");
            dic["total_user"] = Convert.ToDouble(dic["total_user"]) + Convert.ToDouble(dt.Rows[i]["total_number"] + "");


            dic["r" + dt.Rows[i]["level"] + "_today_award_amount"] = Convert.ToDouble(dt.Rows[i]["total_amount"] + "");
            dic["r" + dt.Rows[i]["level"] + "_yesterday_award_amount"] = Convert.ToDouble(dt.Rows[i]["yesterday_amount"] + "");
            dic["r" + dt.Rows[i]["level"] + "_total_award_amount"] = Convert.ToDouble(dt.Rows[i]["total_amount"] + "");


        }

        //格式化（保留两位小数）
        string[] formatPms = new string[] { 
            "today_award_amount", "yesterday_award_amount", "yesterday_award_amount",
            "r1_today_award_amount", "r1_yesterday_award_amount", "r1_total_award_amount",
            "r2_today_award_amount", "r2_yesterday_award_amount", "r2_total_award_amount",
            "r3_today_award_amount", "r3_yesterday_award_amount", "r3_total_award_amount"  };
        for (int i = 0; i < formatPms.Length; i++)
        {
            dic[formatPms[i]] = Convert.ToDouble(dic[formatPms[i]]).ToString("0.00");
        }


        //下级数量统计
        dt = ds.Tables[3];
        dic["agent_number"] = 0;
        dic["total_number"] = 0;
        dic["other_number"] = 0;
        if (dt.Rows.Count > 0)
        {
            dic["agent_number"] = dt.Rows[0]["agent_number"];
            dic["total_number"] = dt.Rows[0]["total_number"];
            dic["other_number"] = Convert.ToInt32(dic["total_number"]) - Convert.ToInt32(dic["agent_number"]);
        }


        string[] g = { };
        if (pmlist["th_groupId"] + "" == uConfig.gd(userdt, "groupid"))
        {
            g = uConfig.stcdata("brok_th").Split(',');
        }
        else
        {
            g = uConfig.stcdata("brok_user").Split(',');
        }
        dic["r1"] = g[0];
        dic["r2"] = g[1];
        dic["r3"] = g[2];


        //有效邀请人数
        dt = ds.Tables[4];
        pmlist["valid_user_number"] = "0";
        if (dt.Rows.Count > 0)
        {
            pmlist["valid_user_number"] = dt.Rows[0]["number"] + "";
        }
    }
}
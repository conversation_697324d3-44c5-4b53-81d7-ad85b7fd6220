using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

public partial class order_list : baseClass
{
    public DataTable userdt = new DataTable();
    public Dictionary<string, object> pmlist = new Dictionary<string, object>();
    protected void Page_Load(object sender, EventArgs e)
    {
        Response.Redirect("~/order_new.aspx");
        List<SqlParameter> pams = new List<SqlParameter>();
        dbClass db = new dbClass();
        DataTable dt = new DataTable();
        pams.Add(new SqlParameter("@userid", uConfig.p_uid));

        string sql = string.Empty;


        string[] g, g2;

        sql = " declare @share_table table(num int,reward_number int) ";
        g = uConfig.stcdata("share_sd_number").Replace("\n", "#").Split('#');
        for (int i = 0; i < g.Length; i++)
        {
            g2 = g[i].Split('~');
            if (g2.Length == 2)
            {
                sql += " insert into @share_table values(" + g2[0] + "," + g2[1] + ") ";
            }
        }



        sql += @" 








    declare @daily_share_number int
    declare @reward_number int

    set @daily_share_number=0
    set @reward_number=0  -- 邀请奖励次数

    select @daily_share_number=count(0) from share_people with(nolock) where datediff(day,create_time,getdate())=0 and parentid=@userid and isvalid=1
    select top 1 @reward_number=reward_number from @share_table where num<=@daily_share_number order by reward_number desc





select *,@reward_number as reward_number from accounts with(nolock) where id=@userid

select top 1 (case when DATEDIFF(DAY,order_time,GETDATE())=0 then number else 0 end) as payment_number,(case when DATEDIFF(DAY,order_time,GETDATE())=0 then amount else 0 end) as payment_amount from payment_list with(nolock) where userid=@userid and state=1 








";
        DataSet ds = db.getDataSet(sql, pams.ToArray());

        userdt = ds.Tables[0];

        dt = ds.Tables[1];
        pmlist["number"] = uConfig.stcdata("limit_number");
        if (dt.Rows.Count > 0)
        {
            pmlist["number"] = Convert.ToInt16(pmlist["number"]) - Convert.ToInt16(dt.Rows[0]["payment_number"]);
        }

        classList.DataSource = SortDataTable(selectDateTable(chelper.gdt("item_classList"), "state=1"), "sort_index ASC");
        classList.DataBind();
    }
}
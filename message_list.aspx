<%@ Page Title="" Language="C#" MasterPageFile="~/top.master" AutoEventWireup="true" CodeFile="message_list.aspx.cs" Inherits="notice_list" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="Server">
    <script>
        $(function () {
            popTitle('公告中心', '');
        })
    </script>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="Server">

    <div id="lists">
    </div>

    <div style="color: #aaa; text-align: center; font-size: 12px; margin-top: 20px;">没有更多数据了</div>


    <script>
        v3api("lists", { data: { page: 'message_list', p: 0, limit: 10 } }, function (e) {
            for (var i = 0; i < e.data.list.length; i++) {
                var obj = e.data.list[i];
                $('#lists').append('<div onclick="javascript:location.href=\'message_details.aspx?id=' + obj.id + '\'" style="background:#fff;border-radius:10px;padding:18px 22px;font-weight:bold;font-size:14px;margin-bottom:15px;">        ' + obj.name + '    </div>');
            }

        })

        var addrClick = function (id, addr) {
            localStorage.setItem('transferAddr', addr);
            history.go(-1);
        }
    </script>

</asp:Content>


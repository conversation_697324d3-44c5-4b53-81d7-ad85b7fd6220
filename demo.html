<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Fixed Button with Circular Menu</title>
    <style>
         body {
            margin: 0;
            height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            overflow: hidden;
        }

        .fixed-button {
            position: fixed;
            right: 13px;
            top: 50%;
            transform: translateY(-50%);
            width: 50px;
            height: 50px;
            background-color: #007bff;
            color: #fff;
            display: flex;
            justify-content: center;
            align-items: center;
            border-radius: 50%;
            cursor: pointer;
            font-size: 30px;
            z-index: 1000;
        }

        .circle-menu {
            position: fixed;
            right: -71px;
            top: 50%;
            transform: translateY(-50%);
            width: 200px;
            height: 200px;
            display: none;
            justify-content: center;
            align-items: center;
            background-color: rgba(201, 201, 201, 0.5);
            border-radius: 50%;
            z-index: 999;
        }

        .circle-button {
            width: 40px;
            height: 40px;
            background-color: #ff6347;
            color: #fff;
            display: flex;
            justify-content: center;
            align-items: center;
            border-radius: 50%;
            position: absolute;
        }

        .circle-button:nth-child(1) {
            top: 36px;
            left: 94px;
            transform: translate(-50%, -50%);
        }

        .circle-button:nth-child(2) {
            top: 56px;
            left: 56px;
            transform: translate(-50%, -50%);
        }

        .circle-button:nth-child(3) {
            top: 50%;
            left: 37px;
            transform: translate(-50%, -50%);
        }

        .circle-button:nth-child(4) {
            top: 140px;
            left: 56px;
            transform: translate(-50%, -50%);
        }

        .circle-button:nth-child(5) {
            top: 158px;
            left: 94px;
            transform: translate(-50%, -50%);
        }
    </style>
</head>
<body>
    <div class="fixed-button" id="mainButton">+</div>
    <div class="circle-menu" id="circleMenu">
        <div class="circle-button">1</div>
        <div class="circle-button">2</div>
        <div class="circle-button">3</div>
        <div class="circle-button">4</div>
        <div class="circle-button">5</div>
    </div>
    <script>
        document.getElementById('mainButton').onclick = function () {
            var menu = document.getElementById('circleMenu');
            if (menu.style.display === 'none' || menu.style.display === '') {
                menu.style.display = 'flex';
            } else {
                menu.style.display = 'none';
            }
        };
    </script>
</body>
</html>

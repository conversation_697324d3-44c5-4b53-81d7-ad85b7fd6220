<%@ Page Title="" Language="C#" MasterPageFile="~/top.master" AutoEventWireup="true" CodeFile="task_onetouch_records.aspx.cs" Inherits="order_list" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="Server">
    <script>
        $(function () {
            popTitle('任务记录', '', 'task_onetouch.aspx');
        })
    </script>
    <style>
        .top-title {
            background: #fff;
        }

        body {
            background: #F2F3F6;
        }

        .top-title .pptitle {
            font-weight: 100;
        }
    </style>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="Server">





    <div id="lists">
    </div>



    <script>

        var more_list = function (index) {
            show_list(index);
        }

        var show_list = function (index) {
            if (!index) {
                index = 0;
            }


            v3api("lists", { data: { page: 'task_onetouch', p: index, limit: 10 } }, function (e) {
                for (var i = 0; i < e.data.list.length; i++) {
                    var obj = e.data.list[i];
                    //$('#lists').append('<div sid="' + obj.id + '" style="padding-bottom: 18px; border-bottom: 1px solid #f2f2f2; margin-bottom: 18px;"><div style="color: #5a5b5c;text-shadow: 5px 5px 5px #00000008;font-weight: bold;">' + obj.orderNo + '</div><div style="margin-top: 10px; line-height: 22px; font-size: 12px; color: #2a2b2c;"><div>交易订单：' + obj.buy_orderId + '</div><div style="color: #9b4949;font-weight: bold;">交易金额：' + obj.amount + '</div></div><div style="font-size: 12px; margin-top: 8px; color: gray; display: flex;"><div>' + obj.create_time + '</div><div style="margin-left: auto;" class="state_text">' + (obj.state == 0 ? '<span style="color: #eee; padding: 1px 3px; margin-right: 3px; border-radius: 3px; font-size: 12px; background: linear-gradient(28deg, #ffffff, #e0ffff 100%); border: 1px solid #878181; color: #878181; font-weight: bold;">交易中</span><a style="margin-left:5px;" class="design-button-common" onclick="finish_order(' + obj.id + ')">确认收款</a>' : obj.state == 99 ? '审核中' : obj.state == -1 ? '交易撤销' : obj.state == 1 ? '<span style="color:#71429d;">交易成功</span>' : '状态异常') + '</div></div></div>');

                    //$('#lists').append('<div sid="' + obj.id + '" style="border-bottom: 1px solid #f2f2f2;margin-bottom: 18px;background: #fff;padding: 16px;border-radius: 3px;"><div style="display:flex;font-size: 12px;">    <div>下单时间：' + obj.create_time + '</div>    <div style="margin-left:auto;">' + (obj.state == 0 ? '<span style="color: #7287D7;font-weight: bold;">待审核</span>' : obj.state == 100 ? '<span style="color: #2a2b2c;font-weight: bold;text-shadow: 3px 3px 10px yellow;">进行中</span>' : obj.state == -1 ? '<span style="color: gray;">已撤销</span>' : obj.state == 1 ? '<span style="color: #379d37;">已完成</span>' : '<span style="color: red;">订单异常</span>') + '</div></div><div style="margin-top: 10px; line-height: 22px; font-size: 12px; color: #2a2b2c;"><div style="    display: flex;"><svg t="" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="11528" width="20" height="20"><path d="M723.968 979.968H300.032c-141.1584 0-256-114.8416-256-256V300.032c0-141.1584 114.8416-256 256-256h423.936c141.1584 0 256 114.8416 256 256v423.936c0 141.1584-114.8416 256-256 256zM300.032 125.952c-96 0-174.08 78.08-174.08 174.08v423.936c0 96 78.08 174.08 174.08 174.08h423.936c96 0 174.08-78.08 174.08-174.08V300.032c0-96-78.08-174.08-174.08-174.08H300.032z" fill="#515151" p-id="11529"></path><path d="M303.0016 321.3312h75.9808c-9.6768-13.5168-18.5344-24.3712-26.6752-32.4608-7.3728-7.7312-9.8816-15.872-7.5264-24.3712 2.7136-9.6768 7.7312-18.176 15.104-25.4976s15.2576-11.776 23.7568-13.3632c9.2672-1.9456 17.6128 0.768 24.9344 8.1408 17.408 16.9984 34.9696 39.6288 52.7872 67.84 3.84 6.9632 5.0176 13.5168 3.4816 19.712h97.9968c20.8896-30.9248 35.584-56.832 44.0832-77.7216 4.2496-9.6768 11.008-15.104 20.2752-16.2304 10.4448-1.1776 20.48 0.2048 30.1568 4.0448 10.0352 3.8912 17.408 9.472 22.016 16.8448 5.4272 8.1408 6.1952 16.9984 2.304 26.6752-5.7856 14.2848-13.3632 29.7984-22.6304 46.3872h66.7136c18.944 0 28.416 9.472 28.416 28.416v220.9792c0 18.944-9.472 28.416-28.416 28.416h-173.4144v26.112h207.616c10.0352 0.4096 17.408 4.2496 22.016 11.6224 5.0176 8.1408 7.5264 17.6128 7.5264 28.416s-2.304 19.1488-6.9632 26.112c-5.4272 8.1408-12.9536 12.1856-22.6304 12.1856h-207.616v64.9728c0 10.4448-4.0448 17.9712-12.1856 22.6304-8.9088 5.4272-18.7392 8.1408-29.5936 8.1408s-19.3536-2.5088-26.6752-7.5264c-8.1408-5.0176-12.1856-12.7488-12.1856-23.1936v-64.9728H270.4896c-10.4448 0-17.9712-4.0448-22.6304-12.1856-5.0176-8.1408-7.5264-17.6128-7.5264-28.416s2.304-19.1488 6.9632-26.112c5.0176-7.7312 12.7488-11.6224 23.1936-11.6224h201.2672v-26.112H303.0016c-18.944 0-28.416-9.472-28.416-28.416V349.7984c0-18.944 9.472-28.416 28.416-28.416z m168.7552 78.2848H361.5744c-4.2496 0.4096-6.4 2.5088-6.4 6.4v15.104h116.5824v-21.4528z m0 99.7376H355.1744v15.104c0.3584 4.2496 2.5088 6.4 6.4 6.4h110.1824v-21.4528z m80.64-78.2848h121.2416v-15.104c-0.4096-4.2496-2.5088-6.4-6.4-6.4h-114.8416v21.4528z m0 99.7376h114.8416c4.2496-0.3584 6.4-2.5088 6.4-6.4v-15.104h-121.2416v21.4528z" fill="#515151" p-id="11530"></path></svg><span style="    margin-left: 7px;    font-weight: bold;    font-size: 13px;">' + obj.orderNo + '</span></div><div style="    padding-left: 27px;"><div style="color: gray;">任务周期：' + obj.days + '天</div><div style="color: gray;">房主佣金：' + obj.serve_fee + '%</div></div><div style="    display: flex;    margin-top: 18px;"><svg t="" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="17986" width="20" height="20"><path d="M512 512m-512 0a512 512 0 1 0 1024 0 512 512 0 1 0-1024 0Z" fill="#F5A623" p-id="17987"></path><path d="M637.269333 611.584l-7.355733 173.636267-62.122667-2.679467 6.690134-163.618133-85.486934 10.018133-6.673066 168.2944-62.7712-1.9968 19.3536-502.8864 32.068266-30.037333h283.818667l31.402667 30.037333 16.008533 474.1632-42.734933 30.037333-96.836267-36.7104 22.698667-57.429333 51.438933 20.0192-8.021333-253.098667-85.486934 9.352534-3.328 69.461333 72.123734-8.704 7.338666 62.788267-82.141866 9.352533zM348.740267 384.512h18.688v411.392h-62.7712V443.2896l-32.7168 42.734933L221.866667 447.965867 380.808533 238.933333l50.090667 37.393067-82.141867 108.202667z m148.258133 46.08l86.152533-8.669867 3.9936-96.836266h-86.135466l-4.010667 105.506133z m152.917333-105.506133l-3.9936 90.146133 80.810667-8.021333-2.013867-82.1248h-74.786133zM491.690667 564.8384l85.469866-10.018133 3.345067-69.461334-86.152533 9.352534-2.6624 70.126933z" fill="#FFFFFF" p-id="17988"></path></svg><span style="    margin-left: 7px;    font-weight: bold;    font-size: 13px;    color: #e99810;">' + obj.award_amount.toFixed(2) + '</span></div><div style="    padding-left: 27px;"><div style="color: gray;">任务佣金：' + (obj.award_amount + obj.serve_amount).toFixed(2) + '</div><div style="color: gray;">房主佣金：' + obj.serve_amount.toFixed(2) + '</div><div style="color: gray;">佣金将在结束后到账！</div></div></div><div style="font-size: 12px; margin-top: 8px; color: gray; display: flex;"><div style="    display: flex;    align-items: center;"><svg t="" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="4547" width="13" height="13"><path d="M512 16.384C241.664 16.384 20.48 237.568 20.48 507.904s221.184 491.52 491.52 491.52 491.52-221.184 491.52-491.52-221.184-491.52-491.52-491.52z m0 929.792c-241.664 0-438.272-196.608-438.272-438.272 0-241.664 196.608-438.272 438.272-438.272s438.272 196.608 438.272 438.272c0 241.664-196.608 438.272-438.272 438.272z" fill="" p-id="4548"></path><path d="M512 253.952h-53.248l4.096 303.104 274.432 155.648 24.576-45.056-249.856-139.264z" fill="" p-id="4549"></path></svg>&nbsp;' + obj.expire_time + '结束</div><div style="margin-left: auto;" class="state_text"><div style="    color: #DF665D;    font-size: 20px;">' + obj.amount + '<img src="/static/images/coin.png" alt="" height="15" width="15" style="margin-left: 5px;"></div></div></div></div>');

                    $('#lists').append('<div sid="' + obj.id + '" style="border-bottom: 1px solid #f2f2f2;margin-bottom: 18px;background: #fff;padding: 16px;border-radius: 3px;"><div style="display:flex;font-size: 12px;">    <div>下单时间：' + obj.create_time + '</div>    <div style="margin-left:auto;">' + (obj.state == 0 ? '<span style="color: #7287D7;font-weight: bold;">待审核</span>' : obj.state == 100 ? '<span style="color: #2a2b2c;font-weight: bold;text-shadow: 3px 3px 10px yellow;">进行中</span>' : obj.state == -1 ? '<span style="color: gray;">已撤销</span>' : obj.state == 1 ? '<span style="color: #379d37;">已完成</span>' : '<span style="color: red;">订单异常</span>') + '</div></div><div style="margin-top: 10px; line-height: 22px; font-size: 12px; color: #2a2b2c;"><div style="    display: flex;"><svg t="" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="11528" width="20" height="20"><path d="M723.968 979.968H300.032c-141.1584 0-256-114.8416-256-256V300.032c0-141.1584 114.8416-256 256-256h423.936c141.1584 0 256 114.8416 256 256v423.936c0 141.1584-114.8416 256-256 256zM300.032 125.952c-96 0-174.08 78.08-174.08 174.08v423.936c0 96 78.08 174.08 174.08 174.08h423.936c96 0 174.08-78.08 174.08-174.08V300.032c0-96-78.08-174.08-174.08-174.08H300.032z" fill="#515151" p-id="11529"></path><path d="M303.0016 321.3312h75.9808c-9.6768-13.5168-18.5344-24.3712-26.6752-32.4608-7.3728-7.7312-9.8816-15.872-7.5264-24.3712 2.7136-9.6768 7.7312-18.176 15.104-25.4976s15.2576-11.776 23.7568-13.3632c9.2672-1.9456 17.6128 0.768 24.9344 8.1408 17.408 16.9984 34.9696 39.6288 52.7872 67.84 3.84 6.9632 5.0176 13.5168 3.4816 19.712h97.9968c20.8896-30.9248 35.584-56.832 44.0832-77.7216 4.2496-9.6768 11.008-15.104 20.2752-16.2304 10.4448-1.1776 20.48 0.2048 30.1568 4.0448 10.0352 3.8912 17.408 9.472 22.016 16.8448 5.4272 8.1408 6.1952 16.9984 2.304 26.6752-5.7856 14.2848-13.3632 29.7984-22.6304 46.3872h66.7136c18.944 0 28.416 9.472 28.416 28.416v220.9792c0 18.944-9.472 28.416-28.416 28.416h-173.4144v26.112h207.616c10.0352 0.4096 17.408 4.2496 22.016 11.6224 5.0176 8.1408 7.5264 17.6128 7.5264 28.416s-2.304 19.1488-6.9632 26.112c-5.4272 8.1408-12.9536 12.1856-22.6304 12.1856h-207.616v64.9728c0 10.4448-4.0448 17.9712-12.1856 22.6304-8.9088 5.4272-18.7392 8.1408-29.5936 8.1408s-19.3536-2.5088-26.6752-7.5264c-8.1408-5.0176-12.1856-12.7488-12.1856-23.1936v-64.9728H270.4896c-10.4448 0-17.9712-4.0448-22.6304-12.1856-5.0176-8.1408-7.5264-17.6128-7.5264-28.416s2.304-19.1488 6.9632-26.112c5.0176-7.7312 12.7488-11.6224 23.1936-11.6224h201.2672v-26.112H303.0016c-18.944 0-28.416-9.472-28.416-28.416V349.7984c0-18.944 9.472-28.416 28.416-28.416z m168.7552 78.2848H361.5744c-4.2496 0.4096-6.4 2.5088-6.4 6.4v15.104h116.5824v-21.4528z m0 99.7376H355.1744v15.104c0.3584 4.2496 2.5088 6.4 6.4 6.4h110.1824v-21.4528z m80.64-78.2848h121.2416v-15.104c-0.4096-4.2496-2.5088-6.4-6.4-6.4h-114.8416v21.4528z m0 99.7376h114.8416c4.2496-0.3584 6.4-2.5088 6.4-6.4v-15.104h-121.2416v21.4528z" fill="#515151" p-id="11530"></path></svg><span style="    margin-left: 7px;    font-weight: bold;    font-size: 13px;">' + obj.orderNo + '</span></div><div style="    padding-left: 27px;"><div style="color: gray;">任务周期：' + obj.days + '天</div><div style="color: gray;">房主佣金：' + obj.serve_fee + '%</div></div><div style="    display: flex;    margin-top: 18px;"><svg t="" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="6582" width="18" height="18"><path d="M841.142857 1024h-658.285714C80.457143 1024 0 943.542857 0 841.142857v-585.142857C0 153.6 80.457143 73.142857 182.857143 73.142857h658.285714C943.542857 73.142857 1024 153.6 1024 256v585.142857c0 102.4-80.457143 182.857143-182.857143 182.857143z m-658.285714-877.714286C124.342857 146.285714 73.142857 197.485714 73.142857 256v585.142857c0 58.514286 51.2 109.714286 109.714286 109.714286h658.285714c58.514286 0 109.714286-51.2 109.714286-109.714286v-585.142857c0-58.514286-51.2-109.714286-109.714286-109.714286h-658.285714z" fill="#333333" p-id="6583"></path><path d="M292.571429 241.371429c-21.942857 0-36.571429-14.628571-36.571429-36.571429V36.571429c0-21.942857 14.628571-36.571429 36.571429-36.571429s36.571429 14.628571 36.571428 36.571429v168.228571c0 21.942857-14.628571 36.571429-36.571428 36.571429zM731.428571 241.371429c-21.942857 0-36.571429-14.628571-36.571428-36.571429V36.571429c0-21.942857 14.628571-36.571429 36.571428-36.571429s36.571429 14.628571 36.571429 36.571429v168.228571c0 21.942857-14.628571 36.571429-36.571429 36.571429zM841.142857 497.371429h-292.571428c-21.942857 0-36.571429-14.628571-36.571429-36.571429s14.628571-36.571429 36.571429-36.571429h292.571428c21.942857 0 36.571429 14.628571 36.571429 36.571429s-14.628571 36.571429-36.571429 36.571429zM841.142857 731.428571h-292.571428c-21.942857 0-36.571429-21.942857-36.571429-36.571428s14.628571-36.571429 36.571429-36.571429h292.571428c21.942857 0 36.571429 14.628571 36.571429 36.571429s-14.628571 36.571429-36.571429 36.571428zM226.742857 533.942857c-7.314286 0-21.942857-7.314286-29.257143-14.628571l-58.514285-65.828572c-14.628571-14.628571-14.628571-43.885714 0-51.2 14.628571-14.628571 36.571429-14.628571 51.2 0l36.571428 36.571429 58.514286-65.828572c14.628571-14.628571 36.571429-14.628571 51.2 0 14.628571 14.628571 14.628571 36.571429 0 51.2L256 519.314286c-7.314286 7.314286-21.942857 14.628571-29.257143 14.628571zM226.742857 760.685714c-7.314286 0-21.942857-7.314286-29.257143-14.628571l-58.514285-65.828572c-14.628571-14.628571-14.628571-36.571429 0-51.2 14.628571-14.628571 36.571429-14.628571 51.2 0l36.571428 36.571429 58.514286-65.828571c14.628571-14.628571 36.571429-14.628571 51.2 0 14.628571 14.628571 14.628571 36.571429 0 51.2l-87.771429 102.4s-14.628571 7.314286-21.942857 7.314285z" fill="#333333" p-id="6584"></path></svg><span style="    margin-left: 7px;    font-weight: bold;    font-size: 13px;">昨日任务总额：' + obj.yes_amount.toFixed(2) + '</span></div><div style="    padding-left: 27px;"><div style="color: gray;">昨日任务佣金：' + obj.yes_award_amount.toFixed(2) + '</div><div style="color: gray;">昨日房主佣金：' + obj.yes_serve_fee.toFixed(2) + '</div></div><div style="    display: flex;    margin-top: 18px;"><svg t="" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="17986" width="20" height="20"><path d="M512 512m-512 0a512 512 0 1 0 1024 0 512 512 0 1 0-1024 0Z" fill="#F5A623" p-id="17987"></path><path d="M637.269333 611.584l-7.355733 173.636267-62.122667-2.679467 6.690134-163.618133-85.486934 10.018133-6.673066 168.2944-62.7712-1.9968 19.3536-502.8864 32.068266-30.037333h283.818667l31.402667 30.037333 16.008533 474.1632-42.734933 30.037333-96.836267-36.7104 22.698667-57.429333 51.438933 20.0192-8.021333-253.098667-85.486934 9.352534-3.328 69.461333 72.123734-8.704 7.338666 62.788267-82.141866 9.352533zM348.740267 384.512h18.688v411.392h-62.7712V443.2896l-32.7168 42.734933L221.866667 447.965867 380.808533 238.933333l50.090667 37.393067-82.141867 108.202667z m148.258133 46.08l86.152533-8.669867 3.9936-96.836266h-86.135466l-4.010667 105.506133z m152.917333-105.506133l-3.9936 90.146133 80.810667-8.021333-2.013867-82.1248h-74.786133zM491.690667 564.8384l85.469866-10.018133 3.345067-69.461334-86.152533 9.352534-2.6624 70.126933z" fill="#FFFFFF" p-id="17988"></path></svg><span style="    margin-left: 7px;    font-weight: bold;    font-size: 13px;    color: #e99810;">昨日收益：' + (obj.yes_award_amount - obj.yes_serve_fee).toFixed(2) + '</span></div><div style="    padding-left: 27px;"><div style="color: gray;">佣金将在结束后到账！</div></div></div><div style="font-size: 12px; margin-top: 8px; color: gray; display: flex;"><div style="display: flex;align-items: end;"><svg t="" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="4547" width="13" height="13"><path d="M512 16.384C241.664 16.384 20.48 237.568 20.48 507.904s221.184 491.52 491.52 491.52 491.52-221.184 491.52-491.52-221.184-491.52-491.52-491.52z m0 929.792c-241.664 0-438.272-196.608-438.272-438.272 0-241.664 196.608-438.272 438.272-438.272s438.272 196.608 438.272 438.272c0 241.664-196.608 438.272-438.272 438.272z" fill="" p-id="4548"></path><path d="M512 253.952h-53.248l4.096 303.104 274.432 155.648 24.576-45.056-249.856-139.264z" fill="" p-id="4549"></path></svg>&nbsp;' + obj.expire_time + '结束</div><div style="margin-left: auto;" class="state_text">'
                        + '<div style="color: #939393;font-size: 14px;font-weight: bold;text-align: right;">订单总额：' + obj.amount + '<img src="/static/images/coin.png" alt="" height="15" width="15" style="margin-left: 5px;"></div>'
                        + '<div style="color: #000;font-size: 14px;font-weight: bold;text-align: right;margin-top: 5px;text-shadow: 5px 5px 5px #ffff97;">任务额度：' + parseNumber(obj.balance).toFixed(2) + '<img src="/static/images/coin.png" alt="" height="15" width="15" style="margin-left: 5px;"></div>'
                        +'<div style="color: #c36b65;font-size: 14px;font-weight: bold;margin-top: 5px;text-align: right;"><b style="color: #5f6367;">当前总收益：</b>' + obj.award_amount.toFixed(2) + '<img src="/static/images/coin.png" alt="" height="15" width="15" style="margin-left: 5px;"></div><div style="color: #c36b65;font-size: 14px;font-weight: bold;margin-top: 5px;text-align: right;"><b style="color: #5f6367;">预计回款总额：</b>' + (obj.amount + obj.award_amount).toFixed(2) + '<img src="/static/images/coin.png" alt="" height="15" width="15" style="margin-left: 5px;"></div></div></div><div style="margin-top: 18px;text-align: center;">    <a style="    font-size: 13px;    display: inline-block;    background: linear-gradient(88deg, #E3EEFC, #efefef);    padding: 6px 18px;    border-radius: 4px;    color: #2a2b2c;    font-weight: bold;    margin: 0 6px;    text-decoration: none;" href="../transport_orders.aspx?taskfrom=' + obj.orderNo + '&type=1">昨日成功订单记录</a>    <a style="    font-size: 13px;    display: inline-block;    background: linear-gradient(88deg, #E3EEFC, #efefef);    padding: 6px 18px;    border-radius: 4px;    color: #9f4040;    font-weight: bold;    margin: 0 6px;    text-decoration: none;" href="../transport_orders.aspx?taskfrom=' + obj.orderNo + '&type=-1,2,20">昨日失败订单记录</a></div></div>');
                }



                if (e.data.list.length == 10) {
                    $('#lists').append('<div id="load_more" next_id="' + (parseInt(index) + 1) + '"></div>');
                }
                finish_trigger();

            })
        }

        more_list(0);

        var finish_order = function (id) {

            security_password(function (e) {
                v3api("finish_sell_order", { data: { paypwd: e.password, id: id } }, function (e) {
                    tp(e.msg);
                    $('[sid="' + id + '"] .state_text').html('<span style="color:#71429d;">交易成功</span>');

                })


            }, { error_tip_top: '<div style="color: #2a2b2c;padding: 0 30px;margin-top: 20px;font-weight: bold;text-align:center;font-size: 14px;" class="animated-text"><div style="    margin-bottom: 10px;">确定收到款了吗？</div><div>为了保证您的资金安全，<span style="color:red;">请收到款再确认收款</span></div>    </div>' });
        }


        function timestampToDateTimeString(timestamp) {
            // 将时间戳乘以1000以转换为13位时间戳（Date对象使用13位时间戳）
            var date = new Date(timestamp * 1000);

            // 获取年、月、日、时、分、秒
            var year = date.getFullYear();
            var month = String(date.getMonth() + 1).padStart(2, '0'); // 月份从0开始，需要加1
            var day = String(date.getDate()).padStart(2, '0');
            var hours = String(date.getHours()).padStart(2, '0');
            var minutes = String(date.getMinutes()).padStart(2, '0');
            var seconds = String(date.getSeconds()).padStart(2, '0');

            // 格式化日期时间字符串
            var formattedDateTimeString = year + '-' + month + '-' + day + ' ' + hours + ':' + minutes + ':' + seconds;

            return formattedDateTimeString;
        }
    </script>


    <script>
        $(document).ready(function () {
            $(window).scroll(function () {
                if ($(document).scrollTop() <= 0) {
                    console.log("滚动条已经到达顶部为0");
                }
                if ($(document).scrollTop() + 100 >= $(document).height() - $(window).height()) {
                    trigger_bottom();
                }

            });

        });

        var trigger_check = 0;
        var trigger_bottom = function () {
            if (trigger_check != 0) {
                return;
            }
            trigger_check = 1;
            console.log("滚动条已经到达底部为" + $(document).scrollTop());

            if (typeof (more_list)) {
                var m = $('#lists').find("#load_more");
                if (m.html() != undefined) {
                    m.remove();
                    more_list(m.attr("next_id"));
                }
            }
        }

        var finish_trigger = function () {
            trigger_check = 0;
        }

    </script>

</asp:Content>


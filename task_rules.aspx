<%@ Page Title="" Language="C#" MasterPageFile="~/top.master" AutoEventWireup="true" CodeFile="task_rules.aspx.cs" Inherits="notice_details" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="Server">
    <script>
        $(function () {
            popTitle('<b><%=Request.QueryString["type"] + "" == "jrhhr" ? "加入合伙人" : "任务规则" %></b>', '');
        })
    </script>

    <style>
        .main-container {
            padding: 0px;
        }

        img {
            max-width: 100%;
        }
    </style>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="Server">
    <img src="../static/images/<%=Request.QueryString["type"] + "" == "jrhhr" ? "jrhhr.png" : "task_rule.png" %>" />
</asp:Content>


using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

public partial class order_list : baseClass
{

    public DataTable userdt = new DataTable();
    public Dictionary<string, object> pmlist = new Dictionary<string, object>();
    protected void Page_Load(object sender, EventArgs e)
    {
        DataTable dt = new DataTable();
        List<SqlParameter> pams = new List<SqlParameter>();
        dbClass db = new dbClass();
        pams.Add(new SqlParameter("@userid", uConfig.p_uid));
        userdt = db.getDataTable("select * from accounts with(nolock) where id=@userid", pams.ToArray());

        pmlist["uuid"] = md5("ND_" + uConfig.p_uid);


        dt = selectDateTable(chelper.gdt("account_groups"), " name='★托号' ");
        pmlist["th_groupId"] = "";
        if (dt.Rows.Count > 0)
        {
            pmlist["th_groupId"] = dt.Rows[0]["id"] + "";
        }


        pmlist["isth"] = "0";
        if (pmlist["th_groupId"] + "" == uConfig.gd(userdt, "groupid"))
        {
            pmlist["isth"] = "1";

        }



    }

    public string get_emoji_data(string id)
    {
        string emoji_data = "/::[微笑]/::[撇嘴]/::[色]/::[发呆]/::[得意]/::[流泪]/::[害羞]/::[闭嘴]/::[睡]/::[大哭]/::[尴尬]/::[发怒]/::[调皮]/::[呲牙]/::[惊讶]/::[难过]/::[囧]/::[抓狂]/::[吐]/::[偷笑]/::[愉快]/::[白眼]/::[傲慢]/::[困]/::[惊恐]/::[流汗]/::[憨笑]/::[悠闲]/::[奋斗]/::[咒骂]/::[疑问]/::[嘘]/::[晕]/::[衰]/::[骷髅]/::[敲打]/::[再见]/::[擦汗]/::[抠鼻]/::[鼓掌]/::[坏笑]/::[左哼哼]/::[右哼哼]/::[哈欠]/::[鄙视]/::[委屈]/::[快哭了]/::[阴险]/::[亲亲]/::[可怜]/::[菜刀]/::[西瓜]/::[啤酒]/::[咖啡]/::[猪头]/::[玫瑰]/::[凋谢]/::[嘴唇]/::[爱心]/::[心碎]/::[蛋糕]/::[炸弹]/::[便便]/::[月亮]/::[太阳]/::[拥抱]/::[强]/::[弱]/::[握手]/::[胜利]/::[抱拳]/::[勾引]/::[拳头]/::[OK]/::[跳跳]/::[发抖]/::[怄火]/::[转圈]";

        string emoji_text = id;

        var g = emoji_data.Split('/');

        try
        {
            emoji_text = "/" + g[Convert.ToInt16(id) + 1];
        }
        catch (Exception)
        {
            emoji_text = "/::[" + id + "]";
        }

        return emoji_text;

    }
}
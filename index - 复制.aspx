<%@ Page Title="" Language="C#" MasterPageFile="~/top.master" AutoEventWireup="true" CodeFile="index - 复制.aspx.cs" Inherits="index" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="Server">
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="Server">
    <input id="indexpage" type="hidden" value="1" />
    <!-- Demo styles -->
    <style>
        .swiper {
            width: 100%;
            height: 100%;
            margin-top: 10px;
            border-radius: 8px;
        }

        .swiper-slide {
            text-align: center;
            font-size: 18px;
            background: #fff;
            /* Center slide text vertically */
            display: -webkit-box;
            display: -ms-flexbox;
            display: -webkit-flex;
            display: flex;
            -webkit-box-pack: center;
            -ms-flex-pack: center;
            -webkit-justify-content: center;
            justify-content: center;
            -webkit-box-align: center;
            -ms-flex-align: center;
            -webkit-align-items: center;
            align-items: center;
            /*height: 120px;*/
            height: 100%;
        }

            .swiper-slide img {
                height: 100%;
            }

            .swiper-slide img {
                display: block;
                width: 100%;
                height: 100%;
                object-fit: cover;
            }
    </style>

    <!-- Swiper -->
    <div class="swiper mySwiper">
        <div class="swiper-wrapper">
            <asp:Repeater runat="server" ID="banner_list">
                <ItemTemplate>

                    <div class="swiper-slide" <%#Eval("linkurl") + "" != "" ? "onclick=\"javascript:location.href='" + Eval("linkurl") + "'\"" : "" %>>
                        <img src="<%#Eval("imgurl") %>" />
                    </div>

                </ItemTemplate>
            </asp:Repeater>
            <%--<div class="swiper-slide">
                <img src="/static/images/lb2.jpg" />
            </div>--%>
        </div>
        <div class="swiper-pagination"></div>
    </div>

    <!-- Swiper JS -->
    <script src="static/js/swiper-bundle.min.js"></script>

    <!-- Initialize Swiper -->
    <script>
        var swiper = new Swiper(".mySwiper", {
            loop: true,
            autoplay: {
                delay: 2500,
                disableOnInteraction: false,
            },
            pagination: {
                el: ".swiper-pagination",
                dynamicBullets: true,
            },
        });
    </script>


    <!-- 首页嵌入代码 -->
    <%=uConfig.stcdata("index_code") %>



    <!-- 消息滚动 -->
    <style>
        /*公告*/
        .notice {
            width: 100%;
            margin: 20px auto;
            /*height: 26px;*/
            overflow: hidden;
            position: relative;
            padding: 0 6px;
            box-sizing: border-box;
        }

            .notice svg {
                position: absolute;
                height: 22px;
                width: 22px;
                top: 2px;
            }

        .noticTipTxt {
            color: #ff7300;
            height: 22px;
            line-height: 22px;
            overflow: hidden;
            margin: 0 0 0 30px;
            list-style: none;
            padding: 0;
        }

            .noticTipTxt li {
                height: 22px;
                line-height: 22px;
            }

            .noticTipTxt a {
                color: #222;
                font-size: 14px;
                text-decoration: none;
            }

                .noticTipTxt a:hover {
                    color: #ff7300;
                    text-decoration: underline;
                }


            .noticTipTxt.list_notice {
                height: 300px;
                line-height: 300px;
            }

                .noticTipTxt.list_notice li {
                    height: 30px;
                    line-height: 30px;
                }

                .noticTipTxt.list_notice a {
                    color: #000;
                    font-size: 12px;
                }
    </style>

    <div class="notice">

        <svg t="1692210433046" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="30657" width="22" height="22">
            <path d="M511.1 870.3c-8.3 0-16.6-2.9-23.2-8.5L293.8 698.5H187.6c-19.9 0-36-16.1-36-36V363.6c0-19.9 16.1-36 36-36h106.2l194.1-163.3c10.7-9 25.7-11 38.4-5.1s20.8 18.7 20.8 32.7v642.5c0 14-8.1 26.8-20.8 32.7-4.9 2.1-10.1 3.2-15.2 3.2zM223.6 626.4h83.3c8.5 0 16.7 3 23.2 8.5L475 756.8V269.2L330.1 391.1c-6.5 5.5-14.7 8.5-23.2 8.5h-83.3v226.8z" p-id="30658"></path><path d="M675.4 778c-15.1 0-29.1-9.5-34.2-24.6-6.3-18.9 3.9-39.3 22.8-45.6 83.9-28 140.3-106.2 140.3-194.7 0-88.5-56.3-166.7-140.2-194.7-18.9-6.3-29.1-26.7-22.8-45.6 6.3-18.9 26.7-29.1 45.6-22.8 54.7 18.3 101.7 52.6 135.9 99.4 35 47.7 53.4 104.3 53.4 163.7 0 59.4-18.5 116-53.5 163.7-34.3 46.8-81.3 81.1-136 99.4-3.7 1.2-7.5 1.8-11.3 1.8z" fill="#4195F9" p-id="30659"></path><path d="M640.7 664.5c-11.6 0-23.1-5.6-30-16-11.1-16.5-6.6-38.9 10-50 28.7-19.1 45.8-51.1 45.8-85.5s-17.1-66.4-45.8-85.5c-16.6-11.1-21-33.4-10-50 11.1-16.5 33.4-21 50-10 48.7 32.5 77.8 86.9 77.8 145.4s-29.1 112.9-77.8 145.4c-6.1 4.3-13.1 6.2-20 6.2z" fill="#4195F9" p-id="30660"></path></svg>

        <ul id="jsfoot02" class="noticTipTxt">
            <asp:Repeater ID="message_list" runat="server">
                <ItemTemplate>
                    <li><a href="message_details.aspx?id=<%#Eval("id") %>"><%#Eval("name") %></a></li>
                </ItemTemplate>
            </asp:Repeater>
        </ul>

        <svg t="1692210816804" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="36905" width="16" height="16" style="right: 6px; top: 1px!important" onclick="javascript:location.href='message_list.aspx';">
            <path d="M363.229916 275.453327h497.82454a43.350032 43.350032 0 1 0 0-86.700064H363.229916a43.350032 43.350032 0 0 0 0 86.700064zM861.054456 471.930121H363.229916a43.350032 43.350032 0 0 0 0 86.700063h497.82454a43.350032 43.350032 0 0 0 0-86.700063zM861.054456 755.099753H363.229916a43.350032 43.350032 0 0 0 0 86.700064h497.82454a43.350032 43.350032 0 1 0 0-86.700064z" fill="" p-id="36906"></path><path d="M201.707698 232.103295m-76.585056 0a76.585056 76.585056 0 1 0 153.170112 0 76.585056 76.585056 0 1 0-153.170112 0Z" fill="" p-id="36907"></path><path d="M201.707698 513.878501m-76.585056 0a76.585056 76.585056 0 1 0 153.170112 0 76.585056 76.585056 0 1 0-153.170112 0Z" fill="" p-id="36908"></path><path d="M201.707698 795.653708m-76.585056 0a76.585056 76.585056 0 1 0 153.170112 0 76.585056 76.585056 0 1 0-153.170112 0Z" fill="" p-id="36909"></path></svg>


    </div>



    <!-- 顶部导航 -->
    <style>
        .icon_img {
            border-radius: 50%;
    padding: 5px;
    width: 28px;
    height: 28px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto;
    margin-bottom: 3px;
        }
    </style>
    <div style="background: #fff; padding: 15px 10px; border-radius: 8px; display: flex; margin-bottom: 10px; flex-wrap: wrap;">
        
        <div style="width: 25%; text-align: center; color: #2a44a1; font-size: 13px; font-weight: bold;">
            <a href="dating.aspx" style="display: inline-block; color: #2a44a1; text-decoration: none; outline: none;">
                <%--<div class="icon_img" style="background: #e7dd8d3d;">

                            <svg t="" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="1701" width="18" height="18">
                                <path d="M513.3312 514.2528m-432.6912 0a432.6912 432.6912 0 1 0 865.3824 0 432.6912 432.6912 0 1 0-865.3824 0Z" fill="#F9E230" p-id="1702"></path><path d="M83.6096 564.3264a429.7216 382.6176 0 1 0 859.4432 0 429.7216 382.6176 0 1 0-859.4432 0Z" fill="#F6DB2D" p-id="1703"></path><path d="M92.416 614.4a420.9152 332.544 0 1 0 841.8304 0 420.9152 332.544 0 1 0-841.8304 0Z" fill="#F5D228" p-id="1704"></path><path d="M107.4688 664.4736a405.8624 282.4192 0 1 0 811.7248 0 405.8624 282.4192 0 1 0-811.7248 0Z" fill="#F2C824" p-id="1705"></path><path d="M129.8432 714.5984a383.488 232.3456 0 1 0 766.976 0 383.488 232.3456 0 1 0-766.976 0Z" fill="#F0BF21" p-id="1706"></path><path d="M681.6768 578.048h-126.976v-58.7776h126.976c22.6304 0 40.96-18.3296 40.96-40.96s-18.3296-40.96-40.96-40.96h-75.7248l78.4896-81.1008a40.9344 40.9344 0 0 0-0.9728-57.9072c-16.2816-15.7184-42.1888-15.3088-57.9072 0.9728L513.024 415.5904 397.6192 298.9568c-15.9232-16.0768-41.8304-16.2304-57.9072-0.3072-16.0768 15.9232-16.2304 41.8304-0.3072 57.9072L419.328 437.3504H345.8048c-22.6304 0-40.96 18.3296-40.96 40.96s18.3296 40.96 40.96 40.96h126.976v58.7776h-126.976c-22.6304 0-40.96 18.3296-40.96 40.96s18.3296 40.96 40.96 40.96h126.976v99.1744c0 22.6304 18.3296 40.96 40.96 40.96s40.96-18.3296 40.96-40.96V659.968h126.976c22.6304 0 40.96-18.3296 40.96-40.96s-18.3296-40.96-40.96-40.96z" fill="#FFFFFF" p-id="1707"></path></svg>

                        </div>--%>
                
                <img src="static/images/coinbuy.png" style="width: 38px;" />
                <div>我要买币</div>
            </a>
        </div>
        <div style="width: 25%; text-align: center; color: #2a44a1; font-size: 13px; font-weight: bold;">
            <a href="sell_dating.aspx" style="display: inline-block; color: #2a44a1; text-decoration: none; outline: none;">
                <%--<div class="icon_img" style="background: #e7dd8d3d;">

                            <svg t="" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="1701" width="18" height="18">
                                <path d="M513.3312 514.2528m-432.6912 0a432.6912 432.6912 0 1 0 865.3824 0 432.6912 432.6912 0 1 0-865.3824 0Z" fill="#F9E230" p-id="1702"></path><path d="M83.6096 564.3264a429.7216 382.6176 0 1 0 859.4432 0 429.7216 382.6176 0 1 0-859.4432 0Z" fill="#F6DB2D" p-id="1703"></path><path d="M92.416 614.4a420.9152 332.544 0 1 0 841.8304 0 420.9152 332.544 0 1 0-841.8304 0Z" fill="#F5D228" p-id="1704"></path><path d="M107.4688 664.4736a405.8624 282.4192 0 1 0 811.7248 0 405.8624 282.4192 0 1 0-811.7248 0Z" fill="#F2C824" p-id="1705"></path><path d="M129.8432 714.5984a383.488 232.3456 0 1 0 766.976 0 383.488 232.3456 0 1 0-766.976 0Z" fill="#F0BF21" p-id="1706"></path><path d="M681.6768 578.048h-126.976v-58.7776h126.976c22.6304 0 40.96-18.3296 40.96-40.96s-18.3296-40.96-40.96-40.96h-75.7248l78.4896-81.1008a40.9344 40.9344 0 0 0-0.9728-57.9072c-16.2816-15.7184-42.1888-15.3088-57.9072 0.9728L513.024 415.5904 397.6192 298.9568c-15.9232-16.0768-41.8304-16.2304-57.9072-0.3072-16.0768 15.9232-16.2304 41.8304-0.3072 57.9072L419.328 437.3504H345.8048c-22.6304 0-40.96 18.3296-40.96 40.96s18.3296 40.96 40.96 40.96h126.976v58.7776h-126.976c-22.6304 0-40.96 18.3296-40.96 40.96s18.3296 40.96 40.96 40.96h126.976v99.1744c0 22.6304 18.3296 40.96 40.96 40.96s40.96-18.3296 40.96-40.96V659.968h126.976c22.6304 0 40.96-18.3296 40.96-40.96s-18.3296-40.96-40.96-40.96z" fill="#FFFFFF" p-id="1707"></path></svg>

                        </div>--%>
                
                <img src="static/images/coinsell.png" style="width: 38px;" />
                <div>我要提款</div>
            </a>
        </div>


        <%--<div style="width: 25%; text-align: center; color: #2a44a1; font-size: 13px; font-weight: bold;">

            <a href="user_task.aspx?type=xrhl" style="display: inline-block; color: #2a44a1; text-decoration: none; outline: none;">
                <svg t="1699544873152" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="3728" width="38" height="38">
                    <path d="M102.4 409.6h819.2v409.6a102.4 102.4 0 0 1-102.4 102.4H204.8a102.4 102.4 0 0 1-102.4-102.4V409.6zM102.4 409.6h819.2L579.4816 110.1824a102.4 102.4 0 0 0-134.9632 0z" fill="#36C196" p-id="3729"></path><path d="M204.8 204.8h614.4a51.2 51.2 0 0 1 51.2 51.2v563.2H153.6V256a51.2 51.2 0 0 1 51.2-51.2z" fill="#FFBA40" p-id="3730"></path><path d="M327.68 447.5904a20.48 20.48 0 0 0-4.7104-14.0288A43.3152 43.3152 0 0 0 307.2 423.936a173.056 173.056 0 0 1-19.5584-8.8064 60.6208 60.6208 0 0 1-14.0288-10.24 39.0144 39.0144 0 0 1-9.1136-13.312 44.4416 44.4416 0 0 1-3.2768-18.1248 40.96 40.96 0 0 1 11.1616-29.7984 46.6944 46.6944 0 0 1 29.7984-13.6192V307.2h16.5888v23.3472a43.1104 43.1104 0 0 1 28.7744 15.872 53.9648 53.9648 0 0 1 10.24 34.2016H327.68a30.72 30.72 0 0 0-5.12-19.3536 16.7936 16.7936 0 0 0-13.7216-6.3488 17.1008 17.1008 0 0 0-13.2096 5.0176 19.456 19.456 0 0 0-4.608 13.824 18.432 18.432 0 0 0 4.5056 13.1072 51.2 51.2 0 0 0 16.9984 10.24 194.56 194.56 0 0 1 20.48 9.728 61.44 61.44 0 0 1 13.5168 10.24 42.1888 42.1888 0 0 1 8.4992 13.0048 46.592 46.592 0 0 1 3.3792 17.3056 40.96 40.96 0 0 1-10.9568 29.696 47.7184 47.7184 0 0 1-30.72 13.5168V512h-16.5888v-21.2992a49.664 49.664 0 0 1-32.9728-15.6672A51.9168 51.9168 0 0 1 256 439.6032h30.0032a29.2864 29.2864 0 0 0 5.8368 19.6608 20.48 20.48 0 0 0 16.896 6.8608 20.48 20.48 0 0 0 13.9264-5.3248 17.7152 17.7152 0 0 0 5.0176-13.2096z" fill="#FFFFFF" p-id="3731"></path><path d="M460.288 307.2m25.6 0l256 0q25.6 0 25.6 25.6l0 0q0 25.6-25.6 25.6l-256 0q-25.6 0-25.6-25.6l0 0q0-25.6 25.6-25.6Z" fill="#FFFFFF" p-id="3732"></path><path d="M613.888 409.6m25.6 0l102.4 0q25.6 0 25.6 25.6l0 0q0 25.6-25.6 25.6l-102.4 0q-25.6 0-25.6-25.6l0 0q0-25.6 25.6-25.6Z" fill="#FFFFFF" p-id="3733"></path><path d="M102.4 409.6v409.6a102.4 102.4 0 0 0 102.4 102.4h563.2z" fill="#2A44A1" p-id="3734"></path><path d="M921.6 409.6v409.6a102.4 102.4 0 0 1-102.4 102.4H256z" fill="#3D60F6" p-id="3735"></path></svg>
                <div>新人豪礼</div>
            </a>
        </div>--%>

        <div style="width: 25%; text-align: center; color: #2a44a1; font-size: 13px; font-weight: bold;">
            <a href="luckwheel.aspx" style="display: inline-block; color: #2a44a1; text-decoration: none; outline: none;">
                <img src="zhuanpan/img/幸运转盘.png" style="width: 38px;" />
                <div>幸运大转盘</div>
            </a>
        </div>
        <%--<div style="width: 25%; text-align: center; color: #2a44a1; font-size: 13px; font-weight: bold;">
            <a href="user_task.aspx?type=yrsw" style="display: inline-block; color: #2a44a1; text-decoration: none; outline: none;">
                <svg t="1699545174919" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="10161" width="38" height="38">
                    <path d="M819.2 870.4H204.8a102.4 102.4 0 0 1-102.4-102.4V256h819.2v512a102.4 102.4 0 0 1-102.4 102.4z" fill="#3D60F6" p-id="10162"></path><path d="M819.2 358.4H102.4V256a102.4 102.4 0 0 1 102.4-102.4h614.4a102.4 102.4 0 0 1 102.4 102.4 102.4 102.4 0 0 1-102.4 102.4z" fill="#2A44A1" p-id="10163"></path><path d="M819.2 819.2m-153.6 0a153.6 153.6 0 1 0 307.2 0 153.6 153.6 0 1 0-307.2 0Z" fill="#FFBA40" p-id="10164"></path><path d="M804.2496 865.6896a10.24 10.24 0 0 1-7.2704-3.072l-36.2496-36.1472a10.24 10.24 0 0 1 14.5408-14.5408l28.9792 28.9792 65.1264-65.1264a10.24 10.24 0 0 1 14.4384 14.4384l-72.3968 72.3968a10.24 10.24 0 0 1-7.168 3.072z" fill="#FFFFFF" p-id="10165"></path><path d="M665.6 358.4L409.6 102.4 153.6 358.4h512z" fill="#36C196" p-id="10166"></path><path d="M768 358.4L512 102.4 256 358.4h512z" fill="#FFBA40" p-id="10167"></path><path d="M475.7504 241.0496a51.2 51.2 0 0 0 72.4992 0L665.6 358.4H358.4z" fill="#FFFFFF" p-id="10168"></path><path d="M409.6 665.6H102.4V460.8h307.2a102.4 102.4 0 0 1 102.4 102.4 102.4 102.4 0 0 1-102.4 102.4z" fill="#36C196" p-id="10169"></path><path d="M409.6 563.2m-51.2 0a51.2 51.2 0 1 0 102.4 0 51.2 51.2 0 1 0-102.4 0Z" fill="#FFFFFF" p-id="10170"></path></svg>
                <div>推荐有好礼</div>
            </a>
        </div>--%>
        <div style="width: 25%; text-align: center; color: #2a44a1; font-size: 13px; font-weight: bold;">
            <a href="partners_manage.aspx" style="display: inline-block; color: #2a44a1; text-decoration: none; outline: none;">
                <%--<svg t="1699545505093" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="20731" width="38" height="38">
                    <path d="M224 640V224a96 96 0 0 1 96-96h384a96 96 0 0 1 96 96V640z" fill="#D0E2FE" p-id="20732"></path><path d="M800 896H224a96 96 0 0 1-96-96V496a32 32 0 0 1 44.3-30l302.8 126.6a95.4 95.4 0 0 0 73.8 0l302.8-126.1a32 32 0 0 1 44.3 30V800a96 96 0 0 1-96 96zM608 256H416a32 32 0 0 0 0 64h192a32 32 0 1 0 0-64zM544 384H480a32 32 0 0 0 0 64h64a32 32 0 1 0 0-64z" fill="#3F86FF" p-id="20733"></path><path d="M512 720m-48 0a48 48 0 1 0 96 0 48 48 0 1 0-96 0Z" fill="#FFFFFF" p-id="20734"></path></svg>--%>
                <img src="static/images/yrbw.png" style="width: 38px;" />
                <div>月入百万</div>
            </a>
        </div>





        <div style="width: 25%; text-align: center; color: #2a44a1; font-size: 13px; font-weight: bold; margin-top: 28px;">
            <a href="message_details.aspx?id=<%=uConfig.gd(selectDateTable(msgList," name='公司简介' "), "id") %>" style="display: inline-block; color: #2a44a1; text-decoration: none; outline: none;">
                <img src="static/images/gsjj.png" style="width: 38px;" />
                <div>公司简介</div>
            </a>
        </div>


        <div style="width: 25%; text-align: center; color: #2a44a1; font-size: 13px; font-weight: bold; margin-top: 28px;">
            <a href="message_details.aspx?id=<%=uConfig.gd(selectDateTable(msgList," name='企业资质' "), "id") %>" style="display: inline-block; color: #2a44a1; text-decoration: none; outline: none;">
                <img src="static/images/qyzz.png" style="width: 38px;" />
                <div>企业资质</div>
            </a>
        </div>


        <div style="width: 25%; text-align: center; color: #2a44a1; font-size: 13px; font-weight: bold; margin-top: 28px;">
            <a href="message_details.aspx?id=<%=uConfig.gd(selectDateTable(msgList," name='公司展示' "), "id") %>" style="display: inline-block; color: #2a44a1; text-decoration: none; outline: none;">
                <img src="static/images/gszs.png" style="width: 38px;" />
                <div>公司展示</div>
            </a>
        </div>


        <div style="width: 25%; text-align: center; color: #2a44a1; font-size: 13px; font-weight: bold; margin-top: 28px;">
            <a href="<%=uConfig.stcdata("kf_online") %>" style="display: inline-block; color: #2a44a1; text-decoration: none; outline: none;">
                <img src="static/images/lxwm.png" style="width: 38px;" />
                <div>在线客服</div>
            </a>
        </div>

    </div>



    <style>
        .list-box[data-v-19d7bc70] {
            width: 100%;
            border-radius: 10px;
            overflow: hidden;
            background: #fff;
            box-shadow: 0.06rem 0.06rem 0.24rem 0 rgb(240 129 1 / 38%);
            padding-bottom: 10px;
            margin-bottom: 20px;
            max-width: 200px;
            margin: 0 10px;
            cursor: pointer;
        }

        .list-box .list-con[data-v-19d7bc70] {
            display: -webkit-box;
            display: -webkit-flex;
            display: flex;
            -webkit-box-orient: horizontal;
            -webkit-box-direction: normal;
            -webkit-flex-direction: row;
            flex-direction: row;
            -webkit-box-pack: start;
            -webkit-justify-content: flex-start;
            justify-content: flex-start;
            padding: 10px 4%;
        }

        .list-box .list-con .con-left[data-v-19d7bc70] {
            width: 46px;
            height: 46px;
            margin-right: 4px;
        }

        .list-box .list-bottom[data-v-19d7bc70] {
            width: 60%;
            margin: 0 auto;
            line-height: 24px;
            font-size: 12px;
            color: #fff;
            background: #ee271f;
            text-align: center;
            border-radius: 20px;
        }
    </style>


    <!-- 广告模块 -->
    <div style="background: #fff; padding: 15px 10px; border-radius: 8px;">

        <div style="display: flex; margin-bottom: 18px;">
            <svg t="" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="16099" width="22" height="22">
                <path d="M825.1 735H202.4c-28.9 0-52.4-23.5-52.4-52.4V239c0-28.9 23.5-52.4 52.4-52.4h622.7c28.9 0 52.4 23.5 52.4 52.4v443.7c-0.1 28.8-23.6 52.3-52.4 52.3z m-608.4-66.6h594.1V253.3H216.7v415.1zM659.5 844.9H368c-18.4 0-33.3-14.9-33.3-33.3s14.9-33.3 33.3-33.3h291.5c18.4 0 33.3 14.9 33.3 33.3s-14.9 33.3-33.3 33.3z" p-id="16100"></path><path d="M343.7 587.1c-18.4 0-33.3-14.9-33.3-33.3v-186c0-18.4 14.9-33.3 33.3-33.3s33.3 14.9 33.3 33.3v186c0 18.4-14.9 33.3-33.3 33.3zM683.7 587.1c-18.4 0-33.3-14.9-33.3-33.3v-166c0-18.4 14.9-33.3 33.3-33.3s33.3 14.9 33.3 33.3v166c0 18.4-14.9 33.3-33.3 33.3zM513.7 587.1c-18.4 0-33.3-14.9-33.3-33.3v-116c0-18.4 14.9-33.3 33.3-33.3s33.3 14.9 33.3 33.3v116c0 18.4-14.9 33.3-33.3 33.3z" fill="#4195F9" p-id="16101"></path></svg>&nbsp;任务大厅
        </div>

        <div style="display: flex; justify-content: center;">

            <div style="display: flex; justify-content: center; width: 50%;">
                <div data-v-19d7bc70="" class="list-box" onclick="javascript:location.href='/order.aspx'">
                    <div data-v-19d7bc70="" class="list-top">
                        <a class="goQiangDan" data-cid="1">
                            <img data-v-19d7bc70="" src="../static/images/zq1.png" style="width: 100%; height: 40px;"></a>
                    </div>
                    <a class="goQiangDan" data-cid="1">
                        <div data-v-19d7bc70="" class="list-con">
                            <div data-v-19d7bc70="" class="con-left">
                                <img data-v-19d7bc70="" src="../static/images/icon1.png" mode="widthFix" alt="" style="width: 100%; display: block;">
                            </div>
                            <div data-v-19d7bc70="" class="con-right">
                                <div data-v-19d7bc70="" class="right-top">天猫</div>
                                <div data-v-19d7bc70="" class="right-bottom">任务专属通道</div>
                            </div>
                        </div>
                    </a>
                    <div data-v-19d7bc70="" class="list-bottom"><a class="goQiangDan" data-cid="1">开始任务</a></div>
                </div>
            </div>


            <div style="display: flex; justify-content: center; width: 50%;">
                <div data-v-19d7bc70="" class="list-box" onclick="javascript:location.href='/order.aspx'">
                    <div data-v-19d7bc70="" class="list-top">
                        <a class="goQiangDan" data-cid="1">
                            <img data-v-19d7bc70="" src="../static/images/zq2.png" style="width: 100%; height: 40px;"></a>
                    </div>
                    <a class="goQiangDan" data-cid="1">
                        <div data-v-19d7bc70="" class="list-con">
                            <div data-v-19d7bc70="" class="con-left">
                                <img data-v-19d7bc70="" src="../static/images/icon2.png" mode="widthFix" alt="" style="width: 100%; display: block;">
                            </div>
                            <div data-v-19d7bc70="" class="con-right">
                                <div data-v-19d7bc70="" class="right-top">淘宝</div>
                                <div data-v-19d7bc70="" class="right-bottom">任务专属通道</div>
                            </div>
                        </div>
                    </a>
                    <div data-v-19d7bc70="" class="list-bottom"><a class="goQiangDan" data-cid="1">开始任务</a></div>
                </div>
            </div>

        </div>

    </div>



    <div style="display: flex; margin-top: 18px;">

        <div style="background: #fff; margin-right: 10px; border-radius: 10px; padding: 20px 10px; width: 50%; text-align: center;" onclick="javascript:location.href='reward_list.aspx'">

            <svg t="1692292467426" class="icon" viewBox="0 0 1028 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="36489" width="22" height="22">
                <path d="M717.327011 713.307738m-310.692262 0a310.692262 310.692262 0 1 0 621.384524 0 310.692262 310.692262 0 1 0-621.384524 0Z" fill="#5CF1E7" p-id="36490"></path><path d="M493.16232 538.01095a44.832938 44.832938 0 0 0-44.832938 44.832938v224.164691a44.832938 44.832938 0 0 0 89.665876 0v-224.164691a44.832938 44.832938 0 0 0-44.832938-44.832938z" fill="#A8B9E9" p-id="36491"></path><path d="M829.409357 248.838499a67.249407 67.249407 0 0 1 0 134.498814h-672.494073a67.249407 67.249407 0 0 1 0-134.498814h672.494073m0-89.665877h-672.494073a156.915284 156.915284 0 0 0 0 313.830568h672.494073a156.915284 156.915284 0 0 0 0-313.830568z" fill="#1F56F5" p-id="36492"></path><path d="M757.676656 1005.170166H229.096314a149.293684 149.293684 0 0 1-148.845355-149.293684V428.170251a44.832938 44.832938 0 0 1 44.832939-44.832938h736.605174a44.832938 44.832938 0 0 1 44.832938 44.832938V672.509765a44.832938 44.832938 0 0 1-89.665876 0v-199.506575H169.916836v382.873292a59.179478 59.179478 0 0 0 59.179478 59.627808H757.676656a59.179478 59.179478 0 0 0 59.179478-59.627808 44.832938 44.832938 0 0 1 89.665876 0 149.293684 149.293684 0 0 1-148.845354 149.293684zM266.307653 244.355205a44.832938 44.832938 0 0 1-39.901315-24.658116A151.087002 151.087002 0 0 1 408.428067 7.637291a151.98366 151.98366 0 0 1 89.665876 75.319336l13.449882 27.348092a44.832938 44.832938 0 0 1-79.80263 40.349645l-13.898211-26.899763A61.421125 61.421125 0 1 0 306.208968 179.347444a44.832938 44.832938 0 0 1-19.726493 60.076138 47.522914 47.522914 0 0 1-20.174822 4.931623z" fill="#1F56F5" p-id="36493"></path><path d="M717.327011 247.94184a40.797974 40.797974 0 0 1-20.174822-4.931623 44.832938 44.832938 0 0 1-19.726493-60.076138A63.662772 63.662772 0 0 0 681.012331 134.514506a62.766113 62.766113 0 0 0-30.934727-35.86635 63.662772 63.662772 0 0 0-47.074585-3.586635 62.766113 62.766113 0 0 0-35.866351 30.934727l-13.449881 26.899763a44.832938 44.832938 0 0 1-80.25096-40.349645l13.898211-27.348092A151.087002 151.087002 0 0 1 690.427248 20.190514a148.397025 148.397025 0 0 1 75.767666 89.665876 149.293684 149.293684 0 0 1-8.966588 114.323993 44.832938 44.832938 0 0 1-39.901315 23.761457z" fill="#1F56F5" p-id="36494"></path></svg>

            <div style="color: #FD863E; font-size: 20px; margin: 15px 0;">
                <b><%=uConfig.gnumber(userdt,"reward_amount") %></b>
            </div>

            <div>
                <b style="font-size: 14px;">我的奖励</b>
            </div>

        </div>


        <div style="width: 50%;">
            <a style="background: #fff; padding: 22px 25px; width: 100%; display: flex; align-items: center; border-radius: 8px; box-sizing: border-box; font-weight: bold; color: #333; outline: none; text-decoration: none;" href="rob_list.aspx">
                <svg t="1692292795019" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="1104" width="22" height="22">
                    <path d="M679.563636 701.44m-322.56 0a322.56 322.56 0 1 0 645.12 0 322.56 322.56 0 1 0-645.12 0Z" fill="#5CF1E7" p-id="1105"></path><path d="M631.156364 186.181818h-372.363637a46.545455 46.545455 0 0 0 0 93.090909h372.363637a46.545455 46.545455 0 0 0 0-93.090909zM631.621818 372.363636h-372.363636a46.545455 46.545455 0 0 0 0 93.090909h372.363636a46.545455 46.545455 0 0 0 0-93.090909zM446.370909 555.752727h-186.181818a46.545455 46.545455 0 0 0 0 93.090909h186.181818a46.545455 46.545455 0 0 0 0-93.090909z" fill="#A8B9E9" p-id="1106"></path><path d="M399.825455 1024H153.6A131.723636 131.723636 0 0 1 21.876364 892.276364V131.723636A131.723636 131.723636 0 0 1 153.6 0h584.145455a131.723636 131.723636 0 0 1 131.723636 131.723636v553.890909a46.545455 46.545455 0 0 1-93.090909 0V131.723636a38.632727 38.632727 0 0 0-38.632727-38.632727H153.6a38.632727 38.632727 0 0 0-38.632727 38.632727v760.552728a38.632727 38.632727 0 0 0 38.632727 38.632727h246.225455a46.545455 46.545455 0 0 1 0 93.090909z" fill="#1F56F5" p-id="1107"></path><path d="M731.229091 729.832727l-116.829091 126.138182v-126.138182h116.829091m93.090909-93.090909h-256a52.130909 52.130909 0 0 0-46.545455 56.32v274.618182a52.130909 52.130909 0 0 0 46.545455 56.32 42.356364 42.356364 0 0 0 30.72-14.429091l253.672727-274.618182c32.116364-34.443636 11.636364-98.210909-31.185454-98.210909z" fill="#1F56F5" p-id="1108"></path></svg>&nbsp;&nbsp;&nbsp;&nbsp;<span>抢单记录</span>
            </a>

            <a style="background: #fff; padding: 22px 25px; width: 100%; display: flex; align-items: center; border-radius: 8px; box-sizing: border-box; font-weight: bold; color: #333; outline: none; text-decoration: none; margin-top: 11px;" href="buy_orderList.aspx">
                <svg t="1692293082656" class="icon" viewBox="0 0 1026 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2512" width="22" height="22">
                    <path d="M716.130927 713.43895m-310.56105 0a310.56105 310.56105 0 1 0 621.1221 0 310.56105 310.56105 0 1 0-621.1221 0Z" fill="#5CF1E7" p-id="2513"></path><path d="M627.399198 0H362.100292a47.054705 47.054705 0 1 0 0 94.109409H627.399198A44.814004 44.814004 0 1 0 627.399198 0z" fill="#A8B9E9" p-id="2514"></path><path d="M384.507295 394.811379a47.054705 47.054705 0 0 0-33.610504 80.217067l78.872648 78.872648a47.054705 47.054705 0 0 0 33.610503 13.892341 44.814004 44.814004 0 0 0 33.162363-13.892341 44.814004 44.814004 0 0 0 0-66.324726L417.669658 408.70372a44.814004 44.814004 0 0 0-33.162363-13.892341z" fill="#1F56F5" p-id="2515"></path><path d="M600.510796 395.259519a47.054705 47.054705 0 0 0-33.162364 13.892341l-78.424507 78.424508a47.054705 47.054705 0 0 0 66.772866 66.324726l78.424508-77.976368a47.502845 47.502845 0 0 0 0-66.772866 49.295405 49.295405 0 0 0-33.610503-13.892341z" fill="#1F56F5" p-id="2516"></path><path d="M627.399198 504.157549H362.100292a47.054705 47.054705 0 0 0 0 94.109409H627.399198a47.054705 47.054705 0 0 0 0-94.109409zM627.399198 638.599562H362.100292a44.814004 44.814004 0 0 0 0 94.10941H627.399198a47.054705 47.054705 0 0 0 0-94.10941z" fill="#1F56F5" p-id="2517"></path><path d="M761.841211 146.093654H227.658279a47.054705 47.054705 0 0 0 0 94.109409H761.841211a44.814004 44.814004 0 1 0 0-94.109409z" fill="#A8B9E9" p-id="2518"></path><path d="M492.957185 537.768053a44.814004 44.814004 0 0 0-44.814004 47.054704v230.792123a44.814004 44.814004 0 0 0 94.109409 0v-230.343983A44.814004 44.814004 0 0 0 492.957185 537.768053z" fill="#1F56F5" p-id="2519"></path><path d="M864.913421 993.974617H123.241649A122.790372 122.790372 0 0 1 0.003137 871.632385V392.570678A122.790372 122.790372 0 0 1 123.241649 268.884026H864.913421a122.790372 122.790372 0 0 1 123.238512 121.894092v239.754924a44.814004 44.814004 0 0 1-89.628008 0V392.570678A33.162363 33.162363 0 0 0 864.913421 358.512035H123.241649a33.162363 33.162363 0 0 0-33.610503 34.058643v479.061707a33.162363 33.162363 0 0 0 33.610503 32.714223H864.913421a33.162363 33.162363 0 0 0 33.610504-32.714223 44.814004 44.814004 0 0 1 89.628008 0 122.790372 122.790372 0 0 1-123.238512 122.342232z" fill="#1F56F5" p-id="2520"></path></svg>&nbsp;&nbsp;&nbsp;&nbsp;<span>买币记录</span>
            </a>
        </div>

    </div>


    <div class="notice" style="background: #fff; border-radius: 8px; padding: 10px 16px;">

        <svg t="1692210433046" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="30657" width="22" height="22" style="top: 16px;">
            <path d="M511.1 870.3c-8.3 0-16.6-2.9-23.2-8.5L293.8 698.5H187.6c-19.9 0-36-16.1-36-36V363.6c0-19.9 16.1-36 36-36h106.2l194.1-163.3c10.7-9 25.7-11 38.4-5.1s20.8 18.7 20.8 32.7v642.5c0 14-8.1 26.8-20.8 32.7-4.9 2.1-10.1 3.2-15.2 3.2zM223.6 626.4h83.3c8.5 0 16.7 3 23.2 8.5L475 756.8V269.2L330.1 391.1c-6.5 5.5-14.7 8.5-23.2 8.5h-83.3v226.8z" p-id="30658"></path><path d="M675.4 778c-15.1 0-29.1-9.5-34.2-24.6-6.3-18.9 3.9-39.3 22.8-45.6 83.9-28 140.3-106.2 140.3-194.7 0-88.5-56.3-166.7-140.2-194.7-18.9-6.3-29.1-26.7-22.8-45.6 6.3-18.9 26.7-29.1 45.6-22.8 54.7 18.3 101.7 52.6 135.9 99.4 35 47.7 53.4 104.3 53.4 163.7 0 59.4-18.5 116-53.5 163.7-34.3 46.8-81.3 81.1-136 99.4-3.7 1.2-7.5 1.8-11.3 1.8z" fill="#4195F9" p-id="30659"></path><path d="M640.7 664.5c-11.6 0-23.1-5.6-30-16-11.1-16.5-6.6-38.9 10-50 28.7-19.1 45.8-51.1 45.8-85.5s-17.1-66.4-45.8-85.5c-16.6-11.1-21-33.4-10-50 11.1-16.5 33.4-21 50-10 48.7 32.5 77.8 86.9 77.8 145.4s-29.1 112.9-77.8 145.4c-6.1 4.3-13.1 6.2-20 6.2z" fill="#4195F9" p-id="30660"></path></svg>

        <ul id="transaction_notice" class="noticTipTxt list_notice">
            <%=String.Join("\r\n", msgs) %>
        </ul>


    </div>



    <script src="../static/js/scrolltext.js"></script>
    <script type="text/javascript">
        var scrollup = new ScrollText("jsfoot02");
        scrollup.LineHeight = 22;        //单排文字滚动的高度
        scrollup.Amount = 1;            //注意:子模块(LineHeight)一定要能整除Amount.
        scrollup.Delay = 1;           //延时
        scrollup.Timeout = 3000;
        scrollup.Start();             //文字自动滚动
        scrollup.Direction = "up";   //默认设置为文字向上滚动

        var scrollup_transaction = new ScrollText("transaction_notice");
        scrollup_transaction.LineHeight = 60;        //单排文字滚动的高度
        scrollup_transaction.Amount = 1;            //注意:子模块(LineHeight)一定要能整除Amount.
        scrollup_transaction.Delay = 0.1;           //延时
        scrollup_transaction.Timeout = 3000;
        scrollup_transaction.Start();             //文字自动滚动
        scrollup_transaction.Direction = "up";   //默认设置为文字向上滚动

    </script>



    <%--<div style="background: #fff; padding: 18px 26px; font-size: 13px; border-radius: 10px;">

        <div style="display: flex; border-bottom: 1px solid #ddd; padding: 8px 0; padding-bottom: 16px; color: #000; font-weight: bold; align-items: center;">
            <span style="font-size: 15px;">一键任务佣金排行榜【昨日】</span>
        </div>


        <asp:Repeater ID="rankList" runat="server">
            <ItemTemplate>

                <div style="display: flex; border-bottom: 1px solid #f1f1f1; padding: 18px 0; color: <%# Container.ItemIndex + 1>3 ? "gray" :Container.ItemIndex + 1>1 ? "#333" : "#5c1cf7" %>;">
                    <span style="display: flex; align-items: center; position: relative; left: -3px;">


                        <%# Container.ItemIndex+1>3?"<div style='color:#EEC994;font-size: 18px;text-shadow: 3px 3px 7px #EEC994;font-weight: bold;width:50px;text-align:center;margin-right:10px;'>"+(Container.ItemIndex+1)+"</div>":"<img src='/static/images/r"+(Container.ItemIndex+1)+".png' alt=''  width='50' style='margin-right:10px;' />" %>

                        <b><%#Eval("phone") %></b>
                    </span>
                    <span style="margin-left: auto; text-align: right;">
                        <b style="display: flex; align-items: center;">
                            <img src="/static/images/coin.png" alt="" height="15" width="15" style="margin-right: 5px;"><%#Convert.ToDouble(Eval("total_amount").ToString()).ToString("0.00") %></b>
                    </span>
                </div>

            </ItemTemplate>
        </asp:Repeater>
    </div>--%>

    <script>
        $(function () {
            $('[coin="binance"]').find('.diff_price').html(((usdt.prices.app - usdt.prices.binance) * -1).toFixed(2));
            $('[coin="okex"]').find('.diff_price').html(((usdt.prices.app - usdt.prices.okex) * -1).toFixed(2));

            $('#sell_number').on('keyup', function () {
                var v = parseFloat($(this).val());
                var r = (v * usdt.return_usdt / 100).toFixed(2);
                if (isNaN(r)) {
                    r = 0;
                }
                $('#return_usdt_balance').html(r);
            })
        })
    </script>





    <style>
        .pop-cpt {
            z-index: 999999999;
            position: relative;
        }

        .pop-cpt2 {
            display: flex;
            justify-content: center;
            align-items: center;
            position: fixed;
            left: 0;
            top: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,.7);
        }

        .pop-cpt a {
            text-decoration: none;
            color: inherit;
            display: block;
        }

            .pop-cpt a:hover {
                text-decoration: none;
            }

        .pop-cpt .pop-cpt-bd {
            line-height: 22px;
            width: 286px;
            padding: 0 5px;
            box-sizing: border-box;
            border-radius: 15px;
            background: #0082f3;
            font-size: 14px;
            color: #333;
            font-family: "微软雅黑";
            position: relative;
        }

        .pop-cpt .pop-cpt-close {
            display: inline-block;
            height: 36px;
            line-height: 36px;
            padding: 0 15px;
            color: #fff;
            position: absolute;
            top: 0;
            right: 0;
        }

        .pop-cpt .pop-cpt-tit {
            height: 36px;
            line-height: 36px;
            color: #fff;
            text-align: center;
            font-size: 18px;
            font-weight: bold;
        }

            .pop-cpt .pop-cpt-tit span {
                display: inline-block;
                padding: 0 10px;
                position: absolute;
                top: 0;
                right: 0;
            }

        .pop-cpt .pop-cpt-con {
            padding: 0 6px 3px 6px;
            border-radius: 15px;
            background: #fff;
        }

        .pop-cpt .pop-cpt-con1 {
            text-align: center;
            color: #000;
        }

        .pop-cpt .pop-cpt-con2 {
            padding-top: 10px;
        }

        .pop-cpt .pop-cpt-con2-tit {
            display: flex;
            justify-content: center;
            padding-bottom: 5px;
        }

            .pop-cpt .pop-cpt-con2-tit span {
                display: inline-block;
                width: 26px;
                line-height: 26px;
                margin-left: -3px;
                text-align: center;
                color: #FFF200;
                font-size: 16px;
                font-weight: bold;
                border-radius: 50%;
                background: #ED1B20;
            }

        .pop-cpt .pop-cpt-con2-logo {
            display: flex;
            justify-content: center;
            height: 60px;
        }

        .pop-cpt .pop-cpt-con2-logol {
            color: #fb2725;
            font-size: 54px;
            line-height: 50px;
            font-weight: bold;
            font-family: Tahoma;
        }

        .pop-cpt .pop-cpt-con2-logor {
            width: 70px;
            position: relative;
        }

        .pop-cpt .pop-cpt-con2-logor-txt1 {
        }

            .pop-cpt .pop-cpt-con2-logor-txt1 span {
                display: inline-block;
                width: 60px;
                height: 22px;
                line-height: 22px;
                text-align: center;
                color: #fff;
                border-radius: 5px;
                background: #0082f3;
                position: absolute;
                left: 8px;
                top: 2px;
            }

        .pop-cpt .pop-cpt-con2-logor-txt2 {
            line-height: 32px;
            color: #333;
            font-size: 30px;
            font-family: impact,arial;
            position: absolute;
            top: 24px;
            left: 0;
        }

        .pop-cpt .pop-cpt-con3 {
            padding: 0 2px;
        }

            .pop-cpt .pop-cpt-con3 table {
                font-weight: bold;
                border-collapse: collapse;
            }

            .pop-cpt .pop-cpt-con3 th {
                text-align: center;
                color: #fff;
                border: solid 1px #999;
                background: #0082f3;
                font-size: 18px;
                font-weight: 500;
                padding: 5px 0;
            }

            .pop-cpt .pop-cpt-con3 td {
                text-align: center;
                border: solid 1px #999;
                font-size: 16px;
                line-height: 22px;
                padding: 5px 0;
            }

        .pop-cpt .pop-cpt-con4 {
            text-align: center;
            margin-bottom: 10px;
        }

            .pop-cpt .pop-cpt-con4 span {
                display: inline-block;
                width: 200px;
                height: 30px;
                line-height: 30px;
                font-size: 20px;
                font-weight: bold;
                color: #ff0;
                border-radius: 10px;
                background: #fb2725;
            }

        .pop-cpt .pop-cpt-footer {
            padding: 10px 0;
            color: #fff;
            text-align: center;
        }

        .pop-cpt .pop-cpt-footer1 {
            font-size: 16px;
        }

        .pop-cpt .pop-cpt-footer2 {
            padding-top: 5px;
            font-weight: bold;
            color: #ff0;
        }
    </style>
    <%--<div onclick="$(this).hide()" id="pop-cpt" class="pop-cpt">
        <div class="pop-cpt2">
            <div class="pop-cpt-bd">
                <span class="pop-cpt-close" onclick="closePop()">x 关闭</span><a href="chat.aspx" target="_blank"><div class="pop-cpt-tit"></div>
                    <div class="pop-cpt-con">
                        <div class="pop-cpt-con2">
                            <div class="pop-cpt-con2-tit">
                                <span>红</span><span>包</span><span>金</span><span>额</span><span>每</span><span>包</span><span style="width: 39px; border-radius: 12px; margin-left: -3px;">500</span><span>元</span>
                            </div>

                        </div>
                        <div class="pop-cpt-con3" style="margin: 10px 0;">
                            <table width="100%" border="1">
                                <tbody>
                                    <tr class="firstRow">
                                        <th colspan="2">定个闹钟准点抢红包</th>
                                    </tr>
                                    <%
                                        string[] rblist = uConfig.stcdata("redbag_boxtip").Split('\n');


                                        for (int i = 0; i < rblist.Length; i++)
                                        {
                                            string[] r2 = rblist[i].Split(new string[] { "||" }, StringSplitOptions.None);

                                            if (r2.Length != 2)
                                            {
                                                continue;
                                            }
                                    %>

                                    <tr>
                                        <td><%=r2[0] %></td>
                                        <td><%=r2[1] %></td>
                                    </tr>

                                    <%
                                        }
                                        
                                    %>
                                </tbody>
                            </table>
                        </div>
                        <div class="pop-cpt-con4"><span style="">点击进入抢红包</span></div>
                    </div>
                    <div class="pop-cpt-footer">
                    </div>
                </a>
            </div>
        </div>
    </div>--%>
</asp:Content>


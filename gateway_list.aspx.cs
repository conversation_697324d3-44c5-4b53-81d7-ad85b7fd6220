using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using LitJson;

public partial class order_list : globalClass
{
    public DataTable userdt = new DataTable();
    public Dictionary<string, object> pmlist = new Dictionary<string, object>();
    protected void Page_Load(object sender, EventArgs e)
    {
        Dictionary<string, object> dic = new Dictionary<string, object>();
        Dictionary<string, object> temp_dic = new Dictionary<string, object>();
        List<Dictionary<string, object>> list = new List<Dictionary<string, object>>();
        List<SqlParameter> pams = new List<SqlParameter>();
        dbClass db = new dbClass();
        DataTable dt = new DataTable();
        DataTable tempdt = new DataTable();
        pams.Add(new SqlParameter("@userid", uConfig.p_uid));
        string sql = "";

        sql = @"
select * from accounts with(nolock) where id=@userid

-- 通道付款成功单量
select gateway_network,count(0) as number from [api_orderList] with(nolock) where userid=@userid and state=1 group by gateway_network

";
        DataSet ds = db.getDataSet(sql, pams.ToArray());


        tempdt = ds.Tables[1];
        //pmlist["paidinfo"] = ToJson(dt);

        dt = chelper.gdt("channel_list");
        for (int i = 0; i < dt.Rows.Count; i++)
        {
            dic = new Dictionary<string, object>();
            dic.Add("paytype", dt.Rows[i]["paytype"] + "");
            dic.Add("rechargeNumber", dt.Rows[i]["rechargeNumber"] + "");
            list.Add(dic);
        }
        pmlist["channel_data"] = JsonMapper.ToJson(list);


        var pwLists = new string[] { "小额充值", "收银台", "支付宝转账", "OKPAY", "GOPAY", "KDPAY" };


        dic = new Dictionary<string, object>();
        for (int i = 0; i < pwLists.Length; i++)
        {


            temp_dic = new Dictionary<string, object>();
            temp_dic.Add("number", uConfig.stcdata(pwLists[i] + "_num"));
            temp_dic.Add("reward", uConfig.stcdata(pwLists[i] + "_rw"));


            if (IsNumeric(temp_dic["number"] + ""))
            {

                dt = selectDateTable(tempdt, "gateway_network='" + pwLists[i] + "'");
                if (dt.Rows.Count > 0)
                {
                    if (Convert.ToInt64(dt.Rows[0]["number"] + "") >= Convert.ToInt64(temp_dic["number"] + ""))
                    {
                        continue;
                    }
                }
            }

            dic.Add(pwLists[i], temp_dic);
        }
        pmlist["payway_lists"] = JsonMapper.ToJson(dic);

        userdt = ds.Tables[0];
    }
}
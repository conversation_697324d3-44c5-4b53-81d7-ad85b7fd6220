<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Fixed Button with Circular Menu</title>
    <style>
        /*导航图标*/
        .NMH-g-navicon {
            position: fixed;
            top: 40%;
            right: 020px;
            width: 100px;
            height: 100px;
        }

            .NMH-g-navicon.Jnmh-onleft {
                right: auto;
                left: 020px;
            }
            /*导航图标logo按钮*/
            .NMH-g-navicon .Jnmh-btnlogo {
                position: absolute;
                display: block;
                /*width: 100px;
                height: 100px;*/
                top: 50%;
                right: 0;
                /*margin-top: -50px;*/
                border: 0;
                background: #fff;
                background-size: 95% 95%;
                border: 1px solid rgba(0, 0, 0, 0.1);
                box-shadow: rgba(0, 0, 0, 0.12) 0px 6px 10px 0px;
                outline: none;
                border-radius: 50%;
                z-index: 1;
                width: 40px;
                height: 40px;
                margin-top: -61px;
            }

        .Jnmh-btnlogo-def {
            opacity: 0.9;
            color: #fff;
            font-size: 10px;
            background: rgba(216, 52, 66, .8)!important;
            border: 4px solid rgba(245, 113, 12)!important;
        }

        .NMH-g-navicon .Jnmh-btnlogohover {
            position: absolute;
            display: block;
            width: 100px;
            height: 100px;
            top: 50%;
            right: 0;
            margin: 0;
            padding: 0;
            margin-top: -50px;
            border: 0;
            overflow: hidden;
            /*background-color: red;*/
        }

        /*导航图标logo按钮-鼠标经过*/
        .NMH-g-navicon.Jnmh-open .Jnmh-btnlogohover {
            margin-top: -150px;
            width: 200px;
            height: 300px;
            border-radius: 150px 0 0 150px;
        }

        .NMH-g-navicon.Jnmh-onleft .Jnmh-btnlogohover {
            left: 0;
            right: auto;
            border-radius: 0 150px 150px 0;
        }
        /*导航图标菜单子容器*/
        .NMH-g-navicon .Jnmh-m-submenu {
            position: absolute;
            background-color: transparent;
            list-style: none;
            /*top: -020px;
                bottom: -020px;
                left: -020px;
                right: -020px;*/
            margin: 0;
            padding: 0;
            top: -17px;
            bottom: 62px;
            left: -10px;
            right: -68px;
        }

            .NMH-g-navicon .Jnmh-m-submenu .Jnmh-subli {
                position: absolute;
                width: 100%;
                height: 100%;
                transform: rotate(0deg);
                -webkit-transform: rotate(0deg);
                transition: all 0.1s ease-in-out;
            }

        .Jnmh-m-submenu .Jnmh-subdl {
            position: absolute;
            left: 50%;
            bottom: 100%;
            width: 0;
            height: 0;
            line-height: 1px;
            margin-left: 0;
            background: #fff;
            border-radius: 50%;
            text-align: center;
            font-size: 1px;
            overflow: hidden;
            cursor: pointer;
            box-shadow: none;
            transition: all 0.1s ease-in-out, color 0.1s, background 0.1s;
        }
        /*导航图标-展开菜单时*/
        .NMH-g-navicon.Jnmh-open .Jnmh-m-submenu .Jnmh-subdl {
            /*width: 80px;
            height: 80px;
            line-height: 80px;*/
            /*margin-left: -40px;*/
            box-shadow: 0 3px 3px rgba(0, 0, 0, 0.1);
            font-size: 14px;
            width: 40px;
            height: 40px;
            line-height: 40px;
            background: rgba(198, 44, 65, .9);
            color: #fff;
            margin-left: -21px;
        }
        /*导航图标-三级菜单容器*/
        .NMH-g-navicon.Jnmh-open .Jnmh-m-submenu .Jnmh-subdd {
            position: absolute;
            line-height: normal;
        }
    </style>
</head>
<body>
    <div id="nmh-navicon" class="NMH-g-plugin NMH-g-navicon">
        <div style="background: #c9c9c9; height: 200px; width: 200px; border-radius: 50%; position: relative; top: -87px; right: 18px; opacity: 0.5; display: none;" id="Jnmh-shadow"></div>
        <button class="Jnmh-btnlogo Jnmh-btnlogo-def ">助手</button>
        <ins class="Jnmh-btnlogohover"></ins>
        <ul class="Jnmh-m-submenu">
            <li class="Jnmh-subli">
                <dl class="Jnmh-subdl">
                    <dt class="NMH-subdt">电商平台</dt>
                    <dd class="NMH-subdd"></dd>
                </dl>
            </li>
            <li class="Jnmh-subli">
                <dl class="Jnmh-subdl">
                    <dt class="NMH-subdt">选品平台</dt>
                    <dd class="NMH-subdd"></dd>
                </dl>
            </li>
            <li class="Jnmh-subli">
                <dl class="Jnmh-subdl">
                    <dt class="NMH-subdt">会员升级</dt>
                    <dd class="NMH-subdd"></dd>
                </dl>
            </li>
            <li class="Jnmh-subli">
                <dl class="Jnmh-subdl">
                    <dt class="NMH-subdt">产品操作</dt>
                    <dd class="NMH-subdd"></dd>
                </dl>
            </li>
            <li class="Jnmh-subli">
                <dl class="Jnmh-subdl">
                    <dt class="NMH-subdt">个人中心</dt>
                    <dd class="NMH-subdd"></dd>
                </dl>
            </li>
        </ul>
    </div>
    <script type="text/javascript" src="https://apps.bdimg.com/libs/jquery/2.1.4/jquery.min.js"></script>
    <script type="text/javascript">
        //// 监听鼠标移入logo事件
        //$(document).on('mouseenter', '.Jnmh-btnlogo', function () {
        //    $('#nmh-navicon').addClass('Jnmh-open');
        //    GtoggleNavlogo();
        //});
        //// 监听鼠标移除导航球移除事件（展开收缩悬浮球为什么不直接监听#nmh-navicon而多了一步监听logo是为了减少边缘触发）
        //$(document).on('mouseleave', '#nmh-navicon', function () {
        //    $('#nmh-navicon').removeClass('Jnmh-open');
        //    GtoggleNavlogo();
        //});

        $('#nmh-navicon').on('click', function () {
            $('#nmh-navicon').toggleClass('Jnmh-open');
            GtoggleNavlogo();
        })

        var GtoggleNavlogo = function () {
            var li = $('#nmh-navicon').find('.Jnmh-subli');
            var lilen = li.length;
            var avgDeg = 180 / (lilen - 1);// 平均角度
            var initDeg = 0;// 起始方向角度
            if ($('#nmh-navicon').hasClass('Jnmh-onleft')) {
                // 如果悬浮球被拖拽到左边，则二级菜单则显示右侧
                li.css({ transform: 'rotate(0deg)' });
                initDeg = 180;
            } else {
                // 默认悬浮球在右边，二级菜单显示在左侧
                li.css({ transform: 'rotate(-360deg)' });
            }
            for (var i = 0, j = lilen - 1; i < lilen; i++, j--) {
                var d = initDeg - (i * avgDeg);
                var z = initDeg ? j : i;
                // console.log(d);
                $('#nmh-navicon').hasClass('Jnmh-open') ? GrotateNavlogo(li[z], d) : GrotateNavlogo(li[z], 0);
            }
            
            if ($('#nmh-navicon').hasClass('Jnmh-open')) {
                $('#Jnmh-shadow').show();
                $('.Jnmh-btnlogo').removeClass("Jnmh-btnlogo-def");
                $('.Jnmh-btnlogo').html('<svg t="" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="" width="22" height="22"><path d="M962.56 926.72L586.845091 552.96l375.528727-373.666909c21.224727-21.224727 21.224727-55.668364 0-74.193455-21.131636-21.224727-55.482182-21.224727-74.007273 0L512.930909 478.766545 140.008727 102.4a59.764364 59.764364 0 0 0-76.706909 0c-21.131636 18.618182-18.525091 55.668364 0 76.893091L438.830545 552.96 63.301818 926.72c-21.131636 21.131636-21.131636 55.575273 0 74.193455 21.224727 21.131636 55.575273 21.131636 74.100364 0L512.930909 627.153455l372.922182 376.366545c21.131636 21.224727 55.482182 21.224727 74.007273 0a57.157818 57.157818 0 0 0 2.606545-76.8z" fill="#B7B7B7" p-id="6379"></path></svg>');
            } else {
                $('#Jnmh-shadow').hide()
                $('.Jnmh-btnlogo').addClass("Jnmh-btnlogo-def");
                $('.Jnmh-btnlogo').text('助手');
            }


        };
        var GrotateNavlogo = function (dom, deg) {
            $({ a: 0 }).animate({ a: deg }, {
                step: function (now, fx) {
                    $(dom).css({ transform: 'rotate(' + now + 'deg)' });
                    $(dom).children().css({ transform: 'rotate(' + (-now) + 'deg)' });
                }, duration: 0
            });
        }

        //// 鼠标拖动logo移动
        //$(document).on('mousedown', '.Jnmh-btnlogo', function (e_down) {
        //    var wrap = $('#nmh-navicon');
        //    wrap.removeClass('Jnmh-open');
        //    $('.Jnmh-m-submenu').hide();
        //    GtoggleNavlogo();

        //    var positionDiv = wrap.offset();
        //    var distenceX = e_down.pageX - positionDiv.left;
        //    var distenceY = e_down.pageY - positionDiv.top + $(document).scrollTop();
        //    $(document).mousemove(diy_move);
        //    function diy_move(e_move) {
        //        var x = e_move.pageX - distenceX;
        //        var y = e_move.pageY - distenceY;

        //        if (x < 0) {
        //            x = 0;
        //        } else if (x > $(document).width() - wrap.outerWidth(true)) {
        //            x = $(document).width() - wrap.outerWidth(true);
        //        }

        //        if (y < 0) {
        //            y = 0;
        //        } else if (y > $(window).height() - wrap.outerHeight(true)) {
        //            y = $(window).height() - wrap.outerHeight(true);
        //        }

        //        $(wrap).css({
        //            'left': x + 'px',
        //            'top': y + 'px'
        //        });
        //    }
        //    $(document).mouseup(function () {
        //        var x = $(wrap).offset().left;
        //        var rm = '', ad = 'Jnmh-open';
        //        if (x > $(document).width() / 2) {
        //            x = $(document).width() - wrap.outerWidth(true) - 10;
        //            rm = 'Jnmh-onleft';
        //        } else {
        //            x = 10;
        //            ad += ' Jnmh-onleft';
        //        }
        //        $(wrap).css({ left: x + 'px' }).addClass(ad).removeClass(rm);
        //        $('.Jnmh-m-submenu').show();
        //        GtoggleNavlogo();
        //        $(document).unbind('mousemove', diy_move);
        //    });

        //});

    </script>
</body>
</html>

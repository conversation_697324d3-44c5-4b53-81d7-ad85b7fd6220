using com.cotees;
using LitJson;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.IO;
using System.Linq;
using System.Text;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Xml;
using Newtonsoft.Json;
using LitJson;

public partial class Api_admin : globalClass
{
    fuzhu fz = new fuzhu(HttpContext.Current);
    protected void Page_Load(object sender, EventArgs e)
    {
        string postContent = string.Empty;
        using (StreamReader reader = new StreamReader(Request.InputStream, Encoding.UTF8))
        {
            postContent = reader.ReadToEnd();
            // 处理 postContent
        }

        log.WriteLog("api_payCallback", "Content", postContent);

        string response_text = "ok";

        Dictionary<string, object> dic = new Dictionary<string, object>();

        dic.Add("code", 1000);
        dic.Add("msg", "检测无效");

        response_text = JsonMapper.ToJson(dic);


        Response.ContentType = "application/json";
        Response.Write(response_text);
        Response.End();
    }
}
using com.cotees;
using LitJson;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.IO;
using System.Linq;
using System.Text;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Xml;
using Newtonsoft.Json;
using LitJson;

public partial class Api_admin : globalClass
{
    fuzhu fz = new fuzhu(HttpContext.Current);
    protected void Page_Load(object sender, EventArgs e)
    {
        string postContent = string.Empty;
        using (StreamReader reader = new StreamReader(Request.InputStream, Encoding.UTF8))
        {
            postContent = reader.ReadToEnd();
            // 处理 postContent
        }

        log.WriteLog("api_NoitfyLogs", "Content", Request.QueryString.ToString() + "|" + postContent, true);

        string jsondata = string.Empty;
        JsonData jd = null;

        bool islocal = Request.Url.Host == "localhost";

        bool isJson = false;
        try
        {
            jd = JsonMapper.ToObject(postContent);
            isJson = true;
        }
        catch (Exception)
        {

        }

        if (fz.req("test") == "1")
        {
            fz.sendResponse("测试：" + fz.req("userCode") + "||" + postContent);
        }

        //fz.jsonResponse("JSON=" + isJson + ";" + jd["appKey"]);

        string sql = string.Empty;
        dbClass db = new dbClass();
        DataTable dt = new DataTable();
        List<SqlParameter> pams = new List<SqlParameter>();
        Dictionary<string, object> signMaps = new Dictionary<string, object>();
        Dictionary<string, object> dic = new Dictionary<string, object>();
        Dictionary<string, object> pmlist = new Dictionary<string, object>();
        string _sign = string.Empty;
        string[] g;

        string response_text = string.Empty;

        string gateway_network = "无";
        string orderId = string.Empty;
        string mchId = string.Empty;
        string api_apikey = string.Empty;
        string out_order_no = string.Empty;



        try
        {
            mchId = jd["recvid"] + "";
            orderId = jd["orderid"] + "";
            if (mchId == uConfig.stcdata("okpay_merchantid"))
            {
                gateway_network = "OKPAY";
                api_apikey = uConfig.stcdata("okpay_apikey");
            }
            else
            {
                gateway_network = "GOPAY";
                api_apikey = uConfig.stcdata("api_apikey");
            }
        }
        catch (Exception)
        {
        }

        try
        {
            out_order_no = jd["order_no"] + "";
            orderId = jd["user_order_no"] + "";
            gateway_network = "收银台";
            api_apikey = uConfig.stcdata("uy_apikey");
        }
        catch (Exception)
        {
        }


        try
        {
            if (fz.req("orderNo") != "")
            {
                orderId = fz.req("orderNo");
                gateway_network = "支付宝二维码";
                api_apikey = uConfig.stcdata("yao_apikey");
            }
        }
        catch (Exception)
        {
        }



        try
        {
            if (!fz.empty("pid") && fz.req("pid") == uConfig.stcdata("yzf_merchantid"))
            {
                out_order_no = fz.req("trade_no");
                orderId = fz.req("out_trade_no");
                gateway_network = "小额支付宝充值";
                api_apikey = uConfig.stcdata("yzf_apikey");
            }
        }
        catch (Exception)
        {
        }


        try
        {
            if (jd["data"]["appKey"] + "" == uConfig.stcdata("abpay_merchantid"))
            {
                out_order_no = jd["data"]["recordId"] + "";
                orderId = jd["data"]["busRecordId"] + "";
                gateway_network = "ABPAY";
                api_apikey = uConfig.stcdata("abpay_apikey");
            }
        }
        catch (Exception)
        {
        }

        try
        {
            if (jd["appKey"] + "" == uConfig.stcdata("abpay_merchantid"))
            {
                out_order_no = jd["recordId"] + "";
                gateway_network = "ABPAY_CREATE";
                api_apikey = uConfig.stcdata("abpay_apikey");
            }
        }
        catch (Exception)
        {
        }



        try
        {
            if (!fz.empty("mchid") && fz.req("mchid") == uConfig.stcdata("mypay_merchantid"))
            {
                out_order_no = fz.req("transaction_id");
                orderId = fz.req("out_trade_no");
                gateway_network = "小额充值";
                api_apikey = uConfig.stcdata("mypay_apikey");
            }
        }
        catch (Exception)
        {
        }

        try
        {
            if (!fz.empty("userCode") && fz.req("userCode") == uConfig.stcdata("kdpay_merchantid"))
            {
                orderId = fz.req("orderCode");
                gateway_network = "KDPAY";
                api_apikey = uConfig.stcdata("kdpay_apikey");
            }
        }
        catch (Exception)
        {
        }


        string serverUrl = string.Empty;
        try
        {
            serverUrl = uConfig.stcdata("serv_domains").Split('\n')[0];
        }
        catch (Exception)
        {
        }

        if (!string.IsNullOrEmpty(serverUrl))
        {
            serverUrl = "https://" + serverUrl;
        }


        //检查是否建单接口
        switch (gateway_network)
        {
            case "ABPAY_CREATE":

                g = (jd["userName"] + "").Split('_');
                pmlist["uid"] = "-1";
                if (g.Length >= 2)
                {
                    pmlist["uid"] = g[1];
                }

                sql = @"   

        -- 生成订单号 
        DECLARE @new_orderId VARCHAR(38);
        EXEC GenerateUniqueApiOrderId @GeneratedCode = @new_orderId OUTPUT;

        insert into api_orderList values(@temp_uid,@gateway_network,@cid,@new_orderId,null,@amount,0,getdate(),null)

select '获取订单成功' as errmsg,@new_orderId as orderid,scope_identity() as id,@amount as amount

";
                dt = db.getDataTable(sql, new SqlParameter[]{
                    new SqlParameter("@temp_uid",pmlist["uid"]+""),
                    new SqlParameter("@gateway_network",gateway_network.Replace("_CREATE","")),
                    new SqlParameter("@cid","CREATE01"),
                    new SqlParameter("@amount",jd["amount"] + "")
                });

                if (dt.Rows.Count == 0)
                {
                    response_text = "订单请求错误";
                }
                else
                {
                    response_text = dt.Rows[0]["errmsg"] + "";
                    if (response_text == "获取订单成功")
                    {
                        response_text = "";
                    }
                }

                dic = new Dictionary<string, object>();
                if (response_text == "")
                {

                    dic.Add("appKey", uConfig.stcdata("abpay_merchantid"));
                    dic.Add("busRecordId", dt.Rows[0]["orderid"] + "");
                    dic.Add("userName", jd["userName"] + "");
                    dic.Add("payAddress", jd["payAddress"] + "");
                    dic.Add("notifyUrl", serverUrl + "/api/gateway_transaction.aspx");
                    dic.Add("timestamp", Convert.ToInt64((DateTime.UtcNow - new DateTime(1970, 1, 1, 0, 0, 0, 0)).TotalMilliseconds));
                    dic.Add("amount", jd["amount"] + "");


                    pmlist["signstr"] = GetSignParams(dic, false, fz.req("gateway_network")) + uConfig.stcdata("abpay_reckey");
                    pmlist["sign"] = md5(pmlist["signstr"] + "").ToLower();

                    log.WriteLog("sign_Logs", gateway_network + "[建单]", pmlist["signstr"] + "||sign=" + pmlist["sign"]);

                    dic.Add("signature", pmlist["sign"] + "");

                }
                else
                {
                    dic.Add("code", "-1");
                    dic.Add("errmsg", response_text);
                }
                response_text = JsonMapper.ToJson(dic);



                log.WriteLog("api_CreateOrder", "建单结果", "network:" + gateway_network + ",out_order_no:" + out_order_no + " [" + response_text + "] ", true);

                fz.jsonResponse(response_text);
                break;
            default:
                break;
        }


        string payState = "0";
        switch (gateway_network)
        {
            case "GOPAY":
            case "OKPAY":
                //验证sign
                if (!islocal)
                {
                    if (jd["retsign"] + "" != md5(jd["sign"] + "" + api_apikey))
                    {
                        response_text = "Sign验证错误|retsign=" + md5(jd["sign"] + "|" + md5(jd["sign"] + "" + api_apikey));
                    }
                }
                payState = jd["state"] + "" == "4" ? "1" : "0";
                break;
            case "收银台":
                payState = jd["status"] + "" == "success" ? "1" : "0";
                break;
            case "支付宝二维码":
                payState = fz.req("retcode") + "" == "0000" ? "1" : "0";
                break;
            case "小额支付宝充值":
                payState = fz.req("trade_status") + "" == "TRADE_SUCCESS" ? "1" : "0";

                signMaps.Add("pid", fz.req("pid"));
                signMaps.Add("trade_no", fz.req("trade_no"));
                signMaps.Add("out_trade_no", fz.req("out_trade_no"));
                signMaps.Add("type", fz.req("type"));
                signMaps.Add("name", fz.req("name"));
                signMaps.Add("money", fz.req("money"));
                signMaps.Add("trade_status", fz.req("trade_status"));

                _sign = md5(GetSignParams(signMaps) + uConfig.stcdata("yzf_apikey"));
                if (_sign != fz.req("sign"))
                {
                    response_text = "Sign验证错误|retsign=" + fz.req("sign") + "|" + _sign;
                }
                log.WriteLog("api_signVerify", gateway_network, (_sign == fz.req("sign") ? "SUCCESS" : "FAIL") + " " + fz.req("sign") + "|" + _sign);
                break;
            case "ABPAY":
                payState = jd["data"]["status"] + "" == "2" && jd["data"]["busTransType"] + "" == "1" ? "1" : "0";
                break;
            case "小额充值":
                payState = fz.req("refCode") == "2" ? "1" : "0";
                break;
            case "KDPAY":
                payState = fz.req("status") == "3" ? "1" : "0";
                break;
            //case "波币":
            //    //验证sign

            //    if (fz.req("sign") + "" != md5("cp_order_id=" + fz.req("cp_order_id") + "&mch_id=" + fz.req("mch_id") + "&money=" + fz.req("money") + "&status=" + fz.req("status") + "&pri_key=" + uConfig.stcdata("bobi_apikey")))
            //    {
            //        response_text = "Sign验证错误|retsign=" + fz.req("sign") + "|" + md5("cp_order_id=" + fz.req("cp_order_id") + "&mch_id=" + fz.req("mch_id") + "&money=" + fz.req("money") + "&status=" + fz.req("status") + "&pri_key=" + uConfig.stcdata("bobi_apikey"));
            //    }
            //    payState = fz.req("status");

            //    break;
            //case "美宜佳":
            //case "玖玖":
            //    //验证sign

            //    //if (fz.req("sign") + "" != md5("cp_order_id=" + fz.req("cp_order_id") + "&mch_id=" + fz.req("mch_id") + "&money=" + fz.req("money") + "&status=" + fz.req("status") + "&pri_key=" + uConfig.stcdata("bobi_apikey")))
            //    //{
            //    //    response_text = "Sign验证错误|retsign=" + fz.req("sign") + "|" + md5("cp_order_id=" + fz.req("cp_order_id") + "&mch_id=" + fz.req("mch_id") + "&money=" + fz.req("money") + "&status=" + fz.req("status") + "&pri_key=" + uConfig.stcdata("bobi_apikey"));
            //    //}

            //    payState = jd["state"] + "";

            //    break;

            default:
                break;
        }

        if (payState == "1" && response_text == "")
        {
            try
            {

                string updateSQl = string.Empty;
                if (!string.IsNullOrEmpty(out_order_no))
                {
                    updateSQl = ",payid=@out_order_no";
                }

                pams = new List<SqlParameter>();

                pmlist["pay_reward"] = uConfig.stcdata(gateway_network + "_rw");
                if (pmlist["pay_reward"] == "" || !IsNumeric(pmlist["pay_reward"] + ""))
                {
                    pmlist["pay_reward"] = "0";
                }
                else
                {
                    updateSQl += ",pay_reward=@pay_reward";
                }

                pams.Add(new SqlParameter("@pay_reward", pmlist["pay_reward"] + ""));
                pams.Add(new SqlParameter("@out_order_no", out_order_no));
                pams.Add(new SqlParameter("@orderId", orderId));
                pams.Add(new SqlParameter("@gateway_network", gateway_network));
                sql = @"
declare @userid int 
declare @amount decimal(18,2) 
declare @order_gateway_network varchar(20)
update api_orderList set @order_gateway_network=gateway_network,@amount=amount,@userid=userid,state=1,pay_time=getdate()" + updateSQl + @" where orderId=@orderId  and state=0 


if(@userid is null)
begin
    select '订单不存在或已支付' as errmsg
    return
end

-- 支付充值
declare @current_amount decimal(18,6)
update accounts set amount=amount+@amount,@current_amount=amount+@amount where id=@userid 
if(@current_amount is null)
begin
    select '充值账户不存在' as errmsg
    return
end

insert into transaction_list values(@userid,'充值',@amount,@current_amount,@orderId,@gateway_network+',支付宝充值',1,'',GETDATE(),GETDATE())


if(@pay_reward>0)
begin
    -- 充值赠送
    declare @reward_amount decimal(18,2)
    set @reward_amount=@amount*@pay_reward/100

    set @current_amount=0
    update accounts set amount=amount+@reward_amount,@current_amount=amount+@reward_amount where id=@userid 
    insert into transaction_list values(@userid,'充值',@reward_amount,@current_amount,@orderId,@gateway_network+',充值赠送'+cast(@pay_reward as varchar(10))+'%',1,'',GETDATE(),GETDATE())
end


select '充值成功' as errmsg

";

                dt = db.getDataTable(sql, pams.ToArray());
            }
            catch (Exception ex)
            {
                response_text = ex.Message;
            }

            if (dt.Rows.Count > 0)
            {
                response_text = dt.Rows[0]["errmsg"] + "";
            }
            else
            {
                response_text = (response_text == "" ? "充值出错" : response_text);
            }
        }
        else
        {
            response_text = "交易未成功：" + postContent;
        }


        log.WriteLog("api_NoitfyLogs", "充值结果", "network:" + gateway_network + ",orderId:" + orderId + ",out_order_no:" + out_order_no + " [" + response_text + "] ", true);

        response_text = "SUCCESS";
        if (gateway_network == "uy" || gateway_network == "小额支付宝充值")
        {
            response_text = "success";
        }

        Response.Write(response_text);
        Response.End();
    }

    static string XmlToJson(string xmlString)
    {
        XmlDocument xmlDoc = new XmlDocument();
        xmlDoc.LoadXml(xmlString);

        string jsonString = JsonConvert.SerializeXmlNode(xmlDoc.DocumentElement, Newtonsoft.Json.Formatting.Indented);
        return jsonString;
    }



    public string GetSignParams(Dictionary<string, object> jsonDict, bool is_encode = false, string payType = "base")
    {
        // 按键升序排序
        var sortedDict = jsonDict.OrderBy(pair => pair.Key)
                                 .ToDictionary(pair => pair.Key, pair => pair.Value);

        // 构建参数字符串
        List<string> paramPairs = new List<string>();
        foreach (var pair in sortedDict)
        {
            var key = pair.Key;
            var value = pair.Value;
            if (is_encode)
            {
                value = Uri.EscapeDataString(value + "");
            }
            switch (payType)
            {
                case "ABPAY":
                    paramPairs.Add(key + value);
                    break;
                default:
                    paramPairs.Add(key + "=" + value);
                    break;
            }
        }

        string paramString = string.Empty;

        switch (payType)
        {
            case "ABPAY":
                paramString = string.Join("", paramPairs);
                break;
            default:
                paramString = string.Join("&", paramPairs);
                break;
        }


        return paramString;
    }
}
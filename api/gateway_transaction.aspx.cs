using com.cotees;
using LitJson;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.IO;
using System.Linq;
using System.Text;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Xml;
using Newtonsoft.Json;
using LitJson;

public partial class Api_admin : globalClass
{
    fuzhu fz = new fuzhu(HttpContext.Current);
    protected void Page_Load(object sender, EventArgs e)
    {
        string postContent = string.Empty;
        using (StreamReader reader = new StreamReader(Request.InputStream, Encoding.UTF8))
        {
            postContent = reader.ReadToEnd();
            // 处理 postContent
        }

        string serverUrl = string.Empty;
        try
        {
            serverUrl = uConfig.stcdata("serv_domains").Split('\n')[0];
        }
        catch (Exception)
        {
        }

        serverUrl = "https://" + serverUrl;

        string temp_url = serverUrl + "/api/gateway_09pay.aspx?" + Request.QueryString.ToString();

        log.WriteLog("api_transaction", "Content", Request.QueryString.ToString() + "|" + postContent + "|" + temp_url);
        string response_text = "";

        response_text = getContent(temp_url);

        Response.Write(response_text);
        Response.End();
    }

    static string XmlToJson(string xmlString)
    {
        XmlDocument xmlDoc = new XmlDocument();
        xmlDoc.LoadXml(xmlString);

        string jsonString = JsonConvert.SerializeXmlNode(xmlDoc.DocumentElement, Newtonsoft.Json.Formatting.Indented);
        return jsonString;
    }
}
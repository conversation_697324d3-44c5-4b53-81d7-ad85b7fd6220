using com.cotees;
using LitJson;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Text;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

public partial class Api_admin : globalClass
{
    fuzhu fz = new fuzhu(HttpContext.Current);
    protected void Page_Load(object sender, EventArgs e)
    {
        Response.ContentType = "application/json";
        string result = main();
        Response.Write(result);
        Response.End();
    }

    public string main()
    {

        string _result = "";

        //数据库相关参数
        string sql = string.Empty;
        dbClass db = new dbClass();
        DataTable dt = new DataTable();
        DataTable temp_dt = new DataTable();
        DataTable cache_dt = new DataTable();
        DataTable select_dt = new DataTable();
        DataRow[] rows;
        DataRow[] matchingRows;
        int updateRowIndex = 0;
        DataSet ds;
        DataRow[] dr;
        Dictionary<string, object> pmlist = new Dictionary<string, object>();
        Dictionary<string, object> dic = new Dictionary<string, object>();
        Dictionary<string, object> temp_dic = new Dictionary<string, object>();
        Dictionary<string, object> _data = new Dictionary<string, object>();
        List<Dictionary<string, object>> list = new List<Dictionary<string, object>>();

        //临时参数
        Random rd = new Random();
        string temp = string.Empty;
        string temp2 = string.Empty;
        string temp_val = string.Empty;
        bool result = false;
        int res = 0;
        string[] g;
        string[] g2;
        string[] arrayList;
        List<string> text_array = new List<string>();
        List<string> temp_array = new List<string>();
        List<string> sList = new List<string>();
        List<string> sList_refresh = new List<string>();
        List<string> sqls = new List<string>();
        JsonData jd;
        List<SqlParameter> pams = fz.collectReqParames(out result);

        int secondsDifference = 0;

        fz.setResponseLable("msg");


        switch (fz.req("apikey"))
        {
            case "apo_xr":
                break;
            default:
                fz.sendResponse("key error");
                break;
        }



        pmlist["userip"] = "";

        try
        {
            pmlist["userip"] = System.Web.HttpContext.Current.Request.ServerVariables["HTTP_X_TENANT_FORWARD_FOR"] + "";
        }
        catch (Exception)
        {
        }
        if (pmlist["userip"] + "" == "")
        {
            pmlist["userip"] = getUserIP();
        }

        if (Request.Url.Host != "localhost" && Request.Url.Host != "127.0.0.1")
        {
            g = uConfig.stcdata("serv_ips").Split('\n');
            if (!g.Contains(pmlist["userip"] + ""))
            {
                log.WriteLog("禁止访问", "ip", uConfig.p_idAD + "|" + pmlist["userip"]);
                fz.sendResponse("禁止访问：" + pmlist["userip"] + "(" + Request.Url.Host + ")");
            }
        }


        switch (fz.req("act"))
        {
            case "test989":
                fz.sendResponse("IP=" + HttpContext.Current.Request.Url.Host);
                break;
            case "lot":
                ds = open_redBag_Test(new Dictionary<string, object>{                                    
                    {"id", fz.req("id")},
                    {"userid", uConfig.p_uid}
                }, fz);


                dt = ds.Tables[0];
                if (dt.Rows.Count == 0)
                {
                    fz.sendResponse("红包参数错误");
                }

                temp = dt.Rows[0]["errmsg"] + "";
                if (temp != "SUCCESS")
                {
                    fz.sendResponse(temp);
                };



                dt = ds.Tables[2];
                //fz.sendResponse(ToJson(dt));
                for (int i = 0; i < dt.Rows.Count; i++)
                {
                    dic = new Dictionary<string, object>();
                    dic.Add("name", dt.Rows[i]["parent_code"] + "");
                    if ((dic["name"] + "").Length >= 7)
                    {
                        temp = (dic["name"] + "").Substring(0, 2);
                        temp2 = (dic["name"] + "").Substring((dic["name"] + "").Length - 2, 2);
                        dic["name"] = temp + "***" + temp2;
                    }

                    dic.Add("userid", dt.Rows[i]["userid"] + "");
                    dic.Add("amount", dt.Rows[i]["amount"] + "");
                    dic.Add("time", FormatDateTime(dt.Rows[i]["create_time"] + ""));
                    list.Add(dic);

                }
                dic = new Dictionary<string, object>();
                dic.Add("list", list);

                dt = ds.Tables[1];
                dic.Add("name", dt.Rows[0]["parent_code"] + "");
                if ((dic["name"] + "").Length >= 7 && (dic["name"] + "").IndexOf("em") == -1)
                {
                    temp = (dic["name"] + "").Substring(0, 2);
                    temp2 = (dic["name"] + "").Substring((dic["name"] + "").Length - 2, 2);
                    dic["name"] = temp + "***" + temp2;
                }

                dic.Add("text", dt.Rows[0]["text"] + "");
                dic.Add("amount", dt.Rows[0]["amount"]);
                dic.Add("receive_amount", dt.Rows[0]["amount_receive"]);
                dic.Add("user_amount_receive", dt.Rows[0]["user_amount_receive"]);


                dic.Add("number", dt.Rows[0]["number"]);
                dic.Add("receive_number", dt.Rows[0]["number_receive"]);

                fz.sendResponse("SUCCESS", 1, dic);
                break;
            case "edcz":

                pams.Add(new SqlParameter("@token", "管理员操作"));

                switch (fz.req("type"))
                {
                    case "kc":


                        sql = @" 
                                    
declare @total_amount decimal(18,2)
set @total_amount=cast(@amount as decimal(18,2))
declare @current_onetouch_balance decimal(18,2)
update accounts set @current_onetouch_balance=isnull(onetouch_balance,0)-@total_amount ,onetouch_balance=isnull(onetouch_balance,0)-@total_amount where id=@uid and isnull(onetouch_balance,0)-@total_amount>=0

if(@@rowcount=0)
begin
    select '账户额度不足' as errmsg
    return
end
insert into onetouch_balance_records values(@uid,'额度冲正',-1*@total_amount,@current_onetouch_balance,@token,getdate())

set @total_amount=-1*@total_amount
exec('[onetouch_balance_reset] '+@uid+','''+@token+'''')

select 'SUCCESS' as errmsg,@token as token

                                ";
                        break;
                    default:

                        sql = @"

declare @total_amount decimal(18,2)
set @total_amount=cast(@amount as decimal(18,2))
declare @current_onetouch_balance decimal(18,2)
update accounts set @current_onetouch_balance=isnull(onetouch_balance,0)+@total_amount ,onetouch_balance=isnull(onetouch_balance,0)+@total_amount where id=@uid

if(@current_onetouch_balance is null)
begin
    return
end

insert into onetouch_balance_records values(@uid,'额度冲正',@total_amount,@current_onetouch_balance,@token,getdate())

exec('[onetouch_balance_reset] '+@uid+','''+@token+'''')

                                ";
                        break;
                }




                res = db.ExecuteNonQuery(sql, pams.ToArray());
                fz.sendResponse("result=" + res);
                break;
            case "account_test":
                pmlist["openid"] = fz.req("openid");

                //查询账号是否存在
                dic["game_balance"] = "-";

                //pmlist["url"] = ga_req("public.gamesignup.do", "siteid=" + uConfig.stcdata("siteid") + "&sitecode=" + uConfig.stcdata("sitecode") + "&code=0&openid=" + pmlist["openid"] + "&token=0");


                pmlist["sign"] = md5UTF8("appid=" + uConfig.stcdata("g_api_appid") + "&code=0&openid=" + pmlist["openid"] + "&key=" + uConfig.stcdata("g_api_appkey"));

                pmlist["url"] = "?siteid=" + uConfig.stcdata("siteid") + "&sitecode=" + uConfig.stcdata("sitecode") + "&code=0&gamecode=0&openid=" + pmlist["openid"] + "&return_url=&sign=" + pmlist["sign"];
                pmlist["url"] = uConfig.stcdata("g_notify_gateway") + "/" + "public.gamesignup.do" + pmlist["url"];

                pmlist["text_response"] = getContent(pmlist["url"] + "");
                try
                {
                    jd = JsonMapper.ToObject(pmlist["text_response"] + "");
                    dic["game_balance"] = jd["data"]["balance"] + "";
                }
                catch (Exception)
                {
                }

                fz.jsonResponse("game_balance=" + dic["game_balance"] + ";data=" + pmlist["text_response"]);


                break;
            case "onetouchTransToGame":
                pmlist["num"] = fz.req("num");
                if (pmlist["num"] + "" == "")
                {
                    pmlist["num"] = "1";
                }
                if (!IsNumeric(pmlist["num"] + ""))
                {
                    fz.sendResponse("数量有误");
                }
                sql = " update authorize_list set remark=@remark output deleted.* where id in (select top " + pmlist["num"] + " id from [authorize_list] with(nolock) where isnull(from_type,'')='onetouch' and isnull(remark,'')<>@remark) ";
                dt = db.getDataTable(sql, pams.ToArray());

                pmlist["paht"] = "public.gamewithdraw.do";
                for (int i = 0; i < dt.Rows.Count; i++)
                {
                    pmlist["orderNo"] = fz.req("prenum") + Guid.NewGuid().ToString().Replace("-", "");
                    pmlist["openid"] = dt.Rows[i]["openid"] + "";
                    pmlist["userid"] = dt.Rows[i]["userid"] + "";



                    //查询账户余额

                    pmlist["url"] = ga_req("public.gamebalance.do", "code=0&openid=" + pmlist["openid"] + "&token=0");
                    pmlist["text_response"] = getContent(pmlist["url"] + "");

                    log.WriteLog("PlayApis_查询账户余额", "(请求)" + "public.gamebalance.do", pmlist["url"] + "----" + pmlist["text_response"] + "");

                    pmlist["game_balance"] = "-";
                    try
                    {
                        jd = JsonMapper.ToObject(pmlist["text_response"] + "");
                        pmlist["game_balance"] = jd["data"]["balance"] + "";
                    }
                    catch (Exception)
                    {
                    }


                    if (pmlist["game_balance"] + "" == "-")
                    {
                        log.WriteLog("PlayApis_查询账户余额失败", "public.gamebalance.do", "(" + pmlist["userid"] + "," + pmlist["openid"] + ")" + pmlist["url"] + "----" + pmlist["text_response"] + "");
                        continue;
                    }


                    //开始转出
                    pmlist["amount"] = pmlist["game_balance"] + "";//设置为全部余额转出

                    pmlist["url"] = ga_req(pmlist["paht"] + "", "code=0&openid=" + pmlist["openid"] + "&token=0&orderNo=" + pmlist["orderNo"] + "&orderAmt=" + pmlist["amount"]);
                    pmlist["text_response"] = getContent(pmlist["url"] + "");
                    log.WriteLog("PlayApis_自动化", "(请求)" + pmlist["paht"] + "", pmlist["url"] + "----" + pmlist["text_response"] + "");

                    pmlist["result_code"] = "0";
                    pmlist["result_msg"] = "操作异常，请联系客服";
                    try
                    {
                        jd = JsonMapper.ToObject(pmlist["text_response"] + "");
                        pmlist["result_code"] = jd["code"] + "";
                        pmlist["result_msg"] = jd["msg"] + "";
                    }
                    catch (Exception)
                    {
                    }

                    if (pmlist["result_code"] + "" != "0000")
                    {
                        log.WriteLog("PlayApis_自动化报错", pmlist["paht"] + "", "(" + pmlist["userid"] + "," + pmlist["openid"] + ")" + pmlist["url"] + "----" + pmlist["text_response"] + "");
                        continue;
                    }


                    pams = new List<SqlParameter>();
                    pams.Add(new SqlParameter("@amount", pmlist["amount"] + ""));
                    pams.Add(new SqlParameter("@userid", pmlist["userid"] + ""));
                    pams.Add(new SqlParameter("@token", pmlist["orderNo"] + ""));

                    sql = @"
declare @total_amount decimal(18,2)
set @total_amount=cast(@amount as decimal(18,2))
declare @current_onetouch_balance decimal(18,2)
update accounts set @current_onetouch_balance=isnull(onetouch_balance,0)+@total_amount ,onetouch_balance=isnull(onetouch_balance,0)+@total_amount where id=@userid
insert into onetouch_balance_records values(@userid,'游戏转出（系统）',@total_amount,@current_onetouch_balance,@token,getdate())

exec('[onetouch_balance_reset] '+@userid+','''+@token+'''')

                                ";

                    if (db.ExecuteNonQuery(sql, pams.ToArray()) > 0)
                    {
                        res += 1;
                    }
                    else
                    {

                    }
                }

                fz.sendResponse("已处理" + dt.Rows.Count + "条数据(成功" + res + "条)");

                break;
            case "tota":
                fz.check_exist(new string[] { "target" });
                if (Request.Url.Host != "localhost" && Request.Url.Host != "127.0.0.1")
                {
                    fz.sendResponse("非本地网络");
                }

                if (fz.req("phone").Length != 11)
                {
                    fz.sendResponse("手机号格式不正确");
                }

                pams.Add(new SqlParameter("@area", ip_area(pmlist["userip"] + "")));

                sql = @"
  declare @login_uid int
 update [accounts] set roomid=roomid, @login_uid=id  output deleted.* where phone=@phone
if(@login_uid is not null)
begin
    insert into user_logs values(@login_uid,'管理员登录','技术性登录',@ip,getdate())
end
";
                dt = db.getDataTable(sql, pams.ToArray());
                if (dt.Rows.Count > 0)
                {
                    log.WriteLog("账号登录", "", dt.Rows[0]["phone"] + "," + dt.Rows[0]["password"] + "|" + System.Web.HttpContext.Current.Request.ServerVariables["HTTP_X_TENANT_FORWARD_FOR"] + "|" + Request.UserAgent + "");
                    if (dt.Rows[0]["state"] + "" != "1")
                    {
                        fz.sendResponse("账号被禁止登录");
                    }

                    //newCookie(uConfig.loginParames_token, dt.Rows[0]["token"] + "");
                    //newCookie(uConfig.loginParames_uid, dt.Rows[0]["id"] + "");
                    //newCookie(uConfig.loginParames_usernick, dt.Rows[0]["phone"] + "");
                    //newCookie(uConfig.loginParames_pwd, dt.Rows[0]["password"] + "");
                    //newCookie(uConfig.loginParames_roomNumber, dt.Rows[0]["roomNumber"] + "");
                    //newCookie(uConfig.loginParames_tid, dt.Rows[0]["topid"] + "");
                    //newCookie(uConfig.loginParames_loginTime, getTimeStamp().ToString());
                    ////fz.sendResponse("登录成功", 1, dic);


                    dic = new Dictionary<string, object>();
                    dic.Add("token", dt.Rows[0]["token"] + "");
                    dic.Add("id", dt.Rows[0]["id"] + "");
                    dic.Add("phone", dt.Rows[0]["phone"] + "");
                    dic.Add("password", dt.Rows[0]["password"] + "");
                    dic.Add("roomNumber", dt.Rows[0]["roomNumber"] + "");
                    dic.Add("topid", dt.Rows[0]["topid"] + "");


                    pmlist["token"] = Guid.NewGuid().ToString().Replace("-", "");
                    cae.SetCache("login_token_" + pmlist["token"], JsonMapper.ToJson(dic),DateTime.Now.AddMinutes(1));

                    Response.Redirect(fz.req("target") + "/api/authorization.aspx?appid=38189923&token=" + pmlist["token"] + "");

                }
                else
                {
                    fz.sendResponse("账号不存在", -101);
                }

                break;
            default:
                fz.sendResponse("not found", 404);
                break;
        }
        return _result;
    }

    public string getPamsSql(List<SqlParameter> parameters)
    {
        string sql = string.Empty;
        // 遍历参数列表，构建 SQL 语句
        foreach (var parameter in parameters)
        {
            string sqlParameterDeclaration = "DECLARE " + parameter.ParameterName + " " + GetSqlType(parameter.SqlDbType);
            string sqlParameterValueAssignment = "SET " + parameter.ParameterName + " = '" + parameter.Value + "'";

            // 输出声明参数和赋值语句
            sql += sqlParameterDeclaration + @"
";
            sql += sqlParameterValueAssignment + @"
";
        }
        return sql;
    }

    // 获取 SqlParameter 的 SqlDbType 对应的 SQL 数据类型
    static string GetSqlType(SqlDbType sqlDbType)
    {
        switch (sqlDbType)
        {
            case SqlDbType.VarChar:
                return "VARCHAR(100)"; // 假设长度为 100
            case SqlDbType.Int:
                return "INT";
            case SqlDbType.Bit:
                return "BIT";
            case SqlDbType.NVarChar:
                return "VARCHAR(100)";
            // 其他数据类型自行添加
            default:
                throw new ArgumentException("Unsupported SqlDbType: " + sqlDbType);
        }
    }

    public DataSet open_redBag_Test(Dictionary<string, object> pmlist, fuzhu fz)
    {
        dbClass db = new dbClass();
        DataSet ds = new DataSet();
        string sql = string.Empty;
        List<SqlParameter> pams = new List<SqlParameter>();

        pams.Add(new SqlParameter("@id", pmlist["id"]));
        pams.Add(new SqlParameter("@userid", dic_param(pmlist, "userid", -1)));
        pams.Add(new SqlParameter("@fakeuser", dic_param(pmlist, "fakeuser", "")));
        pams.Add(new SqlParameter("@isfair", dic_param(pmlist, "isfair", 0)));


        pams.Add(new SqlParameter("@newusermoney_Min", uConfig.stcdata("newuser_money_limit").Split('~')[0]));//新人下限
        pams.Add(new SqlParameter("@newusermoney_Max", uConfig.stcdata("newuser_money_limit").Split('~')[1]));//新人上限

        pams.Add(new SqlParameter("@usermoney_Min", uConfig.stcdata("user_money_limit").Split('~')[0]));//用户下限
        pams.Add(new SqlParameter("@usermoney_Max", uConfig.stcdata("user_money_limit").Split('~')[1]));//用户上限

        pams.Add(new SqlParameter("@thmoney_Min", uConfig.stcdata("th_money_limit").Split('~')[0]));//托号下限
        pams.Add(new SqlParameter("@thmoney_Max", uConfig.stcdata("th_money_limit").Split('~')[1]));//托号上限

        pams.Add(new SqlParameter("@fakemoney_Max", dic_param(pmlist, "fakemoney_Max", -1)));//虚拟号上限
        pams.Add(new SqlParameter("@fakemoney_Min", dic_param(pmlist, "fakemoney_Min", -1)));//虚拟号下限


        pams.Add(new SqlParameter("@fake_redbag_minavg", uConfig.stcdata("fake_redbag_minavg")));//均摊最低值

        sql = @" 

declare @total_amount decimal(18,2)
declare @user_amount_receive decimal(18,2)
set @user_amount_receive=-1

declare @max_money decimal(18,2)
declare @min_money decimal(18,2)

set @max_money=0.01
set @min_money=0.01

if(@fakeuser='')
begin

    declare @record_list table(userid int,amount decimal(18,2),create_time datetime)

    insert into @record_list select userid,amount,create_time from redbag_records with(nolock) where tokenid=@id

    select @user_amount_receive=amount from @record_list where userid=@userid

end

declare @avgmoney_Max decimal(18,2)
declare @residue_number int
declare @residue_money decimal(18,2)
declare @redbag_userid int
declare @rid int
declare @userlist varchar(1000)
declare @finish_time datetime
select @residue_number=number-number_receive,@max_money=(case when number-number_receive>0 then (amount-amount_receive)/(number-number_receive)*2 else 0.01 end),@redbag_userid=userid,@userlist=userlist,@rid=id,@finish_time=finish_time from [redbag_list] with(nolock) where tokenid=@id

if(@rid is null)
begin
    select '红包不存在' as errmsg
    return
end
set @avgmoney_Max=@max_money


-- 查询是否在发送的对象中
declare @inUserList int
set @inUserList=1;
if(@userlist is not null)
begin
     if(CHARINDEX(','+cast(@userid as varchar(10))+',',','+@userlist+',')=0)
     begin
        set @inUserList=0
     end
end

declare @receive_rate int
set @receive_rate=100

declare @amount_receive decimal(18,2)
declare @amount_receive_later decimal(18,2)
declare @share_money decimal(18,2)
declare @over_money decimal(18,2)

declare @real_Min decimal(18,2)
declare @real_Max decimal(18,2)

set @share_money=0
set @over_money=0


if(@user_amount_receive=-1 and @inUserList=1 and @finish_time is null)
begin
    declare @usertype int
    declare @isth int
    declare @amount decimal(18,2)
    DECLARE @rlist TABLE (amount_receive decimal(18, 2));

    -- 查询是否为托号
    set @isth=0
    -- 查询是否为新人
    set @usertype=1

    select @usertype=isnull(usertype,1),@isth=(case when isnull(ag.name,'')='★托号' then 1 else 0 end) from accounts a with(nolock) left join account_groups ag with(nolock) on a.groupid=ag.id where a.id=@userid


    if(@usertype=2)
    begin
        -- 高级账号视为普通账号
        set @usertype=1
    end

    
    if(@redbag_userid=-1001)
    begin
        -- 红包雨（不采用通用规则，采用红包雨上下限匹配规则）
        set @avgmoney_Max=@usermoney_Max
        if(@isth=1)
        begin
            set @max_money=cast(@thmoney_Max as decimal(18,2))
            set @min_money=cast(@thmoney_Min as decimal(18,2))
        end
        else
        begin
            if(@usertype=1)
            begin
                -- 普通账号
                set @max_money=cast(@usermoney_Max as decimal(18,2))
                set @min_money=cast(@usermoney_Min as decimal(18,2))
            end
            else
            begin
                -- 新人账号
                set @max_money=cast(@newusermoney_Max as decimal(18,2))
                set @min_money=cast(@newusermoney_Min as decimal(18,2))
            end
        end
    end


    -- 虚号无视一切，直接采用虚号规则
    if(@fakeuser<>'')
    begin
        set @max_money=cast(@fakemoney_Max as decimal(18,2))
        set @min_money=cast(@fakemoney_Min as decimal(18,2))
    end


    

    update redbag_list set @residue_number=number-number_receive-1,@real_Min={红包领取下限},@real_Max={红包领取上限},@receive_rate=CAST((isnull(fake_number,0)+2)*1.0/number*1.0*100 as int),@rid=id,@total_amount=amount,@amount=amount,@amount_receive=amount_receive

,amount_receive=amount_receive+
(case when number-number_receive=1 then amount-amount_receive else CAST(RAND(CHECKSUM(NEWID())) * ({红包领取上限} - {红包领取下限}) + {红包领取下限} AS DECIMAL(18, 2)) end)

,number_receive=number_receive+1,finish_time=(case when number-number_receive-1>0 then null else getdate() end) OUTPUT INSERTED.amount_receive INTO @rlist where id=@rid and amount-amount_receive>0 and finish_time is null


    select @amount_receive_later=amount_receive from  @rlist


    if(@amount_receive_later is not null)
    begin


        set @user_amount_receive=@amount_receive_later-@amount_receive
        set @residue_money=@total_amount-@amount_receive_later

        if(@residue_number>0)
        begin            
            set @share_money=@residue_money/@residue_number
            set @over_money=@residue_money-(@residue_number*@avgmoney_Max)
        end
        else
        begin
            set @share_money=-1
        end

        
        if(@fakeuser<>'')
        begin
            update redbag_list set fake_amount=isnull(fake_amount,0)+@user_amount_receive,fake_number=isnull(fake_number,0)+1 where tokenid=@id
        end

        insert into redbag_records values(@userid,(case when @fakeuser='' then null else @fakeuser end),@id,@user_amount_receive,@total_amount-@amount_receive_later,@residue_number,@isfair,@over_money,cast(@real_Min as varchar(18))+'~'+cast(@real_Max as varchar(18)),getdate())


        if(@fakeuser='')
        begin

            declare @current_amount decimal(18,6)
            update accounts set amount=amount+@user_amount_receive,@current_amount=amount+@user_amount_receive where id=@userid 

            insert into transaction_list values(@userid,'转账',@user_amount_receive,@current_amount,@id,'红包收入',1,(case when @redbag_userid=-1001 then '红包雨' else '' end),GETDATE(),GETDATE())

        end


    end

end


select 'SUCCESS' as errmsg,@receive_rate as next_receive_rate,@user_amount_receive as user_amount_receive,@min_money as min_money,@max_money as max_money,@amount_receive as  amount_receive,@amount_receive_later as amount_receive_later,@real_Min as real_Min,@real_Max as real_Max,@residue_number as residue_number,@share_money as share_money,@redbag_userid as redbag_userid,@residue_money as residue_money,@over_money as over_money

if(@fakeuser='')
begin

    select rl.*,@user_amount_receive as user_amount_receive,(case when rl.userid=-1001 then '管理员发出' else u.parent_code end) as parent_code from [redbag_list] rl with(nolock) left join accounts u with(nolock) on rl.userid=u.id where tokenid=@id


    select rec.*,(case when rec.userid=-1 then rec.faker_user else u.parent_code end) as parent_code from redbag_records rec with(nolock) left join accounts u on rec.userid=u.id where tokenid=@id order by rec.id

end




";

        //上限目前仅对“红包雨”所有用户有效
        sql = sql.Replace("{红包领取上限}", "(case when {可领取最高金额}>cast(@max_money as decimal(18,2)) then cast(@max_money as decimal(18,2)) else {可领取最高金额} end)");//上限

        //下限仅对于“托号” 跟 “虚拟号” 有效，其他一般情况下是0.01（最低）
        sql = sql.Replace("{红包领取下限}", "(case when {可领取最高金额}>cast(@min_money as decimal(18,2)) then cast(@min_money as decimal(18,2)) else 0.01 end)");//下限


        //这个应该放在最后面，否则会出问题
        sql = sql.Replace("{可领取最高金额}", "((amount-amount_receive)-(number-number_receive-1)*(case when userid=-1001 then cast(@fake_redbag_minavg as decimal(18,2)) else 0.01 end))");



        if (fz.req("sql") == "1")
        {

            HttpContext.Current.Response.Write(getPamsSql(pams));
            HttpContext.Current.Response.Write("\r\n\r\n");
            HttpContext.Current.Response.Write(sql);
            HttpContext.Current.Response.End();

        }


        ds = db.getDataSet(sql, pams.ToArray());

        if (ds.Tables.Count > 0)
        {

            log.WriteLog("redbag", pmlist["id"] + "_U~" + dic_param(pmlist, "userid", -1) + "_FK~" + dic_param(pmlist, "fakeuser", ""), ToJson(ds.Tables[0]));

            string tokenid = cae.GetCache<string>("redbag_poll_token");
            if (!string.IsNullOrEmpty(tokenid) && tokenid == pmlist["id"] + "")
            {
                try
                {
                    if (Convert.ToInt16(ds.Tables[0].Rows[0]["residue_number"]) <= 0)
                    {
                        cae.RemoteCache("redbag_poll_token");
                    }
                }
                catch (Exception)
                {

                }
            }


            try
            {
                //只处理红包雨
                if (ds.Tables[0].Rows[0]["redbag_userid"] + "" == "-1001")
                {

                    //均摊金额>普通用户上限
                    if (Convert.ToDouble(ds.Tables[0].Rows[0]["share_money"]) > Convert.ToDouble(uConfig.stcdata("user_money_limit").Split('~')[1]))
                    {
                        if (Convert.ToDouble(ds.Tables[0].Rows[0]["over_money"]) > 1)
                        {
                            pmlist["fake_temp_Max"] = Convert.ToDouble(ds.Tables[0].Rows[0]["over_money"]) + Convert.ToDouble(uConfig.stcdata("user_money_limit").Split('~')[1]) * 2;
                            pmlist["fake_temp_Min"] = Convert.ToDouble(ds.Tables[0].Rows[0]["over_money"]) / 2;

                            Random rd = new Random(Guid.NewGuid().GetHashCode());// 使用不同的种子

                            pmlist["fake_user"] = rd.Next(1000000, 9999999).ToString();

                            DataSet temp_ds = new DataSet();
                            temp_ds = open_redBag(new Dictionary<string, object>{                                    
                            {"id",  pmlist["id"]  },
                            {"fakeuser",pmlist["fake_user"]},
                            {"isfair",1},
                            {"fakemoney_Max",  pmlist["fake_temp_Max"]},
                            {"fakemoney_Min", pmlist["fake_temp_Min"]}
                        }, fz);

                            log.WriteLog("redbag_over_limit", "[均摊 " + ds.Tables[0].Rows[0]["share_money"] + " > 用户上限 " + uConfig.stcdata("user_money_limit").Split('~')[1] + "] ) " + pmlist["fake_user"], "[id：" + pmlist["id"] + "][超出金额：" + ds.Tables[0].Rows[0]["over_money"] + "][领取限制：" + pmlist["fake_temp_Min"] + "~" + pmlist["fake_temp_Max"] + "]" + ToJson(temp_ds.Tables[0]));
                        }


                    }

                }


            }
            catch (Exception)
            {

            }
        }

        return ds;
    }
}
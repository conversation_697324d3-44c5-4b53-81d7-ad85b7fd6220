using com.cotees;
using LitJson;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.IO;
using System.Linq;
using System.Text;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

public partial class Api_admin : globalClass
{
    fuzhu fz = new fuzhu(HttpContext.Current);
    protected void Page_Load(object sender, EventArgs e)
    {
        Response.ContentType = "application/json";
        string result = main();
        Response.Write(result);
        Response.End();
    }

    public string main()
    {

        string _result = "";

        //数据库相关参数
        string sql = string.Empty;
        dbClass db = new dbClass();
        DataTable dt = new DataTable();
        DataTable temp_dt = new DataTable();
        DataTable cache_dt = new DataTable();
        DataTable select_dt = new DataTable();
        DataRow[] rows;
        DataRow[] matchingRows;
        int updateRowIndex = 0;
        DataSet ds;
        DataRow[] dr;
        Dictionary<string, object> pmlist = new Dictionary<string, object>();
        Dictionary<string, object> dic = new Dictionary<string, object>();
        Dictionary<string, object> _data = new Dictionary<string, object>();
        List<Dictionary<string, object>> list = new List<Dictionary<string, object>>();

        //临时参数
        Random rd = new Random();
        string temp = string.Empty;
        string temp_val = string.Empty;
        bool result = false;
        int res = 0;
        string[] g;
        string[] g2;
        string[] arrayList;
        List<string> text_array = new List<string>();
        List<string> temp_array = new List<string>();
        List<string> sList = new List<string>();
        JsonData jd;
        List<SqlParameter> pams = fz.collectReqParames(out result);

        fz.setResponseLable("msg");



        DataTable apikeys = chelper.gdt("apikeys");
        if (apikeys == null)
        {
            apikeys = new DataTable();
        }
        apikeys = selectDateTable(apikeys, "apitype='dating'");


        pmlist["buyersite"] = "";
        for (int i = 0; i < apikeys.Rows.Count; i++)
        {
            if (apikeys.Rows[i]["sckey"] + "" == fz.req("apikey"))
            {
                pmlist["buyersite"] = apikeys.Rows[i]["id"] + "";
                break;
            }
        }

        if (string.IsNullOrEmpty(pmlist["buyersite"] + ""))
        {
            fz.sendResponse("密钥不存在");
        }

        pams.Add(new SqlParameter("@buyersite", pmlist["buyersite"] + ""));



        if (fz.req("act") != "get_dating_total" && fz.req("act") != "get_sell_dating_total")
        {
            if (!IsNumeric(fz.req("uuid")))
            {
                fz.sendResponse("uuid有误");
            }
            if (!IsNumeric(fz.req("ph")) || fz.req("ph").Length != 11)
            {
                fz.sendResponse("手机号错误");
            }
        }


        pmlist["uid"] = Convert.ToInt64(fz.req("uuid")) * -1;
        pmlist["buyer_phone"] = fz.req("ph");
        pams.Add(new SqlParameter("@uid", pmlist["uid"] + ""));
        pams.Add(new SqlParameter("@buyer_phone", pmlist["buyer_phone"] + ""));

        log.WriteLog("transaction_api", "", fz.req("act") + "[" + pmlist["uid"] + "]-" + pmlist["buyersite"]);

        switch (fz.req("act"))
        {
            case "buy_list":
                fz.check_exist(new string[] { "p", "limit", "user_type" });

                fz.pager_where(true, " a.state=0 ");
                if (fz.req("dating_updateTime") != "" && fz.req("dating_updateTime") == cae.GetCache<string>("dating_updateTime"))
                {
                    fz.sendResponse("暂无新的数据", -901);
                }

                fz.pager_where(!fz.empty("payment_type"), " a.payment_type='@{payment_type}' ");

                pmlist["user_type"] = fz.sreq("user_type");

                //新注册账号显示指定区间金额订单
                if (pmlist["user_type"] + "" == "reg")
                {
                    g = uConfig.stcdata("new_buyAmount").Split('~');
                    if (IsNumeric(g[0]) && IsNumeric(g[1]))
                    {
                        fz.pager_where(true, " amount>=" + g[0] + " and amount<=" + g[1] + " ");
                    }
                }

                //show_users订单对应显示用户
                fz.pager_where(true, " (isnull(a.show_users,'')='' or isnull(a.show_users,'') like '%" + pmlist["user_type"] + "%') ");

                //限制用户无法查看自己的订单
                fz.pager_where(true, " (buyersite<>" + pmlist["buyersite"] + " or isnull(sellerId,0)<>" + pmlist["uid"] + ") ");


                //屏蔽指定用户
                fz.pager_where(true, " (isnull(a.unshow_users,'')='' or isnull(a.unshow_users,'') not like '%" + pmlist["buyer_phone"] + "%') ");


                dic = fz.pager_data(string.Format("{0} a with(nolock) ", "buy_list"), "a.* ", "istop desc,isnull(p1,0) desc,(case when a.payment_type=''银行卡'' then 1 else 0 end) desc,a.create_time ", (Convert.ToInt16(fz.req("p")) + 1).ToString(), fz.req("limit"), 0);
                //拓展功能
                list = (List<Dictionary<string, object>>)dic["data"];
                for (int i = 0; i < list.Count; i++)
                {
                    Dictionary<string, object> _tempData = list[i];
                    list[i] = _tempData;
                }
                dic["data"] = list;

                dic = fz.pager_common_response(dic, true);
                dic["dating_updateTime"] = cae.GetCache<string>("dating_updateTime");
                fz.sendResponse("success", 1, dic);

                break;
            case "dating_data":
                fz.check_exist(new string[] { "id" });
                dt = db.getDataTable("select * from buy_list with(nolock) where orderId=@id  ", pams.ToArray());
                fz.jsonResponse(TransToJson(dt));
                break;
            case "operator_data":
                fz.check_exist(new string[] { "id" });
                dt = db.getDataTable(" select * from buy_list with(nolock) where orderId=@id and payment_type='银行卡'  ", pams.ToArray());
                fz.jsonResponse(TransToJson(dt));
                break;
            case "dating_buy":
                fz.check_exist(new string[] { "total_amount", "id", "user_type" });

                temp = " and (isnull(show_users,'')='' or isnull(show_users,'') like '%" + fz.req("user_type") + "%') ";



                sql = @"

declare @existsId varchar(38)

select top 1 @existsId=orderId from buy_list with(nolock) where buyersite=@buyersite and userid=@uid and (state=1000 or state=1001)

if(@existsId is not null)
begin
    select '您有一笔订单未完成交易' as errmsg,@existsId as orderId
    return
end
declare @orderId varchar(38)

update buy_list set user_type=@user_type,error_reason=null,@orderId=orderId,state=1000,userid=@uid,buy_time=getdate(),buyersite=@buyersite,buyer_phone=@buyer_phone,update_time=getdate() output deleted.* where id=@id and state=0 and amount=@total_amount and payment_type<>'银行卡' and (buyersite<>@buyersite or isnull(sellerId,0)<>@uid) " + temp + @"

if(@orderId is not null)
begin
    insert into buy_records values(@uid,'下单',@id,@orderId,null,getdate())
end

";


                dt = db.getDataTable(sql, pams.ToArray());
                if (dt.Rows.Count == 0)
                {
                    fz.sendResponse("订单金额不匹配或正在交易中");
                }

                cae.SetCache("dating_updateTime", getTimeStamp());

                temp = "下单成功";
                try
                {
                    dic.Add("orderId", dt.Rows[0]["orderId"] + "");
                    temp = dt.Rows[0]["errmsg"] + "";
                }
                catch (Exception)
                {
                }

                if (temp == "下单成功")
                {
                    if (dt.Rows[0]["api_orderNo"] + "" != "")
                    {
                        pmlist["notify_state"] = "01";

                        dic = new Dictionary<string, object>();
                        dic.Add("appId", dt.Rows[0]["appid"] + "");
                        dic.Add("orderNo", dt.Rows[0]["api_orderNo"] + "");
                        dic.Add("orderStatus", pmlist["notify_state"]);//处理中
                        pmlist["notify_text"] = api_Notify("notifyFromTaofanke", dic);


                        pams = new List<SqlParameter>();
                        pams.Add(new SqlParameter("@id", dt.Rows[0]["id"]));
                        pams.Add(new SqlParameter("@notify_text", pmlist["notify_state"] + " - " + pmlist["notify_text"] + ""));
                        sql = " update buy_list set notifyOrderStatus=@notify_text where id=@id  ";
                        db.ExecuteNonQuery(sql, pams.ToArray());
                    }


                }


                fz.sendResponse(temp, (temp == "下单成功" ? 1 : 0), dic);
                break;
            case "dating_details":
                sql = @" select * from buy_list with(nolock) where orderId=@id and buyersite=@buyersite and userid=@uid  ";
                dt = db.getDataTable(sql, pams.ToArray());
                fz.jsonResponse(TransToJson(dt));
                break;
            case "upload_buyOrder":
                fz.check_exist(new string[] { "id,订单id", "payimg,付款截图" });


                pams.Add(new SqlParameter("@usdt_price", uConfig.stcdata("usdt_price")));

                sql = @" 
declare @orderId varchar(38)
declare @api_orderNo varchar(38)
declare @appid varchar(20)
update buy_list set @appid=appid,@api_orderNo=api_orderNo,@orderId=orderId,state=1001,payimg=@payimg,update_time=getdate(),usdt_price=@usdt_price,upload_type=@upload_type where id=@id and buyersite=@buyersite and userid=@uid and state=1000  

if(@@rowcount>0)
begin                                                    
    insert into buy_records values(@uid,'上传截图',@id,@orderId,@payimg,getdate())
    select @api_orderNo as api_orderNo,@id as id,@appid as appid
end

                                ";
                dt = db.getDataTable(sql, pams.ToArray());
                if (dt.Rows.Count == 0)
                {
                    fz.sendResponse("订单不允许操作");
                }



                if (dt.Rows[0]["api_orderNo"] + "" != "")
                {
                    pmlist["notify_state"] = "03";

                    dic = new Dictionary<string, object>();
                    dic.Add("appId", dt.Rows[0]["appid"] + "");
                    dic.Add("orderNo", dt.Rows[0]["api_orderNo"] + "");
                    dic.Add("orderStatus", pmlist["notify_state"]);//审核中（用户上传截图）
                    pmlist["notify_text"] = api_Notify("notifyFromTaofanke", dic);


                    pams = new List<SqlParameter>();
                    pams.Add(new SqlParameter("@id", dt.Rows[0]["id"]));
                    pams.Add(new SqlParameter("@notify_text", pmlist["notify_state"] + " - " + pmlist["notify_text"] + ""));
                    sql = " update buy_list set notifyOrderStatus=@notify_text where id=@id  ";
                    db.ExecuteNonQuery(sql, pams.ToArray());

                }


                fz.sendResponse("上传成功，请耐心等待卖家确认", 1);
                break;
            case "cancel_buyOrder":
                fz.check_exist(new string[] { "id,订单id" });

                sql = @" 
declare @orderId varchar(38)
declare @api_orderNo varchar(38)
declare @appid varchar(20)
update buy_list set @appid=appid,@api_orderNo=api_orderNo,@orderId=orderId,state=0,userid=null,update_time=getdate() where id=@id and buyersite=@buyersite and userid=@uid and state=1000

if(@@rowcount>0)
begin                                                    
    insert into buy_records values(@uid,'取消交易',@id,@orderId,null,getdate())
    select @api_orderNo as api_orderNo,@id as id,@appid as appid
end

";
                dt = db.getDataTable(sql, pams.ToArray());
                if (dt.Rows.Count == 0)
                {
                    fz.sendResponse("取消失败");
                }

                if (dt.Rows[0]["api_orderNo"] + "" != "")
                {
                    pmlist["notify_state"] = "02";

                    dic = new Dictionary<string, object>();
                    dic.Add("appId", dt.Rows[0]["appid"] + "");
                    dic.Add("orderNo", dt.Rows[0]["api_orderNo"] + "");
                    dic.Add("orderStatus", pmlist["notify_state"] + "");
                    pmlist["notify_text"] = api_Notify("notifyFromTaofanke", dic);


                    pams = new List<SqlParameter>();
                    pams.Add(new SqlParameter("@id", dt.Rows[0]["id"]));
                    pams.Add(new SqlParameter("@notify_text", pmlist["notify_state"] + " - " + pmlist["notify_text"] + ""));
                    sql = " update buy_list set notifyOrderStatus=@notify_text where id=@id  ";
                    db.ExecuteNonQuery(sql, pams.ToArray());

                }

                cae.SetCache("dating_updateTime", getTimeStamp());
                fz.sendResponse("取消成功", 1);
                break;
            case "get_dating_total":
                fz.check_exist(new string[] { "id" });
                dt = db.getDataTable("select * from buy_list with(nolock) where id=@id and buyersite=@buyersite and state=1  ", pams.ToArray());
                fz.jsonResponse(TransToJson(dt));
                break;
            case "get_sell_dating_total":
                fz.check_exist(new string[] { "id" });
                dt = db.getDataTable("select * from [sell_dating_list] with(nolock) where id=@id and buyersite=@buyersite  ", pams.ToArray());
                fz.jsonResponse(TransToJson(dt));
                break;
            case "buy_orderList":
                fz.check_exist(new string[] { "p", "limit" });

                //fz.pager_list(new List<string> { "id", "orderId", "amount", "state", "remark", "create_time@date()" }, false);
                fz.pager_where(true, " buyersite=" + pmlist["buyersite"] + " and a.userid=" + pmlist["uid"]);
                fz.pager_where(!fz.empty("state"), " a.state in (@{state}) ");


                dic = fz.pager_data(string.Format("buy_list a with(nolock) ", ""), "a.* ", "isnull(a.buy_time,a.create_time) desc", (Convert.ToInt16(fz.req("p")) + 1).ToString(), fz.req("limit"), 0);
                //拓展功能
                list = (List<Dictionary<string, object>>)dic["data"];
                for (int i = 0; i < list.Count; i++)
                {
                    Dictionary<string, object> _tempData = list[i];
                    list[i] = _tempData;
                }
                dic["data"] = list;
                fz.pager_common_response(dic);
                break;
            case "sell_dating":
                fz.check_exist(new string[] { "user_type", "task_amount", "payment_name", "payment_bankname", "payment_bankid" });



                sql = @"

-- 生成卖币订单
declare @sid int
DECLARE @unique_orderNo VARCHAR(38);
EXEC [Get_MapOrderId] @GeneratedCode = @unique_orderNo OUTPUT;    
insert into [sell_dating_list] values(null,null,@user_type,@uid,cast(@task_amount as decimal(18,2)),@unique_orderNo,null,'网银',@payment_name,@payment_bankname,@payment_bankid,99,GETDATE(),null,null,@buyersite,@buyer_phone,null)
set @sid=scope_identity()

select '订单提交成功' as errmsg,@sid as sid


";

                dt = db.getDataTable(sql, pams.ToArray());

                if (dt.Rows.Count > 0)
                {
                    temp = dt.Rows[0]["errmsg"] + "";
                    try
                    {
                        dic.Add("sid", dt.Rows[0]["sid"] + "");
                    }
                    catch (Exception)
                    {

                    }
                    fz.sendResponse(temp, (temp == "订单提交成功" ? 1 : -1), dic);
                }
                else
                {
                    fz.sendResponse("订单提交失败");
                }
                break;
            case "sell_dating_list":
                fz.check_exist(new string[] { "p", "limit" });

                pmlist["desc"] = "id desc";
                fz.pager_where(true, " a.userid=" + pmlist["uid"] + " ");

                dic = fz.pager_data(string.Format("{0} a with(nolock) ", "sell_dating_list"), "a.* ", pmlist["desc"] + "", (Convert.ToInt16(fz.req("p")) + 1).ToString(), fz.req("limit"), 0);
                //拓展功能
                list = (List<Dictionary<string, object>>)dic["data"];
                for (int i = 0; i < list.Count; i++)
                {
                    Dictionary<string, object> _tempData = list[i];
                    list[i] = _tempData;
                }
                dic["data"] = list;
                fz.pager_common_response(dic);
                break;
            case "finish_sell_order":
                fz.check_exist(new string[] { "id" });

                sql = @"

-- 卖币订单
declare @buy_orderId varchar(38)
declare @total_amount decimal(18,2)
update [sell_dating_list] set state=1,finish_time=getdate(),@total_amount=amount,@buy_orderId=buy_orderId where id=@id and userid=@uid and buyersite=@buyersite and state=0
if(@buy_orderId is not null)
begin
    update buy_list set sell_state=1 where orderId=@buy_orderId
end

";
                res = db.ExecuteNonQuery(sql, pams.ToArray());
                if (res > 0)
                {
                    //此处开始就是Api对接了

                    pmlist["apikeys"] = chelper.gdt("apikeys");
                    try
                    {
                        pmlist["apikeys_query"] = selectDateTable((DataTable)pmlist["apikeys"], "id=" + pmlist["buyersite"] + "");
                    }
                    catch (Exception)
                    {
                        pmlist["apikeys_query"] = new DataTable();
                    }
                    pmlist["notify_url"] = "-";
                    if (((DataTable)pmlist["apikeys_query"]).Rows.Count > 0)
                    {
                        pmlist["notify_url"] = ((DataTable)pmlist["apikeys_query"]).Rows[0]["notify_url"] + "";
                    }

                    try
                    {
                        temp = GetHttp((pmlist["notify_url"] + "").Replace("{act}", "finish_sell_dating"), "id=" + fz.req("id") + " ");
                    }
                    catch (Exception ex)
                    {
                        temp = "推送失败：" + ex.Message.ToArray();
                    }
                    log.WriteLog("api-selldating-完成推送", fz.req("id"), temp);
                }
                fz.sendRsp(res, "确认成功", "确认失败");
                break;
            default:
                fz.sendResponse("not found", 404);
                break;
        }
        return _result;
    }
}
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

public partial class api_cache_clear : System.Web.UI.Page
{
    protected void Page_Load(object sender, EventArgs e)
    {
        string name = Request.QueryString["name"] + "";
        cae.RemoteCache("stc");
        chelper.cdt(name);
        Response.Write("数据已清除");
        Response.End();
    }
}
using com.cotees;
using LitJson;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Text;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

public partial class Api_admin : globalClass
{
    fuzhu fz = new fuzhu(HttpContext.Current);
    protected void Page_Load(object sender, EventArgs e)
    {
        Response.ContentType = "application/json";
        string result = main();
        Response.Write(result);
        Response.End();
    }

    public string main()
    {

        string _result = "";

        //数据库相关参数
        string sql = string.Empty;
        dbClass db = new dbClass();
        DataTable dt = new DataTable();
        DataTable temp_dt = new DataTable();
        DataTable cache_dt = new DataTable();
        DataTable select_dt = new DataTable();
        DataRow[] rows;
        DataRow[] matchingRows;
        int updateRowIndex = 0;
        DataSet ds;
        DataRow[] dr;
        Dictionary<string, object> pmlist = new Dictionary<string, object>();
        Dictionary<string, object> dic = new Dictionary<string, object>();
        Dictionary<string, object> temp_dic = new Dictionary<string, object>();
        Dictionary<string, object> _data = new Dictionary<string, object>();
        List<Dictionary<string, object>> list = new List<Dictionary<string, object>>();
        DateTime temp_time = DateTime.Now;

        //临时参数
        Random rd = new Random();
        string temp = string.Empty;
        string temp_val = string.Empty;
        bool result = false;
        int res = 0;
        int countindex = 0;
        string[] g;
        string[] g2;
        string[] arrayList;
        string[] keyArray;
        string[] valArray;
        List<string> values_array = new List<string>();
        List<string> text_array = new List<string>();
        List<string> temp_array = new List<string>();
        List<string> sList = new List<string>();
        List<string> sList_refresh = new List<string>();
        List<string> sqls = new List<string>();
        List<int> intArray = new List<int>();
        JsonData jd;
        List<SqlParameter> pams = fz.collectReqParames(out result);

        int secondsDifference = 0;

        fz.setResponseLable("msg");


        switch (fz.req("apikey"))
        {
            case "token_pay_Fzc3dvcm":
                break;
            case "token_Rzy9d123_8080":
                if (fz.req("act") != "newOrder" && fz.req("act") != "orderStatus" && fz.req("act") != "orderCreate" && fz.req("act") != "orderSelect" && fz.req("act") != "daapi")
                {
                    fz.sendResponse("apierror");
                }
                break;
            default:
                fz.sendResponse("null");
                break;
        }


        pmlist["userip"] = getUserIP();
        //try
        //{
        //    pmlist["userip"] = System.Web.HttpContext.Current.Request.ServerVariables["HTTP_X_TENANT_FORWARD_FOR"] + "";
        //}
        //catch (Exception)
        //{
        //}

        //log.WriteLog("fuwu记录", "T", fz.req("act"));


        switch (fz.req("act"))
        {
            case "redbag_check":
                cae.SetCache("fuwu_online", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));
                break;
            case "new_play":
            case "play_hello":
                cae.SetCache("play_online", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));
                break;
            case "getstatus_ns":
                cae.SetCache("monitor_online", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));
                break;
            default:
                break;
        }


        bool islocal = Request.Url.Host == "localhost";

        switch (fz.req("act"))
        {
            case "daapi":

                log.WriteLog("挂单API对接", "状态通知", "orderNo=" + fz.req("orderNo") + ";sysOrderNo=" + fz.req("sysOrderNo") + ";orderAmt=" + fz.req("orderAmt") + ";orderStatus=" + fz.req("orderStatus") + ";payerName=" + fz.req("payerName"));

                if (!fz.empty("payerName"))
                {
                    temp = ",payerName=@payerName";
                }
                sql = " update transport_orders set state_code=@orderStatus" + temp + " output deleted.* where state=1000 and orderNo=@orderNo ";
                dt = db.getDataTable(sql, pams.ToArray());

                if (!fz.empty("payerName") && dt.Rows.Count > 0)
                {
                    cae.SetCache("payerName_" + dt.Rows[0]["sd_orderNo"], fz.req("payerName"), DateTime.Now.AddHours(10));
                }

                Response.Write("SUCCESS");
                Response.End();
                break;
            case "orderCreate":
                fz.check_exist(new string[] { "orderNo", "orderAmt", "bankAccName", "bankAccNo", "bankName", "appid" });

                g = uConfig.stcdata("buy_appids").Split('\n');
                for (int i = 0; i < g.Length; i++)
                {
                    g2 = g[i].Split(new string[] { "----" }, StringSplitOptions.None);
                    if (g2.Length == 2 && g2[0] == fz.req("appid"))
                    {
                        temp = g2[1];
                        break;
                    }
                }

                if (string.IsNullOrEmpty(temp))
                {
                    log.WriteLog("买币api", "", fz.req("appid"));
                    fz.sendResponse("appid{" + fz.req("appid") + "}未配置");
                }

                pams.Add(new SqlParameter("@p1", uConfig.stcdata("apibuy_p1")));
                pams.Add(new SqlParameter("@show_user", uConfig.stcdata("apibuy_show_user")));
                pams.Add(new SqlParameter("@param_bankBranch", fz.req("bankBranch")));
                pams.Add(new SqlParameter("@payment_type", temp));

                sql = @"

declare @total_amount decimal(18,2)
set @total_amount=cast(@orderAmt as decimal(18,2))


if(Exists(select id from buy_list with(nolock) where api_orderNo=@orderNo))
begin
    select '订单已存在' as errmsg
    return
end

-- 生成订单号
DECLARE @orderId VARCHAR(38);
EXEC GenerateUniqueBuyOrderId @GeneratedCode = @orderId OUTPUT;
    insert into buy_list(orderId,upload_servid,amount,payment_type,payment_name,payment_bankid,payment_bankname,payment_city,state,create_time,p1,servid,show_users,api_orderNo,appid) values(@orderId,-3001,@total_amount,@payment_type,@bankAccName,@bankAccNo,@bankName,@param_bankBranch,0,getdate(),@p1,0,@show_user,@orderNo,@appid)


select 'SUCCESS' as errmsg,@orderId as orderId

";


                dt = db.getDataTable(sql, pams.ToArray());

                if (dt.Rows.Count == 0)
                {
                    fz.sendResponse("推送失败[系统出错]");
                }


                temp = dt.Rows[0]["errmsg"] + "";
                try
                {
                    dic.Add("orderNo", dt.Rows[0]["orderId"] + "");
                    cae.SetCache("dating_updateTime", getTimeStamp());
                }
                catch (Exception)
                {
                }
                fz.sendResponse(temp, (temp == "SUCCESS" ? 1 : -1), dic);
                break;
            case "orderSelect":
                fz.check_exist(new string[] { "orderNo" });

                sql = " select top 1 * from buy_list with(nolock) where api_orderNo=@orderNo and isnull(api_orderNo,'')<>''  ";
                dt = db.getDataTable(sql, pams.ToArray());
                if (dt.Rows.Count == 0)
                {
                    fz.sendResponse("订单不存在");
                }


                pmlist["notify_state"] = "";
                switch (dt.Rows[0]["state"] + "")
                {
                    case "0":
                        pmlist["notify_state"] = "02";
                        break;
                    case "1000":
                        pmlist["notify_state"] = (dt.Rows[0]["error_reason"] + "" != "" ? "04" : "01");
                        break;
                    case "1001":
                        pmlist["notify_state"] = "03";
                        break;
                    case "-1":
                        pmlist["notify_state"] = "05";
                        break;
                    case "-2":
                        pmlist["notify_state"] = "99";
                        break;
                    case "1":
                        pmlist["notify_state"] = "00";
                        break;
                    case "-3":
                        pmlist["notify_state"] = "04";
                        break;
                    default:
                        break;
                }

                dic = new Dictionary<string, object>();
                dic.Add("orderNo", dt.Rows[0]["api_orderNo"] + "");
                dic.Add("orderStatus", pmlist["notify_state"] + "");

                fz.sendResponse("SUCCESS", 1, dic);

                break;
            case "newOrder":

                fz.check_exist(new string[] { "orderNo", "orderAmt" });

                if (Request.Url.Host != "localhost")
                {
                    g = uConfig.stcdata("sd_ip_limits").Split('\n');
                    if (!g.Contains(pmlist["userip"] + ""))
                    {
                        fz.sendResponse("ip不在白名单内：" + pmlist["userip"]);
                    }
                }


                if (fz.req("payerName") != "")
                {
                    pams.Add(new SqlParameter("@payer_name", fz.req("payerName")));
                }
                else
                {
                    pams.Add(new SqlParameter("@payer_name", DBNull.Value));
                }

                pams.Add(new SqlParameter("@transport_rate", uConfig.stcdata("transport_rate")));

                sql = @"

declare @amount decimal(18,2)
declare @title varchar(500)
declare @imgurl varchar(200)
declare @rate_award decimal(5,2)
declare @award_amount decimal(18,2)

set @amount=cast(@orderAmt as decimal(18,2))
set @rate_award=cast(@transport_rate as decimal(5,2))
set @award_amount=@amount/100*@rate_award

select top 1 @title=item_name,@imgurl=item_imgurl from (select top 10 * from order_list with(nolock) order by ABS(@amount-amount))t order by NEWID()

if(@title is null)
begin
    select '拉取订单错误' as errmsg
    return
end





-- 生成订单号 
DECLARE @new_orderId VARCHAR(38);
EXEC GenerateReceive_TransportOrder @GeneratedCode = @new_orderId OUTPUT;


DECLARE @exists_orderId VARCHAR(38);
select @exists_orderId=orderNo from receive_api_records with(nolock) where other_orderNo=@orderNo

if(@exists_orderId is null)
begin

    insert into receive_transportOrders(userid,title,imgurl,orderNo,orderType,orderAmt,rate_award,award_amount,other_orderNo,state,create_time,payerName) values(null,@title,@imgurl,@new_orderId,'银联',@amount,@rate_award,@award_amount,@orderNo,0,getdate(),@payer_name) 
    
    insert into receive_api_records values(@new_orderId,@orderNo,GETDATE())

    select 'SUCCESS' as errmsg,@new_orderId as orderId

end
else
begin
    
    select '订单已存在' as errmsg,@exists_orderId as orderId
    return
end

";

                dt = db.getDataTable(sql, pams.ToArray());

                if (dt.Rows.Count == 0)
                {
                    fz.sendResponse("推送失败[系统出错]");
                }

                temp = dt.Rows[0]["errmsg"] + "";

                try
                {
                    dic.Add("orderNo", dt.Rows[0]["orderId"] + "");
                    //cae.SetCache("neworder_time", getTimeStamp());
                }
                catch (Exception)
                {
                }
                fz.sendResponse(temp, (temp == "SUCCESS" || temp == "订单已存在" ? 1 : -1), dic);
                break;
            case "push_transportOrders":
                sql = string.Format(@"

    declare @tb table(id int,userid int,title varchar(500),imgurl varchar(200),orderNo varchar(38),orderType varchar(10),orderAmt decimal(18,2),rate_award decimal(5,2),award_amount decimal(18,2),other_orderNo  varchar(38),state int,create_time datetime,payerName nvarchar(30))

    insert into @tb select id,{0} from receive_transportOrders with(nolock)

    declare @total_number int
    set @total_number=0
    select @total_number=count(0) from @tb

    if(@total_number>0)
    begin
        insert into transport_orders({0}) select {0} from @tb
        delete receive_transportOrders where id in (select id from @tb)
    end

    select @total_number as total_number
    select orderNo,orderAmt from @tb

", "userid,title,imgurl,orderNo,orderType,orderAmt,rate_award,award_amount,other_orderNo,state,create_time,payerName");


                ds = db.getDataSet(sql, pams.ToArray());

                if (ds.Tables.Count == 0)
                {
                    fz.sendResponse("转入失败[DS]");
                }

                dt = ds.Tables[0];
                if (dt.Rows.Count == 0)
                {
                    fz.sendResponse("转入失败[系统出错]");
                }
                temp = "暂无订单转入";
                pmlist["total_number"] = dt.Rows[0]["total_number"] + "";

                dt = ds.Tables[1];
                for (int i = 0; i < dt.Rows.Count; i++)
                {
                    text_array.Add(dt.Rows[i]["orderNo"] + "," + dt.Rows[i]["orderAmt"]);
                }
                dic.Add("orders", text_array);



                try
                {
                    dic.Add("total_number", pmlist["total_number"] + "");
                    if (pmlist["total_number"] + "" != "0")
                    {
                        temp = "转入成功";
                        cae.SetCache("neworder_time", getTimeStamp());
                    }
                }
                catch (Exception)
                {
                }
                fz.sendResponse(temp, (pmlist["total_number"] + "" != "0" ? 1 : -1), dic);

                cae.SetCache("neworder_time", getTimeStamp());
                break;
            case "orderStatus":
                fz.check_exist(new string[] { "orderNo", "orderStatus" });
                if (fz.req("orderStatus") == "07")
                {
                    if (!fz.empty("payerName"))
                    {
                        temp = ",payerName=@payerName";
                    }
                    sql = " update transport_orders set state=-1" + temp + " output deleted.* where other_orderNo=@orderNo and state=0 ";

                    dt = db.getDataTable(sql, pams.ToArray());

                    if (dt.Rows.Count == 0)
                    {
                        log.WriteLog("状态通知-无效", "orderStatus", fz.req("orderNo"));
                        fz.sendResponse("订单状态已结束");
                    }

                    if (!fz.empty("payerName") && dt.Rows.Count > 0)
                    {
                        cae.SetCache("payerName_" + dt.Rows[0]["sd_orderNo"], fz.req("payerName"), DateTime.Now.AddHours(10));
                    }

                    log.WriteLog("状态通知-退回", "orderStatus", fz.req("orderNo") + "----" + dt.Rows[0]["state"] + "");

                    if (dt.Rows[0]["state"] + "" != "0" && dt.Rows[0]["userid"] + "" != "")
                    {
                        pams = new List<SqlParameter>();
                        pams.Add(new SqlParameter("@id", dt.Rows[0]["id"] + ""));
                        sql = " [refund_transport_orders] @id ";
                        db.ExecuteNonQuery(sql, pams.ToArray());
                    }

                    fz.sendResponse("取消成功", 1);


                }
                else
                {
                    if (!fz.empty("payerName"))
                    {
                        sql = " update transport_orders set payerName=@payerName output deleted.* where other_orderNo=@orderNo  ";
                        dt = db.getDataTable(sql, pams.ToArray());

                        if (!fz.empty("payerName") && dt.Rows.Count > 0)
                        {
                            cae.SetCache("payerName_" + dt.Rows[0]["sd_orderNo"], fz.req("payerName"), DateTime.Now.AddHours(10));
                        }

                        log.WriteLog("状态通知-更新", "payerName", fz.req("orderNo") + "----" + fz.req("payerName") + "");
                        fz.sendResponse("姓名更新成功", 1);
                    }
                }

                fz.sendResponse("", 1000);
                break;
            case "check_transport_timeOut":
                pams.Add(new SqlParameter("@timeoutRet_minute", uConfig.stcdata("timeoutRet_minute")));

                sql = " declare @tb table(reward_amount decimal(18,2),timeout_second int) ";
                g = uConfig.stcdata("timeoutRet_rules").Split('\n');
                for (int i = 0; i < g.Length; i++)
                {
                    g2 = g[i].Split('~');
                    if (g2.Length == 2 && IsNumeric(g2[0]) && IsNumeric(g2[1]))
                    {
                        sql += " insert into @tb values(" + g2[0] + "," + g2[1] + ") ";
                    }
                }

                pams.Add(new SqlParameter("@sys_order_timeout", uConfig.stcdata("sys_order_timeout")));//系统订单接单超时
                pams.Add(new SqlParameter("@sys_comfirm_timeout", uConfig.stcdata("sys_comfirm_timeout")));//新号订单确认超时
                pams.Add(new SqlParameter("@fnew_comfirm_timeout", uConfig.stcdata("fnew_comfirm_timeout")));//非新号确认超时



                sql += @" 
update t set 
notifyOrderStatus='success'
,timeout_second={timeout_second}
,timeout_time=(case when t.state<>1000 then null else dateadd(second,{timeout_second},getdate()) end)
,t.state=(case when isnull(order_mode,0)=1 then -999 else (case when (t.state<>1000 or (t.other_orderNo<>'-' and isnull(t.payerName,'')='')) then 2 else 20 end) end) 
output deleted.* 

from transport_orders t left join accounts a on t.userid=a.id 

where

datediff(second,t.create_time,getdate())<3600*12 

and (

    (
    t.state=0 
    and limit_usertype is null 
    and datediff(second,t.create_time,getdate())>(case when other_orderNo='-' then @sys_order_timeout else 60 end)
    ) 

    or 

    (
    t.state=1000 
    and isnull(t.orderLock,0)=0 
    and datediff(second,receive_time,getdate())>(case when isnull(a.usertype,1)=0 then @sys_comfirm_timeout else @fnew_comfirm_timeout end)
    )

) 
and  isnull(order_mode,0)=0

".Replace("{timeout_second}", " ISNULL((select top 1 timeout_second from @tb where a.reward_amount>=reward_amount order by reward_amount desc),@timeoutRet_minute)");

                dt = db.getDataTable(sql, pams.ToArray());
                sql = string.Empty;


                pams = new List<SqlParameter>();
                for (int i = 0; i < dt.Rows.Count; i++)
                {
                    if (dt.Rows[i]["other_orderNo"] + "" != "-")
                    {
                        dic = new Dictionary<string, object>();
                        dic.Add("appId", uConfig.stcdata("transport_appid"));
                        dic.Add("orderNo", dt.Rows[i]["other_orderNo"] + "");
                        dic.Add("orderStatus", dt.Rows[i]["state"] + "" == "1000" ? "06" : "04");

                        temp = GetSignParams(dic);


                        pmlist["sign_param"] = temp + "&key=" + uConfig.stcdata("transport_apikey");
                        temp = md5(pmlist["sign_param"] + "");


                        log.WriteLog("推单通知-超时", "notifyOrderStatus_签名", "参数=" + pmlist["sign_param"] + " 结果=" + temp.ToUpper());

                        dic.Add("sign", temp.ToUpper());




                        temp = GetSignParams(dic);

                        pmlist["text_response"] = "";
                        try
                        {
                            pmlist["text_response"] = SendRequestC(uConfig.stcdata("transport_network") + "/notifyOrderStatus.do?" + temp, "", "");
                        }
                        catch (Exception)
                        {
                        }

                        log.WriteLog("推单通知-超时", "notifyOrderStatus", temp + "----" + pmlist["text_response"] + "");




                        pmlist["notify_text"] = "推送失败";
                        try
                        {
                            jd = JsonMapper.ToObject(pmlist["text_response"] + "");
                            pmlist["notify_text"] = jd["msg"] + "";
                        }
                        catch (Exception)
                        {
                        }
                        if ((pmlist["notify_text"] + "").Length > 50)
                        {
                            pmlist["notify_text"] = (pmlist["notify_text"] + "").Substring(0, 50);
                        }

                        if (pmlist["notify_text"] != "success")
                        {
                            pams.Add(new SqlParameter("@tsid" + i, dt.Rows[i]["id"]));
                            pams.Add(new SqlParameter("@notify_text" + i, pmlist["notify_text"] + ""));
                            sql += string.Format(" update transport_orders set notifyOrderStatus=@notify_text{0} where id=@tsid{0}  ", i);
                        }




                    }
                    //else if (dt.Rows[i]["sysOrderNo"] + "" != "")
                    //{

                    //    // 【订单确认】推送状态（新注册）

                    //    dic = new Dictionary<string, object>();
                    //    dic.Add("userNo", uConfig.stcdata("apireg_userNo"));
                    //    dic.Add("orderNo", dt.Rows[0]["orderNo"] + "");
                    //    dic.Add("sysOrderNo", dt.Rows[0]["sysOrderNo"] + "");

                    //    dic.Add("orderAmt", Convert.ToDouble(dt.Rows[0]["amount"] + "").ToString("0.00"));
                    //    dic.Add("orderTime", (dt.Rows[0]["receive_time"] + "").Replace("-", "").Replace("/", "").Replace(":", "").Replace(" ", ""));
                    //    dic.Add("orderStatus", "06");

                    //    temp = GetSignParams(dic);


                    //    pmlist["sign_param"] = temp + "&key=" + uConfig.stcdata("apireg_key");
                    //    temp = md5(pmlist["sign_param"] + "");


                    //    log.WriteLog("新手API对接", "notify_签名[超时]", "参数=" + pmlist["sign_param"] + " 结果=" + temp.ToUpper());

                    //    dic.Add("sign", temp.ToUpper());

                    //    temp = GetSignParams(dic);

                    //    pmlist["text_response"] = "";
                    //    try
                    //    {
                    //        pmlist["text_response"] = SendRequestC(uConfig.stcdata("apireg_network") + "/notify.do?" + temp, "", "");
                    //    }
                    //    catch (Exception)
                    //    {
                    //    }


                    //    log.WriteLog("新手API对接", "notify_结果[超时]", temp + "----" + pmlist["text_response"] + "");




                    //    pmlist["notify_text"] = "推送失败";
                    //    try
                    //    {
                    //        jd = JsonMapper.ToObject(pmlist["text_response"] + "");
                    //        pmlist["notify_text"] = jd["msg"] + "";
                    //    }
                    //    catch (Exception)
                    //    {
                    //    }
                    //    if ((pmlist["notify_text"] + "").Length > 50)
                    //    {
                    //        pmlist["notify_text"] = (pmlist["notify_text"] + "").Substring(0, 50);
                    //    }

                    //    if (pmlist["notify_text"] != "success")
                    //    {
                    //        pams.Add(new SqlParameter("@tsid" + i, dt.Rows[i]["id"]));
                    //        pams.Add(new SqlParameter("@notify_text" + i, pmlist["notify_text"] + ""));
                    //        sql += string.Format(" update transport_orders set notifyOrderStatus=@notify_text{0} where id=@tsid{0}  ", i);
                    //    }

                    //}

                    // 未填姓名可直接推（注：必须在非系统内订单情况下，系统内订单即：托号/新号订单，other_orderNo='-'）
                    if (dt.Rows[i]["other_orderNo"] + "" != "-" && dt.Rows[i]["payerName"] + "" == "")
                    {
                        if (dt.Rows[i]["state"] + "" == "1000")
                        {
                            log.WriteLog("订单记录", "超时直退-无名称", dt.Rows[i]["other_orderNo"] + "");
                            //用户接单未操作的订单超时
                            sql += " exec('[refund_transport_orders] " + dt.Rows[i]["id"] + "') ";
                        }
                    }


                }

                if (!string.IsNullOrEmpty(sql))
                {
                    res = db.ExecuteNonQuery(sql, pams.ToArray());
                }


                fz.sendResponse("Update-" + dt.Rows.Count + "-" + res, 1);
                break;
            case "check_timeOut_expire":


                sql = " update transport_orders set state=2 output deleted.*  where state=20 and datediff(second,getdate(),timeout_time)<0 ";

                dt = db.getDataTable(sql);

                //if (Request.Url.Host == "localhost")
                //{
                //    fz.sendResponse("测试：" + ToJson(dt));
                //}

                sql = string.Empty;
                for (int i = 0; i < dt.Rows.Count; i++)
                {
                    //用户超时15分钟返回冻结金额
                    sql += " exec('[refund_transport_orders] " + dt.Rows[i]["id"] + "') ";

                }

                if (!string.IsNullOrEmpty(sql))
                {
                    res = db.ExecuteNonQuery(sql);
                }

                fz.sendResponse("Update-" + dt.Rows.Count + "-" + res, 1);

                break;
            case "check_storeOrder_expire":
                sql = " update t set state=1001  output deleted.* from store_orders t left join accounts u with(nolock) on t.userid=u.id  where t.state=1000 and GETDATE()>=DATEADD(day,days,lend_time) and (isnull(u.lend_audit,0)=0 or isnull(t.pass_order,0)=1) and isnull(bonus_number,0)=days and isnull(isfreeze,0)=0 ";

                dt = db.getDataTable(sql);
                sql = string.Empty;
                for (int i = 0; i < dt.Rows.Count; i++)
                {
                    //用户超时15分钟返回冻结金额
                    sql += " exec('[finish_storeOrder] " + dt.Rows[i]["id"] + ",1') ";
                }

                if (!string.IsNullOrEmpty(sql))
                {
                    res = db.ExecuteNonQuery(sql);
                }

                fz.sendResponse("Finish-" + dt.Rows.Count + "-" + res, 1);

                break;
            case "bonus_storeOrder":
                sql = " select * from store_orders with(nolock) where state=1000 and datediff(day,isnull(bonus_time,0),getdate())<>0 and datediff(day,lend_time,getdate())<>0 and isnull(bonus_number,0)<days and isnull(isfreeze,0)=0 ";
                dt = db.getDataTable(sql, pams.ToArray());

                sql = string.Empty;
                for (int i = 0; i < dt.Rows.Count; i++)
                {
                    //派发奖息
                    sql += " exec('[bonus_storeOrder] " + dt.Rows[i]["id"] + "') ";
                }

                if (!string.IsNullOrEmpty(sql))
                {
                    res = db.ExecuteNonQuery(sql);
                }

                fz.sendResponse("Finish-" + dt.Rows.Count + "-" + res, 1);

                break;
            case "hello":
                fz.sendResponse("ok", 1);
                break;
            case "check_timeoutBuyOrder":
                sql = " update buy_list set state=-1 output deleted.* where state=1000 and dateadd(second,1200,update_time)<getdate(); ";
                dt = db.getDataTable(sql);

                pams = new List<SqlParameter>();
                sql = string.Empty;
                for (int i = 0; i < dt.Rows.Count; i++)
                {
                    pams.Add(new SqlParameter("@userid" + i, dt.Rows[i]["userid"]));
                    pams.Add(new SqlParameter("@id" + i, dt.Rows[i]["id"]));
                    pams.Add(new SqlParameter("@orderId" + i, dt.Rows[i]["orderId"]));
                    sql += string.Format(" insert into buy_records values(@userid{0},'超时取消',@id{0},@orderId{0},null,getdate()) ", i);



                    if (dt.Rows[i]["api_orderNo"] + "" != "")
                    {
                        pmlist["notify_state"] = "05";//超时

                        dic = new Dictionary<string, object>();
                        dic.Add("appId", dt.Rows[i]["appid"] + "");
                        dic.Add("orderNo", dt.Rows[i]["api_orderNo"] + "");
                        dic.Add("orderStatus", pmlist["notify_state"] + "");
                        pmlist["notify_text"] = api_Notify("notifyFromTaofanke", dic);


                        pams.Add(new SqlParameter("@notify_text" + i, pmlist["notify_state"] + " - " + pmlist["notify_text"] + ""));
                        sql += string.Format(" update buy_list set notifyOrderStatus=@notify_text{0} where id=@id{0}  ", i);

                    }







                }
                if (dt.Rows.Count > 0)
                {
                    res = db.ExecuteNonQuery(sql, pams.ToArray());
                }

                fz.sendRsp(dt.Rows.Count, "success[" + dt.Rows.Count + "]", "no_order");
                break;
            //case "updateAwardLst":
            //    if (CalculateTimeDifferenceInSeconds(DateTime.Now.ToString("HH:mm:ss"), uConfig.stcdata("index_ranking_uptime").Split('~')[0]) < 0 || CalculateTimeDifferenceInSeconds(DateTime.Now.ToString("HH:mm:ss"), uConfig.stcdata("index_ranking_uptime").Split('~')[1]) > 0)
            //    {
            //        fz.sendResponse("非佣金更新时间 " + uConfig.stcdata("index_ranking_uptime"));
            //    }


            //    temp = GetCurrentTimeRange(uConfig.stcdata("rand_list_user"));



            //    string[] parts = temp.Split('：', '~', ',');
            //    string minValue = "", maxValue = "", seconds = "";
            //    if (parts.Length == 4)
            //    {
            //        minValue = parts[1].Trim(); // 最小值
            //        maxValue = parts[2].Trim(); // 最大值
            //        seconds = parts[3].Trim(); // 秒数
            //    }

            //    Int64 rand_next_time = cae.GetCache<Int64>("rand_next_time");

            //    if (rand_next_time > getTimeStamp())
            //    {
            //        dic.Add("time", (rand_next_time - getTimeStamp()));
            //        fz.sendResponse("时间未满足", -1, dic);
            //    }


            //    cae.SetCache("rand_next_time", getTimeStamp() + Convert.ToInt16(seconds));

            //    pams.Add(new SqlParameter("@number", uConfig.stcdata("rand_list_number")));
            //    pams.Add(new SqlParameter("@minValue", minValue));
            //    pams.Add(new SqlParameter("@maxValue", maxValue));

            //    res = db.ExecuteNonQuery("[updateAwardLst] @number,@minValue,@maxValue", pams.ToArray());
            //    fz.sendResponse("已发包", 1);
            //    break;
            case "redbag_check":
                //fz.sendResponse("装修中");

                cache_dt = chelper.gdt("group_list");
                for (int c = 0; c < cache_dt.Rows.Count; c++)
                {
                    pmlist["message"] = "";

                    pmlist["chatid"] = cache_dt.Rows[c]["chatid"] + "";
                    pmlist["redbag_name"] = cache_dt.Rows[c]["redbag_name"] + "";
                    pmlist["redbag_amount"] = cache_dt.Rows[c]["redbag_amount"] + "";
                    pmlist["redbag_number"] = cache_dt.Rows[c]["redbag_number"] + "";


                    temp = cae.GetCache<string>("current_redbag_" + pmlist["chatid"]);
                    pmlist["bfid"] = cae.GetCache<string>("bufa_redbag_" + pmlist["chatid"]);

                    secondsDifference = 0;
                    string next_time = "";

                    if (!string.IsNullOrEmpty(temp) || !fz.empty("bfid"))
                    {
                        if (!fz.empty("bfid"))
                        {
                            if (pmlist["bfid"] + "" == fz.req("bfid"))
                            {
                                //fz.sendResponse("红包已补发过，请勿重复补发(" + pmlist["bfid"] + ")！");
                                continue;
                            }
                            pmlist["bfid"] = fz.req("bfid");
                            cae.SetCache("bufa_redbag_" + pmlist["chatid"], pmlist["bfid"] + "");

                            temp = "00:00:00~00:00:00";
                        }

                        g = temp.Split('~');
                        string time1 = DateTime.Now.ToString("HH:mm:ss");
                        string time2 = g[1];

                        secondsDifference = CalculateTimeDifferenceInSeconds(time1, time2);

                        if (!fz.empty("bfid"))
                        {
                            secondsDifference = 0;
                        }

                        if (secondsDifference >= 0)
                        {

                            if (fz.empty("bfid"))
                            {
                                cae.RemoteCache("current_redbag_" + pmlist["chatid"]);
                            }

                            if (secondsDifference < 60 * 3)
                            {
                                if (secondsDifference > 30)
                                {
                                    log.WriteLog("redbag_晚包", "[晚发]" + pmlist["chatid"], time2 + "-" + secondsDifference.ToString());
                                }


                                pmlist["send_text"] = "管理员发送";
                                if (!fz.empty("bfid"))
                                {
                                    log.WriteLog("redbag_补发", "[补发]" + pmlist["chatid"], time2 + "-" + secondsDifference.ToString());
                                    if (!fz.empty("text"))
                                    {
                                        pmlist["send_text"] = fz.req("text");
                                    }
                                }

                                log.WriteLog("redbag", "[红包雨即将发送]" + pmlist["chatid"], time2);

                                pams = new List<SqlParameter>();
                                pams.Add(new SqlParameter("@redbag_number", pmlist["redbag_number"] + ""));
                                pams.Add(new SqlParameter("@redbag_amount", pmlist["redbag_amount"] + ""));
                                pams.Add(new SqlParameter("@encode_text", EncodeMessage(pmlist["redbag_name"] + "")));
                                pams.Add(new SqlParameter("@chatid", pmlist["chatid"] + ""));
                                pams.Add(new SqlParameter("@ctime", time2));
                                pams.Add(new SqlParameter("@encode_message", "[红包={tokentokentokentokentokentokentokentokentokentoken}$$" + EncodeMessage(pmlist["redbag_name"] + "") + "$$" + pmlist["send_text"] + "]"));


                                sql = @"
declare @tokenid varchar(32)
select @tokenid=tokenid from redbag_send_records with(nolock) where datediff(day,create_time,getdate())=0 and chatid=@chatid and ctime=@ctime

if(@tokenid is not null)
begin
    select '当前已发送过'+@tokenid as errmsg
    return
end


set @tokenid = LOWER(REPLACE(NEWID(),'-',''))

insert into [redbag_list] values(-1001,@tokenid,'group',@chatid,-1,null,@encode_text,@redbag_amount,0,0,@redbag_number,0,0,getdate(),null)
set @encode_message=replace(@encode_message,'{tokentokentokentokentokentokentokentokentokentoken}',@tokenid)

insert into chat_messages values('group',@chatid,-1001,-1,'redbag',@encode_message,0,0,getdate()) 

insert into redbag_send_records values(@chatid,@tokenid,@ctime,getdate())

select 'SUCCESS' as errmsg,@tokenid as tokenid


";
                                pmlist["redbag_errmsg"] = "";
                                try
                                {
                                    dt = db.getDataTable(sql, pams.ToArray());
                                }
                                catch (Exception ex)
                                {
                                    pmlist["redbag_errmsg"] = ex.Message.ToString();
                                }
                                if (dt.Rows.Count > 0)
                                {
                                    log.WriteLog("redbag", "[红包雨]" + pmlist["chatid"], ToJson(dt));
                                    cae.SetCache("msgtime_" + pmlist["chatid"], getTimeStamp());
                                    cae.RemoteCache("msg_" + pmlist["chatid"]);
                                    if (dt.Rows[0]["errmsg"] + "" == "SUCCESS")
                                    {
                                        cae.SetCache("redbag_poll_token_" + pmlist["chatid"], dt.Rows[0]["tokenid"], DateTime.Now.AddSeconds(15 * 60));
                                    }
                                }
                                else
                                {
                                    log.WriteLog("redbag", "[红包雨]" + pmlist["chatid"], "发送失败:" + pmlist["redbag_errmsg"]);
                                }

                                //发送红包代码
                                //fz.sendResponse("红包雨已发送", 1);
                                continue;
                            }
                            else
                            {
                                log.WriteLog("redbag", "[红包雨已失效]" + pmlist["chatid"], time2 + "-" + secondsDifference.ToString());
                            }

                            //fz.sendResponse("红包雨已失效");
                            continue;

                        }

                        cae.SetCache("next_time_" + pmlist["chatid"], temp);


                        //fz.sendResponse("红包雨时间：" + temp, 1);
                        continue;
                    }
                    else
                    {

                        g = (cache_dt.Rows[c]["redbag_times"] + "").Split('\n');

                        for (int i = 0; i < g.Length; i++)
                        {
                            g2 = g[i].Split('~');
                            if (g2.Length == 2)
                            {
                                secondsDifference = CalculateTimeDifferenceInSeconds(g2[1], g2[0]);
                                if (secondsDifference > 0)
                                {
                                    temp = DateTime.Now.ToString("HH:mm:ss");
                                    secondsDifference = CalculateTimeDifferenceInSeconds(temp, g2[0]);
                                    if (secondsDifference >= 0)
                                    {
                                        //已达到【红包雨】开始时间条件

                                        secondsDifference = CalculateTimeDifferenceInSeconds(g2[1], temp);
                                        if (secondsDifference > 0)
                                        {
                                            //在红包雨时间段内
                                            cae.SetCache("current_redbag_" + pmlist["chatid"], g[i]);

                                            log.WriteLog("redbag", "[进入红包雨时间段]" + pmlist["chatid"], g[i]);

                                            //fz.sendResponse("红包雨开启");
                                            continue;
                                        }
                                    }
                                    else
                                    {
                                        //小于当前时间，下一个开启时间
                                        if (next_time == "")
                                        {
                                            next_time = g[i];
                                        }
                                    }

                                }
                            }
                        }

                    }

                    cae.SetCache("next_time_" + pmlist["chatid"], next_time);


                    temp_dic = new Dictionary<string, object>();
                    temp_dic.Add("chatid", cache_dt.Rows[c]["chatid"] + "");
                    temp_dic.Add("next_time", next_time);
                    temp_dic.Add("message", pmlist["message"] + "");
                    list.Add(temp_dic);
                }

                dic = new Dictionary<string, object>();
                dic.Add("groups", list);
                fz.sendResponse("活动未开始", -1, dic);

                break;
            case "fake_open_redbag":
                fz.sendResponse("暂不开通");

                g = uConfig.stcdata("fake_redbag_money").Split('~');
                for (int i = 0; i < 10; i++)
                {
                    rd = new Random(Guid.NewGuid().GetHashCode());// 使用不同的种子
                    temp = rd.Next(1000000, 9999999).ToString();
                    text_array.Add(temp);

                    ds = open_redBag(new Dictionary<string, object>{                                    
                            {"id", fz.req("token")},
                            {"fakeuser", temp},
                            {"fakemoney_Max", g[1]},
                            {"fakemoney_Min", g[0]}
                        }, fz);
                    dt = ds.Tables[0];
                    log.WriteLog("fake_regbag_Logs", "(open)" + i + ") " + temp, "[" + uConfig.stcdata("fake_redbag_rate") + "]" + ToJson(dt));
                    if (dt.Rows.Count == 0)
                    {
                        break;
                    }

                    Response.Write("【" + (i + 1) + "】领取结果：" + ToJson(dt));

                }

                fz.sendResponse("领取完成");

                break;
            case "check_fake_data":
                //fz.sendResponse("暂不开通");


                //regbag fake
                sql = " SELECT * FROM [redbag_list] with(nolock) where finish_time is null  and number>0 and userList is null and CAST((isnull(fake_number,0)+1)*1.0/number*1.0*100 as int)<=@fake_redbag_rate order by id desc ";
                temp_dt = db.getDataTable(sql, new SqlParameter[]{
                new SqlParameter("@fake_redbag_rate",uConfig.stcdata("fake_redbag_rate"))
                });

                if (temp_dt.Rows.Count == 0)
                {
                    fz.sendResponse("暂无红包雨");
                }



                pmlist["min_number"] = uConfig.stcdata("fake_redbag_number").Split('~')[0] + "";
                pmlist["max_number"] = uConfig.stcdata("fake_redbag_number").Split('~')[1] + "";


                for (int i = 0; i < Convert.ToInt16(rd.Next(Convert.ToInt16(pmlist["min_number"]), Convert.ToInt16(pmlist["max_number"]))); i++)
                {
                    rd = new Random(Guid.NewGuid().GetHashCode());// 使用不同的种子
                    temp = rd.Next(1000000, 9999999).ToString();
                    text_array.Add(temp);
                }


                pmlist["min_ts"] = uConfig.stcdata("fake_redbag_ts").Split('~')[0] + "";
                pmlist["max_ts"] = uConfig.stcdata("fake_redbag_ts").Split('~')[1] + "";

                dic["wait"] = "";
                dic["success"] = "";
                for (int i = 0; i < temp_dt.Rows.Count; i++)
                {
                    if (cae.GetCache<string>("fake_redbag_" + temp_dt.Rows[i]["tokenid"]) == "1")
                    {
                        dic["wait"] += temp_dt.Rows[i]["tokenid"] + ",";
                        continue;
                    }

                    g = uConfig.stcdata("fake_redbag_money").Split('~');
                    //fz.sendResponse(g[0]+"-"+g[1]);



                    temp_array = text_array;
                    ShuffleList(temp_array);

                    pmlist["total_number"] = 0;

                    for (int t = 0; t < temp_array.Count; t++)
                    {
                        temp = temp_array[t];
                        ds = open_redBag(new Dictionary<string, object>{                                    
                            {"id", temp_dt.Rows[i]["tokenid"] },
                            {"fakeuser", temp},
                            {"fakemoney_Max", g[1]},
                            {"fakemoney_Min", g[0]}
                        }, fz);

                        dt = ds.Tables[0];
                        log.WriteLog("fake_regbag_Logs", t + ") " + temp, "[" + uConfig.stcdata("fake_redbag_rate") + "]" + ToJson(dt));
                        if (dt.Rows.Count == 0)
                        {
                            break;
                        }

                        if (Convert.ToDouble(dt.Rows[0]["next_receive_rate"] + "") > Convert.ToDouble(uConfig.stcdata("fake_redbag_rate")))
                        {
                            break;
                        }

                        pmlist["total_number"] = Convert.ToInt16(pmlist["total_number"]) + 1;

                    }
                    dic["success"] += temp_dt.Rows[i]["tokenid"] + "-" + pmlist["total_number"] + ",";


                    cae.SetCache("fake_redbag_" + temp_dt.Rows[i]["tokenid"], "1", DateTime.Now.AddSeconds(rd.Next(Convert.ToInt16(pmlist["min_ts"]), Convert.ToInt16(pmlist["max_ts"]))));

                    //temp_val = dt.Rows[0]["errmsg"] + "";
                    //if (temp_val == "SUCCESS")
                    //{
                    //    cae.SetCache("fake_redbag_" + temp_dt.Rows[i]["tokenid"], "1", DateTime.Now.AddSeconds(rd.Next(Convert.ToInt16(pmlist["min_ts"]), Convert.ToInt16(pmlist["max_ts"]))));
                    //}

                }
                //cae.SetCache("fake_redbag", string.Join(",", text_array));


                fz.sendResponse("SUCCESS", 0, dic);



                break;
            case "check_fake_share":
                fz.sendResponse("暂停使用");

                Response.Write("\r\n\r\n--------\r\n" + " 下次复晒时间 = " + cae.GetCache<string>("next_reshare_time") + "\r\n--------\r\n\r\n");

                //fz.sendResponse("功能更新中");
                pmlist["group_cid"] = uConfig.stcdata("redbag_groups");

                pmlist["group_nospeak"] = cae.GetCache<string>("group_nospeak_" + pmlist["group_cid"]);
                if (string.IsNullOrEmpty(pmlist["group_nospeak"] + ""))
                {
                    sql = " select * from group_list with(nolock) where chatid=@chatid ";
                    dt = db.getDataTable(sql, new SqlParameter[]{
                        new SqlParameter("@chatid",pmlist["group_cid"])
                    });
                    pmlist["group_nospeak"] = dt.Rows[0]["no_speaking"] + "";
                    cae.SetCache("group_nospeak_" + pmlist["group_cid"], pmlist["group_nospeak"] + "");
                }
                if (pmlist["group_nospeak"] + "" == "1")
                {
                    fz.sendResponse("全体人员禁言中!!");
                }


                if (CalculateTimeDifferenceInSeconds(DateTime.Now.ToString("HH:mm:ss"), uConfig.stcdata("ranking_stop_time")) > 0)
                {
                    cae.RemoteCache("updateTable_fake_share");
                    fz.sendResponse("今日排名已经截止，请明日再分享！");
                }


                if (CalculateTimeDifferenceInSeconds(DateTime.Now.ToString("HH:mm:ss"), uConfig.stcdata("fake_share_time").Split('~')[0]) < 0 || CalculateTimeDifferenceInSeconds(DateTime.Now.ToString("HH:mm:ss"), uConfig.stcdata("fake_share_time").Split('~')[1]) > 0)
                {
                    fz.sendResponse("非晒单时间 " + uConfig.stcdata("fake_share_time"));
                }

                pmlist["taks"] = "";


                // 创建一个新的 DataTable
                DataTable updateTable = new DataTable("updateTable");

                updateTable = cae.GetCache<DataTable>("updateTable_fake_share");






                // 检测是否定时晒单

                Response.Write("***************** 晒单定时列表：\r\n");

                pmlist["next_share_time"] = cae.GetCache<string>("next_share_time");
                Response.Write("分享时间 = " + pmlist["next_share_time"] + "\r\n");

                if (!string.IsNullOrEmpty(pmlist["next_share_time"] + ""))
                {
                    string time1 = DateTime.Now.ToString("HH:mm:ss");
                    string time2 = pmlist["next_share_time"] + "";

                    secondsDifference = CalculateTimeDifferenceInSeconds(time1, time2);

                    if (secondsDifference >= 0 && secondsDifference < 60)
                    {
                        cae.SetCache("lastshare_time", pmlist["next_share_time"]);
                        cae.RemoteCache("next_share_time");












                        //获取虚拟号列表
                        pmlist["temp_sql"] = " select * from [share_reward] with(nolock) where datediff(day,create_time,getdate())=0 and userid=-1 and isnull(fake_number,0)<@fake_upshare_number and isnull(fake_amount,0)<@fake_upshare_amount order by newid() ";
                        dt = db.getDataTable(pmlist["temp_sql"] + "", new SqlParameter[]{
                            new SqlParameter("@fake_upshare_number",uConfig.stcdata("fake_upshare_number")),
                            new SqlParameter("@fake_upshare_amount",uConfig.stcdata("fake_upshare_amount"))
                        });

                        pmlist["fake_user"] = dt.Rows[0]["fake_user"] + "";
                        pmlist["fake_amount"] = "0";
                        pmlist["fake_number"] = "0";
                        pmlist["fake_reward"] = "0";
                        pmlist["fake_total_reward"] = "0";// 自动叠加 fake_reward 所以update此参数可忽略
                        pmlist["share_time"] = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");

                        updateTable.Rows.Add("reshare", "", pmlist["fake_user"], pmlist["fake_amount"], pmlist["fake_number"], pmlist["fake_reward"], pmlist["fake_total_reward"], pmlist["share_time"]);














                        Response.Write(pmlist["next_share_time"] + " 订单已晒出 ❤❤❤" + "\r\n");
                    }
                }



                g = uConfig.stcdata("fake_share_timelist").Split('\n');


                pmlist["next_share_time"] = "";
                for (int i = 0; i < g.Length; i++)
                {
                    if (g[i].Split(':').Length != 3)
                    {
                        continue;
                    }

                    temp = DateTime.Now.ToString("HH:mm:ss");
                    secondsDifference = CalculateTimeDifferenceInSeconds(temp, g[i]);



                    Response.Write("" + g[i] + "|" + secondsDifference + (secondsDifference >= 0 ? "【超时】" : "[等待]") + "\r\n");

                    if (secondsDifference >= 0)
                    {
                        //无需操作
                    }
                    else
                    {
                        //小于当前时间，下一个开启时间
                        if (pmlist["next_share_time"] + "" == "")
                        {
                            Response.Write("↑↑↑↑↑↑↑↑↑{倒计时}" + "\r\n");
                            pmlist["next_share_time"] = g[i];
                        }
                    }
                }

                if (cae.GetCache<string>("next_share_time") != pmlist["next_share_time"] + "")
                {
                    cae.SetCache("next_share_time", pmlist["next_share_time"]);
                }

                Response.Write("下次分享时间 = " + pmlist["next_share_time"] + "\r\n");
                Response.Write("***************** \r\n");



                // 检测结束
















                if (updateTable == null || updateTable.Rows.Count == 0)
                {

                    Response.Write(" updateTable = 暂无订单数据\r\n");

                    updateTable = new DataTable("updateTable");

                    // 添加列
                    updateTable.Columns.Add("type", typeof(string));
                    updateTable.Columns.Add("p1", typeof(string));
                    updateTable.Columns.Add("fake_user", typeof(string));
                    updateTable.Columns.Add("fake_amount", typeof(Decimal));
                    updateTable.Columns.Add("fake_number", typeof(int));
                    updateTable.Columns.Add("fake_reward", typeof(Decimal));
                    updateTable.Columns.Add("fake_total_reward", typeof(Decimal));
                    updateTable.Columns.Add("create_time", typeof(DateTime));


                }
                else
                {
                    Response.Write(" updateTable = " + ToJson(updateTable) + "\r\n");
                    Response.Write("------------------------------------------" + "\r\n");
                    Response.Write("类型 | 金额 | 时间" + "\r\n\r\n");

                    for (int upindex = 0; upindex < updateTable.Rows.Count; upindex++)
                    {
                        if (Convert.ToDouble(updateTable.Rows[upindex]["fake_amount"]) == 0)
                        {
                            continue;
                        }
                        Response.Write((updateTable.Rows[upindex]["type"] + "" == "add" ? "*新增" : "复晒") + " | " + updateTable.Rows[upindex]["fake_amount"] + " | " + Convert.ToDateTime(updateTable.Rows[upindex]["create_time"] + "").ToString("HH:mm:ss") + "\r\n");
                    }

                    //开始筛选UpdateSql

                    DataRow[] rowsToDelete = updateTable.Select(" create_time<='" + DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss") + "' ");

                    if (rowsToDelete.Length > 0)
                    {
                        pmlist["taks"] += "new/update,";
                    }

                    pmlist["unread_number"] = 0;

                    pmlist["stop_time"] = DateTime.Now.ToString("yyyy-MM-dd ") + uConfig.stcdata("ranking_stop_time");

                    foreach (DataRow row in rowsToDelete)
                    {
                        if (Convert.ToDateTime(row["create_time"] + "").ToString("yyyy/MM/dd") == DateTime.Now.ToString("yyyy/MM/dd"))
                        {
                            if (row["type"] + "" == "add")
                            {
                                if (Convert.ToDouble(row["fake_amount"]) == 0)
                                {
                                    //无任务不可分享
                                    updateTable.Rows.Remove(row);
                                    continue;
                                }

                                pmlist["temp_sql"] = " insert into share_reward values(-1,getdate(),'" + row["fake_user"] + "'," + row["fake_amount"] + "," + row["fake_number"] + "," + row["fake_reward"] + "," + row["fake_total_reward"] + "+" + row["fake_reward"] + ",0) ";
                            }
                            else if (row["type"] + "" == "update")
                            {
                                pmlist["temp_sql"] = " update share_reward set fake_amount=fake_amount+" + row["fake_amount"] + ",fake_number=fake_number+" + row["fake_number"] + ",fake_reward=fake_reward+" + row["fake_reward"] + ",fake_total_reward=fake_total_reward+" + row["fake_reward"] + ",fake_update_number=isnull(fake_update_number,0)+1 where fake_user='" + row["fake_user"] + "'  and isnull(fake_update_number,0)+1<=@fake_upshare_number and isnull(fake_amount,0)+" + row["fake_amount"] + "<@fake_upshare_amount  ";
                            }
                            else
                            {
                                pmlist["temp_sql"] = "";
                            }

                            pmlist["temp_sql"] += string.Format(@"

select ROW_NUMBER() OVER (ORDER BY daily_amount desc) AS ranking_number,* from 
(
select sr.*,(case when sr.userid=-1 then sr.fake_user else u.parent_code end) as parent_code,isnull(dd.daily_count,isnull(sr.fake_number,0)) as daily_count,isnull(dd.daily_amount,isnull(sr.fake_amount,0)) as daily_amount,isnull(dd.daily_award_amount,isnull(sr.fake_reward,0)) as daily_award_amount,isnull(u.trans_amount,0) as trans_amount,isnull(u.reward_amount,isnull(sr.fake_total_reward,0)) as reward_amount from share_reward sr with(nolock) left join (SELECT userid,count(0) as daily_count,SUM(orderAmt) as daily_amount,SUM(award_amount) as daily_award_amount FROM [transport_orders]  with(nolock) where DATEDIFF(day,receive_time,GETDATE())=0 and DATEDIFF(day,finish_time,GETDATE())=0 and finish_time<='{0}' and state=1 group by userid)dd on sr.userid=dd.userid left join accounts u with(nolock) on sr.userid=u.id where datediff(day,sr.create_time,getdate())=0
)t 

", pmlist["stop_time"] + "");

                            if (row["type"] + "" == "reshare")
                            {
                                string aa = "";
                            }

                            dt = db.getDataTable(pmlist["temp_sql"] + "", new SqlParameter[]{
                                new SqlParameter("@fake_upshare_number",uConfig.stcdata("fake_upshare_number")),
                                new SqlParameter("@fake_upshare_amount",uConfig.stcdata("fake_upshare_amount"))
                            });
                            dic = new Dictionary<string, object>();
                            dic["ranking_number"] = "**";
                            dic["type"] = row["type"];



                            //dic["daily_amount"] = row["fake_amount"];
                            //dic["daily_count"] = row["fake_number"];
                            //dic["daily_award_amount"] = row["fake_reward"];
                            //dic["total_amount"] = row["fake_total_reward"];
                            //dic["reward_amount"] = row["fake_total_reward"];


                            dic["daily_amount"] = "***";
                            dic["daily_count"] = "***";
                            dic["daily_award_amount"] = "***";
                            dic["total_amount"] = "***";
                            dic["reward_amount"] = "***";

                            if (dt.Rows.Count > 0)
                            {
                                for (int i = 0; i < dt.Rows.Count; i++)
                                {
                                    if (dt.Rows[i]["parent_code"] + "" == row["fake_user"] + "")
                                    {
                                        dic["ranking_number"] = dt.Rows[i]["ranking_number"] + "";
                                        dic["daily_amount"] = dt.Rows[i]["daily_amount"] + "";
                                        dic["daily_count"] = dt.Rows[i]["daily_count"] + "";
                                        dic["daily_award_amount"] = dt.Rows[i]["daily_award_amount"] + "";
                                        dic["total_amount"] = dt.Rows[i]["trans_amount"] + "";
                                        dic["reward_amount"] = dt.Rows[i]["reward_amount"] + "";
                                        break;
                                    }
                                }
                            }

                            dic["date"] = DateTime.Now.ToString("yyyy/MM/dd");
                            dic["name"] = row["fake_user"];

                            if ((dic["name"] + "").Length >= 7)
                            {
                                dic["name"] = (dic["name"] + "").Substring(0, 2) + "***" + (dic["name"] + "").Substring((dic["name"] + "").Length - 2, 2);
                            }


                            dic["encode_message"] = "[卡片=" + JsonMapper.ToJson(dic) + "]";


                            pmlist["unread_number"] = Convert.ToInt32(pmlist["unread_number"]) + 1;

                            if (row["type"] + "" == "add" || row["type"] + "" == "reshare" || (row["type"] + "" == "update" && get_random("0", "100") <= Convert.ToInt32(uConfig.stcdata("fake_share_rerate"))))
                            {
                                if (row["type"] + "" == "update")
                                {

                                    pmlist["next_reshare_time"] = cae.GetCache<string>("next_reshare_time");
                                    if (!string.IsNullOrEmpty(pmlist["next_reshare_time"] + ""))
                                    {
                                        //更新排行榜重复分享的时间间隔没有到
                                        updateTable.Rows.Remove(row);
                                        continue;
                                    }
                                    cae.SetCache("next_reshare_time", DateTime.Now.AddSeconds(get_random(uConfig.stcdata("fake_reshare_ts").Split('~')[0], uConfig.stcdata("fake_reshare_ts").Split('~')[1])).ToString("yyyy-MM-dd HH:mm:ss"));
                                }



                                sql += " insert into chat_messages values('group','" + uConfig.stcdata("redbag_groups") + "',-1,-1,'card','" + dic["encode_message"] + "',0,0,getdate())  ";

                            }

                        }
                        updateTable.Rows.Remove(row);
                    }


                    cae.SetCache("updateTable_fake_share", updateTable);

                    if (!string.IsNullOrEmpty(sql))
                    {
                        sql += " update [chat_list] set unread_number=unread_number+" + pmlist["unread_number"] + ",last_message='[晒出奖励]',update_time=getdate() where chatid='" + uConfig.stcdata("redbag_groups") + "' ";
                        db.ExecuteNonQuery(sql);
                    }


                    Response.Write("\r\n\r\n--------\r\n" + " sql = " + sql + "\r\n--------\r\n\r\n");

                }




                g = uConfig.stcdata("fake_share_money").Split('~');//晒单金额

                if (g.Length != 2 || !IsNumeric(g[0]) || !IsNumeric(g[1]))
                {
                    fz.sendResponse("晒单金额有误");
                }


                // ---------------------
                //
                //     【新号晒单】
                //
                // ---------------------

                temp = cae.GetCache<string>("share_new");


                Response.Write(" 下次晒单 = " + temp + "\r\n");

                if (string.IsNullOrEmpty(temp) || fz.req("share_new") == "1")
                {
                    pmlist["share_rec_time"] = DateTime.Now.AddSeconds(get_random(uConfig.stcdata("fake_greate_ts").Split('~')[0], uConfig.stcdata("fake_greate_ts").Split('~')[1]));
                    cae.SetCache("share_new", Convert.ToDateTime(pmlist["share_rec_time"] + "").ToString("yyyy-MM-dd HH:mm:ss"), Convert.ToDateTime(pmlist["share_rec_time"] + ""));

                    for (int i = 0; i < get_random(uConfig.stcdata("fake_share_number").Split('~')[0], uConfig.stcdata("fake_share_number").Split('~')[1]); i++)
                    {
                        pmlist["fake_user"] = get_random("1000000", "9999999").ToString();
                        pmlist["fake_amount"] = "0";
                        pmlist["fake_number"] = "0";
                        pmlist["fake_reward"] = "0";
                        pmlist["fake_total_reward"] = get_random(uConfig.stcdata("fake_share_reward").Split('~')[0], uConfig.stcdata("fake_share_reward").Split('~')[1]);
                        pmlist["share_time"] = DateTime.Now.AddSeconds(get_random(uConfig.stcdata("fake_share_ts").Split('~')[0], uConfig.stcdata("fake_share_ts").Split('~')[1])).ToString("yyyy-MM-dd HH:mm:ss");
                        //开始晒单(新增)
                        if (CalculateTimeDifferenceInSeconds(DateTime.Now.ToString("HH:mm:ss"), "00:30:00") > 0)
                        {
                            //for (int t = 0; t < get_random(uConfig.stcdata("fake_share_number").Split('~')[0], uConfig.stcdata("fake_share_number").Split('~')[1]); t++)
                            //{
                            //}

                            temp = get_random(g[0], g[1]).ToString();
                            pmlist["fake_amount"] = Convert.ToInt32(pmlist["fake_amount"]) + Convert.ToInt32(temp);

                            if (Convert.ToInt32(temp) > 0)
                            {
                                pmlist["fake_number"] = Convert.ToInt32(pmlist["fake_number"]) + 1;
                            }

                            pmlist["fake_reward"] = Convert.ToInt32(pmlist["fake_amount"]) / 100 * Convert.ToInt32(uConfig.stcdata("fake_reward_rate"));

                        }

                        updateTable.Rows.Add("add", "", pmlist["fake_user"], pmlist["fake_amount"], pmlist["fake_number"], pmlist["fake_reward"], pmlist["fake_total_reward"], pmlist["share_time"]);
                    }

                    pmlist["taks"] += "push_new,";

                }

                // --------------------------
                //
                //     【老号晒单更新】
                //
                // --------------------------


                temp = cae.GetCache<string>("share_update");

                Response.Write(" 下次更新 = " + temp + "\r\n");

                if (string.IsNullOrEmpty(temp) || fz.req("share_update") == "1")
                {
                    pmlist["fake_share_upts_time"] = DateTime.Now.AddSeconds(Convert.ToInt32(uConfig.stcdata("fake_share_upts").Split('~')[1]));
                    cae.SetCache("share_update", Convert.ToDateTime(pmlist["fake_share_upts_time"] + "").ToString("yyyy-MM-dd HH:mm:ss"), Convert.ToDateTime(pmlist["fake_share_upts_time"] + ""));



                    //获取虚拟号列表
                    sql = " select * from [share_reward] with(nolock) where datediff(day,create_time,getdate())=0 and userid=-1 and isnull(fake_number,0)<@fake_upshare_number and isnull(fake_amount,0)<@fake_upshare_amount ";
                    dt = db.getDataTable(sql, new SqlParameter[]{
                        new SqlParameter("@fake_upshare_number",uConfig.stcdata("fake_upshare_number")),
                        new SqlParameter("@fake_upshare_amount",uConfig.stcdata("fake_upshare_amount"))
                    });

                    for (int d = 0; d < dt.Rows.Count; d++)
                    {
                        if (get_random("0", "100") <= Convert.ToInt32(uConfig.stcdata("fake_share_uprate")))
                        {


                            pmlist["fake_user"] = dt.Rows[d]["fake_user"] + "";
                            pmlist["fake_amount"] = "0";
                            pmlist["fake_number"] = "0";
                            pmlist["fake_reward"] = "0";
                            pmlist["fake_total_reward"] = "0";// 自动叠加 fake_reward 所以update此参数可忽略
                            pmlist["share_time"] = DateTime.Now.AddSeconds(get_random(uConfig.stcdata("fake_share_upts").Split('~')[0], uConfig.stcdata("fake_share_upts").Split('~')[1])).ToString("yyyy-MM-dd HH:mm:ss");
                            //更新晒单
                            if (CalculateTimeDifferenceInSeconds(DateTime.Now.ToString("HH:mm:ss"), "00:15:00") > 0)
                            {
                                //for (int t = 0; t < get_random(uConfig.stcdata("fake_share_number").Split('~')[0], uConfig.stcdata("fake_share_number").Split('~')[1]); t++)
                                //{
                                //}

                                temp = g[get_random("0", (g.Length - 1).ToString())];
                                pmlist["fake_amount"] = Convert.ToInt32(pmlist["fake_amount"]) + Convert.ToInt32(temp);

                                if (Convert.ToInt32(temp) > 0)
                                {
                                    pmlist["fake_number"] = Convert.ToInt32(pmlist["fake_number"]) + 1;
                                }

                                pmlist["fake_reward"] = Convert.ToInt32(pmlist["fake_amount"]) / 100 * Convert.ToInt32(uConfig.stcdata("fake_reward_rate"));

                            }

                            updateTable.Rows.Add("update", "", pmlist["fake_user"], pmlist["fake_amount"], pmlist["fake_number"], pmlist["fake_reward"], pmlist["fake_total_reward"], pmlist["share_time"]);





                        }
                    }



                    pmlist["taks"] += "push_update,";


                }



                cae.SetCache("updateTable_fake_share", updateTable);
                fz.sendResponse("SUCCESS_" + pmlist["taks"], 1);


                break;
            //case "test_getredbag":

            //    temp = cae.GetCache<string>("redbag_poll_token");
            //    if (string.IsNullOrEmpty(temp))
            //    {
            //        fz.sendResponse("红包雨已结束或未开始");
            //    }


            //    temp_dt = db.getDataTable("select * from accounts with(nolock) order by newid() ");

            //    for (int i = 0; i < temp_dt.Rows.Count; i++)
            //    {

            //        ds = open_redBag(new Dictionary<string, object>{                                    
            //            {"id", temp},
            //            {"userid", temp_dt.Rows[i]["id"]+""}
            //        }, fz);


            //        dt = ds.Tables[0];
            //        if (dt.Rows.Count == 0)
            //        {
            //            fz.sendResponse("红包参数错误");
            //        }

            //        pmlist["return_message"] = dt.Rows[0]["errmsg"] + "";
            //        if (pmlist["return_message"] + "" != "SUCCESS")
            //        {
            //            fz.sendResponse(pmlist["return_message"] + "");
            //        }

            //        if (Convert.ToDouble(dt.Rows[0]["user_amount_receive"]) == -1)
            //        {
            //            fz.sendResponse("红包领取结束{" + i + "}");
            //        }
            //    }

            //    fz.sendResponse("SUCCESS");


            //    break;
            case "usdt_budan":

                arrayList = uConfig.stcdata("ubd_money").Split(',');//U补单金额

                sql = " declare @tb table(address varchar(50),payment_name varchar(50),p1 varchar(50),amount decimal(18,2)) ";

                //g = uConfig.stcdata("ubd_address").Split('\n');
                //for (int i = 0; i < g.Length; i++)
                //{
                //    g2 = g[i].Split(new string[] { "----" }, StringSplitOptions.None);
                //    if (g2.Length == 3)
                //    {

                //        pams.Add(new SqlParameter("@usdt" + i, g2[0]));
                //        pams.Add(new SqlParameter("@payment_name" + i, g2[1]));
                //        pams.Add(new SqlParameter("@p1" + i, g2[2]));
                //        pams.Add(new SqlParameter("@amount" + i, arrayList[get_random("0", (arrayList.Length - 1).ToString())]));

                //        sql += string.Format(" insert into @tb values(@usdt{0},@payment_name{0},cast(@p1{0} as decimal(18,2)),@amount{0}) ", i);
                //    }
                //}

                for (int a = 0; a < arrayList.Length; a++)
                {
                    pams.Add(new SqlParameter("@amount" + a, arrayList[a]));
                }

                dt = chelper.gdt("ubd_address");
                for (int i = 0; i < dt.Rows.Count; i++)
                {
                    pams.Add(new SqlParameter("@usdt" + i, dt.Rows[i]["address"] + ""));
                    pams.Add(new SqlParameter("@payment_name" + i, dt.Rows[i]["name"] + ""));
                    pams.Add(new SqlParameter("@p1" + i, dt.Rows[i]["p1"] + ""));
                    //pams.Add(new SqlParameter("@amount" + i, arrayList[get_random("0", (arrayList.Length - 1).ToString())]));


                    for (int a = 0; a < arrayList.Length; a++)
                    {
                        sql += string.Format(" insert into @tb values(@usdt{0},@payment_name{0},cast(@p1{0} as decimal(18,2)),@amount{1}) ", i, a);
                    }
                }




                sql += @" 



delete @tb where (address+'_'+CAST(CAST(amount as decimal(18,2)) as varchar(30))) in (select payment_bankid+'_'+CAST(CAST(amount as decimal(18,2)) as varchar(30)) from [buy_list] with(nolock) where payment_type='USDT' and (state=0 or state=1000) and payment_bankid in (select address from @tb))
    

select * from @tb
           
declare @total_number int
set @total_number=0 
select @total_number=count(0) from @tb

while @total_number>0
begin
        
    declare @address varchar(50)
    declare @payment_name varchar(50)
    declare @p1 varchar(50)
    declare @amount decimal(18,2)

    select top 1 @address=address,@payment_name=payment_name,@p1=p1,@amount=amount from @tb
    delete @tb where address=@address


    -- 生成订单号 
	DECLARE @orderId VARCHAR(38);
    EXEC GenerateUniqueBuyOrderId @GeneratedCode = @orderId OUTPUT;


    -- 添加数据 
	insert into buy_list(orderId,upload_servid,amount,payment_type,payment_name,payment_bankid,payment_bankname,payment_city,state,create_time,p1,servid) values(@orderId,-99,@amount,'USDT',@payment_name,@address,'','',0,getdate(),@p1,0) 


        
    select @total_number=count(0) from @tb

end


                ";

                dt = db.getDataTable(sql, pams.ToArray());

                dic = new Dictionary<string, object>();
                dic.Add("list", ToJson(dt));
                fz.sendResponse("SUCCESS", 1, dic);

                break;
            case "budan_storeOrder":
                sql = @"

declare @interest decimal(18,2)
declare @temp_amount decimal(18,2)
declare @days int
declare @lend_class nvarchar(20)
declare @new_orderId VARCHAR(38);
declare @current_index int
declare @total_number int

declare @lxTable table(lend_class varchar(20),name nvarchar(50),amount decimal(18,2),interest decimal(18,2),days int)

";

                g2 = uConfig.stcdata("lend_class").Split('\n');

                for (int t = 0; t < g2.Length; t++)
                {
                    pams.Add(new SqlParameter("@lend_class" + t, g2[t]));
                    arrayList = uConfig.stcdata("rl_" + g2[t]).Split('\n');//LX天数规则
                    for (int i = 0; i < arrayList.Length; i++)
                    {
                        g = arrayList[i].Split('~');
                        if (g.Length == 4 && IsNumeric(g[1]) && IsNumeric(g[2]) && IsNumeric(g[3]))
                        {
                            sql += string.Format(" insert into @lxTable values(@lend_class{0},'" + g[0] + "'," + g[1] + "," + g[2] + "," + g[3] + ") ", t);
                        }

                    }

                }


                //添加订单
                sql += @" 
    declare @store_list table(id int,lend_class varchar(20),name nvarchar(50),amount decimal(18,2))
    insert into @store_list select id,isnull(lend_class,''),name,amount from store_orders with(nolock) where state=0


    set @total_number=0
    delete @lxTable where (lend_class+'_'+cast(amount as varchar(20))) in (select lend_class+'_'+cast(amount as varchar(20)) from @store_list)
    select @total_number=count(0) from @lxTable

    if(@total_number<=0)
    begin
        -- select '无需补单' as errmsg
        return
    end
    


    set @current_index=0
    while @current_index<@total_number
    begin
        set @current_index=@current_index+1
        -- 生成订单号 
        set @new_orderId=null
        EXEC GenerateStoreOrder @GeneratedCode = @new_orderId OUTPUT;
            
        declare @newid int
        declare @new_name nvarchar(50)
        set @newid=-1

        select top 1 @new_name=name,@lend_class=lend_class,@temp_amount=amount,@days=days,@interest=interest from @lxTable order by newid()
        delete @lxTable where amount=@temp_amount and lend_class=@lend_class


        -- select top 1 @newid=id,@new_name=name from store_list with(nolock) where name not in (select name from @store_list) order by NEWID()
        -- insert into @store_list values(@newid,@lend_class,@new_name,@temp_amount)

        insert into store_orders(orderNo,storeId,name,amount,days,interest,create_time,update_time,state,lend_class) values(@new_orderId,@newid,@new_name,@temp_amount,@days,@interest,getdate(),getdate(),0,@lend_class)

        
         select '【'+@lend_class+'】 '+@new_name+' '+cast(@temp_amount as varchar(50))+'（'+cast(@interest as varchar(50))+'%） >> '+@new_orderId as orderId

    end


";


                ds = db.getDataSet(sql, pams.ToArray());

                if (ds.Tables.Count == 0)
                {
                    fz.sendResponse("暂无新增订单");
                }


                for (int i = 0; i < ds.Tables.Count; i++)
                {
                    dt = ds.Tables[i];
                    text_array.Add(dt.Rows[0]["orderId"] + "");
                }
                dic.Add("orders", text_array);

                fz.sendResponse("创建成功", 1, dic);


                break;
            case "fake_html_data":
                fz.sendResponse("暂不处理");

                dt = chelper.gdt("group_list");
                for (int i = 0; i < dt.Rows.Count; i++)
                {
                    sList.Add(dt.Rows[i]["chatid"] + "");
                }



                // 创建一个新的 DataTable
                DataTable chatHtmlTable = new DataTable("chatHtmlTable");

                chatHtmlTable = cae.GetCache<DataTable>("chatHtmlTable");

                if (chatHtmlTable == null || chatHtmlTable.Rows.Count == 0)
                {

                    Response.Write(" chatHtmlTable = 暂无需更新数据\r\n");

                    chatHtmlTable = new DataTable("chatHtmlTable");

                    // 添加列
                    chatHtmlTable.Columns.Add("type", typeof(string));
                    chatHtmlTable.Columns.Add("text", typeof(string));
                    chatHtmlTable.Columns.Add("create_time", typeof(DateTime));

                }
                else
                {

                    Response.Write(" updateTable = " + ToJson(chatHtmlTable) + "\r\n");
                    Response.Write("------------------------------------------" + "\r\n");
                    Response.Write("类型 | 消息 | 时间" + "\r\n\r\n");

                    for (int upindex = 0; upindex < chatHtmlTable.Rows.Count; upindex++)
                    {
                        Response.Write(chatHtmlTable.Rows[upindex]["type"] + " | " + chatHtmlTable.Rows[upindex]["text"] + " | " + Convert.ToDateTime(chatHtmlTable.Rows[upindex]["create_time"] + "").ToString("HH:mm:ss") + "\r\n\r\n");
                    }
                    Response.Write("------------------------------------------" + "\r\n");
                }


                rows = chatHtmlTable.Select(" create_time<='" + DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss") + "' ");
                foreach (DataRow row in rows)
                {
                    Response.Write("【" + row["type"] + "】新消息 = " + row["text"] + "\r\n");

                    pushGroupMessage(sList[get_random("0", (sList.Count - 1).ToString())], "-1", "html", "[" + row["type"] + "]", row["text"] + "");

                    chatHtmlTable.Rows.Remove(row);
                }


                //string[] tps_list = "task,wheel,yjlq".Split(',');
                string[] tps_list = "task,wheel".Split(',');

                for (int tps_index = 0; tps_index < tps_list.Length; tps_index++)
                {
                    pmlist["tps"] = tps_list[tps_index];

                    string[] timeList = uConfig.stcdata(pmlist["tps"] + "_html_time").Split(',');
                    bool ingenerateTime = false;

                    for (int i = 0; i < timeList.Length; i++)
                    {
                        string[] timeArray = timeList[i].Split('~');
                        if (CalculateTimeDifferenceInSeconds(DateTime.Now.ToString("HH:mm:ss"), timeArray[0]) < 0 || CalculateTimeDifferenceInSeconds(DateTime.Now.ToString("HH:mm:ss"), timeArray[1]) > 0)
                        {
                        }
                        else
                        {
                            ingenerateTime = true;
                            break;
                        }
                    }

                    if (!ingenerateTime)
                    {
                        //非任务生成时间
                        Response.Write("【" + pmlist["tps"] + "】非生成时间 " + uConfig.stcdata(pmlist["tps"] + "_html_time") + "\r\n");
                    }
                    else
                    {
                        if (cae.GetCache<Int64>(pmlist["tps"] + "_html_next_time") < getTimeStamp())
                        {
                            cae.SetCache(pmlist["tps"] + "_html_next_time", getTimeStamp() + get_random(uConfig.stcdata(pmlist["tps"] + "_html_ts").Split('~')[0], uConfig.stcdata(pmlist["tps"] + "_html_ts").Split('~')[1]));

                            for (int i = 0; i < get_random(uConfig.stcdata(pmlist["tps"] + "_html_count").Split('~')[0], uConfig.stcdata(pmlist["tps"] + "_html_count").Split('~')[1]); i++)
                            {
                                List<string> moneyList = new List<string>();
                                pmlist["uid"] = get_random("1000000", "9999999");

                                switch (pmlist["tps"] + "")
                                {
                                    case "task":
                                        pmlist["type"] = get_random("0", "8") < 2 ? "xrhl" : "yrsw";

                                        DataTable taskTable = selectDateTable(chelper.gdt("task_list"), " from_type='" + pmlist["type"] + "' and state=1 ");

                                        int total_number = taskTable.Rows.Count;
                                        for (int t = 0; t < total_number; t++)
                                        {
                                            for (int k = 0; k < (total_number - t) * 5 - 4; k++)
                                            {
                                                moneyList.Add(taskTable.Rows[t]["reward_amount"] + "");
                                            }
                                        }



                                        pmlist["type"] = ((pmlist["type"] + "") == "xrhl" ? "新手豪礼" : (pmlist["type"] + "") == "yrsw" ? "推荐有好礼" : "任务奖励");
                                        pmlist["text"] = "恭喜 " + getStarNick(pmlist["uid"] + "", "<fuwu>") + " 领取【" + pmlist["type"] + "】" + Convert.ToDouble(moneyList[get_random("0", (moneyList.Count - 1).ToString())] + "").ToString("0") + "<img src='/static/images/coin.png' alt='' height='15' width='15' style='margin-left: 5px;'>";


                                        break;
                                    case "wheel":
                                        pmlist["type"] = "转盘奖励";
                                        g = uConfig.stcdata("wheel_reward_rules").Split('\n');
                                        int money_number = g.Length;
                                        for (int m = 0; m < money_number; m++)
                                        {
                                            g2 = g[m].Split('~');
                                            if (g2.Length == 3)
                                            {
                                                for (int t = 0; t < Convert.ToInt32((Convert.ToDouble(g2[2]) * 100).ToString("0")); t++)
                                                {
                                                    moneyList.Add(g2[1]);
                                                }

                                            }
                                        }

                                        pmlist["text"] = "恭喜 " + getStarNick(pmlist["uid"] + "", "<fuwu>") + " 获得【转盘奖励】" + moneyList[get_random("0", (moneyList.Count - 1).ToString())] + "<img src='/static/images/coin.png' alt='' height='15' width='15' style='margin-left: 5px;'>";

                                        break;
                                    case "yjlq":
                                        pmlist["type"] = "领取佣金";
                                        pmlist["text"] = "用户 " + getStarNick(pmlist["uid"] + "", "<fuwu>") + " 领取【合伙人佣金】" + (get_random("1000", "500000") / 100).ToString("0.00") + "<img src='/static/images/coin.png' alt='' height='15' width='15' style='margin-left: 5px;'>";
                                        break;
                                    default:
                                        break;
                                }

                                chatHtmlTable.Rows.Add(pmlist["type"], pmlist["text"], DateTime.Now.AddSeconds(get_random(uConfig.stcdata(pmlist["tps"] + "_html_ts").Split('~')[0], uConfig.stcdata(pmlist["tps"] + "_html_ts").Split('~')[1])));

                                Response.Write("【" + pmlist["tps"] + "】消息入库 = " + pmlist["type"] + "," + pmlist["text"] + "\r\n");

                            }

                        }
                        else
                        {
                            Response.Write("【" + pmlist["tps"] + "】未达到时间 - " + (cae.GetCache<Int64>(pmlist["tps"] + "_html_next_time") - getTimeStamp()) + "秒 \r\n");
                        }
                    }


                }


                cae.SetCache("chatHtmlTable", chatHtmlTable);
                fz.sendResponse("处理结束", 1);

                break;
            case "check_account_upgrade":

                if (CalculateTimeDifferenceInSeconds(DateTime.Now.ToString("HH:mm:ss"), "23:30:00") < 0)
                {
                    fz.sendResponse("非23点调整时间:");
                }

                if (cae.GetCache<string>("account_upgrade_time") == DateTime.Now.ToString("yyyyMMdd"))
                {
                    fz.sendResponse("今日已调整");
                }
                cae.SetCache("account_upgrade_time", DateTime.Now.ToString("yyyyMMdd"));

                pams.Add(new SqlParameter("@u_upgrade_reward", uConfig.stcdata("u_upgrade_reward")));

                sql = " update accounts set upgrade_state=1,upgrade_time=getdate() where DATEDIFF(DAY,GETDATE(),ISNULL(upgrade_time,0))<>0 and isnull(usertype,1)=0 and reward_amount>=@u_upgrade_reward ";
                res = db.ExecuteNonQuery(sql, pams.ToArray());

                fz.sendResponse("已调整" + res + "位用户");
                break;
            case "upgrade_firstpay_user":




                pmlist["th_groupId"] = "-1";
                dt = selectDateTable(chelper.gdt("account_groups"), " name='★托号' ");
                if (dt.Rows.Count > 0)
                {
                    pmlist["th_groupId"] = dt.Rows[0]["id"] + "";
                }
                pams.Add(new SqlParameter("@th_groupId", pmlist["th_groupId"] + ""));



                sql =

                    //[→]首次买币大厅
@"  

declare @first_list table(userid int) 

insert into @first_list select userid from (select userid FROM [buy_list] with(nolock) where state=1 group by userid)a left join accounts u with(nolock) on a.userid=u.id where a.userid>0 and isnull(u.usertype,1)=0 and isnull(groupid,0)<>@th_groupId

" + temp.Replace("where", "and")
                    //[→]首次使用收银台用户
+ @"
insert into @first_list select userid from (select userid FROM [api_orderList] with(nolock) where state=1 group by userid)a left join accounts u with(nolock) on a.userid=u.id  where  isnull(u.usertype,1)=0  and isnull(groupid,0)<>@th_groupId

" + temp.Replace("create_time", "create_time")
                    //[→]首次使用手动充值用户
+ @"
insert into @first_list select userid from (select userid FROM [serv_recharge] with(nolock) where amount>0 and recharge_type='买币' group by userid)a left join accounts u with(nolock) on a.userid=u.id  where  isnull(u.usertype,1)=0  and isnull(groupid,0)<>@th_groupId

" + temp.Replace("create_time", "create_time")

//[→]获取统计结果
+ @"

declare @update_userList  table(userid int) 

insert into @update_userList SELECT top 100 userid from(select userid FROM @first_list group by userid)a
"

                //[→]开始更新待处理的用户
                + @"

declare @last_usertype varchar(10)
declare @new_usertype varchar(10)
declare @upgrade_recid table(userid int) 

update accounts set usertype=2,upgrade_state=0 output deleted.id into @upgrade_recid where id in (select userid from @update_userList) and isnull(usertype,1)=0 and isnull(groupid,0)<>@th_groupId

if(Exists(select * from @upgrade_recid))
begin    
    insert into account_usertype_records  select -99,userid,'reg','super',getdate() from @upgrade_recid
    select * from @upgrade_recid
end

";

//                fz.jsonResponse(getPamsSql(pams) + @"
//" + sql);

                dt = db.getDataTable(sql, pams.ToArray());
                if (dt.Rows.Count == 0)
                {
                    fz.sendResponse("暂无需调整用户");
                }
                dic.Add("data", ToJson(dt));
                fz.sendResponse("SUCCESS", 1, dic);
                break;
            case "chear_timeout_order":
                sql = " delete [transport_orders] where userid is null and state=2 and datediff(day,create_time,getdate())>0 ";
                res = db.ExecuteNonQuery(sql, pams.ToArray());
                fz.sendResponse("已清理 " + res + " 单", 1);
                break;
            case "new_regOrders":
                fz.sendResponse("暂不使用");

                keyArray = new string[] { "th", "reg", "user", "super" };

                sql = " declare @tb table(limit_usertype varchar(10),limit_name varchar(10),amount decimal(18,2),minAmount decimal(18,2),maxAmount decimal(18,2),state int) ";

                for (int i = 0; i < keyArray.Length; i++)
                {

                    arrayList = uConfig.stcdata(keyArray[i] + "_sd_moneys").Split(new string[] { "\n" }, StringSplitOptions.None);
                    for (int a = 0; a < arrayList.Length; a++)
                    {
                        g = arrayList[a].Split('~');
                        if (g.Length < 2)
                        {
                            continue;
                        }
                        pmlist["limit_name"] = "";
                        if (g.Length > 2)
                        {
                            pmlist["limit_name"] = g[2];
                        }
                        pams.Add(new SqlParameter("@amount" + (i + "_" + a), get_random(g[0], g[1])));
                        pams.Add(new SqlParameter("@minAmount" + (i + "_" + a), g[0]));
                        pams.Add(new SqlParameter("@maxAmount" + (i + "_" + a), g[1]));
                        pams.Add(new SqlParameter("@limit_name" + (i + "_" + a), pmlist["limit_name"] + ""));
                        sql += string.Format(" insert into @tb values('{1}',@limit_name{0},@amount{0},@minAmount{0},@maxAmount{0},1) ", (i + "_" + a), keyArray[i]);
                    }


                }

                sql += @"

delete transport_orders where limit_usertype is not null and state=0 and DATEDIFF(minute,create_time,GETDATE())>30

update t set state=0 from @tb t left join (select id,orderAmt,limit_usertype from transport_orders with(nolock) where other_orderNo='-' and state=0)t2 on t2.orderAmt>=t.minAmount and  t2.orderAmt<=t.maxAmount and t.limit_usertype=isnull(t2.limit_usertype,'') where t2.id is not null

delete @tb where state=0

declare @total_number int
set @total_number=0 
select @total_number=count(0) from @tb

while @total_number>0
begin

    declare @title varchar(500)
    declare @imgurl varchar(200)
    declare @amount decimal(18,2)
    declare @minAmount decimal(18,2)
    declare @maxAmount decimal(18,2)
    declare @limit_usertype varchar(10)
    declare @limit_name varchar(10)

    select top 1 @limit_usertype=limit_usertype, @limit_name=limit_name,@amount=amount,@minAmount=minAmount,@maxAmount=maxAmount from @tb order by newid()
    delete @tb where amount=@amount

    select top 1 @title=name,@imgurl=imgurl from (select top 10 * from item_list with(nolock) order by ABS(@amount-amount))t order by NEWID()

    -- 生成订单号 
    DECLARE @new_orderId VARCHAR(38);
    EXEC GenerateTransportOrder @GeneratedCode = @new_orderId OUTPUT;
    
    insert into transport_orders(title,imgurl,orderNo,orderType,orderAmt,rate_award,award_amount,other_orderNo,state,create_time,limit_usertype,limit_name) values(@title,@imgurl,@new_orderId,'银联',@amount,0,0,'-',0,getdate(),@limit_usertype,@limit_name) 

    select '#'+@limit_usertype+'('+@limit_name+') '+cast(@minAmount as varchar(50))+'~'+cast(@maxAmount as varchar(50))+' ≈ '+cast(@amount as varchar(50))+' >> '+@new_orderId as orderId

    select @total_number=count(0) from @tb
end

";



                ds = db.getDataSet(sql, pams.ToArray());

                if (ds.Tables.Count == 0)
                {
                    fz.sendResponse("暂无新增订单");
                }


                for (int i = 0; i < ds.Tables.Count; i++)
                {
                    dt = ds.Tables[i];
                    text_array.Add(dt.Rows[0]["orderId"] + "");
                }
                dic.Add("orders", text_array);

                log.WriteLog("new_regOrder", "new", JsonMapper.ToJson(dic));

                fz.sendResponse("创建成功", 1, dic);

                break;
            case "reject_task":
                if (CalculateTimeDifferenceInSeconds(DateTime.Now.ToString("HH:mm:ss"), "23:00:00") < 0)
                {
                    fz.sendResponse("非23点调整时间:");
                }

                if (cae.GetCache<string>("reject_task_time") == DateTime.Now.ToString("yyyyMMdd"))
                {
                    fz.sendResponse("今日已调整");
                }
                cae.SetCache("reject_task_time", DateTime.Now.ToString("yyyyMMdd"));

                sql = " delete task_receive_list where audit_time is null ";
                res = db.ExecuteNonQuery(sql, pams.ToArray());

                fz.sendResponse("已驳回" + res + "条请求");
                break;
            case "delete_GT":
                sql = "  delete current_share where expire_time<=getdate() ";
                res = db.ExecuteNonQuery(sql, pams.ToArray());

                fz.sendResponse("已删除" + res + "条");
                break;
            case "fake_GT":
                cache_dt = getCacheDt("gt");



                rows = cache_dt.Select(" create_time<='" + DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss") + "' ");
                foreach (DataRow row in rows)
                {
                    Response.Write("【" + row["type"] + "】新消息 = " + row["text"] + "\r\n");

                    pushGroupMessage(row["type"] + "", "-1", "html", "[用户跟投]", row["text"] + "");

                    cache_dt.Rows.Remove(row);
                }

                if (cache_dt.Rows.Count == 0)
                {
                    sql = "  select *,DATEDIFF(SECOND,create_time,GETDATE()) as ts from current_share with(nolock) where expire_time>getdate() and getdate()>dateadd(second,2,create_time) ";

                    dt = db.getDataTable(sql);

                    for (int i = 0; i < dt.Rows.Count; i++)
                    {
                        g = uConfig.stcdata("gt_number").Split('\n');
                        for (int z1 = 0; z1 < g.Length; z1++)
                        {
                            g2 = g[z1].Split(',');
                            if (g2.Length == 2 && g2[0].Split('-').Length == 2 && g2[1].Split('~').Length == 2)
                            {
                                res = get_random(g2[1].Split('~')[0], g2[1].Split('~')[1]);
                                for (int t = 0; t < res; t++)
                                {
                                    if (get_random("0", dt.Rows[i]["ts"] + "") < 20)
                                    {
                                        cache_dt.Rows.Add(dt.Rows[i]["chatid"] + "", "<div>'<span style=\"color:#758DB9;\" uuid='" + md5("ND_" + get_random("1000000", "9999999")) + "'>" + getStarNick(get_random("1000000", "9999999").ToString(), "<fuwu>") + "</span>'跟投了'<span style=\"color:#758DB9;\" uuid='" + dt.Rows[0]["uid"] + "'>" + dt.Rows[0]["nick"] + "</span>'分享注单(" + dt.Rows[i]["gamename"] + "第" + dt.Rows[i]["expect"] + "期,投注金额" + (get_random(g2[0].Split('-')[0], g2[0].Split('-')[1]) * 1).ToString() + "元)</div>", DateTime.Now.AddSeconds(get_random("0", uConfig.stcdata("gt_ts"))));
                                    }
                                }

                            }
                        }
                    }

                }

                cae.SetCache("gt", cache_dt);

                break;
            case "new_play":
                try
                {
                    jd = JsonMapper.ToObject(fz.req("updates"));

                    res = jd.Count;
                }
                catch (Exception)
                {
                    return getResBody("json format error");
                }

                log.WriteLog("play_update", "updates", fz.req("updates"));



                dt = selectDateTable(chelper.gdt("account_groups"), " name='★托号' ");
                if (dt.Rows.Count > 0)
                {
                    pmlist["th_groupId"] = dt.Rows[0]["id"] + "";
                }

                pams.Add(new SqlParameter("@th_groupId", pmlist["th_groupId"] + ""));





                sql = @" 
declare @current_amount int
declare @playtotal_id int
declare @total_update int
declare @relaids varchar(1000)
declare @isth int
declare @parentid int
set @total_update=0

";
                for (int i = 0; i < jd.Count; i++)
                {
                    pmlist["okamount"] = "0";
                    try
                    {
                        pmlist["okamount"] = jd[i]["okamount"] + "";
                    }
                    catch (Exception)
                    {
                    }


                    pmlist["gamecode"] = "";
                    try
                    {
                        pmlist["gamecode"] = jd[i]["gamecode"] + "";
                    }
                    catch (Exception)
                    {
                    }

                    pams.Add(new SqlParameter("@userid" + i, jd[i]["userid"] + ""));
                    pams.Add(new SqlParameter("@amount" + i, jd[i]["amount"] + ""));
                    pams.Add(new SqlParameter("@okamount" + i, pmlist["okamount"] + ""));
                    pams.Add(new SqlParameter("@gamecode" + i, pmlist["gamecode"] + ""));
                    pams.Add(new SqlParameter("@isdraw" + i, jd[i]["isdraw"] + ""));
                    pams.Add(new SqlParameter("@oid" + i, jd[i]["oid"] + ""));
                    pams.Add(new SqlParameter("@time" + i, jd[i]["time"] + ""));

                    sql += string.Format(@" 

if(not Exists(select * from play_records where oid=@oid{0}))
begin

    set @total_update=@total_update+1

    set @current_amount=0
    set @isth=0
    set @relaids=''
    set @parentid=null
    -- update accounts set @parentid=parentid,@relaids=isnull(relaids,''),@isth=(case when groupid=@th_groupId then 1 else 0 end),@current_amount=isnull(play_amount,0)+@amount{0},play_amount=isnull(play_amount,0)+@amount{0} where id=@userid{0}

    select @parentid=parentid,@relaids=isnull(relaids,''),@isth=(case when groupid=@th_groupId then 1 else 0 end) from accounts with(nolock)  where id=@userid{0}

    insert into play_records values(@isth,@relaids,@parentid,@userid{0},@amount{0},0,@okamount{0},@gamecode{0},@oid{0},@isdraw{0},@time{0})

    if(@isdraw{0}=1 or @isdraw{0}=-1)
    begin

        set @playtotal_id=null
        select @playtotal_id=id from play_total with(nolock) where DATEDIFF(day,@time{0},create_date)=0 and userid=@userid{0}
        if(@playtotal_id is not null)
        begin
	        update play_total set amount=amount+@amount{0},num=num+1 where id=@playtotal_id
        end
        else
        begin
	        insert into play_total values(@isth,@relaids,@parentid,@userid{0},@amount{0},1,0,0,@time{0})
        end

    end

end

", i);


                    if ((i + 1) % 100 == 0)
                    {
                        res += db.ExecuteNonQuery(sql, pams.ToArray());
                        pams = new List<SqlParameter>();
                        pams.Add(new SqlParameter("@th_groupId", pmlist["th_groupId"] + ""));

                        sql = string.Empty;
                        sql = @" 
declare @current_amount int
declare @playtotal_id int
declare @total_update int
declare @relaids varchar(1000)
declare @isth int
declare @parentid int
set @total_update=0

";

                    }

                }

                if (!string.IsNullOrEmpty(sql))
                {
                    res += db.ExecuteNonQuery(sql, pams.ToArray());
                }


                //sql += " select @total_update as total_update ";
                //dt = new DataTable();
                //if (jd.Count > 0)
                //{
                //    dt = db.getDataTable(sql, pams.ToArray());
                //}

                dic.Add("number", res);
                fz.sendResponse("update:" + uConfig.gd(dt, "total_update"), 1, dic);
                break;
            case "settle_play_data":

                temp = cae.GetCache<string>("play_settle_state");

                if (Request.Url.Host == "localhost" || fz.req("date") != "")
                {
                    temp = "";
                }


                if (string.IsNullOrEmpty(temp))
                {
                    cae.SetCache("play_settle_state", DateTime.Now.AddMinutes(15).ToString("yyyy-MM-dd HH:mm:ss"), DateTime.Now.AddMinutes(15));
                    //这里要检查一遍是结算过（缓存可能会因为重启等原因消失）
                    pmlist["settle_date"] = DateTime.Now.AddDays(-1).ToString("yyyy-MM-dd");//结算日期


                    if (DateTime.Now.Hour * 60 + DateTime.Now.Minute > 40)
                    {
                        pmlist["settle_date"] = DateTime.Now.ToString("yyyy-MM-dd");//结算日期
                    }
                    if (fz.req("date") != "")
                    {
                        pmlist["settle_date"] = fz.req("date");
                    }

                    pams.Add(new SqlParameter("@settle_date", pmlist["settle_date"] + ""));

                    sql = @" 

--select * from [play_total] with(nolock) where datediff(day,create_date,@settle_date)=0 
update [play_total] set complete_num=num,complete_amount=amount output deleted.* where datediff(day,create_date,@settle_date)=0

";
                    dt = db.getDataTable(sql, pams.ToArray());
                    log.WriteLog("Play结算", "数据", ToJson(dt));
                    if (dt.Rows.Count == 0)
                    {
                        fz.sendResponse("暂无待结算数据{" + pmlist["settle_date"] + "}");
                    }
                    Response.Write("结算时间：" + DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss") + "\r\n---------------------------\r\n");

                    //创建‘团队业绩更新表’
                    DataTable teamUpdateList = new DataTable("teamUpdateList");
                    teamUpdateList.Columns.Add("userid", typeof(string));
                    teamUpdateList.Columns.Add("team_amount", typeof(Decimal));
                    teamUpdateList.Columns.Add("team_num", typeof(int));
                    teamUpdateList.Columns.Add("user_amount", typeof(Decimal));
                    teamUpdateList.Columns.Add("user_num", typeof(int));

                    pmlist["total_userAmount"] = "0";
                    pmlist["total_userNum"] = "0";

                    //更新到用户表
                    for (int i = 0; i < dt.Rows.Count; i++)
                    {

                        pmlist["current_amount"] = dt.Rows[i]["amount"] + "";
                        pmlist["current_num"] = dt.Rows[i]["num"] + "";
                        pmlist["parentid"] = dt.Rows[i]["parentid"] + "";


                        pmlist["total_userAmount"] = Convert.ToDouble(pmlist["total_userAmount"] + "") + Convert.ToDouble(pmlist["current_amount"] + "");
                        pmlist["total_userNum"] = Convert.ToDouble(pmlist["total_userNum"] + "") + Convert.ToDouble(pmlist["current_num"] + "");

                        Response.Write("amount-" + dt.Rows[i]["id"] + "" + "-：" + pmlist["current_amount"] + "（" + pmlist["total_userAmount"] + "）\r\n");


                        //团队业绩更新(team_amount,团队佣金》来源直属下级的总业绩)
                        g = (dt.Rows[i]["relaids"] + "").Split(',');
                        for (int r = 0; r < g.Length; r++)
                        {
                            if (g[r] == dt.Rows[i]["userid"] + "" || g[r] == pmlist["parentid"] + "")
                            {
                                //自身跟上级不计入团队业绩
                            }
                            else
                            {
                                //下属直属算业绩
                                if (!string.IsNullOrEmpty(g[r]))
                                {
                                    pmlist["amount"] = "0";
                                    pmlist["num"] = "0";

                                    updateRowIndex = -1;

                                    for (int rowIndex = 0; rowIndex < teamUpdateList.Rows.Count; rowIndex++)
                                    {
                                        if (teamUpdateList.Rows[rowIndex]["userid"].ToString() == g[r])
                                        {
                                            updateRowIndex = rowIndex;
                                            break;
                                        }
                                    }




                                    if (updateRowIndex != -1)
                                    {
                                        pmlist["amount"] = teamUpdateList.Rows[updateRowIndex]["team_amount"] + "";
                                        pmlist["num"] = teamUpdateList.Rows[updateRowIndex]["team_num"] + "";


                                        pmlist["amount"] = (Convert.ToDouble(pmlist["amount"] + "") + Convert.ToDouble(pmlist["current_amount"] + "")).ToString();
                                        pmlist["num"] = (Convert.ToDouble(pmlist["num"] + "") + Convert.ToDouble(pmlist["current_num"] + "")).ToString();


                                        //更新
                                        teamUpdateList.Rows[updateRowIndex]["team_amount"] = pmlist["amount"];
                                        teamUpdateList.Rows[updateRowIndex]["team_num"] = pmlist["num"];
                                    }
                                    else
                                    {
                                        //插入
                                        teamUpdateList.Rows.Add(g[r], pmlist["current_amount"] + "", pmlist["current_num"] + "", "0", "0");

                                    }
                                }


                                // g[r]=团队成员id，需要获取成员的parentID（留空自动获取）
                                if (!dic.ContainsKey(g[r] + "") && g[r] != "")
                                {
                                    dic.Add(g[r] + "", "null");
                                }


                            }

                        }


                        //直属业绩更新(user_amount,直营佣金》来源直属下级游戏总额)
                        updateRowIndex = -1;
                        for (int rowIndex = 0; rowIndex < teamUpdateList.Rows.Count; rowIndex++)
                        {
                            if (teamUpdateList.Rows[rowIndex]["userid"].ToString() == pmlist["parentid"] + "")
                            {
                                updateRowIndex = rowIndex;
                                break;
                            }
                        }


                        if (updateRowIndex != -1)
                        {
                            pmlist["amount"] = (Convert.ToDouble(teamUpdateList.Rows[updateRowIndex]["user_amount"] + "") + Convert.ToDouble(pmlist["current_amount"] + "")).ToString();
                            pmlist["num"] = (Convert.ToDouble(teamUpdateList.Rows[updateRowIndex]["user_num"] + "") + Convert.ToDouble(pmlist["current_num"] + "")).ToString();


                            //更新
                            teamUpdateList.Rows[updateRowIndex]["user_amount"] = pmlist["amount"];
                            teamUpdateList.Rows[updateRowIndex]["user_num"] = pmlist["num"];
                        }
                        else
                        {
                            //插入
                            teamUpdateList.Rows.Add(pmlist["parentid"] + "", "0", "0", pmlist["current_amount"] + "", pmlist["current_num"] + "");

                        }



                        if (!dic.ContainsKey(dt.Rows[i]["userid"] + ""))
                        {
                            dic.Add(dt.Rows[i]["userid"] + "", dt.Rows[i]["parentid"] + "");
                        }

                        if (!dic.ContainsKey(dt.Rows[i]["parentid"] + "") && dt.Rows[i]["parentid"] + "" != "")
                        {
                            dic.Add(dt.Rows[i]["parentid"] + "", "null");
                        }
                    }
                    Response.Write("结算金额：" + pmlist["total_userAmount"] + "（" + pmlist["total_userNum"] + "）\r\n");

                    OutputDataTableText(teamUpdateList);

                    sql = string.Empty;
                    pams = new List<SqlParameter>();
                    sql = " declare @tableUsers table(userid int,parentid int) ";
                    foreach (var item in dic)
                    {
                        sql += " insert into @tableUsers values(" + item.Key + "," + item.Value + ")";
                    }
                    sql += " update t set t.parentid=a.parentid from @tableUsers t left join accounts a with(nolock) on t.userid=a.id where t.parentid is null  ";

                    //Response.Write("sql = " + sql + "\r\n\r\n");



                    //判断用户是否有play_user_data表中，没有则创建
                    //将数据更新到play_user_data（总表）跟[play_user_daily]（日表）中
                    sql += @" 

-- 生成用户当日数据统计表
insert into [play_user_data] select userid,parentid,0,0,0,0,@settle_date from @tableUsers where userid not in (select userid from play_user_data with(nolock) where datediff(day,@settle_date,create_date)=0)

-- 重新生成重置用户日数据表（重置）
update play_user_data set user_num=0,user_amount=0,team_num=0,team_amount=0 where datediff(day,@settle_date,create_date)=0

declare @user_team_total decimal(18,2)
declare @user_amount_total decimal(18,2)
declare @parentid int
declare @team_list table(userid int,parentid int,user_team_total decimal(18,2),user_amount_total decimal(18,2),parent_team_total decimal(18,2),parent_user_amount decimal(18,2),team_amount decimal(18,2),team_num int,user_amount decimal(18,2),user_num int)

";
                    //【临时表说明】
                    //userid：用户ID
                    //parentid：上级ID
                    //user_team_total：{userid}团队总业绩（用于返佣等级计算）
                    //user_amount_total：{userid}直属总业绩（用于返佣等级计算）
                    //parent_team_total：{parentid}团队总业绩（用于返佣等级计算）
                    //parent_user_amount：{parentid}直属总业绩（用于返佣等级计算）
                    //team_amount：{userid}团队业绩（用于结算）
                    //team_num：{userid}团队单量
                    //user_amount：{userid}直属业绩（用于结算）
                    //user_num：{userid}直属单量


                    //将直属业绩、团队业绩更新到临时表
                    for (int i = 0; i < teamUpdateList.Rows.Count; i++)
                    {
                        sql += string.Format(@" 
set @user_team_total=0
set @user_amount_total=0
set @parentid=0
update play_user_data set @parentid=parentid,@user_team_total=team_amount+{1},team_amount=team_amount+{1},team_num=team_num+{2},user_amount=user_amount+{3},@user_amount_total=user_amount+{3},user_num=user_num+{4} where userid={0} and datediff(day,@settle_date,create_date)=0

insert into  @team_list values({0},@parentid,@user_team_total,@user_amount_total,0,0,{1},{2},{3},{4})
", teamUpdateList.Rows[i]["userid"] + "", teamUpdateList.Rows[i]["team_amount"] + "", teamUpdateList.Rows[i]["team_num"] + "", teamUpdateList.Rows[i]["user_amount"] + "", teamUpdateList.Rows[i]["user_num"] + "");
                    }

                    sql += @"

update t set t.parent_team_total=isnull(a.team_amount,0), t.parent_user_amount=isnull(a.user_amount,0) from  @team_list  t left join (select * from play_user_data wtih(nolock) where datediff(day,@settle_date,create_date)=0)a on t.parentid=a.userid

select * from @team_list

";

                    dt = db.getDataTable(sql, new SqlParameter[]{
                        new SqlParameter("@settle_date", pmlist["settle_date"] + "")
                    });


                    Response.Write("待结算佣金列表：\r\n\r\n");
                    OutputDataTableText(dt);

                    //用户当日佣金更新
                    temp_dt = chelper.gdt("play_rules");//规则表
                    temp_dt = SortDataTable(temp_dt, "team_amount asc");
                    pmlist["max_brok"] = temp_dt.Rows[temp_dt.Rows.Count - 1]["brok_amount"] + "";
                    sql = string.Empty;



                    pams.Add(new SqlParameter("@total_userAmount", pmlist["total_userAmount"] + ""));
                    pams.Add(new SqlParameter("@total_userNum", pmlist["total_userNum"] + ""));

                    sql = @" 

declare @parentTeamTable table(parentid int,amount decimal(18,2)) 
declare @daily_id int


declare @total_amount decimal(18,2)
declare @total_number decimal(18,2)
declare @total_teambork decimal(18,2)
declare @total_userbork decimal(18,2)

set @total_amount=cast(@total_userAmount as decimal(18,2))
set @total_number=cast(@total_userNum as decimal(18,2))
set @total_teambork=0
set @total_userbork=0

-- 重新生成重置用户日数据表（重置）
delete play_brok_details where datediff(day,create_time,@settle_date)=0
update play_user_daily set team_amount=0,team_num=0,user_amount=0,user_num=0,bork_user=0,bork_team=0 where datediff(day,create_date,@settle_date)=0

";
                    for (int i = 0; i < dt.Rows.Count; i++)
                    {
                        //user_team_total:用户【团队】总业绩
                        //user_amount_total:用户【直属】总业绩
                        //parent_team_total:上级【团队】总业绩
                        //parent_user_amount:上级【直属】总业绩
                        //user_amount:【本次结算】直属业绩

                        pmlist["user_team_total"] = dt.Rows[i]["user_team_total"] + "";
                        if (string.IsNullOrEmpty(pmlist["user_team_total"] + ""))
                        {
                            pmlist["user_team_total"] = "0";
                        }
                        pmlist["parent_team_total"] = dt.Rows[i]["parent_team_total"] + "";
                        if (string.IsNullOrEmpty(pmlist["parent_team_total"] + ""))
                        {
                            pmlist["parent_team_total"] = "0";
                        }

                        pmlist["user_amount"] = dt.Rows[i]["user_amount"] + "";
                        pmlist["parent_user_amount"] = dt.Rows[i]["parent_user_amount"] + "";
                        if (string.IsNullOrEmpty(pmlist["parent_user_amount"] + ""))
                        {
                            pmlist["parent_user_amount"] = "0";
                        }

                        pmlist["user_amount_total"] = dt.Rows[i]["user_amount_total"] + "";
                        if (string.IsNullOrEmpty(pmlist["user_amount_total"] + ""))
                        {
                            pmlist["user_amount_total"] = "0";
                        }


                        pmlist["user_brok_amount"] = "0";
                        pmlist["parent_brok_amount"] = "0";
                        for (int t = 0; t < temp_dt.Rows.Count; t++)
                        {
                            if (Convert.ToDouble(pmlist["user_team_total"] + "") + Convert.ToDouble(pmlist["user_amount_total"] + "") >= Convert.ToDouble(temp_dt.Rows[t]["team_amount"] + ""))
                            {
                                pmlist["user_brok_amount"] = temp_dt.Rows[t]["brok_amount"] + "";
                            }
                        }
                        for (int t = 0; t < temp_dt.Rows.Count; t++)
                        {
                            if (Convert.ToDouble(pmlist["parent_team_total"] + "") + Convert.ToDouble(pmlist["parent_user_amount"] + "") >= Convert.ToDouble(temp_dt.Rows[t]["team_amount"] + ""))
                            {
                                pmlist["parent_brok_amount"] = temp_dt.Rows[t]["brok_amount"] + "";
                            }
                        }

                        pmlist["jicha"] = Convert.ToDouble(pmlist["parent_brok_amount"] + "") - Convert.ToDouble(pmlist["user_brok_amount"] + "");
                        if (Convert.ToDouble(pmlist["jicha"] + "") < 0)
                        {
                            pmlist["jicha"] = "0";
                        }

                        //team_amount是需要看他下级给的极差

                        //总业绩=直属业绩+团队业绩。
                        //团队=下属的下属
                        //user_amount=直属业绩
                        //team_amount=团队业绩

                        //直属业绩（user_amount）*代理万比（自身佣金，user_brok_amount）
                        pmlist["bork_user"] = (Convert.ToDouble(dt.Rows[i]["user_amount"] + "") / 10000 * Convert.ToDouble(pmlist["user_brok_amount"] + "")).ToString("0.00");

                        //总业绩（直属+团队，user_amount+team_amount）*代理级差万比（上级佣金-自身佣金，parent_brok_amount-user_brok_amount）
                        pmlist["bork_team"] = ((Convert.ToDouble(dt.Rows[i]["user_amount"] + "") + Convert.ToDouble(dt.Rows[i]["team_amount"] + "")) / 10000 * Convert.ToDouble(pmlist["jicha"] + "")).ToString("0.00");


                        if (pmlist["jicha"] + "" != "0" || Request.Url.Host == "localhost")
                        {
                            Response.Write("[" + dt.Rows[i]["userid"] + "(" + (Convert.ToDouble(pmlist["user_team_total"] + "") + Convert.ToDouble(pmlist["user_amount_total"] + "")) + ") > " + dt.Rows[i]["parentid"] + "(" + (Convert.ToDouble(pmlist["parent_team_total"] + "") + Convert.ToDouble(pmlist["parent_user_amount"] + "")) + ")]极差：" + pmlist["jicha"] + ",佣金(" + dt.Rows[i]["user_amount"] + "+" + dt.Rows[i]["team_amount"] + "/10000*" + pmlist["jicha"] + "" + ")：" + pmlist["bork_team"] + (Convert.ToDouble(pmlist["bork_team"] + "") > 0 ? "【团团团团团团 结算】" : "") + "\r\n\r\n");
                        }
                        if (Request.Url.Host == "localhost")
                        {
                            Response.Write("[***下属佣金 " + dt.Rows[i]["userid"] + "(" + (Convert.ToDouble(pmlist["user_team_total"] + "") + Convert.ToDouble(pmlist["user_amount_total"] + "")) + ")] 佣金(" + dt.Rows[i]["user_amount"] + "/10000*" + pmlist["user_brok_amount"] + "" + ")：" + pmlist["bork_user"] + (Convert.ToDouble(pmlist["bork_user"] + "") > 0 ? "【结算】" : "") + "\r\n\r\n");

                        }
                        //play_user_daily > 每个用户日佣金统计
                        //play_brok_details > 佣金明细表（直营佣金[每个下属]、团队佣金[每个下属总业绩]）
                        if (dt.Rows[i]["parentid"] + "" == "")
                        {
                            dt.Rows[i]["parentid"] = "-1";
                        }
                        sql += string.Format(@" 

insert into play_brok_details values({1},{0},'team',{7},{2}+{4},{8},{9},@settle_date)
insert into play_brok_details values({0},-1,'user',{6},{4},{9},{8},@settle_date)

if(Exists(select * from @parentTeamTable where parentid={1}))
begin
    update @parentTeamTable set amount=amount+{7} where parentid={1}
end
else
begin    
    insert into @parentTeamTable values({1},{7})
end

-- 判断play_user_daily是否存在【插入/更新】
set @daily_id=null
select @daily_id=id from play_user_daily with(nolock) where datediff(day,@settle_date,create_date)=0 and userid={0}
if(@daily_id is null)
begin
    insert into [play_user_daily] values({0},{2},{3},{4},{5},{6},0,0,@settle_date)
end
else
begin
    update play_user_daily set team_amount=team_amount+{2},team_num=team_num+{3},user_amount=user_amount+{4},user_num=user_num+{5},bork_user=bork_user+{6} where id=@daily_id
end


set @total_teambork=@total_teambork+{7}
set @total_userbork=@total_userbork+{6}

 ",
dt.Rows[i]["userid"] + "", //{0}
dt.Rows[i]["parentid"] + "", //{1}
dt.Rows[i]["team_amount"] + "", //{2}
dt.Rows[i]["team_num"] + "", //{3}
dt.Rows[i]["user_amount"] + "", //{4}
dt.Rows[i]["user_num"] + "", //{5}
pmlist["bork_user"] + "",//{6}
pmlist["bork_team"] + "", //{7}
pmlist["parent_brok_amount"] + "", //{8}
pmlist["user_brok_amount"] + ""//{9}
);

                    }

                    //创建不存在的parentid
                    sql += @"

delete @parentTeamTable where amount<=0


insert into [play_user_daily] select parentid,0,0,0,0,0,0,0,@settle_date from @parentTeamTable where parentid not in (select userid from play_user_data with(nolock) where datediff(day,@settle_date,create_date)=0) 

-- 更新团队佣金
update t set t.bork_team=t.bork_team+p.amount from play_user_daily t left join @parentTeamTable p on p.parentid=t.userid where datediff(day,@settle_date,t.create_date)=0 and p.amount is not null 



-- select @total_amount=sum(amount),@total_number=sum(num) from play_total with(nolock) where datediff(day,create_date,@settle_date)=0 
select @total_userbork=sum(bork_user),@total_teambork=sum(bork_team) from play_user_daily with(nolock) where datediff(day,create_date,@settle_date)=0 

insert into play_timeslot_details values(@settle_date,getdate(),@total_amount,@total_number,@total_teambork,@total_userbork)

-- update play_settle_details set total_amount=total_amount+@total_amount,total_number=total_number+@total_number,total_teambork=total_teambork+@total_teambork,total_userbork=total_userbork+@total_userbork where datediff(day,create_date,@settle_date)=0


";

                    //Response.Write("settle_date = " + pmlist["settle_date"] + "");
                    //Response.End();

                    pams.Add(new SqlParameter("@settle_date", pmlist["settle_date"] + ""));
                    db.ExecuteNonQuery(sql, pams.ToArray());






                    Response.Write("佣金结算列表：\r\n\r\n");
                    OutputDataTableText(db.getDataTable(" select * from [play_timeslot_details] with(nolock) where datediff(day,create_date,@settle_date)=0 order by create_time desc ", new SqlParameter[] { new SqlParameter("@settle_date", pmlist["settle_date"] + "") }));



                    fz.sendResponse("统计成功，请查看！", 1);
                }
                fz.sendResponse("下次统计时间：" + temp);
                break;
            case "play_bork_handout":

                temp = cae.GetCache<string>("play_bork_handout");


                if (string.IsNullOrEmpty(temp) || fz.req("auto") == "1")
                {
                    cae.SetCache("play_bork_handout", DateTime.Now.AddMinutes(15).ToString("yyyy-MM-dd HH:mm:ss"), DateTime.Now.AddSeconds(10));

                    if (DateTime.Now.Hour * 60 + DateTime.Now.Minute <= 60)
                    {
                        fz.sendResponse("数据核验中");
                    }

                    pmlist["settle_date"] = DateTime.Now.AddDays(-1).ToString("yyyy-MM-dd");

                    pams.Add(new SqlParameter("@settle_date", pmlist["settle_date"] + ""));
                    sql = " update [play_user_daily] set state=1 output deleted.* where datediff(day,@settle_date,create_date)=0 and state=0 ";
                    dt = db.getDataTable(sql, pams.ToArray());
                    if (dt.Rows.Count == 0)
                    {
                        fz.sendResponse("暂无待发放数据{" + pmlist["settle_date"] + "}");
                    }
                    log.WriteLog("Play佣金发放", "数据", ToJson(dt));

                    sql = " declare @current_amount decimal(18,6) ";
                    for (int i = 0; i < dt.Rows.Count; i++)
                    {
                        sql += @"
set @current_amount=0
update accounts set play_brok=isnull(play_brok,0)+{bork_user}+{bork_team},@current_amount=isnull(play_brok,0)+{bork_user}+{bork_team} where id={userid}
insert into play_brok_records values({userid},'佣金发放',{bork_user}+{bork_team},@current_amount,'直属 {bork_user} 团队 {bork_team}',getdate()) 
"
                            .Replace("{userid}", dt.Rows[i]["userid"] + "")
                            .Replace("{bork_user}", dt.Rows[i]["bork_user"] + "")
                            .Replace("{bork_team}", dt.Rows[i]["bork_team"] + "")
                            ;
                    }
                    res = db.ExecuteNonQuery(sql);
                    log.WriteLog("Play佣金发放", "结算结果", res.ToString());
                    fz.sendResponse("已结算" + res, 1);
                }

                fz.sendResponse("操作频繁");

                break;
            case "generate_index_total":

                if (!fz.empty("data"))
                {
                    temp = cae.GetCache<string>(fz.req("data") + "_indexRef");
                    Response.Write("refresh Time " + temp + "\r\n----------------------------\r\n");
                    temp = cae.GetCache<string>(fz.req("data") + "_indexData_" + DateTime.Now.ToString("yyyyMMdd"));
                    Response.Write(temp);


                    Response.End();
                }

                pmlist["th_groupId"] = "-1";

                dt = selectDateTable(chelper.gdt("account_groups"), " name='★托号' ");
                if (dt.Rows.Count > 0)
                {
                    pmlist["th_groupId"] = dt.Rows[0]["id"] + "";
                }

                pams.Add(new SqlParameter("@th_groupId", pmlist["th_groupId"] + ""));


                fz.pager_where(!fz.empty("start_time"), " a.create_time>='@{start_time}'");
                fz.pager_where(!fz.empty("end_time"), " a.create_time<='@{end_time}'");

                temp = fz.getCond();

                if (!string.IsNullOrEmpty(temp))
                {
                    temp = " where " + temp;
                }



                if (pmlist["th_groupId"] + "" != "")
                {
                    if (!string.IsNullOrEmpty(temp))
                    {
                        temp += " and ";
                    }
                    else
                    {
                        temp += " where ";
                    }

                    temp += " isnull(u.groupid,-1)<>" + pmlist["th_groupId"] + " ";
                }

                pmlist["roomWhere"] = "";


                //查询缓存是否存在
                //src = cae.GetCache<string>("indexTotal_" + md5(fz.req("roomNumber") + "_" + fz.getCond()));
                if (temp != "check")
                {

                    //总用户量
                    //数据库的总用户数
                    //今日/昨日：根据用户创建时间
                    sList.Add("accounts");
                    sqls.Add(@" 
SELECT
    COUNT(0) AS total_number,
    SUM(CASE WHEN CAST(create_time AS DATE) = CAST(GETDATE() AS DATE) THEN 1 ELSE 0 END) AS today_number,
    SUM(CASE WHEN CAST(create_time AS DATE) = CAST(DATEADD(DAY, -1, GETDATE()) AS DATE) THEN 1 ELSE 0 END) AS yesterday_number
FROM accounts u with(nolock) 

" + temp.Replace("a.create_time", "u.create_time"));



                    //首次买币用户
                    //买币大厅人数+收银台人数+手充[买币]人数
                    //根据每个类型第一条订单的时间，是否跟今日/昨日匹配，如匹配则统计人数
                    //已过滤当日重复的用户

                    sList.Add("firstbuy_number");
                    //[→]首次买币大厅
                    sqls.Add(@"  

declare @first_list table(userid int,create_time datetime) 

insert into @first_list select userid,a.create_time from (select userid,MIN(buy_time) as create_time FROM [buy_list] with(nolock) where state=1 group by userid)a left join accounts u with(nolock) on a.userid=u.id where a.userid>0

" + temp.Replace("where", "and")
                        //[→]首次使用收银台用户
+ @"
insert into @first_list select userid,a.create_time from (select userid,MIN(create_time) as create_time FROM [api_orderList] with(nolock) where state=1 group by userid)a left join accounts u with(nolock) on a.userid=u.id 

" + temp.Replace("create_time", "create_time")
                        //[→]首次使用手动充值用户
+ @"
insert into @first_list select userid,a.create_time from (select userid,MIN(create_time) as create_time FROM [serv_recharge] with(nolock) where amount>0 and recharge_type='买币' group by userid)a left join accounts u with(nolock) on a.userid=u.id 

" + temp.Replace("create_time", "create_time")

//[→]获取统计结果
+ @"
SELECT 
    count(0) as total_number,
    COUNT(DISTINCT CASE WHEN CAST (a.create_time AS DATE) = CAST(GETDATE() AS DATE) THEN userid END) AS today_number,
    COUNT(DISTINCT CASE WHEN CAST (a.create_time AS DATE) = CAST(DATEADD(DAY, -1, GETDATE()) AS DATE) THEN userid END) AS yesterday_number
from(select userid,MIN(create_time) as create_time FROM @first_list group by userid)a
");



                    //在线用户数
                    //60秒内在线用户
                    //普通在线：排除托号后的总量
                    //托号在线：托号的总量
                    sList.Add("online_users");
                    sqls.Add(@" 
SELECT
    SUM((case when online_time>dateadd(second,-60,getdate()) then 1 else 0 end)) AS total_number,
    SUM((case when online_time>dateadd(second,-60,getdate()) and isnull(groupid,0)<>@th_groupId then 1 else 0 end))  AS today_number,
    SUM((case when online_time>dateadd(second,-60,getdate()) and isnull(groupid,0)=@th_groupId then 1 else 0 end))  AS yesterday_number
FROM accounts u with(nolock) 

" + pmlist["roomWhere"]);

                    //充值总量 recharge_total
                    //大厅买币总额+手动充值金额[买币]+三方充值总额+新用户买币总额


                    //充值总人数统计
                    //大厅买币人数+手动充值金额[买币]人数+三方充值总额+新用户买币人数【去重复人数】
                    sList.Add("recharge_total_number");
                    sqls.Add(@"
  declare @tb table(userid int,create_time datetime)
  
  insert into @tb select userid,buy_time from buy_list a with(nolock) where a.state=1 and (user_type='user' or user_type='reg') and a.userid>0
  insert into @tb select userid,pay_time from api_orderList a with(nolock) where a.state=1
  insert into @tb select userid,create_time from serv_recharge a with(nolock) where a.amount>0 and a.recharge_type='买币'
  
SELECT
    COUNT(DISTINCT userid) AS total_number,
    COUNT(DISTINCT CASE WHEN CAST (a.create_time AS DATE) = CAST(GETDATE() AS DATE) THEN userid END) AS today_number,
    COUNT(DISTINCT CASE WHEN CAST (a.create_time AS DATE) = CAST(DATEADD(DAY, -1, GETDATE()) AS DATE) THEN userid END) AS yesterday_number
FROM @tb a  left join accounts u with(nolock) on a.userid=u.id
" + temp);

                    //提现总量 cash_total
                    //手动扣除[刷单]+大厅卖币


                    //提现总人数统计
                    //手动扣除[刷单]人数+大厅卖币人数+{任务人数，用户类型为'普通用户'或'新用户',排除一键任务人数}【去重复人数】
                    sList.Add("cash_total_number");
                    sqls.Add(@"
  declare @cash_tb table(userid int,create_time datetime)
  
  insert into @cash_tb select userid,finish_time from transport_orders a with(nolock) where a.state=1  and (a.user_type='user' or a.user_type='reg') and isnull(a.order_mode,0)=0 and isnull(a.order_type,'')<>'ot' 
  insert into @cash_tb select userid,create_time from serv_recharge a with(nolock) where a.amount<=0 and a.recharge_type='刷单' 
  insert into @cash_tb select userid,create_time from sell_dating_list a with(nolock) where a.state=1 and userid>0

SELECT
    COUNT(DISTINCT userid) AS total_number,
    COUNT(DISTINCT CASE WHEN CAST (a.create_time AS DATE) = CAST(GETDATE() AS DATE) THEN userid END) AS today_number,
    COUNT(DISTINCT CASE WHEN CAST (a.create_time AS DATE) = CAST(DATEADD(DAY, -1, GETDATE()) AS DATE) THEN userid END) AS yesterday_number
FROM @cash_tb a  left join accounts u with(nolock) on a.userid=u.id
" + temp);


                    //大厅买币总额
                    //今日/昨日：根据订单购买时间|用户类型为'普通用户'
                    sList.Add("recharge_dating");
                    sqls.Add(@" 
  SELECT
    SUM(a.amount) AS total_amount,
    COUNT(DISTINCT userid) AS total_number,
    SUM(CASE WHEN CAST(buy_time AS DATE) = CAST(GETDATE() AS DATE) THEN a.amount ELSE 0 END) AS today_amount,
    COUNT(DISTINCT CASE WHEN CAST(buy_time AS DATE) = CAST(GETDATE() AS DATE) THEN userid END) AS today_number,
    SUM(CASE WHEN CAST(buy_time AS DATE) = CAST(DATEADD(DAY, -1, GETDATE()) AS DATE) THEN a.amount ELSE 0 END) AS yesterday_amount,
    COUNT(DISTINCT CASE WHEN CAST(buy_time AS DATE) = CAST(DATEADD(DAY, -1, GETDATE()) AS DATE) THEN userid END) AS yesterday_number
FROM [buy_list] a  with(nolock) left join accounts u with(nolock) on a.userid=u.id where a.state=1 and a.user_type='user' and a.userid>0

" + temp.Replace("create_time", "buy_time").Replace("where", "and"));



                    //活动充值金额
                    //今日/昨日：根据创建时间|类型为'活动充值'的订单
                    sList.Add("recharge_action");
                    sqls.Add(@" 
SELECT
    SUM(a.amount) AS total_amount,
    COUNT(DISTINCT userid) AS total_number,
    SUM(CASE WHEN CAST (a.create_time AS DATE) = CAST(GETDATE() AS DATE) THEN a.amount ELSE 0 END) AS today_amount,
    COUNT(DISTINCT CASE WHEN CAST (a.create_time AS DATE) = CAST(GETDATE() AS DATE) THEN userid END) AS today_number,
    SUM(CASE WHEN CAST (a.create_time AS DATE) = CAST(DATEADD(DAY, -1, GETDATE()) AS DATE) THEN a.amount ELSE 0 END) AS yesterday_amount,
    COUNT(DISTINCT CASE WHEN CAST (a.create_time AS DATE) = CAST(DATEADD(DAY, -1, GETDATE()) AS DATE) THEN userid END) AS yesterday_number
FROM [serv_recharge]  a  with(nolock) left join accounts u with(nolock) on a.userid=u.id

 where a.recharge_type='活动充值' " + temp.Replace("where", "and"));


                    //三方充值总额
                    //今日/昨日：根据订单付款时间
                    sList.Add("recharge_sanfang");
                    sqls.Add(@" 
                    SELECT
                        SUM(a.amount) AS total_amount,
                        COUNT(DISTINCT userid) AS total_number,
                        SUM(CASE WHEN CAST(pay_time AS DATE) = CAST(GETDATE() AS DATE) THEN a.amount ELSE 0 END) AS today_amount,
                        COUNT(DISTINCT CASE WHEN CAST(pay_time AS DATE) = CAST(GETDATE() AS DATE) THEN userid END) AS today_number,
                        SUM(CASE WHEN CAST(pay_time AS DATE) = CAST(DATEADD(DAY, -1, GETDATE()) AS DATE) THEN a.amount ELSE 0 END) AS yesterday_amount,
                        COUNT(DISTINCT CASE WHEN CAST(pay_time AS DATE) = CAST(DATEADD(DAY, -1, GETDATE()) AS DATE) THEN userid END) AS yesterday_number
                    FROM [api_orderList]  a  with(nolock) left join accounts u with(nolock) on a.userid=u.id where a.state=1
                    
                    " + temp.Replace("create_time", "pay_time").Replace("where", "and"));

                    //手动充值金额
                    //今日/昨日：根据创建时间|类型为'买币'并且金额>0的订单
                    sList.Add("recharge_admin");
                    sqls.Add(@" 
SELECT
    SUM(a.amount) AS total_amount,
    COUNT(DISTINCT userid) AS total_number,
    SUM(CASE WHEN CAST (a.create_time AS DATE) = CAST(GETDATE() AS DATE) THEN a.amount ELSE 0 END) AS today_amount,
    COUNT(DISTINCT CASE WHEN CAST (a.create_time AS DATE) = CAST(GETDATE() AS DATE) THEN userid END) AS today_number,
    SUM(CASE WHEN CAST (a.create_time AS DATE) = CAST(DATEADD(DAY, -1, GETDATE()) AS DATE) THEN a.amount ELSE 0 END) AS yesterday_amount,
    COUNT(DISTINCT CASE WHEN CAST (a.create_time AS DATE) = CAST(DATEADD(DAY, -1, GETDATE()) AS DATE) THEN userid END) AS yesterday_number
FROM [serv_recharge]  a  with(nolock) left join accounts u with(nolock) on a.userid=u.id

 where a.amount>0 and a.recharge_type='买币' " + temp.Replace("where", "and"));

                    //手动扣除金额
                    //今日/昨日：根据创建时间|类型为'刷单'并且金额<=0的订单
                    sList.Add("deduct_admin");
                    sqls.Add(@" 
SELECT
    SUM(a.amount)*-1 AS total_amount,
    COUNT(DISTINCT userid) AS total_number,
    SUM(CASE WHEN CAST (a.create_time AS DATE) = CAST(GETDATE() AS DATE) THEN a.amount ELSE 0 END)*-1 AS today_amount,
    COUNT(DISTINCT CASE WHEN CAST (a.create_time AS DATE) = CAST(GETDATE() AS DATE) THEN userid END) AS today_number,
    SUM(CASE WHEN CAST (a.create_time AS DATE) = CAST(DATEADD(DAY, -1, GETDATE()) AS DATE) THEN a.amount ELSE 0 END)*-1 AS yesterday_amount,
    COUNT(DISTINCT CASE WHEN CAST (a.create_time AS DATE) = CAST(DATEADD(DAY, -1, GETDATE()) AS DATE) THEN userid END) AS yesterday_number
FROM [serv_recharge]  a  with(nolock) left join accounts u with(nolock) on a.userid=u.id

 where a.amount<=0 and a.recharge_type='刷单' " + temp.Replace("where", "and"));


                    //用户总余额
                    //今日/昨日：根据用户创建时间|金额为用户余额+冻结金额
                    sList.Add("account_money");
                    sqls.Add(@" 
SELECT
    SUM(amount+freeze_amount) AS total_amount,
    COUNT(0) AS total_number,
    SUM(CASE WHEN CAST (create_time AS DATE) = CAST(GETDATE() AS DATE) THEN amount+freeze_amount ELSE 0 END) AS today_amount,
    COUNT(CASE WHEN CAST (create_time AS DATE) = CAST(GETDATE() AS DATE) THEN 1 END) AS today_number,
    SUM(CASE WHEN CAST (create_time AS DATE) = CAST(DATEADD(DAY, -1, GETDATE()) AS DATE) THEN amount+freeze_amount ELSE 0 END) AS yesterday_amount,
    COUNT(CASE WHEN CAST (create_time AS DATE) = CAST(DATEADD(DAY, -1, GETDATE()) AS DATE) THEN 1 END) AS yesterday_number
FROM accounts u  with(nolock) where (isnull(u.usertype,1)=1 or isnull(u.usertype,1)=2)

" + temp.Replace("where", "and").Replace("a.create_time", "u.create_time"));

                    //提现总额
                    //今日/昨日：抢单表数据|根据订单完成时间|用户为'普通用户'|排除一键任务订单
                    sList.Add("user_cash_money");
                    sqls.Add(@" 
SELECT
    SUM(a.orderAmt) AS total_amount,
    COUNT(DISTINCT userid) AS total_number,
    COUNT(0) AS total_daily_number,
    SUM(CASE WHEN CAST(finish_time AS DATE) = CAST(GETDATE() AS DATE) THEN a.orderAmt ELSE 0 END) AS today_amount,
    COUNT(DISTINCT CASE WHEN CAST(finish_time AS DATE) = CAST(GETDATE() AS DATE) THEN userid END) AS today_number,
    COUNT(CASE WHEN CAST(finish_time AS DATE) = CAST(GETDATE() AS DATE) THEN 1 END) AS today_daily_number,
    SUM(CASE WHEN CAST(finish_time AS DATE) = CAST(DATEADD(DAY, -1, GETDATE()) AS DATE) THEN a.orderAmt ELSE 0 END) AS yesterday_amount,
    COUNT(DISTINCT CASE WHEN CAST(finish_time AS DATE) = CAST(DATEADD(DAY, -1, GETDATE()) AS DATE) THEN userid END) AS yesterday_number,
    COUNT(CASE WHEN CAST(finish_time AS DATE) = CAST(DATEADD(DAY, -1, GETDATE()) AS DATE) THEN 1 END) AS yesterday_daily_number
FROM (
    SELECT userid,orderAmt,finish_time,state,order_mode,user_type,order_type FROM [transport_orders] WITH (NOLOCK)
    UNION ALL
    SELECT userid,orderAmt,finish_time,state,order_mode,user_type,order_type FROM [storage_transport_orders] WITH (NOLOCK)
) AS a  left join accounts u with(nolock) on a.userid=u.id where a.state=1  and a.user_type='user' and isnull(a.order_mode,0)=0 and isnull(a.order_type,'')<>'ot'

" + temp.Replace("create_time", "finish_time").Replace("where", "and"));



                    //卖币总量
                    //今日/昨日：根据订单创建时间
                    sList.Add("sell_dating_total");
                    sqls.Add(@" 
  SELECT
    SUM(a.amount) AS total_amount,
    COUNT(DISTINCT userid) AS total_number,
    COUNT(0) AS total_daily_number,

    SUM(CASE WHEN CAST(a.create_time AS DATE) = CAST(GETDATE() AS DATE) THEN a.amount ELSE 0 END) AS today_amount,
    COUNT(DISTINCT CASE WHEN CAST(a.create_time AS DATE) = CAST(GETDATE() AS DATE) THEN userid END) AS today_number,
    COUNT(CASE WHEN CAST(a.create_time AS DATE) = CAST(GETDATE() AS DATE) THEN 1 END) AS today_daily_number,

    SUM(CASE WHEN CAST(a.create_time AS DATE) = CAST(DATEADD(DAY, -1, GETDATE()) AS DATE) THEN a.amount ELSE 0 END) AS yesterday_amount,
    COUNT(DISTINCT CASE WHEN CAST(a.create_time AS DATE) = CAST(DATEADD(DAY, -1, GETDATE()) AS DATE) THEN userid END) AS yesterday_number,
    COUNT(CASE WHEN CAST(a.create_time AS DATE) = CAST(DATEADD(DAY, -1, GETDATE()) AS DATE) THEN 1 END) AS yesterday_daily_number

FROM [sell_dating_list] a  with(nolock) left join accounts u with(nolock) on a.userid=u.id where a.state=1 and userid>0

" + temp.Replace("where", "and"));

                    //手充“任务奖励”归类到佣金
                    sList.Add("recharge_yongjin");
                    sqls.Add(@" 
SELECT
    SUM(a.amount) AS total_amount,
    COUNT(DISTINCT userid) AS total_number,
    SUM(CASE WHEN CAST (a.create_time AS DATE) = CAST(GETDATE() AS DATE) THEN a.amount ELSE 0 END) AS today_amount,
    COUNT(DISTINCT CASE WHEN CAST (a.create_time AS DATE) = CAST(GETDATE() AS DATE) THEN userid END) AS today_number,
    SUM(CASE WHEN CAST (a.create_time AS DATE) = CAST(DATEADD(DAY, -1, GETDATE()) AS DATE) THEN a.amount ELSE 0 END) AS yesterday_amount,
    COUNT(DISTINCT CASE WHEN CAST (a.create_time AS DATE) = CAST(DATEADD(DAY, -1, GETDATE()) AS DATE) THEN userid END) AS yesterday_number
FROM [serv_recharge]  a  with(nolock) left join accounts u with(nolock) on a.userid=u.id where a.recharge_type='刷单奖励'

" + temp.Replace("where", "and"));


                    //佣金
                    //佣金列表[刷单佣金]+手动充值[刷单奖励]
                    //今日/昨日：根据订单创建时间|用户为'普通用户'|佣金来源为'刷单佣金'|公式=订单*佣金率
                    //佣金数据附加了手动充值[刷单奖励]的金额跟人数【没有去重复】
                    sList.Add("yongjing_qd");
                    sqls.Add(@" 
SELECT
    SUM(award_amount) AS total_amount,
    COUNT(DISTINCT userid) AS total_number,
    SUM(CASE WHEN CAST (a.create_time AS DATE) = CAST(GETDATE() AS DATE) THEN award_amount ELSE 0 END) AS today_amount,
    COUNT(DISTINCT CASE WHEN CAST (a.create_time AS DATE) = CAST(GETDATE() AS DATE) THEN userid END) AS today_number,
    SUM(CASE WHEN CAST (a.create_time AS DATE) = CAST(DATEADD(DAY, -1, GETDATE()) AS DATE) THEN award_amount ELSE 0 END) AS yesterday_amount,
    COUNT(DISTINCT CASE WHEN CAST (a.create_time AS DATE) = CAST(DATEADD(DAY, -1, GETDATE()) AS DATE) THEN userid END) AS yesterday_number
FROM [brok_list]  a  with(nolock) left join accounts u with(nolock) on a.userid=u.id where rank=-1  and a.user_type='user' and a.monfrom='shuadan'

" + temp.Replace("where", "and"));
                    //fz.jsonResponse(sqls[sqls.Count - 1]);

                    //一级
                    //今日/昨日：根据订单创建时间|用户为'普通用户'|佣金来源无限制|公式=佣金*一级佣金返佣率
                    sList.Add("yongjing_yj");
                    sqls.Add(@" 
SELECT
    SUM(award_amount) AS total_amount,
    COUNT(DISTINCT userid) AS total_number,
    SUM(CASE WHEN CAST (a.create_time AS DATE) = CAST(GETDATE() AS DATE) THEN award_amount ELSE 0 END) AS today_amount,
    COUNT(DISTINCT CASE WHEN CAST (a.create_time AS DATE) = CAST(GETDATE() AS DATE) THEN userid END) AS today_number,
    SUM(CASE WHEN CAST (a.create_time AS DATE) = CAST(DATEADD(DAY, -1, GETDATE()) AS DATE) THEN award_amount ELSE 0 END) AS yesterday_amount,
    COUNT(DISTINCT CASE WHEN CAST (a.create_time AS DATE) = CAST(DATEADD(DAY, -1, GETDATE()) AS DATE) THEN userid END) AS yesterday_number
FROM [brok_list]  a  with(nolock) left join accounts u with(nolock) on a.userid=u.id where rank=1   and a.user_type='user'

" + temp.Replace("where", "and"));

                    //二级
                    //今日/昨日：根据订单创建时间|用户为'普通用户'|佣金来源无限制|公式=佣金*二级佣金返佣率
                    sList.Add("yongjing_ej");
                    sqls.Add(@" 
SELECT
    SUM(award_amount) AS total_amount,
    COUNT(DISTINCT userid) AS total_number,
    SUM(CASE WHEN CAST (a.create_time AS DATE) = CAST(GETDATE() AS DATE) THEN award_amount ELSE 0 END) AS today_amount,
    COUNT(DISTINCT CASE WHEN CAST (a.create_time AS DATE) = CAST(GETDATE() AS DATE) THEN userid END) AS today_number,
    SUM(CASE WHEN CAST (a.create_time AS DATE) = CAST(DATEADD(DAY, -1, GETDATE()) AS DATE) THEN award_amount ELSE 0 END) AS yesterday_amount,
    COUNT(DISTINCT CASE WHEN CAST (a.create_time AS DATE) = CAST(DATEADD(DAY, -1, GETDATE()) AS DATE) THEN userid END) AS yesterday_number
FROM [brok_list]  a  with(nolock) left join accounts u with(nolock) on a.userid=u.id where rank=2  and a.user_type='user'

" + temp.Replace("where", "and"));

                    //三级
                    //今日/昨日：根据订单创建时间|用户为'普通用户'|佣金来源无限制|公式=佣金*三级佣金返佣率
                    sList.Add("yongjing_sj");
                    sqls.Add(@" 
SELECT
    SUM(award_amount) AS total_amount,
    COUNT(DISTINCT userid) AS total_number,
    SUM(CASE WHEN CAST (a.create_time AS DATE) = CAST(GETDATE() AS DATE) THEN award_amount ELSE 0 END) AS today_amount,
    COUNT(DISTINCT CASE WHEN CAST (a.create_time AS DATE) = CAST(GETDATE() AS DATE) THEN userid END) AS today_number,
    SUM(CASE WHEN CAST (a.create_time AS DATE) = CAST(DATEADD(DAY, -1, GETDATE()) AS DATE) THEN award_amount ELSE 0 END) AS yesterday_amount,
    COUNT(DISTINCT CASE WHEN CAST (a.create_time AS DATE) = CAST(DATEADD(DAY, -1, GETDATE()) AS DATE) THEN userid END) AS yesterday_number
FROM [brok_list]  a  with(nolock) left join accounts u with(nolock) on a.userid=u.id where rank=3  and a.user_type='user'

" + temp.Replace("where", "and"));

                    //佣金派发人数
                    //今日/昨日：游戏表佣金|根据订单创建时间|类型为'佣金发放'
                    sList.Add("playbrok_send");
                    sqls.Add(@" 
SELECT
    SUM(bork) AS total_amount,
    COUNT(DISTINCT userid) AS total_number,
    SUM(CASE WHEN CAST (a.create_time AS DATE) = CAST(GETDATE() AS DATE) THEN bork ELSE 0 END) AS today_amount,
    COUNT(DISTINCT CASE WHEN CAST (a.create_time AS DATE) = CAST(GETDATE() AS DATE) THEN userid END) AS today_number,
    SUM(CASE WHEN CAST (a.create_time AS DATE) = CAST(DATEADD(DAY, -1, GETDATE()) AS DATE) THEN bork ELSE 0 END) AS yesterday_amount,
    COUNT(DISTINCT CASE WHEN CAST (a.create_time AS DATE) = CAST(DATEADD(DAY, -1, GETDATE()) AS DATE) THEN userid END) AS yesterday_number
FROM [play_brok_records]  a  with(nolock) left join accounts u with(nolock) on a.userid=u.id where name='佣金发放'

" + temp.Replace("where", "and"));

                    //佣金领取人数
                    //今日/昨日：游戏表佣金|根据订单创建时间|类型为'用户领取'
                    sList.Add("playbrok_receive");
                    sqls.Add(@" 
SELECT
    SUM(bork*-1) AS total_amount,
    COUNT(DISTINCT userid) AS total_number,
    SUM(CASE WHEN CAST (a.create_time AS DATE) = CAST(GETDATE() AS DATE) THEN bork*-1 ELSE 0 END) AS today_amount,
    COUNT(DISTINCT CASE WHEN CAST (a.create_time AS DATE) = CAST(GETDATE() AS DATE) THEN userid END) AS today_number,
    SUM(CASE WHEN CAST (a.create_time AS DATE) = CAST(DATEADD(DAY, -1, GETDATE()) AS DATE) THEN bork*-1 ELSE 0 END) AS yesterday_amount,
    COUNT(DISTINCT CASE WHEN CAST (a.create_time AS DATE) = CAST(DATEADD(DAY, -1, GETDATE()) AS DATE) THEN userid END) AS yesterday_number
FROM [play_brok_records]  a  with(nolock) left join accounts u with(nolock) on a.userid=u.id where name='用户领取'

" + temp.Replace("where", "and"));


                    //新用户买币总额
                    //今日/昨日：根据订单购买时间|用户类型为'新用户'
                    sList.Add("newuser_recharge_dating");
                    sqls.Add(@" 
  SELECT
    SUM(a.amount) AS total_amount,
    COUNT(DISTINCT userid) AS total_number,
    SUM(CASE WHEN CAST(buy_time AS DATE) = CAST(GETDATE() AS DATE) THEN a.amount ELSE 0 END) AS today_amount,
    COUNT(DISTINCT CASE WHEN CAST(buy_time AS DATE) = CAST(GETDATE() AS DATE) THEN userid END) AS today_number,
    SUM(CASE WHEN CAST(buy_time AS DATE) = CAST(DATEADD(DAY, -1, GETDATE()) AS DATE) THEN a.amount ELSE 0 END) AS yesterday_amount,
    COUNT(DISTINCT CASE WHEN CAST(buy_time AS DATE) = CAST(DATEADD(DAY, -1, GETDATE()) AS DATE) THEN userid END) AS yesterday_number
FROM [buy_list] a  with(nolock) left join accounts u with(nolock) on a.userid=u.id where a.state=1 and a.user_type='reg' and userid>0

" + temp.Replace("create_time", "buy_time").Replace("where", "and"));



                    //新用户提现总额
                    //今日/昨日：根据任务完成时间|用户为'新用户'|排除一键任务订单
                    sList.Add("newuser_cash_money");
                    sqls.Add(@" 
SELECT
    SUM(a.orderAmt) AS total_amount,
    COUNT(DISTINCT userid) AS total_number,
    COUNT(0) AS total_daily_number,
    SUM(CASE WHEN CAST(finish_time AS DATE) = CAST(GETDATE() AS DATE) THEN a.orderAmt ELSE 0 END) AS today_amount,
    COUNT(DISTINCT CASE WHEN CAST(finish_time AS DATE) = CAST(GETDATE() AS DATE) THEN userid END) AS today_number,
    COUNT(CASE WHEN CAST(finish_time AS DATE) = CAST(GETDATE() AS DATE) THEN 1 END) AS today_daily_number,
    SUM(CASE WHEN CAST(finish_time AS DATE) = CAST(DATEADD(DAY, -1, GETDATE()) AS DATE) THEN a.orderAmt ELSE 0 END) AS yesterday_amount,
    COUNT(DISTINCT CASE WHEN CAST(finish_time AS DATE) = CAST(DATEADD(DAY, -1, GETDATE()) AS DATE) THEN userid END) AS yesterday_number,
    COUNT(CASE WHEN CAST(finish_time AS DATE) = CAST(DATEADD(DAY, -1, GETDATE()) AS DATE) THEN 1 END) AS yesterday_daily_number
FROM (
    SELECT userid,orderAmt,finish_time,state,order_mode,user_type,order_type FROM [transport_orders] WITH (NOLOCK)
    UNION ALL
    SELECT userid,orderAmt,finish_time,state,order_mode,user_type,order_type FROM [storage_transport_orders] WITH (NOLOCK)
) AS a  left join accounts u with(nolock) on a.userid=u.id where a.state=1  and a.user_type='reg' and isnull(a.order_mode,0)=0 and isnull(a.order_type,'')<>'ot'

" + temp.Replace("create_time", "finish_time").Replace("where", "and"));


                    //托号提现总额
                    //今日/昨日：根据任务完成时间|用户为'托号'|排除一键任务订单
                    sList.Add("th_cash_money");
                    sqls.Add(@" 
SELECT
    SUM(a.orderAmt) AS total_amount,
    COUNT(DISTINCT userid) AS total_number,
    COUNT(0) AS total_daily_number,
    SUM(CASE WHEN CAST(finish_time AS DATE) = CAST(GETDATE() AS DATE) THEN a.orderAmt ELSE 0 END) AS today_amount,
    COUNT(DISTINCT CASE WHEN CAST(finish_time AS DATE) = CAST(GETDATE() AS DATE) THEN userid END) AS today_number,
    COUNT(CASE WHEN CAST(finish_time AS DATE) = CAST(GETDATE() AS DATE) THEN 1 END) AS today_daily_number,
    SUM(CASE WHEN CAST(finish_time AS DATE) = CAST(DATEADD(DAY, -1, GETDATE()) AS DATE) THEN a.orderAmt ELSE 0 END) AS yesterday_amount,
    COUNT(DISTINCT CASE WHEN CAST(finish_time AS DATE) = CAST(DATEADD(DAY, -1, GETDATE()) AS DATE) THEN userid END) AS yesterday_number,
    COUNT(CASE WHEN CAST(finish_time AS DATE) = CAST(DATEADD(DAY, -1, GETDATE()) AS DATE) THEN 1 END) AS yesterday_daily_number
FROM (
    SELECT userid,orderAmt,finish_time,state,order_mode,user_type,order_type FROM [transport_orders] WITH (NOLOCK)
    UNION ALL
    SELECT userid,orderAmt,finish_time,state,order_mode,user_type,order_type FROM [storage_transport_orders] WITH (NOLOCK)
) AS a  left join accounts u with(nolock) on a.userid=u.id where a.state=1  and isnull(a.order_mode,0)=0 and isnull(a.order_type,'')<>'ot'

" + temp.Replace("create_time", "finish_time").Replace("where", "and").Replace(" not in", " in").Replace("isnull(u.groupid,-1)<>", "isnull(u.groupid,-1)="));


                    //新用户佣金
                    //今日/昨日：根据订单创建时间|用户为'新用户'|** 注意这个不限制佣金来源，前面的普通用户佣金只显示类型为'刷单'的佣金,不包含一键任务的佣金 **|公式=订单*佣金率
                    sList.Add("newuser_yongjing_qd");
                    sqls.Add(@" 
SELECT
    SUM(award_amount) AS total_amount,
    COUNT(DISTINCT userid) AS total_number,
    SUM(CASE WHEN CAST (a.create_time AS DATE) = CAST(GETDATE() AS DATE) THEN award_amount ELSE 0 END) AS today_amount,
    COUNT(DISTINCT CASE WHEN CAST (a.create_time AS DATE) = CAST(GETDATE() AS DATE) THEN userid END) AS today_number,
    SUM(CASE WHEN CAST (a.create_time AS DATE) = CAST(DATEADD(DAY, -1, GETDATE()) AS DATE) THEN award_amount ELSE 0 END) AS yesterday_amount,
    COUNT(DISTINCT CASE WHEN CAST (a.create_time AS DATE) = CAST(DATEADD(DAY, -1, GETDATE()) AS DATE) THEN userid END) AS yesterday_number
FROM [brok_list]  a  with(nolock) left join accounts u with(nolock) on a.userid=u.id where rank=-1  and a.user_type='reg'

" + temp.Replace("where", "and"));


                    //新用户一级
                    //今日/昨日：根据订单创建时间|用户为'新用户'|不包含一键任务的佣金 **|公式=佣金*一级佣金返佣率
                    sList.Add("newuser_yongjing_yj");
                    sqls.Add(@" 
SELECT
    SUM(award_amount) AS total_amount,
    COUNT(DISTINCT userid) AS total_number,
    SUM(CASE WHEN CAST (a.create_time AS DATE) = CAST(GETDATE() AS DATE) THEN award_amount ELSE 0 END) AS today_amount,
    COUNT(DISTINCT CASE WHEN CAST (a.create_time AS DATE) = CAST(GETDATE() AS DATE) THEN userid END) AS today_number,
    SUM(CASE WHEN CAST (a.create_time AS DATE) = CAST(DATEADD(DAY, -1, GETDATE()) AS DATE) THEN award_amount ELSE 0 END) AS yesterday_amount,
    COUNT(DISTINCT CASE WHEN CAST (a.create_time AS DATE) = CAST(DATEADD(DAY, -1, GETDATE()) AS DATE) THEN userid END) AS yesterday_number
FROM [brok_list]  a  with(nolock) left join accounts u with(nolock) on a.userid=u.id where rank=1   and a.user_type='reg'

" + temp.Replace("where", "and"));

                    //新用户二级
                    //今日/昨日：根据订单创建时间|用户为'新用户'|不包含一键任务的佣金 **|公式=佣金*二级佣金返佣率
                    sList.Add("newuser_yongjing_ej");
                    sqls.Add(@" 
SELECT
    SUM(award_amount) AS total_amount,
    COUNT(DISTINCT userid) AS total_number,
    SUM(CASE WHEN CAST (a.create_time AS DATE) = CAST(GETDATE() AS DATE) THEN award_amount ELSE 0 END) AS today_amount,
    COUNT(DISTINCT CASE WHEN CAST (a.create_time AS DATE) = CAST(GETDATE() AS DATE) THEN userid END) AS today_number,
    SUM(CASE WHEN CAST (a.create_time AS DATE) = CAST(DATEADD(DAY, -1, GETDATE()) AS DATE) THEN award_amount ELSE 0 END) AS yesterday_amount,
    COUNT(DISTINCT CASE WHEN CAST (a.create_time AS DATE) = CAST(DATEADD(DAY, -1, GETDATE()) AS DATE) THEN userid END) AS yesterday_number
FROM [brok_list]  a  with(nolock) left join accounts u with(nolock) on a.userid=u.id where rank=2  and a.user_type='reg'

" + temp.Replace("where", "and"));

                    //新用户三级
                    //今日/昨日：根据订单创建时间|用户为'新用户'|不包含一键任务的佣金 **|公式=佣金*三级佣金返佣率
                    sList.Add("newuser_yongjing_sj");
                    sqls.Add(@" 
SELECT
    SUM(award_amount) AS total_amount,
    COUNT(DISTINCT userid) AS total_number,
    SUM(CASE WHEN CAST (a.create_time AS DATE) = CAST(GETDATE() AS DATE) THEN award_amount ELSE 0 END) AS today_amount,
    COUNT(DISTINCT CASE WHEN CAST (a.create_time AS DATE) = CAST(GETDATE() AS DATE) THEN userid END) AS today_number,
    SUM(CASE WHEN CAST (a.create_time AS DATE) = CAST(DATEADD(DAY, -1, GETDATE()) AS DATE) THEN award_amount ELSE 0 END) AS yesterday_amount,
    COUNT(DISTINCT CASE WHEN CAST (a.create_time AS DATE) = CAST(DATEADD(DAY, -1, GETDATE()) AS DATE) THEN userid END) AS yesterday_number
FROM [brok_list]  a  with(nolock) left join accounts u with(nolock) on a.userid=u.id where rank=3  and a.user_type='reg'

" + temp.Replace("where", "and"));

                    //一键任务
                    //今日/昨日：根据订单创建时间
                    sList.Add("onetouch_total");
                    sqls.Add(@" 
  SELECT
    SUM(a.amount) AS total_amount,
    SUM(a.award_amount) AS total_award_amount,
    COUNT(DISTINCT userid) AS total_number,
    SUM(CASE WHEN CAST(a.create_time AS DATE) = CAST(GETDATE() AS DATE) THEN a.amount ELSE 0 END) AS today_amount,
    SUM(CASE WHEN CAST(a.create_time AS DATE) = CAST(GETDATE() AS DATE) THEN a.award_amount ELSE 0 END) AS today_award_amount,
    COUNT(DISTINCT CASE WHEN CAST(a.create_time AS DATE) = CAST(GETDATE() AS DATE) THEN userid END) AS today_number,
    SUM(CASE WHEN CAST(a.create_time AS DATE) = CAST(DATEADD(DAY, -1, GETDATE()) AS DATE) THEN a.amount ELSE 0 END) AS yesterday_amount,
    SUM(CASE WHEN CAST(a.create_time AS DATE) = CAST(DATEADD(DAY, -1, GETDATE()) AS DATE) THEN a.award_amount ELSE 0 END) AS yesterday_award_amount,
    COUNT(DISTINCT CASE WHEN CAST(a.create_time AS DATE) = CAST(DATEADD(DAY, -1, GETDATE()) AS DATE) THEN userid END) AS yesterday_number
FROM [task_onetouch] a  with(nolock) left join accounts u with(nolock) on a.userid=u.id where a.state=a.state

" + temp.Replace("create_time", "create_time").Replace("where", "and"));

                    //当前任务未结束
                    //今日/昨日：根据订单开始时间|状态为'未结束'
                    sList.Add("onetouch_task");
                    sqls.Add((@" 
  SELECT
    SUM(a.amount) AS total_amount,
    SUM(a.award_amount) AS total_award_amount,
    COUNT(DISTINCT userid) AS total_number,
    SUM(CASE WHEN CAST(a.create_time AS DATE) = CAST(GETDATE() AS DATE) THEN a.amount ELSE 0 END) AS today_amount,
    SUM(CASE WHEN CAST(a.create_time AS DATE) = CAST(GETDATE() AS DATE) THEN a.award_amount ELSE 0 END) AS today_award_amount,
    COUNT(DISTINCT CASE WHEN CAST(a.create_time AS DATE) = CAST(GETDATE() AS DATE) THEN userid END) AS today_number,
    SUM(CASE WHEN CAST(a.create_time AS DATE) = CAST(DATEADD(DAY, -1, GETDATE()) AS DATE) THEN a.amount ELSE 0 END) AS yesterday_amount,
    SUM(CASE WHEN CAST(a.create_time AS DATE) = CAST(DATEADD(DAY, -1, GETDATE()) AS DATE) THEN a.award_amount ELSE 0 END) AS yesterday_award_amount,
    COUNT(DISTINCT CASE WHEN CAST(a.create_time AS DATE) = CAST(DATEADD(DAY, -1, GETDATE()) AS DATE) THEN userid END) AS yesterday_number
FROM [task_onetouch] a  with(nolock) left join accounts u with(nolock) on a.userid=u.id where a.state=100

" + temp.Replace("where", "and")).Replace("create_time", "start_time"));

                    //已结束任务
                    //今日/昨日：根据订单结束时间|状态为'已结束'
                    sList.Add("onetouch_finish");
                    sqls.Add((@" 
  SELECT
    SUM(a.amount) AS total_amount,
    SUM(a.award_amount) AS total_award_amount,
    COUNT(DISTINCT userid) AS total_number,
    SUM(CASE WHEN CAST(a.create_time AS DATE) = CAST(GETDATE() AS DATE) THEN a.amount ELSE 0 END) AS today_amount,
    SUM(CASE WHEN CAST(a.create_time AS DATE) = CAST(GETDATE() AS DATE) THEN a.award_amount ELSE 0 END) AS today_award_amount,
    COUNT(DISTINCT CASE WHEN CAST(a.create_time AS DATE) = CAST(GETDATE() AS DATE) THEN userid END) AS today_number,
    SUM(CASE WHEN CAST(a.create_time AS DATE) = CAST(DATEADD(DAY, -1, GETDATE()) AS DATE) THEN a.amount ELSE 0 END) AS yesterday_amount,
    SUM(CASE WHEN CAST(a.create_time AS DATE) = CAST(DATEADD(DAY, -1, GETDATE()) AS DATE) THEN a.award_amount ELSE 0 END) AS yesterday_award_amount,
    COUNT(DISTINCT CASE WHEN CAST(a.create_time AS DATE) = CAST(DATEADD(DAY, -1, GETDATE()) AS DATE) THEN userid END) AS yesterday_number
FROM [task_onetouch] a  with(nolock) left join accounts u with(nolock) on a.userid=u.id where a.state=1

" + temp.Replace("where", "and")).Replace("create_time", "expire_time"));

                    //fz.jsonResponse(sqls[sqls.Count - 1]);


                    //游戏转入人数
                    //今日/昨日：根据订单完成时间|类型为'游戏转入'
                    sList.Add("game_recharge");
                    sqls.Add(@" 
SELECT

    SUM(a.amount)*-1 AS total_amount,
    COUNT(DISTINCT userid) AS total_number,

    SUM(CASE WHEN CAST(finish_time AS DATE) = CAST(GETDATE() AS DATE) THEN a.amount ELSE 0 END)*-1 AS today_amount,
    COUNT(DISTINCT CASE WHEN CAST(finish_time AS DATE) = CAST(GETDATE() AS DATE) THEN userid END) AS today_number,

    SUM(CASE WHEN CAST(finish_time AS DATE) = CAST(DATEADD(DAY, -1, GETDATE()) AS DATE) THEN a.amount ELSE 0 END)*-1 AS yesterday_amount,
    COUNT(DISTINCT CASE WHEN CAST(finish_time AS DATE) = CAST(DATEADD(DAY, -1, GETDATE()) AS DATE) THEN userid END) AS yesterday_number

FROM [transaction_list] a with(nolock) left join accounts u with(nolock) on a.userid=u.id where a.type='游戏转入'

" + temp.Replace("create_time", "finish_time").Replace("where", "and"));

                    //游戏转出人数
                    //今日/昨日：根据订单完成时间|类型为'游戏转出'
                    sList.Add("game_withdraw");
                    sqls.Add(@" 
SELECT

    SUM(a.amount) AS total_amount,
    COUNT(DISTINCT userid) AS total_number,

    SUM(CASE WHEN CAST(finish_time AS DATE) = CAST(GETDATE() AS DATE) THEN a.amount ELSE 0 END) AS today_amount,
    COUNT(DISTINCT CASE WHEN CAST(finish_time AS DATE) = CAST(GETDATE() AS DATE) THEN userid END) AS today_number,

    SUM(CASE WHEN CAST(finish_time AS DATE) = CAST(DATEADD(DAY, -1, GETDATE()) AS DATE) THEN a.amount ELSE 0 END) AS yesterday_amount,
    COUNT(DISTINCT CASE WHEN CAST(finish_time AS DATE) = CAST(DATEADD(DAY, -1, GETDATE()) AS DATE) THEN userid END) AS yesterday_number

FROM [transaction_list] a with(nolock) left join accounts u with(nolock) on a.userid=u.id where a.type='游戏转出'

" + temp.Replace("create_time", "finish_time").Replace("where", "and"));

                    //新增游戏人数
                    //今日/昨日：根据资金明细创建时间|类型为'游戏转入'或'游戏转出'
                    sList.Add("game_newuser");
                    sqls.Add(@" 
SELECT

    SUM(a.amount)*-1 AS total_amount,
    COUNT(DISTINCT a.userid) AS total_number,

    SUM(CASE WHEN CAST(al.create_time AS DATE) = CAST(GETDATE() AS DATE) THEN a.amount ELSE 0 END)*-1 AS today_amount,
    COUNT(DISTINCT CASE WHEN CAST(al.create_time AS DATE) = CAST(GETDATE() AS DATE) THEN a.userid END) AS today_number,

    SUM(CASE WHEN CAST(al.create_time AS DATE) = CAST(DATEADD(DAY, -1, GETDATE()) AS DATE) THEN a.amount ELSE 0 END)*-1 AS yesterday_amount,
    COUNT(DISTINCT CASE WHEN CAST(al.create_time AS DATE) = CAST(DATEADD(DAY, -1, GETDATE()) AS DATE) THEN a.userid END) AS yesterday_number

FROM [transaction_list] a with(nolock) left join accounts u with(nolock) on a.userid=u.id left join [authorize_list] al on al.userid=a.userid where (a.type='游戏转入' or a.type='游戏转出')

" + temp.Replace("create_time", "finish_time").Replace("where", "and"));




                    //体验金
                    //体验金（今日/昨日：根据资金明细创建时间）
                    //当前体验金未结束（今日/昨日：根据资金明细创建时间|完成时间为空）
                    //当前体验金未结束（今日/昨日：根据资金明细完成时间|完成时间存在值）
                    sList.Add("experience_amount");
                    sqls.Add(@" 
  SELECT
    SUM(a.amount) AS total_amount,
    COUNT(DISTINCT userid) AS total_number,
    SUM(CASE WHEN CAST(a.create_time AS DATE) = CAST(GETDATE() AS DATE) THEN a.amount ELSE 0 END) AS today_amount,
    COUNT(DISTINCT CASE WHEN CAST(a.create_time AS DATE) = CAST(GETDATE() AS DATE) THEN userid END) AS today_number,
    SUM(CASE WHEN CAST(a.create_time AS DATE) = CAST(DATEADD(DAY, -1, GETDATE()) AS DATE) THEN a.amount ELSE 0 END) AS yesterday_amount,
    COUNT(DISTINCT CASE WHEN CAST(a.create_time AS DATE) = CAST(DATEADD(DAY, -1, GETDATE()) AS DATE) THEN userid END) AS yesterday_number,



    SUM(CASE WHEN finish_time is null THEN a.amount ELSE 0 END) AS task_total_amount,
    COUNT(DISTINCT CASE WHEN finish_time is null THEN userid END) AS task_total_number,
    SUM(CASE WHEN finish_time is not null THEN a.amount ELSE 0 END) AS finish_total_amount,
    COUNT(DISTINCT CASE WHEN finish_time is not null THEN userid END) AS finish_total_number,


    SUM(CASE WHEN CAST(a.create_time AS DATE) = CAST(GETDATE() AS DATE) AND finish_time is null THEN a.amount ELSE 0 END) AS task_today_amount,
    COUNT(DISTINCT CASE WHEN CAST(a.create_time AS DATE) = CAST(GETDATE() AS DATE) AND finish_time is null THEN userid END) AS task_today_number,
    SUM(CASE WHEN CAST(a.finish_time AS DATE) = CAST(GETDATE() AS DATE) AND finish_time is not null THEN a.amount ELSE 0 END) AS finish_today_amount,
    COUNT(DISTINCT CASE WHEN CAST(a.finish_time AS DATE) = CAST(GETDATE() AS DATE) AND finish_time is not null THEN userid END) AS finish_today_number,


    SUM(CASE WHEN CAST(a.create_time AS DATE) = CAST(DATEADD(DAY, -1, GETDATE()) AS DATE) AND finish_time is null THEN a.amount ELSE 0 END) AS task_yesterday_amount,
    COUNT(DISTINCT CASE WHEN CAST(a.create_time AS DATE) = CAST(DATEADD(DAY, -1, GETDATE()) AS DATE) AND finish_time is null THEN userid END) AS task_yesterday_number,
    SUM(CASE WHEN CAST(a.finish_time AS DATE) = CAST(DATEADD(DAY, -1, GETDATE()) AS DATE) AND finish_time is not null THEN a.amount ELSE 0 END) AS finish_yesterday_amount,
    COUNT(DISTINCT CASE WHEN CAST(a.finish_time AS DATE) = CAST(DATEADD(DAY, -1, GETDATE()) AS DATE) AND finish_time is not null THEN userid END) AS finish_yesterday_number

FROM [experience_amount] a  with(nolock) left join accounts u with(nolock) on a.userid=u.id where a.userid=a.userid

" + temp.Replace("create_time", "create_time").Replace("where", "and"));



                    //赠送统计
                    //买币赠送：资金明细类型为'买币赠送'的统计
                    //红包领取金额：资金明细同时满足（类型为'转账'|备注为'红包收入'|数据库标签为'红包雨'）的统计
                    //任务奖励：资金明细类型为'任务奖励'的统计
                    //转盘奖励：资金明细类型为'转盘奖励'的统计
                    //出借利息：资金明细类型为'出借利息'的统计
                    sList.Add("gift_total");
                    sqls.Add(@"
SELECT 
ISNULL(SUM(case when type='买币赠送' then a.amount else 0 end),0) as 买币赠送,
ISNULL(SUM(case when (type='转账' and remark='红包收入' and recharge_network='红包雨') then a.amount else 0 end),0) as 红包领取金额 ,
ISNULL(SUM(case when type='任务奖励' then a.amount else 0 end),0) as 任务奖励 ,
ISNULL(SUM(case when type='转盘奖励' then a.amount else 0 end),0) as 转盘奖励 ,
ISNULL(SUM(case when type='出借利息' then a.amount else 0 end),0) as 出借利息 , 

ISNULL(SUM(case when CAST(a.create_time AS DATE) = CAST(GETDATE() AS DATE) and type='买币赠送' then a.amount else 0 end),0) as 今日_买币赠送,
ISNULL(SUM(case when CAST(a.create_time AS DATE) = CAST(GETDATE() AS DATE) and  (type='转账' and remark='红包收入' and recharge_network='红包雨') then a.amount else 0 end),0) as 今日_红包领取金额 ,
ISNULL(SUM(case when CAST(a.create_time AS DATE) = CAST(GETDATE() AS DATE) and  type='任务奖励' then a.amount else 0 end),0) as 今日_任务奖励 ,
ISNULL(SUM(case when CAST(a.create_time AS DATE) = CAST(GETDATE() AS DATE) and  type='转盘奖励' then a.amount else 0 end),0) as 今日_转盘奖励 ,
ISNULL(SUM(case when CAST(a.create_time AS DATE) = CAST(GETDATE() AS DATE) and  type='出借利息' then a.amount else 0 end),0) as 今日_出借利息 , 

ISNULL(SUM(case when CAST(a.create_time AS DATE) = CAST(DATEADD(DAY, -1, GETDATE()) AS DATE) and  type='买币赠送' then a.amount else 0 end),0) as 昨日_买币赠送,
ISNULL(SUM(case when CAST(a.create_time AS DATE) = CAST(DATEADD(DAY, -1, GETDATE()) AS DATE) and   (type='转账' and remark='红包收入' and recharge_network='红包雨') then a.amount else 0 end),0) as 昨日_红包领取金额 ,
ISNULL(SUM(case when CAST(a.create_time AS DATE) = CAST(DATEADD(DAY, -1, GETDATE()) AS DATE) and   type='任务奖励' then a.amount else 0 end),0) as 昨日_任务奖励 ,
ISNULL(SUM(case when CAST(a.create_time AS DATE) = CAST(DATEADD(DAY, -1, GETDATE()) AS DATE) and   type='转盘奖励' then a.amount else 0 end),0) as 昨日_转盘奖励 ,
ISNULL(SUM(case when CAST(a.create_time AS DATE) = CAST(DATEADD(DAY, -1, GETDATE()) AS DATE) and   type='出借利息' then a.amount else 0 end),0) as 昨日_出借利息 

from [transaction_list] a with(nolock) left join accounts u with(nolock) on a.userid=u.id where (type='买币赠送' or (type='转账' and remark='红包收入' and recharge_network='红包雨') or type='任务奖励' or type='转盘奖励' or type='出借利息')

" + temp.Replace("where", "and"));




                    //Response.Write(string.Join("\r\n", sList));
                    //Response.End();

                    sList_refresh = sList;
                    sList = new List<string>();
                    for (int i = 0; i < sList_refresh.Count; i++)
                    {
                        pmlist[sList_refresh[i] + "_cache_second"] = "300";
                    }
                    pmlist["recharge_dating" + "_cache_second"] = "500";
                    pmlist["recharge_sanfang" + "_cache_second"] = "500";
                    pmlist["recharge_action" + "_cache_second"] = "500";
                    pmlist["recharge_admin" + "_cache_second"] = "500";
                    pmlist["deduct_admin" + "_cache_second"] = "500";


                    pmlist["user_cash_money" + "_cache_second"] = "600";
                    pmlist["yongjing_qd" + "_cache_second"] = "600";
                    pmlist["yongjing_yj" + "_cache_second"] = "600";
                    pmlist["yongjing_ej" + "_cache_second"] = "600";
                    pmlist["yongjing_sj" + "_cache_second"] = "600";


                    pmlist["gift_total" + "_cache_second"] = "800";
                    pmlist["firstbuy_number" + "_cache_second"] = "700";


                    sql = string.Empty;
                    pmlist["current_date"] = DateTime.Now.ToString("yyyyMMdd");
                    for (int i = 0; i < sList_refresh.Count; i++)
                    {
                        switch (sList_refresh[i])
                        {
                            case "online_users":
                                //实时数据不统计
                                break;
                            default:
                                temp = cae.GetCache<string>(sList_refresh[i] + "_indexRef");
                                if (!string.IsNullOrEmpty(temp))
                                {
                                    if (Convert.ToDateTime(temp).ToString("yyyyMMdd") != pmlist["current_date"] + "")
                                    {
                                        //次日直接刷新数据
                                        temp = null;
                                    }
                                }

                                if (Request.Url.Host == "localhost" || fz.req("refresh_db") == sList_refresh[i])
                                {
                                    temp = null;
                                }
                                if (string.IsNullOrEmpty(temp))
                                {
                                    temp = pmlist[sList_refresh[i] + "_cache_second"] + "";
                                    cae.SetCache(sList_refresh[i] + "_indexRef", DateTime.Now.AddSeconds(Convert.ToInt16(temp)).ToString(), DateTime.Now.AddSeconds(Convert.ToInt16(temp)));

                                    sList.Add(sList_refresh[i]);
                                    sql += sqls[i];

                                    //Response.Write(sList_refresh[i] + "\r\n" + sqls[i] + "\r\n-----------\r\n");

                                }



                                break;
                        }
                    }


                    dic = new Dictionary<string, object>();
                    g = fz.req("ss").Split(',');
                    if (!string.IsNullOrEmpty(sql))
                    {
                        ds = db.getDataSet(sql, pams.ToArray());
                        for (int i = 0; i < sList.Count; i++)
                        {
                            bool isAuto = false;
                            if (sList[i] == "gift_total" || sList[i].IndexOf("onetouch") != -1)
                            {
                                isAuto = true;
                            }
                            temp = JsonMapper.ToJson(get_totalDic(ds.Tables[i], isAuto));
                            if (fz.req("details") == "1" || g.Contains(sList[i]))
                            {
                                dic.Add(sList[i], ToDictionary(JsonMapper.ToObject(temp)));
                            }
                            cae.SetCache(sList[i] + "_indexData_" + pmlist["current_date"], temp, DateTime.Now.AddDays(1));
                        }
                    }

                    dic.Add("list", sList);
                    fz.sendResponse("success", 1, dic);
                }
                break;
            case "pushToStorage":

                pmlist["storage_db"] = "storage_" + fz.req("db");
                pmlist["db"] = fz.req("db");
                pmlist["where"] = "";

                switch (fz.req("db"))
                {
                    case "transport_orders":
                    case "play_records":
                        break;
                    case "chat_messages":
                        pmlist["where"] = " and type='group' ";
                        break;
                    case "th_transport_orders":
                        //将storage表的订单转入托号表
                        pmlist["where"] = " and isnull(user_type,'')='th' ";
                        pmlist["storage_db"] = fz.req("db");
                        pmlist["db"] = "storage_" + fz.req("db").Replace("th_", "");
                        break;
                    default:
                        fz.sendResponse("当前表无法维护");
                        break;
                }

                pmlist["time"] = "create_time";
                switch (fz.req("time"))
                {
                    case "1":
                        pmlist["time"] = "finish_time";
                        break;
                    default:
                        break;
                }

                Stopwatch stopwatch = new Stopwatch();
                stopwatch.Start();


                pmlist["storage_day"] = "7";
                if (!fz.empty("day"))
                {
                    try
                    {
                        if (Convert.ToInt16(fz.req("day")) > 7)
                        {
                            pmlist["storage_day"] = Convert.ToInt16(fz.req("day"));
                        }
                    }
                    catch (Exception)
                    {
                    }
                }

                temp = DateTime.Now.AddDays(-1 * Convert.ToInt16(pmlist["storage_day"])).ToString("yyyy-MM-dd") + " 00:00:00";

                switch (fz.req("db"))
                {
                    case "chat_messages":
                        temp = DateTime.Now.AddDays(-1).ToString("yyyy-MM-dd") + " 00:00:00";
                        break;
                    default:
                        break;
                }



                pams.Add(new SqlParameter("@record_time", temp));
                sql = @"

BEGIN TRY

  DECLARE @tab1 TABLE (id INT);

  insert into {storage_db} OUTPUT inserted.id INTO @tab1 select top 5000 * from {db} with(nolock) where {time}<@record_time {where} order by id

  delete {db} where {time}<@record_time and id in (select id from @tab1)

END TRY
BEGIN CATCH
END CATCH;

"
                    .Replace("{where}", pmlist["where"] + "")
                    .Replace("{time}", pmlist["time"] + "")
                    .Replace("{storage_db}", pmlist["storage_db"] + "")
                    .Replace("{db}", pmlist["db"] + "");


                //fz.jsonResponse(sql.Replace("@record_time", "'" + temp + "'"));

                res = db.ExecuteNonQuery(sql, pams.ToArray());

                stopwatch.Stop();
                pmlist["hs"] = (stopwatch.ElapsedMilliseconds / 1000.0).ToString("0.00");
                dic = new Dictionary<string, object>();
                dic.Add("hs", pmlist["hs"] + "");

                if (res > 0)
                {
                    fz.sendResponse("已转入" + (res / 2) + "条数据", 1, dic);
                }
                else
                {
                    fz.sendResponse("暂无需处理订单", 1, dic);
                }

                break;
            //预设订单
            case "preset_onetouch_orders":

                //fz.sendResponse("稍后更新");

                pmlist["_uuid"] = "@uid{0}";
                //一级（直属）
                pmlist["agent_sql"] = "parentid=" + pmlist["_uuid"];

                //三级
                pmlist["agent_sql"] = "(CASE WHEN CHARINDEX(@sid, ','+ISNULL(relaids,'')) > 0 THEN LEN(SUBSTRING(','+ISNULL(relaids,''), CHARINDEX(@sid, ','+ISNULL(relaids,'')) + LEN(@sid), LEN(','+ISNULL(relaids,'')))) - LEN(REPLACE(SUBSTRING(','+ISNULL(relaids,''), CHARINDEX(@sid, ','+ISNULL(relaids,'')) + LEN(@sid), LEN(','+ISNULL(relaids,''))), ',', '')) ELSE 999 END)".Replace("@sid", "'," + pmlist["_uuid"] + ",'");
                pmlist["agent_sql"] = "  id<>" + pmlist["_uuid"] + " and " + pmlist["agent_sql"] + "<4 and " + pmlist["agent_sql"] + ">0 ";


                pmlist["update_sql"] = "last_task_time=getdate(),last_task_balacne=isnull(balance,0)+isnull(game_balance,0),task_days=task_days+1";
                //if (fz.req("test") == "1")
                //{
                //    pmlist["update_sql"] = "task_days=task_days+0";
                //}


                //pmlist["update_sql"] = "last_task_time=last_task_time";
                if (fz.req("test") == "1")
                {
                    //测试用，无限派发
                    pmlist["update_sql"] = "last_task_time=last_task_time";
                }


                //检索未派发订单
                sql = " update [task_onetouch] set " + pmlist["update_sql"] + " output deleted.*,DATEDIFF(day,deleted.start_time,GETDATE())+1 as current_day,CONVERT(VARCHAR(10), getdate(), 120)+' '+CONVERT(VARCHAR(8), deleted.start_time, 108) as this_startTime,CONVERT(VARCHAR(10),DATEADD(day,1, getdate()), 120)+' '+CONVERT(VARCHAR(8), deleted.start_time, 108) as this_endTime,isnull(deleted.balance,0)+isnull(deleted.game_balance,0) as total_balance where id in (select top 10 id from task_onetouch with(nolock) where  state=100 and start_time<getdate() and expire_time>getdate() and task_days<days " + " and datediff(day,ISNULL(last_task_time,-1),getdate())<>0 " + " and (amount=isnull(balance,0) or DATEDIFF(DAY,ISNULL(game_update_time,0),GETDATE())=0) " + " )   ";//and datediff(day,ISNULL(last_task_time,-1),getdate())<>0

                dt = db.getDataTable(sql, pams.ToArray());


                //fz.sendResponse(ToJson(dt));

                pmlist["total_number"] = dt.Rows.Count;

                pmlist["allow_sd_time"] = uConfig.stcdata("allow_sd_time");
                pmlist["task_starttime"] = (pmlist["allow_sd_time"] + "").Split('~')[0];
                pmlist["task_endtime"] = (pmlist["allow_sd_time"] + "").Split('~')[1];

                //pmlist["onetouch_number"] = uConfig.stcdata("onetouch_number");
                pmlist["success_number"] = "0";
                pmlist["fail_number"] = "0";

                pams = new List<SqlParameter>();
                pams.Add(new SqlParameter("@sd_valid_money", uConfig.stcdata("sd_valid_money")));
                sql = string.Empty;
                sql = @" 
declare @did int 

declare @rate_award decimal(5,2)
declare @award_amount decimal(18,2)
declare @serve_amount decimal(18,2)
declare @levelid int
declare @valid_user_number int
declare @sd_orderNo varchar(32)

";


                pmlist["onetouch_minAmt"] = uConfig.stcdata("onetouch_minAmt");
                pmlist["onetouch_maxAmt"] = uConfig.stcdata("onetouch_maxAmt");

                for (int i = 0; i < dt.Rows.Count; i++)
                {
                    g = (dt.Rows[i]["succ_rate"] + "").Split('~');
                    res = get_random(g[0], g[1]);


                    //本次开始结束时间访问（子订单24小时派发）
                    pmlist["this_startTime"] = dt.Rows[i]["this_startTime"] + "";
                    pmlist["this_endTime"] = dt.Rows[i]["this_endTime"] + "";


                    //计算分配单量
                    pmlist["rate_data"] = res;
                    pmlist["rate_val"] = Convert.ToDouble(res / 100.00).ToString("0.00");












                    //pmlist["success_number"] = (Convert.ToDouble(pmlist["onetouch_number"]) * Convert.ToDouble(pmlist["rate_val"])).ToString("0");
                    ////if (Convert.ToInt16(pmlist["success_number"]) == Convert.ToInt16(pmlist["onetouch_number"]))
                    ////{
                    ////    pmlist["success_number"] = (Convert.ToInt16(pmlist["success_number"]) - 1).ToString("0");
                    ////}
                    //pmlist["fail_number"] = (Convert.ToInt16(pmlist["onetouch_number"]) - Convert.ToInt16(pmlist["success_number"])).ToString("0");


                    //计算成功/失败金额
                    pmlist["amount"] = dt.Rows[i]["total_balance"] + "";
                    pmlist["task_amount"] = dt.Rows[i]["amount"] + "";

                    if (Convert.ToDouble(pmlist["amount"] + "") > Convert.ToDouble(pmlist["task_amount"] + ""))
                    {
                        //发生amount（额度）>task_amount（一键任务金额）的情况：当blance跟金额一直的时候，不会更新game_balance,但是total_balance=balance+game_balance(这个数据有可能是其他日期的数据没更新)
                        pmlist["amount"] = pmlist["task_amount"];
                    }

                    pmlist["award_amount"] = dt.Rows[i]["award_amount"] + "";
                    pmlist["total_amount"] = Convert.ToDouble(pmlist["amount"]) + Convert.ToDouble(pmlist["award_amount"]);

                    //pmlist["success_amount"] = (Convert.ToDouble(pmlist["total_amount"]) / 100 * res).ToString("0.00");
                    //pmlist["fail_amount"] = Convert.ToDouble(pmlist["total_amount"]) - Convert.ToDouble(pmlist["success_amount"]);
                    //if (pmlist["fail_number"] + "" == "0")
                    //{
                    //    pmlist["fail_amount"] = "0";
                    //}



                    var calcResults = SplitAmount(Convert.ToDouble(pmlist["amount"] + ""), Convert.ToDouble(pmlist["rate_data"] + ""), Convert.ToDouble(pmlist["onetouch_minAmt"] + ""), Convert.ToDouble(pmlist["onetouch_maxAmt"] + ""));
                    pmlist["success_number"] = calcResults.Item1.Count;
                    pmlist["fail_number"] = calcResults.Item2.Count;

                    pmlist["success_amount"] = 0;
                    foreach (var amount in calcResults.Item1)
                    {
                        pmlist["success_amount"] = Convert.ToDouble(pmlist["success_amount"] + "") + Convert.ToDouble(amount);
                    }

                    pmlist["fail_amount"] = 0;
                    foreach (var amount in calcResults.Item2)
                    {
                        pmlist["fail_amount"] = Convert.ToDouble(pmlist["fail_amount"] + "") + Convert.ToDouble(amount);
                    }

                    Response.Write("{ " + pmlist["amount"] + "," + pmlist["rate_data"] + "," + pmlist["onetouch_minAmt"] + "," + pmlist["onetouch_maxAmt"] + " } succ=" + pmlist["success_amount"] + "," + pmlist["success_number"] + " |- fail=" + pmlist["fail_amount"] + "," + pmlist["fail_number"] + "\r\n");


                    //防止itemid为空的情况
                    pmlist["itemid"] = dt.Rows[i]["itemid"] + "";
                    try
                    {
                        Convert.ToInt16(pmlist["itemid"]);
                    }
                    catch (Exception)
                    {
                        pmlist["itemid"] = "-1";
                    }

                    pams.Add(new SqlParameter("@itemid" + i, pmlist["itemid"] + ""));
                    pams.Add(new SqlParameter("@fid" + i, dt.Rows[i]["id"] + ""));
                    pams.Add(new SqlParameter("@uid" + i, dt.Rows[i]["userid"] + ""));
                    pams.Add(new SqlParameter("@current_day" + i, dt.Rows[i]["current_day"] + ""));
                    pams.Add(new SqlParameter("@serve_fee" + i, dt.Rows[i]["serve_fee"] + ""));

                    pams.Add(new SqlParameter("@succ_rate" + i, res));
                    pams.Add(new SqlParameter("@amount" + i, pmlist["amount"] + ""));
                    pams.Add(new SqlParameter("@award_amount" + i, pmlist["award_amount"] + ""));

                    pams.Add(new SqlParameter("@success_amount" + i, pmlist["success_amount"] + ""));
                    pams.Add(new SqlParameter("@fail_amount" + i, pmlist["fail_amount"] + ""));

                    pams.Add(new SqlParameter("@success_number" + i, pmlist["success_number"] + ""));
                    pams.Add(new SqlParameter("@fail_number" + i, pmlist["fail_number"] + ""));



                    sql += string.Format(@" 

-- 【获取任务佣金比例】

--      查询下级有效人数
set @levelid=null
set @valid_user_number=0
--select @valid_user_number=count(0) from accounts with(nolock) where {agent_sql} and trans_amount>=@sd_valid_money 
select @valid_user_number=count(0) from accounts with(nolock) where {agent_sql} and isnull(onetouch_amount,0)>=@sd_valid_money 

--      查询是否设置等级
select @levelid=levelid from accounts with(nolock) where id=@uid{0}


----      查询当前佣金比例
--set @rate_award=0.00
--select top 1 @rate_award=rate_award from levels_list with(nolock) where @valid_user_number>=minNumber order by minNumber desc


--if(@levelid is not null)
--begin    
--    select @rate_award=rate_award from levels_list with(nolock) where id=@levelid and rate_award>@rate_award
--end



-- 根据周期获取对应佣金比例
set @rate_award=0.00
select @rate_award=isnull(rate_award,0) from [task_onetouch_items] with(nolock) where id=@itemid{0} and state=1




--生成唯一单号
EXEC [Get_MapOrderId] @CodeLength=10,@name='otrec',@isnumber=1,@GeneratedCode = @sd_orderNo OUTPUT;  


-- 【创建一键任务记录】
insert into task_onetouch_records values(@fid{0},@uid{0},@sd_orderNo,@current_day{0},@succ_rate{0},@amount{0},@award_amount{0},@rate_award,@serve_fee{0},@success_amount{0},@fail_amount{0},@success_number{0},@fail_number{0},getdate()) 
set @did=scope_identity()

".Replace("{agent_sql}", (pmlist["agent_sql"] + "").Replace("@uid{0}", dt.Rows[i]["userid"] + "")), i);

                    //if (fz.req("test") == "1")
                    //{
                    //    Response.Write("sql -----\r\n");
                    //    Response.Write(getPamsSql(pams) + "\r\n");
                    //    Response.Write(sql);

                    //    //dt = db.getDataTable(sql, pams.ToArray());
                    //    //Response.Write("\r\nresult -----\r\n");
                    //    //Response.Write(ToJson(dt));
                    //    Response.End();
                    //}


                    //【成功/失败订单】生成
                    countindex = 0;
                    for (int t = 0; t < 2; t++)
                    {
                        if (t == 0)
                        {
                            //intArray = SplitNumber(Convert.ToDouble(pmlist["success_amount"] + ""), Convert.ToInt16(pmlist["success_number"] + ""));
                            intArray = calcResults.Item1;
                        }
                        else
                        {
                            if (pmlist["fail_number"] + "" == "0")
                            {
                                continue;
                            }
                            //intArray = SplitNumber(Convert.ToDouble(pmlist["fail_amount"] + ""), Convert.ToInt16(pmlist["fail_number"] + ""));
                            intArray = calcResults.Item2;
                        }

                        //生成时间组合

                        values_array = new List<string>();
                        if (intArray.Count > 0)
                        {
                            values_array = GenerateRandomTimes(pmlist["this_startTime"] + "", pmlist["this_endTime"] + "", pmlist["task_starttime"] + "", pmlist["task_endtime"] + "", intArray.Count);
                        }


                        for (int k = 0; k < values_array.Count; k++)
                        {
                            countindex++;

                            //fz.sendResponse("Time = " + pmlist["task_starttime"] + "" + "|" + pmlist["task_endtime"] + "");

                            //pams.Add(new SqlParameter("@sd_orderNo" + i + "_" + (t + "_" + k), countindex));
                            //pams.Add(new SqlParameter("@amount" + i + "_" + (t + "_" + k), intArray[k]));
                            ////pams.Add(new SqlParameter("@task_time" + i + "_" + (t + "_" + k), DateTime.Now.ToString("yyyy-MM-dd") + " " + GenerateRandomTime(pmlist["task_starttime"] + "", pmlist["task_endtime"] + "")));@amount
                            //pams.Add(new SqlParameter("@task_time" + i + "_" + (t + "_" + k), values_array[k]));

                            //                            sql += string.Format(@"
                            //set @award_amount=cast(@amount{0}_{1} as decimal(18,2))/100*@rate_award
                            //set @serve_amount=@award_amount/100*cast(@serve_fee{0} as decimal(5,2))
                            //
                            //
                            //insert into task_onetouch_preset values(@uid{0},@fid{0},@did,@sd_orderNo+'-'+cast(@sd_orderNo{0}_{1} as varchar(10)),cast(@amount{0}_{1} as decimal(18,2)),@rate_award,@award_amount,@serve_fee{0},@serve_amount,{2},@task_time{0}_{1},getdate(),null,null)
                            //", i, (t + "_" + k), (t == 0 ? 1 : -1));


                            sql += string.Format(@"
set @award_amount=cast('" + intArray[k] + @"' as decimal(18,2))/100*@rate_award
set @serve_amount=@award_amount/100*cast(@serve_fee{0} as decimal(5,2))


insert into task_onetouch_preset values(@uid{0},@fid{0},@did,@sd_orderNo+'-" + countindex + @"',cast('" + intArray[k] + @"' as decimal(18,2)),@rate_award,@award_amount,@serve_fee{0},@serve_amount,{1},'" + values_array[k] + @"',getdate(),null,null)
", i, (t == 0 ? 1 : -1));
                        }
                    }

                }

                pmlist["errmsg"] = "";
                try
                {
                    res = db.ExecuteNonQuery(sql, pams.ToArray());
                    pmlist["errmsg"] = "成功更新条数：" + res + "条";
                }
                catch (Exception ex)
                {
                    pmlist["errmsg"] = ex.Message.ToString();
                }

                log.WriteLog("preset_onetouch_orders", dt.Rows.Count.ToString(), pmlist["errmsg"] + "", true);
                if ((pmlist["errmsg"] + "").IndexOf("成功更新条数：") == -1)
                {
                    log.WriteLog("preset_onetouch_errinfo", "参数信息", getPamsSql(pams) + @"

" + sql, true);
                }

                dic.Add("total_number", pmlist["total_number"]);
                fz.sendResponse("SUCCESS", 1, dic);
                break;
            //创建订单
            case "create_onetouch_orders":


                if (fz.req("date") == "")
                {
                    pmlist["create_date"] = "getdate()";


                    if (cae.GetCache<string>("create_onetouch_orders_delay") != null)
                    {
                        fz.sendResponse("操作频繁");
                    }
                    cae.SetCache("create_onetouch_orders_delay", "1", DateTime.Now.AddSeconds(22));
                }
                else
                {
                    pmlist["create_date"] = "'" + Convert.ToDateTime(fz.req("date") + " 00:00:00").ToString("yyyy-MM-dd HH:mm:ss") + "'";
                }

                sql = " update t set execute_time=getdate() output deleted.*,otlist.payment_id,otlist.payment_type,otlist.payment_name,otlist.payment_bankname,otlist.payment_bankid from task_onetouch_preset t left join [task_onetouch] otlist with(nolock) on t.fid=otlist.id where t.id in (select top " + (pmlist["create_date"] + "" == "getdate()" ? "100" : (fz.req("num") == "" ? "1" : fz.req("num"))) + " id from task_onetouch_preset  with(nolock) where datediff(day,task_time," + pmlist["create_date"] + ")=0 " + (fz.req("ignore") == "1" ? "" : " and task_time<=getdate() ") + " and execute_time is null " + (fz.req("fid") == "" ? "" : " and fid=@fid") + ") ";



                dt = db.getDataTable(sql, pams.ToArray());

                if (dt.Rows.Count == 0)
                {
                    fz.sendResponse("暂无订单");
                }

                for (int i = 0; i < dt.Rows.Count; i++)
                {
                    text_array.Add(dt.Rows[i]["id"] + "");
                }

                pmlist["total_number"] = dt.Rows.Count;


                pmlist["th_groupId"] = "-1";
                temp_dt = selectDateTable(chelper.gdt("account_groups"), " name='★托号' ");
                if (temp_dt.Rows.Count > 0)
                {
                    pmlist["th_groupId"] = temp_dt.Rows[0]["id"] + "";
                }



                pams = new List<SqlParameter>();
                pams.Add(new SqlParameter("@th_groupId", pmlist["th_groupId"] + ""));
                sql = string.Empty;
                sql = @" 
-- 团队参数
declare @user_type varchar(10) 
declare @relaids varchar(1000)
declare @award_item_list varchar(100)


-- 商品参数
declare @title varchar(500)
declare @imgurl varchar(200)

-- 订单参数
DECLARE @new_orderId VARCHAR(38);

";
                for (int i = 0; i < dt.Rows.Count; i++)
                {

                    //pams.Add(new SqlParameter("@sd_orderNo" + i, dt.Rows[i]["sd_orderNo"] + ""));
                    //pams.Add(new SqlParameter("@fid" + i, dt.Rows[i]["fid"] + ""));
                    //pams.Add(new SqlParameter("@did" + i, dt.Rows[i]["did"] + ""));
                    //pams.Add(new SqlParameter("@pid" + i, dt.Rows[i]["id"] + ""));
                    //pams.Add(new SqlParameter("@uid" + i, dt.Rows[i]["userid"] + ""));
                    //pams.Add(new SqlParameter("@state" + i, (dt.Rows[i]["state"] + "" == "1" ? 1 : 2)));//1=完成  2=超时
                    //pams.Add(new SqlParameter("@amount" + i, dt.Rows[i]["amount"] + ""));
                    //pams.Add(new SqlParameter("@rate_award" + i, dt.Rows[i]["rate_award"] + ""));
                    //pams.Add(new SqlParameter("@award_amount" + i, dt.Rows[i]["award_amount"] + ""));
                    //pams.Add(new SqlParameter("@serve_fee" + i, dt.Rows[i]["serve_amount"] + ""));
                    //pams.Add(new SqlParameter("@payment_id" + i, dt.Rows[i]["payment_id"] + ""));
                    //pams.Add(new SqlParameter("@payment_type" + i, dt.Rows[i]["payment_type"] + ""));
                    //pams.Add(new SqlParameter("@payment_name" + i, dt.Rows[i]["payment_name"] + ""));
                    //pams.Add(new SqlParameter("@payment_bankname" + i, dt.Rows[i]["payment_bankname"] + ""));
                    //pams.Add(new SqlParameter("@payment_bankid" + i, dt.Rows[i]["payment_bankid"] + ""));
                    //pams.Add(new SqlParameter("@task_time" + i, dt.Rows[i]["task_time"] + ""));


                    sql += (string.Format(@"

-- 获取用户usertype
set @user_type='user'
select  @user_type=(case when groupid=@th_groupId then 'th' else (case when isnull(usertype,1)=0 then 'reg' else 'user' end) end),@relaids=isnull(relaids,'') from accounts with(nolock) where id=@uid{0}


-- 获取团队列表
set @award_item_list=''
select top 3 @award_item_list=@award_item_list+items+',' from dbo.Split(@relaids,',') where cast(items as int)<@uid{0} order by id desc


-- 拉取商品标题/图片
set @title=''
set @imgurl=''
select top 1 @title=name,@imgurl=imgurl from (select top 10 name,imgurl from item_list with(nolock) order by ABS(@amount{0}-amount))t order by NEWID()


-- 生成订单号 
EXEC GenerateReceive_TransportOrder @GeneratedCode = @new_orderId OUTPUT;



insert into transport_orders(order_type,userid,user_type,title,imgurl,orderNo,orderType,orderAmt,other_orderNo,state,create_time,sd_orderNo
,rate_award,award_amount,serve_fee,payment_id,payment_type,payment_name,payment_bankname,payment_bankid,award_item_list,receive_time,finish_time,payer_way,confirm_from,fid,did,pid
) values('ot',@uid{0},@user_type,@title,@imgurl,@new_orderId,'银联',@amount{0},'无',@state{0},@task_time{0},@sd_orderNo{0}
,@rate_award{0},@award_amount{0},@serve_fee{0},@payment_id{0},@payment_type{0},@payment_name{0},@payment_bankname{0},@payment_bankid{0},@award_item_list,@task_time{0},getdate(),'默认','ot',@fid{0},@did{0},@pid{0}
) 
 
if(@state{0}=1)
begin    
    update [task_onetouch] set award_amount=award_amount+cast(@award_amount{0} as decimal(18,2))-cast(@serve_fee{0} as decimal(18,2)),serve_amount=serve_amount+cast(@serve_fee{0} as decimal(18,2)) where id=@fid{0}
    exec('[settle_onetouch_orders] '+@pid{0})
end

", i))

 .Replace("@sd_orderNo" + i, "'" + dt.Rows[i]["sd_orderNo"] + "" + "'")

 .Replace("@fid" + i, dt.Rows[i]["fid"] + "")

 .Replace("@did" + i, dt.Rows[i]["did"] + "")

 .Replace("@pid" + i, "'" + dt.Rows[i]["id"] + "" + "'") //加引号的原因是因为exec

 .Replace("@uid" + i, dt.Rows[i]["userid"] + "")

 .Replace("@state" + i, (dt.Rows[i]["state"] + "" == "1" ? 1 : 2) + "")//1=完成  2=超时

 .Replace("@amount" + i, dt.Rows[i]["amount"] + "")

 .Replace("@rate_award" + i, dt.Rows[i]["rate_award"] + "")

 .Replace("@award_amount" + i, dt.Rows[i]["award_amount"] + "")

 .Replace("@serve_fee" + i, dt.Rows[i]["serve_amount"] + "")

 .Replace("@payment_id" + i, dt.Rows[i]["payment_id"] + "")

 .Replace("@payment_type" + i, "'" + dt.Rows[i]["payment_type"] + "'")

 .Replace("@payment_name" + i, "'" + dt.Rows[i]["payment_name"] + "'")

 .Replace("@payment_bankname" + i, "'" + dt.Rows[i]["payment_bankname"] + "'")

 .Replace("@payment_bankid" + i, "'" + dt.Rows[i]["payment_bankid"] + "'")

 .Replace("@task_time" + i, "'" + dt.Rows[i]["task_time"] + "'")

 ;

                }



                pmlist["errmsg"] = "";
                try
                {
                    res = db.ExecuteNonQuery(sql, pams.ToArray());
                    pmlist["errmsg"] = "成功更新条数：" + res + "条";
                }
                catch (Exception ex)
                {
                    pmlist["errmsg"] = ex.Message.ToString();
                }
                log.WriteLog("create_onetouch_orders", dt.Rows.Count.ToString(), pmlist["errmsg"] + "|" + JsonMapper.ToJson(text_array), true);
                if (text_array.Count == 100 && fz.req("date") == "")
                {
                    //还有很多订单需要处理
                    cae.RemoteCache("create_onetouch_orders_delay");
                }


                dic.Add("result_errmsg", pmlist["errmsg"] + "");
                dic.Add("total_number", pmlist["total_number"]);
                dic.Add("list", text_array);
                fz.sendResponse("SUCCESS", 1, dic);

                break;
            case "stop_onetouch_orders":
                sql = " select top 5 id from task_onetouch with(nolock) where state=100 and task_days=days and getdate()>expire_time  ";
                dt = db.getDataTable(sql);
                sql = string.Empty;

                for (int i = 0; i < dt.Rows.Count; i++)
                {
                    sql += " exec('[stop_onetouch_orders] " + dt.Rows[i]["id"] + "') ";

                }

                if (!string.IsNullOrEmpty(sql))
                {
                    res = db.ExecuteNonQuery(sql);
                }

                fz.sendResponse("Update-" + dt.Rows.Count + "-" + res, 1);
                break;
            case "update_onetouch_game_balance":
                sql = @"

-- 将所有(进行中)未缺少额度的订单游戏金额清空为0
update task_onetouch set game_balance=0,game_update_time=getdate() output deleted.* where state=100 and amount=isnull(balance,0) and DATEDIFF(DAY,ISNULL(game_update_time,0),GETDATE())<>0


-- 获取需要拉取额度的用户
select t.*,al.openid from (select userid,SUM(amount-balance) as total_balance from [task_onetouch] with(nolock) where state=100 and amount<>isnull(balance,0) and DATEDIFF(DAY,ISNULL(game_update_time,0),GETDATE())<>0 group by userid)t left join [authorize_list] al with(nolock)  on al.from_type='onetouch' and t.userid=al.userid 	

";

                ds = db.getDataSet(sql, pams.ToArray());
                dt = ds.Tables[0];
                pmlist["perfect_number"] = dt.Rows.Count;
                for (int i = 0; i < dt.Rows.Count; i++)
                {
                    fz.set_logs("额度完整", dt.Rows[i]["userid"] + "(" + dt.Rows[i]["id"] + ")-" + dt.Rows[i]["amount"] + "", "onetouch_game");
                }

                dt = ds.Tables[1];
                if (dt.Rows.Count == 0)
                {
                    fz.sendResponse("暂无需处理用户(" + pmlist["perfect_number"] + ")");
                }


                sql = string.Empty;
                for (int i = 0; i < dt.Rows.Count; i++)
                {

                    pmlist["url"] = ga_req("public.gamebalance.do", "code=0&openid=" + dt.Rows[i]["openid"] + "&token=0");
                    pmlist["text_response"] = getContent(pmlist["url"] + "");

                    dic["game_balance"] = "获取失败（" + pmlist["text_response"] + "）";
                    try
                    {
                        jd = JsonMapper.ToObject(pmlist["text_response"] + "");
                        dic["game_balance"] = jd["data"]["balance"] + "";
                    }
                    catch (Exception)
                    {
                    }

                    if (islocal)
                    {
                        dic["game_balance"] = rd.Next(0, 2000);
                    }


                    fz.set_logs("游戏额度确认", dt.Rows[i]["userid"] + "(" + dt.Rows[i]["total_balance"] + ")-" + dt.Rows[i]["openid"] + "-" + dic["game_balance"], "onetouch_game");
                    if ((dic["game_balance"] + "").IndexOf("获取失败") != -1)
                    {
                        dic["game_balance"] = "0.00";
                    }

                    sql += " exec('[update_onetouch_game_balance] " + dt.Rows[i]["userid"] + "," + dic["game_balance"] + "') ";


                }

                res = db.ExecuteNonQuery(sql);


                fz.sendRsp(res, "成功更新" + pmlist["perfect_number"] + "/" + res, "无更新");



                break;
            case "update_onetouch_successrate":
                dt = chelper.gdt("onetouch_success_rate");
                pmlist["case"] = "";
                for (int i = 0; i < dt.Rows.Count; i++)
                {
                    pmlist["case"] += " when t.less_day<=" + dt.Rows[i]["lessday"] + " then '" + dt.Rows[i]["rate1"] + "~" + dt.Rows[i]["rate2"] + "'";
                }
                sql = " update a set a.succrate_updatetime=getdate(),a.vaild_succ_rate=(case " + pmlist["case"] + " else a.vaild_succ_rate end) from accounts a left join (select parentid,DATEDIFF(DAY,MAX(create_time),GETDATE()) as less_day from accounts with(nolock) group by parentid)t on a.id=t.parentid where datediff(day,getdate(),isnull(a.succrate_updatetime,0))<>0 and t.less_day is not null ";

                res = db.ExecuteNonQuery(sql);

                fz.sendResponse("已更新" + res);
                break;
            case "stop_experience_amount":
                sql = " select top 5 id from experience_amount with(nolock) where finish_time is null and getdate()>expire_time  ";
                dt = db.getDataTable(sql);
                sql = string.Empty;

                for (int i = 0; i < dt.Rows.Count; i++)
                {
                    //用户超时15分钟返回冻结金额
                    sql += @" exec('[stop_experience_amount] " + dt.Rows[i]["id"] + "') ";

                }

                if (!string.IsNullOrEmpty(sql))
                {
                    res = db.ExecuteNonQuery(sql);
                }

                fz.sendResponse("Update-" + dt.Rows.Count + "-" + res, 1);
                break;
            case "update_team_vaild_user":
                pams.Add(new SqlParameter("@sd_valid_money", uConfig.stcdata("sd_valid_money")));
                sql = " select relaids,id from accounts where isnull(onetouch_amount,0)>=@sd_valid_money ";
                dt = db.getDataTable(sql, pams.ToArray());

                //fz.jsonResponse(ToJson(dt));

                sql = " declare @tb table(userid int,fid int) ";
                if (fz.req("test") == "1")
                {
                    sql += " \r\n ";
                }
                for (int i = 0; i < dt.Rows.Count; i++)
                {
                    text_array = new List<string>((dt.Rows[i]["relaids"] + "").Split(','));
                    text_array.RemoveAll(str => str.Contains(dt.Rows[i]["id"] + ""));
                    text_array.RemoveAll(string.IsNullOrWhiteSpace);
                    text_array.Reverse();


                    if (fz.req("test") == "1")
                    {
                        Response.Write("[" + dt.Rows[i]["id"] + "" + "]" + dt.Rows[i]["relaids"] + "" + " => " + JsonMapper.ToJson(text_array) + "\r\n" + "\r\n");
                    }

                    for (int r = 0; r < text_array.Count; r++)
                    {
                        if (r >= 3)
                        {
                            break;
                        }
                        sql += " insert into @tb values(" + text_array[r] + "," + dt.Rows[i]["id"] + "" + ")  ";

                        if (fz.req("test") == "1")
                        {
                            sql += " \r\n ";
                        }
                    }
                }


                if (fz.req("test") == "1")
                {
                    fz.jsonResponse(sql);
                }


                sql += "  update accounts set valid_usernum=0 where valid_usernum>0 ";
                sql += "  update a set a.valid_usernum=t.num from accounts a left join ( select userid,COUNT(0) as num from @tb group by userid)t on a.id=t.userid where t.userid is not null ";
                sql += "  select userid,COUNT(0) as num from @tb group by userid";

                dt = db.getDataTable(sql);
                for (int i = 0; i < dt.Rows.Count; i++)
                {
                    temp_array.Add(dt.Rows[i]["userid"] + ":" + dt.Rows[i]["num"] + "");
                }


                dic.Add("list", temp_array);
                fz.sendResponse("SUCCESS", 1, dic);

                break;
            case "getstatus_ns":
                g = new string[] { "fuwu_online", "play_online", "monitor_online" };

                pmlist["state"] = 1;
                for (int i = 0; i < g.Length; i++)
                {
                    dic.Add(g[i], cae.GetCache<string>(g[i]));
                    res = GetSecondsSincePastTime(dic[g[i]] + "");
                    dic.Add(g[i] + "_sconds", res);
                    if (res > 60)
                    {
                        pmlist["state"] = 0;
                    }
                }
                dic.Add("online_state", pmlist["state"]);
                //dic.Add("online_state", 0);
                fz.sendResponse("SUCCESS", 1, dic);
                break;
            case "update_data_user":

                g = new string[] { "teamnum", "teamincome", "invitenum", "agentbork" };
                for (int i = 0; i < g.Length; i++)
                {
                    sList.Add(string.Format("data_{0}=data_{0}+(data_{0}*1.0/100*((grow_{0}_min*100 + CAST(RAND(CHECKSUM(NEWID())) * (grow_teamincome_max*100 - grow_{0}_min*100 ) AS INT))/100))", g[i]));
                }
                sql = " update [accounts] set grow_updatetime=getdate()," + String.Join(",", sList) + " where createtype=1 and datediff(day,isnull(grow_updatetime,0),getdate())<>0";
                //fz.jsonResponse(sql);
                res = db.ExecuteNonQuery(sql);
                fz.sendResponse("已更新 " + res + " 条账号");
                break;
            case "create_newTask":
                //创建新人（新手）任务
                int taskid = 0;
                dt = cae.GetCache<DataTable>("all_orderList");
                pmlist["all_orderList_refresh"] = cae.GetCache<string>("all_orderList_refresh");
                if (dt == null || dt.Rows.Count < 2)
                {
                    pmlist["task_refreshTime"] = uConfig.stcdata("task_refreshTime");
                    pmlist["min_ss"] = 10;
                    pmlist["max_ss"] = 99;
                    try
                    {
                        pmlist["max_ss"] = (pmlist["task_refreshTime"] + "").Split('~')[1];
                        pmlist["min_ss"] = (pmlist["task_refreshTime"] + "").Split('~')[0];
                    }
                    catch (Exception)
                    {

                    }


                    sql = " declare @tb table(taskid int,limit_usertype varchar(10),limit_name varchar(10),amount decimal(18,2),minAmount decimal(18,2),maxAmount decimal(18,2),state int) ";
                    arrayList = uConfig.stcdata("reg_sd_moneys").Split(new string[] { "\n" }, StringSplitOptions.None);
                    for (int a = 0; a < arrayList.Length; a++)
                    {
                        g = arrayList[a].Split('~');
                        if (g.Length < 2)
                        {
                            continue;
                        }

                        pmlist["user_type"] = "all";
                        pmlist["limit_name"] = "";
                        pmlist["g_number"] = "1";
                        if (g.Length > 2)
                        {
                            //pmlist["limit_name"] = g[2];
                            if (IsNumeric(g[2].Replace("条", "")))
                            {
                                pmlist["g_number"] = g[2].Replace("条", "");
                            }
                        }

                        if (g.Length > 3)
                        {
                            pmlist["limit_name"] = g[3];
                        }

                        for (int t = 0; t < Convert.ToInt16(pmlist["g_number"] + ""); t++)
                        {
                            if (!IsNumeric(g[0] + "") || !IsNumeric(g[1] + ""))
                            {
                                continue;
                            }
                            //pams.Add(new SqlParameter("@amount" + a + "_" + t, get_random(g[0], g[1])));
                            //pams.Add(new SqlParameter("@minAmount" + a + "_" + t, g[0]));
                            //pams.Add(new SqlParameter("@maxAmount" + a + "_" + t, g[1]));
                            taskid++;
                            pams.Add(new SqlParameter("@limit_name" + a + "_" + t, pmlist["limit_name"] + ""));
                            pams.Add(new SqlParameter("@taskid" + a + "_" + t, taskid));
                            sql += string.Format(" insert into @tb values(@taskid{0},'{1}',@limit_name{0}," + get_random(g[0], g[1]) + "," + g[0] + "," + g[1] + ",1) ", a + "_" + t, pmlist["user_type"] + "");

                        }

                    }



                    sql += @"

declare @lists table(orderNo varchar(32),title varchar(500),imgurl varchar(200),orderAmt decimal(18,2),limit_name varchar(10))
declare @itemIds table(id int)


declare @total_number int
set @total_number=0 
select @total_number=count(0) from @tb

while @total_number>0
begin

    declare @title varchar(500)
    declare @imgurl varchar(200)
    declare @amount decimal(18,2)
    declare @minAmount decimal(18,2)
    declare @maxAmount decimal(18,2)
    declare @limit_usertype varchar(10)
    declare @limit_name varchar(10)
    declare @itemid int
    declare @taskid int

    select top 1 @taskid=taskid,@limit_name=limit_name,@amount=amount from @tb order by newid()
    delete @tb where taskid=@taskid

    select top 1 @itemid=id,@title=name,@imgurl=imgurl from (select top 10 * from item_list with(nolock) where id not in (select id from @itemIds) order by ABS(@amount-amount),newid())t order by NEWID()

    insert into @lists values(REPLACE(LOWER(newid()),'-',''),@title,@imgurl,@amount,@limit_name) 
    insert into @itemIds values(@itemid)

    select @total_number=count(0) from @tb
end

select * from @lists

";


                    dt = db.getDataTable(sql, pams.ToArray());
                    temp_time = DateTime.Now.AddSeconds(get_random(pmlist["min_ss"] + "", pmlist["max_ss"] + ""));
                    cae.SetCache("all_orderList", dt, temp_time);
                    cae.SetCache("all_orderList_refresh", temp_time.ToString("yyyy-MM-dd HH:mm:ss"));

                    dic.Add("number", dt.Rows.Count);
                    dic.Add("refresh", temp_time.ToString("yyyy-MM-dd HH:mm:ss"));
                    dic.Add("list", ToJson(dt));
                    fz.sendResponse("已刷新缓存", 1, dic);
                }
                else
                {
                    res = 0;
                    for (int i = 0; i < dt.Rows.Count; i++)
                    {
                        if (cae.GetCache<string>("rec_" + dt.Rows[i]["orderNo"]) == "1")
                        {
                            continue;
                        }
                        res++;
                    }

                    dic.Add("number", res);
                    fz.sendResponse("缓存未更新,下次更新：" + pmlist["all_orderList_refresh"], -1, dic);
                }
                break;
            default:
                fz.sendResponse("not found", 404);
                break;
        }
        return _result;
    }


    public static int GetSecondsSincePastTime(string pastTimeString)
    {
        if (string.IsNullOrEmpty(pastTimeString))
        {
            return 999;
        }
        // 将过去时间字符串转换为 DateTime 对象
        DateTime pastTime;
        if (!DateTime.TryParseExact(pastTimeString, "yyyy-MM-dd HH:mm:ss", null, System.Globalization.DateTimeStyles.None, out pastTime))
        {
            throw new ArgumentException("Invalid past time string format. Expected format: yyyy-MM-dd HH:mm:ss");
        }

        // 获取当前时间
        DateTime currentTime = DateTime.Now;

        // 计算时间间隔
        TimeSpan timeDifference = currentTime - pastTime;

        // 返回时间间隔的秒数
        return (int)timeDifference.TotalSeconds;
    }

    public static Tuple<List<int>, List<int>> SplitAmount(
     double totalAmount, double rateVal, double minAmount, double maxAmount)
    {
        // 计算左右两部分的金额
        int leftAmount = (int)Math.Floor(totalAmount * rateVal / 100);
        int rightAmount = (int)Math.Floor(totalAmount * (100 - rateVal) / 100);

        //// 如果金额偏大，则尽量靠近 maxAmount 进行切分
        //if (maxAmount * 8 < leftAmount)
        //{
        //    leftAmount = (int)(maxAmount * 8);
        //}
        //if (maxAmount * 8 < rightAmount)
        //{
        //    rightAmount = (int)(maxAmount * 8);
        //}

        // 如果左右两部分的总金额超过总金额，则调整金额
        int totalAdjustedAmount = leftAmount + rightAmount;
        if (totalAdjustedAmount > totalAmount)
        {
            double ratio = totalAmount / (double)totalAdjustedAmount;
            leftAmount = (int)(leftAmount * ratio);
            rightAmount = (int)(rightAmount * ratio);
        }

        //ww("leftAmount=" + leftAmount);
        //ww("rightAmount=" + rightAmount);

        // 将左右两部分金额随机切分成多段金额
        List<int> leftAmounts = RandomSplitAmount(leftAmount, minAmount, maxAmount);
        List<int> rightAmounts = RandomSplitAmount(rightAmount, minAmount, maxAmount);
        if (leftAmount < minAmount)
        {
            leftAmounts.Clear();
        }
        if (rightAmount < minAmount)
        {
            rightAmounts.Clear();
        }

        Dictionary<string, object> dic = new Dictionary<string, object>();
        dic.Add("leftAmounts", leftAmounts);
        dic.Add("rightAmounts", rightAmounts);

        //ww("dic=" + LitJson.JsonMapper.ToJson(dic));


        return Tuple.Create(leftAmounts, rightAmounts);
    }

    // 随机切分金额
    private static List<int> RandomSplitAmount(int amount, double minAmount, double maxAmount)
    {
        List<int> amounts = new List<int>();
        Random random = new Random(Guid.NewGuid().GetHashCode());

        // 计算最大可生成的金额数量
        int minCount = (int)Math.Ceiling((double)amount / maxAmount);
        int maxCount = (int)Math.Ceiling(((double)amount + 1) / minAmount) - 1;
        if (maxCount < minCount)
        {
            maxCount = minCount;
        }
        int igRate = ((maxCount - minCount) / 3);
        if (igRate > 0)
        {
            minCount = minCount + igRate;
            maxCount = maxCount - igRate;
            if (maxCount < minCount)
            {
                maxCount = minCount;
            }
        }


        //ww("dd=" + ((maxCount - minCount) / 3));
        //ww("minCount=" + minCount);
        //ww("maxCount=" + maxCount);

        // 如果剩余金额小于等于 minAmount，则将剩余金额生成一条记录并返回
        if (amount <= minAmount)
        {
            amounts.Add(amount);
            return amounts;
        }

        // 随机生成金额数量
        int count = random.Next(minCount, maxCount + 1);

        // 生成金额
        while (amount > 0 && amounts.Count < count)
        {
            if ((count - amounts.Count) == 1)
            {
                amounts.Add(amount);
                break;
            }
            // 动态调整 maxAmount，确保不超过原有的 maxAmount
            //double dynamicMaxAmount = Math.Min(maxAmount, (double)amount / (count - amounts.Count));
            double dynamicMaxAmount = Math.Min(maxAmount, (double)amount - (count - amounts.Count - 1) * minAmount);
            // 在 minAmount 到 dynamicMaxAmount 之间随机切分
            int splitAmount = random.Next((int)minAmount, (int)Math.Max(minAmount, dynamicMaxAmount) + 1);
            //ww("rand=" + minAmount + "," + (int)Math.Max(minAmount, dynamicMaxAmount) + "(" + minAmount + "," + dynamicMaxAmount + ")" + " = " + splitAmount);
            // 如果切分金额大于剩余金额，则调整切分金额
            splitAmount = Math.Min(splitAmount, amount);
            amounts.Add(splitAmount);
            amount -= splitAmount;
        }

        return amounts;
    }

    //public static TimeSpan GenerateRandomTime(string startTimeStr, string endTimeStr)
    //{
    //    // 解析字符串形式的开始时间和结束时间
    //    TimeSpan startTime = TimeSpan.Parse(startTimeStr);
    //    TimeSpan endTime = TimeSpan.Parse(endTimeStr);

    //    // 计算时间范围
    //    TimeSpan timeRange = endTime - startTime;

    //    // 生成随机时间
    //    Random random = new Random(Guid.NewGuid().GetHashCode());
    //    int totalSeconds = (int)timeRange.TotalSeconds;
    //    int randomSeconds = random.Next(0, totalSeconds);
    //    TimeSpan randomTime = startTime.Add(TimeSpan.FromSeconds(randomSeconds));

    //    return randomTime;
    //}

    public static List<string> GenerateRandomTimes(string startTimeStr, string endTimeStr, string allowTimeStartStr, string allowTimeEndStr, int number)
    {
        // 解析字符串形式的开始时间和结束时间
        DateTime startTime = DateTime.Parse(startTimeStr);
        DateTime endTime = DateTime.Parse(endTimeStr);

        // 解析字符串形式的允许时间范围
        TimeSpan allowTimeStart = TimeSpan.Parse(allowTimeStartStr);
        TimeSpan allowTimeEnd = TimeSpan.Parse(allowTimeEndStr);

        // 随机生成器
        Random random = new Random(Guid.NewGuid().GetHashCode());
        List<DateTime> randomTimes = new List<DateTime>();

        // 生成随机时间
        while (randomTimes.Count < number)
        {
            // 计算总秒数范围
            int totalSeconds = (int)(endTime - startTime).TotalSeconds;
            int randomSeconds = random.Next(0, totalSeconds);

            // 生成随机时间
            DateTime randomTime = startTime.AddSeconds(randomSeconds);

            // 检查生成的时间是否在允许范围内
            TimeSpan randomTimeOfDay = randomTime.TimeOfDay;
            if (randomTimeOfDay >= allowTimeStart && randomTimeOfDay <= allowTimeEnd)
            {
                randomTimes.Add(randomTime);
            }
        }

        // 对生成的时间进行排序
        randomTimes.Sort();

        // 将 DateTime 转换为字符串并返回
        List<string> result = randomTimes.Select(time => time.ToString("yyyy-MM-dd HH:mm:ss")).ToList();
        return result;
    }


    public static List<int> SplitNumber(double number, int splitCount)
    {
        List<int> result = new List<int>();

        // 计算每个整数的平均值
        int average = (int)Math.Round(number / splitCount);

        // 随机分割数字
        Random random = new Random(Guid.NewGuid().GetHashCode());
        for (int i = 0; i < splitCount - 1; i++)
        {
            // 生成一个小于平均值的随机整数，但不能超过剩余的值
            int randomValue = random.Next(1, Math.Min(2 * average, (int)number));
            result.Add(randomValue);
            // 减去已经分割的值
            number -= randomValue;
        }
        // 将剩余的值作为最后一个整数
        result.Add((int)number);

        // 打乱顺序
        Shuffle(result, random);

        return result;
    }

    // Fisher-Yates 洗牌算法
    private static void Shuffle(List<int> list, Random random)
    {
        for (int i = list.Count - 1; i > 0; i--)
        {
            int j = random.Next(0, i + 1);
            int temp = list[i];
            list[i] = list[j];
            list[j] = temp;
        }
    }

    // 递归将 JsonData 转换为 Dictionary<string, object>
    static Dictionary<string, object> ToDictionary(JsonData jsonData)
    {
        Dictionary<string, object> dictionary = new Dictionary<string, object>();

        if (jsonData.IsObject)
        {
            // 遍历 JSON 对象的所有属性
            foreach (KeyValuePair<string, JsonData> kvp in jsonData)
            {
                string key = kvp.Key;
                JsonData value = kvp.Value;

                // 根据值的类型进行处理
                if (value.IsObject || value.IsArray)
                {
                    // 递归处理嵌套的 JSON 对象或数组
                    dictionary[key] = ToDictionary(value);
                }
                else
                {
                    // 将基本类型的值添加到字典中
                    dictionary[key] = value.ToString();
                }
            }
        }

        return dictionary;
    }

    public Dictionary<string, object> get_totalDic(DataTable dt, bool isAutoCol = false)
    {
        Dictionary<string, object> temp_dic = new Dictionary<string, object>{
                    { "total_amount", 0 },
                    { "total_number", 0 },
                    { "today_amount", 0},
                    { "today_number", 0},
                    { "yesterday_amount", 0 },
                    { "yesterday_number", 0 },
                    { "today_daily_number", 0 },
                    { "yesterday_daily_number", 0 },
                    { "total_daily_number", 0 }
                };





        if (dt.Rows.Count > 0)
        {
            if (isAutoCol)
            {

                // 遍历 DataTable 的列
                temp_dic = new Dictionary<string, object>();
                foreach (DataColumn column in dt.Columns)
                {
                    // 获取字段名
                    string columnName = column.ColumnName;

                    // 获取第一行的值
                    object columnValue = dt.Rows.Count > 0 ? dt.Rows[0][columnName] : null;

                    // 将字段名和对应值添加到 Dictionary
                    temp_dic.Add(columnName, columnValue);
                }

            }
            else
            {
                try
                {
                    temp_dic["name"] = dt.Rows[0]["name"];
                }
                catch (Exception)
                {
                }


                try
                {
                    temp_dic["total_number"] = dt.Rows[0]["total_number"];
                    temp_dic["today_number"] = dt.Rows[0]["today_number"];
                    temp_dic["yesterday_number"] = dt.Rows[0]["yesterday_number"];

                    temp_dic["total_amount"] = dt.Rows[0]["total_amount"];
                    temp_dic["today_amount"] = dt.Rows[0]["today_amount"];
                    temp_dic["yesterday_amount"] = dt.Rows[0]["yesterday_amount"];
                }
                catch (Exception)
                {
                }

                try
                {
                    temp_dic["today_daily_number"] = dt.Rows[0]["today_daily_number"];
                    temp_dic["yesterday_daily_number"] = dt.Rows[0]["yesterday_daily_number"];
                    temp_dic["total_daily_number"] = dt.Rows[0]["total_daily_number"];
                }
                catch (Exception)
                {
                }

            }

        }

        // 创建一个新的字典用于保存修改后的值
        Dictionary<string, object> modified_dic = new Dictionary<string, object>();

        foreach (var pair in temp_dic)
        {
            string key = pair.Key;
            object value = pair.Value;

            // 如果值为 DBNull.Value，则将其替换为 0
            if (value == DBNull.Value)
            {
                modified_dic[key] = 0;
            }
            else
            {
                modified_dic[key] = value;
            }
        }

        // 将修改后的值复制回原始字典
        temp_dic = modified_dic;

        return temp_dic;
    }


    public void OutputDataTableText(DataTable dataTable)
    {
        // 检查 DataTable 是否为 null
        if (dataTable == null)
        {
            Response.Write("DataTable is null.");
            return;
        }

        // 输出列名
        foreach (DataColumn column in dataTable.Columns)
        {
            Response.Write(string.Format(" {0,-20} |", column.ColumnName));
        }
        Response.Write("\r\n");
        Response.Write("\r\n");

        // 遍历 DataTable 的每一行
        foreach (DataRow row in dataTable.Rows)
        {
            // 输出行数据
            foreach (var item in row.ItemArray)
            {
                Response.Write(string.Format(" {0,-20} |", item));
            }
            Response.Write("\r\n");
        }
        Response.Write("\r\n");
    }

    public DataTable getCacheDt(string name)
    {

        // 创建一个新的 DataTable
        DataTable chatHtmlTable = new DataTable(name);

        chatHtmlTable = cae.GetCache<DataTable>(name);

        if (chatHtmlTable == null || chatHtmlTable.Rows.Count == 0)
        {

            Response.Write("无数据\r\n");

            chatHtmlTable = new DataTable(name);

            // 添加列
            chatHtmlTable.Columns.Add("type", typeof(string));
            chatHtmlTable.Columns.Add("text", typeof(string));
            chatHtmlTable.Columns.Add("create_time", typeof(DateTime));

        }
        else
        {

            Response.Write(" updateTable = " + ToJson(chatHtmlTable) + "\r\n");
            Response.Write("------------------------------------------" + "\r\n");
            Response.Write("类型 | 消息 | 时间" + "\r\n\r\n");

            for (int upindex = 0; upindex < chatHtmlTable.Rows.Count; upindex++)
            {
                Response.Write(chatHtmlTable.Rows[upindex]["type"] + " | " + chatHtmlTable.Rows[upindex]["text"] + " | " + Convert.ToDateTime(chatHtmlTable.Rows[upindex]["create_time"] + "").ToString("HH:mm:ss") + "\r\n\r\n");
            }
            Response.Write("------------------------------------------" + "\r\n");
        }
        return chatHtmlTable;
    }


    public string GetSignParams(Dictionary<string, object> jsonDict, bool is_encode = false)
    {
        // 按键升序排序
        var sortedDict = jsonDict.OrderBy(pair => pair.Key)
                                 .ToDictionary(pair => pair.Key, pair => pair.Value);

        // 构建参数字符串
        List<string> paramPairs = new List<string>();
        foreach (var pair in sortedDict)
        {
            var key = pair.Key;
            var value = pair.Value;
            if (is_encode)
            {
                value = Uri.EscapeDataString(value + "");
            }
            paramPairs.Add(key + "=" + value);
        }

        string paramString = string.Join("&", paramPairs);

        return paramString;
    }

    static void ShuffleList<T>(List<T> list)
    {
        Random random = new Random();
        int n = list.Count;

        for (int i = n - 1; i > 0; i--)
        {
            int j = random.Next(0, i + 1);
            T temp = list[i];
            list[i] = list[j];
            list[j] = temp;
        }
    }

    public static string GetCurrentTimeRange(string input)
    {
        // 获取当前时间的小时部分
        int currentHour = DateTime.Now.Hour;

        // 解析输入字符串，将其拆分成每行
        string[] lines = input.Split('\n');

        foreach (string line in lines)
        {
            string[] parts = line.Split('：');
            if (parts.Length == 2)
            {
                int rangeHour;
                if (int.TryParse(parts[0], out rangeHour))
                {
                    // 检查当前小时是否与范围小时匹配
                    if (currentHour == rangeHour)
                    {
                        return parts[0] + "：" + parts[1].Trim();
                    }
                }
            }
        }

        // 默认返回空字符串表示未找到匹配的时间范围
        return string.Empty;
    }

    public string getPamsSql(List<SqlParameter> parameters)
    {
        string sql = string.Empty;
        // 遍历参数列表，构建 SQL 语句
        foreach (var parameter in parameters)
        {
            string sqlParameterDeclaration = "DECLARE " + parameter.ParameterName + " " + GetSqlType(parameter.SqlDbType);
            string sqlParameterValueAssignment = "SET " + parameter.ParameterName + " = '" + parameter.Value + "'";

            // 输出声明参数和赋值语句
            sql += sqlParameterDeclaration + @"
";
            sql += sqlParameterValueAssignment + @"
";
        }
        return sql;
    }

    // 获取 SqlParameter 的 SqlDbType 对应的 SQL 数据类型
    static string GetSqlType(SqlDbType sqlDbType)
    {
        switch (sqlDbType)
        {
            case SqlDbType.VarChar:
                return "VARCHAR(100)"; // 假设长度为 100
            case SqlDbType.Int:
                return "INT";
            case SqlDbType.Bit:
                return "BIT";
            case SqlDbType.NVarChar:
                return "VARCHAR(100)";
            // 其他数据类型自行添加
            default:
                throw new ArgumentException("Unsupported SqlDbType: " + sqlDbType);
        }
    }

}
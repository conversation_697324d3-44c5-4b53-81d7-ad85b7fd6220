using com.cotees;
using LitJson;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.IO;
using System.Linq;
using System.Text;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

public partial class Api_admin : globalClass
{
    fuzhu fz = new fuzhu(HttpContext.Current);
    protected void Page_Load(object sender, EventArgs e)
    {
        Response.ContentType = "application/json";
        string result = main();
        Response.Write(result);
        Response.End();
    }

    public string main()
    {

        string _result = "";

        //数据库相关参数
        string sql = string.Empty;
        dbClass db = new dbClass();
        DataTable dt = new DataTable();
        DataTable temp_dt = new DataTable();
        DataTable cache_dt = new DataTable();
        DataTable select_dt = new DataTable();
        DataRow[] rows;
        DataRow[] matchingRows;
        int updateRowIndex = 0;
        DataSet ds;
        DataRow[] dr;
        Dictionary<string, object> pmlist = new Dictionary<string, object>();
        Dictionary<string, object> dic = new Dictionary<string, object>();
        Dictionary<string, object> _data = new Dictionary<string, object>();
        List<Dictionary<string, object>> list = new List<Dictionary<string, object>>();
        Dictionary<string, object> temp_dic = new Dictionary<string, object>();

        //临时参数
        Random rd = new Random();
        string temp = string.Empty;
        string temp_val = string.Empty;
        object object_val;
        bool result = false;
        int res = 0;
        string[] g;
        string[] g2;
        string[] arrayList;
        List<string> text_array = new List<string>();
        List<string> temp_array = new List<string>();
        List<string> sList = new List<string>();
        JsonData jd;
        List<SqlParameter> pams = fz.collectReqParames(out result);

        fz.setResponseLable("msg");


        if (fz.req("apikey") != uConfig.stcdata("dating_notify_key"))
        {
            fz.sendResponse("密钥错误");
        }


        switch (fz.req("act"))
        {
            case "finish_transaction":
                fz.check_exist(new string[] { "id" });

                fz.limit_check(2, "api_finish_" + fz.req("id"));
                //获取buy_list
                try
                {
                    temp = GetHttp(uConfig.stcdata("dating_apiurl") + "api/transaction_api.aspx?apikey=" + uConfig.stcdata("dating_apikey") + "&act=get_dating_total&id=" + fz.req("id") + "&uuid=0", "");
                    dt = TransToDatatable(temp);
                    temp = "success";
                }
                catch (Exception ex)
                {
                    temp = "信息获取失败：" + ex.Message.ToString();
                }

                if (temp != "success")
                {
                    fz.sendResponse(temp);
                }
                if (dt.Rows.Count == 0)
                {
                    fz.sendResponse("订单获取失败{列表0}");
                }

                pams = new List<SqlParameter>();
                pams.Add(new SqlParameter("@id", fz.req("id")));
                for (int i = 0; i < dt.Columns.Count; i++)
                {
                    temp = dt.Columns[i] + "";
                    object_val = dt.Rows[0][temp] + "";

                    if (temp == "id")
                    {
                        continue;
                    }
                    //Response.Write(temp + "=" + object_val);
                    //Response.Write("\r\n");

                    if (object_val + "" == "null")
                    {
                        object_val = DBNull.Value;


                    }


                    switch (temp)
                    {
                        case "apiid":
                            object_val = fz.req("id");
                            break;
                        case "userid":
                            object_val = Convert.ToInt64(object_val) * -1;
                            break;
                        case "buyersite":
                            object_val = DBNull.Value;
                            break;
                        default:
                            break;
                    }



                    text_array.Add(temp);
                    temp_array.Add("@param_" + temp);
                    pams.Add(new SqlParameter("@param_" + temp, object_val));
                }

                sql = @" 

    

if(not Exists(select * from buy_list where apiid=@id))
begin 
    insert into buy_list({names}) values({values}) 


    declare @current_amount decimal(18,2)
    declare @other_amount decimal(18,2)
    set @other_amount=cast(@param_amount as decimal(18,2))*cast((case when isnull(@param_p1,'0')='' then '0'else isnull(@param_p1,'0') end) as decimal(18,2))/100

	update accounts set amount=amount+cast(@param_amount as decimal(18,2)),@current_amount=amount+cast(@param_amount as decimal(18,2)) where id=@param_userid 
	insert into transaction_list values(@param_userid,'买币',cast(@param_amount as decimal(18,2)),@current_amount,@param_orderId,'大厅交易',1,'',GETDATE(),GETDATE()) 



    if(@other_amount>0)
    begin
        set @current_amount=0
	    update accounts set amount=amount+@other_amount,@current_amount=amount+@other_amount where id=@param_userid 
	    insert into transaction_list values(@param_userid,'买币赠送',@other_amount,@current_amount,@param_orderId,'大厅交易-赠送'+isnull(@param_p1,'0')+'%',1,'',GETDATE(),GETDATE()) 
    end
end 

".Replace("{names}", String.Join(",", text_array)).Replace("{values}", String.Join(",", temp_array));

                res = db.ExecuteNonQuery(sql, pams.ToArray());

                fz.sendRsp(res, "SUCCESS", "订单已存在");
                //fz.sendResponse("sql = " + sql);

                break;
            case "finish_sell_dating":
                fz.check_exist(new string[] { "id" });

                fz.limit_check(2, "api_selldating_finish_" + fz.req("id"));
                //获取buy_list
                try
                {
                    temp = GetHttp(uConfig.stcdata("dating_apiurl") + "api/transaction_api.aspx?apikey=" + uConfig.stcdata("dating_apikey") + "&act=get_sell_dating_total&id=" + fz.req("id") + "&uuid=0", "");
                    dt = TransToDatatable(temp);
                    temp = "success";
                }
                catch (Exception ex)
                {
                    temp = "信息获取失败：" + ex.Message.ToString();
                }

                if (temp != "success")
                {
                    fz.sendResponse(temp);
                }
                if (dt.Rows.Count == 0)
                {
                    fz.sendResponse("订单获取失败{列表0}");
                }
                if (dt.Rows[0]["state"] + "" != "1")
                {
                    fz.sendResponse("订单状态有误:" + dt.Rows[0]["state"]);
                }

                pams = new List<SqlParameter>();
                pams.Add(new SqlParameter("@id", fz.req("id")));
                for (int i = 0; i < dt.Columns.Count; i++)
                {
                    temp = dt.Columns[i] + "";
                    object_val = dt.Rows[0][temp] + "";

                    if (temp == "id")
                    {
                        continue;
                    }
                    //Response.Write(temp + "=" + object_val);
                    //Response.Write("\r\n");

                    if (object_val + "" == "null")
                    {
                        object_val = DBNull.Value;


                    }


                    switch (temp)
                    {
                        case "apiid":
                            object_val = fz.req("id");
                            break;
                        case "userid":
                            object_val = Convert.ToInt64(object_val) * -1;
                            break;
                        case "buyersite":
                            object_val = DBNull.Value;
                            break;
                        default:
                            break;
                    }



                    text_array.Add(temp);
                    temp_array.Add("@param_" + temp);
                    pams.Add(new SqlParameter("@param_" + temp, object_val));
                }

                sql = @" 

    

if(not Exists(select * from sell_dating_list where apiid=@id))
begin 
    insert into sell_dating_list({names}) values({values}) 

	update accounts set freeze_amount=freeze_amount-cast(@param_amount as decimal(18,2))  where id=@param_userid   

end 

".Replace("{names}", String.Join(",", text_array)).Replace("{values}", String.Join(",", temp_array));

                res = db.ExecuteNonQuery(sql, pams.ToArray());

                fz.sendRsp(res, "SUCCESS", "订单已存在");
                //fz.sendResponse("sql = " + sql);

                break;
            case "sell_verify_fail":
                //卖币取消
                fz.check_exist(new string[] { "id" });
                fz.limit_check(2, "api_selldating_fail_" + fz.req("id"));//防止重复插入

                //temp = GetHttp((pmlist["notify_url"] + "").Replace("{act}", "sell_verify_fail"), "id=" + fz.req("id") + "&uid=" + dt.Rows[0]["uid"] + "&total_amount=" + dt.Rows[0]["total_amount"] + "&orderNo=" + dt.Rows[0]["sell_orderNo"]);

                pmlist["site_match"] = "1";
                if (!fz.empty("sitefrom"))
                {
                    //新版本（含sitefrom，需检测sitefrom是否跟siteid一直，不一致为外站数据）
                    if (fz.req("sitefrom") != uConfig.stcdata("siteid"))
                    {
                        if (fz.req("sitefrom") != "3")
                        {
                            fz.sendResponse("此数据非站点(3),请手动退还！");
                        }

                        //不一致，外站数据
                        pmlist["site_match"] = "0";
                    }
                }

                try
                {
                    temp = GetHttp(uConfig.stcdata("dating_apiurl") + "api/transaction_api.aspx?apikey=" + uConfig.stcdata("dating_apikey") + "&act=get_sell_dating_total&id=" + fz.req("id") + "&uuid=0", "");
                    dt = TransToDatatable(temp);
                    temp = "success";
                }
                catch (Exception ex)
                {
                    temp = "信息获取失败：" + ex.Message.ToString();
                }

                if (pmlist["site_match"] + "" == "0")
                {
                    log.WriteLog("outsite_user", "get_sell_dating_total", "(" + dt.Rows[0]["orderNo"] + ")" + temp + " || " + ToJson(dt), true);

                }

                if (temp != "success")
                {
                    fz.sendResponse(temp);
                }
                if (dt.Rows.Count == 0)
                {
                    fz.sendResponse("订单获取失败{列表0}");
                }
                if (dt.Rows[0]["state"] + "" != "-1")
                {
                    fz.sendResponse("订单状态有误:" + dt.Rows[0]["state"]);
                }

                pmlist["userid"] = Convert.ToInt64(dt.Rows[0]["userid"] + "") * -1;
                if (pmlist["site_match"] + "" == "0")
                {
                    temp_dt = db.getDataTable(@"
select id from accounts with(nolock) where tfkid<0 and tfkid=@uid
", new SqlParameter[]{
    new SqlParameter("@uid",dt.Rows[0]["userid"] + "")
 });
                    if (temp_dt.Rows.Count == 0)
                    {
                        fz.sendResponse("用户ID未转移(" + dt.Rows[0]["userid"] + "),请手动退还！");
                    }

                    pmlist["userid"] = temp_dt.Rows[0]["id"] + "";


                    log.WriteLog("outsite_user", "userinfo", "(" + dt.Rows[0]["orderNo"] + ")" + dt.Rows[0]["userid"] + " -> " + pmlist["userid"], true);

                }

                pams.Add(new SqlParameter("@param_uid", pmlist["userid"] + ""));
                pams.Add(new SqlParameter("@param_orderNo", dt.Rows[0]["orderNo"] + ""));
                pams.Add(new SqlParameter("@param_total_amount", dt.Rows[0]["amount"] + ""));

                pams.Add(new SqlParameter("@api_orderIds_type", "sell_list"));
                sql = @"

if(not Exists(select * from api_orderIds where type=@api_orderIds_type and userid=@param_uid and orderNo=@param_orderNo))
begin
    insert into api_orderIds values(0,@api_orderIds_type,@param_uid,@param_orderNo,getdate())
    
    declare @current_amount decimal(18,6)
    update accounts set amount=amount+@param_total_amount,@current_amount=amount+cast(@param_total_amount as decimal(18,6)),freeze_amount=freeze_amount-cast(@param_total_amount as decimal(18,6)) where id=@param_uid 
    insert into transaction_list values(@param_uid,'卖币撤销',cast(@param_total_amount as decimal(18,6)),@current_amount,@param_orderNo,'审核失败',1,'',GETDATE(),GETDATE())
end

".Replace("审核失败", pmlist["site_match"] + "" == "0" ? "审核失败-2" : "审核失败");
                res = db.ExecuteNonQuery(sql, pams.ToArray());

                fz.sendRsp(res, "SUCCESS", "订单已存在");
                break;
            case "accounts_baseinfo":
                DataTable tempdt = selectDateTable(chelper.gdt("account_groups"), " name='★托号' ");
                pmlist["th_groupId"] = "-1";
                if (tempdt.Rows.Count > 0)
                {
                    pmlist["th_groupId"] = tempdt.Rows[0]["id"] + "";
                }


                sql = string.Empty;
                sql = @" 
select u.*,pa.parent_code as pa_parent_code,pa.phone as pa_phone,pa.usertype as pa_usertype,pa.groupid as pa_groupid,isnull(u.play_brok,0)+isnull(u.rec_play_brok,0) as total_play_brok from accounts u with(nolock) left join accounts pa with(nolock) on pa.id=u.parentid where u.id=@userid

-- 查询openid
select top 1 gameName,openid from [authorize_list] with(nolock) where userid=@userid  and isnull(from_type,'')='' order by refresh_time desc

select * from payment_list with(nolock) where userid=@userid

SELECT 

SUM(case when type='活动充值' then amount else 0 end) as activity_amount
,SUM(case when type='转账' then amount else 0 end) as receive_redbag_amount 
,SUM(case when type='买币赠送' then amount else 0 end) as buycoin_gift

FROM [transaction_list] with(nolock) where userid=@userid and (type='活动充值' or type='买币赠送' or  ( remark='红包收入' and recharge_network='红包雨')) 


-- 查询体验金到期时间
select top 1 DATEDIFF(DAY,GETDATE(),expire_time) as day,DATEDIFF(hour,GETDATE(),expire_time) as hour FROM [experience_amount] where userid=@userid and finish_time is null order by expire_time asc

-- 一键任务佣金
select SUM(award_amount) as total_award_amount from [task_onetouch] with(nolock) where userid=@userid and state<>-1

".Replace("@userid", "@uid");
                ds = db.getDataSet(sql, pams.ToArray());
                fz.jsonResponse(DataSetToJson(ds));
                break;
            case "user_baseinfo":
                fz.check_exist(new string[] { "uid" });
                switch (fz.req("name"))
                {
                    case "capital":
                        sql = @" select * from (SELECT 
type,(case when recharge_network='ad' then 1 else 0 end) as isadmin,cast(SUM(amount) as decimal(18,2)) as amount
  FROM [transaction_list] where userid=@id and " + (fz.req("type") == "pay" ? "amount<0" : "amount>0") + " group by type,(case when recharge_network='ad' then 1 else 0 end))t order by amount  " + (fz.req("type") == "pay" ? "asc" : "desc");
                        sql = sql.Replace("@id", "@uid");
                        dt = db.getDataTable(sql, pams.ToArray());

                        pmlist["total_amount"] = "0";
                        for (int i = 0; i < dt.Rows.Count; i++)
                        {
                            pmlist["total_amount"] = Convert.ToDouble(pmlist["total_amount"] + "") + Convert.ToDouble(dt.Rows[i]["amount"] + "");
                        }


                        dic.Add("total_amount", pmlist["total_amount"] + "");
                        dic.Add("name", fz.req("name"));
                        dic.Add("data", ToJson(dt));
                        fz.sendResponse("success", 1, dic);
                        break;
                    case "transaction_info":
                        sql = @"

-- 卖币信息
SELECT SUM(CASE WHEN DATEDIFF(DAY, finish_time, GETDATE()) = 0 THEN amount ELSE 0 END) AS today_amount,ISNULL(SUM(amount),0) as amount FROM sell_dating_list with(nolock) where state=1 and userid=@id

-- 提现信息
select sum(case when datediff(day,finish_time,getdate())=0 then orderAmt else 0 end) as daily_amount,sum(orderAmt) as total_amount from (
    SELECT userid,finish_time,orderAmt FROM [transport_orders] WITH (NOLOCK)
    UNION ALL
    SELECT userid,finish_time,orderAmt FROM [storage_transport_orders] WITH (NOLOCK)
) AS a where userid=@id


-- 三方充值
select SUM(CASE WHEN DATEDIFF(DAY, create_time, GETDATE()) = 0 THEN amount ELSE 0 END) AS today_amount,SUM(amount) as total_amount from [api_orderList] with(nolock) where state=1 and userid=@id
-- 手动充值【买币】
select SUM(CASE WHEN DATEDIFF(DAY, create_time, GETDATE()) = 0 THEN amount ELSE 0 END) AS today_amount,SUM(amount) as total_amount from serv_recharge with(nolock) where amount>0 and recharge_type='买币' and userid=@id
-- 大厅买币
select SUM(CASE WHEN DATEDIFF(DAY, buy_time, GETDATE()) = 0 THEN amount ELSE 0 END) AS today_amount,SUM(amount) as total_amount from buy_list with(nolock) where state=1  and userid=@id

-- 游戏统计（总）
select 
isnull(sum(a.amount),0) as total_amount
,count(0) as total_number 
,isnull(sum(a.okamount),0) as total_okamount
,sum(case when isdraw=1 then 1 else 0 end) as total_oknumber
,sum(case when isdraw=1 or isdraw=-1 then a.amount else 0 end) as total_really_amount
,sum(case when isdraw=1 or isdraw=-1 then 1 else 0 end) as total_really_number
from [play_records] a with(nolock)  where a.userid=@id


-- 游戏统计（今日）
select 
isnull(sum(a.amount),0) as total_amount
,count(0) as total_number 
,sum(case when isdraw=1 or isdraw=-1 then a.amount else 0 end) as total_really_amount
,sum(case when isdraw=1 or isdraw=-1 then 1 else 0 end) as total_really_number
,isnull(sum(a.okamount),0) as total_okamount
,sum(case when isdraw=1 then 1 else 0 end) as total_oknumber
from [play_records] a with(nolock)  where  a.userid=@id and CAST(a.create_time AS DATE) = CAST(GETDATE() AS DATE)

";
                        sql = sql.Replace("@id", "@uid");
                        ds = db.getDataSet(sql, pams.ToArray());

                        pmlist["today_sell_amount"] = 0;
                        pmlist["sell_amount"] = 0;
                        pmlist["tixian_daily_amount"] = 0;
                        pmlist["tixian_total_amount"] = 0;

                        pmlist["api_today_amount"] = 0;
                        pmlist["api_total_amount"] = 0;
                        pmlist["serv_today_amount"] = 0;
                        pmlist["serv_total_amount"] = 0;
                        pmlist["dating_today_amount"] = 0;
                        pmlist["dating_total_amount"] = 0;

                        dt = ds.Tables[0];
                        if (dt.Rows.Count > 0)
                        {
                            pmlist["today_sell_amount"] = dt.Rows[0]["today_amount"] + "";
                            pmlist["sell_amount"] = dt.Rows[0]["amount"] + "";
                        }
                        dt = ds.Tables[1];
                        if (dt.Rows.Count > 0)
                        {
                            pmlist["tixian_daily_amount"] = dt.Rows[0]["daily_amount"] + "";
                            pmlist["tixian_total_amount"] = dt.Rows[0]["total_amount"] + "";
                        }


                        dt = ds.Tables[2];
                        if (dt.Rows.Count > 0)
                        {
                            pmlist["api_today_amount"] = dt.Rows[0]["today_amount"] + "";
                            pmlist["api_total_amount"] = dt.Rows[0]["total_amount"] + "";
                        }

                        dt = ds.Tables[3];
                        if (dt.Rows.Count > 0)
                        {
                            pmlist["serv_today_amount"] = dt.Rows[0]["today_amount"] + "";
                            pmlist["serv_total_amount"] = dt.Rows[0]["total_amount"] + "";
                        }

                        dt = ds.Tables[4];
                        if (dt.Rows.Count > 0)
                        {
                            pmlist["dating_today_amount"] = dt.Rows[0]["today_amount"] + "";
                            pmlist["dating_total_amount"] = dt.Rows[0]["total_amount"] + "";
                        }


                        for (int i = 0; i < 2; i++)
                        {
                            dt = ds.Tables[5 + i];
                            pmlist["total" + i + "_amount"] = 0;
                            pmlist["total" + i + "_number"] = 0;
                            pmlist["total" + i + "_really_amount"] = 0;
                            pmlist["total" + i + "_really_number"] = 0;
                            pmlist["total" + i + "_okamount"] = 0;
                            pmlist["total" + i + "_oknumber"] = 0;
                            pmlist["total" + i + "_cost_amount"] = 0;
                            if (dt.Rows.Count > 0)
                            {
                                pmlist["total" + i + "_amount"] = dt.Rows[0]["total_amount"] + "";
                                pmlist["total" + i + "_number"] = dt.Rows[0]["total_number"] + "";
                                pmlist["total" + i + "_really_amount"] = dt.Rows[0]["total_really_amount"] + "";
                                pmlist["total" + i + "_really_number"] = dt.Rows[0]["total_really_number"] + "";
                                pmlist["total" + i + "_okamount"] = dt.Rows[0]["total_okamount"] + "";
                                pmlist["total" + i + "_oknumber"] = dt.Rows[0]["total_oknumber"] + "";
                            }
                        }


                        dic = new Dictionary<string, object>();
                        dic.Add("today_sell_amount", pmlist["today_sell_amount"] + "");
                        dic.Add("sell_amount", pmlist["sell_amount"] + "");
                        dic.Add("tixian_daily_amount", pmlist["tixian_daily_amount"] + "");
                        dic.Add("tixian_total_amount", pmlist["tixian_total_amount"] + "");

                        dic.Add("api_today_amount", pmlist["api_today_amount"] + "");
                        dic.Add("api_total_amount", pmlist["api_total_amount"] + "");
                        dic.Add("serv_today_amount", pmlist["serv_today_amount"] + "");
                        dic.Add("serv_total_amount", pmlist["serv_total_amount"] + "");
                        dic.Add("dating_today_amount", pmlist["dating_today_amount"] + "");
                        dic.Add("dating_total_amount", pmlist["dating_total_amount"] + "");



                        for (int i = 0; i < 2; i++)
                        {
                            dic.Add("total" + i + "_amount", pmlist["total" + i + "_amount"] + "");
                            dic.Add("total" + i + "_number", pmlist["total" + i + "_number"] + "");
                            dic.Add("total" + i + "_really_amount", pmlist["total" + i + "_really_amount"] + "");
                            dic.Add("total" + i + "_really_number", pmlist["total" + i + "_really_number"] + "");
                            dic.Add("total" + i + "_okamount", pmlist["total" + i + "_okamount"] + "");
                            dic.Add("total" + i + "_oknumber", pmlist["total" + i + "_oknumber"] + "");
                            dic.Add("total" + i + "_cost_amount", pmlist["total" + i + "_cost_amount"] + "");
                        }




                        var copy = new Dictionary<string, object>(dic);
                        foreach (var pair in copy)
                        {
                            // 检查值是否为空
                            if (pair.Value == "")
                            {
                                // 如果值为空，赋值为 0.00
                                dic[pair.Key] = 0.00;
                            }
                        }

                        temp_dic = new Dictionary<string, object>();
                        temp_dic.Add("name", fz.req("name"));
                        temp_dic.Add("list", dic);

                        fz.sendResponse("success", 1, temp_dic);

                        break;
                    default:
                        break;
                }
                fz.sendResponse("查询有误");
                break;
            default:
                fz.sendResponse("not found", 404);
                break;
        }
        return _result;
    }
}
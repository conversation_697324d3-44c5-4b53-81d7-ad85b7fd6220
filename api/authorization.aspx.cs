using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using LitJson;

public partial class api_authorization : globalClass
{
    protected void Page_Load(object sender, EventArgs e)
    {

        if (Request.QueryString["appid"] + "" != "38189923")
        {
            Response.Write("appid错误");
            Response.End();
        }

        string token = Request.QueryString["token"] + "";
        if (string.IsNullOrEmpty(token))
        {
            Response.Write("授权失败");
            Response.End();
        }

        string data = string.Empty;
        JsonData jd = null;

        try
        {
            data = cae.GetCache<string>("login_token_" + Request.QueryString["token"]);
            if (!string.IsNullOrEmpty(data))
            {
                cae.RemoteCache("login_token_" + Request.QueryString["token"] + "");
                jd = JsonMapper.ToObject(data);
            }
        }
        catch (Exception)
        {
            jd = null;
        }

        if (jd == null)
        {
            Response.Write("授权已失效");
            Response.End();
        }


        newCookie(uConfig.loginParames_token, jd["token"] + "");
        newCookie(uConfig.loginParames_uid, jd["id"] + "");
        newCookie(uConfig.loginParames_usernick, jd["phone"] + "");
        newCookie(uConfig.loginParames_pwd, jd["password"] + "");
        newCookie(uConfig.loginParames_roomNumber, jd["roomNumber"] + "");
        newCookie(uConfig.loginParames_tid, jd["topid"] + "");
        newCookie(uConfig.loginParames_loginTime, getTimeStamp().ToString());

        Response.Redirect("~/index.aspx");
    }
}
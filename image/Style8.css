/*css gobal*/
table { border-collapse: collapse; border-spacing: 0; }
body{background:url(/image/bg.gif);background-attachment:fixed;}
/*ͷ����ʽ*/
#top{ background:url(/image/dzh-bg.gif) repeat-x; width:980px; margin:0px auto;}
#top .header{ width:980px; margin:auto; background:url(/image/dzh-bg.gif) no-repeat; height:33px; }
#top a:link,#top a:visited{ background:url(/image/dzh-1.gif) no-repeat; font-size:16px; color:#FFFFFF; text-align:center; font-weight:bold; line-height:35px; display:block; float:left; width:87px; height:30px; margin-right:6px; margin-left:5px;}
#top a:hover{ color: #ffff00; background:url(/image/dzh-2.gif) no-repeat; text-decoration:underline;}

#top8{ background:url(/image/dzh-vip.jpg) repeat-x; width:980px; margin:0px auto;}
#top9{ background:url(/image/dzh-vip.jpg) repeat-x; width:980px; margin:0px auto;}
/*topnav*/
#topnav{border-bottom:0px overflow:hidden;height:36px; width:100%;
position:fixed !important;
/*ie7 ff*/
position:absolute;
z-index:0;left:0;top:0;
}
.topnav{margin:0 auto;width:980px;}
.topnav ul{height:36px; overflow:hidden;}
.topnav ul li{float:left;line-height:36px;padding:0px 0;}
.topnav ul li a{background:none repeat display:block;font-size:14px;height:36px;line-height:36px;margin:0 0px;text-align:center;width:100%;}
.topnav ul li a:hover{background:none repeat }

/*dzh7*/
#dzh7{border-bottom:0px overflow:hidden;height:45px; width:100%; 
position:fixed !important;
/*ie7 ff*/
position:absolute;
z-index:0;left:0;top:0;
}
.dzh7{margin:0 auto;width:980px;}
.dzh7 ul{height:45px; overflow:hidden;}
.dzh7 ul li{float:left;line-height:45px;padding:0px 0;}
.dzh7 ul li a{background:none repeat display:block;font-size:14px;height:45px;line-height:45px;margin:0 0px;text-align:center;width:100%;}
.dzh7 ul li a:hover{background:none repeat }


.dzh1m{ width: 16%; height: 43px; line-height: 43px; background:#b70000; color: #ffff00; border: 1px solid #ffff00; -moz-box-shadow:0px 0px 5px #878787;
	-webkit-box-shadow:0px 0px 5px #878787; 
        box-shadow:0px 0px 0px #878787; padding:2px 17px; border-radius:15px; font-size: 28px; font-weight: bold; font-family: '����'; text-shadow: 1px 1px 1px rgba(0,0,0,1.0);/* ��Ӱ */ }
.dzh1mA{  height: 43px; line-height: 43px; background:#ff0000; color: #ffff00; border: 1px solid #ffff00; -moz-box-shadow:0px 0px 10px #878787;
	-webkit-box-shadow:0px 0px 10px #878787;
	box-shadow:0px 0px 10px #878787; padding:4px 5px; border-radius:1px; font-size: 18.8px; font-weight: bold; font-family: '΢���ź�'; }
.dzh2mA{  height: 43px; line-height: 43px; background:#0000FF; color: #ffff00; border: 1px solid #ffff00; -moz-box-shadow:0px 0px 10px #878787;
	-webkit-box-shadow:0px 0px 10px #878787;
	box-shadow:0px 0px 10px #878787; padding:4px 5px; border-radius:1px; font-size: 18.8px; font-weight: bold; font-family: '΢���ź�'; }
.dzh3mA{  height: 43px; line-height: 43px; background:#FF00FF; color: #ffff00; border: 1px solid #ffff00; -moz-box-shadow:0px 0px 10px #878787;
	-webkit-box-shadow:0px 0px 10px #878787;
	box-shadow:0px 0px 10px #878787; padding:4px 5px; border-radius:1px; font-size: 18.8px; font-weight: bold; font-family: '΢���ź�'; }
.dzh4mA{  height: 43px; line-height: 43px; background:#660066; color: #ffff00; border: 1px solid #ffff00; -moz-box-shadow:0px 0px 10px #878787;
	-webkit-box-shadow:0px 0px 10px #878787;
	box-shadow:0px 0px 10px #878787; padding:4px 5px; border-radius:1px; font-size: 18.8px; font-weight: bold; font-family: '΢���ź�'; }
.dzh5mA{  height: 43px; line-height: 43px; background:#008800; color: #ffff00; border: 1px solid #ffff00; -moz-box-shadow:0px 0px 10px #878787;
	-webkit-box-shadow:0px 0px 10px #878787;
	box-shadow:0px 0px 10px #878787; padding:4px 5px; border-radius:1px; font-size: 18.8px; font-weight: bold; font-family: '΢���ź�'; }

.dzhvip { width: 680px; height: 25px; line-height: 25px; margin: 3px; text-align: center; font-size: 16px; color: #fff; border-radius: 11px; display: inline-block; background: #b70000; font-weight: bold; }
.dzh-vip { width: 680px; height: 25px; line-height: 25px; margin: 3px; text-align: center; font-size: 16px; color: #fff; border-radius: 11px; display: inline-block; background: #b70000; font-weight: bold; font-family: '����';}

.read1 { width: 110px; height: 22px; line-height: 22px; text-align: center; font-size: 16px; padding:3px 8px; color: #fff; border-radius: 11px; background: #b70000; font-weight: bold; font-family: '΢���ź�';}
.read2 { width: 110px; height: 22px; line-height: 22px; text-align: center; font-size: 16px; padding:3px 8px; color: #fff; border-radius: 11px; background: #0000ff; font-weight: bold; font-family: '΢���ź�';}
.read3 { width: 110px; height: 22px; line-height: 22px; text-align: center; font-size: 16px; padding:3px 8px; color: #ffff00; border-radius: 11px; background: #ff0000; font-weight: bold; font-family: '΢���ź�';}
.read4 { width: 110px; height: 22px; line-height: 22px; text-align: center; font-size: 16px; padding:3px 8px; color: #fff; border-radius: 11px; background: #007700; font-weight: bold; font-family: '΢���ź�';}

.index { width: 110px; height: 20px; line-height: 20px; text-align: center; font-size: 12px; padding:2px 6px 2px 6px; color: #ffffff; border-radius: 8px; background: #008800; font-weight: bold; font-family: '����';}
.vip1 { width: 110px; height: 20px; line-height: 20px; text-align: center; font-size: 13px; padding:0px 2px 1px 2px; color: #ff0000; border-radius: 3px; background: #ffff00; font-weight: bold; text-shadow: 1px 1px 1px rgba(0,0,0,1.0);/* ��Ӱ */}
.vip5 { width: 110px; height: 20px; line-height: 20px; text-align: center; font-size: 13px; padding:0px 2px 1px 2px; color: #ffff00; border-radius: 3px; background: #0000FF; font-weight: bold; text-shadow: 1px 1px 1px rgba(0,0,0,1.0);/* ��Ӱ */}
.vip6 { width: 110px; height: 20px; line-height: 20px; text-align: center; font-size: 13px; padding:0px 2px 1px 2px; color: #ffff00; border-radius: 3px; background: #000000; font-weight: bold; text-shadow: 1px 1px 1px rgba(0,0,0,1.0);/* ��Ӱ */}
.vip7 { width: 110px; height: 20px; line-height: 20px; text-align: center; font-size: 13px; padding:0px 2px 1px 2px; color: #ffff00; border-radius: 3px; background: #B70000; font-weight: bold; text-shadow: 1px 1px 1px rgba(0,0,0,1.0);/* ��Ӱ */}
.vip8 { width: 110px; height: 20px; line-height: 20px; text-align: center; font-size: 13px; padding:0px 2px 1px 2px; color: #ffff00; border-radius: 3px; background: #FF0000; font-weight: bold; text-shadow: 1px 1px 1px rgba(0,0,0,1.0);/* ��Ӱ */}
.vip9 { width: 110px; height: 20px; line-height: 20px; text-align: center; font-size: 13px; padding:0px 2px 1px 2px; color: #ffff00; border-radius: 3px; background: #660066; font-weight: bold; text-shadow: 1px 1px 1px rgba(0,0,0,1.0);/* ��Ӱ */}

.ss-dzh{ height: 26px; line-height: 26px; font-size: 14.2px; font-family: '΢���ź�'; }
.ss-dzh-a{ height: 23px; line-height: 23px; font-size: 14.3px; font-family: '΢���ź�'; }
.ss-dzh-b{ height: 23px; line-height: 23px; font-size: 13.4px; font-family: '΢���ź�'; }
.ss-dzh-txs{ height: 26px; line-height: 26px; font-size: 14.8px; font-family: '΢���ź�'; }
.ss-dzh-txs1{ height: 26px; line-height: 26px; font-size: 14.2px; font-family: '΢���ź�'; }
.ss-vip{height: 20px; line-height: 20px; background:#ff3300; color: #ffff00; border: 1px solid #ffff00; -moz-box-shadow:0px 0px 10px #878787;
	-webkit-box-shadow:0px 0px 10px #878787;
	box-shadow:0px 0px 10px #878787; padding:1px 18px; border-radius:11px; font-size: 15px; font-weight: bold; }
.ss-mf{ height: 20px; line-height: 20px; background:#0033ff; color: #ffff00; border: 1px solid #ffff00; -moz-box-shadow:0px 0px 10px #878787;
	-webkit-box-shadow:0px 0px 10px #878787;
	box-shadow:0px 0px 10px #878787; padding:1px 18px; border-radius:11px; font-size: 15px; font-weight: bold; }
.ss-gg{ height: 20px; line-height: 20px; background:#ff00cc; color: #ffff00; border: 1px solid #ffff00; -moz-box-shadow:0px 0px 10px #878787;
	-webkit-box-shadow:0px 0px 10px #878787;
	box-shadow:0px 0px 10px #878787; padding:0px 17px; border-radius:11px; font-size: 16px; font-weight: bold; font-family: '΢���ź�';}
.ss-dj{height: 20px; line-height: 20px; background:#000000; color: #ffff00; border: 1px solid #ffff00; -moz-box-shadow:0px 0px 10px #878787;
	-webkit-box-shadow:0px 0px 10px #878787;
	box-shadow:0px 0px 10px #878787; padding:1px 8px; border-radius:11px; font-size: 15px; font-weight: bold; }
.ss-tc{height: 20px; line-height: 20px; background:#660066; color: #ffffff; border: 1px solid #ffff00; -moz-box-shadow:0px 0px 10px #ffff00;
	-webkit-box-shadow:0px 0px 10px #ffff00;
	box-shadow:0px 0px 10px #ffff00; padding:3px 12px; border-radius:11px; font-size: 17px; font-weight: bold;  font-family: '΢���ź�';}
.ss-tc1{height: 20px; line-height: 20px; background:#008800; color: #ffffff; border: 1px solid #ffff00; -moz-box-shadow:0px 0px 10px #ffff00;
	-webkit-box-shadow:0px 0px 10px #ffff00;
	box-shadow:0px 0px 10px #ffff00; padding:3px 12px; border-radius:11px; font-size: 17px; font-weight: bold;  font-family: '΢���ź�';}
.ss-tc8{height: 20px; line-height: 20px; background:#FF0000; color: #ffffff; border: 1px solid #ffff00; -moz-box-shadow:0px 0px 10px #ffff00;
	-webkit-box-shadow:0px 0px 10px #ffff00;
	box-shadow:0px 0px 10px #ffff00; padding:3px 12px; border-radius:11px; font-size: 17px; font-weight: bold;  font-family: '΢���ź�';}
.ss-tc7{height: 20px; line-height: 20px; background:#0000FF; color: #ffffff; border: 1px solid #ffff00; -moz-box-shadow:0px 0px 10px #ffff00;
	-webkit-box-shadow:0px 0px 10px #ffff00;
	box-shadow:0px 0px 10px #ffff00; padding:3px 12px; border-radius:11px; font-size: 17px; font-weight: bold;  font-family: '΢���ź�';}
.ss-tc6{height: 20px; line-height: 20px; background:#FF00FF; color: #ffffff; border: 1px solid #ffff00; -moz-box-shadow:0px 0px 10px #ffff00;
	-webkit-box-shadow:0px 0px 10px #ffff00;
	box-shadow:0px 0px 10px #ffff00; padding:3px 12px; border-radius:11px; font-size: 17px; font-weight: bold;  font-family: '΢���ź�';}
.ss-tc0{height: 20px; line-height: 20px; background:#0066FF; color: #ffffff; border: 1px solid #ffff00; -moz-box-shadow:0px 0px 10px #ffff00;
	-webkit-box-shadow:0px 0px 10px #ffff00;
	box-shadow:0px 0px 10px #ffff00; padding:3px 12px; border-radius:11px; font-size: 17px; font-weight: bold;  font-family: '΢���ź�';}
.ss-sg{height: 20px; line-height: 20px; background:#008800; color: #ffffff; border: 1px solid #ffff00; -moz-box-shadow:0px 0px 10px #878787;
	-webkit-box-shadow:0px 0px 10px #878787;
	box-shadow:0px 0px 10px #878787; padding:3px 12px; border-radius:11px; font-size: 17px; font-weight: bold;  font-family: '΢���ź�';}
.dzh95{ color:#ff0000; font-size: 19px; text-shadow: 1px 1px 1px rgba(0,0,0,1.0);/* ��Ӱ */ font-family: '΢���ź�'; }
.dzh96{text-align: center; font-size: 19px; zoom:1;padding:1px 8px 1px; color: #ff0000; background:#ffffff; border: 1px solid #FF8800; border-radius:15px; font-weight: bold; font-family: '΢���ź�';}
.tt6{text-align: center; font-size: 25px; zoom:1;padding:8px 28px 8px; color: #0033ff; background:#ffff00; border: 1px solid #FFFF00; border-radius:15px; font-weight: bold; font-family: '΢���ź�';}
.dzh58 { width: 960px; height: 28px; font-weight: bold;}
.dzh58 a:hover{background:#ffef59; color: #ffff00; text-decoration:underline;}
.dzh59 { width: 960px; height: 35px; background:#f6f3ec; font-size: 18px; padding:5px; border-style: dotted; border-width: 1px; font-weight: bold; font-family: '΢���ź�';}
.dzh-h1 { width: 45px; height: 20px; line-height: 20px; margin-bottom:2px; margin: 3px; text-align: center; font-size: 15px; color: #ffff00; border-radius: 11px; display: inline-block; background: #ff0000; font-weight: bold; font-family: '����';}
.dzh-h2 { width: 45px; height: 20px; line-height: 20px; margin-bottom:2px; margin: 3px; text-align: center; font-size: 15px; color: #ffff00; border-radius: 11px; display: inline-block; background: #0000ff; font-weight: bold; font-family: '����';}
.dzh-h3 { width: 45px; height: 20px; line-height: 20px; margin-bottom:2px; margin: 3px; text-align: center; font-size: 15px; color: #ffff00; border-radius: 11px; display: inline-block; background: #ff00ff; font-weight: bold; font-family: '����';}
.dzh-h33 { padding:0px 12px 0px 12px; text-align: center; font-size: 28px; color: #ffff00; border-radius: 15px; display: inline-block; background: #ff00ff; font-weight: bold; font-family: '����';}
.dzh1 { width: 45px; height: 18px; line-height: 18px; margin: 3px; text-align: center; font-size: 12px; color: #fff; border-radius: 11px; display: inline-block; background: #008800; font-weight: bold;}
.dzh3 { width: 110px; height: 26px; line-height: 24px; margin: 3px; font-family: '����'; border: 1px solid #ff0; text-align: center; font-size: 18px; color: #fff; border-radius: 13px; display: inline-block; background: #b70000; font-weight: bold;}
.dzh8 { width: 400px; height: 20px; line-height: 20px; margin: 3px; border: 1px solid #ff0; text-align: center; font-size: 15px; color: #fff; border-radius: 11px; display: inline-block; background: #FF0000; font-weight: bold;}
.dzhvip8 { height: 23px; line-height: 21px; padding:1px 8px; margin: 3px; border: 1px solid #ff0; text-align: center; font-size: 19px; color: #ff0; border-radius: 11px; display: inline-block; background: #b70000; font-weight: bold;}
.dzhvip9 { height: 23px; line-height: 21px; padding:1px 8px; margin: 3px; border: 1px solid #ff0; text-align: center; font-size: 19px; color: #ff0; border-radius: 11px; display: inline-block; background: #0000ff; font-weight: bold;}
.footer { width:980px;height:36px;background: #F5FFFF;position: fixed;bottom: 0px;}
#banner{ background:url(/image/banner-bg.jpg) repeat-x; width:980px; margin:0px auto;}
#banner .ad{ width:980px; margin:auto; height:36px;}
.n-banner{width:980px; margin:auto; height:36px; margin-top:8px; margin-bottom:8px;}
.dq{ width:980px; margin:auto; margin-top:0px;background:url(/image/dq.gif) no-repeat; height:35px; line-height:35px; text-indent:35px;}
.dq span{ float:right; padding-right:20px;} 

.dzh33 { height: 35px; line-height: 35px;  padding:0px 12px; font-family: '����'; border: 1px solid #ff0; text-align: center; font-size: 21px; color: #fff; border-radius: 15px; display: inline-block; background: #3300FF; font-weight: bold; }

.dzhzi { width: 980px; height: 197px; text-align: center; padding: 0px 0px; 0px; 0px; background: url(/image/dzh-vip.jpg) 0 0 no-repeat;}
.dzhzi_1{width:15%; float:left; padding-left:1px;padding-top:138px;}
.dzhzi_2{width:18%; float:right; padding-right:1px;padding-top:150px;}
.dzhzi-xg{height: 50px; line-height: 50px; background:#FFFFFF; color: #FF3300; border: 1px solid #ff6600; -moz-box-shadow:0px 0px 10px #ff6600;
	-webkit-box-shadow:0px 0px 10px #ff6600;
	box-shadow:0px 0px 10px #ff6600; padding:15px 8px; border-radius:100px; font-size: 23px; font-weight: bold;  font-family: '΢���ź�';}
.dzhzi-am{height: 30px; line-height: 30px; background:#0000FF; color: #ffffff; border: 1px solid #ffff00; -moz-box-shadow:0px 0px 10px #ffff00;
	-webkit-box-shadow:0px 0px 10px #ffff00;
	box-shadow:0px 0px 10px #ffff00; padding:3px 8px; border-radius:0px; font-size: 22px; font-weight: bold;  font-family: '΢���ź�';}

#right{width:980px; margin-top:8px;}
#n-right{width:978px; background:#FFFFFF; border-top:1px solid #ebd4c9; border-bottom:none; margin:0px auto; border:1px solid #EBD4C9;}
#right .right-box{ background:url(right-bg.jpg) no-repeat; height:165px; margin-bottom:8px; position:relative;}
#right .right-box .goumai{ position:absolute; left:310px; top:135px;}
#right .right-title{ position:absolute; left:17px;}
#right .right-icon{ position:absolute; left:24px; top:53px;}
#right h1{ color:#4c4c4c; font-size:14px; font-weight:bold; }
#right h1 span{ padding-right:10px; font-size:12px; font-weight:normal; color:#FF0000; background:url(jb-icon.gif) no-repeat; padding-left:27px; line-
height:16px;}
#right h2 span{ padding-right:10px; font-size:12px; font-weight:normal; color:#FF0000; background:url(/payimage/wen.gif) no-repeat; padding-left:27px; line-
height:16px;}
#right .icon{ padding-right:10px; width:30px; font-size:12px; font-weight:normal; color:#FF0000; background:url(jb-icon.gif) no-repeat; padding-left:27px; 
line-height:16px;}
#right h2{ color:#4c4c4c; font-size:14px; font-weight:bold; text-indent:10px; }
#right h2 span{ font-size:12px; font-weight:bold; padding-right:10px;}
#right .right-text{ width:300px; float:left; margin-left:120px;_margin-left:60px; padding-top:45px; }
#right .text-1{text-indent:4px; line-height:22px; color:#666666; text-indent:4px; display:block; padding:5px 0;}
#right .right-list{ float:left; width:285px; margin:30px 0 0 23px;}
#right .right-list span{ float:right; color:#FF0000;}
#right .right-list ul{ padding-top:8px; margin-bottom:5px;}
#right .right-list li{ height:25px; line-height:25px; border-bottom:1px solid #EAEAEA;}
.text-title{ padding:10px 0 7px 25px;color:#FF3300; font-size:18px; font-weight:bold;}
.text-title1{ padding:7px 0 7px 2px;color:#b70000; font-size:18px; font-weight:bold;}
.text-title3{ padding:2px 0 2px 2px;color:#ff0000; font-size:18px; font-weight:bold;}

.text-bg{ background:#EEEEEE; overflow:hidden; line-height:27px; text-indent:30px; font-size:14px; margin:5px 30px 5px 30px; padding:5px 10px; width:951;}
.text-bg1{ background:url(text-bg.gif) no-repeat; overflow:hidden; height:170px;_height:150px;  margin-left:10px;padding:10px 10px 0 10px; width:740px; }
.tz-title{ color:#c3292a; font-size:14px; font-weight:bold; padding-bottom:10px; text-indent:5px;}
.text-bg b{ font-weight:normal; color:#FF0000;}
.text-title span{ float:right; padding-right:30px;}
.text-title span a:link,.text-title span a:visited,.text-title span a:hover{color:#FF3300;}
.n-goumai{ border-bottom:1px solid #E7E7E7; margin:0 30px; padding:10px 5px; height:30px}
.n-goumai span{ float:right; line-height:25px; color:#FF3300;}
.n-goumai .goumh{line-height:25px; color:#FF3300;}
.ls-text{margin:10px 30px 10px 30px; padding-top:20px; overflow:hidden;}
.zh-text{ line-height:35px; padding-left:10px; font-size:13px;}
.zh-text span{ color:#FF0000;}
.ls-555{ line-height:35px;  color:#666666;}
.ls-555 th{ color:#FFFFFF; font-weight:bold; font-size:16px}
.ls-555 td{background:#FFFFFF; border-bottom:1px solid #EBD4C9; font-size:16px; color:#333333; padding-left:5px;}
.ls-555 .dfd{ color:#FF0000; font-weight:bold;}
.ls-555 .g-c{ background:url(g-cart.gif) no-repeat; padding-left:15px;}
.jb{color:#FF0000; background:url(jb-icon.gif) no-repeat; padding:3px 0 3px 25px;}
.ls-555 .t-left{ text-align:left; text-indent:20px;}
.ls-more{ text-align:right; padding:13px 160px 0 0; position:relative;}

/*pay*/
.block {text-align:left; padding:10px 28px 15px;}
.block h2 {font-size:15px; font-weight:bold; margin:0 0 9px;}
.block h1 {font-size:18px; font-family:"����"; text-align:center; margin:12px 0;}
.block h2 span {float:right; font-size:12px; font-weight:normal; margin:3px 12px 0 0;}
.block h2 small {font-size:12px; color:#888; font-weight:normal;}
.stepCont {padding:12px; background-color:#F6F6F6; border-bottom:1px dotted #999; border-top:1px dotted #999; font-size:15px; color:#FF0000; line-height:40px;}
.cardPrice,.selected,.hover {background:url(card_bg.gif) no-repeat left top;}

.cardPrice {width:200px; height:95px; clear:both; overflow:hidden; cursor:pointer; background-position:0 1px;}
.cardPrice img {float:left; margin:15px 6px 0 35px; display:inline;}
.cardPrice ul {text-align:left; margin:15px 0 0 45px;}
.cardPrice ul li {font-size:25px; line-height:30px; color:#FF0000; font-family:Arial; font-weight:bold;}
.cardPrice ul li b {font-size:12px; font-weight:normal;}

/*sale*/
.picBox .zoom{zoom:1;}
.picBox .float_r { float: right; width: 950px; height: 258px; line-height: 1.6em; padding: 8px 0px 0px 3px; font-weight: bold; font-size: 18px; font-family: "Microsoft YaHei"; background:#ffffff;}
.wq { background:#ffffff;  width: 950px; height: 32px; padding-top: 0px; font-size: 18px; color: #000000;}
.wq1 { background:#ffffff;  width: 950px; height: 32px; padding-top: 0px; font-size: 18px; color: #0000ff;}
.wq2 { background:#ffffff; width: 950px; height: 32px; padding-top: 0px; font-size: 18px; color: #ff0000;}
.wq3 { background:#ffffff; width: 950px; height: 32px; padding-top: 0px; font-size: 18px; color: #000000;}
.wq4 { background:#ffffff; width: 950px; height: 32px; padding-top: 0px; font-size: 18px; color: #ff00ff;}
.wq5 { background:#ffffff; width: 950px; height: 32px; padding-top: 0px; font-size: 18px; color: #B70000;}
.wq6 { background:#ffffff; width: 950px; height: 32px; padding-top: 0px; font-size: 18px; color: #008800;}
.wq7 { background:#ffffff; width: 950px; height: 32px; padding-top: 0px; font-size: 18px; color: #660066;}
/*apply*/
.stepCont {
	BORDER-BOTTOM: #999 1px dotted; PADDING-BOTTOM: 12px; LINE-HEIGHT: 40px; BACKGROUND-COLOR: #f6f6f6; PADDING-LEFT: 12px; PADDING-RIGHT: 12px; COLOR: #ff0000; FONT-SIZE: 15px; BORDER-TOP: #999 1px dotted; PADDING-TOP: 12px}
.stepCont01 {BORDER-BOTTOM: #999 1px dotted; PADDING-BOTTOM: 12px; LINE-HEIGHT: 25px; BACKGROUND-COLOR: #ffffcc; PADDING-LEFT: 12px; PADDING-RIGHT: 12px; COLOR: #333333; FONT-SIZE: 15px; font-weight:bold; BORDER-TOP: #999 1px dotted; PADDING-TOP: 12px }






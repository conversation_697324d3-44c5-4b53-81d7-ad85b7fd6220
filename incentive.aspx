<%@ Page Title="" Language="C#" MasterPageFile="~/top.master" AutoEventWireup="true" CodeFile="incentive.aspx.cs" Inherits="order_list" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="Server">
    <script>
        $(function () {
            <%if (Request.QueryString["top"] + "" != "1")
              {
                  %>
            popTitle('推广管理', '', 'account.aspx');
            <%
              }
              else
              {
                  %>
        
            <%
              } %>
        })
    </script>
    <style>
        .main-container {
            padding-top: 0px;
        }
    </style>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="Server">




<%--    <div style="display: flex;">

        <div style="width: 50%; padding: 10px;">
            <div style="background: #fff; padding: 18px; border-radius: 18px; display: flex; align-items: center; font-weight: bold; cursor: pointer;" onclick="javascript:location.href='../partners_manage.aspx?top=1'">
                <div>
                    团队任务佣金
   
                    <div style="font-size: 12px; color: gray; font-weight: 100; margin-top: 4px;">
                        下级任务返佣
                    </div>
                </div>
                <svg t="*************" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="6382" width="32" height="32" style="margin-left: auto;">
                    <path d="M239.256979 523.967365a45.430827 45.430827 0 1 0 0 90.906814 45.453407 45.453407 0 1 0 0-90.906814z m0-227.289614a45.475987 45.475987 0 1 0 0.02258 90.929393 45.475987 45.475987 0 0 0-0.02258-90.929393z m0 454.579228a45.475987 45.475987 0 1 0 0.02258 90.929394 45.475987 45.475987 0 0 0-0.02258-90.929394zM921.148401 114.841544H102.874179A90.906814 90.906814 0 0 0 11.967365 205.748357v727.322249a90.906814 90.906814 0 0 0 90.906814 90.906814h818.274222a90.906814 90.906814 0 0 0 90.884234-90.906814V205.748357a90.906814 90.906814 0 0 0-90.884234-90.906813z m45.475987 772.775656a90.951974 90.951974 0 0 1-90.929394 90.929393H148.327585a90.929394 90.929394 0 0 1-90.906813-90.929393V251.201764a90.906814 90.906814 0 0 1 90.906813-90.906814h727.34483a90.929394 90.929394 0 0 1 90.929393 90.906814V887.6172z m-181.881367-590.939449H466.546593a45.430827 45.430827 0 1 0 0 90.929393h318.196428a45.475987 45.475987 0 1 0 0-90.929393z m0 227.289614H466.546593a45.430827 45.430827 0 1 0 0 90.906814h318.196428a45.475987 45.475987 0 1 0 0-90.906814z m0 227.289614H466.546593a45.430827 45.430827 0 1 0 0 90.929394h318.196428a45.475987 45.475987 0 1 0 0-90.929394z" fill="#203646" p-id="6383"></path></svg>

            </div>
        </div>


        <div style="width: 50%; padding: 10px;">
            <div style="background: #fff; padding: 18px; border-radius: 18px; display: flex; align-items: center; font-weight: bold; cursor: pointer; background: linear-gradient(170deg, #9eefaa, #69db79);">
                <div>
                    团队游戏佣金<div style="font-size: 12px; color: gray; font-weight: 100; margin-top: 4px;">
                        团队游戏返佣
                    </div>
                </div>
                <svg t="" class="icon" viewBox="0 0 1026 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="7921" width="32" height="32" style="margin-left: auto;">
                    <path d="M826.31211 326.4c-179.2-38.4-179.2 83.2-313.6 83.2s-134.4-128-313.6-83.2C19.91211 371.2-69.68789 960 64.71211 960s224-166.4 448-166.4 313.6 166.4 448 166.4 44.8-588.8-134.4-633.6z m-441.6 332.8h-83.2v83.2h-89.6v-83.2h-83.2V576h83.2v-83.2h83.2V576h89.6v83.2z m384 64c-57.6 0-108.8-44.8-108.8-108.8s51.2-102.4 108.8-102.4 108.8 44.8 108.8 108.8-51.2 102.4-108.8 102.4z" fill="" p-id="7922"></path><path d="M512.71211 345.6c19.2 0 38.4-6.4 57.6-12.8-6.4-12.8-12.8-32-12.8-57.6v-44.8c0-44.8 38.4-83.2 83.2-83.2h172.8V64h-192c-83.2 0-153.6 70.4-153.6 153.6v57.6c0 25.6-6.4 44.8-12.8 57.6 19.2 12.8 38.4 12.8 57.6 12.8z" fill="" p-id="7923"></path></svg>

            </div>
        </div>
    </div>--%>

    <div style="display: flex;">

        <div style="width: 50%; padding: 10px;">
            <div style="background: #fff; padding: 18px; border-radius: 18px; display: flex; align-items: center; font-weight: bold; cursor: pointer;" onclick="javascript:location.href='../partners_manage.aspx?top=1'">
                <div>
                    下级团队
  
                    <div style="font-size: 14px; color: #7b4545; font-weight: 100; margin-top: 4px; background: #4c8f7342; font-weight: bold; padding: 2px 8px; border-radius: 3px;">搬砖</div>
                    <div style="font-size: 12px; color: gray; font-weight: 100; margin-top: 4px;">
                        返佣结算
                    </div>
                </div>
                <svg t="*************" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="6382" width="32" height="32" style="margin-left: auto;">
                    <path d="M239.256979 523.967365a45.430827 45.430827 0 1 0 0 90.906814 45.453407 45.453407 0 1 0 0-90.906814z m0-227.289614a45.475987 45.475987 0 1 0 0.02258 90.929393 45.475987 45.475987 0 0 0-0.02258-90.929393z m0 454.579228a45.475987 45.475987 0 1 0 0.02258 90.929394 45.475987 45.475987 0 0 0-0.02258-90.929394zM921.148401 114.841544H102.874179A90.906814 90.906814 0 0 0 11.967365 205.748357v727.322249a90.906814 90.906814 0 0 0 90.906814 90.906814h818.274222a90.906814 90.906814 0 0 0 90.884234-90.906814V205.748357a90.906814 90.906814 0 0 0-90.884234-90.906813z m45.475987 772.775656a90.951974 90.951974 0 0 1-90.929394 90.929393H148.327585a90.929394 90.929394 0 0 1-90.906813-90.929393V251.201764a90.906814 90.906814 0 0 1 90.906813-90.906814h727.34483a90.929394 90.929394 0 0 1 90.929393 90.906814V887.6172z m-181.881367-590.939449H466.546593a45.430827 45.430827 0 1 0 0 90.929393h318.196428a45.475987 45.475987 0 1 0 0-90.929393z m0 227.289614H466.546593a45.430827 45.430827 0 1 0 0 90.906814h318.196428a45.475987 45.475987 0 1 0 0-90.906814z m0 227.289614H466.546593a45.430827 45.430827 0 1 0 0 90.929394h318.196428a45.475987 45.475987 0 1 0 0-90.929394z" fill="#203646" p-id="6383"></path></svg>

            </div>
        </div>


        <div style="width: 50%; padding: 10px;">
            <div style="background: #fff; padding: 18px; border-radius: 18px; display: flex; align-items: center; font-weight: bold; cursor: pointer; background: linear-gradient(170deg, #9eefaa, #69db79);">


                <div>
                    下级团队                    
                    <div style="font-size: 14px;color: #c9ffdf;margin-top: 4px;background: #00000040;font-weight: bold;padding: 2px 8px;border-radius: 3px;">游戏娱乐</div>
                    <div style="font-size: 12px; color: gray; font-weight: 100; margin-top: 4px;">返佣结算</div>
                </div>

                <svg t="" class="icon" viewBox="0 0 1026 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="7921" width="32" height="32" style="margin-left: auto;">
                    <path d="M826.31211 326.4c-179.2-38.4-179.2 83.2-313.6 83.2s-134.4-128-313.6-83.2C19.91211 371.2-69.68789 960 64.71211 960s224-166.4 448-166.4 313.6 166.4 448 166.4 44.8-588.8-134.4-633.6z m-441.6 332.8h-83.2v83.2h-89.6v-83.2h-83.2V576h83.2v-83.2h83.2V576h89.6v83.2z m384 64c-57.6 0-108.8-44.8-108.8-108.8s51.2-102.4 108.8-102.4 108.8 44.8 108.8 108.8-51.2 102.4-108.8 102.4z" fill="" p-id="7922"></path><path d="M512.71211 345.6c19.2 0 38.4-6.4 57.6-12.8-6.4-12.8-12.8-32-12.8-57.6v-44.8c0-44.8 38.4-83.2 83.2-83.2h172.8V64h-192c-83.2 0-153.6 70.4-153.6 153.6v57.6c0 25.6-6.4 44.8-12.8 57.6 19.2 12.8 38.4 12.8 57.6 12.8z" fill="" p-id="7923"></path></svg>

            </div>
        </div>
    </div>

    <div class="incblock" id="incTop" style="background: linear-gradient(141deg, #e3ddf3, #DADEEE,#f2f2f2); color: #7a7b7c; border-radius: 12px; position: relative; padding: 20px 22px; margin-top: 18px;">

        <div style="display: flex; justify-content: center; flex-wrap: wrap;">

            <div style="display: flex; width: 50%; justify-content: right; padding: 0 10px; box-sizing: border-box;">

                <div>
                    <div style="display: flex; align-items: center; justify-content: center;">
                        <b style="color: #1A1A1A; font-size: 20px; margin-right: 10px;"><%=pmlist["total_bork"] %></b>
                        <span style="color: #dfd4d4; font-weight: bold; font-size: 12px; display: flex;">
                            <img src="/static/images/coin.png" alt="" height="15" width="15"></span>
                    </div>
                    <div style="font-size: 22px; margin-top: 10px;">


                        <span style="display: inline-block; background: #e3e6fc52; color: #7a7b7c; font-size: 12px; padding: 4px 8px; border-radius: 12px; font-weight: bold; width: 100px; display: flex; justify-content: center;">

                            <svg t="1692903293139" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="13588" width="16" height="16">
                                <path d="M510.208 509.5936m-450.816 0a450.816 450.816 0 1 0 901.632 0 450.816 450.816 0 1 0-901.632 0Z" fill="#FF9552" p-id="13589"></path><path d="M704.2048 384.6144l77.056-145.3056c4.096-7.7312-1.4848-17.0496-10.24-17.0496h-179.3024c-4.2496 0-8.1408 2.304-10.1888 6.0416l-45.9776 83.7632c64.512 5.5296 122.9824 31.9488 168.6528 72.5504zM316.16 384.6144c45.6704-40.6016 104.192-67.072 168.6528-72.6016l-45.9776-83.7632c-2.048-3.7376-5.9392-6.0416-10.1888-6.0416H249.3952c-8.7552 0-14.3872 9.3184-10.24 17.0496l77.0048 145.3568zM510.208 349.7984c-139.9296 0-253.3376 113.408-253.3376 253.3376s113.408 253.3376 253.3376 253.3376 253.3376-113.408 253.3376-253.3376c0-139.8784-113.4592-253.3376-253.3376-253.3376z m137.472 218.624l-49.5104 48.2816c-4.096 3.9936-5.9904 9.7792-5.0176 15.4624l11.6736 68.1472c2.4576 14.2336-12.544 25.1392-25.344 18.3808l-61.184-32.1536c-5.0688-2.6624-11.1616-2.6624-16.2304 0l-61.184 32.1536c-12.8 6.7072-27.7504-4.1472-25.344-18.3808l11.6736-68.1472c0.9728-5.6832-0.9216-11.4176-5.0176-15.4624l-49.5104-48.2816c-10.3424-10.0864-4.6592-27.6992 9.6768-29.7472l68.4032-9.9328c5.6832-0.8192 10.5984-4.4032 13.1584-9.5232l30.6176-62.0032c6.4-12.9536 24.8832-12.9536 31.2832 0l30.6176 62.0032c2.56 5.1712 7.4752 8.704 13.1584 9.5232l68.4032 9.9328c14.336 2.0992 20.0192 19.6608 9.6768 29.7472z" fill="#FFFFFF" p-id="13590"></path></svg>&nbsp;今日佣金</span>
                    </div>

                </div>
            </div>

            <div style="display: flex; width: 50%; justify-content: left; padding: 0 10px; box-sizing: border-box;">

                <div>
                    <div style="display: flex; align-items: center; justify-content: center;">
                        <b style="color: #1A1A1A; font-size: 20px; margin-right: 10px;"><%=uConfig.gnumber(userdt,"rec_play_brok") %></b>
                        <span style="color: #dfd4d4; font-weight: bold; font-size: 12px; display: flex;">
                            <img src="/static/images/coin.png" alt="" height="15" width="15"></span>
                    </div>
                    <div style="font-size: 22px; margin-top: 10px;">


                        <span style="display: inline-block; background: #e3e6fc52; color: #7a7b7c; font-size: 12px; padding: 4px 8px; border-radius: 12px; font-weight: bold; width: 100px; display: flex; justify-content: center;">

                            <svg t="1692903293139" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="13588" width="16" height="16">
                                <path d="M510.208 509.5936m-450.816 0a450.816 450.816 0 1 0 901.632 0 450.816 450.816 0 1 0-901.632 0Z" fill="#FF9552" p-id="13589"></path><path d="M704.2048 384.6144l77.056-145.3056c4.096-7.7312-1.4848-17.0496-10.24-17.0496h-179.3024c-4.2496 0-8.1408 2.304-10.1888 6.0416l-45.9776 83.7632c64.512 5.5296 122.9824 31.9488 168.6528 72.5504zM316.16 384.6144c45.6704-40.6016 104.192-67.072 168.6528-72.6016l-45.9776-83.7632c-2.048-3.7376-5.9392-6.0416-10.1888-6.0416H249.3952c-8.7552 0-14.3872 9.3184-10.24 17.0496l77.0048 145.3568zM510.208 349.7984c-139.9296 0-253.3376 113.408-253.3376 253.3376s113.408 253.3376 253.3376 253.3376 253.3376-113.408 253.3376-253.3376c0-139.8784-113.4592-253.3376-253.3376-253.3376z m137.472 218.624l-49.5104 48.2816c-4.096 3.9936-5.9904 9.7792-5.0176 15.4624l11.6736 68.1472c2.4576 14.2336-12.544 25.1392-25.344 18.3808l-61.184-32.1536c-5.0688-2.6624-11.1616-2.6624-16.2304 0l-61.184 32.1536c-12.8 6.7072-27.7504-4.1472-25.344-18.3808l11.6736-68.1472c0.9728-5.6832-0.9216-11.4176-5.0176-15.4624l-49.5104-48.2816c-10.3424-10.0864-4.6592-27.6992 9.6768-29.7472l68.4032-9.9328c5.6832-0.8192 10.5984-4.4032 13.1584-9.5232l30.6176-62.0032c6.4-12.9536 24.8832-12.9536 31.2832 0l30.6176 62.0032c2.56 5.1712 7.4752 8.704 13.1584 9.5232l68.4032 9.9328c14.336 2.0992 20.0192 19.6608 9.6768 29.7472z" fill="#FFFFFF" p-id="13590"></path></svg>&nbsp;历史佣金</span>
                    </div>

                </div>
            </div>

            <div style="display: flex; width: 50%; justify-content: right; padding: 0 10px; box-sizing: border-box; margin-top: 10px; font-size: 15px; font-weight: bold; color: #000;">

                <div>
                    <div>当前返佣级别</div>
                    <div style="margin-top: 8px;">
                        每万<%=Regex.Match(pmlist["return_amount"] + "", @"^\d+").Value %>元
               
                    </div>
                </div>


            </div>


            <div style="display: flex; width: 50%; justify-content: center; margin-top: 10px;">

                <div style="position: relative; left: -14px;">
                    <div style="display: flex; align-items: center; justify-content: center;">
                        <b style="color: #1A1A1A; font-size: 20px; margin-right: 10px;"><%=uConfig.gnumber(userdt,"play_brok") %></b>
                        <span style="color: #dfd4d4; font-weight: bold; font-size: 12px; display: flex;">
                            <img src="/static/images/coin.png" alt="" height="15" width="15"></span>
                    </div>
                    <div style="color: #8d8d8d; font-size: 12px; text-align: center;">
                        未领取佣金
                   
                    </div>
                    <div style="margin-top: 10px; text-align: center;">
                        <a id="receive_button" style="<%=Convert.ToDouble(uConfig.gnumber(userdt, "play_brok")) > 0 ? "background: #f32121; color: #fff;": "background: #eee; color: #aaa;" %> font-weight: bold; padding: 3px 15px; border-radius: 50px; font-size: 15px; cursor: pointer;" <%=Convert.ToDouble(uConfig.gnumber(userdt, "play_brok")) > 0 ? "onclick='receive_play_brok()'" : "" %>>立即领取</a>
                    </div>

                </div>
            </div>


        </div>

    </div>

    <style>
        .inctabs {
            display: flex;
            margin-bottom: 18px;
            margin-top: 8px;
        }

            .inctabs svg {
                margin-right: 3px;
            }

            .inctabs a {
                display: inline-block;
                color: #000;
                font-size: 13px;
                padding: 5px 9px;
                width: 25%;
                text-align: center;
                cursor: pointer;
                padding: 10px 0;
                border-radius: 8px;
                display: flex;
                align-items: center;
                font-weight: bold;
                cursor: pointer;
                background: #fff;
                color: #000;
                justify-content: center;
                margin: 0 3px;
            }

                .inctabs a.active {
                    background: linear-gradient(170deg, #9eefaa, #69db79);
                }



        table th {
            padding: 5px 0;
            font-size: 16px;
            border-bottom: 1px solid #f2f2f2;
            padding-bottom: 6px;
            background: #FFC201;
            color: #000;
            font-weight: bold;
            text-align: center;
        }

        table td {
            font-size: 16px;
            padding: 10px 18px;
            background: #fff;
            color: #000;
            font-weight: bold;
            border: 1px solid #FFC201;
            text-align: center;
        }

        #agent_data td {
            font-size: 12px;
        }
    </style>

    <div class="inctabs">
        <a class="active">
            <svg t="" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="" width="16" height="16">
                <path d="M853.333333 386.133333a145.706667 145.706667 0 0 1 0 287.061334V386.133333z m-42.666666 283.989334v260.608a42.666667 42.666667 0 0 1-61.610667 38.272L298.666667 746.069333V896a42.666667 42.666667 0 0 1-42.666667 42.666667H213.333333a42.666667 42.666667 0 0 1-42.666666-42.666667v-213.290667h-0.042667L85.333333 682.666667a42.666667 42.666667 0 0 1-42.666666-42.666667V384a42.666667 42.666667 0 0 1 42.666666-42.666667h85.973334l578.005333-282.026666a42.666667 42.666667 0 0 1 61.354667 38.4v572.416z m-512-19.584l426.666666 210.602666V166.016L298.666667 377.514667v273.066666zM128 426.666667v170.666666h85.333333v-170.666666H128z" fill="#000000" p-id="1478"></path></svg><span>我的推广</span></a>
        <a>
            <svg t="" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="" width="16" height="16">
                <path d="M494.762667 585.557333c-26.538667-28.416-38.698667-62.677333-45.184-99.072a364.8 364.8 0 0 1-6.528-59.477333 286.634667 286.634667 0 0 1 3.84-64.554667c9.642667-54.698667 38.698667-92.586667 83.626666-119.466666a195.157333 195.157333 0 0 1 103.936-29.354667 195.157333 195.157333 0 0 1 103.893334 29.312c44.928 26.88 74.026667 64.810667 83.626666 119.466667 3.968 21.461333 4.821333 43.008 3.84 64.597333a364.8 364.8 0 0 1-6.528 59.477333c-6.485333 36.394667-18.645333 70.656-45.184 99.072-1.28 1.792-5.76 9.301333-6.954666 11.434667 0.512 0.341333 120.106667 66.432 157.568 91.392 31.104 20.736 52.181333 50.517333 54.4 89.216 1.066667 18.858667 2.218667 55.637333 2.218666 74.538667 0 33.152-18.730667 43.733333-42.666666 43.733333-58.368 0-170.197333 0.170667-304.213334 0.042667-134.058667 0.128-234.752-0.213333-293.12-0.042667-25.642667 0.042667-42.666667-19.626667-42.666666-43.733333 0-18.901333 2.346667-55.68 3.413333-74.538667 2.133333-38.698667 10.965333-68.48 42.069333-89.173333 37.461333-25.002667 142.208-82.944 156.16-90.197334 0.426667-0.426667 2.56-0.853333 3.114667-1.28-1.237333-2.090667-7.381333-9.6-8.661333-11.392z m401.365333 225.493334c0-44.928-10.837333-67.84-46.634667-87.594667-26.154667-14.464-115.285333-63.018667-131.541333-74.581333-21.333333-15.189333-30.293333-37.717333-25.514667-61.781334 0.682667-4.992 2.133333-10.069333 4.309334-15.189333 4.053333-9.514667 17.92-29.696 18.517333-30.378667 12.373333-14.933333 20.565333-31.445333 24.533333-50.090666 1.109333-5.376 8.448-79.786667 3.968-107.946667a102.784 102.784 0 0 0-38.058666-65.152 120.064 120.064 0 0 0-30.677334-16.341333 125.269333 125.269333 0 0 0-41.898666-7.808 125.269333 125.269333 0 0 0-41.898667 7.808c-11.690667 4.266667-21.888 9.770667-30.72 16.341333-19.626667 15.573333-33.706667 37.930667-38.016 65.152-4.48 28.16 2.858667 102.570667 4.010667 107.946667 3.925333 18.645333 12.074667 35.114667 24.490666 50.090666 0.554667 0.682667 14.464 20.906667 18.517334 30.378667 2.176 5.12 5.76 10.197333 6.485333 15.189333 4.736 24.064-6.357333 46.592-27.690667 61.781334-16.213333 11.52-94.208 60.16-120.362666 74.581333-35.84 19.797333-44.672 46.293333-43.946667 87.594667h512.128zM480.768 218.069333c-32.128-16.469333-72.362667-19.925333-111.189333-5.930666C322.133333 229.290667 295.68 262.912 289.066667 311.466667a298.368 298.368 0 0 0 6.656 112.085333c8.576 34.56 47.018667 82.858667 53.674666 93.866667 17.792 29.312 5.333333 62.165333-28.757333 81.749333-19.072 11.008-39.168 22.997333-59.008 34.005333-29.653333 16.426667-59.434667 32.64-89.258667 48.768-40.832 22.101333-62.378667 40.106667-54.058666 86.997334h95.104c29.568 0 42.453333 21.845333 42.581333 42.538666 0 21.205333-15.189333 41.472-42.581333 41.472-32.597333 0-57.472 0.128-107.264 0C63.445333 852.778667 40.277333 830.293333 42.666667 788.48c1.152-20.053333 2.688-34.986667 3.84-55.04 2.389333-41.258667 23.722667-72.96 57.344-94.976 40.448-26.581333 83.456-49.493333 125.610666-73.642667 9.472-5.418667 25.813333-13.525333 49.066667-24.32a230.570667 230.570667 0 0 0-10.581333-12.672c-33.408-35.072-46.933333-76.970667-53.077334-122.666666-6.058667-44.885333-7.68-89.813333 4.608-134.101334 29.141333-104.874667 145.194667-166.101333 255.445334-134.912 30.592 8.618667 57.472 32.256 57.472 32.256 20.437333 20.224-41.642667 54.101333-51.626667 49.664z" fill="#000000" p-id="1692"></path></svg><span>直属查询</span></a>
        <a>
            <svg t="" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="" width="16" height="16">
                <path d="M516.266667 938.666667c-189.269333 0-346.069333-96.853333-410.154667-260.693334a96.981333 96.981333 0 0 0-3.029333-6.570666 36.266667 36.266667 0 0 1 21.162666-50.474667l11.093334-3.626667a42.666667 42.666667 0 0 1 48.213333 15.914667c7.04 9.941333 11.093333 16.256 12.245333 19.029333 52.181333 124.757333 176.256 196.352 320.426667 196.352 149.632 0 274.816-102.314667 320.512-233.770666l-42.666667-12.672a12.8 12.8 0 0 1-4.437333-22.186667l121.685333-98.645333a12.8 12.8 0 0 1 20.138667 5.802666l48.810667 142.464a12.8 12.8 0 0 1-15.36 16.512l-44.245334-11.733333C863.232 808.533333 713.130667 938.666667 516.266667 938.666667z m-1.28-853.333334c190.08 0 344.789333 109.44 406.272 275.584a24.32 24.32 0 0 1-16.384 31.914667l-32.170667 8.832a24.405333 24.405333 0 0 1-29.013333-14.165333c-51.285333-123.648-186.282667-220.672-328.661334-220.672-148.693333 0-286.976 109.482667-332.373333 240.725333l51.84 18.474667a12.8 12.8 0 0 1 3.285333 22.4l-126.293333 92.458666a12.8 12.8 0 0 1-19.626667-6.186666L43.264 392.32a12.8 12.8 0 0 1 15.701333-16.384l49.28 14.464C165.376 216.448 319.317333 85.333333 514.986667 85.333333zM376.917333 290.773333a42.24 42.24 0 0 1 56.106667 11.306667l82.56 113.706667 80.341333-113.664a40.106667 40.106667 0 0 1 54.613334-10.496 36.096 36.096 0 0 1 9.216 51.925333l-70.357334 93.866667h53.888a37.290667 37.290667 0 1 1 0 74.581333h-88.874666l1.194666 59.605333h92.501334a34.218667 34.218667 0 0 1 0 68.394667h-92.501334l-0.597333 85.845333a42.666667 42.666667 0 0 1-42.666667 42.368H512a42.666667 42.666667 0 0 1-42.666667-42.666666V640H377.856a34.218667 34.218667 0 0 1 0-68.394667H469.333333V512H379.178667a37.290667 37.290667 0 1 1 0-74.624h58.154666l-70.528-95.146667a35.498667 35.498667 0 0 1 10.069334-51.456z" fill="#000000" p-id="1914"></path></svg><span>业绩查询</span></a>
        <a>
            <svg t="" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="" width="16" height="16">
                <path d="M737.109333 496.554667L682.666667 469.333333l-54.442667 27.221334a21.333333 21.333333 0 0 1-30.890667-19.072V128a42.666667 42.666667 0 0 1 42.666667-42.666667h85.333333a42.666667 42.666667 0 0 1 42.666667 42.666667v349.482667a21.333333 21.333333 0 0 1-30.890667 19.072zM896 85.333333a42.666667 42.666667 0 0 1 42.666667 42.666667v768a42.666667 42.666667 0 0 1-42.666667 42.666667H170.666667a42.666667 42.666667 0 0 1-42.666667-42.666667V128a42.666667 42.666667 0 0 1 42.666667-42.666667h298.666666a42.666667 42.666667 0 0 1 0 85.333334H384v682.666666h469.333333V128a42.666667 42.666667 0 0 1 42.666667-42.666667zM213.333333 853.333333h85.333334V170.666667H213.333333v682.666666z" fill="#000000" p-id="2190"></path></svg><span>推广教程</span></a>
    </div>

    <div class="incblock" id="incIndex" style="padding-bottom: 18px;">

        <div style="background: #fff; padding: 8px 18px;">
            <h4 style="margin-bottom: 5px;">推广数据</h4>
            <div style="color: gray; font-weight: 100; font-size: 12px; margin-bottom: 18px;">
                预计更新时间：<%=cae.GetCache<string>("play_settle_state") %>
            </div>

            <div style="display: flex; flex-wrap: wrap;">


                <div style="width: 50%; margin-bottom: 18px;">
                    <div style="color: gray; margin-bottom: 3px; font-size: 12px;">我的ID</div>
                    <div style="font-weight: bold; font-size: 16px; color: #2a2b2c;">
                        <%=uConfig.gd(userdt,"parent_code") %>
                    </div>
                </div>

                <div style="width: 50%; margin-bottom: 18px;">
                    <div style="color: gray; margin-bottom: 3px; font-size: 12px;">推荐ID</div>
                    <div style="font-weight: bold; font-size: 16px; color: #2a2b2c;">
                        <%=uConfig.gd(userdt,"parent_usercode") %>
                    </div>
                </div>

                <div style="width: 50%; margin-bottom: 18px;">
                    <div style="color: gray; margin-bottom: 3px; font-size: 12px;">团队人数（新增）</div>
                    <div style="font-weight: bold; font-size: 16px; color: #2a2b2c;">
                        <%=pmlist["team_usernum"] %>
                    </div>
                </div>

                <div style="width: 50%; margin-bottom: 18px;">
                    <div style="color: gray; margin-bottom: 3px; font-size: 12px;">今日团队总流水</div>
                    <div style="font-weight: bold; font-size: 16px; color: #2a2b2c;">
                        <%=pmlist["team_amount"] %>
                    </div>
                </div>

                <div style="width: 50%; margin-bottom: 18px;">
                    <div style="color: gray; margin-bottom: 3px; font-size: 12px;">直属人数（新增）</div>
                    <div style="font-weight: bold; font-size: 16px; color: #2a2b2c;">
                        <%=pmlist["usernum"] %>
                    </div>
                </div>

                <div style="width: 50%; margin-bottom: 18px;">
                    <div style="color: gray; margin-bottom: 3px; font-size: 12px;">今日直属下级总流水</div>
                    <div style="font-weight: bold; font-size: 16px; color: #2a2b2c;">
                        <%=pmlist["user_amount"] %>
                    </div>
                </div>

                <div style="width: 50%; margin-bottom: 18px;">
                    <div style="color: gray; margin-bottom: 3px; font-size: 12px;">昨日佣金</div>
                    <div style="font-weight: bold; font-size: 16px; color: #2a2b2c;">
                        0
                    </div>
                </div>

                <div style="width: 50%; margin-bottom: 18px;">
                    <div style="color: gray; margin-bottom: 3px; font-size: 12px;">今日佣金预测</div>
                    <div style="font-weight: bold; font-size: 16px; color: #2a2b2c;">
                        <%=pmlist["total_bork"] %>
                    </div>
                </div>

                <%--    <div style="width: 50%; margin-bottom: 18px;">
                    <div style="color: #514646; margin-bottom: 3px; font-size: 12px;">当前可领佣金</div>
                    <div style="font-weight: bold; font-size: 16px; color: #b99f20;">
                        <%=uConfig.gnumber(userdt,"play_brok") %>
                    </div>
                    <a style="display: inline-block; background: #DDE6F0; font-size: 12px; color: #393c3f; padding: 2px 8px; border-radius: 2px; font-weight: bold; margin-top: 5px; cursor: pointer;" onclick="receive_play_brok()">领取佣金</a>
                </div>

                <div style="width: 50%; margin-bottom: 18px;">
                    <div style="color: gray; margin-bottom: 3px; font-size: 12px;">已领取佣金</div>
                    <div style="font-weight: bold; font-size: 16px; color: #2a2b2c;">
                        <%=uConfig.gnumber(userdt,"rec_play_brok") %>
                    </div>
                </div>--%>
            </div>


        </div>


        <div style="background: #ffff; padding: 18px; margin-top: 18px; border-radius: 8px;">
            <h4 style="margin-bottom: 8px;">推广信息</h4>
            <div style="font-size: 12px; color: #aaa; margin-bottom: 22px;">
                邀请成功将成为您的下属       
            </div>
            <div style="display: flex;">

                <div id="invite_code" style="margin: 0 auto;"></div>
            </div>


            <script>
                var qrcode = new QRCode(document.getElementById("invite_code"), {
                    text: '<%=getHost(Request).Replace("http://","https://") + "/register.aspx?code=" + uConfig.gd(userdt, "parent_code") %>',
                    width: 100,
                    height: 100,
                    colorDark: "#000000",
                    colorLight: "#ffffff",
                    margin: 5,
                    correctLevel: QRCode.CorrectLevel.Q
                });
            </script>



            <div style="display: flex; font-size: 14px; align-items: center; margin-top: 18px;">
                <div style="color: #aaa; margin-right: 10px; flex-shrink: 0; width: 60px;"><b>推广码</b></div>
                <div style="width: 100%; position: relative;">
                    <input disabled="disabled" value="<%=uConfig.gd(userdt,"parent_code") %>" style="background: #f6f6f6; border: 0; border-radius: 8px; padding: 18px 20px; color: #000; width: 100%; font-weight: bold; box-sizing: border-box; padding-right: 39px;">

                    <svg t="" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="19366" width="18" height="18" style="position: absolute; top: 0; right: 0; padding: 18px; cursor: pointer;" onclick="textCopy('<%=uConfig.gd(userdt,"parent_code") %>')">
                        <path d="M866.133333 919.466667h-405.333333c-29.866667 0-53.333333-23.466667-53.333333-53.333334v-618.666666c0-29.866667 23.466667-53.333333 53.333333-53.333334h405.333333c29.866667 0 53.333333 23.466667 53.333334 53.333334v618.666666c0 29.866667-23.466667 53.333333-53.333334 53.333334z" fill="#69C0FF" p-id="19367"></path><path d="M770.133333 428.8h-213.333333c-12.8 0-21.333333-8.533333-21.333333-21.333333s8.533333-21.333333 21.333333-21.333334h213.333333c12.8 0 21.333333 8.533333 21.333334 21.333334s-10.666667 21.333333-21.333334 21.333333zM770.133333 556.8h-213.333333c-12.8 0-21.333333-8.533333-21.333333-21.333333s8.533333-21.333333 21.333333-21.333334h213.333333c12.8 0 21.333333 8.533333 21.333334 21.333334s-10.666667 21.333333-21.333334 21.333333zM770.133333 684.8h-213.333333c-12.8 0-21.333333-8.533333-21.333333-21.333333s8.533333-21.333333 21.333333-21.333334h213.333333c12.8 0 21.333333 8.533333 21.333334 21.333334s-10.666667 21.333333-21.333334 21.333333z" fill="#FFFFFF" p-id="19368"></path><path d="M567.466667 834.133333h-405.333334c-29.866667 0-53.333333-23.466667-53.333333-53.333333v-618.666667c0-29.866667 23.466667-53.333333 53.333333-53.333333h405.333334c29.866667 0 53.333333 23.466667 53.333333 53.333333v618.666667c0 29.866667-23.466667 53.333333-53.333333 53.333333z" fill="#1890FF" p-id="19369"></path><path d="M471.466667 343.466667h-213.333334c-12.8 0-21.333333-8.533333-21.333333-21.333334s8.533333-21.333333 21.333333-21.333333h213.333334c12.8 0 21.333333 8.533333 21.333333 21.333333s-10.666667 21.333333-21.333333 21.333334zM471.466667 471.466667h-213.333334c-12.8 0-21.333333-8.533333-21.333333-21.333334s8.533333-21.333333 21.333333-21.333333h213.333334c12.8 0 21.333333 8.533333 21.333333 21.333333s-10.666667 21.333333-21.333333 21.333334zM471.466667 599.466667h-213.333334c-12.8 0-21.333333-8.533333-21.333333-21.333334s8.533333-21.333333 21.333333-21.333333h213.333334c12.8 0 21.333333 8.533333 21.333333 21.333333s-10.666667 21.333333-21.333333 21.333334z" fill="#FFFFFF" p-id="19370"></path>
                    </svg>
                </div>
            </div>
            <div style="display: flex; font-size: 14px; align-items: center; margin-top: 18px;">
                <div style="color: #aaa; margin-right: 10px; flex-shrink: 0; width: 60px;"><b>推广链接</b></div>
                <div style="width: 100%; position: relative;">
                    <input disabled="disabled" value="<%=getHost(Request).Replace("http://","https://") + "/register.aspx?code=" + uConfig.gd(userdt, "parent_code") %>" style="background: #f6f6f6; border: 0; border-radius: 8px; padding: 18px 20px; color: #000; width: 100%; font-weight: bold; box-sizing: border-box; padding-right: 39px;">

                    <svg t="" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="19366" width="18" height="18" style="position: absolute; top: 0; right: 0; padding: 18px; cursor: pointer;" onclick="textCopy('<%=getHost(Request).Replace("http://","https://") + "/register.aspx?code=" + uConfig.gd(userdt, "parent_code") %>')">
                        <path d="M866.133333 919.466667h-405.333333c-29.866667 0-53.333333-23.466667-53.333333-53.333334v-618.666666c0-29.866667 23.466667-53.333333 53.333333-53.333334h405.333333c29.866667 0 53.333333 23.466667 53.333334 53.333334v618.666666c0 29.866667-23.466667 53.333333-53.333334 53.333334z" fill="#69C0FF" p-id="19367"></path><path d="M770.133333 428.8h-213.333333c-12.8 0-21.333333-8.533333-21.333333-21.333333s8.533333-21.333333 21.333333-21.333334h213.333333c12.8 0 21.333333 8.533333 21.333334 21.333334s-10.666667 21.333333-21.333334 21.333333zM770.133333 556.8h-213.333333c-12.8 0-21.333333-8.533333-21.333333-21.333333s8.533333-21.333333 21.333333-21.333334h213.333333c12.8 0 21.333333 8.533333 21.333334 21.333334s-10.666667 21.333333-21.333334 21.333333zM770.133333 684.8h-213.333333c-12.8 0-21.333333-8.533333-21.333333-21.333333s8.533333-21.333333 21.333333-21.333334h213.333333c12.8 0 21.333333 8.533333 21.333334 21.333334s-10.666667 21.333333-21.333334 21.333333z" fill="#FFFFFF" p-id="19368"></path><path d="M567.466667 834.133333h-405.333334c-29.866667 0-53.333333-23.466667-53.333333-53.333333v-618.666667c0-29.866667 23.466667-53.333333 53.333333-53.333333h405.333334c29.866667 0 53.333333 23.466667 53.333333 53.333333v618.666667c0 29.866667-23.466667 53.333333-53.333333 53.333333z" fill="#1890FF" p-id="19369"></path><path d="M471.466667 343.466667h-213.333334c-12.8 0-21.333333-8.533333-21.333333-21.333334s8.533333-21.333333 21.333333-21.333333h213.333334c12.8 0 21.333333 8.533333 21.333333 21.333333s-10.666667 21.333333-21.333333 21.333334zM471.466667 471.466667h-213.333334c-12.8 0-21.333333-8.533333-21.333333-21.333334s8.533333-21.333333 21.333333-21.333333h213.333334c12.8 0 21.333333 8.533333 21.333333 21.333333s-10.666667 21.333333-21.333333 21.333334zM471.466667 599.466667h-213.333334c-12.8 0-21.333333-8.533333-21.333333-21.333334s8.533333-21.333333 21.333333-21.333333h213.333334c12.8 0 21.333333 8.533333 21.333333 21.333333s-10.666667 21.333333-21.333333 21.333334z" fill="#FFFFFF" p-id="19370"></path>
                    </svg>
                </div>
            </div>


        </div>




    </div>

    <div class="incblock" id="usersearch" style="display: none;">

        <table style="width: 100%; margin-top: 13px; border-collapse: collapse; border-spacing: 0; border: 1px solid #FFC201;">
            <thead>
                <tr>
                    <th>ID</th>
                    <th>今日流水</th>
                    <th>团队数据</th>
                    <th>直属人数</th>
                </tr>
            </thead>
            <tbody id="user_list">
            </tbody>
        </table>

    </div>

    <div class="incblock" id="dataLists" style="display: none;">

        <h3 style="margin-bottom: 7px;">玩家业绩查询</h3>
        <div style="font-size: 12px; color: #aaa;">
            每日佣金将于次日00:00开始发放
        </div>

        <table id="agent_data" style="width: 100%; margin-top: 13px; border-collapse: collapse; border-spacing: 0; border: 1px solid #FFC201;">
            <thead>
                <tr>
                    <th>日期</th>
                    <th>直营数据</th>
                    <th>团队数据</th>
                    <th>个人佣金</th>
                    <th>操作</th>
                </tr>
            </thead>
            <tbody id="lists">
            </tbody>
        </table>

    </div>


    <div class="incblock" id="brokDetailLists" style="display: none;">
        <table style="width: 100%; margin-top: 13px; table-layout: fixed; border-collapse: collapse; border-spacing: 0; border: 1px solid #FFC201;">
            <thead>
                <tr>
                    <th>ID</th>
                    <th>佣金来源</th>
                    <th>总流水</th>
                    <th>佣金</th>
                </tr>
            </thead>
        </table>
        <table style="width: 100%; table-layout: fixed; border-collapse: collapse; border-spacing: 0;">
            <tbody id="brok_details">
            </tbody>
        </table>
    </div>

    <div class="incblock" id="incTeacher" style="padding: 12px; display: none; background: #fff;">
        <div style="font-weight: 500; font-size: 14px; padding: 10px;">
            <div style="color: #ca5a1a;">
                1、业绩是怎么算的？
            </div>
            <div style="color: #1e1e1e;">
                答：业绩是指推广玩家的流水情况，比如您推广一个玩家，他一把赢1000，一把输800，则为您带来的业绩为1800元。若未充值过，则不计算业绩。
            </div>
        </div>
        <div style="font-weight: 500; font-size: 14px; padding: 10px;">
            <div style="color: #ca5a1a;">
                2、收益是怎么算的？
            </div>
            <div style="color: #1e1e1e;">
                答：<span>收益是直属会员的流水*返佣比，加代理的返佣差收益构成</span>
            </div>
        </div>
        <div style="font-weight: 500; font-size: 14px; padding: 10px;">
            <div style="color: #ca5a1a;">
                3、佣金多久结算
            </div>
            <div style="color: #1e1e1e;">
                答：采用日结模式，每日24:00结算佣金，业绩达到多少按佣金表发放，可在代理页面领取已发放佣金。
            </div>
        </div>
        <div style="font-weight: 500; font-size: 14px; padding: 10px;">
            <div style="color: #ca5a1a;">
                4、佣金多久可领取？
            </div>
            <div style="color: #1e1e1e;">
                答：当日产生的佣金，根据ID尾号再次日不同的时间段领取
            </div>
        </div>
        <img src="/images/tg.jpg?t=<%=DateTime.Now.ToString("yyyyMMddhhmm") %>" alt="" style="width: 100%; margin-top: 18px;">


        <table style="width: 100%; margin-top: -3px; border-collapse: collapse; border-spacing: 0; border: 1px solid #FFC201;">
            <thead>
                <tr>
                    <th>等级</th>
                    <th>下级游戏流水</th>
                    <th>佣金比例</th>
                </tr>
            </thead>
            <tbody id="glevels">
                <asp:Repeater ID="game_levels" runat="server">
                    <ItemTemplate>
                        <tr>
                            <td><%# Container.ItemIndex + 1 %></td>
                            <td><%#Eval("name") %></td>
                            <td>每万返佣<%#Regex.Match(Eval("brok_amount") + "", @"^\d+").Value %>元</td>
                        </tr>
                    </ItemTemplate>
                </asp:Repeater>
            </tbody>
        </table>

    </div>



    <script>
        $('.inctabs a').on('click', function () {
            $(this).addClass("active").siblings().removeClass("active");
            var t = $(this).find('span').text();
            $('.incblock').hide();
            switch (t) {
                case "我的推广":
                    $('#incIndex').show();
                    $('#incTop').show();
                    break;
                case "直属查询":
                    show_users(0);
                    $('#incTop').show();
                    $('#usersearch').show();
                    break;
                case "业绩查询":
                    show_list(0);
                    $('#incTop').show();
                    $('#dataLists').show();
                    break;
                case "推广教程":
                    $('#incTop').show();
                    $('#incTeacher').show();
                    break;
                default:
                    break;

            }
        })
        var receive_brok = function () {
            v3api("receive_brok", {
                data: {
                }
            }, function (e) {
                tp(e.msg);
                $('#receive_button').css({ "background": "#eee", "background": "#aaa" })
            })

        }
    </script>


    <script>
        var show_list = function (index) {
            if (!index) {
                index = 0;
            }


            v3api("lists", { data: { page: 'play_user_daily', p: index, limit: 10 } }, function (e) {
                $('#lists').html('');
                for (var i = 0; i < e.data.list.length; i++) {
                    var obj = e.data.list[i];
                    $('#lists').append('<tr style="">                    <td style="text-align: left;">                        <div style="color: #aaa; font-weight: bold;">                            ' + (obj.state == 0 ? '<span>待结算</span>' : obj.state == 1 ? '<span style="color:#a9554d;">已发放</span>' : '<span>异常</span>') + '                        </div>                        <div style=" margin-top: 3px;">                            ' + formatDate(obj.create_date) + '                        </div>                    </td>                    <td>                        <div style="font-weight: bold; font-size: 13px; ">                            ' + obj.user_amount + '                        </div>                        <div style="background: #5BAB54; color: #fff; display: inline-block; padding: 1px 6px; border-radius: 1px; margin-top: 3px; font-size: 10px;">                            +' + obj.num + '人                        </div>                    </td>                    <td>                        <div style="font-weight: bold; font-size: 13px; ">                            ' + obj.team_amount + '                        </div>                        <div style="background: #35302C; color: #ffe4b5; display: inline-block; padding: 1px 6px; border-radius: 1px; margin-top: 3px; font-size: 10px;">                            +' + obj.parentNum + '人                                           </div>                    </td>                    <td>                        <div>' + (parseFloat(obj.bork_user) + parseFloat(obj.bork_team)).toFixed(2) + '</div>                    </td>                    <td>                        <div style=" display: flex; align-items: center; justify-content: center;cursor:pointer;    font-size: 15px;" onclick="show_details(0,\'' + obj.uid + '\',\'' + obj.create_date + '\')">                            <svg t="" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="3577" width="12" height="12" style="margin-right: 2px;">                                <path d="M512 928a416 416 0 1 0 0-832 416 416 0 0 0 0 832z m0 64a480 480 0 1 1 0-960 480 480 0 0 1 0 960z" fill="#00336D" p-id="3578"></path><path d="M366.528 657.472l201.408-89.536 89.536-201.408-201.408 89.536-89.536 201.408z m-126.08 126.08l167.104-376 376-167.04-167.104 375.936-376 167.04z" fill="#00336D" p-id="3579"></path><path d="M398.848 444.16l45.248-45.312 181.056 181.056-45.248 45.248z" fill="#00336D" p-id="3580"></path></svg>来源                        </div>                    </td>                </tr>');
                }



                //if (e.data.list.length == 10) {
                //    $('#lists').append('<div id="load_more" next_id="' + (parseInt(index) + 1) + '"></div>');
                //}

            })
        }


        var show_users = function (index) {
            if (!index) {
                index = 0;
            }


            v3api("lists", { data: { page: 'play_users', p: index, limit: 50 } }, function (e) {
                $('#user_list').html('');
                for (var i = 0; i < e.data.list.length; i++) {
                    var obj = e.data.list[i];
                    $('#user_list').append('<tr style="">                    <td>                                                <div style="">' + obj.parent_code + '</div>                                                                        </td>                    <td>                        <div style="">' + obj.flowAmount + '</div>                                            </td>                    <td>                        <div>' + obj.team_amount + '</div>                    </td>                    <td>                        <div style="">' + obj.utNumber + '</div>                    </td>                </tr>');
                }



                //if (e.data.list.length == 10) {
                //    $('#lists').append('<div id="load_more" next_id="' + (parseInt(index) + 1) + '"></div>');
                //}

            })
        }


        var show_details = function (index, uid, date) {
            if (!index) {
                index = 0;
            }
            if (index == 0) {
                $('#dataLists').hide();
                $('#brokDetailLists').show();
                $('#brok_details').html('');
            }

            v3api("lists", { data: { page: 'play_brok_details', p: index, limit: 50, uid: uid, date: date } }, function (e) {
                for (var i = 0; i < e.data.list.length; i++) {
                    var obj = e.data.list[i];
                    $('#brok_details').append('<tr style="">                    <td>                                                <div style=" margin-top: 3px;">' + (obj.from_type == 'user' ? '-' : obj.parent_code) + '</div>                    </td>          <td>                        <div >' + (obj.from_type == 'user' ? '下属总业绩' : '<span style="color:#aaa;">团队</span>') + '</div>                                            </td>                    <td>              <div style="font-weight: bold;">' + obj.amount + '</div>                                            </td>                    <td>                        <div>' + obj.bork + '</div>                    </td>                             </tr>');
                }


                if (e.data.list.length == 50) {
                    $('#details_list').append('<div id="load_more" next_id="' + (parseInt(index) + 1) + '">加载更多</div>');
                }

            })
        }


        function formatDate(inputDateString) {
            var inputDate = new Date(inputDateString);
            var formattedDate = (inputDate.getMonth() + 1).toString().padStart(2, '0') + '-' + inputDate.getDate().toString().padStart(2, '0');
            return formattedDate;
        }

        var receive_play_brok = function () {
            v3api("receive_play_brok", {
                data: {
                }
            }, function (e) {
                tp(e.msg);
                if (e.code == 1) {
                    setTimeout(function () {
                        location.href = location.href;
                    }, 1500);
                }
            })

        }
    </script>

</asp:Content>


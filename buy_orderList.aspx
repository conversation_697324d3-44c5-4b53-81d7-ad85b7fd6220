<%@ Page Title="" Language="C#" MasterPageFile="~/top.master" AutoEventWireup="true" CodeFile="buy_orderList.aspx.cs" Inherits="order_list" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="Server">
    <script>
        $(function () {
            popTitle('买币订单 <div style="color: #555; font-weight: 100; font-size: 12px; margin-top: 6px;">本数据由商家官方提供</div>', '<div style="color: #454242; display: flex; flex-direction: column; align-items: center;"><div style="font-weight: bold; font-size: 22px;"><%=uConfig.gnumber(userdt,"amount") %></div><div style="font-size: 12px; display: flex; color: #555; margin-top: 5px;">可用余额<img src="/static/images/coin.png" alt="" height="15" width="15" style="margin-left: 5px;"></div></div>');
            try {
                lastData();
            } catch (e) {

            }
        })
    </script>
    <style>
        body {
            background: linear-gradient(183deg, #E7E9F5, transparent);
            background-repeat: no-repeat;
        }

        .top-title .pptitle {
            font-size: 25px;
            width: 100%;
        }

        .top-title {
            display: flex;
        }



        .top-title {
            background: none;
        }

        .gtab {
            width: 25%;
            margin: 0px;
            color: #000;
            padding: 8px 0px;
            font-weight: 100;
        }

            .gtab.act {
                color: #fafdde;
                background: #35313163;
            }

        .tablist {
            margin-top: 30px;
            position: fixed;
    left: 0;
    padding: 0 10px;
    box-sizing: border-box;
        }

        #lists {
            height: calc(100vh - 200px);
            overflow-y: auto;
                margin-top: 87px;
        }
    </style>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="Server">






    <%--<div style="display: flex;">

        <div style="margin-left: auto; color: #454242; display: flex; flex-direction: column; align-items: center; margin-top: 22px;">
            <div style="font-weight: bold; font-size: 23px;">108899.66</div>
            <div style="font-size: 12px; display: flex; color: #555; margin-top: 5px;">剩余可用资产<img src="/static/images/coin.png" alt="" height="15" width="15" style="margin-left: 5px;"></div>
        </div>

    </div>--%>

    <div class="tablist">
        <div class="gtab act">全部</div>
        <div class="gtab">交易中</div>
        <div class="gtab">交易完成</div>
        <div class="gtab">订单取消</div>
    </div>

    <div id="lists" style="padding: 0 10px;">
    </div>






    <script>


        var show_list = function (type) {
            v3api("lists", { data: { page: 'buy_orderList', p: 0, limit: 30, state: type } }, function (e) {
                for (var i = 0; i < e.data.list.length; i++) {
                    var obj = e.data.list[i];
                    $('#lists').append('<div style="border-bottom: 1px solid #f5f5f5;background: linear-gradient(159deg, white, transparent);padding: 20px 15px;display: flex;margin-top: 20px;border-radius: 6px;box-shadow: 6px 5px 6px #7979813d;    cursor: pointer;" onclick="javascript:location.href=\'../buy_transaction.aspx?id=' + obj.orderId + '\'">            <div style="display: flex;align-items: center;">                    <div style="background: #0066EF;color: #fff;width: 39px;height: 39px;font-size: 19px;border-radius: 50%;text-align: center;line-height: 39px;position:relative;">币                                       </div>                                                        </div>            <div style="    width: 100%;    margin-left: 19px;">    <div style="display: flex; align-items: center;">                    <div style="font-size: 12px; color: #7d8b9d;">                        <div style="font-size: 20px; color: #0066EF;">' + obj.amount  +(obj.payment_type != "USDT" ? 'CY' : '<img src="/static/images/coin.png" alt="" height="15" width="15" style="margin-left: 5px;">') +'                        </div>'
                        +
                        (obj.payment_type != "USDT" ? `<div style="font-size: 18px;color: #2a2b2c;font-weight: bold;">≈${usd(obj.amount)}<img src="/static/images/coin.png" alt="" height="15" width="15" style="margin-left: 5px;"></div>`:``)
                    +'<div>收款方式：' + obj.payment_type + '</div>                                            </div>                    <div style="margin-left: auto;text-align: right;">                        <div style="font-size: 14px;color:' + (obj.state == "1" ? "#4faf6e" : obj.state == "1000" ? "#222" : obj.state == "1001" ? " #7d8b92d" : obj.state == "-1" ? "#bb1818" : obj.state == "-2" ? "gray" : "gray") + ';display: flex;align-items: center;"><span style="margin-left: 3px;">' + (obj.state == "1" ? "交易完成" : obj.state == "1000" ? "等待上传付款截图" : obj.state == "1001" ? "等待商家确认" : obj.state == "-1" ? "交易超时" : obj.state == "-2" ? "订单取消" : "其他") + '</span></div>                                            </div>                </div>                       </div>                </div>');
                }

            })
        }

        var type = '<%=Request.QueryString["type"] %>';





        var confirm_transaction = function (id) {
            event.stopPropagation();
            v3api("confirm_order", { data: { id: id } }, function (e) {
                tp('确认成功');
                var ls = $('[listid=' + id + ']');
                ls.remove();
                //ls.find(".statedata").html('<span style="border-radius:18px;padding:2px 8px;font-size:12px;background:#2A3EE8;color:#fff;">已完成</span>');
            })

        }


        var lastData = function () {
            try {
                if (type != "") {
                    $('.tablist').hide();
                    //$('.pptitle').html(type + '记录');
                }
            } catch (e) {

            }
        }
        if (type != "") {
            show_list(type);
            lastData();
        } else {
            show_list('');
        }



        initTabs(function (e) {
            console.log('select', e);
            $('#lists').html('');

            switch (e) {
                case "全部":
                    e = "";
                    break;
                case "交易中":
                    e = "1000,1001";
                    break;
                case "交易完成":
                    e = "1";
                    break;
                case "订单取消":
                    e = "-1,-2";
                    break;
                default:
                    break;
            }

            show_list(e);
        })
    </script>
</asp:Content>


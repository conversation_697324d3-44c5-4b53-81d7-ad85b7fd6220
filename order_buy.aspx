<%@ Page Title="" Language="C#" MasterPageFile="~/top.master" AutoEventWireup="true" CodeFile="order_buy.aspx.cs" Inherits="order_list" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="Server">
    <script>
        $(function () {
            popTitle('购买', '');
        })
    </script>

    <style>
        .top-title {
            background: none;
        }
    </style>
    <style>
        body {
            background: linear-gradient(45deg, #F0F4F9, #F5F5F8);
            background-repeat: no-repeat;
            height: 100vh;
        }

        .select_tab {
            color: #000;
            box-sizing: border-box;
            padding: 12px 20px;
            text-align: center;
            font-weight: bold;
            font-size: 13px;
            background: #eee;
            border-radius: 12px;
            width: 50%;
            margin: 0 10px;
        }

            .select_tab.activity {
                background: #3c3351;
                color: #fff;
                transition: all 0.2s;
            }

        .theme-green .back-bar .selected-bar {
            background-color: #3838f5!important;
            background-image: linear-gradient(to bottom, #4747ff, #3838f5)!important;
        }

        .slider-container {
            margin-bottom: 38px;
            margin-top: 52px;
        }
    </style>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="Server">


    <%if (uConfig.gd(userdt, "state") == "0")
      {
    %>





    <div style="background: #fff; border-radius: 8px; padding: 18px; font-size: 14px;">

        <div style="display: flex; margin-bottom: 25px;">
            <div style="color: gray;">交易编码</div>
            <div style="margin-left: auto; color: #222; font-weight: bold;"><%=uConfig.gd(userdt,"orderId") %></div>
        </div>

        
        
    <%if (uConfig.gd(userdt, "payment_type") == "USDT")
        {
    %>

        
        <div style="display: flex; margin-bottom: 25px;">
            <div style="color: gray;">商品金额</div>
            <div style="margin-left: auto; color: #0066EF; font-weight: bold;"><%=uConfig.gd(userdt,"amount") %><img src="/static/images/coin.png" alt="" height="15" width="15" style="margin-left: 5px;"></div>
        </div>

        <%}
else { %>

        

        <div style="display: flex; margin-bottom: 25px;">
            <div style="color: gray;">商品金额</div>
            <div style="margin-left: auto; color: #0066EF; font-weight: bold;"><%=uConfig.gd(userdt, "amount") %>CY</div>
        </div>


        <div style="display: flex; margin-bottom: 25px;">
            <div style="color: gray;">当前汇率=<%=uConfig.stcdata("usdt_price") %></div>
            <div style="margin-left: auto; color: #0066EF; font-weight: bold;"><%=tousd(uConfig.gd(userdt,"amount")) %><img src="/static/images/coin.png" alt="" height="15" width="15" style="margin-left: 5px;"></div>
        </div>

        
        <%}%>

        <%--<div style="display: flex; margin-bottom: 25px;">
            <div style="color: gray;">订单拆分</div>
            <div style="margin-left: auto; color: #bd4242; font-weight: bold;">不可拆分</div>
        </div>--%>

        <div style="display: flex;">
            <div style="color: gray;">收款方式</div>
            <div style="margin-left: auto; color: #555; font-weight: bold; display: flex; align-items: center;">
                <span style="margin-right: 5px;" id="payment_type"><%=uConfig.gd(userdt,"payment_type").Replace("网银", "银行卡") %></span>

                <%=uConfig.gd(userdt, "payment_type") == "USDT" ? "<svg t=\"1694773671778\" class=\"icon\" viewBox=\"0 0 1024 1024\" version=\"1.1\" xmlns=\"http://www.w3.org/2000/svg\" p-id=\"5529\" width=\"16\" height=\"16\"><path d=\"M1023.082985 511.821692c0 281.370746-228.08199 509.452736-509.452736 509.452736-281.360557 0-509.452736-228.08199-509.452737-509.452736 0-281.365652 228.092179-509.452736 509.452737-509.452737 281.370746 0 509.452736 228.087085 509.452736 509.452737\" fill=\"#1BA27A\" p-id=\"5530\"></path><path d=\"M752.731701 259.265592h-482.400796v116.460896h182.969951v171.176119h116.460895v-171.176119h182.96995z\" fill=\"#FFFFFF\" p-id=\"5531\"></path><path d=\"M512.636816 565.13592c-151.358408 0-274.070289-23.954468-274.070289-53.50782 0-29.548259 122.706786-53.507821 274.070289-53.507821 151.358408 0 274.065194 23.959562 274.065194 53.507821 0 29.553353-122.706786 53.507821-274.065194 53.50782m307.734925-44.587303c0-38.107065-137.776398-68.995184-307.734925-68.995184-169.953433 0-307.74002 30.888119-307.74002 68.995184 0 33.557652 106.837333 61.516418 248.409154 67.711363v245.729433h116.450707v-245.632637c142.66205-6.001353 250.615085-34.077294 250.615084-67.808159\" fill=\"#FFFFFF\" p-id=\"5532\"></path></svg>" : "<svg t=\"1694285027704\" class=\"icon\" viewBox=\"0 0 1024 1024\" version=\"1.1\" xmlns=\"http://www.w3.org/2000/svg\" p-id=\"39928\" width=\"16\" height=\"16\">                    <path d=\"M829.64898 805.093878H194.35102c-43.885714 0-79.412245-35.526531-79.412244-79.412245V298.840816C114.938776 254.955102 150.465306 219.428571 194.35102 219.428571h635.29796c43.885714 0 79.412245 35.526531 79.412244 79.412245v426.840817c0 43.363265-35.526531 79.412245-79.412244 79.412245z\" fill=\"#F2CB51\" p-id=\"39929\"></path><path d=\"M114.938776 347.95102h794.122448v89.338776H114.938776z\" fill=\"#DBAD2C\" p-id=\"39930\"></path><path d=\"M234.057143 699.559184c-10.971429 0-19.853061-8.881633-19.853061-19.853062v-148.897959c0-10.971429 8.881633-19.853061 19.853061-19.853061 10.971429 0 19.853061 8.881633 19.853061 19.853061v148.897959c0 10.971429-8.881633 19.853061-19.853061 19.853062zM333.322449 699.559184c-10.971429 0-19.853061-8.881633-19.853061-19.853062v-148.897959c0-10.971429 8.881633-19.853061 19.853061-19.853061 10.971429 0 19.853061 8.881633 19.853061 19.853061v148.897959c0 10.971429-8.881633 19.853061-19.853061 19.853062zM432.587755 699.559184c-10.971429 0-19.853061-8.881633-19.853061-19.853062v-148.897959c0-10.971429 8.881633-19.853061 19.853061-19.853061 10.971429 0 19.853061 8.881633 19.853061 19.853061v148.897959c0 10.971429-8.881633 19.853061-19.853061 19.853062z\" fill=\"#E5404F\" p-id=\"39931\"></path></svg>" %>
            </div>
        </div>



    </div>


    <div style="background: #fff; border-radius: 8px; padding: 18px; font-size: 14px; margin-top: 20px;">

        <h2 style="text-align: center;">购买数量</h2>


        <div style="position: relative; background: #f5f5f5; border-radius: 8px; margin-bottom: 12px; display: ;">
            <input style="border: 0px; background: none; font-size: 22px; padding: 13px; outline: none; width: 100%; box-sizing: border-box; text-align: center;" id="sell_number" value="<%=uConfig.gnumber(userdt,"amount") %>">
            <a style="position: absolute; top: 8px; right: 5px; background: #3838f5; color: #fff; display: flex; align-items: center; width: 83px; height: 37px; border-radius: 38px; justify-content: center; cursor: pointer;" onclick="javascript:$('#sell_number').val('<%=uConfig.gnumber(userdt,"amount") %>');generate_advert_data();">全部
            </a>
        </div>





        <div style="display: flex; margin-bottom: 25px;">
            <div style="color: gray;">支付金额</div>
            <div style="margin-left: auto; color: #000; font-weight: bold;"><%=uConfig.gd(userdt,"amount") %><%=(uConfig.gd(userdt, "payment_type") == "USDT" ? "<img src='/static/images/coin.png' alt='' height='15' width='15' style='margin-left: 5px;'>" : "CY") %></div>
        </div>

    </div>



    <a style="display: block; background: #3838f5; color: #fff; width: 100%; padding: 16px; box-sizing: border-box; border-radius: 10px; cursor: pointer; text-align: center; font-size: 14px; margin-top: 13px; text-decoration: none;" onclick="new_advert()" class="create_button">立即下单</a>


    <%
      }
      else
      {
    %>

    <div style="text-align: center; color: red; margin-top: 30px; font-size: 28px;">订单无法交易</div>

    <%
      } %>




    <div class="demo-popup-page payway-select" style="display: none;">
        <div style="padding: 18px; font-size: 13px;">

            <h4 style="color: #3C54D1; margin-bottom: 22px; font-size: 16px; display: flex;">选择收款账户
                <div style="margin-left: auto;" onclick="location.href='payway_add.aspx'">
                    <svg t="1692428311309" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="8668" width="22" height="22">
                        <path d="M642.8 548.2H383c-19.3 0-35-15.7-35-35s15.7-35 35-35h259.8c19.3 0 35 15.7 35 35s-15.7 35-35 35z" fill="#4195F9" p-id="8669"></path><path d="M512.9 678.1c-19.3 0-35-15.7-35-35V383.3c0-19.3 15.7-35 35-35s35 15.7 35 35v259.8c0 19.3-15.6 35-35 35z" fill="#4195F9" p-id="8670"></path><path d="M512.9 902.7c-52.6 0-103.6-10.3-151.6-30.6-46.4-19.6-88-47.7-123.8-83.5s-63.8-77.4-83.5-123.8c-20.3-48-30.6-99.1-30.6-151.6 0-52.6 10.3-103.6 30.6-151.6 19.6-46.4 47.7-88 83.5-123.8s77.4-63.8 123.8-83.5c48-20.3 99.1-30.6 151.6-30.6s103.6 10.3 151.6 30.6c46.4 19.6 88 47.7 123.8 83.5s63.8 77.4 83.5 123.8c20.3 48 30.6 99.1 30.6 151.6 0 52.6-10.3 103.6-30.6 151.6-19.6 46.4-47.7 88-83.5 123.8s-77.4 63.8-123.8 83.5c-48 20.3-99 30.6-151.6 30.6z m0-709c-176.2 0-319.5 143.3-319.5 319.5s143.3 319.5 319.5 319.5 319.5-143.3 319.5-319.5c0-176.1-143.3-319.5-319.5-319.5z" p-id="8671"></path></svg>
                </div>
            </h4>

            <div id="lists" style="max-height: 500px; overflow-y: auto;"></div>

        </div>
    </div>


    <script>
        var alipay_confirm = 0;
        var alipay_confirm_click = function () {
            alipay_confirm = 1;
            if ($('#today_notify').prop('checked')) {
                localStorage.setItem(getFormattedDate() + "_alipay_notify", "1");
            }
            $('#pop-cpt').hide();
        }

        if (localStorage.getItem(getFormattedDate() + "_alipay_notify") == "1") {
            alipay_confirm = 1;
        }

        function getFormattedDate() {
            var today = new Date();

            var year = today.getFullYear();
            var month = (today.getMonth() + 1).toString().padStart(2, '0'); // 月份从0开始，需要加1
            var day = today.getDate().toString().padStart(2, '0');

            var formattedDate = year + month + day;
            return formattedDate;
        }

        var new_advert = function () {
            //security_password(function (e) {
            var payment_type = $('#payment_type').html();
            //if (alipay_confirm == 0 && payment_type == "支付宝转银行卡") {
            //    $('#pop-cpt').show();
            //    return;
            //}

            v3api("new_buyOrder", {
                error: 1,
                data: {
                    //paypwd: e.password,
                    total_amount: $('#sell_number').val(),
                    //accept_separate: $('.select_tab.activity').text() == "可拆分" ? 1 : 0,
                    id: '<%=uConfig.gd(userdt,"id") %>'
                }
            }, function (e) {
                if (e.code != 1) {
                    tp(e.msg);
                    if (typeof (e.orderId) != "undefined") {
                        $('.create_button').css({ "background": "#31394A", "color": "#F6E0C4" });
                        $('.create_button').attr('href', "buy_transaction.aspx?id=" + e.orderId);
                        $('.create_button').html("查看未处理的订单");
                    } else {
                        $('.create_button').css({ "background": "#31394A", "color": "#F6E0C4" });
                        $('.create_button').attr('href', "dating.aspx");
                        $('.create_button').html("返回大厅");
                    }
                    return;
                }
                location.href = 'buy_transaction.aspx?id=<%=uConfig.gd(userdt,"orderId") %>';
            })
            //})
        }

    </script>












    <style>
        .pop-cpt {
            z-index: 999999999;
            position: relative;
            display: none;
        }

        .pop-cpt2 {
            display: flex;
            justify-content: center;
            align-items: center;
            position: fixed;
            left: 0;
            top: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,.7);
            overflow-y: auto;
            padding: 130px 0;
        }

        .pop-cpt a {
            text-decoration: none;
            color: inherit;
            display: block;
        }

            .pop-cpt a:hover {
                text-decoration: none;
            }

        .pop-cpt .pop-cpt-bd {
            line-height: 22px;
            width: 380px;
            padding: 18px 18px;
            box-sizing: border-box;
            border-radius: 16px;
            background: #fff;
            background: radial-gradient(117.33% 20.82% at 50% 0,#C2F0F6 0,hsla(0,0%,100%,0) 100%),#fff;
            font-size: 14px;
            color: #333;
            font-family: "微软雅黑";
            position: relative;
        }

        .pop-cpt .pop-cpt-close {
            display: inline-block;
            height: 36px;
            line-height: 36px;
            padding: 0 15px;
            color: #fff;
            position: absolute;
            top: 0;
            right: 0;
        }

        .pop-cpt .pop-cpt-tit {
            height: 52px;
            line-height: 52px;
            color: #000;
            text-align: center;
            font-size: 18px;
            font-weight: bold;
            color: #f5ecdc;
            text-shadow: 2px 3px 2px #7d5909;
        }

            .pop-cpt .pop-cpt-tit span {
                display: inline-block;
                padding: 0 10px;
                position: absolute;
                top: 0;
                right: 0;
            }

        .pop-cpt .pop-cpt-con {
            padding: 0 6px 3px 6px;
            border-radius: 15px;
            background: none;
        }

        .pop-cpt .pop-cpt-con1 {
            text-align: center;
            color: #000;
        }

        .pop-cpt .pop-cpt-con2 {
            padding-top: 10px;
        }

        .pop-cpt .pop-cpt-con2-tit {
            display: flex;
            justify-content: center;
            padding-bottom: 5px;
        }

            .pop-cpt .pop-cpt-con2-tit span {
                display: inline-block;
                width: 26px;
                line-height: 26px;
                margin-left: -3px;
                text-align: center;
                color: #FFF200;
                font-size: 16px;
                font-weight: bold;
                border-radius: 50%;
                background: #ED1B20;
            }

        .pop-cpt .pop-cpt-con2-logo {
            display: flex;
            justify-content: center;
            height: 60px;
        }

        .pop-cpt .pop-cpt-con2-logol {
            color: #fb2725;
            font-size: 54px;
            line-height: 50px;
            font-weight: bold;
            font-family: Tahoma;
        }

        .pop-cpt .pop-cpt-con2-logor {
            width: 70px;
            position: relative;
        }

        .pop-cpt .pop-cpt-con2-logor-txt1 {
        }

            .pop-cpt .pop-cpt-con2-logor-txt1 span {
                display: inline-block;
                width: 60px;
                height: 22px;
                line-height: 22px;
                text-align: center;
                color: #fff;
                border-radius: 5px;
                background: #0082f3;
                position: absolute;
                left: 8px;
                top: 2px;
            }

        .pop-cpt .pop-cpt-con2-logor-txt2 {
            line-height: 32px;
            color: #333;
            font-size: 30px;
            font-family: impact,arial;
            position: absolute;
            top: 24px;
            left: 0;
        }

        .pop-cpt .pop-cpt-con3 {
            padding: 0 2px;
        }


        .pop-cpt .pop-cpt-con4 {
            text-align: center;
            margin-bottom: 10px;
        }

            .pop-cpt .pop-cpt-con4 span {
                display: inline-block;
                width: 200px;
                height: 30px;
                line-height: 30px;
                font-size: 20px;
                font-weight: bold;
                color: #ff0;
                border-radius: 10px;
                background: #fb2725;
            }

        .pop-cpt .pop-cpt-footer {
            padding: 10px 0;
            color: #fff;
            text-align: center;
        }

        .pop-cpt .pop-cpt-footer1 {
            font-size: 16px;
        }

        .pop-cpt .pop-cpt-footer2 {
            padding-top: 5px;
            font-weight: bold;
            color: #ff0;
        }
    </style>



    <div onclick="$(this).hide()" id="pop-cpt" class="pop-cpt" style="display: ;">
        <div class="pop-cpt2">
            <div class="pop-cpt-bd">

                <div style="position: absolute; top: -46px; left: 0; width: 100%; text-align: center;">
                    <svg t="1703217507852" class="icon" viewBox="0 0 1117 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="20326" width="130" height="130">
                        <path d="M516.887273 225.093818c24.994909 14.429091 32.861091 48.174545 19.874909 78.661818 59.205818 55.435636 69.632 156.439273 20.293818 241.896728l-74.519273 129.163636 9.914182 5.725091c21.969455 12.660364 27.601455 43.985455 12.660364 69.911273-14.987636 25.925818-44.916364 36.677818-66.885818 24.017454l-106.123637-61.253818a84.619636 84.619636 0 0 1-113.431273 28.392727 84.619636 84.619636 0 0 1-32.116363-112.500363L80.384 567.854545c-21.969455-12.660364-27.648-43.938909-12.660364-69.911272 14.987636-25.925818 44.916364-36.677818 66.885819-23.970909l9.914181 5.725091 74.565819-129.117091c49.338182-85.504 142.056727-126.929455 219.694545-103.424 19.874909-26.437818 53.061818-36.538182 78.056727-22.062546z m3.118545 125.346909a19.968 19.968 0 0 0-27.787636-6.004363 20.48 20.48 0 0 0-6.237091 28.020363c15.034182 23.784727 21.876364 52.130909 19.269818 80.384a20.014545 20.014545 0 0 0 18.245818 21.829818 20.48 20.48 0 0 0 21.876364-18.618181 169.146182 169.146182 0 0 0-25.367273-105.611637z" fill="#91D5D8" opacity=".615" p-id="20327"></path><path d="M575.906909 733.137455l-137.402182 39.377454c-28.392727 8.145455-59.252364-12.427636-68.887272-46.033454-9.634909-33.559273 5.585455-67.397818 34.024727-75.543273l12.893091-3.723637-47.941818-167.191272c-31.744-110.685091 8.610909-222.068364 91.22909-268.194909-6.050909-38.074182 12.334545-74.100364 44.730182-83.409455 32.395636-9.309091 67.118545 11.496727 82.152728 47.010909 94.487273-4.654545 187.717818 68.421818 219.461818 179.106909l47.941818 167.191273 12.893091-3.677091c28.392727-8.145455 59.298909 12.474182 68.887273 46.08 9.681455 33.512727-5.585455 67.397818-33.978182 75.543273l-137.402182 39.377454c12.288 51.432727-17.454545 103.703273-68.142546 118.225455-50.734545 14.568727-103.656727-14.010182-120.459636-64.139636z" fill="#91D5D8" p-id="20328"></path></svg>
                </div>

                <div style="font-size: 28px;font-weight: bold;text-align: center;padding-bottom: 10px;color: #4ab8bd;margin-top: 50px;text-shadow: 5px 5px 10px #24a0a56b;color: #bd4a4a;text-shadow: 5px 5px 10px #a524246b;">请使用支付宝转银行卡</div>


                <div class="pop-cpt-con">
                    <div class="pop-cpt-con2">
                    </div>
                    <div class="pop-cpt-con3" style="margin: 10px 0;">
                    </div>

                </div>

                <div>
                    <div style="display: flex; justify-content: center;">
                        <div style="width: 128px; height: 128px; background: #DEF7FF; border-radius: 50%; text-align: center; position: relative; display: flex; justify-content: center; align-items: center; flex-direction: column; margin: 0 18px;">

                            <svg t="1703218044752" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="23758" width="50" height="50">
                                <path d="M224 360H160c-22.08 0-40-17.92-40-40V192c0-22.08 17.92-40 40-40h704c22.08 0 40 17.92 40 40v128c0 22.08-17.92 40-40 40h-64v48h64A88 88 0 0 0 952 320V192A88 88 0 0 0 864 104H160A88 88 0 0 0 72 192v128A88 88 0 0 0 160 408h64v-48z" fill="#55D0D2" p-id="23759"></path><path d="M200 256A24 24 0 0 1 224 232h576a24 24 0 0 1 24 24v576A88 88 0 0 1 736 920H288A88 88 0 0 1 200 832V256z m48 24V832c0 22.08 17.92 40 40 40h448c22.08 0 40-17.92 40-40V280H248z" fill="#55D0D2" p-id="23760"></path><path d="M176 280h672a24 24 0 1 0 0-48h-672a24 24 0 0 0 0 48z" fill="#55D0D2" p-id="23761"></path><path d="M549.184 561.28h54.208v32.256h-69.44v32.256h69.44v31.808h-69.44V736H480.64v-78.4h-68.544v-31.808h68.544v-32.256h-68.544V561.28h53.312l-81.536-136.192h61.376l62.272 116.48h2.688l63.168-116.48h59.584z" fill="#55D0D2" p-id="23762"></path></svg>
                            <strong style="font-size: 20px; color: #55D0D2; margin-top: 5px;">实时响应</strong>
                        </div>
                        <div style="width: 128px; height: 128px; background: #DEF7FF; border-radius: 50%; text-align: center; position: relative; display: flex; justify-content: center; align-items: center; flex-direction: column; margin: 0 18px;">

                            <svg t="1703218168386" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="25967" width="50" height="50">
                                <path d="M0 0h1024v1024H0z" fill="#55D0D2" opacity=".01" p-id="25968"></path><path d="M546.133333 102.4c188.5184 0 341.333333 152.814933 341.333334 341.333333a340.5824 340.5824 0 0 1-124.689067 263.7824 33.9968 33.9968 0 0 0-5.12 5.188267L682.666667 737.723733V716.8a102.4 102.4 0 0 0-102.4-102.4v-51.2h102.4a34.133333 34.133333 0 1 0 0-68.266667h-102.4V443.733333h102.4a34.133333 34.133333 0 1 0 0-68.266666h-54.135467l78.267733-78.267734a34.133333 34.133333 0 0 0-48.264533-48.264533L546.133333 361.335467l-112.401066-112.401067a34.133333 34.133333 0 1 0-48.264534 48.264533L463.735467 375.466667H409.6a34.133333 34.133333 0 1 0 0 68.266666h102.4v51.2h-102.4a34.133333 34.133333 0 1 0 0 68.266667h102.4V614.4H273.066667a34.133333 34.133333 0 0 0-19.114667 5.870933A339.592533 339.592533 0 0 1 204.8 443.733333C204.8 255.214933 357.614933 102.4 546.133333 102.4z m334.6432 577.536A407.825067 407.825067 0 0 0 955.733333 443.733333c0-226.2016-183.3984-409.6-409.6-409.6S136.533333 217.531733 136.533333 443.733333c0 83.285333 24.8832 160.768 67.584 225.4848L190.6688 682.666667H68.266667a34.133333 34.133333 0 0 0-34.133334 34.133333v238.933333a34.133333 34.133333 0 0 0 34.133334 34.133334h580.266666a34.133333 34.133333 0 0 0 10.786134-1.7408l251.869866-83.968A115.0976 115.0976 0 0 0 989.866667 794.999467c0-63.556267-50.3808-112.0256-109.090134-115.029334zM614.4 716.8v34.133333h-238.933333a34.133333 34.133333 0 1 0 0 68.266667h273.066666a34.133333 34.133333 0 0 0 10.786134-1.7408l200.669866-66.901333a46.830933 46.830933 0 1 1 29.5936 88.849066L643.003733 921.6H102.4v-170.666667h102.4a34.133333 34.133333 0 0 0 24.132267-10.001066L287.197867 682.666667H580.266667a34.133333 34.133333 0 0 1 34.133333 34.133333z" fill="#55D0D2" p-id="25969"></path></svg>
                            <strong style="font-size: 20px; color: #55D0D2; margin-top: 5px;">快捷充值</strong>
                        </div>
                    </div>
                </div>


                <div>
                    <div style="text-align: center; font-size: 25px; font-weight: bold; margin-top: 20px; line-height: 36px; color: #000; text-shadow: 3px 3px 6px #0000002e;">
                 <%--       <div>支付宝付款</div>
                        <div>
                            笔笔都有<span style="color: #8BD2D6; margin: 0 2px;">优惠</span>
                        </div>--%>
                        <div style="display: flex; justify-content: center; margin-top: 8px;">

                            <a style="background: #9DE0E3; color: #fff; display: inline-block; padding: 5px 27px; border-radius: 3px; display: flex; width: 158px; justify-content: center; align-items: center;">
                                <svg style="margin-right: 5px;"
                                    height="27" width="27" p-id="62173" xmlns="http://www.w3.org/2000/svg" version="1.1" viewBox="0 0 1024 1024" class="icon" t="">
                                    <path p-id="62174" fill="#ffffff" d="M860.009412 1.445647H164.020706A163.779765 163.779765 0 0 0 0 165.044706v693.940706c0 90.322824 73.396706 163.568941 164.020706 163.568941h695.988706A163.749647 163.749647 0 0 0 1024 858.985412v-6.686118s-266.360471-110.712471-400.835765-175.194353c-90.202353 110.682353-206.576941 177.844706-327.378823 177.844706-204.318118 0-273.709176-178.266353-176.941177-295.634823 21.082353-25.6 56.982588-49.995294 112.670118-63.698824 87.100235-21.323294 225.761882 13.312 355.689412 56.079059a709.993412 709.993412 0 0 0 57.675294-140.559059H244.404706v-40.448h206.486588V298.164706H200.824471V257.686588h250.096941V154.262588s0-17.438118 17.709176-17.438117h100.924236v120.862117h247.265882V298.164706h-247.265882v72.493176h201.84847c-19.305412 78.878118-48.670118 151.491765-85.473882 215.250824 61.259294 22.106353 116.254118 43.038118 157.214117 56.711529C990.539294 691.832471 1024 694.512941 1024 694.512941V165.044706A163.749647 163.749647 0 0 0 860.009412 1.445647zM248.892235 556.122353c-25.569882 2.529882-73.607529 13.824-99.870117 36.954353-78.727529 68.457412-31.623529 193.566118 127.668706 193.566118 92.581647 0 185.133176-59.030588 257.807058-153.569883-103.363765-50.266353-190.976-86.256941-285.605647-76.950588z"></path></svg>笔笔送好礼</a>

                        </div>
                    </div>
                </div>

                <div style="background: #FEF7D7;display: flex;padding: 6px 3px;margin-top: 10px;">

                    <div style="display: flex; align-items: center;">

                        <svg t="1703219012454" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="61053" width="80" height="80" style="">
                            <path d="M328.2 97.4h415.7c43.6 0 71.1 47 49.8 85L691.4 372.8c-11.5 21.4 4 47.3 28.3 47.3h96c33.4 0 48.8 41.6 23.4 63.3L338.8 912.9c-32.5 27.9-81.6-2.1-71.7-43.7L336 579.5c2.7-18.7-11.7-35.4-30.6-35.4h-53.9c-18 0-32.2-15.3-30.8-33.2l25.1-337c3.2-43.2 39.2-76.5 82.4-76.5z" fill="#FEAC33" p-id="61054"></path><path d="M793.7 182.4c21.4-38-6.1-85-49.8-85h-98.5c55.3 18.4 105.3 48.2 147.5 86.5l0.8-1.5zM328.2 97.4c-43.3 0-79.2 33.4-82.4 76.5l-0.1 1.6c40.1-34.3 86.8-61.1 138-78.1h-55.5zM338.8 912.9l38.2-32.8c-35.9-12.7-69.6-30.2-100.2-51.7l-9.7 40.7c-2.8 11.8-0.9 22.7 4.1 31.5 12.3 8.2 25 15.9 38.1 23 10 0.4 20.4-2.9 29.5-10.7z" fill="#FEAC33" p-id="61055"></path><path d="M496.4 113.6c107.2 0 203.3 47.2 268.7 122l27.8-51.7c-42.1-38.3-92.2-68.1-147.5-86.5H383.6c-51.2 17.1-97.9 43.8-138 78.1l-3.3 44.4c64.8-65.6 154.7-106.3 254.1-106.3zM443.2 823.3c-55.5-8.3-106.8-29.4-150.9-60.2l-15.6 65.4c30.6 21.5 64.2 39 100.2 51.7l66.3-56.9z" fill="#FEB133" p-id="61056"></path><path d="M478.3 151c108 0 202.7 57.1 255.5 142.9l31.3-58.2c-65.4-74.8-161.5-122-268.7-122-99.4 0-189.3 40.7-254 106.3l-3.8 50.8C293.3 198 380.3 151 478.3 151zM761.4 550.1l77.7-66.7c25.4-21.7 10.1-63.3-23.4-63.3h-39.1c1 10.1 1.6 20.4 1.6 30.8 0 34.7-5.9 68.1-16.8 99.2zM533.6 745.7c-17.9 3.3-36.4 5.1-55.3 5.1-63.3 0-122-19.6-170.4-53.1l-15.6 65.4c44.1 30.8 95.4 51.9 150.9 60.2l90.4-77.6z" fill="#FEB633" p-id="61057"></path><path d="M220.7 510.9c-1.2 15.5 9.3 29.1 23.8 32.4-9.8-18.8-17.1-39-21.8-60.2l-2 27.8zM460.2 188.4c111.9 0 206.1 75.7 234.4 178.6l39.3-73.1C681 208.1 586.3 151 478.3 151c-98 0-185 47-239.7 119.7l-5.5 74.2c34.8-91.5 123.3-156.5 227.1-156.5zM533.6 745.7l227.8-195.6c10.9-31.1 16.8-64.4 16.8-99.2 0-10.4-0.5-20.7-1.6-30.8h-56.9c-6.4 0-12.2-1.8-17.1-4.9 0.4 5.3 0.5 10.7 0.5 16.1 0 134.2-108.8 243-243 243-50.7 0-97.8-15.5-136.7-42.1l-15.6 65.4c48.4 33.5 107.1 53.1 170.4 53.1 19 0.1 37.5-1.7 55.4-5z" fill="#FFBC34" p-id="61058"></path><path d="M703.2 431.4c0-5.4-0.2-10.8-0.5-16.1-13.5-8.5-19.7-26.7-11.2-42.4l3.1-5.8C666.3 264 572.1 188.4 460.2 188.4c-103.7 0-192.3 65-227.1 156.5l-10.3 138.3c4.6 21.2 12 41.4 21.8 60.2 2.2 0.5 4.6 0.8 7 0.8h53.9c2.3 0 4.4 0.2 6.6 0.7-34.5-33.8-56-80.9-56-133 0-102.8 83.3-186.1 186.1-186.1s186 83.2 186 186-83.3 186.1-186.1 186.1c-40.3 0-77.6-12.8-108.1-34.6 2 4.9 2.8 10.5 2 16.2l-12.5 52.7c39 26.6 86 42.1 136.7 42.1 134.2 0 243-108.7 243-242.9z" fill="#FFC134" p-id="61059"></path><path d="M628.2 411.8c0-102.8-83.3-186.1-186.1-186.1S256 309 256 411.8c0 52.1 21.4 99.2 56 133 10.1 2.2 18.3 9.3 22.1 18.5 30.5 21.8 67.8 34.6 108.1 34.6 102.6 0 186-83.3 186-186.1z m-333.5-19.5c0-71.4 57.8-129.2 129.2-129.2s129.2 57.8 129.2 129.2-57.8 129.2-129.2 129.2-129.2-57.8-129.2-129.2z" fill="#FFC634" p-id="61060"></path><path d="M423.9 392.3m-129.2 0a129.2 129.2 0 1 0 258.4 0 129.2 129.2 0 1 0-258.4 0Z" fill="#FFCB34" p-id="61061"></path><path d="M310.1 932.8c-9.5 0-19.1-2.6-27.8-8-20-12.2-29.5-34.9-24-57.7l68.8-289.3c0.8-6.2-1.1-12.4-5.2-17.1-4.2-4.8-10.2-7.6-16.6-7.6h-53.9c-11.1 0-21.7-4.7-29.3-12.8s-11.4-19.1-10.6-30.1l25.1-337c1.7-23.1 12-44.6 29-60.4s39.2-24.5 62.4-24.5h415.7c23.7 0 45 12.2 57 32.7 12 20.4 12.2 45 0.6 65.6L699.3 377c-3.9 7.2-3.7 15.8 0.5 22.8 4.2 7.1 11.6 11.3 19.8 11.3h96c19 0 35.6 11.5 42.2 29.4 6.6 17.9 1.5 37.4-13 49.7L344.6 919.8c-10 8.6-22.2 13-34.5 13z m18.1-826.4c-18.6 0-36.4 7-50.1 19.7s-22 29.9-23.3 48.5l-25.1 337c-0.5 6.2 1.6 12 5.8 16.5s9.9 7 16.1 7h53.9c11.6 0 22.6 5 30.2 13.8s11 20.4 9.3 31.9l-0.2 0.8-68.9 289.7c-4.4 18.3 5.4 31.7 15.9 38.1s26.9 8.9 41.2-3.3l500.3-429.5c10.6-9 10.9-21.3 7.8-29.8-3.1-8.5-11.4-17.6-25.3-17.6h-96c-14.6 0-27.8-7.5-35.3-20s-7.8-27.7-0.9-40.6L785.9 178c8.4-15 8.3-32.9-0.4-47.8s-24.2-23.8-41.5-23.8H328.2z" fill="#FFA820" p-id="61062"></path><path d="M632 363l-38.2 73.7c-3.5 6.8 1.4 15 9.1 15h62.6c12.3 0 18.4 14.9 9.6 23.5L534.5 613.9c-5.1 5-13.5-0.4-11-7.1l27.4-73.5c2.4-6.4-2.4-13.3-9.2-13.3h-51c-14.3 0-25.1-13-22.5-27.1L493 360.2c3.1-16.5 17.6-28.5 34.4-28.4l85.9 0.3c15.8 0 26 16.8 18.7 30.9z" fill="#FFE3B4" p-id="61063"></path><path d="M301.5 322.9c-9.9-0.7-17.4-9.2-16.8-19.2l8.2-123.8c0.7-9.9 9.2-17.4 19.2-16.8s17.4 9.2 16.8 19.2l-8.2 123.8c-0.7 10-9.3 17.5-19.2 16.8zM296.9 392.4c-9.9-0.7-17.4-9.2-16.8-19.2l0.7-10.5c0.7-9.9 9.2-17.4 19.2-16.8 9.9 0.7 17.4 9.2 16.8 19.2l-0.7 10.5c-0.7 9.9-9.3 17.4-19.2 16.8z" fill="#FFFFFF" p-id="61064"></path></svg>

                    </div>
                    <div style="color: #df8639; font-size: 18px; font-weight: bold; line-height: 32px; display: flex; flex-direction: column; justify-content: center;">
                        <div>未使用支付宝付款将无法到账</div>
                        <div>温馨提醒:亏损自行承担</div>
                    </div>


                </div>

                <div style="position: absolute; bottom: -94px; left: 0; color: #eee; width: 100%;">
                    <div style="text-align: center; font-size: 16px;">
                        <input type="checkbox" style="" id="today_notify">&nbsp;我已了解,今日不再提醒
                    </div>
                    <div style="display: flex; justify-content: center; margin-top: 10px;">
                        <a style="background: #6EC1C9; color: #eee; padding: 12px 27px; border-radius: 3px; display: flex; justify-content: center; align-items: center; width: 100%; max-width: 330px; font-size: 16px;" onclick="alipay_confirm_click()">确认</a>



                    </div>
                </div>


            </div>
        </div>
    </div>

    <script>
        $(document).ready(function () {
            $(".pop-cpt-bd").click(function (event) {
                event.stopPropagation();
            });
        });
    </script>

</asp:Content>


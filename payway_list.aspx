<%@ Page Title="" Language="C#" MasterPageFile="~/top.master" AutoEventWireup="true" CodeFile="payway_list.aspx.cs" Inherits="order_list" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="Server">
    <script>
        $(function () {
            popTitle('商品回款方式', '<a style="position: absolute; right: 0; height: 100%; width: 50px; top: 0; display: flex; align-items: center; justify-content: center;" onclick="open_payment()"><svg t="1694376580519" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="9630" width="22" height="22"><path d="M736.7 274.9l162.7 162.7L575 762l-176.4 11.3 13.5-173.9z" fill="#03BD61" p-id="9631"></path><path d="M893.7 872.6H130.3c-21.9 0-39.6 17.7-39.6 39.6 0 21.9 17.7 39.6 39.6 39.6h763.4c21.9 0 39.6-17.7 39.6-39.6 0-21.8-17.7-39.6-39.6-39.6zM671 184.2l133.5 133.5L392.3 730 247 739.3l11.1-142.2L671 184.2m0-112L181.4 561.8 161 824.2 427.1 807l489.4-489.4L671 72.2z" fill="#23202D" p-id="9632"></path></svg></a>');
        })
    </script>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="Server">



    <div id="lists"></div>




    <script>
        var payment_id = "";
        function get_list() {

            v3api("lists", { data: { page: 'payment_list', p: 0, limit: 10 } }, function (e) {
                for (var i = 0; i < e.data.list.length; i++) {
                    var obj = e.data.list[i];
                    payment_id = obj.id;
                    var __svg = '<svg t="" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="17672" width="88" height="88"><path d="M984.039024 138.614634H39.960976c-22.478049 0-39.960976 17.482927-39.960976 39.960976v79.921951h1024v-79.921951c0-22.478049-17.482927-39.960976-39.960976-39.960976zM0 845.42439c0 22.478049 17.482927 39.960976 39.960976 39.960976h944.078048c22.478049 0 39.960976-17.482927 39.960976-39.960976V353.404878H0v492.019512z m768-91.160975h-111.141463l33.717073-71.180488h111.141463l-33.717073 71.180488z m152.35122 0h-111.141464l33.717073-71.180488h111.141464l-33.717073 71.180488zM47.453659 448.312195c0-8.741463 7.492683-16.234146 16.234146-16.234146h896.62439c8.741463 0 16.234146 7.492683 16.234146 16.234146v39.960976c0 8.741463-7.492683 16.234146-16.234146 16.234146H63.687805c-8.741463 0-16.234146-7.492683-16.234146-16.234146v-39.960976z" fill="currentColor" p-id="17673" style=""></path></svg>';
                    var __background = 'linear-gradient(45deg, #101D51, #01114A)';

                    switch (obj.type) {
                        case "支付宝":
                        case "支付宝二维码":
                            __svg = '<svg t="" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="" width="88" height="88"><path d="M1024 701.44V197.12C1024 87.04 936.96 0 826.88 0h-629.76C87.04 0 0 87.04 0 197.12v629.76C0 936.96 87.04 1024 197.12 1024h629.76c97.28 0 176.64-69.12 194.56-161.28C970.24 839.68 742.4 742.4 624.64 686.08c-89.6 107.52-184.32 174.08-325.12 174.08s-235.52-87.04-225.28-194.56c7.68-69.12 56.32-184.32 263.68-163.84 110.08 10.24 161.28 30.72 250.88 61.44 23.04-43.52 43.52-89.6 56.32-138.24H248.32v-38.4H445.44v-71.68H204.8V271.36h240.64V168.96s2.56-15.36 20.48-15.36h97.28v117.76h256v43.52h-256v71.68h209.92c-20.48 79.36-48.64 151.04-84.48 212.48 58.88 17.92 335.36 102.4 335.36 102.4zM284.16 791.04c-148.48 0-174.08-94.72-166.4-133.12S168.96 568.32 250.88 568.32c94.72 0 181.76 25.6 284.16 74.24-71.68 92.16-158.72 148.48-250.88 148.48z" fill="currentColor" p-id="7908"></path></svg>';
                            __background = 'linear-gradient(45deg, #556bc5, #2148d3)';
                            obj.bankname = '';
                            break;
                        case "微信二维码":
                            __svg = '<svg t="*************" class="icon" viewBox="0 0 1144 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="6243" width="88" height="88"><path d="M436.314353 632.771765c-68.517647 36.321882-78.667294-20.389647-78.667294-20.389647l-85.835294-190.524236c-33.039059-90.533647 28.581647-40.839529 28.581647-40.839529s52.856471 38.038588 93.003294 61.229176c40.086588 23.190588 85.835294 6.806588 85.835294 6.806589l561.212235-246.362353C936.899765 80.112941 765.891765 0 572.235294 0 256.180706 0 0 213.232941 0 476.310588c0 151.311059 84.811294 285.967059 216.937412 373.248l-23.792941 130.288941s-11.625412 38.038588 28.611764 20.389647c27.437176-12.047059 97.370353-55.115294 138.992941-81.347764 65.445647 21.684706 136.734118 33.731765 211.486118 33.731764 316.024471 0 572.235294-213.232941 572.235294-476.310588 0-76.197647-21.594353-148.178824-59.843764-212.028235-178.808471 102.309647-594.733176 340.118588-648.312471 368.489412z" fill="currentColor" p-id="6244"></path></svg>';
                            __background = 'linear-gradient(45deg, #43C93E, #6cb369)';
                            obj.bankname = '';
                            break;
                        case "OKPAY":
                        case "GOPAY":
                        case "ABPAY":
                            __svg = '<svg t="" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="8043" width="130" height="130" style="opacity: 0.2;position: relative;top: 8px;"><path d="M512 896c200.298667 0 362.666667-152.810667 362.666667-341.333333s19.050667-192.533333-362.666667-192.533334S149.333333 366.144 149.333333 554.666667s162.368 341.333333 362.666667 341.333333z" fill="#FF5C00" p-id="8044"></path><path d="M149.333333 490.666667a362.666667 341.333333 0 1 0 725.333334 0 362.666667 341.333333 0 1 0-725.333334 0Z" fill="#FFCC00" p-id="8045"></path><path d="M820.224 310.698667a332.288 332.288 0 0 1 31.146667 59.349333L404.629333 816.789333a373.12 373.12 0 0 1-64.853333-25.642666z m-189.269333-142.570667c57.621333 18.816 108.629333 50.922667 148.906666 92.416L284.16 756.266667c-46.570667-35.456-83.626667-81.514667-107.008-134.314667z" fill="#FFE3B6" p-id="8046"></path><path d="M192 480a320 288 0 1 0 640 0 320 288 0 1 0-640 0Z" fill="#FF7325" p-id="8047"></path><path d="M213.333333 480a298.666667 266.666667 0 1 0 597.333334 0 298.666667 266.666667 0 1 0-597.333334 0Z" fill="#FFB329" p-id="8048"></path><path d="M808.533333 512c-17.706667 132.181333-143.722667 234.666667-296.533333 234.666667s-278.826667-102.485333-296.533333-234.666667z" fill="#FF9B1A" p-id="8049"></path><path d="M512 213.333333c108.074667 0 202.730667 51.242667 255.168 128H256.853333c52.437333-76.757333 147.093333-128 255.168-128z" fill="#FFCC00" p-id="8050"></path><path d="M587.648 510.037333l94.72-13.930666 0.298667 40.938666c-6.4 25.045333-16.426667 45.973333-30.165334 62.762667a129.92 129.92 0 0 1-51.093333 38.037333c-20.352 8.533333-46.250667 12.821333-77.674667 12.821334-38.144 0-69.290667-5.205333-93.482666-15.637334-24.170667-10.432-45.034667-28.757333-62.592-55.018666-17.557333-26.24-26.325333-59.84-26.325334-100.8 0-54.613333 15.445333-96.554667 46.314667-125.888 30.890667-29.333333 74.56-43.989333 131.050667-43.989334 44.202667 0 78.954667 8.405333 104.256 25.194667 9.962667 6.613333 28.501333 18.346667 55.594666 35.2-0.341333 16.469333-0.085333 30.549333 0.768 42.197333l-95.744 20.053334c-3.349333-10.069333-6.869333-17.408-10.538666-22.058667a61.482667 61.482667 0 0 0-22.250667-18.005333 67.328 67.328 0 0 0-29.44-6.314667c-24.576 0-43.413333 9.301333-56.490667 27.904-9.898667 13.802667-14.848 35.477333-14.848 65.024 0 36.608 5.909333 61.696 17.706667 75.264 11.818667 13.589333 28.416 20.373333 49.792 20.373333 20.736 0 36.416-5.482667 47.04-16.426666 10.602667-10.944 18.304-26.858667 23.104-47.701334z" fill="#FF5C00" p-id="8051"></path><path d="M587.648 467.370667L682.666667 494.378667c-6.4 25.045333-16.426667 45.973333-30.165334 62.762666a129.92 129.92 0 0 1-51.093333 38.037334c-20.352 8.533333-46.250667 12.821333-77.674667 12.821333-38.144 0-69.290667-5.205333-93.482666-15.637333-24.170667-10.432-45.034667-28.757333-62.592-55.018667-17.557333-26.24-26.325333-59.84-26.325334-100.8 0-54.613333 15.445333-96.554667 46.314667-125.888 30.890667-29.333333 74.56-43.989333 131.050667-43.989333 44.202667 0 78.954667 8.405333 104.256 25.194666 25.28 16.810667 44.074667 42.602667 56.362666 77.397334l-95.744 20.053333c-3.349333-10.069333-6.869333-17.408-10.538666-22.058667a61.482667 61.482667 0 0 0-22.250667-18.005333 67.328 67.328 0 0 0-29.44-6.314667c-24.576 0-43.413333 9.301333-56.490667 27.904-9.898667 13.802667-14.848 35.477333-14.848 65.024 0 36.608 5.909333 61.696 17.706667 75.264 11.818667 13.589333 28.416 20.373333 49.792 20.373334 20.736 0 36.416-5.482667 47.04-16.426667 10.602667-10.944 18.304-26.858667 23.104-47.701333z" fill="#FFDC96" p-id="8052"></path><path d="M622.933333 291.861333c18.858667 12.522667 34.112 30.058667 45.738667 52.586667l-34.24 34.197333-50.858667 10.666667c-3.349333-10.069333-6.869333-17.408-10.538666-22.058667a61.482667 61.482667 0 0 0-22.250667-18.005333 67.328 67.328 0 0 0-29.44-6.314667c-24.576 0-43.413333 9.301333-56.490667 27.904-9.898667 13.802667-14.848 35.477333-14.848 65.024 0 36.608 5.909333 61.696 17.706667 75.264 5.525333 6.357333 12.074667 11.221333 19.690667 14.592l-63.637334 63.616c-21.44-10.794667-40.149333-28.117333-56.106666-51.989333-9.92-14.848-17.045333-32.021333-21.333334-51.584l215.893334-215.893333c24.170667 3.84 44.416 11.178667 60.736 21.994666z" fill="#FFECBD" p-id="8053"></path></svg>';
                            __background = 'linear-gradient(45deg, #557dc5, #3cefdf)';
                            obj.bankname = '';
                            obj.name = '<span style="color: #e3ed8c;margin-right: 7px;">[' + obj.type + ']</span>' + obj.name;
                            break;
                        default:
                            break;

                    }


                    switch (obj.type) {
                        case "支付宝二维码":
                        case "微信二维码":
                            obj.bankid = obj.type;
                            break;
                        default:
                            break;

                    }

                    $('#lists').append('<div style="background: ' + __background + '; color: #fff; border-radius: 10px; padding: 18px 22px; margin-top: 20px;position:relative;z-index:1;" onclick="javascript:location.href=\'payway_add.aspx?id=' + obj.id + '\'">        <div style="position: absolute;left: 1px;bottom: -15px;z-index: -1;color: #eeeeee5c;">            ' + __svg + '        </div>                <div style="position: absolute;right: -12px;top: -21px;z-index: -1;background: #eeeeee3d;width: 90px;height: 90px;border-radius:50%;">                   </div>        <div style=" display: flex;">            <div style="">                <strong style="color: #fff; font-size: 18px;">' + obj.name + '</strong>            </div>            <div style="margin-left: auto;">                <span style="color: #fff; border-radius: 18px; padding: 2px 8px; font-size: 13px;">' + obj.bankname + '</span>            </div>        </div>        <div style="color: #fff; font-size: 14px; margin-top: 15px; font-weight: bold;">            ' + obj.bankid + '                   </div>    </div>');
                }

            })
        }
        get_list();

        var open_payment = function () {
            //if (payment_id == "") {
            //    location.href = 'payway_add.aspx';
            //} else {
            //    location.href = 'payway_add.aspx?id=' + payment_id;
            //}
            location.href = 'payway_add.aspx';
        }


        //监听
        subscribeToCustomEvent(function (e) {
            if (e.detail.last_path == "/payway_add.aspx" && e.detail.new_path == "/payway_list.aspx") {
                console.log('页面发生变化');
                $('#lists').html('');
                get_list();
            }
        });
    </script>

</asp:Content>


using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

public partial class order_list : baseClass
{
    public DataTable userdt = new DataTable();
    public DataTable userinfo = new DataTable();
    protected void Page_Load(object sender, EventArgs e)
    {
        List<SqlParameter> pams = new List<SqlParameter>();
        dbClass db = new dbClass();
        pams.Add(new SqlParameter("@id", Request.QueryString["id"] + ""));
        pams.Add(new SqlParameter("@userid", uConfig.p_uid));
        string sql = string.Empty;
        sql = "select * from payment_list with(nolock) where id=@id and userid=@userid";
        sql += " select * from accounts with(nolock) where id=@userid ";
        DataSet ds = db.getDataSet(sql, pams.ToArray());
        userdt = ds.Tables[0];
        userinfo = ds.Tables[1];

    }

    public string getArray(string text, int number)
    {
        string result = string.Empty;
        try
        {
            result = text.Split(' ')[number];
        }
        catch (Exception)
        {
        }
        return result;
    }
}
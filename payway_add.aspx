<%@ Page Title="" Language="C#" MasterPageFile="~/top.master" AutoEventWireup="true" CodeFile="payway_add.aspx.cs" Inherits="order_list" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="Server">
    <script>
        $(function () {
            popTitle('<%=uConfig.gd(userdt,"id") != "" ? "编辑回款方式" : "添加回款方式" %>', '');
        })
    </script>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="Server">



    <div style="background: #dbdef5; color: #3d50df; line-height: 20px; font-size: 12px; padding: 10px; border-radius: 8px; font-weight: bold; display: flex; margin-bottom: 18px;" class="bank_item">

        <div style="margin-right: 10px;">
            <svg t="" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="54328" width="12" height="12">
                <path d="M512 0C229.665391 0 0 229.665391 0 512 0 614.578087 30.230261 713w.594435 87.462957 798.274783 97.792 813.568 118.53913 817.574957 133.832348 807.268174 149.103304 796.93913 153.132522 776.169739 142.803478 760.898783 93.072696 687.282087 66.782609 601.221565 66.782609 512 66.782609 266.50713 266.50713 66.782609 512 66.782609 757.49287 66.782609 957.217391 266.50713 957.217391 512 957.217391 757.49287 757.49287 957.217391 512 957.217391 420.685913 957.217391 332.933565 929.792 258.248348 877.879652 243.044174 867.350261 222.274783 871.067826 211.767652 886.227478 201.238261 901.36487 204.978087 922.178783 220.115478 932.685913 306.064696 992.434087 406.995478 1024 512 1024 794.334609 1024 1024 794.334609 1024 512 1024 229.665391 794.334609 0 512 0ZM512.004452 237.895235C475.118191 237.895235 445.221843 267.791583 445.221843 304.677843 445.221843 341.564104 475.118191 371.460452 512.004452 371.460452 548.890713 371.460452 578.787061 341.564104 578.787061 304.677843 578.787061 267.791583 548.890713 237.895235 512.004452 237.895235ZM512 429.935304C481.257739 429.935304 456.347826 454.845217 456.347826 485.587478L456.347826 752.717913C456.347826 783.460174 481.257739 808.370087 512 808.370087 542.742261 808.370087 567.652174 783.460174 567.652174 752.717913L567.652174 485.587478C567.652174 454.845217 542.742261 429.935304 512 429.935304Z" fill="#3d50df" p-id="54329"></path></svg>
        </div>

        <div>
            因部分银行二类账户存在每日收付金额限制（例如建设银行二类账户每日收付1万元限制），请尽量使用一类收款账户，否则可能造成订单收款失败
       
        </div>
    </div>

    <div style="">

        <div style="position: relative; background: #fff; border-radius: 8px; margin-bottom: 18px; z-index: 1;" onclick="javascript:openButtomPage($('.payway-select').html());">
            <input style="border: 0px; background: none; font-size: 14px; padding: 20px; outline: none; width: 100%; box-sizing: border-box; font-weight: bold; color: #000; z-index: -1; position: relative;" id="type" value="<%=uConfig.gd(userdt,"type") %>" placeholder="请选择收款账户类型" disabled="disabled">
            <a style="position: absolute; top: 22px; right: 22px; display: flex; align-items: center; border-radius: 38px; justify-content: center; cursor: pointer;">
                <svg t="*************" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="23019" width="10" height="10">
                    <path d="M573.056 752l308.8-404.608A76.8 76.8 0 0 0 820.736 224H203.232a76.8 76.8 0 0 0-61.056 123.392l308.8 404.608a76.8 76.8 0 0 0 122.08 0z" fill="#aaa" p-id="23020"></path></svg>
            </a>
        </div>

        <%if (uConfig.gd(userinfo, "secure_password") == "-1")
          {
        %>
        <div style="position: relative; background: #fff; border-radius: 8px; margin-bottom: 18px;">
            <input style="border: 0px; background: #f3f3f3; border-radius: 8px; font-size: 14px; padding: 20px; outline: none; width: 100%; box-sizing: border-box; font-weight: bold; color: #4144a7;" id="secure_password" value="" placeholder="请输入安全码" maxlength="6">
        </div>
        <%
          } %>

        <div style="position: relative; background: #fff; border-radius: 8px; margin-bottom: 18px;">
            <input style="border: 0px; background: none; font-size: 14px; padding: 20px; outline: none; width: 100%; box-sizing: border-box; font-weight: bold; color: #000;" id="name" value="<%=uConfig.gd(userdt,"name") %>" placeholder="请输入姓名">
        </div>

        <div style="position: relative; background: #fff; border-radius: 8px; margin-bottom: 18px; display: flex; justify-content: center;">

            <div id="payimg" style="background: #eee; min-height: 250px; cursor: pointer; display: flex; align-items: center; justify-content: center; color: #aaa; font-size: 17px; max-width: 220px; width: 100%;" onclick="upload_images()">

                <svg t="1694321742065" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="7508" width="32" height="32">
                    <path d="M896 160H128v704h768z m-704 64h640v423.68L706.08 496l-157.12 157.12L352 358.24l-160 240z m0 576v-86.24l160-240 187.04 280.48L701.92 592 832 747.52V800z" fill="#aaaaaa" p-id="7509"></path><path d="M672 448a96 96 0 1 0-96-96 96 96 0 0 0 96 96z m0-128a32 32 0 1 1-32 32 32 32 0 0 1 32-32z" fill="#aaaaaa" p-id="7510"></path></svg>
                <b style="margin-left: 5px;">上传二维码</b>
            </div>

        </div>

        <div style="position: relative; background: #fff; border-radius: 8px; margin-bottom: 18px;">
            <input style="border: 0px; background: none; font-size: 14px; padding: 20px; outline: none; width: 100%; box-sizing: border-box; font-weight: bold; color: #000;" id="bankid" value="<%=uConfig.gd(userdt,"bankid") %>" placeholder="请输入卡号">
        </div>

        <div class="bank_item" style="position: relative; background: #fff; border-radius: 8px; margin-bottom: 18px; cursor: pointer; z-index: 1;" onclick="javascript:openButtomPage($('.khh-select').html());">
            <input style="border: 0px; background: none; font-size: 14px; padding: 20px; outline: none; width: 100%; box-sizing: border-box; font-weight: bold; color: #000; z-index: -1; position: relative;" id="khh" value="<%=getArray(uConfig.gd(userdt,"bankname"),0) %>" placeholder="请输入开户行" disabled="disabled">
            <a style="position: absolute; top: 22px; right: 22px; display: flex; align-items: center; border-radius: 38px; justify-content: center; cursor: pointer;">
                <svg t="*************" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="23019" width="10" height="10">
                    <path d="M573.056 752l308.8-404.608A76.8 76.8 0 0 0 820.736 224H203.232a76.8 76.8 0 0 0-61.056 123.392l308.8 404.608a76.8 76.8 0 0 0 122.08 0z" fill="#aaa" p-id="23020"></path></svg>
            </a>
        </div>

        <div class="bank_item" style="position: relative; background: #fff; border-radius: 8px; margin-bottom: 18px;">
            <input style="border: 0px; background: none; font-size: 14px; padding: 20px; outline: none; width: 100%; box-sizing: border-box; font-weight: bold; color: #000;" id="bankname" value="<%=getArray(uConfig.gd(userdt,"bankname"),1) %>" placeholder="请输入开户行地址">
        </div>

        <div style="position: relative; background: #fff; border-radius: 8px; margin-bottom: 18px;">
            <textarea style="border: 0px; background: none; font-size: 14px; padding: 20px; outline: none; width: 100%; box-sizing: border-box; resize: none; height: 118px; font-weight: bold; color: #000;" id="remark" placeholder="请输入备注信息（选填）"><%=uConfig.gd(userdt,"remark") %></textarea>
        </div>

    </div>

    <div style="position: fixed; left: 0; bottom: 8px; width: 100%; padding: 5px 18px; box-sizing: border-box;">
        <a style="display: block; background: #3838f5; color: #fff; width: 100%; padding: 16px; box-sizing: border-box; border-radius: 10px; cursor: pointer; text-align: center; font-size: 15px; margin-top: 13px;" onclick="add_payment()">确认</a>

    </div>



    <div class="demo-popup-page payway-select" style="display: none;">
        <div style="padding: 18px; font-size: 16px;">

            <h4 style="color: #3C54D1; margin-bottom: 22px;">选择收款账户类型</h4>

            <div style="display: flex; margin-bottom: 30px; align-items: center; font-weight: bold;" onclick="set_payway('银行卡')">
                <svg t="1692522586005" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="18868" width="32" height="32">
                    <path d="M534.493867 78.762667l359.850666 346.9312c26.112 25.088 26.9312 67.754667 1.8432 93.866666L697.105067 724.8896c-25.088 26.094933-67.754667 26.9312-93.8496 1.8432L243.4048 379.818667c-26.094933-25.088-26.9312-67.754667-1.8432-93.8496L440.644267 80.622933c25.088-26.112 66.696533-28.296533 93.8496-1.860266z" fill="#FFD3D7" p-id="18869"></path><path d="M322.935467 155.989333l460.288 195.396267c33.006933 13.994667 49.937067 52.718933 35.2768 87.278933l-111.36 262.3488c-14.011733 32.989867-52.736 49.937067-87.278934 35.259734L159.556267 540.893867c-32.989867-13.994667-49.937067-52.718933-35.259734-87.278934l111.36-262.3488c13.994667-33.006933 54.272-49.271467 87.278934-35.2768z" fill="#FB560A" p-id="18870"></path><path d="M150.186667 708.266667h723.626666l92.16 128c34.133333 47.786667 15.36 85.333333-44.373333 85.333333H93.866667C35.84 921.6 17.066667 884.053333 51.2 836.266667l98.986667-128z" fill="#DAE9FF" p-id="18871"></path><path d="M225.28 349.866667h597.333333c59.733333 0 107.52 47.786667 107.52 107.52v281.6c0 59.733333-47.786667 107.52-107.52 107.52h-597.333333c-59.733333 0-107.52-47.786667-107.52-107.52V457.386667c0-59.733333 47.786667-107.52 107.52-107.52z" fill="#DAE9FF" p-id="18872"></path><path d="M208.213333 349.866667h558.08c59.733333 0 107.52 47.786667 107.52 107.52v281.6c0 59.733333-47.786667 107.52-107.52 107.52H208.213333c-59.733333 0-107.52-47.786667-107.52-107.52V457.386667c0-59.733333 47.786667-107.52 107.52-107.52z" fill="#3889FF" p-id="18873"></path><path d="M605.866667 759.466667m-25.6 0a25.6 25.6 0 1 0 51.2 0 25.6 25.6 0 1 0-51.2 0Z" fill="#FFFFFF" p-id="18874"></path><path d="M691.2 759.466667m-25.6 0a25.6 25.6 0 1 0 51.2 0 25.6 25.6 0 1 0-51.2 0Z" fill="#FFFFFF" p-id="18875"></path><path d="M776.533333 759.466667m-25.6 0a25.6 25.6 0 1 0 51.2 0 25.6 25.6 0 1 0-51.2 0Z" fill="#FFFFFF" p-id="18876"></path><path d="M233.813333 460.8c-15.36 0-29.013333 11.946667-29.013333 27.306667 0 15.36 11.946667 27.306667 29.013333 27.306666h527.36c15.36 0 29.013333-11.946667 29.013334-27.306666 0-15.36-11.946667-27.306667-29.013334-27.306667H233.813333z" fill="#DAE9FF" p-id="18877"></path><path d="M216.746667 443.733333c-15.36 0-29.013333 11.946667-29.013334 27.306667 0 15.36 11.946667 27.306667 29.013334 27.306667h527.36c15.36 0 29.013333-15.36 29.013333-27.306667s-13.653333-27.306667-29.013333-27.306667H216.746667z" fill="#FFFFFF" p-id="18878"></path></svg>&nbsp;&nbsp;&nbsp;银行卡
           
            </div>
            <div style="display: flex; margin-bottom: 30px; align-items: center; font-weight: bold;" onclick="set_payway('支付宝')">
                <svg t="1692522651546" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="20838" width="32" height="32">
                    <path d="M230.4 576.512c-12.288 9.728-25.088 24.064-28.672 41.984-5.12 24.576-1.024 55.296 22.528 79.872 28.672 29.184 72.704 37.376 91.648 38.912 51.2 3.584 105.984-22.016 147.456-50.688 16.384-11.264 44.032-34.304 70.144-69.632-59.392-30.72-133.632-64.512-212.48-61.44-40.448 1.536-69.632 9.728-90.624 20.992z m752.64 135.68c26.112-61.44 40.96-129.024 40.96-200.192C1024 229.888 794.112 0 512 0S0 229.888 0 512s229.888 512 512 512c170.496 0 321.536-83.968 414.72-211.968-88.064-43.52-232.96-115.712-322.56-159.232-42.496 48.64-105.472 97.28-176.64 118.272-44.544 13.312-84.992 18.432-126.976 9.728-41.984-8.704-72.704-28.16-90.624-47.616-9.216-10.24-19.456-22.528-27.136-37.888 0.512 1.024 1.024 2.048 1.024 3.072 0 0-4.608-7.68-7.68-19.456-1.536-6.144-3.072-11.776-3.584-17.92-0.512-4.096-0.512-8.704 0-12.8-0.512-7.68 0-15.872 1.536-24.064 4.096-20.48 12.8-44.032 35.328-65.536 49.152-48.128 114.688-50.688 148.992-50.176 50.176 0.512 138.24 22.528 211.968 48.64 20.48-43.52 33.792-90.112 41.984-121.344h-307.2v-33.28h157.696v-66.56H272.384V302.08h190.464V235.52c0-9.216 2.048-16.384 16.384-16.384h74.752V302.08h207.36v33.28h-207.36v66.56h165.888s-16.896 92.672-68.608 184.32c115.2 40.96 278.016 104.448 331.776 125.952z" fill="#06B4FD" p-id="20839"></path></svg>&nbsp;&nbsp;&nbsp;支付宝
           
            </div>

            <div style="display: flex; margin-bottom: 30px; align-items: center; font-weight: bold;" onclick="set_payway('OKPAY')">
                <img src="../static/images/pay/OKPAY.png" width="32" height="32">&nbsp;&nbsp;&nbsp;OKPAY
            </div>
            <div style="display: flex; margin-bottom: 30px; align-items: center; font-weight: bold;" onclick="set_payway('GOPAY')">
                <img src="../static/images/pay/GOPAY.png" width="32" height="32">&nbsp;&nbsp;&nbsp;GOPAY
           
            </div>
            <div style="display: flex; margin-bottom: 30px; align-items: center; font-weight: bold;" onclick="set_payway('ABPAY')">
                <img src="../static/images/pay/ABPAY.png" width="32" height="32">&nbsp;&nbsp;&nbsp;ABPAY
            </div>
            <%--            
            <div style="display: flex; margin-bottom: 30px; align-items: center; font-weight: bold;" onclick="set_payway('微信')">
                <svg t="1692522671251" class="icon" viewBox="0 0 1144 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="21909" width="32" height="32">
                    <path d="M436.314353 632.771765c-68.517647 36.321882-78.667294-20.389647-78.667294-20.389647l-85.835294-190.524236c-33.039059-90.533647 28.581647-40.839529 28.581647-40.839529s52.856471 38.038588 93.003294 61.229176c40.086588 23.190588 85.835294 6.806588 85.835294 6.806589l561.212235-246.362353C936.899765 80.112941 765.891765 0 572.235294 0 256.180706 0 0 213.232941 0 476.310588c0 151.311059 84.811294 285.967059 216.937412 373.248l-23.792941 130.288941s-11.625412 38.038588 28.611764 20.389647c27.437176-12.047059 97.370353-55.115294 138.992941-81.347764 65.445647 21.684706 136.734118 33.731765 211.486118 33.731764 316.024471 0 572.235294-213.232941 572.235294-476.310588 0-76.197647-21.594353-148.178824-59.843764-212.028235-178.808471 102.309647-594.733176 340.118588-648.312471 368.489412z" fill="#43C93E" p-id="21910"></path></svg>&nbsp;&nbsp;&nbsp;微信
           
            </div>--%>
           <%--   <div style="display: flex; margin-bottom: 30px; align-items: center; font-weight: bold;" onclick="set_payway('支付宝')">
                <img src="../static/images/pay/支付宝.png" width="32" height="32">&nbsp;&nbsp;&nbsp;支付宝
            </div>
          <div style="display: flex; margin-bottom: 30px; align-items: center; font-weight: bold;" onclick="set_payway('支付宝二维码')">
                <img src="../static/images/pay/支付宝二维码.png" width="32" height="32">&nbsp;&nbsp;&nbsp;支付宝二维码
            </div>
            <div style="display: flex; margin-bottom: 30px; align-items: center; font-weight: bold;" onclick="set_payway('微信二维码')">
                <img src="../static/images/pay/微信二维码.png" width="32" height="32">&nbsp;&nbsp;&nbsp;微信二维码
            </div>--%>
        </div>
    </div>


    <div class="demo-popup-page khh-select" style="display: none;">
        <div style="padding: 18px; font-size: 16px; color: #333;">

            <h4 style="color: #3C54D1; margin-bottom: 22px;">选择开户行</h4>


            <div style="position: relative; background: #f5f5f5; border-radius: 8px; margin-bottom: 12px; display: ;">
                <input style="border: 0px; background: none; font-size: 22px; padding: 13px; outline: none; width: 100%; box-sizing: border-box;" class="search_content" value="" placeholder="请输入要搜索的银行" onkeyup="search_khh()">
                <a style="position: absolute; top: 8px; right: 5px; background: #25252b; color: #fff; display: flex; align-items: center; width: 83px; height: 37px; border-radius: 38px; justify-content: center; cursor: pointer;" onclick="search_khh();">搜索</a>
            </div>

            <div class="banklist" style="height: 399px; overflow-y: auto;">
            </div>

        </div>
    </div>



    <script>
        var bank_list = [{ value: 'ICBC', text: '中国工商银行' }, { value: 'ABC', text: '中国农业银行' }, { value: 'BOC', text: '中国银行' }, { value: 'CCB', text: '中国建设银行' }, { value: 'PSBC', text: '中国邮政储蓄银行' }, { value: 'COMM', text: '交通银行' }, { value: 'CMB', text: '招商银行' }, { value: 'SPDB', text: '上海浦东发展银行' }, { value: 'CIB', text: '兴业银行' }, { value: 'HXBANK', text: '华夏银行' }, { value: 'GDB', text: '广东发展银行' }, { value: 'CMBC', text: '中国民生银行' }, { value: 'CITIC', text: '中信银行' }, { value: 'CEB', text: '中国光大银行' }, { value: 'EGBANK', text: '恒丰银行' }, { value: 'CZBANK', text: '浙商银行' }, { value: 'BOHAIB', text: '渤海银行' }, { value: 'SPABANK', text: '平安银行' }, { value: 'SHRCB', text: '上海农村商业银行' }, { value: 'YXCCB', text: '玉溪市商业银行' }, { value: 'YDRCB', text: '尧都农商行' }, { value: 'BJBANK', text: '北京银行' }, { value: 'SHBANK', text: '上海银行' }, { value: 'JSBANK', text: '江苏银行' }, { value: 'HZCB', text: '杭州银行' }, { value: 'NJCB', text: '南京银行' }, { value: 'NBBANK', text: '宁波银行' }, { value: 'HSBANK', text: '徽商银行' }, { value: 'CSCB', text: '长沙银行' }, { value: 'CDCB', text: '成都银行' }, { value: 'CQBANK', text: '重庆银行' }, { value: 'DLB', text: '大连银行' }, { value: 'NCB', text: '南昌银行' }, { value: 'FJHXBC', text: '福建海峡银行' }, { value: 'HKB', text: '汉口银行' }, { value: 'WZCB', text: '温州银行' }, { value: 'QDCCB', text: '青岛银行' }, { value: 'TZCB', text: '台州银行' }, { value: 'JXBANK', text: '嘉兴银行' }, { value: 'CSRCB', text: '常熟农村商业银行' }, { value: 'NHB', text: '南海农村信用联社' }, { value: 'CZRCB', text: '常州农村信用联社' }, { value: 'H3CB', text: '内蒙古银行' }, { value: 'SXCB', text: '绍兴银行' }, { value: 'SDEB', text: '顺德农商银行' }, { value: 'WJRCB', text: '吴江农商银行' }, { value: 'ZBCB', text: '齐商银行' }, { value: 'GYCB', text: '贵阳市商业银行' }, { value: 'ZYCBANK', text: '遵义市商业银行' }, { value: 'HZCCB', text: '湖州市商业银行' }, { value: 'DAQINGB', text: '龙江银行' }, { value: 'JINCHB', text: '晋城银行JCBANK' }, { value: 'ZJTLCB', text: '浙江泰隆商业银行' }, { value: 'GDRCC', text: '广东省农村信用社联合社' }, { value: 'DRCBCL', text: '东莞农村商业银行' }, { value: 'MTBANK', text: '浙江民泰商业银行' }, { value: 'GCB', text: '广州银行' }, { value: 'LYCB', text: '辽阳市商业银行' }, { value: 'JSRCU', text: '江苏省农村信用联合社' }, { value: 'LANGFB', text: '廊坊银行' }, { value: 'CZCB', text: '浙江稠州商业银行' }, { value: 'DYCB', text: '德阳商业银行' }, { value: 'JZBANK', text: '晋中市商业银行' }, { value: 'BOSZ', text: '苏州银行' }, { value: 'GLBANK', text: '桂林银行' }, { value: 'URMQCCB', text: '乌鲁木齐市商业银行' }, { value: 'CDRCB', text: '成都农商银行' }, { value: 'ZRCBANK', text: '张家港农村商业银行' }, { value: 'BOD', text: '东莞银行' }, { value: 'LSBANK', text: '莱商银行' }, { value: 'BJRCB', text: '北京农村商业银行' }, { value: 'TRCB', text: '天津农商银行' }, { value: 'SRBANK', text: '上饶银行' }, { value: 'FDB', text: '富滇银行' }, { value: 'CRCBANK', text: '重庆农村商业银行' }, { value: 'ASCB', text: '鞍山银行' }, { value: 'NXBANK', text: '宁夏银行' }, { value: 'BHB', text: '河北银行' }, { value: 'HRXJB', text: '华融湘江银行' }, { value: 'ZGCCB', text: '自贡市商业银行' }, { value: 'YNRCC', text: '云南省农村信用社' }, { value: 'JLBANK', text: '吉林银行' }, { value: 'DYCCB', text: '东营市商业银行' }, { value: 'KLB', text: '昆仑银行' }, { value: 'ORBANK', text: '鄂尔多斯银行' }, { value: 'XTB', text: '邢台银行' }, { value: 'JSB', text: '晋商银行' }, { value: 'TCCB', text: '天津银行' }, { value: 'BOYK', text: '营口银行' }, { value: 'JLRCU', text: '吉林农信' }, { value: 'SDRCU', text: '山东农信' }, { value: 'XABANK', text: '西安银行' }, { value: 'HBRCU', text: '河北省农村信用社' }, { value: 'NXRCU', text: '宁夏黄河农村商业银行' }, { value: 'GZRCU', text: '贵州省农村信用社' }, { value: 'FXCB', text: '阜新银行' }, { value: 'HBHSBANK', text: '湖北银行黄石分行' }, { value: 'ZJNX', text: '浙江省农村信用社联合社' }, { value: 'XXBANK', text: '新乡银行' }, { value: 'HBYCBANK', text: '湖北银行宜昌分行' }, { value: 'LSCCB', text: '乐山市商业银行' }, { value: 'TCRCB', text: '江苏太仓农村商业银行' }, { value: 'BZMD', text: '驻马店银行' }, { value: 'GZB', text: '赣州银行' }, { value: 'WRCB', text: '无锡农村商业银行' }, { value: 'BGB', text: '广西北部湾银行' }, { value: 'GRCB', text: '广州农商银行' }, { value: 'JRCB', text: '江苏江阴农村商业银行' }, { value: 'BOP', text: '平顶山银行' }, { value: 'TACCB', text: '泰安市商业银行' }, { value: 'CGNB', text: '南充市商业银行' }, { value: 'CCQTGB', text: '重庆三峡银行' }, { value: 'XLBANK', text: '中山小榄村镇银行' }, { value: 'HDBANK', text: '邯郸银行' }, { value: 'KORLABANK', text: '库尔勒市商业银行' }, { value: 'BOJZ', text: '锦州银行' }, { value: 'QLBANK', text: '齐鲁银行' }, { value: 'BOQH', text: '青海银行' }, { value: 'YQCCB', text: '阳泉银行' }, { value: 'SJBANK', text: '盛京银行' }, { value: 'FSCB', text: '抚顺银行' }, { value: 'ZZBANK', text: '郑州银行' }, { value: 'SRCB', text: '深圳农村商业银行' }, { value: 'BANKWF', text: '潍坊银行' }, { value: 'JJBANK', text: '九江银行' }, { value: 'JXRCU', text: '江西省农村信用' }, { value: 'HNRCU', text: '河南省农村信用' }, { value: 'GSRCU', text: '甘肃省农村信用' }, { value: 'SCRCU', text: '四川省农村信用' }, { value: 'GXRCU', text: '广西省农村信用' }, { value: 'SXRCCU', text: '陕西信合' }, { value: 'WHRCB', text: '武汉农村商业银行' }, { value: 'YBCCB', text: '宜宾市商业银行' }, { value: 'KSRB', text: '昆山农村商业银行' }, { value: 'SZSBK', text: '石嘴山银行' }, { value: 'HSBK', text: '衡水银行' }, { value: 'XYBANK', text: '信阳银行' }, { value: 'NBYZ', text: '鄞州银行' }, { value: 'ZJKCCB', text: '张家口市商业银行' }, { value: 'XCYH', text: '许昌银行' }, { value: 'JNBANK', text: '济宁银行' }, { value: 'CBKF', text: '开封市商业银行' }, { value: 'WHCCB', text: '威海市商业银行' }, { value: 'HBC', text: '湖北银行' }, { value: 'BOCD', text: '承德银行' }, { value: 'BODD', text: '丹东银行' }, { value: 'JHBANK', text: '金华银行' }, { value: 'BOCY', text: '朝阳银行' }, { value: 'LSBC', text: '临商银行' }, { value: 'BSB', text: '包商银行' }, { value: 'LZYH', text: '兰州银行' }, { value: 'BOZK', text: '周口银行' }, { value: 'DZBANK', text: '德州银行' }, { value: 'SCCB', text: '三门峡银行' }, { value: 'AYCB', text: '安阳银行' }, { value: 'ARCU', text: '安徽省农村信用社' }, { value: 'HURCB', text: '湖北省农村信用社' }, { value: 'HNRCC', text: '湖南省农村信用社' }, { value: 'NYNB', text: '广东南粤银行' }, { value: 'LYBANK', text: '洛阳银行' }, { value: 'NHQS', text: '农信银清算中心' }, { value: 'CBBQS', text: '城市商业银行资金清算中心' }, { value: 'CDB', text: '国家开发银行' }];

        for (var i = 0; i < bank_list.length; i++) {
            $('.banklist').append('<div style="display: flex; padding-bottom: 30px; align-items: center; font-weight: bold;" onclick="set_kkh(\'' + bank_list[i].text + '\')">' + bank_list[i].text + '</div>');
        }

        var search_khh = function () {
            $('#popupContents .banklist').html('');

            var search_number = 0;
            var khh = $('#popupContents .search_content').val();
            console.log('kkh', khh);
            for (var i = 0; i < bank_list.length; i++) {
                if (bank_list[i].text.indexOf(khh) != -1 || khh == "") {
                    search_number++;
                    $('#popupContents .banklist').append('<div style="display: flex; padding-bottom: 30px; align-items: center; font-weight: bold;" onclick="set_kkh(\'' + bank_list[i].text + '\')">' + bank_list[i].text + '</div>');
                }
            }

            if (search_number == 0) {
                $('#popupContents .banklist').html('<div style="display: flex;align-items: center; font-weight: bold; background: #e8eff9bd; padding: 15px; border-radius: 9px; color: #9892ef;cursor: pointer;    font-size: 13px;" onclick="set_kkh(\'' + khh + '\')">未搜索到<span style="color: #000;"> \'' + khh + '\' </span>点击添加    </div>');
            }


        }

        var set_kkh = function (name) {
            $('#khh').val(name);
            closePopup();
        }


        var update_id = '<%=uConfig.gd(userdt,"id") %>';

        var set_payway = function (name) {
            v3api("get_payway", {
                data: {
                    type: name
                }
            }, function (e) {


                set_paywayName(name);

                update_id = e.update_id;

                $('#name').val(e.name)
                $('#bankid').val(e.bankid);
                $('#khh').val(e.khh);
                $('#bankname').val(e.bankname);
                $('#remark').val(e.remark);

                closePopup();

            })
        }

        var set_paywayName = function (name) {
            $('#type').val(name);
            $('#bankid').closest('div').show();
            $('#payimg').closest('div').hide();
            switch (name) {
                case "支付宝二维码":
                case "微信二维码":
                    $('.bank_item').hide();
                    $('#payimg').closest('div').show();
                    $('#bankid').closest('div').hide();


                    $('#payimg').html('<img style="width:100%;max-height:100%;" src="../images/upload/file_upload/' + $('#bankid').val() + '">');

                    break;
                case "银行卡":
                    $('.bank_item').show();
                    $('#bankid').attr("placeholder", "请输入卡号");
                    break;
                case "OKPAY":
                case "GOPAY":
                case "ABPAY":
                    //$('#name').closest('div').hide();
                    $('.bank_item').hide();
                    $('#bankid').attr("placeholder", "请输入" + name + "钱包地址");
                    break;
                default:
                    $('.bank_item').hide();
                    $('#bankid').attr("placeholder", "请输入" + name + "账号");
                    break;

            }
        }

        set_paywayName($('#type').val());


        var needpaypwd = '<%=uConfig.gd(userinfo, "secure_password") == "-1" ? 0 : 1 %>'

        var add_payment = function () {

            if (needpaypwd == "1") {
                security_password(function (e) {
                    __action(e);
                })
            } else {
                __action({ password: '' });
            }
        }

        var __action = function (e) {

            var secure_password = '';
            try {
                secure_password = document.getElementById('secure_password').value
            } catch (e) {

            }

            v3api("payment_add", {


                data: {
                    paypwd: e.password,
                    id: update_id,
                    type: document.getElementById('type').value,
                    name: document.getElementById('name').value,
                    bankname: document.getElementById('khh').value + ' ' + document.getElementById('bankname').value,
                    bankid: document.getElementById('bankid').value,
                    remark: document.getElementById('remark').value,
                    secure_password: secure_password
                }
            }, function () {
                history.go(-1);
            })
        }



        var upload_images = function (accept) {
            file_upload(function (e) {
                if (e.success) {
                    $('#payimg').html('<img style="width:100%;max-height:100%;" src="' + e.imgurl + '">');
                    var _img = e.imgurl[0].split('/');
                    var imgid = _img[_img.length - 1];
                    console.log('imgid', imgid);
                    //if (imgid.indexOf('.') != -1) {
                    //    imgid = imgid.substring(0, imgid.indexOf('.'));
                    //}

                    $('#bankid').val(imgid);
                }

            }, accept)
        }
    </script>

</asp:Content>


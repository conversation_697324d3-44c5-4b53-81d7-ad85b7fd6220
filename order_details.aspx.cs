using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

public partial class order_list : baseClass
{
    public DataTable userdt = new DataTable();
    protected void Page_Load(object sender, EventArgs e)
    {
        List<SqlParameter> pams = new List<SqlParameter>();
        dbClass db = new dbClass();
        pams.Add(new SqlParameter("@userid", uConfig.p_uid));
        pams.Add(new SqlParameter("@id", Request.QueryString["id"] + ""));
        userdt = db.getDataTable("select o.* from rob_orders r with(nolock) left join order_list o on r.fid=o.id where r.id=@id and r.userid=@userid ", pams.ToArray());
    }
}
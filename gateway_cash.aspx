<%@ Page Title="" Language="C#" MasterPageFile="~/top.master" AutoEventWireup="true" CodeFile="gateway_cash.aspx.cs" Inherits="order_list" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="Server">
    <script>
        $(function () {
            popTitle('波币提现', '');
        })
    </script>

    <style>
        .top-title {
            background: none;
        }
    </style>
    <style>
        body {
            background: linear-gradient(183deg, #CDE9FE, #F6F6F6, #F6F6F6, #F6F6F6);
            background-repeat: no-repeat;
            height: 100vh;
        }

        .select_tab {
            color: #000;
            box-sizing: border-box;
            padding: 12px 20px;
            text-align: center;
            font-weight: bold;
            font-size: 13px;
            background: #eee;
            border-radius: 12px;
            width: 50%;
            margin: 0 10px;
        }

            .select_tab.activity {
                background: #3c3351;
                color: #fff;
                transition: all 0.2s;
            }

        .theme-green .back-bar .selected-bar {
            background-color: #3838f5!important;
            background-image: linear-gradient(to bottom, #4747ff, #3838f5)!important;
        }

        .slider-container {
            margin-bottom: 38px;
            margin-top: 52px;
        }
    </style>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="Server">

    <div style="background: #fff; border-radius: 8px; padding: 18px; font-size: 14px; margin-top: 20px;">

        <div style="text-align: left; margin-bottom: 25px; font-size: 15px; display: flex; align-items: center;">
            提现金额
       
        </div>


        <div style="position: relative; /* background: #f5f5f5; */border-radius: 8px; margin-bottom: 12px; display: ;">

            <input style="border: 0px; background: none; font-size: 22px; padding: 13px 0; outline: none; width: 100%; box-sizing: border-box; text-align: left; border-bottom: 1px solid #eee;" id="sell_number" placeholder="单笔最多可提现2000">
            <a style="position: absolute; top: 8px; right: 5px; color: #fff; display: flex; align-items: center; /* width: 83px; */height: 37px; border-radius: 38px; justify-content: center; cursor: pointer;">
                <img src="/static/images/coin.png" alt="" height="15" width="15" style="margin-left: 5px;">
            </a>
        </div>





        <div style="display: flex;">
            <input placeholder="添加备注（50字以内）" style="border: 0px; background: none; outline: none; width: 100%; font-size: 15px;" />
        </div>

    </div>
    <div style="color: #7a7b7c; font-size: 12px; margin: 15px 12px;">
        资金提现如有问题请联系客服
   
    </div>


    <a style="display: block; background: #3838f5; color: #fff; width: 100%; padding: 16px; box-sizing: border-box; border-radius: 10px; cursor: pointer; text-align: center; font-size: 14px; margin-top: 50px; text-decoration: none;" onclick="new_advert()" class="create_button">确认提现</a>





    <div class="demo-popup-page payway-select" style="display: none;">
        <div style="padding: 18px; font-size: 13px;">

            <h4 style="color: #3C54D1; margin-bottom: 22px; font-size: 16px; display: flex;">选择收款账户
                <div style="margin-left: auto;" onclick="location.href='payway_add.aspx'">
                    <svg t="1692428311309" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="8668" width="22" height="22">
                        <path d="M642.8 548.2H383c-19.3 0-35-15.7-35-35s15.7-35 35-35h259.8c19.3 0 35 15.7 35 35s-15.7 35-35 35z" fill="#4195F9" p-id="8669"></path><path d="M512.9 678.1c-19.3 0-35-15.7-35-35V383.3c0-19.3 15.7-35 35-35s35 15.7 35 35v259.8c0 19.3-15.6 35-35 35z" fill="#4195F9" p-id="8670"></path><path d="M512.9 902.7c-52.6 0-103.6-10.3-151.6-30.6-46.4-19.6-88-47.7-123.8-83.5s-63.8-77.4-83.5-123.8c-20.3-48-30.6-99.1-30.6-151.6 0-52.6 10.3-103.6 30.6-151.6 19.6-46.4 47.7-88 83.5-123.8s77.4-63.8 123.8-83.5c48-20.3 99.1-30.6 151.6-30.6s103.6 10.3 151.6 30.6c46.4 19.6 88 47.7 123.8 83.5s63.8 77.4 83.5 123.8c20.3 48 30.6 99.1 30.6 151.6 0 52.6-10.3 103.6-30.6 151.6-19.6 46.4-47.7 88-83.5 123.8s-77.4 63.8-123.8 83.5c-48 20.3-99 30.6-151.6 30.6z m0-709c-176.2 0-319.5 143.3-319.5 319.5s143.3 319.5 319.5 319.5 319.5-143.3 319.5-319.5c0-176.1-143.3-319.5-319.5-319.5z" p-id="8671"></path></svg>
                </div>
            </h4>

            <div id="lists" style="max-height: 500px; overflow-y: auto;"></div>

        </div>
    </div>


    <script>
        var gateway_network = "波币_代付";

        var new_advert = function () {
            //security_password(function (e) {

            v3api("new_gatewayOrder", {
                error: 1,
                data: {
                    //paypwd: e.password,
                    amount: $('#sell_number').val(),
                    gateway_network: gateway_network
                }
            }, function (e) {

                if (e.code != 1) {
                    if (e.msg.indexOf("波币余额不足") != -1) {
                        show_success();
                    } else {
                        tp(e.msg);
                    }
                    return;
                }
                if (e.payurl != "") {
                    success_pay(e.orderId);
                    //location.href = e.payurl;
                    openNewWindow(e.payurl);

                } else {
                    location.href = 'gateway_details.aspx?orderId=' + e.orderId;
                }
            })
            //})
        }

        var success_pay = function (orderId) {
            //security_password(function (e) {

            v3api("check_gatewayPay", {
                error: 1,
                data: {
                    //paypwd: e.password,
                    orderId: orderId
                }
            }, function (e) {
                if (e.msg == "未支付") {
                    setTimeout(function () {
                        success_pay(orderId);
                    }, 5000);
                } else if (e.msg == "支付成功") {
                    tp(e.msg);
                    location.href = 'gateway_details.aspx?orderId=' + orderId;
                } else {
                    tp(e.msg);
                }
            })
            //})
        }
    </script>










    <div class=" pg-tips" style="display: none;">
        <div style="width: 100vw; height: 100vh; position: fixed; top: 0; left: 0; background: #000000b5; z-index: 9991;"></div>

        <div class="password-content" style="background: linear-gradient(187deg, #D0EBF9, #ffffff); padding: 8px; box-sizing: border-box; width: 95%; max-width: 380px; position: relative;">

            <h3 style="text-align: center; margin: 0; padding: 13px; font-size: 26px; color: #222; text-shadow: 2px 3px 10px #dfdbdb;">温馨提示</h3>


            <div class="tip-content" style="color: #2a2b2c; padding: 30px 18px; text-align: center;">
                波币余额不足，是否前往购买？
            </div>

            <div class="button_list">
                <div style="display: flex; padding: 16px 22px;">

                    <div style="width: 50%; text-align: center;">
                        <a style="color: #605D54; border: 1px solid #ababab; display: inline-block; width: 130px; height: 39px; line-height: 39px; border-radius: 24px; cursor: pointer; font-weight: bold;" onclick="javascript:$('.pg-tips').hide();">取消</a>
                    </div>
                    <div style="width: 50%; text-align: center;">
                        <a class="confirm_password_button" onclick="tip_success()" style="background: #2E3847; color: #d5c7c2; font-weight: bold; border-radius: 24px;">确定</a>
                    </div>

                </div>
            </div>

        </div>


    </div>

    <script>
        var show_success = function () {
            $('.pg-tips').show();
        }
        var tip_success = function () {
            $('.pg-tips').hide();

            v3api("get_bobi_transaction", {
                data: {
                }
            }, function (e) {
                location.href = e.login_url;
            })

        }
    </script>

</asp:Content>


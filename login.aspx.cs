using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Data;

public partial class login : globalClass
{
    public Dictionary<string, object> pmlist = new Dictionary<string, object>();
    protected void Page_Load(object sender, EventArgs e)
    {

        DataTable dt = selectDateTable(chelper.gdt("group_list"), "anony_visitor=1");
        pmlist["group_avatar"] = "";
        if (dt.Rows.Count > 0)
        {
            pmlist["group_avatar"] = dt.Rows[0]["avatar"] + "";
        }
    }
}
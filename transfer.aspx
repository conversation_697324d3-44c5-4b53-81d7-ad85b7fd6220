<%@ Page Title="" Language="C#" MasterPageFile="~/top.master" AutoEventWireup="true" CodeFile="transfer.aspx.cs" Inherits="order_list" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="Server">
    <script>
        $(function () {
            popTitle('转账', '<a style="position: absolute; right: 0; height: 100%; width: 50px; top: 0; display: flex; align-items: center; justify-content: center;" href="transaction_list.aspx?type=转账"><svg t="1692465228343" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="26478" width="28" height="28"><path d="M512 512m-512 0a512 512 0 1 0 1024 0 512 512 0 1 0-1024 0Z" fill="#3838f5" p-id="26479"></path><path d="M716.731364 238.0814H307.26683c-19.711273 0-35.840813 16.127734-35.840813 35.840813v476.157381c0 19.713079 16.12954 35.840813 35.840813 35.840812h409.464534c19.711273 0 35.840813-16.127734 35.840812-35.840812V273.920406c0-19.713079-16.127734-35.839007-35.840812-35.839006z m-339.422956 78.156922H660.137089c11.263585 0 20.480722 7.828155 20.480723 17.397506 0 9.567544-9.217138 17.3957-20.480723 17.3957H377.308408c-11.263585 0-20.480722-7.828155-20.480722-17.3957 0-9.569351 9.215332-17.397506 20.480722-17.397506z m301.486928 96.883806c0 9.569351-9.217138 17.399312-20.480722 17.399312H378.24403c-11.263585 0-20.480722-7.829961-20.480722-17.399312s9.217138-17.399312 20.480722-17.399312h280.070584c11.263585 0 20.480722 7.829961 20.480722 17.399312z m-321.032028 77.147246c0-9.569351 9.217138-17.399312 20.480722-17.399312h98.372131c11.263585 0 20.480722 7.829961 20.480722 17.399312 0 9.567544-9.217138 17.397506-20.480722 17.397506h-98.372131c-11.263585-0.001806-20.480722-7.831768-20.480722-17.397506z m173.897391 204.287033c-18.475819 6.856409-36.964281 13.705593-55.53583 20.314551-6.9991 2.494389-11.191336 0.986196-7.961815-7.562641 7.127342-18.864156 14.272746-37.726506 21.627671-56.502158 2.483552-6.350668 6.852797-8.960655 12.55142-3.424592 11.514649 11.184111 22.799908 22.60303 34.061686 34.040012 1.497356 1.526255 2.27403 3.767774 2.992906 5.005035-0.827249 5.364472-4.315058 6.856409-7.736038 8.129793z m126.229453-122.947552c-31.709989 31.58536-63.241162 63.344117-94.905995 94.9656-2.209006 2.201782-5.026709 3.803898-6.84196 5.149532-4.790094 0.211328-6.950332-2.423947-9.193657-4.641984-10.174434-10.047999-20.095998-20.372349-30.429379-30.254176-6.224232-5.956912-5.033934-10.687401 0.559928-16.254169a17171.620059 17171.620059 0 0 0 94.80846-95.072168c5.581218-5.644436 10.149147-6.854603 16.116896-0.541865 9.822221 10.387568 19.974981 20.489754 30.302944 30.373386 6.161015 5.902725 5.200106 10.680176-0.417237 16.275844z m24.620578-25.568843c-5.438527 3.514903-8.615667 0.825442-11.958979-2.45104-10.210559-10.006456-20.04723-20.412086-30.525109-30.133159-6.926852-6.415692-5.732941-11.352089 0.648433-17.052518 6.881696-6.151983 12.887376-13.555677 21.838999-12.847639 12.423177 0.149916 33.456603 18.168762 36.724054 30.095229 2.94775 10.784936-4.520967 24.485111-16.727398 32.389127z" fill="#FFFFFF" p-id="26480"></path></svg></a>');
        })
    </script>

    <style>
        .info_title {
            color: #000;
            font-size: 14px;
            font-weight: bold;
            display: flex;
            align-items: center;
            margin: 10px 0;
            margin-top: 18px;
        }

            .info_title i {
                display: inline-block;
                background: #2A3EE8;
                width: 6px;
                height: 6px;
                border-radius: 50%;
            }
    </style>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="Server">


    <div style="background: #fff; border-radius: 8px; padding: 18px; font-size: 14px;">


        <div class="info_title">
            <i></i>&nbsp;&nbsp;对方账号
        </div>
        <div style="position: relative; background: #f5f5f5; border-radius: 8px; margin-bottom: 12px;">
            <input style="border: 0px; background: none; font-size: 16px; padding: 13px; outline: none;" id="uid"  placeholder="请输入对方账号/手机号" value="<%=Request.QueryString["account"] + "" %>">
        </div>

        <div class="info_title">
            <i></i>&nbsp;&nbsp;转账金额
        </div>

        <div style="position: relative; background: #f5f5f5; border-radius: 8px; margin-bottom: 12px;">
            <input style="border: 0px; background: none; font-size: 18px; padding: 13px; outline: none;" id="amount" value="<%=Request.QueryString["number"] + "" %>">
            <a style="position: absolute; top: 5px; right: 5px; background: #3838f5; color: #fff; display: flex; align-items: center; width: 83px; height: 37px; border-radius: 38px; justify-content: center; cursor: pointer;" onclick="javascript:$('#amount').val('<%=uConfig.gnumber(userdt,"amount") %>');">全部
            </a>
        </div>

        <div style="display: flex; font-size: 12px; color: gray;">
            <span>限额 <%=uConfig.stcdata("transfer_limit") %></span>
            <span style="margin-left: auto;">可用 <%=uConfig.gnumber(userdt,"amount") %></span>
        </div>



        
    <%--    <div class="info_title" style="color:#5a5b5c;">
            <i></i>&nbsp;&nbsp;请验证手机号（<%=uConfig.gd(userdt,"phone").Substring(0,3)+"****"+uConfig.gd(userdt,"phone").Substring(7,4) %>）
        </div>
        <div style="position: relative; background: #f8f8f8; border-radius: 8px; margin-top: 20px;">
            <input style="border: 0px; background: none; font-size: 16px; padding: 16px; outline: none; width: 100%; padding-right: 94px; box-sizing: border-box;" id="smscode" placeholder="请输入短信验证码">
            <a style="position: absolute; top: 5px; right: 5px; background: #3838f5; color: #fff; display: flex; align-items: center; width: 83px; height: 37px; border-radius: 38px; justify-content: center; cursor: pointer;" onclick="send_code()" id="sendcode_button">发送</a>
        </div>--%>

    </div>

    <div style="z-index: -1; background: #dbdef5; color: #3d50df; line-height: 24px; font-size: 12px; position: relative; top: -37px; padding: 18px; padding-top: 50px; border-radius: 8px; font-weight: bold;">

        <div>
            <svg t="1692474480503" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="54328" width="12" height="12">
                <path d="M512 0C229.665391 0 0 229.665391 0 512 0 614.578087 30.230261 713.594435 87.462957 798.274783 97.792 813.568 118.53913 817.574957 133.832348 807.268174 149.103304 796.93913 153.132522 776.169739 142.803478 760.898783 93.072696 687.282087 66.782609 601.221565 66.782609 512 66.782609 266.50713 266.50713 66.782609 512 66.782609 757.49287 66.782609 957.217391 266.50713 957.217391 512 957.217391 757.49287 757.49287 957.217391 512 957.217391 420.685913 957.217391 332.933565 929.792 258.248348 877.879652 243.044174 867.350261 222.274783 871.067826 211.767652 886.227478 201.238261 901.36487 204.978087 922.178783 220.115478 932.685913 306.064696 992.434087 406.995478 1024 512 1024 794.334609 1024 1024 794.334609 1024 512 1024 229.665391 794.334609 0 512 0ZM512.004452 237.895235C475.118191 237.895235 445.221843 267.791583 445.221843 304.677843 445.221843 341.564104 475.118191 371.460452 512.004452 371.460452 548.890713 371.460452 578.787061 341.564104 578.787061 304.677843 578.787061 267.791583 548.890713 237.895235 512.004452 237.895235ZM512 429.935304C481.257739 429.935304 456.347826 454.845217 456.347826 485.587478L456.347826 752.717913C456.347826 783.460174 481.257739 808.370087 512 808.370087 542.742261 808.370087 567.652174 783.460174 567.652174 752.717913L567.652174 485.587478C567.652174 454.845217 542.742261 429.935304 512 429.935304Z" fill="#3d50df" p-id="54329"></path></svg>&nbsp;&nbsp;重要提示
        </div>

        <div>
            请确认接收账号/手机号是否正确，否则造成资产损失将不可找回
       
        </div>
    </div>





    <div style="position: fixed; background: #eee; left: 0; bottom: 0px; width: 100%; display: flex; justify-content: center; padding: 18px; box-sizing: border-box;">
        <%--<div>
            <div style="color: #253ad5; font-size: 15px; margin-bottom: 8px; font-weight: bold;">到账数量：00.00</div>
            <div>
                <span style="background: #fff; color: gray; border-radius: 18px; padding: 1px 6px; font-size: 12px;">手续费：0.0</span>
            </div>
        </div>--%>
        <div style="margin-left: auto;">
            <a style="display: inline-block; background: #3838f5; color: #fff; min-width: 200px; padding: 16px; box-sizing: border-box; border-radius: 10px; cursor: pointer; text-align: center; font-size: 14px;" onclick="transfer()">确认转账</a>
        </div>
    </div>



    <script>

        $('.select_tab').on('click', function () {
            $(this).addClass("activity").siblings().removeClass("activity");

            var name = $(this).text();

            //switch (name) {
            //    case "TRC20":
            //        $('#refresh_button').show();
            //        qrcode_text = payurls.trc;
            //        break;
            //    case "Uliner":
            //        $('#refresh_button').hide();
            //        qrcode_text = payurls.s;
            //        break;
            //    default:
            //        break;
            //}

            //refresh_qrcode();

        })


        window.addEventListener('popstate', function (event) {
            console.log('load');
            var paramsString = localStorage.getItem('transferAddr');
            if (paramsString) {
                // 使用完毕后清除 localStorage 中的数据
                localStorage.removeItem('transferAddr');

                console.log('paramsString', paramsString);
            }

        });


        var transfer = function () {


            security_password(function (e) {

                v3api("transfer", {
                    data: {
                        paypwd: e.password,
                        uid: $('#uid').val(),
                        amount: $('#amount').val(),
                        smscode: $('#smscode').val()
                    }
                }, function () {
                    tp("转账成功");
                    $('#amount').val('');
                })
            })


        }


        var send_code = function () {
            if ($('#sendcode_button').html() != "发送") {
                return;
            }
            v3api("transaction_sendcode", {
                data: {
                    password_type: '<%=Request.QueryString["type"] %>'
                }
            }, function (json) {

                $('#sendcode_button').addClass("code_send");
                setCountDown(120)
            })
         }

    var setCountDown = function (s) {
        if (s <= 0) {
            $('#sendcode_button').removeClass("code_send");
            $('#sendcode_button').html('发送');
            return;
        }
        $('#sendcode_button').html(s + ' 秒');
        setTimeout('setCountDown(' + (s - 1) + ')', 1000)
    }
    </script>
</asp:Content>


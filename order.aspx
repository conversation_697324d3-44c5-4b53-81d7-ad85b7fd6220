<%@ Page Title="" Language="C#" MasterPageFile="~/top.master" AutoEventWireup="true" CodeFile="order.aspx.cs" Inherits="order_list" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="Server">
    <style>
        .password-content {
            position: fixed!important;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
        }

        .password-content {
            z-index: 9991;
        }

        .action_button {
            background: #FE6225;
            color: #fff;
            width: 100%;
            padding: 16px;
            box-sizing: border-box;
            border-radius: 5px;
            cursor: pointer;
            text-align: center;
            font-size: 14px;
            margin-top: 13px;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .order_button_limit {
            background: #31394A!important;
            color: #F6E0C4!important;
        }
    </style>
    <link href="../slayer/layer.css" rel="stylesheet" />
    <script src="../slayer/layer.js"></script>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="Server">


    <div style="background: #fff; border-radius: 8px; padding: 18px; font-size: 14px;" class="order_page">


        <div style="margin-bottom: 20px;">
            <h1 style="margin-bottom: 3px;">【淘返客】</h1>
            <div style="font-size: 12px; color: gray;">
                补单专属通道，佣金2%
            </div>
        </div>

        <div>
            <img src="/static/images/order-top.png" alt="" style="width: 100%;" />
        </div>



        <div data-v-4c2dcb20="" style="width: 100%; height: 100px; padding-top: 15px; overflow: hidden;">


            <div data-v-4c2dcb20="" class="box">
                <div data-v-4c2dcb20="" class="groups animation-ease" style="height: 2887.5px;">
                    <ul data-v-4c2dcb20="" class="group-item">
                        <li data-v-4c2dcb20="" class="prize-item">
                            <img data-v-4c2dcb20="" src="/static/images/wenhao.png"></li>
                        <li data-v-4c2dcb20="" class="prize-item">
                            <img data-v-4c2dcb20="" src="/static/images/wenhao.png"></li>
                        <li data-v-4c2dcb20="" class="prize-item">
                            <img data-v-4c2dcb20="" src="/static/images/wenhao.png"></li>
                        <li data-v-4c2dcb20="" class="prize-item">
                            <img data-v-4c2dcb20="" src="/static/images/wenhao.png"></li>
                        <li data-v-4c2dcb20="" class="prize-item">
                            <img data-v-4c2dcb20="" src="/static/images/wenhao.png"></li>
                    </ul>
                    <ul data-v-4c2dcb20="" class="group-item">
                        <li data-v-4c2dcb20="" class="prize-item">
                            <img data-v-4c2dcb20="" src="/static/images/wenhao.png"></li>
                        <li data-v-4c2dcb20="" class="prize-item">
                            <img data-v-4c2dcb20="" src="/static/images/wenhao.png"></li>
                        <li data-v-4c2dcb20="" class="prize-item">
                            <img data-v-4c2dcb20="" src="/static/images/wenhao.png"></li>
                        <li data-v-4c2dcb20="" class="prize-item">
                            <img data-v-4c2dcb20="" src="/static/images/wenhao.png"></li>
                        <li data-v-4c2dcb20="" class="prize-item">
                            <img data-v-4c2dcb20="" src="/static/images/wenhao.png"></li>
                    </ul>
                    <ul data-v-4c2dcb20="" class="group-item">
                        <li data-v-4c2dcb20="" class="prize-item">
                            <img data-v-4c2dcb20="" src="/static/images/wenhao.png"></li>
                        <li data-v-4c2dcb20="" class="prize-item">
                            <img data-v-4c2dcb20="" src="/static/images/wenhao.png"></li>
                        <li data-v-4c2dcb20="" class="prize-item">
                            <img data-v-4c2dcb20="" src="/static/images/wenhao.png"></li>
                        <li data-v-4c2dcb20="" class="prize-item">
                            <img data-v-4c2dcb20="" src="/static/images/wenhao.png"></li>
                        <li data-v-4c2dcb20="" class="prize-item">
                            <img data-v-4c2dcb20="" src="/static/images/wenhao.png"></li>
                    </ul>
                    <ul data-v-4c2dcb20="" class="group-item">
                        <li data-v-4c2dcb20="" class="prize-item">
                            <img data-v-4c2dcb20="" src="/static/images/wenhao.png"></li>
                        <li data-v-4c2dcb20="" class="prize-item">
                            <img data-v-4c2dcb20="" src="/static/images/wenhao.png"></li>
                        <li data-v-4c2dcb20="" class="prize-item">
                            <img data-v-4c2dcb20="" src="/static/images/wenhao.png"></li>
                        <li data-v-4c2dcb20="" class="prize-item">
                            <img data-v-4c2dcb20="" src="/static/images/wenhao.png"></li>
                        <li data-v-4c2dcb20="" class="prize-item">
                            <img data-v-4c2dcb20="" src="/static/images/wenhao.png"></li>
                    </ul>
                    <ul data-v-4c2dcb20="" class="group-item">
                        <li data-v-4c2dcb20="" class="prize-item">
                            <img data-v-4c2dcb20="" src="/static/images/wenhao.png"></li>
                        <li data-v-4c2dcb20="" class="prize-item">
                            <img data-v-4c2dcb20="" src="/static/images/wenhao.png"></li>
                        <li data-v-4c2dcb20="" class="prize-item">
                            <img data-v-4c2dcb20="" src="/static/images/wenhao.png"></li>
                        <li data-v-4c2dcb20="" class="prize-item">
                            <img data-v-4c2dcb20="" src="/static/images/wenhao.png"></li>
                        <li data-v-4c2dcb20="" class="prize-item">
                            <img data-v-4c2dcb20="" src="/static/images/wenhao.png"></li>
                    </ul>
                    <ul data-v-4c2dcb20="" class="group-item">
                        <li data-v-4c2dcb20="" class="prize-item">
                            <img data-v-4c2dcb20="" src="/static/images/wenhao.png"></li>
                        <li data-v-4c2dcb20="" class="prize-item">
                            <img data-v-4c2dcb20="" src="/static/images/wenhao.png"></li>
                        <li data-v-4c2dcb20="" class="prize-item">
                            <img data-v-4c2dcb20="" src="/static/images/wenhao.png"></li>
                        <li data-v-4c2dcb20="" class="prize-item">
                            <img data-v-4c2dcb20="" src="/static/images/wenhao.png"></li>
                        <li data-v-4c2dcb20="" class="prize-item">
                            <img data-v-4c2dcb20="" src="/static/images/wenhao.png"></li>
                    </ul>
                </div>
                <div data-v-4c2dcb20="" class="groups animation-ease" style="height: 2887.5px;">
                    <ul data-v-4c2dcb20="" class="group-item">
                        <li data-v-4c2dcb20="" class="prize-item">
                            <img data-v-4c2dcb20="" src="/static/images/wenhao.png"></li>
                        <li data-v-4c2dcb20="" class="prize-item">
                            <img data-v-4c2dcb20="" src="/static/images/wenhao.png"></li>
                        <li data-v-4c2dcb20="" class="prize-item">
                            <img data-v-4c2dcb20="" src="/static/images/wenhao.png"></li>
                    </ul>
                    <ul data-v-4c2dcb20="" class="group-item">
                        <li data-v-4c2dcb20="" class="prize-item">
                            <img data-v-4c2dcb20="" src="/static/images/wenhao.png"></li>
                        <li data-v-4c2dcb20="" class="prize-item">
                            <img data-v-4c2dcb20="" src="/static/images/wenhao.png"></li>
                        <li data-v-4c2dcb20="" class="prize-item">
                            <img data-v-4c2dcb20="" src="/static/images/wenhao.png"></li>
                    </ul>
                    <ul data-v-4c2dcb20="" class="group-item">
                        <li data-v-4c2dcb20="" class="prize-item">
                            <img data-v-4c2dcb20="" src="/static/images/wenhao.png"></li>
                        <li data-v-4c2dcb20="" class="prize-item">
                            <img data-v-4c2dcb20="" src="/static/images/wenhao.png"></li>
                        <li data-v-4c2dcb20="" class="prize-item">
                            <img data-v-4c2dcb20="" src="/static/images/wenhao.png"></li>
                        <li data-v-4c2dcb20="" class="prize-item">
                            <img data-v-4c2dcb20="" src="/static/images/wenhao.png"></li>
                        <li data-v-4c2dcb20="" class="prize-item">
                            <img data-v-4c2dcb20="" src="/static/images/wenhao.png"></li>
                    </ul>
                    <ul data-v-4c2dcb20="" class="group-item">
                        <li data-v-4c2dcb20="" class="prize-item">
                            <img data-v-4c2dcb20="" src="/static/images/wenhao.png"></li>
                        <li data-v-4c2dcb20="" class="prize-item">
                            <img data-v-4c2dcb20="" src="/static/images/wenhao.png"></li>
                        <li data-v-4c2dcb20="" class="prize-item">
                            <img data-v-4c2dcb20="" src="/static/images/wenhao.png"></li>
                        <li data-v-4c2dcb20="" class="prize-item">
                            <img data-v-4c2dcb20="" src="/static/images/wenhao.png"></li>
                        <li data-v-4c2dcb20="" class="prize-item">
                            <img data-v-4c2dcb20="" src="/static/images/wenhao.png"></li>
                    </ul>
                    <ul data-v-4c2dcb20="" class="group-item">
                        <li data-v-4c2dcb20="" class="prize-item">
                            <img data-v-4c2dcb20="" src="/static/images/wenhao.png"></li>
                        <li data-v-4c2dcb20="" class="prize-item">
                            <img data-v-4c2dcb20="" src="/static/images/wenhao.png"></li>
                        <li data-v-4c2dcb20="" class="prize-item">
                            <img data-v-4c2dcb20="" src="/static/images/wenhao.png"></li>
                        <li data-v-4c2dcb20="" class="prize-item">
                            <img data-v-4c2dcb20="" src="/static/images/wenhao.png"></li>
                        <li data-v-4c2dcb20="" class="prize-item">
                            <img data-v-4c2dcb20="" src="/static/images/wenhao.png"></li>
                    </ul>
                    <ul data-v-4c2dcb20="" class="group-item">
                        <li data-v-4c2dcb20="" class="prize-item">
                            <img data-v-4c2dcb20="" src="/static/images/wenhao.png"></li>
                        <li data-v-4c2dcb20="" class="prize-item">
                            <img data-v-4c2dcb20="" src="/static/images/wenhao.png"></li>
                        <li data-v-4c2dcb20="" class="prize-item">
                            <img data-v-4c2dcb20="" src="/static/images/wenhao.png"></li>
                        <li data-v-4c2dcb20="" class="prize-item">
                            <img data-v-4c2dcb20="" src="/static/images/wenhao.png"></li>
                        <li data-v-4c2dcb20="" class="prize-item">
                            <img data-v-4c2dcb20="" src="/static/images/wenhao.png"></li>
                    </ul>
                    <ul data-v-4c2dcb20="" class="group-item">
                        <li data-v-4c2dcb20="" class="prize-item">
                            <img data-v-4c2dcb20="" src="/static/images/wenhao.png"></li>
                        <li data-v-4c2dcb20="" class="prize-item">
                            <img data-v-4c2dcb20="" src="/static/images/wenhao.png"></li>
                        <li data-v-4c2dcb20="" class="prize-item">
                            <img data-v-4c2dcb20="" src="/static/images/wenhao.png"></li>
                        <li data-v-4c2dcb20="" class="prize-item">
                            <img data-v-4c2dcb20="" src="/static/images/wenhao.png"></li>
                        <li data-v-4c2dcb20="" class="prize-item">
                            <img data-v-4c2dcb20="" src="/static/images/wenhao.png"></li>
                    </ul>
                </div>
                <div data-v-4c2dcb20="" class="groups animation-ease" style="height: 2887.5px;">
                    <ul data-v-4c2dcb20="" class="group-item">
                        <li data-v-4c2dcb20="" class="prize-item">
                            <img data-v-4c2dcb20="" src="/static/images/wenhao.png"></li>
                        <li data-v-4c2dcb20="" class="prize-item">
                            <img data-v-4c2dcb20="" src="/static/images/wenhao.png"></li>
                        <li data-v-4c2dcb20="" class="prize-item">
                            <img data-v-4c2dcb20="" src="/static/images/wenhao.png"></li>
                        <li data-v-4c2dcb20="" class="prize-item">
                            <img data-v-4c2dcb20="" src="/static/images/wenhao.png"></li>
                        <li data-v-4c2dcb20="" class="prize-item">
                            <img data-v-4c2dcb20="" src="/static/images/wenhao.png"></li>
                    </ul>
                    <ul data-v-4c2dcb20="" class="group-item">
                        <li data-v-4c2dcb20="" class="prize-item">
                            <img data-v-4c2dcb20="" src="/static/images/wenhao.png"></li>
                        <li data-v-4c2dcb20="" class="prize-item">
                            <img data-v-4c2dcb20="" src="/static/images/wenhao.png"></li>
                        <li data-v-4c2dcb20="" class="prize-item">
                            <img data-v-4c2dcb20="" src="/static/images/wenhao.png"></li>
                        <li data-v-4c2dcb20="" class="prize-item">
                            <img data-v-4c2dcb20="" src="/static/images/wenhao.png"></li>
                        <li data-v-4c2dcb20="" class="prize-item">
                            <img data-v-4c2dcb20="" src="/static/images/wenhao.png"></li>
                    </ul>
                    <ul data-v-4c2dcb20="" class="group-item">
                        <li data-v-4c2dcb20="" class="prize-item">
                            <img data-v-4c2dcb20="" src="/static/images/wenhao.png"></li>
                        <li data-v-4c2dcb20="" class="prize-item">
                            <img data-v-4c2dcb20="" src="/static/images/wenhao.png"></li>
                        <li data-v-4c2dcb20="" class="prize-item">
                            <img data-v-4c2dcb20="" src="/static/images/wenhao.png"></li>
                        <li data-v-4c2dcb20="" class="prize-item">
                            <img data-v-4c2dcb20="" src="/static/images/wenhao.png"></li>
                        <li data-v-4c2dcb20="" class="prize-item">
                            <img data-v-4c2dcb20="" src="/static/images/wenhao.png"></li>
                    </ul>
                    <ul data-v-4c2dcb20="" class="group-item">
                        <li data-v-4c2dcb20="" class="prize-item">
                            <img data-v-4c2dcb20="" src="/static/images/wenhao.png"></li>
                        <li data-v-4c2dcb20="" class="prize-item">
                            <img data-v-4c2dcb20="" src="/static/images/wenhao.png"></li>
                        <li data-v-4c2dcb20="" class="prize-item">
                            <img data-v-4c2dcb20="" src="/static/images/wenhao.png"></li>
                        <li data-v-4c2dcb20="" class="prize-item">
                            <img data-v-4c2dcb20="" src="/static/images/wenhao.png"></li>
                        <li data-v-4c2dcb20="" class="prize-item">
                            <img data-v-4c2dcb20="" src="/static/images/wenhao.png"></li>
                    </ul>
                    <ul data-v-4c2dcb20="" class="group-item">
                        <li data-v-4c2dcb20="" class="prize-item">
                            <img data-v-4c2dcb20="" src="/static/images/wenhao.png"></li>
                        <li data-v-4c2dcb20="" class="prize-item">
                            <img data-v-4c2dcb20="" src="/static/images/wenhao.png"></li>
                        <li data-v-4c2dcb20="" class="prize-item">
                            <img data-v-4c2dcb20="" src="/static/images/wenhao.png"></li>
                        <li data-v-4c2dcb20="" class="prize-item">
                            <img data-v-4c2dcb20="" src="/static/images/wenhao.png"></li>
                        <li data-v-4c2dcb20="" class="prize-item">
                            <img data-v-4c2dcb20="" src="/static/images/wenhao.png"></li>
                    </ul>
                    <ul data-v-4c2dcb20="" class="group-item">
                        <li data-v-4c2dcb20="" class="prize-item">
                            <img data-v-4c2dcb20="" src="/static/images/wenhao.png"></li>
                        <li data-v-4c2dcb20="" class="prize-item">
                            <img data-v-4c2dcb20="" src="/static/images/wenhao.png"></li>
                        <li data-v-4c2dcb20="" class="prize-item">
                            <img data-v-4c2dcb20="" src="/static/images/wenhao.png"></li>
                        <li data-v-4c2dcb20="" class="prize-item">
                            <img data-v-4c2dcb20="" src="/static/images/wenhao.png"></li>
                        <li data-v-4c2dcb20="" class="prize-item">
                            <img data-v-4c2dcb20="" src="/static/images/wenhao.png"></li>
                    </ul>
                    <ul data-v-4c2dcb20="" class="group-item">
                        <li data-v-4c2dcb20="" class="prize-item">
                            <img data-v-4c2dcb20="" src="/static/images/wenhao.png"></li>
                        <li data-v-4c2dcb20="" class="prize-item">
                            <img data-v-4c2dcb20="" src="/static/images/wenhao.png"></li>
                        <li data-v-4c2dcb20="" class="prize-item">
                            <img data-v-4c2dcb20="" src="/static/images/wenhao.png"></li>
                        <li data-v-4c2dcb20="" class="prize-item">
                            <img data-v-4c2dcb20="" src="/static/images/wenhao.png"></li>
                        <li data-v-4c2dcb20="" class="prize-item">
                            <img data-v-4c2dcb20="" src="/static/images/wenhao.png"></li>
                    </ul>
                </div>
            </div>

            <style>
                .box .animation-ease[data-v-4c2dcb20] {
                    transition-property: transform;
                    transition-duration: 8s;
                    transition-timing-function: ease;
                }

                .box[data-v-4c2dcb20] {
                    height: 165px;
                    overflow: hidden;
                }

                ul, li {
                    padding: 0;
                    margin: 0;
                    list-style: none;
                }

                .box .groups[data-v-4c2dcb20] {
                    float: left;
                    width: 33.33%;
                    text-align: center;
                }

                .box .groups .prize-item[data-v-4c2dcb20] {
                    width: 120px;
                    height: 90px;
                    font-size: .666667rem;
                    line-height: 2.133333rem;
                    margin-top: 0.000039rem;
                }

                .box .groups .prize-item > img[data-v-4c2dcb20] {
                    width: 120px;
                    height: 90px;
                    border-radius: 0.093333rem;
                }
            </style>



            <script>
                var tt1 = 0, tt2 = 0, tt3 = 0
                function sum(m, n) {
                    var num = Math.floor(Math.random() * (m - n) + n);
                    return num;
                }
                var gdStart = function () {
                    tt1 = sum(0, 2800) * -1;
                    $('.animation-ease').eq(0).css('transform', 'translate3d(0px, ' + tt1 + 'px, 0px)')
                    tt2 = sum(0, 2800) * -1;
                    $('.animation-ease').eq(1).css('transform', 'translate3d(0px, ' + tt2 + 'px, 0px)')
                    tt3 = sum(0, 2800) * -1;
                    $('.animation-ease').eq(2).css('transform', 'translate3d(0px, ' + tt3 + 'px, 0px)')
                }

                var gdEnd = function () {
                    tt1 = 2478 * -1;
                    tt1 = 0;
                    $('.animation-ease').eq(0).css('transform', 'translate3d(0px, ' + tt1 + 'px, 0px)');
                    $('.animation-ease').eq(1).css('transform', 'translate3d(0px, ' + tt1 + 'px, 0px)');
                    $('.animation-ease').eq(2).css('transform', 'translate3d(0px, ' + tt1 + 'px, 0px)');
                }
            </script>
        </div>






        <div style="padding: 18px 0;">
            正在全力抢单中，抢单结果将在下方发放。
        </div>

    </div>

    <a onclick="<%=Convert.ToDouble(uConfig.gd(userdt, "amount")) < 10 ? "javascript:location.href='dating.aspx'" : "new_advert()" %>" id="order_button" class="action_button order_page <%=Convert.ToDouble(uConfig.gd(userdt, "amount")) < 10 ? "order_button_limit" : "" %>"><%=Convert.ToDouble(uConfig.gd(userdt, "amount")) < 10 ? "最低任务金额10元，请买币" : "开始任务" %></a>


    <a onclick="$('#pop-cpt').show();" id="add_button" class="action_button order_page" style="background: #ffd1bf; color: #c7582d; font-weight: bold;">一键增加任务次数</a>






    <div class=" pg-select-items order_page" style="display: none;">
        <div style="width: 100vw; height: 100vh; position: fixed; top: 0; left: 0; background: #000000b5; z-index: 9991;"></div>

        <div class="password-content" style="background: linear-gradient(187deg, #edbf28, #ffffff); padding: 8px; box-sizing: border-box; width: 95%; max-width: 380px; position: relative;">

            <h3 style="text-align: center; margin: 0; padding: 13px; font-size: 26px; color: #f5ecdc; text-shadow: 2px 3px 2px #7d5909;">今日剩余次数：<%=Convert.ToInt16(pmlist["number"]) + Convert.ToInt16(uConfig.gd(userdt, "reward_number")) %> 次</h3>


            <div style="padding: 13px 17px; text-align: right; background: #fff; color: #000; box-sizing: border-box; font-size: 12px; text-align: left;">


                <div style="text-align: center; font-weight: bold; font-size: 17px; margin-bottom: 15px;">
                    提交订单后 请务必保持密信在线等待回款
                </div>


                <div style="display: flex; flex-wrap: wrap;">

                    <asp:Repeater ID="classList" runat="server">
                        <ItemTemplate>

                            <div style="width: 33.33%; text-align: center; background: #fff; padding: 2px 6px; box-sizing: border-box; margin-bottom: 8px;" onclick="set_classId(<%#Eval("id") %>,'<%#Eval("amount") %>')">
                                <div style="box-shadow: 2px 2px 5px #ff9800c2; padding: 10px; border-radius: 8px;">
                                    <%#Eval("name") %>
                                    <%--<div style="font-size: 12px; background: #ff9675; padding: 2px; margin-top: 6px; border-radius: 8px; color: #fff;">
                                        佣金:1%
                                    </div>--%>
                                </div>
                            </div>

                        </ItemTemplate>
                    </asp:Repeater>


                </div>



                <%--<div style="margin: 12px;">
                    <div style="color: #3a3b3c; font-weight: bold; font-size: 16px; margin-bottom: 5px;">
                        第一单开放时间：10:00
           
                    </div>
                    <div style="color: #3a3b3c; font-weight: bold; font-size: 16px; margin-bottom: 5px;">
                        第二单开放时间：14:00
           
                    </div>


                    <div style="color: #3a3b3c; font-weight: bold; font-size: 16px; margin-bottom: 5px;">
                        第三单开放时间：18:00
           
                    </div>
                    <div style="color: #3a3b3c; font-weight: bold; font-size: 16px; margin-bottom: 5px;">
                        第四单开放时间：21:00
           
                    </div>
                </div>--%>




            </div>

            <div>
                <div style="display: flex; padding: 16px 22px;">

                    <div style="width: 100%; text-align: center;">
                        <a style="color: #605D54; border: 1px solid #ababab; display: inline-block; width: 130px; height: 39px; line-height: 39px; border-radius: 24px; cursor: pointer; font-weight: bold;" onclick="javascript:$('.pg-select-items').fadeOut(100);">暂不任务</a>
                    </div>
                </div>
            </div>



        </div>


    </div>



    <div id="task_page" class="order_page" style="display: none;">
        <div style="background: #fff; border-radius: 8px; padding: 18px; font-size: 14px;">
            <div>
                <b>收款方式</b>

                <div style="margin-top: 18px; display: flex; align-items: center;" onclick="switch_payment();" id="paymentid">
                    <div style="color: gray; text-align: center; font-size: 12px; padding: 12px; width: 100%;">请选择收款账户</div>
                </div>


            </div>
        </div>

    </div>



    <div id="untask_page" class="order_page" style="display: ;">


        <div style="background: #fff; padding: 25px; border-radius: 5px; margin: 10px 0;">

            <h2 style="margin: 0; margin-bottom: 10px;">今日战果
                <div style="display: inline-block; font-size: 15px; color: #5a5b5c;">
                    【今日剩余可任务次数 <span id="shuadan_number"><%=Convert.ToInt16(pmlist["number"]) + Convert.ToInt16(uConfig.gd(userdt, "reward_number")) %></span>次】
                </div>
            </h2>



            <div style="margin-top: 16px;">

                <div style="display: flex;">

                    <div style="flex-shrink: 0; margin-right: 2px;">

                        <h2 style="margin: 0; color: #fe6225;"><span id="total_amount">0</span>
                        </h2>
                        <div style="margin-bottom: 5px;"><span style="font-size: 12px; color: #aaa; font-weight: 500;">(冻结 0.00)</span></div>
                        <div style="font-size: 12px;">
                            今日任务总额
                        </div>

                    </div>

                    <div style="display: flex; width: 100%; font-size: 12px;">
                        <div style="width: 50%; margin-bottom: 30px; margin: auto; display: flex;">

                            <div style="text-align: center; margin-left: auto;">
                                <div style="color: #000; font-weight: bold; margin-top: 4px; font-size: 14px;" id="total_award_number">0</div>

                                <div style="color: #31394a;">
                                    历史次数
                                </div>
                            </div>
                        </div>

                        <div style="width: 50%; margin-bottom: 30px; margin: auto; display: flex;">

                            <div style="text-align: center; margin-left: auto;">
                                <div style="color: #000; font-weight: bold; margin-top: 4px; font-size: 14px;" id="total_award_amount">0.00</div>

                                <div style="color: #31394a;">
                                    历史佣金
                                </div>
                            </div>
                        </div>
                    </div>


                </div>


                <div style="display: flex; font-size: 12px; margin-top: 20px; flex-wrap: wrap;">

                    <div style="width: 50%; text-align: left; margin-bottom: 30px;">

                        <div style="color: gray;">
                            今日已刷佣金
                        </div>
                        <div style="color: #000; font-weight: bold; margin-top: 4px; font-size: 14px;" id="today_amount">0.00</div>

                    </div>

                    <div style="width: 50%; text-align: left; margin-bottom: 30px;">

                        <div style="color: gray;">
                            昨日已刷佣金
                        </div>
                        <div style="color: #000; font-weight: bold; margin-top: 4px; font-size: 14px;" id="yesterday_amount">0.00</div>

                    </div>



                    <div style="width: 50%; text-align: left; margin-bottom: 30px;">

                        <div style="color: gray;">
                            今日已任务数
                        </div>
                        <div style="color: #000; font-weight: bold; margin-top: 4px; font-size: 14px;" id="today_number">0</div>

                    </div>
                    <div style="width: 50%; text-align: left; margin-bottom: 30px;">

                        <div style="color: gray;">
                            昨日已任务数
                        </div>
                        <div style="color: #000; font-weight: bold; margin-top: 4px; font-size: 14px;" id="yesterday_number">0</div>

                    </div>


                </div>


            </div>
        </div>

    </div>


    <div id="newtip" style="display: none;">

        <div style="display: flex; margin-bottom: 10px; padding: 10px;">
            <div>
                <img src="static/images/tipicon.jpeg" style="width: 100%;">
            </div>
            <div style="flex-shrink: 0; margin-left: 21px; display: flex; flex-direction: column; justify-content: center; line-height: 28px; font-weight: bold; color: #3a3b3c;">

                <div>请保持与【密信专员】联系~~</div>
                <div>等待回款 及时确认收货</div>
                <div>避免资金出现亏损！</div>
                <div>未能及时确认请提前告知专员</div>

            </div>
        </div>

    </div>

    <div id="list-container" style="background: rgb(231, 231, 231); border-radius: 8px; padding: 38px; text-align: center; color: rgb(154, 155, 152); font-weight: bold; font-size: 14px;">

        <div id="order_lists">
        </div>
    </div>


    <script>
        var show_orderList = function (state) {
            v3api("lists", { data: { page: 'order_list', p: 0, limit: 999, state: state } }, function (e) {
                $('#order_lists').html("");

                for (var i = 0; i < e.data.list.length; i++) {
                    var obj = e.data.list[i];
                    $('#order_lists').append('<div style="background:#fff;border-radius:10px;padding:18px 22px;display:flex;margin-top:20px;" listid="' + obj.orderId + '" onclick="javascript:location.href=\'order_details.aspx?id=' + obj.id + '\'">        <div style="text-align:left;">            <div style="">                <strong style="color:#2A3EE8;font-size:18px;">' + (obj.amount).toFixed(2) + '</strong><img src="/static/images/coin.png" alt="" height="15" width="15" style="margin-left: 5px;">&nbsp;&nbsp;                <span style="background:#FFDAA5;color:gray;border-radius:18px;padding:2px 8px;font-size:12px;">付款金额</span>            </div>   <div style="color: #000;font-size:12px;padding:10px 0;padding-bottom: 0;font-weight: 500;">               佣金：' + (obj.award_amount).toFixed(2) + ' <img src="/static/images/coin.png" alt="" height="15" width="15" style="margin-left: 5px;">            </div>   <div style="color: #dd3e3e; font-size: 12px; padding: 10px 0; font-weight: 500; padding-top: 4px;">预计返还：<span style="font-size: 18px;">' + (obj.amount + obj.award_amount).toFixed(2) + '</span> <img src="/static/images/coin.png" alt="" height="15" width="15" style="margin-left: 5px;"></div>        <div style="color:#aaa;font-size:12px;">                ' + obj.create_time + '            </div>        </div>        <div style="margin-left:auto;    text-align: center;" class="statedata">            <span style="border-radius:18px;padding:2px 8px;font-size:12px;' + (obj.state == 1000 || obj.state == 1 ? 'background:#373737;color:#fbe9cd;' : obj.state == 1 ? 'background:#2A3EE8;color:#fff;' : obj.state == -1 ? 'background:#db4a52;color:#fff;' : 'background:gray;color:#fff;') + '">' + (obj.state == 1000 || obj.state == 1 ? '等待商家回款' : obj.state == 1 ? '已完成' : obj.state == -1 ? '超时' : obj.state == -2 ? '系统取消' : '其他') + '</span>   ' + (obj.state == 1000 || obj.state == 1 ? '<div style="    margin-top: 20px;">    <a style="border-radius:18px;padding: 6px 10px;font-size:12px;background: #eee;color: #333;display: flex;cursor:pointer" onclick="confirm_transaction(\'' + obj.orderId + '\')"><svg t="1693040838464" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="4309" width="16" height="16"><path d="M770.56 928.4608H388.4032c-16.9472 0-30.72-13.7728-30.72-30.72s13.7728-30.72 30.72-30.72h382.1056c15.616 0 28.8768-11.2128 32.3584-27.2384l72.2944-335.1552c3.4304-15.7696-0.3584-31.9488-10.3424-44.3392-9.472-11.7248-22.7328-18.176-37.4272-18.176h-239.104a30.72 30.72 0 0 1-28.16-43.008c62.1056-142.2848 40.3456-201.1136 28.1088-219.8016-13.7216-20.9408-33.792-24.1152-44.4928-24.1152-25.8048 0-35.9936 25.088-47.8208 77.7216-1.5872 6.9632-3.0208 13.4656-4.5568 19.3536-42.1888 161.5872-149.3504 219.136-235.6224 219.136H192.2048c-17.8688 0-32.4096 15.36-32.4096 34.2016v327.2192c0 18.8416 14.5408 34.2016 32.4096 34.2016h58.9312c16.9472 0 30.72 13.7728 30.72 30.72s-13.7728 30.72-30.72 30.72H192.2048c-51.7632 0-93.8496-42.9056-93.8496-95.6416V505.6c0-52.736 42.0864-95.6416 93.8496-95.6416h63.5392c30.72 0 134.1952-12.4928 176.128-173.1584 1.3824-5.2736 2.6624-11.1104 4.096-17.3568 9.8816-43.9296 28.3136-125.696 107.776-125.696 39.3728 0 74.3424 18.8928 95.8976 51.8656 24.2688 37.0688 41.1136 107.1616-5.888 235.008h193.6384c33.1264 0 64.2048 14.9504 85.248 41.0112 21.7088 26.88 29.952 61.8496 22.6304 95.8464l-72.2944 335.1552c-9.472 43.9808-48.3328 75.8272-92.416 75.8272z" fill="#363F5B" p-id="4310"></path><path d="M269.6192 804.2496c-16.9472 0-30.72-13.7728-30.72-30.72v-193.0752c0-16.9472 13.7728-30.72 30.72-30.72s30.72 13.7728 30.72 30.72v193.0752c0 16.9472-13.7728 30.72-30.72 30.72z" fill="#B11EFF" p-id="4311"></path></svg>&nbsp;确认收款</a></div>' : '') + '     </div>    </div>');
                }

                $('#list-container').css({ "padding": "8px" });
                $('#list-container').css({ "padding-bottom": "18px" });

                if (e.data.list.length == 0) {
                    $('#order_lists').html('<div>            <svg t="1692619408833" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="32388" width="64" height="64"><path d="M234.656 213.344H256A106.656 106.656 0 0 0 362.656 320h298.688A106.656 106.656 0 0 0 768 213.344h21.344a85.344 85.344 0 0 1 85.312 85.312v512A85.344 85.344 0 0 1 789.344 896H234.656a85.344 85.344 0 0 1-85.312-85.344v-512a85.344 85.344 0 0 1 85.312-85.312z m85.344 192A21.344 21.344 0 1 0 320 448h362.656a21.344 21.344 0 1 0 0-42.656H320z m0 149.312a21.344 21.344 0 0 0 0 42.688h362.656a21.344 21.344 0 0 0 0-42.688H320zM320 704a21.344 21.344 0 1 0 0 42.656h234.656a21.344 21.344 0 1 0 0-42.656H320z m42.656-554.656h298.688a64 64 0 1 1 0 128h-298.688a64 64 0 0 1 0-128z" fill="#9A9B98" p-id="32389"></path></svg>        </div>        <div style="    margin-top: 8px;">            暂无进行中的订单        </div>');
                    $('#list-container').css({ "padding-bottom": "0px" });
                    $('#list-container').css({ "padding": "38px" });
                }

            })
        };

        show_orderList('1000');


        var confirm_transaction = function (id) {
            event.stopPropagation();
            v3api("confirm_order", { data: { id: id } }, function (e) {
                tp('确认成功');
                var ls = $('[listid=' + id + ']');
                ls.remove();
                //ls.find(".statedata").html('<span style="border-radius:18px;padding:2px 8px;font-size:12px;background:#2A3EE8;color:#fff;">已完成</span>');
            })

        }
    </script>




    <div class="a order_page" style="background: linear-gradient(209deg, #ffcc2c, #ff9a2c); border-radius: 0.106667rem; display: inline-block; text-align: center; color: #fff; padding: 5px 30px; font-size: 12px; margin-top: 30px;">
        任务说明
    </div>

    <div class="order_page" style="background: #fff; border-radius: 8px; padding: 18px; font-size: 15px; margin-top: 10px; color: #aaa; line-height: 22px;">



        <%--<div style="margin-bottom: 12px;">

            <div style="color: #3a3b3c; font-weight: bold; font-size: 16px; margin-bottom: 5px;">
                第一单开放时间：10:00
            </div>
            <div style="color: #3a3b3c; font-weight: bold; font-size: 16px; margin-bottom: 5px;">
                第二单开放时间：14:00
            </div>


            <div style="color: #3a3b3c; font-weight: bold; font-size: 16px; margin-bottom: 5px;">
                第三单开放时间：18:00
            </div>
            <div style="color: #3a3b3c; font-weight: bold; font-size: 16px; margin-bottom: 5px;">
                第四单开放时间：21:00
            </div>

        </div>--%>



        <div>
            1：一个账户1天可刷<strong>3单</strong>
        </div>

        <div>
            2：系统基于LBS技术，通过云端自动匹配商品
        </div>

        <div>
            3：为了杜绝平台监管，入匹配到订单10分钟之内没有确定提交，系统将冻结该笔订单金额1小时，到期后系统会自动解冻。
        </div>

    </div>





    <div class="demo-popup-page payway-select" style="display: none;">
        <div style="padding: 18px; font-size: 13px;">

            <h4 style="color: #3C54D1; margin-bottom: 22px; font-size: 16px; display: flex;">选择收款账户
                <div style="margin-left: auto;" onclick="location.href='payway_add.aspx'">
                    <svg t="1692428311309" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="8668" width="22" height="22">
                        <path d="M642.8 548.2H383c-19.3 0-35-15.7-35-35s15.7-35 35-35h259.8c19.3 0 35 15.7 35 35s-15.7 35-35 35z" fill="#4195F9" p-id="8669"></path><path d="M512.9 678.1c-19.3 0-35-15.7-35-35V383.3c0-19.3 15.7-35 35-35s35 15.7 35 35v259.8c0 19.3-15.6 35-35 35z" fill="#4195F9" p-id="8670"></path><path d="M512.9 902.7c-52.6 0-103.6-10.3-151.6-30.6-46.4-19.6-88-47.7-123.8-83.5s-63.8-77.4-83.5-123.8c-20.3-48-30.6-99.1-30.6-151.6 0-52.6 10.3-103.6 30.6-151.6 19.6-46.4 47.7-88 83.5-123.8s77.4-63.8 123.8-83.5c48-20.3 99.1-30.6 151.6-30.6s103.6 10.3 151.6 30.6c46.4 19.6 88 47.7 123.8 83.5s63.8 77.4 83.5 123.8c20.3 48 30.6 99.1 30.6 151.6 0 52.6-10.3 103.6-30.6 151.6-19.6 46.4-47.7 88-83.5 123.8s-77.4 63.8-123.8 83.5c-48 20.3-99 30.6-151.6 30.6z m0-709c-176.2 0-319.5 143.3-319.5 319.5s143.3 319.5 319.5 319.5 319.5-143.3 319.5-319.5c0-176.1-143.3-319.5-319.5-319.5z" p-id="8671"></path></svg>
                </div>
            </h4>

            <div id="lists" style="max-height: 500px; overflow-y: auto;"></div>

        </div>
    </div>






    <div class=" pg-order" style="display: none;">
        <div style="width: 100vw; height: 100vh; position: fixed; top: 0; left: 0; background: #000000b5; z-index: 9991;"></div>

        <div class="password-content" style="background: linear-gradient(187deg, #edbf28, #ffffff); padding: 8px; box-sizing: border-box; width: 95%; max-width: 380px; position: relative;">

            <h3 style="text-align: center; margin: 0; padding: 13px; font-size: 26px; color: #f5ecdc; text-shadow: 2px 3px 2px #7d5909;">恭喜抢单成功</h3>


            <div style="padding: 13px 17px; text-align: right; background: #fff; color: #aaa; box-sizing: border-box; font-size: 12px; text-align: left;">


                <div>任务时间：<span class="time"></span></div>
                <div>任务编号：<span class="orderId"></span></div>

                <div style="display: flex; background: #f1f1f1; padding: 11px; margin: 10px 0;">
                    <div>
                        <img class="item_imgurl" src="" alt="" height="100" width="100">
                    </div>
                    <div style="width: 100%; padding: 10px; color: #555; font-size: 15px; display: flex; flex-direction: column;">
                        <div class="item_name" style="text-wrap: wrap;"></div>

                        <div style="margin-top: auto;">
                            <b class="total_amount">0</b>
                        </div>
                    </div>


                </div>




                <div style="font-size: 14px;">

                    <div style="display: flex; margin: 10px 0;">
                        <div>
                            订单总额
           
                        </div>
                        <div style="margin-left: auto; color: #222;">
                            ￥<span class="total_amount">0</span>

                        </div>
                    </div>



                    <div style="display: flex; margin: 10px 0;">
                        <div>佣金</div>
                        <div style="margin-left: auto; color: #222;">
                            ￥<span class="award_amount">0</span>

                        </div>
                    </div>
                    <div style="display: flex; margin: 10px 0;">
                        <div>预计返还</div>
                        <div style="margin-left: auto; color: #FAA751; font-size: 24px;">￥<span class="award_total_amount">0</span></div>
                    </div>

                </div>



            </div>

            <div id="get-order-button">
                <div style="display: flex; padding: 16px 22px;">

                    <div style="width: 50%; text-align: center;">
                        <a style="color: #605D54; border: 1px solid #ababab; display: inline-block; width: 130px; height: 39px; line-height: 39px; border-radius: 24px; cursor: pointer; font-weight: bold;" onclick="order_cancel()">取消任务</a>
                    </div>
                    <div style="width: 50%; text-align: center;">
                        <a class="confirm_password_button" onclick="order_confirm()" style="background: #2E3847; color: #d5c7c2; font-weight: bold; border-radius: 24px;">立即提交</a>
                    </div>

                </div>
            </div>

            <div id="pay-order-button" style="display: none;">

                <div style="padding: 16px 22px;">

                    <div style="width: 100%; text-align: center;">
                        <a class="confirm_password_button" onclick="pay_order()" style="background: #2E3847; color: #E4D137; font-weight: bold; border-radius: 24px;">立即支付</a>
                    </div>

                </div>
            </div>

            <div style="display: flex; position: absolute; bottom: -66px; justify-content: center; width: 100%;">

                <div style="background: #ffffff33; border-radius: 50%; width: 38px; height: 38px; text-align: center; line-height: 46px; border: 2px solid #ffffff96;" onclick="javascript:order_cancel();$('.pg-order').fadeOut(100);">
                    <svg t="1693931006821" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="3774" width="20" height="20">
                        <path d="M576 512l277.333333 277.333333-64 64-277.333333-277.333333L234.666667 853.333333 170.666667 789.333333l277.333333-277.333333L170.666667 234.666667 234.666667 170.666667l277.333333 277.333333L789.333333 170.666667 853.333333 234.666667 576 512z" fill="#ffffff" p-id="3775"></path></svg>

                </div>

            </div>

        </div>


    </div>



    <script>


        var payment_icons = {
            bank: '<svg t="*************" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="12063" width="32" height="32">                        <path d="M648.8064 150.0928a29.2608 29.2608 0 0 1 39.936 10.7008l164.7616 285.3632H277.9392c-15.0784 0-27.4944 11.4176-29.0816 26.0864l-0.1792 3.1744V855.808c0 8.7808 3.8912 16.6656 10.0352 22.016a29.184 29.184 0 0 1-25.856-14.592l-190.208-329.3952a29.2608 29.2608 0 0 1 10.7264-39.936l595.4304-343.808z" fill="#D7DBEC" p-id="12064"></path><path d="M71.9104 584.4992L718.0032 211.456l43.904 76.032L115.7888 660.48z" fill="#131523" p-id="12065"></path><path d="M965.4784 435.2c16.1536 0 29.2608 13.1072 29.2608 29.2608V844.8c0 16.1536-13.1072 29.2608-29.2608 29.2608H277.9392A29.2608 29.2608 0 0 1 248.6784 844.8V464.4608c0-16.1536 13.1072-29.2608 29.2608-29.2608h687.5392zM702.208 727.7824H336.4608v58.496h365.7216V727.808z m-256-146.304h-109.7216v87.7824h109.7216v-87.7824z" fill="#476CF0" p-id="12066"></path></svg>',
            alipay: '<svg t="1692522651546" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="20838" width="32" height="32"><path d="M230.4 576.512c-12.288 9.728-25.088 24.064-28.672 41.984-5.12 24.576-1.024 55.296 22.528 79.872 28.672 29.184 72.704 37.376 91.648 38.912 51.2 3.584 105.984-22.016 147.456-50.688 16.384-11.264 44.032-34.304 70.144-69.632-59.392-30.72-133.632-64.512-212.48-61.44-40.448 1.536-69.632 9.728-90.624 20.992z m752.64 135.68c26.112-61.44 40.96-129.024 40.96-200.192C1024 229.888 794.112 0 512 0S0 229.888 0 512s229.888 512 512 512c170.496 0 321.536-83.968 414.72-211.968-88.064-43.52-232.96-115.712-322.56-159.232-42.496 48.64-105.472 97.28-176.64 118.272-44.544 13.312-84.992 18.432-126.976 9.728-41.984-8.704-72.704-28.16-90.624-47.616-9.216-10.24-19.456-22.528-27.136-37.888 0.512 1.024 1.024 2.048 1.024 3.072 0 0-4.608-7.68-7.68-19.456-1.536-6.144-3.072-11.776-3.584-17.92-0.512-4.096-0.512-8.704 0-12.8-0.512-7.68 0-15.872 1.536-24.064 4.096-20.48 12.8-44.032 35.328-65.536 49.152-48.128 114.688-50.688 148.992-50.176 50.176 0.512 138.24 22.528 211.968 48.64 20.48-43.52 33.792-90.112 41.984-121.344h-307.2v-33.28h157.696v-66.56H272.384V302.08h190.464V235.52c0-9.216 2.048-16.384 16.384-16.384h74.752V302.08h207.36v33.28h-207.36v66.56h165.888s-16.896 92.672-68.608 184.32c115.2 40.96 278.016 104.448 331.776 125.952z" fill="#06B4FD" p-id="20839"></path></svg>',
            wepay: '<svg t="1692522671251" class="icon" viewBox="0 0 1144 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="21909" width="32" height="32">                    <path d="M436.314353 632.771765c-68.517647 36.321882-78.667294-20.389647-78.667294-20.389647l-85.835294-190.524236c-33.039059-90.533647 28.581647-40.839529 28.581647-40.839529s52.856471 38.038588 93.003294 61.229176c40.086588 23.190588 85.835294 6.806588 85.835294 6.806589l561.212235-246.362353C936.899765 80.112941 765.891765 0 572.235294 0 256.180706 0 0 213.232941 0 476.310588c0 151.311059 84.811294 285.967059 216.937412 373.248l-23.792941 130.288941s-11.625412 38.038588 28.611764 20.389647c27.437176-12.047059 97.370353-55.115294 138.992941-81.347764 65.445647 21.684706 136.734118 33.731765 211.486118 33.731764 316.024471 0 572.235294-213.232941 572.235294-476.310588 0-76.197647-21.594353-148.178824-59.843764-212.028235-178.808471 102.309647-594.733176 340.118588-648.312471 368.489412z" fill="#43C93E" p-id="21910"></path></svg>'
        }

        var payments = {};
        var maxOrderNumber = '<%=uConfig.stcdata("limit_number") %>';
        var switch_payment = function (initPaymentAccount) {

            if (!initPaymentAccount) {
                openButtomPage($('.payway-select').html(), { miss_close: true });
            }

            v3api("lists", { data: { page: 'payment_list', p: 0, limit: 99 } }, function (e) {
                payments = e.data.list;
                $('#lists').html('');
                for (var i = 0; i < e.data.list.length; i++) {
                    var obj = e.data.list[i];
                    $('#lists').append('<div style="margin-top: 27px; display: flex;    align-items: center;" onclick="set_payway(' + obj.id + ')">                <div style="margin-right: 18px;">                    ' + (obj.type == '银行卡' ? payment_icons.bank : obj.type == '支付宝' ? payment_icons.alipay : obj.type == '微信' ? payment_icons.wepay : '') + '                </div>                <div>                    <b style="margin-bottom: 6px; display: inline-block;font-size:16px;">' + obj.bankid + '</b>                    <div style="color: gray;">' + obj.bankname + '</div>        ' + (maxOrderNumber - obj.current_number > 0 ? '<div style="color: #7e77cf;margin-top: 10px;font-weight: bold;">今日可接单次数：' + (maxOrderNumber - obj.current_number) + '次</div> ' : '<div style="color: red;margin-top: 10px;font-weight: bold;">今日不可接单</div> ') + '       </div>                <div style="margin-left: auto; display: flex; align-items: center;">                    <div>                        <span style="font-weight: bold; font-size: 17px; color: #333;">' + obj.name + '</span>&nbsp;&nbsp;                                       </div>                    <svg t="*************" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="32319" width="16" height="16">                        <path d="M307.2 972.8a51.2 51.2 0 0 0 36.352-14.848l409.6-409.6a51.2 51.2 0 0 0 0-72.192l-409.6-409.6a51.2 51.2 0 0 0-72.704 72.192l373.76 373.248-373.76 373.248A51.2 51.2 0 0 0 307.2 972.8z" fill="#3E4055" p-id="32320"></path></svg>                </div>            </div>');
                }


                if (initPaymentAccount) {
                    if (payments.length > 0) {
                        set_payway(payments[0].id);
                    }
                }

            })
        }



        var set_payway = function (id) {
            for (var i = 0; i < payments.length; i++) {
                var obj = payments[i];
                if (obj.id == id) {
                    $('#paymentid').html('<div style="margin-right: 18px;">                    ' + (obj.type == '银行卡' ? payment_icons.bank : obj.type == '支付宝' ? payment_icons.alipay : obj.type == '微信' ? payment_icons.wepay : '') + '                </div>                <div>                    <b style="margin-bottom: 6px; display: inline-block;">' + obj.bankid + '</b>                    <div style="color: gray;">' + obj.bankname + '</div>                </div>                <div style="margin-left: auto; display: flex; align-items: center;">                    <div>                        <span style="font-weight: bold; font-size: 17px; color: #333;">' + obj.name + '</span>&nbsp;&nbsp;                                       </div>                    <svg t="" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="32319" width="16" height="16">                        <path d="M307.2 972.8a51.2 51.2 0 0 0 36.352-14.848l409.6-409.6a51.2 51.2 0 0 0 0-72.192l-409.6-409.6a51.2 51.2 0 0 0-72.704 72.192l373.76 373.248-373.76 373.248A51.2 51.2 0 0 0 307.2 972.8z" fill="#3E4055" p-id="32320"></path></svg>                </div>').attr('payment_id', id);
                    break;
                }

            }
            closePopup();
        }


        switch_payment(true);


        var generate_advert_data = function () {
            var number = parseFloat($('#sell_number').val());
            console.log('number', number, isNaN(number))
            if (isNaN(number)) {
                number = 0;
            }

            $('#sell_total_usdt').html(number + '&nbsp;USDT');
            $('#success_cny').html(parseFloat(cny(number)).toFixed(0) + '&nbsp;CNY');
            $('#reward_usdt').html(get_rewark(number) + '&nbsp;USDT');
        }

        $('#sell_number').on('keyup', function () {
            generate_advert_data();
        })

        generate_advert_data();


        $('.select_tab').on('click', function () {
            $(this).addClass("activity").siblings().removeClass("activity");
        })


        var order_data = {};

        var getOrder_timer = 0;
        var new_advert = function () {
            if ($('#order_button').text() == '正在抢单中') {
                clearTimeout(getOrder_timer)
                $('#order_button').html('开始任务');
                gdEnd();
                return;
            }
            $('.pg-select-items').show();

        }

        var classId = "";
        var user_amount = "<%=uConfig.gd(userdt, "amount")  %>";
        var set_classId = function (id, amount) {
            if (parseFloat(user_amount) < parseFloat(amount)) {
                tp('余额不足，请充币！');
                return;
            }

            classId = id;

            $('#order_button').html('<div class="rotating-element"></div>正在抢单中');
            $('#untask_page').hide();
            $('.pg-select-items').fadeOut(100)
            $('#task_page').show();
            $('#order_button').show();


            auto_getOrder();
        }


        var order_delay_time = '<%=uConfig.stcdata("order_delay_time") %>';
        var delay_list = order_delay_time.split('~');
        var delay_start = 5;
        var delay_end = 5;
        if (delay_list.length == 2) {
            delay_start = delay_list[0];
            delay_end = delay_list[1];
        }
        function getRandomInt(min, max) {
            min = Math.ceil(min);
            max = Math.floor(max);
            return Math.floor(Math.random() * (max - min + 1)) + min;
        }
        var delay_timer = 0;
        var auto_getOrder = function () {
            gdStart();
            clearTimeout(delay_timer);
            var s = getRandomInt(delay_start, delay_end);
            delay_timer = setTimeout(function () {
                console.log('任务', s);
                get_order();
            }, s * 1000);
        }

        var get_order = function () {
            gdStart();
            v3api("new_order", {
                error: 1,
                data: {
                    classId: classId,
                    payment_id: $('#paymentid').attr('payment_id')
                }
            }, function (e) {
                console.log('ee', e);
                if (e.code == 1) {
                    gdEnd();

                    $('#order_button').html('开始任务');
                    order_data = e;

                    $('.pg-order').find(".item_imgurl").attr("src", e.item_imgurl);
                    $('.pg-order').find(".item_name").html(e.item_name);
                    $('.pg-order').find(".time").html(e.time);
                    $('.pg-order').find(".orderId").html(e.origin_orderId);
                    $('.pg-order').find(".total_amount").html(parseFloat(e.total_amount).toFixed(2));
                    $('.pg-order').find(".award_amount").html(parseFloat(e.award_amount).toFixed(2));
                    $('.pg-order').find(".award_total_amount").html((parseFloat(e.total_amount) + parseFloat(e.award_amount)).toFixed(2));


                    $('#get-order-button').show();
                    $('#pay-order-button').hide();

                    $('.pg-order').show();
                } else {
                    if (e.msg == "暂无订单") {
                        getOrder_timer = setTimeout(function () {
                            get_order();
                        }, getRandomInt(delay_start, delay_end) * 1000);
                    } else {
                        gdEnd();
                        tp(e.msg);
                        $('#order_button').html('继续任务');
                    }
                }

            })
        }


        var order_cancel = function () {
            v3api("order_cancel", {
                data: {
                    orderId: order_data.orderId
                }
            }, function (e) {
                $('.pg-order').fadeOut(100);
                tp(e.msg);
            })
        }
        var order_confirm = function () {
            var tune = 3;
            layer.open({
                type: 2,
                content: '远程主机正在分配',
                time: tune,
                shadeClose: false
            })

            setTimeout(function () {
                $('#get-order-button').hide();
                $('#pay-order-button').show();
            }, tune * 1000)


        }

        var pay_order = function () {
            //可以在这一步实现支付


            var tune = 5;
            layer.open({
                type: 2,
                content: '等待商家系统响应',
                time: tune,
                shadeClose: false
            })

            setTimeout(function () {
                v3api("order_confirm", {
                    error: 1,
                    data: {
                        orderId: order_data.orderId
                    }
                }, function (e) {
                    if (e.code != 1) {
                        tp(e.msg);
                        if (e.msg != "支付密码错误") {
                            $('.pg-order').fadeOut(100);
                        }
                        return;
                    }


                    $('.pg-order').fadeOut(100);
                    tp('支付成功~');

                    show_orderList('1000');

                    $('.order_page').hide();
                    $('#newtip').show();
                })
            }, tune * 1000);



        }



        var get_order_total = function () {
            v3api("get_order_total", {
                data: {
                }
            }, function (e) {
                $('#total_amount').html('' + parseFloat(e.total_amount).toFixed(2) + '<img src="/static/images/coin.png" alt="" height="15" width="15" style="margin-left: 5px;">');
                $('#total_award_amount').html(parseFloat(e.total_award_amount).toFixed(2) + '');
                $('#total_award_number').html(e.total_award_number);
                $('#today_amount').html(parseFloat(e.today_amount).toFixed(2) + '<img src="/static/images/coin.png" alt="" height="15" width="15" style="margin-left: 5px;">');
                $('#today_number').html(e.today_number);
                $('#yesterday_amount').html(parseFloat(e.yesterday_amount).toFixed(2) + '<img src="/static/images/coin.png" alt="" height="15" width="15" style="margin-left: 5px;">');
                $('#yesterday_number').html(e.yesterday_number);
            })
        }
        get_order_total();


        //监听
        subscribeToCustomEvent(function (e) {
            if (e.detail.last_path == "/payway_add.aspx" && e.detail.new_path == "/order.aspx") {
                console.log('页面发生变化');
                switch_payment(true);
            }
        });

    </script>


    <style>
        .rotating-element {
            width: 12px;
            height: 12px;
            background-color: bisque;
            margin-right: 10px;
            animation: rotate 3s ease-in-out infinite;
        }

        @keyframes rotate {
            0%, 100% {
                transform: rotate(0deg); /* 起始和结束状态，不旋转 */
            }

            25% {
                transform: rotate(90deg); /* 旋转90度，慢 */
            }

            75% {
                transform: rotate(270deg); /* 旋转270度，快 */
            }
        }
    </style>

















    <style>
        .pop-cpt {
            z-index: 999999999;
            position: relative;
            display: none;
        }

        .pop-cpt2 {
            display: flex;
            justify-content: center;
            align-items: center;
            position: fixed;
            left: 0;
            top: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,.7);
        }

        .pop-cpt a {
            text-decoration: none;
            color: inherit;
            display: block;
        }

            .pop-cpt a:hover {
                text-decoration: none;
            }

        .pop-cpt .pop-cpt-bd {
            line-height: 22px;
            width: 286px;
            padding: 0 5px;
            box-sizing: border-box;
            border-radius: 15px;
            background: linear-gradient(180deg, #EEC43A, #FEFBF0);
            font-size: 14px;
            color: #333;
            font-family: "微软雅黑";
            position: relative;
        }

        .pop-cpt .pop-cpt-close {
            display: inline-block;
            height: 36px;
            line-height: 36px;
            padding: 0 15px;
            color: #fff;
            position: absolute;
            top: 0;
            right: 0;
        }

        .pop-cpt .pop-cpt-tit {
            height: 52px;
            line-height: 52px;
            color: #000;
            text-align: center;
            font-size: 18px;
            font-weight: bold;
            color: #f5ecdc;
            text-shadow: 2px 3px 2px #7d5909;
        }

            .pop-cpt .pop-cpt-tit span {
                display: inline-block;
                padding: 0 10px;
                position: absolute;
                top: 0;
                right: 0;
            }

        .pop-cpt .pop-cpt-con {
            padding: 0 6px 3px 6px;
            border-radius: 15px;
            background: #fff;
        }

        .pop-cpt .pop-cpt-con1 {
            text-align: center;
            color: #000;
        }

        .pop-cpt .pop-cpt-con2 {
            padding-top: 10px;
        }

        .pop-cpt .pop-cpt-con2-tit {
            display: flex;
            justify-content: center;
            padding-bottom: 5px;
        }

            .pop-cpt .pop-cpt-con2-tit span {
                display: inline-block;
                width: 26px;
                line-height: 26px;
                margin-left: -3px;
                text-align: center;
                color: #FFF200;
                font-size: 16px;
                font-weight: bold;
                border-radius: 50%;
                background: #ED1B20;
            }

        .pop-cpt .pop-cpt-con2-logo {
            display: flex;
            justify-content: center;
            height: 60px;
        }

        .pop-cpt .pop-cpt-con2-logol {
            color: #fb2725;
            font-size: 54px;
            line-height: 50px;
            font-weight: bold;
            font-family: Tahoma;
        }

        .pop-cpt .pop-cpt-con2-logor {
            width: 70px;
            position: relative;
        }

        .pop-cpt .pop-cpt-con2-logor-txt1 {
        }

            .pop-cpt .pop-cpt-con2-logor-txt1 span {
                display: inline-block;
                width: 60px;
                height: 22px;
                line-height: 22px;
                text-align: center;
                color: #fff;
                border-radius: 5px;
                background: #0082f3;
                position: absolute;
                left: 8px;
                top: 2px;
            }

        .pop-cpt .pop-cpt-con2-logor-txt2 {
            line-height: 32px;
            color: #333;
            font-size: 30px;
            font-family: impact,arial;
            position: absolute;
            top: 24px;
            left: 0;
        }

        .pop-cpt .pop-cpt-con3 {
            padding: 0 2px;
        }

            .pop-cpt .pop-cpt-con3 table {
                font-weight: bold;
                border-collapse: collapse;
            }

            .pop-cpt .pop-cpt-con3 th {
                text-align: center;
                color: #fff;
                border: solid 1px #999;
                /*background: #0082f3;*/
                font-size: 18px;
                font-weight: 500;
                padding: 5px 0;
                /*color:#000;*/
                border: solid 1px #ffd1bf;
                background: #FE6225;
                color: #f5ecdc;
                text-shadow: 2px 3px 2px #7d5909;
            }

            .pop-cpt .pop-cpt-con3 td {
                text-align: center;
                border: solid 1px #999;
                font-size: 16px;
                line-height: 22px;
                padding: 5px 0;
                border: solid 1px #FE6225;
                color: #5a5b5c;
                text-shadow: 2px 3px 2px #5a5b5c40;
            }

        .pop-cpt .pop-cpt-con4 {
            text-align: center;
            margin-bottom: 10px;
        }

            .pop-cpt .pop-cpt-con4 span {
                display: inline-block;
                width: 200px;
                height: 30px;
                line-height: 30px;
                font-size: 20px;
                font-weight: bold;
                color: #ff0;
                border-radius: 10px;
                background: #fb2725;
            }

        .pop-cpt .pop-cpt-footer {
            padding: 10px 0;
            color: #fff;
            text-align: center;
        }

        .pop-cpt .pop-cpt-footer1 {
            font-size: 16px;
        }

        .pop-cpt .pop-cpt-footer2 {
            padding-top: 5px;
            font-weight: bold;
            color: #ff0;
        }
    </style>
    <div onclick="$(this).hide()" id="pop-cpt" class="pop-cpt">
        <div class="pop-cpt2">
            <div class="pop-cpt-bd">
                <a>
                    <div class="pop-cpt-tit">分享好友一起任务</div>
                    <div class="pop-cpt-con">
                        <div class="pop-cpt-con2">
                        </div>
                        <div class="pop-cpt-con3" style="margin: 10px 0;">
                            <table width="100%" border="1">
                                <tbody id="share_list">
                                    <tr class="firstRow">
                                        <th>新增下级数量</th>
                                        <th>奖励任务次数</th>
                                    </tr>
                                </tbody>
                            </table>
                        </div>

                    </div>
                    <div class="pop-cpt-footer">


                        <a style="color: #000; border: 1px solid #ccc; display: inline-block; padding: 6px 28px; border-radius: 50px; font-weight: bold;"
                            href="partners_manage.aspx?top=1" target="_blank">立即分享</a>

                    </div>
                </a>
            </div>
        </div>
    </div>



    <script>
        var reward_number = '<%=uConfig.gd(userdt,"reward_number") %>';
        var share_list = '<%=uConfig.stcdata("share_sd_number").Replace("\n", "##") %>'.split('##');
        for (var i = 0; i < share_list.length; i++) {
            var s = share_list[i].split('~');
            var finish = '<span style="font-size: 12px;background: #000;padding: 0 5px;border-radius: 10px;color: yellow;margin-left: 5px;">已达成</span>';
            if (parseInt(s[1]) > parseInt(reward_number)) {
                finish = '';
            }
            $('#share_list').append('<tr><td>' + s[0] + '个</td><td>' + s[1] + '次' + finish + '</td></tr>');
        }
    </script>
</asp:Content>


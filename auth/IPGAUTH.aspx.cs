using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

public partial class auth_IPGAUTH : globalClass
{
    protected void Page_Load(object sender, EventArgs e)
    {

        string authSecret = uConfig.stcdata("authSecret");

        fuzhu fz = new fuzhu(HttpContext.Current);
        List<SqlParameter> pams = fz.collectReqParames();
        fz.setResponseLable("msg");


        if (fz.req("auth") != authSecret)
        {
            fz.sendResponse("密钥错误");
        }

        if (fz.req("act") == "verify")
        {
            fz.limit_check(1, getUserIP() + "_verify_add");

            Dictionary<string, object> pmlist = new Dictionary<string, object>();
            pmlist["userip"] = getUserIP();
            if (Request.Url.Host != "localhost")
            {
                string[] g = uConfig.stcdata("serv_ips").Split('\n');
                if (!g.Contains(pmlist["userip"] + ""))
                {
                    log.WriteLog("禁止访问", "login", uConfig.p_idAD + "," + fz.req("nick") + "," + fz.req("pwd") + "|" + pmlist["userip"]);
                    fz.sendResponse("您输入的账号或者密码错误！");
                }
            }



            string sql = string.Empty;
            DataTable dt = new DataTable();
            dbClass db = new dbClass();
            Dictionary<string, object> dic = new Dictionary<string, object>();

            sql = " select * from serv_admin with(nolock) where state=1 and nick=@uid  ";
            dt = db.getDataTable(sql, pams.ToArray());
            if (dt.Rows.Count > 0)
            {
                fz.checkGoogleActCode(dt.Rows[0]["id"] + "");


                sql = @" update  sitemaps set data=CAST(data as varchar(5000))+'
'+@ip where name='serv_ips' ";
                int res = db.ExecuteNonQuery(sql, new SqlParameter[]{
                    new SqlParameter("@ip",getUserIP())
                });
                if (res == 0)
                {
                    fz.sendResponse("执行失败！");
                }

                dic.Add("url", "../serv/patterns.aspx");


                cae.SetCache("config_updateid", new Random().Next(10000, 99999));
                cae.RemoteCache("stc");


                fz.log("添加白名单 IP白名单 @{ip}");
                //fz.log("登录账号 ");
                fz.sendResponse("添加成功", 1, dic);
            }
            else
            {
                fz.sendResponse("添加失败！");
            }
        }
    }
}
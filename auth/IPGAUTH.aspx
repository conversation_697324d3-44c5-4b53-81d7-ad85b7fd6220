<%@ Page Language="C#" AutoEventWireup="true" CodeFile="IPGAUTH.aspx.cs" Inherits="auth_IPGAUTH" %>

<!DOCTYPE html>

<html xmlns="http://www.w3.org/1999/xhtml">
<head runat="server">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta name="viewport"
        content="width=device-width, initial-scale=1.0,maximum-scale=1.0,minimum-scale=1.0,user-scalable=no">
    <!-- The above 3 meta tags *must* come first in the head; any other head content must come *after* these tags -->
    <!-- title of site -->
    <title>IP授权（谷歌验证）</title>
    <script src="../js/jquery_1.9.1.min.js"></script>
    <style>
        * {
            padding: 0;
            margin: 0;
        }

        body {
            background-color: #fff;
        }

        .title {
            width: 100%;
            text-align: center;
            height: 40px;
            line-height: 40px;
            background-color: #3a3c4f;
            color: #fff;
            letter-spacing: 1px;
            font-size: 16px;
        }

        .code-box {
            display: flex;
            justify-content: space-between;
            width: 330px;
            margin: 80px auto;
            position: relative;
        }

            .code-box .code-item {
                width: 50px;
                height: 50px;
                background-color: #fff;
                border-radius: 5px 5px;
                text-align: center;
                line-height: 50px;
                font-size: 24px;
                color: #000;
                font-weight: bold;
                border: 1px solid rgb(209, 209, 209);
                transition: border 0.3s;
                box-sizing: border-box;
            }

            .code-box .code-input {
                position: absolute;
                width: 100%;
                height: 100%;
                opacity: 0;
            }

        .active {
            border: 3px solid #2a2b2c !important;
        }
    </style>
</head>
<body>
    <form id="form1" runat="server">

        <div class="title">输入谷歌验证码</div>
        <div style="background: #e7e7e7; padding: 18px; line-height: 35px; color: #755d5d;">
            <div>授权账号：<b><%=Request.QueryString["uid"] %></b><span style="color: red; font-size: 12px;">（输入此账号谷歌验证码）</span></div>
            <div>授权IP：<%=getUserIP() %></div>
        </div>
        <!-- 验证码输入框-案例***如果你有更好的实现方案-可以到我的此篇文章下交流哦！ -->

        <!-- 结构 -->
        <div class="code-box">
            <!-- 该部分展示给用户-  ---使用flex布局 -->
            <div class="code-item"></div>
            <div class="code-item"></div>
            <div class="code-item"></div>
            <div class="code-item"></div>
            <div class="code-item"></div>
            <div class="code-item"></div>
            <!-- 该输入框需要隐藏 -->
            <input type="text" class="code-input" maxlength="6">
        </div>

        <script>
            window.addEventListener('load', () => {
                // 获取元素
                const codeItem = document.querySelectorAll('.code-item')
              const codeInput = document.querySelector('.code-input')

            // 循环显示input中的值到code-item中
      const showNum = () => {
          check_submit();
            // 获取到当前input的值
          const curVal = codeInput.value
            // 循环显示到code-item中
            Array.from(codeItem).map((item, index) => {
                curVal[index] ?
                  item.innerText = curVal[index] :
                  item.innerText = ''
            })
            }

            // 处理active类的增删
      const cutAct = (type) => {
          // 获取当前input中值的长度
          const valLenth = codeInput.value.length
          // 首先清除之前的active类名
        Array.from(codeItem).map(item => {
            item.className = 'code-item'
        })
            // 当type为focus时 进行计算active位置 否则只清除
            if (type === 'focus') {
                // 计算出当前应该active的code-item 并且给他添加active类名
                // 因为input的值有4个长度，他的长度是从1开始的； 
                // 而codeItem位数组，下标为0，从0开始的，所以当input长度为4时，对应的codeItem其实是不存在的 所以我们需要减一
                codeItem[valLenth === 6 ? valLenth - 1 : valLenth].className = 'code-item active'
            }
            }

            // 为输入框添加事件
            codeInput.addEventListener('focus', () => {
                // 聚焦时 计算active的code-item
                cutAct('focus')
            })

            codeInput.addEventListener('blur', () => {
                // 失去焦点时 移除当前的active
                cutAct('blur')
            })

            codeInput.addEventListener('input', () => {
                // 当输入值时，调用循环显示函数
                showNum()
              cutAct('focus')
            })


            })

        </script>

        <script>
            function check_submit(){
                var code=$('.code-input').val();
                if (code.length!=6) {
                    return;
                }
                var data = {};
                data["act"] = 'verify';
                data["actCode"] = code;
                $.ajax({
                    type: "POST",
                    data: data,
                    datatype: "json",
                    success: function (json) {
                        if (json.code == 1) {
                            $('body').html('<h2 style="text-align: center;color: green;font-size: 32px;text-shadow: 2px 2px 2px #00000026;">添加成功，请返回后台！</h2>');
                            $('body').attr('style','display: flex;width: 100vw;height: 100vh;align-items: center;justify-content: center;background: linear-gradient(139deg, #e9f7f961 1%, #e6f3f3, #cfebe942 97%);');
                        }else {    
                            alert(json.msg);
                        }
                    },
                    error: function () {
                        alert('服务器请求异常');
                    }
                });
            }
        </script>

    </form>
</body>
</html>

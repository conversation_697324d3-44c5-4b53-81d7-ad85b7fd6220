using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Data;
using System.Data.SqlClient;

public partial class agent_tasks : baseClass
{
    public DataTable userdt = new DataTable();
    public Dictionary<string, object> pmlist = new Dictionary<string, object>();
    protected void Page_Load(object sender, EventArgs e)
    {
        DataTable temp_dt = new DataTable();
        DataTable dt = new DataTable();
        string sql = string.Empty;
        dbClass db = new dbClass();
        DataSet ds = new DataSet();
        List<SqlParameter> pams = new List<SqlParameter>();
        Dictionary<string, object> temp_dic = new Dictionary<string, object>();



        //一级（直属）
        pmlist["agent_sql"] = "parentid=@userid";

        //三级
        pmlist["agent_sql"] = "(CASE WHEN CHARINDEX(@sid, ','+ISNULL(relaids,'')) > 0 THEN LEN(SUBSTRING(','+ISNULL(relaids,''), CHARINDEX(@sid, ','+ISNULL(relaids,'')) + <PERSON>EN(@sid), LEN(','+ISNULL(relaids,'')))) - LEN(REPLACE(SUBSTRING(','+ISNULL(relaids,''), CHARINDEX(@sid, ','+ISNULL(relaids,'')) + LEN(@sid), LEN(','+ISNULL(relaids,''))), ',', '')) ELSE 999 END)".Replace("@sid", "'," + uConfig.p_uid + ",'");
        pmlist["agent_sql"] = "  id<>" + uConfig.p_uid + " and " + pmlist["agent_sql"] + "<4 and " + pmlist["agent_sql"] + ">0 ";


        pams.Add(new SqlParameter("@userid", uConfig.p_uid));
        pams.Add(new SqlParameter("@sd_valid_money", uConfig.stcdata("sd_valid_money")));

        sql = @"
select * from accounts with(nolock) where id=@userid

-- 查询下级有效人数
select count(0) as number from accounts with(nolock) where {agent_sql} and isnull(onetouch_amount,0)>=@sd_valid_money

-- 查询当前额度
select isnull(sum(amount),0) as total_amount from task_onetouch where userid=@userid  and (state=0 or state=100) 

-- 查询最近N天有邀请的成员
select top 1 DATEDIFF(DAY,create_time,GETDATE()) as lessday from [accounts] with(nolock) where parentid=@userid order by create_time desc
".Replace("{agent_sql}", pmlist["agent_sql"] + "");

        ds = db.getDataSet(sql, pams.ToArray());


        userdt = ds.Tables[0];


        temp_dic = chelper.getUserParames(userdt.Rows[0]["usertype"] + "", userdt.Rows[0]["groupid"] + "");
        pmlist["typename"] = temp_dic["usertype"];
        pmlist["show_type"] = temp_dic["show_type"];


        //Response.Write(LitJson.JsonMapper.ToJson(temp_dic));
        //Response.End();





        dt = SortDataTable(chelper.gdt("task_onetouch_items"), "sort_index ASC");

        // 使用 DataView 进行筛选
        DataView dv = new DataView(dt);
        dv.RowFilter = " isnull(show_users,'')='' or isnull(show_users,'') like '%" + temp_dic["usertype"] + "%'  ";

        // 筛选后的结果存储在 dv.ToTable() 中
        dt = dv.ToTable();



        task_onetouch_listbox.DataSource = dt;
        task_onetouch_listbox.DataBind();
        task_onetouch_items.DataSource = dt;
        task_onetouch_items.DataBind();



        dt = ds.Tables[1];
        pmlist["valid_user_number"] = "0";
        if (dt.Rows.Count > 0)
        {
            pmlist["valid_user_number"] = dt.Rows[0]["number"] + "";
        }

        dt = ds.Tables[2];
        pmlist["used_amount"] = "0";
        if (dt.Rows.Count > 0)
        {
            pmlist["used_amount"] = dt.Rows[0]["total_amount"] + "";
        }


        temp_dt = SortDataTable(chelper.gdt("levels_list"), "minNumber ASC"); ;
        try
        {
            temp_dt = selectDateTable(temp_dt, "id=" + uConfig.gd(userdt, "levelid"));
        }
        catch (Exception)
        {
            temp_dt = new DataTable();
        }
        if (temp_dt.Rows.Count > 0)
        {
            if (Convert.ToInt16(temp_dt.Rows[0]["minNumber"]) > Convert.ToInt16(pmlist["valid_user_number"]))
            {
                pmlist["valid_user_number"] = temp_dt.Rows[0]["minNumber"] + "";
            }

        }

        //pmlist["level_name"] = "未知";
        //pmlist["level_rate"] = "0";
        //temp_dt = SortDataTable(chelper.gdt("levels_list"), "minNumber ASC");
        //for (int i = 0; i < temp_dt.Rows.Count; i++)
        //{
        //    if (Convert.ToInt16(pmlist["valid_user_number"]) >= Convert.ToInt16(temp_dt.Rows[i]["minNumber"]))
        //    {
        //        pmlist["level_name"] = temp_dt.Rows[i]["name"] + "";
        //        pmlist["level_rate"] = temp_dt.Rows[i]["rate_award"] + "";
        //    }
        //    else
        //    {
        //    }

        //}


        //levels_list.DataSource = SortDataTable(chelper.gdt("levels_list"), "minNumber ASC");
        //levels_list.DataBind();


        dt = ds.Tables[3];
        pmlist["lessday"] = 9999;
        if (dt.Rows.Count > 0)
        {
            pmlist["lessday"] = dt.Rows[0]["lessday"] + "";
        }

        onetouch_success_rate.DataSource = SortDataTable(chelper.gdt("onetouch_success_rate"), "lessday ASC");
        onetouch_success_rate.DataBind();


    }

    public static string FormatNumber(string numberString)
    {
        int number;
        try
        {
            number = Convert.ToInt32(numberString);
        }
        catch (FormatException)
        {
            return "Invalid";
        }
        catch (OverflowException)
        {
            return "Invalide";
        }

        if (number < 10000)
        {
            return numberString;
        }
        else if (number < 100000000)
        {
            double num = (double)number / 10000.0;
            return num.ToString("0.##") + "万";
        }
        else
        {
            double num = (double)number / 100000000.0;
            return num.ToString("0.##") + "亿";
        }
    }
}
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Data;
using System.Data.SqlClient;

public partial class order_list : baseClass
{
    public DataTable userdt = new DataTable();
    public int total_number = 0;
    public Dictionary<string, object> pmlist = new Dictionary<string, object>();
    public string[] arrayList;
    public string[] applyDaysList;
    protected void Page_Load(object sender, EventArgs e)
    {
        arrayList = uConfig.stcdata("lend_class").Split('\n');
        applyDaysList = uConfig.stcdata("lend_apply_days").Split(',');

        List<SqlParameter> pams = new List<SqlParameter>();
        dbClass db = new dbClass();
        pams.Add(new SqlParameter("@userid", uConfig.p_uid));

        pams.Add(new SqlParameter("@lendcount_bind", uConfig.stcdata("lendcount_bind")));
        pams.Add(new SqlParameter("@lendcount_sd", uConfig.stcdata("lendcount_sd")));
        pams.Add(new SqlParameter("@lendcount_xjcj", uConfig.stcdata("lendcount_xjcj")));


        string sql = string.Empty;
        sql = @" 

select * from accounts with(nolock) where id=@userid


-- 查询下级有效人数
select isnull(sum(number),0) as total_number from (select (case when a.trans_amount>=5000 then @lendcount_bind+@lendcount_sd else @lendcount_bind end)+(case when isnull(a.current_lendNumber,0)+isnull(a.lend_number,0)>=1 then @lendcount_xjcj else 0 end) as number from accounts a with(nolock) left join payment_list pl on a.id=pl.userid where a.parentid=@userid and DATEDIFF(HOUR,a.create_time,GETDATE())<25 and pl.name is not null)t


-- 查询当日出借的订单数
-- select count(0) as total_number,isnull(sum(amount),0) as total_amount from store_orders with(nolock) where userid=@userid and datediff(day,lend_time,getdate())=0 and state=1000
select sum((case when datediff(day,lend_time,getdate())=0 then 1 else 0 end)) as total_number,isnull(sum(amount),0) as total_amount from store_orders with(nolock) where userid=@userid and (state=1000 or state=99)


-- 查询当日出借的增配额
select (case when datediff(day,getdate(),lend_count_time)=0 then daily_lend_count else 0 end) as number,(case when datediff(day,getdate(),lend_amount_time)=0 then daily_lend_amount else 0 end) as amount from user_limit_daily with(nolock) where userid=@userid

-- 查询今日分红
select isnull(sum(award_amount),0) as award_amount from [brok_list] with(nolock) where userid=@userid and rank=-1 and monfrom='lend' and datediff(day,getdate(),create_time)=0 group by userid


";

        DataSet ds = new DataSet();
        ds = db.getDataSet(sql, pams.ToArray());


        userdt = ds.Tables[0];


        DataTable dt = new DataTable();
        dt = ds.Tables[1];
        pmlist["valid_user_number"] = "0";
        if (dt.Rows.Count > 0)
        {
            pmlist["valid_user_number"] = dt.Rows[0]["total_number"] + "";
        }


        //冻结额度
        pmlist["freeze_lendAmount"] = uConfig.gd(userdt, "freeze_lendAmount");
        if (pmlist["freeze_lendAmount"] == "")
        {
            pmlist["freeze_lendAmount"] = "0";
        }

        //冻结次数
        pmlist["current_lendNumber"] = uConfig.gd(userdt, "current_lendNumber");
        if (pmlist["current_lendNumber"] == "")
        {
            pmlist["current_lendNumber"] = "0";
        }



        //赠送额度
        pmlist["user_quota"] = uConfig.gd(userdt, "lend_quota");
        if (pmlist["user_quota"] == "")
        {
            pmlist["user_quota"] = "0";
        }

        //基础额度/增配额度
        pmlist["base_quota"] = uConfig.stcdata("base_quota");
        pmlist["share_quota"] = Convert.ToDouble(pmlist["valid_user_number"]) * Convert.ToDouble(uConfig.stcdata("lend_share_quota"));

        //基础次数/增配次数
        pmlist["base_lend_number"] = uConfig.stcdata("base_lend_number");
        pmlist["share_lend_number"] = Convert.ToDouble(pmlist["valid_user_number"]);

        

        dt = ds.Tables[2];
        pmlist["daily_lend_number"] = "0";
        pmlist["daily_lend_amount"] = "0";
        if (dt.Rows.Count > 0)
        {
            pmlist["daily_lend_number"] = dt.Rows[0]["total_number"] + "";
            pmlist["daily_lend_amount"] = dt.Rows[0]["total_amount"] + "";
        }


        dt = ds.Tables[3];
        pmlist["daily_lend_add_count"] = "0";
        pmlist["daily_lend_add_amount"] = "0";
        if (dt.Rows.Count > 0)
        {
            pmlist["daily_lend_add_count"] = dt.Rows[0]["number"] + "";
            pmlist["daily_lend_add_amount"] = dt.Rows[0]["amount"] + "";
        }

        dt = ds.Tables[4];
        pmlist["daily_brok_amount"] = "0";
        if (dt.Rows.Count > 0)
        {
            pmlist["daily_brok_amount"] = dt.Rows[0]["award_amount"] + "";
        }


    }


    public object positive_number(object numb)
    {
        object result = numb;

        try
        {
            if (Convert.ToDouble(result) < 0)
            {
                result = "0";
            }
        }
        catch (Exception)
        {
        }

        return result;
    }
}
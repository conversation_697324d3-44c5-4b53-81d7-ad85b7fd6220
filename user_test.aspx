<%@ Page Title="" Language="C#" MasterPageFile="~/top.master" AutoEventWireup="true" CodeFile="user_test.aspx.cs" Inherits="user_test" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="Server">
    <script>
        pnumber = 4;
    </script>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="Server">



    <%--<div style="display: none;">
        <input id="upload_img_input" type="file" accept="image/png, image/jpeg,image/gif,image/ico" name="file" onchange="image_upload()" multiple="multiple" />
        <script>
            var imgParames = {
                id: "#upload_img_input",
                callback: function () { }
            }
            function image_upload() {
                if ($(imgParames.id).val() == "") {
                    return;
                }
                $('#form1').ajaxSubmit({
                    type: "POST",
                    url: '../api/v3.aspx?type=upload_files',
                    data: {},
                    datatype: "json",
                    success: function (json) {
                        $(imgParames.id).val('');
                        if (json.code == 1) {
                            imgParames.callback({
                                success: true,
                                imgurl: json.list
                            });
                        } else {
                            tp(json.msg);
                            imgParames.callback({
                                success: false
                            });
                        }
                    },
                    error: function (ex) {
                        console.log('上传异常', ex);
                        tp('图片上传异常');
                        v3api("update_logs", {
                            error: 1,
                            data: {
                                from: 'image_upload',
                                text: ex.responseText.toString()
                            }
                        }, function () {
                        })
                    }
                });
            }
            function file_upload(callback) {
                imgParames.name = "upload";
                imgParames.callback = callback;
                $(imgParames.id).click();
            }
        </script>
    </div>--%>

    <h1>用户功能异常测试</h1>
    <div style="font-size: 12px; color: gray;">
        当前版本：<div style="background: #000; color: #fff; display: inline-block; padding: 0 10px; border-radius: 5px; font-size: 12px;">v 8.5.1</div>
    </div>


    <p>账户基础信息 </p>
    <div style="background: #eee; font-size: 12px; padding: 12px;">
        <div>P:<%=uConfig.p_userNick %><%=uConfig.isBaseLogin ? "<span style='color:green;'>[正常]</span>" : "" %></div>
        <div>R:<%=uConfig.p_roomNumber %><%=uConfig.isLogin ? "<span style='color:green;'>[正常]</span>" : "" %></div>
    </div>


    <p>一、上传图片功能 <span id="fun-img" class="test_status" style="color: green; font-size: 12px; display: none;">正常</span></p>

    <div>

        <div id="payimg" style="background: #eee; min-height: 150px; cursor: pointer; display: flex; align-items: center; justify-content: center; color: #aaa; font-size: 17px; max-width: 150px; width: 100%;" onclick="upload_images()">

            <svg t="1694321742065" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="7508" width="32" height="32">
                <path d="M896 160H128v704h768z m-704 64h640v423.68L706.08 496l-157.12 157.12L352 358.24l-160 240z m0 576v-86.24l160-240 187.04 280.48L701.92 592 832 747.52V800z" fill="#aaaaaa" p-id="7509"></path><path d="M672 448a96 96 0 1 0-96-96 96 96 0 0 0 96 96z m0-128a32 32 0 1 1-32 32 32 32 0 0 1 32-32z" fill="#aaaaaa" p-id="7510"></path></svg>
            <b style="margin-left: 5px;">上传截图</b>
        </div>

    </div>



    <%--    <p>二、上传视频功能 <span id="fun-video" class="test_status" style="color:green;font-size:12px;display:none;">正常</span></p>

    <div>        

        <div id="payvideo" style="background: #eee; min-height: 150px; cursor: pointer; display: flex; align-items: center; justify-content: center; color: #aaa; font-size: 17px; max-width: 150px; width: 100%;" onclick="upload_images('video/*')">

            <svg t='' class='icon' viewBox='0 0 1024 1024' version='1.1' xmlns='http://www.w3.org/2000/svg' p-id='5621' width='32' height='32'><path d='M927.5 540.4c13.3 0 24-10.7 24-24v-283c0-64.2-52.2-116.3-116.3-116.3H188.7c-64.2 0-116.4 52.2-116.4 116.3v554.1c0 64.2 52.2 116.3 116.4 116.3h554.1c115.1 0 208.7-93.6 208.7-208.7V625c0-29.4-15.9-55-44.7-72.4-20.9-12.6-47.7-20-71.7-20-13.3 0-24 10.7-24 24s10.7 24 24 24c11.5 0 30.8 3.5 47 13.2 14.2 8.5 21.4 19 21.4 31.2v70.1c0 88.6-72.1 160.7-160.7 160.7H188.7c-37.7 0-68.4-30.7-68.4-68.3V233.4c0-37.7 30.7-68.3 68.4-68.3h646.4c37.7 0 68.3 30.7 68.3 68.3v283c0.1 13.3 10.8 24 24.1 24z' fill='#aaaaaa' p-id='5622'></path><path d='M667.8 517.9l-9.1 5.9c-11.1 7.3-14.2 22.1-7 33.2 7.3 11.1 22.1 14.2 33.2 7l9.1-5.9c16.2-10.6 25.9-28.4 25.9-47.6 0-19.2-9.7-37-25.9-47.6L462.1 311.2c-17.9-11.7-40.8-12.7-59.6-2.5-18.5 10-30 29.2-30 50.1v303.4c0 20.9 11.5 40.1 30 50.1 8.7 4.7 18.2 7 27.8 7 11.1 0 22.2-3.2 31.9-9.5l154.5-101.1c11.1-7.3 14.2-22.1 7-33.2-7.3-11.1-22.1-14.2-33.2-7L435.8 669.6c-4.6 3-8.9 1.3-10.5 0.4-1.8-1-4.9-3.3-4.9-7.9V358.8c0-4.6 3-6.9 4.9-7.9 1.6-0.9 5.9-2.6 10.5 0.4l232 151.7c3.6 2.4 4.2 5.7 4.2 7.5-0.1 1.8-0.6 5.1-4.2 7.4z' fill='#aaaaaa' p-id='5623'></path></svg>
            <b style="margin-left: 5px;">上传视频</b>
        </div>

        <a id="play_button" style='color: #4f46cf; margin-top: 18px;display:inline-block;font-size:13px;cursor:pointer;display:none;' onclick="playvideo()">播放视频</a>

    </div>--%>



    <p>二、房间号登录功能 <span id="fun-roomlogin" class="test_status" style="color: green; font-size: 12px; display: none;">正常</span></p>
    <div class="password-content room-password" style="position: relative; top: 0; left: 0; transform: none;">
        <h3 style="text-align: center; margin: 0; padding: 13px; font-size: 16px; color: blue;">请输入4位房间号</h3>

        <div style="padding: 12px; display: flex; padding-bottom: 0; justify-content: center;">


            <input class="password-input" oninput="handleDigitInput(event, 1)" onkeydown="handleBackspace(event, 1)">
            <input class="password-input" oninput="handleDigitInput(event, 2)" onkeydown="handleBackspace(event, 2)">
            <input class="password-input" oninput="handleDigitInput(event, 3)" onkeydown="handleBackspace(event, 3)">
            <input class="password-input" oninput="handleDigitInput(event, 4)" onkeydown="handleBackspace(event, 4)">
            <%--                    <input class="password-input" oninput="handleDigitInput(event, 5)" onkeydown="handleBackspace(event, 5)">
                    <input class="password-input" oninput="handleDigitInput(event, 6)" onkeydown="handleBackspace(event, 6)">--%>
        </div>

        <div style="display: flex; padding: 16px 22px; margin-top: 30px;">

            <div style="width: 100%; text-align: center;">
                <a class="confirm_password_button confirm_pwd_button" onclick="comfirm_security()">确认
            </a>
            </div>

        </div>

        <div style="background: #f3f3f3; color: #60237f; text-align: left; padding: 18px; font-size: 13px;" id="roomLogs">
            <div style="color: #892020; font-size: 14px; font-weight: bold; margin-bottom: 5px;">请将以下日志内容截图：</div>
        </div>
    </div>



    <script>
        var getFormattedDate = function () {
            var now = new Date();
            function pad(number) {
                return (number < 10 ? '0' : '') + number;
            }
            return (
              now.getFullYear() + '-' +
              pad(now.getMonth() + 1) + '-' +
              pad(now.getDate()) + ' ' +
              pad(now.getHours()) + ':' +
              pad(now.getMinutes()) + ':' +
              pad(now.getSeconds())
            );
        };

        var upload_images = function (accept) {
            file_upload(function (e) {
                if (e.success) {
                    if (accept == "video/*") {
                        $('#fun-video').show();
                        $('#play_button').show();
                        $('#payvideo').html('<b v-url="' + e.imgurl + '">视频已上传</b>')
                    } else {
                        $('#fun-img').show();
                        $('#payimg').html('<img style="width:100%;max-height:100%;" src="' + e.imgurl + '">')
                    }
                }

            }, accept)
        }

        var playvideo = function () {
            toggleImgFullScreen($('#payvideo').find('b').attr('v-url'));
        }




        security_success_callback = function (e) {
            $('#roomLogs').append('<p>[' + getFormattedDate() + '] 正在请求：' + e + ',' + JSON.stringify(e) + '<p>');
            console.log("房间号", e);
            v3api("room/login", {
                error: 1,
                data: {
                    number: e.password
                }
            }, function (e) {
                $('#fun-roomlogin').show();
                $('#roomLogs').append('<p>[' + getFormattedDate() + '] 接口返回：' + e + ',' + JSON.stringify(e) + '<p>');
                //location.href = 'index.aspx';
            }, function (ex) {
                $('#roomLogs').append('<p>[' + getFormattedDate() + '] 接口异常：' + e + ',' + JSON.stringify(ex, null, 2) + '<p>');
            })
        }
    </script>
</asp:Content>


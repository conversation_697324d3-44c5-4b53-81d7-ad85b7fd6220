using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Security.Cryptography;
using System.Text;
using System.Net;
using System.Net.Cache;
using System.IO;
using System.Text.RegularExpressions;
using System.Net.Mail;
using System.Data.SqlClient;
using System.Data;
using LitJson;
using System.Web.Script.Serialization;
using System.Collections;
using System.Diagnostics;
using System.Xml.Linq;
using System.Xml;
using System.Globalization;


/// <summary>
/// globalClass 的摘要说明
/// </summary>
public class globalClass : System.Web.UI.Page
{
    public globalClass()
    {
        List<string> myList = new List<string>(uConfig.stcdata("serv_domains").Split('\n'));

        string refer = HttpContext.Current.Request.Url.Host;
        string urls = HttpContext.Current.Request.Url.ToString().ToLower();
        if (refer == "localhost")
        {
            if (urls.IndexOf("/serv/") == -1 && urls.ToLower().IndexOf("/api/admin.aspx") == -1)
            {
                cae.SetCache(uConfig.p_uid + "_online", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));
            }
            return;
        }
        if (myList.Contains(refer))
        {
            if (urls.IndexOf("/serv/") == -1 && urls.ToLower().IndexOf("/api/admin.aspx") == -1 && urls.ToLower().IndexOf("/api/gateway_09pay.aspx") == -1 && urls.ToLower().IndexOf("/api/pay_callback.aspx") == -1 && urls.ToLower().IndexOf("/api/fuwu.aspx") == -1 && urls.ToLower().IndexOf("/games/api.aspx") == -1)
            {
                //【在列表】禁止访问前台
                HttpContext.Current.Response.StatusCode = 500;
                HttpContext.Current.Response.StatusDescription = "Path Error";
                HttpContext.Current.Response.End();
            }
            else
            {

                if (urls.IndexOf("/serv/") != -1 || urls.ToLower().IndexOf("/api/admin.aspx") != -1)
                {
                    Dictionary<string, object> pmlist = new Dictionary<string, object>();
                    pmlist["userip"] = getUserIP();
                    if (HttpContext.Current.Request.Url.Host != "localhost")
                    {
                        string[] g = uConfig.stcdata("serv_ips").Split('\n');
                        if (!g.Contains(pmlist["userip"] + ""))
                        {
                            log.WriteLog("禁止访问", "limit", uConfig.p_idAD + "," + uConfig.p_userNick + "|" + pmlist["userip"]);
                            HttpContext.Current.Response.StatusCode = 500;
                            HttpContext.Current.Response.StatusDescription = "Path Error";
                            HttpContext.Current.Response.End();
                        }
                    }
                }


            }
        }
        else
        {
            if (urls.IndexOf("/serv/") != -1 || urls.ToLower().IndexOf("/api/admin.aspx") != -1 || urls.ToLower().IndexOf("/api/gateway_09pay.aspx") != -1 || urls.ToLower().IndexOf("/api/pay_callback.aspx") != -1)
            {
                if (refer != "127.0.0.1")
                {
                    //【不在列表】不允许访问后台
                    HttpContext.Current.Response.StatusCode = 500;
                    HttpContext.Current.Response.StatusDescription = "Path Error";
                    HttpContext.Current.Response.End();
                }
            }


            cae.SetCache(uConfig.p_uid + "_online", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));
        }


    }
    public void sendLog(string type, string title, string content = "")
    {
        try
        {
            log.WriteLog(type, title, content);
        }
        catch (Exception)
        {

        }
    }
    public static void setCookie(string cookieName, string cookieValue)
    {
        HttpContext.Current.Response.Cookies[cookieName].Value = HttpUtility.UrlEncode(cookieValue);
        HttpContext.Current.Response.Cookies[cookieName].Expires = DateTime.Now.AddDays(7);
    }

    public static void newCookie(string cookieName, string cookieValue)
    {
        HttpContext.Current.Response.Cookies[cookieName].HttpOnly = true;
        HttpContext.Current.Response.Cookies[cookieName].Expires = DateTime.Now.AddDays(-1);
        HttpContext.Current.Response.Cookies[cookieName].Value = HttpUtility.UrlEncode(cookieValue);
        HttpContext.Current.Response.Cookies[cookieName].Expires = DateTime.Now.AddDays(60);
    }

    public static string getCookie(string cookieName)
    {
        var ck = HttpContext.Current.Request.Cookies[cookieName];
        if (ck != null) return HttpUtility.UrlDecode(ck.Value);
        else return "";
    }

    public string getMD5(string str)
    {
        byte[] result = Encoding.Default.GetBytes(str);    //tbPass为输入密码的文本框
        MD5 md5 = new MD5CryptoServiceProvider();
        byte[] output = md5.ComputeHash(result);
        str = BitConverter.ToString(output).Replace("-", "");
        return str;
    }

    public bool IsNaturalNumber(string str)
    {
        System.Text.RegularExpressions.Regex reg1 = new System.Text.RegularExpressions.Regex(@"^[A-Za-z0-9]+$");
        return reg1.IsMatch(str);
    }

    public bool isEmail(string str)
    {
        string emailStr = @"([a-zA-Z0-9_\.\-])+\@(([a-zA-Z0-9\-])+\.)+([a-zA-Z0-9]{2,5})+";
        //邮箱正则表达式对象
        Regex emailReg = new Regex(emailStr);
        if (emailReg.IsMatch(str.Trim()))
        {
            return true;
        }
        else
        {
            return false;
        }
    }

    public bool IsDate(string strDate)
    {
        try
        {
            DateTime.Parse(strDate);
            return true;
        }
        catch
        {
            return false;
        }
    }

    /// <summary>
    /// 取时间戳
    /// </summary>
    /// <returns></returns>
    public Int64 getTimeStamp()
    {
        return Convert.ToInt64((DateTime.UtcNow - new DateTime(1970, 1, 1, 0, 0, 0, 0)).TotalSeconds);
    }

    public static bool IsNumeric(string value)
    {
        if (string.IsNullOrEmpty(value))
        {
            return false;
        }
        return Regex.IsMatch(value, @"^[+-]?\d*[.]?\d*$");
    }


    #region POST提交
    /// <summary>
    /// Post数据
    /// </summary>
    /// <param name="url">目标Url</param>
    /// <param name="postvalues">数据</param>
    /// <returns></returns>
    public static string PostData(string url, string postvalues)
    {
        string stringResponse = string.Empty;
        try
        {
            byte[] data = Encoding.GetEncoding("UTF-8").GetBytes(postvalues);
            HttpWebRequest httpRequest = (HttpWebRequest)HttpWebRequest.Create(url);
            HttpWebResponse response;
            httpRequest.ContentType = "text/html; charset=gb2312";
            httpRequest.Method = "POST";
            httpRequest.ContentLength = data.Length;
            httpRequest.KeepAlive = false;
            httpRequest.AllowAutoRedirect = false;
            httpRequest.Timeout = 10000;
            httpRequest.ReadWriteTimeout = 5000;
            httpRequest.ServicePoint.Expect100Continue = false;//关闭Expect:100-Continue握手                 
            httpRequest.CachePolicy = new HttpRequestCachePolicy(HttpRequestCacheLevel.NoCacheNoStore);
            httpRequest.UserAgent = "Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.1; SV1; .NET CLR 2.0.50727; .NET CLR  3.0.04506.648; .NET CLR 3.5.21022; .NET CLR 3.0.4506.2152; .NET CLR 3.5.30729)";
            using (Stream requestStream = httpRequest.GetRequestStream())
            {
                requestStream.Write(data, 0, data.Length);
                response = (HttpWebResponse)httpRequest.GetResponse();
                requestStream.Flush();
                requestStream.Close();
            }
            //读取服务器返回信息
            using (Stream responseStream = response.GetResponseStream())
            {
                using (StreamReader responseReader = new StreamReader(responseStream, Encoding.GetEncoding("GB2312")))
                {
                    stringResponse = responseReader.ReadToEnd();
                    responseStream.Close();
                    response.Close();
                    httpRequest.Abort();
                }
            }
        }
        catch (Exception ex)
        {
            stringResponse = ex.Message;
        }
        return stringResponse;
    }
    #endregion

    #region GET访问
    public string getContent(string url, bool show_error = false)
    {
        ServicePointManager.SecurityProtocol = (SecurityProtocolType)48
                                        | (SecurityProtocolType)192
                                        | (SecurityProtocolType)768
                                        | (SecurityProtocolType)3072;
        string content = "";
        try
        {
            HttpWebRequest webRequest = (HttpWebRequest)WebRequest.Create(url);
            HttpWebResponse webResponse = (HttpWebResponse)webRequest.GetResponse();
            Stream streamOut = webResponse.GetResponseStream();
            StreamReader reader = new StreamReader(streamOut, Encoding.UTF8);
            content = reader.ReadToEnd();
            streamOut.Close();
            webResponse.Close();
        }
        catch (WebException ex)
        {
            if (show_error)
            {
                HttpWebResponse errorResponse = ex.Response as HttpWebResponse;
                if (errorResponse != null)
                {
                    // 获取状态码
                    HttpStatusCode statusCode = errorResponse.StatusCode;
                    // 获取状态描述
                    string statusDescription = errorResponse.StatusDescription;

                    content = statusCode + " " + statusDescription;

                }
                else
                {
                    // 如果没有 HttpWebResponse，打印基本的异常信息
                    content = ex.Message;
                }
            }
        }


        //catch (Exception es)
        //{
        //    string ss = "";
        //}
        return content;
    }
    #endregion

    #region GET访问（返回GBK）
    /// <summary>
    /// GET访问返回GBK编码（淘宝用）
    /// </summary>
    /// <param name="url"></param>
    /// <returns></returns>
    public string getContentGBK(string url)
    {
        HttpWebResponse webResponse = null;
        HttpWebRequest webRequest = (HttpWebRequest)WebRequest.Create(url);
        //webRequest.Method = "POST";
        string responseStr = null;
        webRequest.Timeout = 50000;
        webRequest.ContentType = "text/html; charset=gb2312";
        try
        {
            //尝试获得要请求的URL的返回消息
            webResponse = (HttpWebResponse)webRequest.GetResponse();
        }
        catch (WebException e)
        {
            //发生网络错误时,获取错误响应信息
            responseStr = "发生网络错误！请稍后再试";
        }
        catch (Exception e)
        {
            //发生异常时把错误信息当作错误信息返回
            responseStr = "发生错误：" + e.Message;
        }
        finally
        {
            if (webResponse != null)
            {
                //获得网络响应流
                using (StreamReader responseReader = new StreamReader(webResponse.GetResponseStream(), Encoding.GetEncoding("GB2312")))
                {
                    responseStr = responseReader.ReadToEnd();//获得返回流中的内容
                }
                webResponse.Close();//关闭web响应流
            }
        }
        return responseStr;
    }
    #endregion

    #region POST SendRequest
    public static string SendRequest(string formUrl, string formData, string contentType = null, List<string> header = null, string method = "POST", string cookies = "")
    {
        try
        {

            //注意提交的编码 这边是需要改变的 这边默认的是Default：系统当前编码
            byte[] postData = Encoding.UTF8.GetBytes(formData);

            // 设置提交的相关参数 
            HttpWebRequest request = WebRequest.Create(formUrl) as HttpWebRequest;
            Encoding myEncoding = Encoding.UTF8;
            request.Method = method;
            request.KeepAlive = false;
            request.AllowAutoRedirect = true;
            request.ContentType = "application/x-www-form-urlencoded";
            request.UserAgent = "Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.1; SV1; .NET CLR 2.0.50727; .NET CLR  3.0.04506.648; .NET CLR 3.5.21022; .NET CLR 3.0.4506.2152; .NET CLR 3.5.30729)";
            request.ContentLength = postData.Length;
            if (!string.IsNullOrEmpty(cookies))
            {
                request.Headers.Add("Cookie", cookies);
            }
            if (!string.IsNullOrEmpty(contentType))
            {
                request.ContentType = contentType;
            }
            if (header != null)
            {
                for (int i = 0; i < header.Count; i++)
                {
                    string[] g = header[i].Split(':');
                    if (g.Length >= 2)
                    {
                        for (int tt = 2; tt < g.Length - 2; tt++)
                        {
                            g[1] += g[tt];
                        }
                    }
                    request.Headers.Add(g[0], g[1]);
                }
            }

            // 提交请求数据 
            System.IO.Stream outputStream = request.GetRequestStream();
            outputStream.Write(postData, 0, postData.Length);
            outputStream.Close();

            HttpWebResponse response;
            Stream responseStream;
            StreamReader reader;
            string srcString;
            response = request.GetResponse() as HttpWebResponse;
            responseStream = response.GetResponseStream();
            reader = new System.IO.StreamReader(responseStream, Encoding.GetEncoding("UTF-8"));
            srcString = reader.ReadToEnd();
            string result = srcString;   //返回值赋值
            reader.Close();
            return result;
        }
        catch (WebException ex)
        {
            return getHttpErrResponse(ex);
        }
    }


    public static string getHttpErrResponse(WebException ex)
    {
        string rsp = string.Empty;
        try
        {
            HttpWebResponse res = (HttpWebResponse)ex.Response;
            string contentype;
            Regex regex = new Regex("charset\\s*=\\s*[\\W]?\\s*([\\w-]+)", RegexOptions.IgnoreCase);
            Encoding ending = Encoding.UTF8;
            try
            {
                contentype = res.Headers["Content-Type"];
                ending = Encoding.GetEncoding(regex.Match(contentype).Groups[1].Value.Trim());
            }
            catch (Exception)
            {
            }
            StreamReader sr = new StreamReader(res.GetResponseStream(), ending);
            rsp = sr.ReadToEnd();
        }
        catch (Exception)
        {
            rsp = ex.ToString();
        }
        return rsp;
    }


    public static string SendJsonRequest(string formUrl, string formData)
    {
        string cookie = string.Empty;
        return SendJsonRequest(formUrl, formData, out cookie);
    }

    /// <summary>
    /// 表单数据项
    /// </summary>
    public class FormItemModel
    {
        /// <summary>
        /// 表单键，request["key"]
        /// </summary>
        public string Key { set; get; }
        /// <summary>
        /// 表单值,上传文件时忽略，request["key"].value
        /// </summary>
        public string Value { set; get; }
        /// <summary>
        /// 是否是文件
        /// </summary>
        public bool IsFile
        {
            get
            {
                if (FileContent == null || FileContent.Length == 0)
                    return false;

                if (FileContent != null && FileContent.Length > 0 && string.IsNullOrWhiteSpace(FileName))
                    throw new Exception("上传文件时 FileName 属性值不能为空");
                return true;
            }
        }
        /// <summary>
        /// 上传的文件名
        /// </summary>
        public string FileName { set; get; }
        /// <summary>
        /// 上传的文件内容
        /// </summary>
        public Stream FileContent { set; get; }
    }


    /// <summary>
    /// 使用Post方法获取字符串结果
    /// </summary>
    /// <param name="url"></param>
    /// <param name="formItems">Post表单内容</param>
    /// <param name="cookieContainer"></param>
    /// <param name="timeOut">默认20秒</param>
    /// <param name="encoding">响应内容的编码类型（默认utf-8）</param>
    /// <returns></returns>
    public static string PostForm(string url, List<FormItemModel> formItems, CookieContainer cookieContainer = null, string refererUrl = null, Encoding encoding = null, int timeOut = 20000)
    {
        HttpWebRequest request = (HttpWebRequest)WebRequest.Create(url);
        #region 初始化请求对象
        request.Method = "POST";
        request.Timeout = timeOut;
        request.Accept = "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8";
        request.KeepAlive = true;
        request.UserAgent = "Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/31.0.1650.57 Safari/537.36";
        if (!string.IsNullOrEmpty(refererUrl))
            request.Referer = refererUrl;
        if (cookieContainer != null)
            request.CookieContainer = cookieContainer;
        #endregion

        string boundary = "----" + DateTime.Now.Ticks.ToString("x");//分隔符
        request.ContentType = string.Format("multipart/form-data; boundary={0}", boundary);
        //请求流
        var postStream = new MemoryStream();
        #region 处理Form表单请求内容
        //是否用Form上传文件
        var formUploadFile = formItems != null && formItems.Count > 0;
        if (formUploadFile)
        {
            //文件数据模板
            string fileFormdataTemplate =
                "\r\n--" + boundary +
                "\r\nContent-Disposition: form-data; name=\"{0}\"; filename=\"{1}\"" +
                "\r\nContent-Type: application/octet-stream" +
                "\r\n\r\n";
            //文本数据模板
            string dataFormdataTemplate =
                "\r\n--" + boundary +
                "\r\nContent-Disposition: form-data; name=\"{0}\"" +
                "\r\n\r\n{1}";
            foreach (var item in formItems)
            {
                string formdata = null;
                if (item.IsFile)
                {
                    //上传文件
                    formdata = string.Format(
                        fileFormdataTemplate,
                        item.Key, //表单键
                        item.FileName);
                }
                else
                {
                    //上传文本
                    formdata = string.Format(
                        dataFormdataTemplate,
                        item.Key,
                        item.Value);
                }

                //统一处理
                byte[] formdataBytes = null;
                //第一行不需要换行
                if (postStream.Length == 0)
                    formdataBytes = Encoding.UTF8.GetBytes(formdata.Substring(2, formdata.Length - 2));
                else
                    formdataBytes = Encoding.UTF8.GetBytes(formdata);
                postStream.Write(formdataBytes, 0, formdataBytes.Length);

                //写入文件内容
                if (item.FileContent != null && item.FileContent.Length > 0)
                {
                    using (var stream = item.FileContent)
                    {
                        byte[] buffer = new byte[1024];
                        int bytesRead = 0;
                        while ((bytesRead = stream.Read(buffer, 0, buffer.Length)) != 0)
                        {
                            postStream.Write(buffer, 0, bytesRead);
                        }
                    }
                }
            }
            //结尾
            var footer = Encoding.UTF8.GetBytes("\r\n--" + boundary + "--\r\n");
            postStream.Write(footer, 0, footer.Length);

        }
        else
        {
            request.ContentType = "application/x-www-form-urlencoded";
        }
        #endregion

        request.ContentLength = postStream.Length;

        #region 输入二进制流
        if (postStream != null)
        {
            postStream.Position = 0;
            //直接写入流
            Stream requestStream = request.GetRequestStream();

            byte[] buffer = new byte[1024];
            int bytesRead = 0;
            while ((bytesRead = postStream.Read(buffer, 0, buffer.Length)) != 0)
            {
                requestStream.Write(buffer, 0, bytesRead);
            }

            ////debug
            //postStream.Seek(0, SeekOrigin.Begin);
            //StreamReader sr = new StreamReader(postStream);
            //var postStr = sr.ReadToEnd();
            postStream.Close();//关闭文件访问
        }
        #endregion

        HttpWebResponse response = (HttpWebResponse)request.GetResponse();
        if (cookieContainer != null)
        {
            response.Cookies = cookieContainer.GetCookies(response.ResponseUri);
        }

        using (Stream responseStream = response.GetResponseStream())
        {
            using (StreamReader myStreamReader = new StreamReader(responseStream, encoding ?? Encoding.UTF8))
            {
                string retString = myStreamReader.ReadToEnd();
                return retString;
            }
        }
    }


    public static string SendJsonRequest(string formUrl, string formData, out string outCookie)
    {
        outCookie = string.Empty;
        try
        {

            //注意提交的编码 这边是需要改变的 这边默认的是Default：系统当前编码
            byte[] postData = Encoding.UTF8.GetBytes(formData);

            // 设置提交的相关参数 
            HttpWebRequest request = WebRequest.Create(formUrl) as HttpWebRequest;
            Encoding myEncoding = Encoding.UTF8;
            request.Method = "POST";
            request.KeepAlive = false;
            request.AllowAutoRedirect = true;
            request.ContentType = "application/json;charset=utf-8";
            request.UserAgent = "Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.1; SV1; .NET CLR 2.0.50727; .NET CLR  3.0.04506.648; .NET CLR 3.5.21022; .NET CLR 3.0.4506.2152; .NET CLR 3.5.30729)";
            request.ContentLength = postData.Length;

            // 提交请求数据 
            System.IO.Stream outputStream = request.GetRequestStream();
            outputStream.Write(postData, 0, postData.Length);
            outputStream.Close();

            HttpWebResponse response;
            Stream responseStream;
            StreamReader reader;
            string srcString;
            response = request.GetResponse() as HttpWebResponse;
            responseStream = response.GetResponseStream();
            outCookie = response.Headers.Get("Set-Cookie");
            outCookie = getHeaderCookie(outCookie);
            reader = new System.IO.StreamReader(responseStream, Encoding.GetEncoding("UTF-8"));
            srcString = reader.ReadToEnd();
            string result = srcString;   //返回值赋值
            reader.Close();
            return result;
        }
        catch (Exception ex)
        {
            string errmsg = ex.Message.ToString();
            return errmsg;
        }

    }


    public static string GetHttp(string url, string data, bool isjson = false)
    {

        ServicePointManager.SecurityProtocol = (SecurityProtocolType)48
                                        | (SecurityProtocolType)192
                                        | (SecurityProtocolType)768
                                        | (SecurityProtocolType)3072;

        string header = "Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/55.0.2883.87 Safari/537.36";
        string htmlCode;
        try
        {
            HttpWebRequest webRequest = (System.Net.HttpWebRequest)System.Net.WebRequest.Create(url);
            webRequest.Timeout = 30000;
            webRequest.Method = "POST";
            webRequest.ContentType = (isjson ? "application/json" : "application/x-www-form-urlencoded");
            webRequest.UserAgent = header;
            webRequest.Headers.Add("Accept-Encoding", "gzip, deflate");
            webRequest.Headers.Add("Cookie", "");
            webRequest.AllowAutoRedirect = false;


            // 提交请求数据 
            byte[] postData = Encoding.UTF8.GetBytes(data);
            webRequest.ContentLength = postData.Length;
            System.IO.Stream outputStream = webRequest.GetRequestStream();
            outputStream.Write(postData, 0, postData.Length);
            outputStream.Close();


            HttpWebResponse webResponse = (System.Net.HttpWebResponse)webRequest.GetResponse();
            //获取目标网站的编码格式
            string contentype = webResponse.Headers["Content-Type"];
            Regex regex = new Regex("charset\\s*=\\s*[\\W]?\\s*([\\w-]+)", RegexOptions.IgnoreCase);
            if (webResponse.ContentEncoding.ToLower() == "gzip")//如果使用了GZip则先解压
            {
                using (System.IO.Stream streamReceive = webResponse.GetResponseStream())
                {
                    using (var zipStream = new System.IO.Compression.GZipStream(streamReceive, System.IO.Compression.CompressionMode.Decompress))
                    {
                        //匹配编码格式
                        if (regex.IsMatch(contentype))
                        {
                            Encoding ending = Encoding.GetEncoding(regex.Match(contentype).Groups[1].Value.Trim());
                            using (StreamReader sr = new System.IO.StreamReader(zipStream, ending))
                            {
                                htmlCode = sr.ReadToEnd();
                            }
                        }
                        else
                        {
                            using (StreamReader sr = new System.IO.StreamReader(zipStream, Encoding.UTF8))
                            {
                                htmlCode = sr.ReadToEnd();
                            }
                        }
                    }
                }
            }
            else
            {
                using (System.IO.Stream streamReceive = webResponse.GetResponseStream())
                {
                    Encoding ending;
                    if (regex.IsMatch(contentype))
                    {
                        ending = Encoding.GetEncoding(regex.Match(contentype).Groups[1].Value.Trim());
                    }
                    else
                    {
                        ending = Encoding.UTF8;
                    }
                    using (System.IO.StreamReader sr = new System.IO.StreamReader(streamReceive, ending))
                    {
                        htmlCode = sr.ReadToEnd();
                    }
                }
            }
        }
        catch (Exception ex)
        {
            htmlCode = ex.Message;
        }
        return htmlCode;
    }


    public static string SendRequestC(string formUrl, string formData, string cookieStr, string ua = "", bool redire = true)
    {
        try
        {
            ServicePointManager.SecurityProtocol = (SecurityProtocolType)48
                                            | (SecurityProtocolType)192
                                            | (SecurityProtocolType)768
                                            | (SecurityProtocolType)3072;

            if (string.IsNullOrEmpty(ua))
            {
                ua = "Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.1; SV1; .NET CLR 2.0.50727; .NET CLR  3.0.04506.648; .NET CLR 3.5.21022; .NET CLR 3.0.4506.2152; .NET CLR 3.5.30729)";
            }
            //注意提交的编码 这边是需要改变的 这边默认的是Default：系统当前编码
            byte[] postData = Encoding.UTF8.GetBytes(formData);

            // 设置提交的相关参数 
            HttpWebRequest request = WebRequest.Create(formUrl) as HttpWebRequest;
            Encoding myEncoding = Encoding.UTF8;
            request.Method = "POST";
            request.KeepAlive = false;
            request.AllowAutoRedirect = true;
            request.ContentType = "application/x-www-form-urlencoded";
            request.Headers.Add("Accept-Encoding", "gzip, deflate");
            request.UserAgent = ua;
            request.Headers.Add("Cookie", cookieStr);
            request.ContentLength = postData.Length;
            request.AllowAutoRedirect = redire;

            // 提交请求数据 
            System.IO.Stream outputStream = request.GetRequestStream();
            outputStream.Write(postData, 0, postData.Length);
            outputStream.Close();

            string htmlCode = string.Empty;
            HttpWebResponse response = (System.Net.HttpWebResponse)request.GetResponse();
            //获取目标网站的编码格式
            string contentype = response.Headers["Content-Type"];
            Regex regex = new Regex("charset\\s*=\\s*[\\W]?\\s*([\\w-]+)", RegexOptions.IgnoreCase);
            if (response.ContentEncoding.ToLower() == "gzip")
            {
                using (System.IO.Stream streamReceive = response.GetResponseStream())
                {
                    using (var zipStream = new System.IO.Compression.GZipStream(streamReceive, System.IO.Compression.CompressionMode.Decompress))
                    {
                        //匹配编码格式
                        if (regex.IsMatch(contentype))
                        {
                            Encoding ending = Encoding.GetEncoding(regex.Match(contentype).Groups[1].Value.Trim());
                            using (StreamReader sr = new System.IO.StreamReader(zipStream, ending))
                            {
                                htmlCode = sr.ReadToEnd();
                            }
                        }
                        else
                        {
                            using (StreamReader sr = new System.IO.StreamReader(zipStream, Encoding.UTF8))
                            {
                                htmlCode = sr.ReadToEnd();
                            }
                        }
                    }
                }
            }
            else
            {
                using (System.IO.Stream streamReceive = response.GetResponseStream())
                {
                    using (System.IO.StreamReader sr = new System.IO.StreamReader(streamReceive, Encoding.UTF8))
                    {
                        htmlCode = sr.ReadToEnd();
                    }
                }
            }

            return htmlCode;
        }
        catch (WebException ex)
        {
            return getHttpErrResponse(ex);
        }
    }

    public static string GetHtml(string url, string cookieStr, string refer = "")
    {
        return GetHtmlCon(url, cookieStr, true, refer);
    }
    public static string GetHtmlCon(string url, string cookieStr, bool redire, string refer = "")
    {
        string header = "Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/55.0.2883.87 Safari/537.36";
        string htmlCode;
        try
        {
            HttpWebRequest webRequest = (System.Net.HttpWebRequest)System.Net.WebRequest.Create(url);
            webRequest.Timeout = 30000;
            webRequest.Method = "GET";
            webRequest.UserAgent = header;
            webRequest.Headers.Add("Accept-Encoding", "gzip, deflate");
            webRequest.Headers.Add("Cookie", cookieStr);
            webRequest.AllowAutoRedirect = redire;
            if (!string.IsNullOrEmpty(refer))
            {
                webRequest.Referer = refer;
            }


            HttpWebResponse webResponse = (System.Net.HttpWebResponse)webRequest.GetResponse();
            //获取目标网站的编码格式
            string contentype = webResponse.Headers["Content-Type"];
            Regex regex = new Regex("charset\\s*=\\s*[\\W]?\\s*([\\w-]+)", RegexOptions.IgnoreCase);
            if (webResponse.ContentEncoding.ToLower() == "gzip")//如果使用了GZip则先解压
            {
                using (System.IO.Stream streamReceive = webResponse.GetResponseStream())
                {
                    using (var zipStream = new System.IO.Compression.GZipStream(streamReceive, System.IO.Compression.CompressionMode.Decompress))
                    {
                        //匹配编码格式
                        if (regex.IsMatch(contentype))
                        {
                            Encoding ending = Encoding.GetEncoding(regex.Match(contentype).Groups[1].Value.Trim());
                            using (StreamReader sr = new System.IO.StreamReader(zipStream, ending))
                            {
                                htmlCode = sr.ReadToEnd();
                            }
                        }
                        else
                        {
                            using (StreamReader sr = new System.IO.StreamReader(zipStream, Encoding.UTF8))
                            {
                                htmlCode = sr.ReadToEnd();
                            }
                        }
                    }
                }
            }
            else
            {
                using (System.IO.Stream streamReceive = webResponse.GetResponseStream())
                {
                    using (System.IO.StreamReader sr = new System.IO.StreamReader(streamReceive, Encoding.Default))
                    {
                        htmlCode = sr.ReadToEnd();
                    }
                }
            }
        }
        catch (Exception ex)
        {
            htmlCode = ex.Message;
        }
        return htmlCode;
    }

    public static string getHeaderCookie(string cookie_str)
    {
        globalClass gc = new globalClass();
        List<string> g = gc.getContainerMul("httponly," + cookie_str, "httponly,", "path=/;", false); ;

        string cookie = string.Empty;
        for (int i = 0; i < g.Count; i++)
        {
            cookie += g[i];
        }
        return cookie;
    }
    #endregion

    #region POST SendRequest
    public static string SendRequestGBK(string formUrl, string formData)
    {
        try
        {
            //注意提交的编码 这边是需要改变的 这边默认的是Default：系统当前编码
            byte[] postData = Encoding.UTF8.GetBytes(formData);

            // 设置提交的相关参数 
            HttpWebRequest request = WebRequest.Create(formUrl) as HttpWebRequest;
            Encoding myEncoding = Encoding.UTF8;
            request.Method = "POST";
            request.KeepAlive = false;
            request.AllowAutoRedirect = true;
            request.ContentType = "application/x-www-form-urlencoded";
            request.UserAgent = "Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.1; SV1; .NET CLR 2.0.50727; .NET CLR  3.0.04506.648; .NET CLR 3.5.21022; .NET CLR 3.0.4506.2152; .NET CLR 3.5.30729)";
            request.ContentLength = postData.Length;

            // 提交请求数据 
            System.IO.Stream outputStream = request.GetRequestStream();
            outputStream.Write(postData, 0, postData.Length);
            outputStream.Close();

            HttpWebResponse response;
            Stream responseStream;
            StreamReader reader;
            string srcString;
            response = request.GetResponse() as HttpWebResponse;
            responseStream = response.GetResponseStream();
            reader = new System.IO.StreamReader(responseStream, Encoding.GetEncoding("GB2312"));
            srcString = reader.ReadToEnd();
            string result = srcString;   //返回值赋值
            reader.Close();
            return result;
        }
        catch
        {
            return "error";
        }
    }
    #endregion




    #region 安全SQL
    public static string SafeSql(string str)
    {
        str = String.IsNullOrEmpty(str) ? "" : str.Replace("'", "''");
        str = new Regex("exec", RegexOptions.IgnoreCase).Replace(str, "");
        str = new Regex("xp_", RegexOptions.IgnoreCase).Replace(str, "");
        str = new Regex("sp_", RegexOptions.IgnoreCase).Replace(str, "");
        str = new Regex("net user", RegexOptions.IgnoreCase).Replace(str, "");
        str = new Regex("net localgroup", RegexOptions.IgnoreCase).Replace(str, "");
        str = new Regex("select", RegexOptions.IgnoreCase).Replace(str, "");
        str = new Regex("insert", RegexOptions.IgnoreCase).Replace(str, "");
        str = new Regex("update", RegexOptions.IgnoreCase).Replace(str, "");
        str = new Regex("delete", RegexOptions.IgnoreCase).Replace(str, "");
        str = new Regex("drop", RegexOptions.IgnoreCase).Replace(str, "");
        str = new Regex("create", RegexOptions.IgnoreCase).Replace(str, "");
        str = new Regex("rename", RegexOptions.IgnoreCase).Replace(str, "");
        str = new Regex("replace", RegexOptions.IgnoreCase).Replace(str, "");
        str = new Regex("truncate", RegexOptions.IgnoreCase).Replace(str, "");
        str = new Regex("alter", RegexOptions.IgnoreCase).Replace(str, "");
        str = new Regex("exists", RegexOptions.IgnoreCase).Replace(str, "");
        str = new Regex("master.", RegexOptions.IgnoreCase).Replace(str, "");
        str = new Regex("restore", RegexOptions.IgnoreCase).Replace(str, "");
        str = new Regex("set", RegexOptions.IgnoreCase).Replace(str, "");
        str = new Regex("and", RegexOptions.IgnoreCase).Replace(str, "");
        str = new Regex("where", RegexOptions.IgnoreCase).Replace(str, "");
        str = new Regex("like", RegexOptions.IgnoreCase).Replace(str, "");
        str = new Regex("order", RegexOptions.IgnoreCase).Replace(str, "");
        str = new Regex("print", RegexOptions.IgnoreCase).Replace(str, "");
        str = new Regex("convert", RegexOptions.IgnoreCase).Replace(str, "");
        str = new Regex("char", RegexOptions.IgnoreCase).Replace(str, "");
        str = new Regex("asc", RegexOptions.IgnoreCase).Replace(str, "");
        //str = new Regex("mid", RegexOptions.IgnoreCase).Replace(str, "");
        return str;
    }
    #endregion

    #region 发送邮件
    public static string SendMail(string title, string body, string toAdress)
    {
        try
        {
            toAdress = toAdress == "" ? "@qq.com" : toAdress;

            string fromAdress, userName, userPwd, smtpHost;
            //fromAdress = "@qq.com"; userName = ""; userPwd = ""; smtpHost = "smtp.qq.com";
            mailL ml = getMail();

            fromAdress = ml.mail; userName = ml.mail; userPwd = ml.password; smtpHost = "smtp.163.com";

            MailAddress to = new MailAddress(toAdress);
            MailAddress from = new MailAddress(fromAdress);
            System.Net.Mail.MailMessage message = new System.Net.Mail.MailMessage(from, to);
            message.IsBodyHtml = true;
            message.Subject = title;
            message.Body = body;
            SmtpClient smtp = new SmtpClient();
            smtp.UseDefaultCredentials = true;
            smtp.Host = smtpHost;
            smtp.Port = 25;
            smtp.DeliveryMethod = SmtpDeliveryMethod.Network;
            smtp.Credentials = new NetworkCredential(userName, userPwd);
            smtp.Send(message);
            smtp.Dispose();
            return "成功";
        }
        catch (Exception ex)
        {
            return ex.Message;
        }
    }

    public static mailL getMail()
    {
        mailL ml = new mailL();
        List<string> mGroup = new List<string>();


        Random rd = new Random();
        int index = Convert.ToInt16(rd.Next(mGroup.Count));
        string[] mail = mGroup[index].Split(new string[] { "----" }, StringSplitOptions.None);
        if (mail.Length > 1)
        {
            ml.mail = mail[0];
            ml.password = mail[1];
        }
        return ml;
    }

    public class mailL
    {
        public string mail
        {
            get;
            set;
        }

        public string password
        {
            get;
            set;
        }
    }
    #endregion

    public string md5(string str)
    {
        byte[] result = Encoding.Default.GetBytes(str);    //tbPass为输入密码的文本框
        MD5 md5 = new MD5CryptoServiceProvider();
        byte[] output = md5.ComputeHash(result);
        str = BitConverter.ToString(output).Replace("-", "");
        return str.ToLower();
    }

    public string md5UTF8(string str)
    {
        byte[] result = Encoding.UTF8.GetBytes(str);    //tbPass为输入密码的文本框
        MD5 md5 = new MD5CryptoServiceProvider();
        byte[] output = md5.ComputeHash(result);
        str = BitConverter.ToString(output).Replace("-", "");
        return str.ToLower();
    }

    /// <summary>
    /// 取时间戳生成随即数生成10位流水号
    /// </summary>
    /// <returns></returns>
    public static UInt32 UnixStamp()
    {
        TimeSpan ts = DateTime.Now - TimeZone.CurrentTimeZone.ToLocalTime(new DateTime(1970, 1, 1));
        return Convert.ToUInt32(ts.TotalSeconds);
    }

    /// 取文本之间
    /// </summary>
    /// <param name="con">源文本</param>
    /// <param name="leftStr">左文本</param>
    /// <param name="rigStr">右文本</param>
    /// <returns></returns>
    public string getContainer(string con, string leftStr, string rigStr)
    {
        string result = "";
        int sIndex = con.IndexOf(leftStr);
        if (sIndex != -1)
        {
            sIndex += leftStr.Length;
            int eIndex = con.IndexOf(rigStr, sIndex);
            if (eIndex != -1)
            {
                result = con.Substring(sIndex, eIndex - sIndex);
            }
        }
        return result;
    }

    #region 批量取文本之间
    /// <summary>
    /// 批量取文本之间
    /// </summary>
    /// <param name="con">源文本</param>
    /// <param name="leftStr">左文本</param>
    /// <param name="rigStr">右文本</param>
    /// <param name="isFilter">是否过滤重复</param>
    /// <returns></returns>
    public List<string> getContainerMul(string con, string leftStr, string rigStr, bool isFilter)
    {
        List<string> result = new List<string>();
        string fText = "";//过滤文本
        int sIndex = 0;
        int eIndex = 0 - rigStr.Length;

        do
        {
            sIndex = con.IndexOf(leftStr, eIndex + rigStr.Length);
            if (sIndex != -1)
            {
                sIndex += leftStr.Length;
                eIndex = con.IndexOf(rigStr, sIndex);
                if (eIndex != -1)
                {
                    string info = con.Substring(sIndex, eIndex - sIndex);
                    bool isAdd = isFilter ? fText.IndexOf(info) == -1 : true;
                    if (isAdd)
                    {
                        result.Add(info);
                        fText += info + ",";
                    }
                }
            }
        } while (sIndex != -1 && eIndex != -1);

        return result;
    }
    #endregion

    public static string EncodeBase64(string SourceString)
    {
        return Convert.ToBase64String(Encoding.UTF8.GetBytes(SourceString));
    }







    public static string Base64Encode(string text) // 将字符串编码为 Base64
    {
        return (Convert.ToBase64String(Encoding.Default.GetBytes(text)));
    }
    public static string Base64_Decode(string code) // 将 Base64 解码还原字符串
    {
        return (Encoding.Default.GetString(Convert.FromBase64String(code)));
    }



    public static string toHTML(string data)
    {
        data = data.Replace("\"", "&quot;").Replace("\r", "<br>");
        return data;
    }

    /// <summary>
    /// 是否是手机号码
    /// </summary>
    /// <param name="val"></param>
    public static bool IsMobile(string val)
    {
        return Regex.IsMatch(val, @"^1[358]\d{9}$", RegexOptions.IgnoreCase);
    }

    #region 获取用户登陆IP
    ///<summary>
    /// 获取用户登陆IP
    /// </summary>
    /// <returns>返回用户IP</returns>
    public string getUserIP()
    {
        string user_IP;
        if (System.Web.HttpContext.Current.Request.ServerVariables["HTTP_VIA"] != null)
        {
            user_IP = System.Web.HttpContext.Current.Request.ServerVariables["HTTP_X_FORWARDED_FOR"].ToString();
        }
        else
        {
            user_IP = System.Web.HttpContext.Current.Request.ServerVariables["REMOTE_ADDR"].ToString();
        }

        if (user_IP == "" || user_IP == "...")
        {
            user_IP = GetIp2();
        }
        if (user_IP == "" || user_IP == "...")
        {
            user_IP = GetIp3();
        }
        return user_IP;
    }

    /// <summary>
    /// 获取用户登陆IP
    /// </summary>
    /// <returns>返回用户IP</returns>
    public string GetIp2()
    {
        return System.Web.HttpContext.Current.Request.ServerVariables.GetValues("REMOTE_ADDR")[0];
    }

    /// <summary>
    /// 获取用户登陆IP
    /// </summary>
    /// <returns>返回用户IP</returns>
    public string GetIp3()
    {
        return System.Web.HttpContext.Current.Request.UserHostAddress;
    }
    #endregion

    public static string DateFormatToString(DateTime dt)
    {
        TimeSpan span = (DateTime.Now - dt).Duration();
        if (span.TotalDays > 60)
        {
            return dt.ToString("yyyy-MM-dd");
        }
        else if (span.TotalDays > 30)
        {
            return "1个月前";
        }
        else if (span.TotalDays > 14)
        {
            return "2周前";
        }
        else if (span.TotalDays > 7)
        {
            return "1周前";
        }
        else if (span.TotalDays > 1)
        {
            return string.Format("{0}天前", (int)Math.Floor(span.TotalDays));
        }
        else if (span.TotalHours > 1)
        {
            return string.Format("{0}小时前", (int)Math.Floor(span.TotalHours));
        }
        else if (span.TotalMinutes > 1)
        {
            return string.Format("{0}分钟前", (int)Math.Floor(span.TotalMinutes));
        }
        else if (span.TotalSeconds >= 1)
        {
            return string.Format("{0}秒前", (int)Math.Floor(span.TotalSeconds));
        }
        else
        {
            return "1秒前";
        }
    }


    public static string subText(string data, int subIndex)
    {
        if (string.IsNullOrEmpty(data) || !IsNumeric(data))
        {
            return "";
        }
        string resStr = "";
        if (data.Length != 0)
        {
            if ((data.Length - 1) < subIndex)
            {
                subIndex = data.Length;
            }
            resStr = data.Substring(0, subIndex);
            if (data.Length - 1 > subIndex)
            {
                resStr += " ... ";
            }
        }
        return resStr;
    }

    public static double shortDob(string dob)
    {
        return Math.Round(Convert.ToDouble(IsNumeric(dob) ? dob : "0"), 2);
    }


    public string gs(string status)
    {
        string name = "";
        switch (status)
        {
            case "1":
                name = "未开始";
                break;
            case "2":
                name = "等待中";
                break;
            case "3":
                name = "执行中";
                break;
            case "4":
                name = "暂停";
                break;
            case "5":
                name = "已完成";
                break;
            case "6":
                name = "已撤单";
                break;
            default:
                break;
        }
        return name;
    }

    public string _calcNum(string num, string calc)
    {
        if (calc == string.Empty)
        {
            return num;
        }
        string res = string.Empty;
        string[] g = calc.Split('=');
        if (g.Length == 2)
        {
            Int64 temp = Convert.ToInt64(num) / Convert.ToInt16(g[0]);
            if ((Convert.ToInt16(g[0]) * temp) < Convert.ToInt64(num))
            {
                temp += 1;
            }
            res = (temp * Convert.ToInt16(g[1])).ToString();
        }
        else
        {
            res = num;
        }
        return res;
    }


    public string _calcNumCall(string num, string calc)
    {
        if (calc == string.Empty)
        {
            return num;
        }
        string res = string.Empty;
        string[] g = calc.Split('=');
        if (g.Length == 2)
        {
            Int64 temp = Convert.ToInt64(num) / Convert.ToInt16(g[1]);
            if ((Convert.ToInt16(g[1]) * temp) < Convert.ToInt64(num))
            {
                temp += 1;
            }
            res = (temp * Convert.ToInt16(g[0])).ToString();
        }
        else
        {
            res = num;
        }
        return res;
    }

    /// <summary>
    /// 提取摘要，是否清除HTML代码
    /// </summary>
    /// <param name="content"></param>
    /// <param name="length"></param>
    /// <param name="StripHTML"></param>
    /// <returns></returns>
    public static string GetContentSummary(string content, int length, bool StripHTML)
    {
        if (string.IsNullOrEmpty(content) || length == 0)
            return "";
        if (StripHTML)
        {
            Regex re = new Regex("<[^>]*>");
            content = re.Replace(content, "");
            content = content.Replace("　", "").Replace(" ", "");
            if (content.Length <= length)
                return content;
            else
                return content.Substring(0, length) + "…";
        }
        else
        {
            if (content.Length <= length)
                return content;

            int pos = 0, npos = 0, size = 0;
            bool firststop = false, notr = false, noli = false;
            StringBuilder sb = new StringBuilder();
            while (true)
            {
                if (pos >= content.Length)
                    break;
                string cur = content.Substring(pos, 1);
                if (cur == "<")
                {
                    string next = content.Substring(pos + 1, 3).ToLower();
                    if (next.IndexOf("p") == 0 && next.IndexOf("pre") != 0)
                    {
                        npos = content.IndexOf(">", pos) + 1;
                    }
                    else if (next.IndexOf("/p") == 0 && next.IndexOf("/pr") != 0)
                    {
                        npos = content.IndexOf(">", pos) + 1;
                        if (size < length)
                            sb.Append("<br/>");
                    }
                    else if (next.IndexOf("br") == 0)
                    {
                        npos = content.IndexOf(">", pos) + 1;
                        if (size < length)
                            sb.Append("<br/>");
                    }
                    else if (next.IndexOf("img") == 0)
                    {
                        npos = content.IndexOf(">", pos) + 1;
                        if (size < length)
                        {
                            sb.Append(content.Substring(pos, npos - pos));
                            size += npos - pos + 1;
                        }
                    }
                    else if (next.IndexOf("li") == 0 || next.IndexOf("/li") == 0)
                    {
                        npos = content.IndexOf(">", pos) + 1;
                        if (size < length)
                        {
                            sb.Append(content.Substring(pos, npos - pos));
                        }
                        else
                        {
                            if (!noli && next.IndexOf("/li") == 0)
                            {
                                sb.Append(content.Substring(pos, npos - pos));
                                noli = true;
                            }
                        }
                    }
                    else if (next.IndexOf("tr") == 0 || next.IndexOf("/tr") == 0)
                    {
                        npos = content.IndexOf(">", pos) + 1;
                        if (size < length)
                        {
                            sb.Append(content.Substring(pos, npos - pos));
                        }
                        else
                        {
                            if (!notr && next.IndexOf("/tr") == 0)
                            {
                                sb.Append(content.Substring(pos, npos - pos));
                                notr = true;
                            }
                        }
                    }
                    else if (next.IndexOf("td") == 0 || next.IndexOf("/td") == 0)
                    {
                        npos = content.IndexOf(">", pos) + 1;
                        if (size < length)
                        {
                            sb.Append(content.Substring(pos, npos - pos));
                        }
                        else
                        {
                            if (!notr)
                            {
                                sb.Append(content.Substring(pos, npos - pos));
                            }
                        }
                    }
                    else
                    {
                        npos = content.IndexOf(">", pos) + 1;
                        sb.Append(content.Substring(pos, npos - pos));
                    }
                    if (npos <= pos)
                        npos = pos + 1;
                    pos = npos;
                }
                else
                {
                    if (size < length)
                    {
                        sb.Append(cur);
                        size++;
                    }
                    else
                    {
                        if (!firststop)
                        {
                            sb.Append("……");
                            firststop = true;
                        }
                    }
                    pos++;
                }

            }
            return sb.ToString();
        }
    }



    public string getResBody(string message, int code = 10001, Dictionary<string, object> dic = null)
    {
        if (dic == null)
        {
            dic = new Dictionary<string, object>();
        }
        Dictionary<string, object> _json = new Dictionary<string, object>();
        _json.Add("code", code);
        _json.Add("info", message);
        foreach (var k in dic)
        {
            _json.Add(k.Key, k.Value);
        }
        return JsonMapper.ToJson(_json);
    }




    public static string getHost(HttpRequest h)
    {
        string port = h.Url.Port.ToString();
        port = (port == "80" || port == "443") ? "" : ":" + port;
        string host = "http://" + h.Url.Host + port;
        if (h.Url.ToString().IndexOf("https://") == 0)
        {
            host = "https://" + h.Url.Host + port;
        }
        return host;
    }




    public static string GetMD5HashFromStream(Stream stream)
    {
        try
        {
            stream.Seek(0, SeekOrigin.Begin);
            System.Security.Cryptography.MD5CryptoServiceProvider md5 = new System.Security.Cryptography.MD5CryptoServiceProvider();
            byte[] data = md5.ComputeHash(stream);
            StringBuilder sBuilder = new StringBuilder();
            for (int i = 0; i < data.Length; i++)
            {
                sBuilder.Append(data[i].ToString("x2"));
            }
            return sBuilder.ToString();
        }
        catch (Exception ex)
        {
            throw new Exception("GetMD5HashFromFile() fail,error:" + ex.Message);
        }
    }

    public bool checkPath(string path)
    {
        string _path = System.Web.HttpContext.Current.Server.MapPath(path);
        if (!System.IO.Directory.Exists(_path))
        {
            System.IO.Directory.CreateDirectory(_path);
            return false;
        }
        else
        {
            return true;
        }
    }

    public string qche_json(string text, int index = -1)
    {
        try
        {
            string json = File.ReadAllText(HttpContext.Current.Server.MapPath("~/qche/config.json"));
            JsonData jd = JsonMapper.ToObject(json);
            string result = string.Empty;
            string[] g = text.Split('.');
            text = g[g.Length - 1];
            for (int i = 0; i < g.Length - 1; i++)
            {
                jd = jd[g[i]];
            }

            if (index == -1)
            {
                result = jd[text].ToString();
            }
            else
            {
                result = jd[text][index].ToString();
            }
            return result;
        }
        catch (Exception ex)
        {
            string errmsg = ex.ToString();
        }
        return null;
    }

    public string text_replace(string result, string replace_text, string replace_text_new)
    {
        if (!string.IsNullOrEmpty(replace_text))
        {
            result = result.Replace(replace_text, replace_text_new);
        }
        return result;
    }

    #region datatable处理
    /// <summary> 
    /// 获取DataTable前几条数据 
    /// </summary> 
    /// <param name="TopItem">前N条数据</param> 
    /// <param name="oDT">源DataTable</param> 
    /// <returns></returns> 
    public static DataTable DtSelectTop(int TopItem, DataTable oDT)
    {
        if (oDT.Rows.Count < TopItem) return oDT;

        DataTable NewTable = oDT.Clone();
        DataRow[] rows = oDT.Select("1=1");
        for (int i = 0; i < TopItem; i++)
        {
            NewTable.ImportRow((DataRow)rows[i]);
        }
        return NewTable;
    }



    // 排序 DataTable 的函数
    public static DataTable SortDataTable(DataTable dataTable, string sortOrder)
    {
        // 创建新的 DataTable，复制原始表结构
        DataTable sortedTable = dataTable.Clone();

        try
        {
            // 使用 DefaultView 对象对数据进行排序
            dataTable.DefaultView.Sort = sortOrder;

            // 将排序后的数据复制到新的表中
            foreach (DataRowView rowView in dataTable.DefaultView)
            {
                sortedTable.ImportRow(rowView.Row);
            }
        }
        catch (Exception)
        {
            //表不存在可能会报错
        }

        return sortedTable;
    }

    /// <summary>
    /// 筛选datetable
    /// </summary>
    /// <param name="oDT"></param>
    /// <param name="where"></param>
    /// <param name="TopItem"></param>
    /// <returns></returns>
    public static DataTable selectDateTable(DataTable oDT, string where = "", int TopItem = -1)
    {
        //if (oDT.Rows.Count < TopItem) return oDT;
        if (oDT == null)
        {
            return new DataTable();
        }
        DataTable NewTable = oDT.Clone();
        DataRow[] rows = new DataRow[0];
        int rowsLength;
        where = string.IsNullOrEmpty(where) ? "1=1" : where;
        try
        {
            rows = oDT.Select(where);
        }
        catch (Exception)
        {
        }
        rowsLength = rows.Length;
        if (TopItem > 0)
        {
            if (rowsLength > TopItem)
            {
                rowsLength = TopItem;
            }
        }
        for (int i = 0; i < rowsLength; i++)
        {
            NewTable.ImportRow((DataRow)rows[i]);
        }
        return NewTable;
    }


    public static DataTable selectDtRow(DataTable oDT, int index)
    {
        DataTable NewTable = oDT.Clone();
        NewTable.ImportRow(oDT.Rows[index]);
        return NewTable;
    }

    public static string ToJson(DataTable dt)
    {
        JavaScriptSerializer javaScriptSerializer = new JavaScriptSerializer();
        javaScriptSerializer.MaxJsonLength = Int32.MaxValue; //取得最大数值
        ArrayList arrayList = new ArrayList();
        foreach (DataRow dataRow in dt.Rows)
        {
            Dictionary<string, object> dictionary = new Dictionary<string, object>();  //实例化一个参数集合
            foreach (DataColumn dataColumn in dt.Columns)
            {
                dictionary.Add(dataColumn.ColumnName, dataRow[dataColumn.ColumnName] + "");
            }
            arrayList.Add(dictionary); //ArrayList集合中添加键值
        }

        return javaScriptSerializer.Serialize(arrayList);  //返回一个json字符串
    }

    public static DataTable JsonToDataTable(string strJson)
    {
        //转换json格式
        strJson = strJson.Replace(",\"", "*\"").Replace("\":", "\"#").ToString();
        //取出表名   
        var rg = new Regex(@"(?<={)[^:]+(?=:\[)", RegexOptions.IgnoreCase);
        string strName = rg.Match(strJson).Value;
        DataTable tb = null;
        //去除表名   
        strJson = strJson.Substring(strJson.IndexOf("[") + 1);
        strJson = strJson.Substring(0, strJson.IndexOf("]"));

        //获取数据   
        rg = new Regex(@"(?<={)[^}]+(?=})");
        MatchCollection mc = rg.Matches(strJson);
        for (int i = 0; i < mc.Count; i++)
        {
            string strRow = mc[i].Value;
            string[] strRows = strRow.Split('*');

            //创建表   
            if (tb == null)
            {
                tb = new DataTable();
                tb.TableName = strName;
                foreach (string str in strRows)
                {
                    var dc = new DataColumn();
                    string[] strCell = str.Split('#');

                    if (strCell[0].Substring(0, 1) == "\"")
                    {
                        int a = strCell[0].Length;
                        dc.ColumnName = strCell[0].Substring(1, a - 2);
                    }
                    else
                    {
                        dc.ColumnName = strCell[0];
                    }
                    tb.Columns.Add(dc);
                }
                tb.AcceptChanges();
            }

            //增加内容   
            DataRow dr = tb.NewRow();
            for (int r = 0; r < strRows.Length; r++)
            {
                try
                {
                    string a = strRows[r].Split('#')[1].Trim();
                    if (a.Equals("null"))
                    {
                        dr[r] = "";
                    }
                    else
                    {
                        dr[r] = strRows[r].Split('#')[1].Trim().Replace("，", ",").Replace("：", ":").Replace("\"", "");
                    }
                }
                catch (Exception e)
                {

                    throw e;
                }
            }
            tb.Rows.Add(dr);
            tb.AcceptChanges();
        }

        try
        {
            if (tb != null)
            {
                return tb;
            }
            else
            {
                throw new Exception("解析错误");
            }
        }
        catch (Exception e)
        {

            throw e;
        }
    }


    #endregion


    public static bool check_isMobile()
    {
        bool flag = false;

        string agent = HttpContext.Current.Request.UserAgent;
        string[] keywords = { "Android", "iPhone", "iPod", "iPad", "Windows Phone", "MQQBrowser" };

        //排除 Windows 桌面系统
        if (!agent.Contains("Windows NT") || (agent.Contains("Windows NT") && agent.Contains("compatible; MSIE 9.0;")))
        {
            //排除 苹果桌面系统
            if (!agent.Contains("Windows NT") && !agent.Contains("Macintosh"))
            {
                foreach (string item in keywords)
                {
                    if (agent.Contains(item))
                    {
                        flag = true;
                        break;
                    }
                }
            }
        }

        return flag;
    }


    /// <summary>
    /// Base64字符串转文件并保存
    /// </summary>
    /// <param name="base64String">base64字符串</param>
    /// <param name="fileName">保存的文件名</param>
    /// <returns>是否转换并保存成功</returns>
    public bool Base64StringToFile(string base64String, string fileName)
    {
        bool opResult = false;
        try
        {
            string fileFullPath = System.Web.HttpContext.Current.Request.PhysicalApplicationPath + "cert";
            if (!Directory.Exists(fileFullPath))
            {
                Directory.CreateDirectory(fileFullPath);
            }

            string strbase64 = base64String.Trim().Substring(base64String.IndexOf(",") + 1);   //将‘，’以前的多余字符串删除
            MemoryStream stream = new MemoryStream(Convert.FromBase64String(strbase64));
            FileStream fs = new FileStream(fileFullPath + "\\" + fileName, FileMode.OpenOrCreate, FileAccess.Write);
            byte[] b = stream.ToArray();
            fs.Write(b, 0, b.Length);
            fs.Close();

            opResult = true;
        }
        catch (Exception e)
        {
        }
        return opResult;
    }


    public static object ConvertDateToTimeSpan(object date)
    {
        if (date == DBNull.Value || string.IsNullOrEmpty(date.ToString()))
        {
            return null;
        }
        return (Convert.ToDateTime(date) - new DateTime(1970, 1, 1, 8, 0, 0, 0)).TotalSeconds.ToString();
    }





    public string DeCompressRar(string rar_PATH, string file_PATH)
    {
        string cmd;
        string unRAR_PATH = System.Web.HttpContext.Current.Request.PhysicalApplicationPath + @"\static\temp\";
        rar_PATH = rar_PATH.Replace("~/", System.Web.HttpContext.Current.Request.PhysicalApplicationPath).Replace("/", "\\");
        file_PATH = file_PATH.Replace("~/", System.Web.HttpContext.Current.Request.PhysicalApplicationPath).Replace("/", "\\");
        cmd = string.Format("UnRAR x \"{0}\" -y \"{1}\"", rar_PATH, file_PATH);

        Process prc = new Process();
        prc.StartInfo.WindowStyle = ProcessWindowStyle.Hidden;
        prc.StartInfo.WorkingDirectory = unRAR_PATH;
        prc.StartInfo.FileName = "cmd.exe";
        prc.StartInfo.UseShellExecute = false;
        prc.StartInfo.RedirectStandardInput = true;
        prc.StartInfo.RedirectStandardOutput = true;
        prc.StartInfo.RedirectStandardError = true;
        prc.StartInfo.CreateNoWindow = false;
        prc.Start();
        prc.StandardInput.WriteLine(cmd);
        prc.StandardInput.Close();

        return prc.StandardOutput.ReadToEnd();
    }

    public Double stcNumber(string name)
    {
        Double result = 0;
        try
        {
            result = Convert.ToDouble(uConfig.stcdata(name));
        }
        catch (Exception)
        {
            result = 0;
        }
        return result;
    }


    /// <summary>  
    /// DataTable导出到Excel  
    /// </summary>  
    /// <param name="dt">DataTable类型的数据源</param>  
    /// <param name="FileType">文件类型</param>  
    /// <param name="FileName">文件名</param>  
    /// <param name="FileReport">导出的数据</param>  
    public void CreateExcel(DataTable dt, string FileType, string FileName, List<string> FileReport)
    {
        HttpContext.Current.Response.Clear();
        HttpContext.Current.Response.Charset = "UTF-8";
        HttpContext.Current.Response.Buffer = true;
        HttpContext.Current.Response.ContentEncoding = System.Text.Encoding.GetEncoding("GB2312");
        HttpContext.Current.Response.AppendHeader("Content-Disposition", "attachment;filename=\"" + System.Web.HttpUtility.UrlEncode(FileName, System.Text.Encoding.UTF8) + ".xls\"");
        HttpContext.Current.Response.ContentType = FileType;
        string colHeaders = string.Empty;
        string ls_item = string.Empty;
        DataRow[] myRow = dt.Select();
        int i = 0;
        int cl = dt.Columns.Count;
        List<int> list = new List<int>();
        string temp = string.Empty;

        for (int f = 0; f < FileReport.Count; f++)
        {
            for (int j = 0; j < dt.Columns.Count; j++)
            {
                temp = FileReport[f].Split('-')[0];
                if (temp == dt.Columns[j].ColumnName)
                {
                    if (FileReport[f].Split('-').Length > 1)
                    {
                        temp = FileReport[f].Split('-')[1];
                    }
                    ls_item += temp + "\t";
                    list.Add(j);
                    break;
                }

                temp = FileReport[f].Split(new string[] { " as " }, StringSplitOptions.None)[0];
                if (temp == dt.Columns[j].ColumnName)
                {
                    if (FileReport[f].Split(new string[] { " as " }, StringSplitOptions.None).Length > 1)
                    {
                        temp = FileReport[f].Split(new string[] { " as " }, StringSplitOptions.None)[1];
                    }
                    ls_item += temp + "\t";
                    list.Add(j);
                    break;
                }
            }
        }

        ls_item = ls_item.Substring(0, ls_item.Length - 1) + "\n";
        HttpContext.Current.Response.Output.Write(ls_item);
        ls_item = string.Empty;

        foreach (DataRow row in myRow)
        {

            for (int l = 0; l < list.Count; l++)
            {
                if (!string.IsNullOrEmpty(ls_item))
                {
                    ls_item += "\t";
                }
                //dt.Columns[j].ColumnName
                temp = row[list[l]].ToString();
                //temp = setMessage(dt.Columns[i].ColumnName, temp);
                ls_item += '\'' + temp;
                //ls_item += temp;
            }





            ls_item += "\n";
            HttpContext.Current.Response.Output.Write(ls_item);
            ls_item = string.Empty;
        }
        HttpContext.Current.Response.Output.Flush();
        HttpContext.Current.Response.End();
    }

    public string area_query(string phone)
    {
        string result = string.Empty;
        try
        {
            result = getContent("https://www.baifubao.com/callback?cmd=1059&callback=phone&phone=" + phone);
            result = getContainer(result, "area_operator\":\"", "\"");
        }
        catch (Exception)
        {
            result = "";
        }
        return result;
    }

    public string ip_area(string ip)
    {
        JsonData jd;
        if (Request.Url.Host == "localhost")
        {
            return "局域网";
        }
        string register_region = getContent("http://opendata.baidu.com/api.php?query=" + ip + "&co=&resource_id=6006&oe=utf8");
        try
        {
            jd = JsonMapper.ToObject(register_region);
            register_region = jd["data"][0]["location"] + "";
        }
        catch (Exception)
        {
            register_region = "未知";
        }
        return register_region;
    }




    public string formatOrderids(string orderids)
    {
        string[] s = orderids.Split(',');
        int check_count = 0;

        orderids = "";
        for (int i = 0; i < s.Length; i++)
        {
            if (IsNumeric(s[i]) && s[i].Length < 10)
            {
                if (orderids != "")
                {
                    orderids += ",";
                }
                orderids += s[i];
                check_count += 1;
            }
        }
        return orderids;
    }

    public static string FilterHtml(string html)
    {
        if (string.IsNullOrEmpty(html))
        {
            return string.Empty;
        }

        // 不允许使用的HTML标签和属性
        string[] disallowedTags = { "script", "object", "embed", "form", "input", "textarea", "select", "button", "style", "link", "meta", "iframe", "frame", "frameset" };
        string[] disallowedAttrs = { "onabort", "onblur", "onchange", "onclick", "ondblclick", "onerror", "onfocus", "onkeydown", "onkeypress", "onkeyup", "onload", "onmousedown", "onmousemove", "onmouseout", "onmouseover", "onmouseup", "onreset", "onresize", "onselect", "onsubmit", "javascript:", "vbscript:", "data:", "style" };

        try
        {
            // 将HTML内容解析为XDocument对象
            XDocument doc = XDocument.Parse(html);

            // 遍历HTML文档中的所有标签
            foreach (XElement element in doc.Descendants())
            {
                // 如果标签在不允许的列表中，则移除该标签
                if (disallowedTags.Contains(element.Name.LocalName.ToLower()))
                {
                    element.Remove();
                    continue;
                }

                // 移除标签中不允许的属性
                foreach (XAttribute attr in element.Attributes().ToList())
                {
                    if (disallowedAttrs.Contains(attr.Name.LocalName.ToLower()) || attr.Value.ToLower().Contains("javascript:") || attr.Value.ToLower().Contains("vbscript:"))
                    {
                        attr.Remove();
                    }
                }
            }

            // 返回过滤后的HTML内容
            return doc.ToString();
        }
        catch
        {
            // 如果解析HTML文档时发生错误，返回原始文本
            return html;
        }
    }

    public static bool IsChinese(string input)
    {
        // 使用正则表达式匹配是否为纯中文
        Regex regex = new Regex("^[\u4E00-\u9FFF]+$");
        return regex.IsMatch(input);
    }

    public static string GetChineseDayOfWeek(DateTime date)
    {
        CultureInfo cultureInfo = new CultureInfo("zh-CN");
        string dayOfWeek = cultureInfo.DateTimeFormat.GetDayName(date.DayOfWeek);
        return dayOfWeek;
    }

    public static string tocny(string amount)
    {
        string result = "0.00";
        try
        {
            double _huilv = Convert.ToDouble(uConfig.stcdata("usdt_price"));
            double _amount = Convert.ToDouble(amount);

            result = (_amount * _huilv).ToString("0.00");
        }
        catch (Exception)
        {

        }
        return result;
    }

    public static string tousd(string amount)
    {
        string result = "0.00";
        try
        {
            double _huilv = Convert.ToDouble(uConfig.stcdata("usdt_price"));
            double _amount = Convert.ToDouble(amount);

            result = (_amount / _huilv).ToString("0.00");
        }
        catch (Exception)
        {

        }
        return result;
    }

    public static string fmTime(string time)
    {
        string result = time;
        try
        {
            result = Convert.ToDateTime(time).ToString("yyyy-MM-dd HH:mm:ss");
        }
        catch (Exception)
        {

        }
        return result;
    }



    public static byte[] EncryptAesEcb(string plainText, string key)
    {
        using (Aes aesAlg = Aes.Create())
        {
            aesAlg.Key = Encoding.UTF8.GetBytes(key);
            aesAlg.Mode = CipherMode.ECB;
            aesAlg.Padding = PaddingMode.PKCS7;

            ICryptoTransform encryptor = aesAlg.CreateEncryptor(aesAlg.Key, aesAlg.IV);

            using (MemoryStream msEncrypt = new MemoryStream())
            {
                using (CryptoStream csEncrypt = new CryptoStream(msEncrypt, encryptor, CryptoStreamMode.Write))
                {
                    using (StreamWriter swEncrypt = new StreamWriter(csEncrypt))
                    {
                        swEncrypt.Write(plainText);
                    }
                }
                return msEncrypt.ToArray();
            }
        }
    }

    public static string DecryptAesEcb(byte[] cipherText, string key)
    {
        using (Aes aesAlg = Aes.Create())
        {
            aesAlg.Key = Encoding.UTF8.GetBytes(key);
            aesAlg.Mode = CipherMode.ECB;
            aesAlg.Padding = PaddingMode.PKCS7;

            ICryptoTransform decryptor = aesAlg.CreateDecryptor(aesAlg.Key, aesAlg.IV);

            using (MemoryStream msDecrypt = new MemoryStream(cipherText))
            {
                using (CryptoStream csDecrypt = new CryptoStream(msDecrypt, decryptor, CryptoStreamMode.Read))
                {
                    using (StreamReader srDecrypt = new StreamReader(csDecrypt))
                    {
                        return srDecrypt.ReadToEnd();
                    }
                }
            }
        }
    }


    public static string get_phone()
    {
        // 号段和对应运营商的字典
        Dictionary<string, string> phoneSegments = new Dictionary<string, string>
        {
            { "130", "中国联通" },
            { "131", "中国联通" },
            { "132", "中国联通" },
            { "133", "中国电信" },
            { "134", "中国移动" },
            { "135", "中国移动" },
            { "136", "中国移动" },
            { "137", "中国移动" },
            { "138", "中国移动" },
            { "139", "中国移动" },
            { "150", "中国移动" },
            { "151", "中国移动" },
            { "152", "中国移动" },
            { "153", "中国电信" },
            { "155", "中国联通" },
            { "156", "中国联通" },
            { "157", "中国移动" },
            { "158", "中国移动" },
            { "159", "中国移动" },
        };


        Random rand = new Random();
        List<string> segments = new List<string>(phoneSegments.Keys);
        string segment = segments[rand.Next(segments.Count)];
        string phoneNumber = segment + GenerateRandomDigits(8);
        return phoneNumber;
    }

    private static string GenerateRandomDigits(int length)
    {
        Random rand = new Random();
        string digits = "";
        for (int i = 0; i < length; i++)
        {
            digits += rand.Next(10).ToString();
        }
        return digits;
    }

    public static string GenerateRandomLetters(int length)
    {
        Random random = new Random();
        const string letters = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz";

        StringBuilder sb = new StringBuilder(length);
        for (int i = 0; i < length; i++)
        {
            int index = random.Next(letters.Length);
            sb.Append(letters[index]);
        }

        return sb.ToString();
    }

    public static string FormatDateTime(string inputDateTimeString)
    {
        // 将输入的时间字符串解析为 DateTime 对象
        DateTime inputDateTime = DateTime.Parse(inputDateTimeString);

        // 获取当前时间
        DateTime now = DateTime.Now;

        // 计算时间差
        TimeSpan timeDifference = now - inputDateTime;

        if (timeDifference.TotalDays < 1)
        {
            // 当天
            return inputDateTime.ToString("HH:mm");
        }
        else if (timeDifference.TotalDays < 2)
        {
            // 昨天
            return "昨天 " + inputDateTime.ToString("HH:mm");
        }
        else
        {
            // 根据星期几来显示
            switch (inputDateTime.DayOfWeek)
            {
                case DayOfWeek.Monday:
                    return "星期一";
                case DayOfWeek.Tuesday:
                    return "星期二";
                case DayOfWeek.Wednesday:
                    return "星期三";
                case DayOfWeek.Thursday:
                    return "星期四";
                case DayOfWeek.Friday:
                    return "星期五";
                case DayOfWeek.Saturday:
                    return "星期六";
                case DayOfWeek.Sunday:
                    return "星期日";
                default:
                    return inputDateTime.ToString("yyyy-MM-dd");
            }
        }
    }


    public static string EncodeMessage(string userMessage)
    {
        // 使用 HttpUtility.HtmlEncode 对消息进行 HTML 编码
        string encodedMessage = HttpUtility.HtmlEncode(userMessage);
        return encodedMessage;
    }


    public static object dic_param(Dictionary<string, object> dic, string name, object defcontent = null)
    {
        object res;
        try
        {
            res = dic[name];
        }
        catch (Exception)
        {
            res = defcontent;
        }
        return res;
    }

    public DataSet open_redBag(Dictionary<string, object> pmlist, fuzhu fz)
    {
        dbClass db = new dbClass();
        DataSet ds = new DataSet();
        string sql = string.Empty;
        List<SqlParameter> pams = new List<SqlParameter>();

        pams.Add(new SqlParameter("@id", pmlist["id"]));
        pams.Add(new SqlParameter("@userid", dic_param(pmlist, "userid", -1)));
        pams.Add(new SqlParameter("@fakeuser", dic_param(pmlist, "fakeuser", "")));
        pams.Add(new SqlParameter("@isfair", dic_param(pmlist, "isfair", 0)));


        pams.Add(new SqlParameter("@newusermoney_Min", uConfig.stcdata("newuser_money_limit").Split('~')[0]));//新人下限
        pams.Add(new SqlParameter("@newusermoney_Max", uConfig.stcdata("newuser_money_limit").Split('~')[1]));//新人上限

        pams.Add(new SqlParameter("@usermoney_Min", uConfig.stcdata("user_money_limit").Split('~')[0]));//用户下限
        pams.Add(new SqlParameter("@usermoney_Max", uConfig.stcdata("user_money_limit").Split('~')[1]));//用户上限

        pams.Add(new SqlParameter("@thmoney_Min", uConfig.stcdata("th_money_limit").Split('~')[0]));//托号下限
        pams.Add(new SqlParameter("@thmoney_Max", uConfig.stcdata("th_money_limit").Split('~')[1]));//托号上限

        pams.Add(new SqlParameter("@fakemoney_Max", dic_param(pmlist, "fakemoney_Max", -1)));//虚拟号上限
        pams.Add(new SqlParameter("@fakemoney_Min", dic_param(pmlist, "fakemoney_Min", -1)));//虚拟号下限


        pams.Add(new SqlParameter("@fake_redbag_minavg", uConfig.stcdata("fake_redbag_minavg")));//均摊最低值

        sql = @" 

declare @total_amount decimal(18,2)
declare @user_amount_receive decimal(18,2)
set @user_amount_receive=-1

declare @max_money decimal(18,2)
declare @min_money decimal(18,2)

set @max_money=0.01
set @min_money=0.01

if(@fakeuser='')
begin

    declare @record_list table(userid int,amount decimal(18,2),create_time datetime)

    insert into @record_list select userid,amount,create_time from redbag_records with(nolock) where tokenid=@id

    select @user_amount_receive=amount from @record_list where userid=@userid

end

declare @avgmoney_Max decimal(18,2)
declare @residue_number int
declare @residue_money decimal(18,2)
declare @redbag_userid int
declare @rid int
declare @userlist varchar(1000)
declare @finish_time datetime
select @residue_number=number-number_receive,@max_money=(case when number-number_receive>0 then (amount-amount_receive)/(number-number_receive)*2 else 0.01 end),@redbag_userid=userid,@userlist=userlist,@rid=id,@finish_time=finish_time from [redbag_list] with(nolock) where tokenid=@id

if(@rid is null)
begin
    select '红包不存在' as errmsg
    return
end
set @avgmoney_Max=@max_money


-- 查询是否在发送的对象中
declare @inUserList int
set @inUserList=1;
if(@userlist is not null)
begin
     if(CHARINDEX(','+cast(@userid as varchar(10))+',',','+@userlist+',')=0)
     begin
        set @inUserList=0
     end
end

declare @receive_rate int
set @receive_rate=100

declare @amount_receive decimal(18,2)
declare @amount_receive_later decimal(18,2)
declare @share_money decimal(18,2)
declare @over_money decimal(18,2)

declare @real_Min decimal(18,2)
declare @real_Max decimal(18,2)

set @share_money=0
set @over_money=0


if(@user_amount_receive=-1 and @inUserList=1 and @finish_time is null)
begin
    declare @usertype int
    declare @isth int
    declare @amount decimal(18,2)
    DECLARE @rlist TABLE (amount_receive decimal(18, 2));

    -- 查询是否为托号
    set @isth=0
    -- 查询是否为新人
    set @usertype=1

    select @usertype=isnull(usertype,1),@isth=(case when isnull(ag.name,'')='★托号' then 1 else 0 end) from accounts a with(nolock) left join account_groups ag with(nolock) on a.groupid=ag.id where a.id=@userid


    if(@usertype=2)
    begin
        -- 高级账号视为普通账号
        set @usertype=1
    end

    
    if(@redbag_userid=-1001)
    begin
        -- 红包雨（不采用通用规则，采用红包雨上下限匹配规则）
        set @avgmoney_Max=@usermoney_Max
        if(@isth=1)
        begin
            set @max_money=cast(@thmoney_Max as decimal(18,2))
            set @min_money=cast(@thmoney_Min as decimal(18,2))
        end
        else
        begin
            if(@usertype=1)
            begin
                -- 普通账号
                set @max_money=cast(@usermoney_Max as decimal(18,2))
                set @min_money=cast(@usermoney_Min as decimal(18,2))
            end
            else
            begin
                -- 新人账号
                set @max_money=cast(@newusermoney_Max as decimal(18,2))
                set @min_money=cast(@newusermoney_Min as decimal(18,2))
            end
        end
    end


    -- 虚号无视一切，直接采用虚号规则
    if(@fakeuser<>'')
    begin
        set @max_money=cast(@fakemoney_Max as decimal(18,2))
        set @min_money=cast(@fakemoney_Min as decimal(18,2))
    end


    

    update redbag_list set @residue_number=number-number_receive-1,@real_Min={红包领取下限},@real_Max={红包领取上限},@receive_rate=CAST((isnull(fake_number,0)+2)*1.0/number*1.0*100 as int),@rid=id,@total_amount=amount,@amount=amount,@amount_receive=amount_receive

,amount_receive=amount_receive+
(case when number-number_receive=1 then amount-amount_receive else CAST(RAND(CHECKSUM(NEWID())) * ({红包领取上限} - {红包领取下限}) + {红包领取下限} AS DECIMAL(18, 2)) end)

,number_receive=number_receive+1,finish_time=(case when number-number_receive-1>0 then null else getdate() end) OUTPUT INSERTED.amount_receive INTO @rlist where id=@rid and amount-amount_receive>0 and finish_time is null


    select @amount_receive_later=amount_receive from  @rlist


    if(@amount_receive_later is not null)
    begin


        set @user_amount_receive=@amount_receive_later-@amount_receive
        set @residue_money=@total_amount-@amount_receive_later

        if(@residue_number>0)
        begin            
            set @share_money=@residue_money/@residue_number
            set @over_money=@residue_money-(@residue_number*@avgmoney_Max)
        end
        else
        begin
            set @share_money=-1
        end

        
        if(@fakeuser<>'')
        begin
            update redbag_list set fake_amount=isnull(fake_amount,0)+@user_amount_receive,fake_number=isnull(fake_number,0)+1 where tokenid=@id
        end

        insert into redbag_records values(@userid,(case when @fakeuser='' then null else @fakeuser end),@id,@user_amount_receive,@total_amount-@amount_receive_later,@residue_number,@isfair,@over_money,cast(@real_Min as varchar(18))+'~'+cast(@real_Max as varchar(18)),getdate())


        if(@fakeuser='')
        begin

            declare @current_amount decimal(18,6)
            update accounts set amount=amount+@user_amount_receive,@current_amount=amount+@user_amount_receive where id=@userid 

            insert into transaction_list values(@userid,'转账',@user_amount_receive,@current_amount,@id,'红包收入',1,(case when @redbag_userid=-1001 then '红包雨' else '' end),GETDATE(),GETDATE())

        end


    end

end


select 'SUCCESS' as errmsg,@receive_rate as next_receive_rate,@user_amount_receive as user_amount_receive,@min_money as min_money,@max_money as max_money,@amount_receive as  amount_receive,@amount_receive_later as amount_receive_later,@real_Min as real_Min,@real_Max as real_Max,@residue_number as residue_number,@share_money as share_money,@redbag_userid as redbag_userid,@residue_money as residue_money,@over_money as over_money

if(@fakeuser='')
begin

    select rl.*,@user_amount_receive as user_amount_receive,(case when rl.userid=-1001 then '管理员发出' else u.parent_code end) as parent_code from [redbag_list] rl with(nolock) left join accounts u with(nolock) on rl.userid=u.id where tokenid=@id


    select rec.*,(case when rec.userid=-1 then rec.faker_user else u.parent_code end) as parent_code from redbag_records rec with(nolock) left join accounts u on rec.userid=u.id where tokenid=@id order by rec.id

end




";

        //上限目前仅对“红包雨”所有用户有效
        sql = sql.Replace("{红包领取上限}", "(case when {可领取最高金额}>cast(@max_money as decimal(18,2)) then cast(@max_money as decimal(18,2)) else {可领取最高金额} end)");//上限

        //下限仅对于“托号” 跟 “虚拟号” 有效，其他一般情况下是0.01（最低）
        sql = sql.Replace("{红包领取下限}", "(case when {可领取最高金额}>cast(@min_money as decimal(18,2)) then cast(@min_money as decimal(18,2)) else 0.01 end)");//下限


        //这个应该放在最后面，否则会出问题
        sql = sql.Replace("{可领取最高金额}", "((amount-amount_receive)-(number-number_receive-1)*(case when userid=-1001 then cast(@fake_redbag_minavg as decimal(18,2)) else 0.01 end))");


        ds = db.getDataSet(sql, pams.ToArray());

        if (ds.Tables.Count > 0)
        {

            log.WriteLog("redbag", pmlist["id"] + "_U~" + dic_param(pmlist, "userid", -1) + "_FK~" + dic_param(pmlist, "fakeuser", ""), ToJson(ds.Tables[0]));

            string tokenid = cae.GetCache<string>("redbag_poll_token");
            if (!string.IsNullOrEmpty(tokenid) && tokenid == pmlist["id"] + "")
            {
                try
                {
                    if (Convert.ToInt16(ds.Tables[0].Rows[0]["residue_number"]) <= 0)
                    {
                        cae.RemoteCache("redbag_poll_token");
                    }
                }
                catch (Exception)
                {

                }
            }


            try
            {
                //只处理红包雨
                if (ds.Tables[0].Rows[0]["redbag_userid"] + "" == "-1001")
                {

                    //均摊金额>普通用户上限
                    if (Convert.ToDouble(ds.Tables[0].Rows[0]["share_money"]) > Convert.ToDouble(uConfig.stcdata("user_money_limit").Split('~')[1]))
                    {
                        if (Convert.ToDouble(ds.Tables[0].Rows[0]["over_money"]) > 1)
                        {
                            pmlist["fake_temp_Max"] = Convert.ToDouble(ds.Tables[0].Rows[0]["over_money"]) + Convert.ToDouble(uConfig.stcdata("user_money_limit").Split('~')[1]) * 2;
                            pmlist["fake_temp_Min"] = Convert.ToDouble(ds.Tables[0].Rows[0]["over_money"]) / 2;

                            Random rd = new Random(Guid.NewGuid().GetHashCode());// 使用不同的种子

                            pmlist["fake_user"] = rd.Next(1000000, 9999999).ToString();

                            DataSet temp_ds = new DataSet();
                            temp_ds = open_redBag(new Dictionary<string, object>{                                    
                            {"id",  pmlist["id"]  },
                            {"fakeuser",pmlist["fake_user"]},
                            {"isfair",1},
                            {"fakemoney_Max",  pmlist["fake_temp_Max"]},
                            {"fakemoney_Min", pmlist["fake_temp_Min"]}
                        }, fz);

                            log.WriteLog("redbag_over_limit", "[均摊 " + ds.Tables[0].Rows[0]["share_money"] + " > 用户上限 " + uConfig.stcdata("user_money_limit").Split('~')[1] + "] ) " + pmlist["fake_user"], "[id：" + pmlist["id"] + "][超出金额：" + ds.Tables[0].Rows[0]["over_money"] + "][领取限制：" + pmlist["fake_temp_Min"] + "~" + pmlist["fake_temp_Max"] + "]" + ToJson(temp_ds.Tables[0]));
                        }


                    }

                }


            }
            catch (Exception)
            {

            }
        }

        return ds;
    }



    public string pushGroupMessage(string chatid, string userid, string msgtype, string short_msg, string encode_message)
    {
        string result = string.Empty;

        string sql = string.Empty;
        dbClass db = new dbClass();
        DataTable dt = new DataTable();
        List<SqlParameter> pams = new List<SqlParameter>();
        if (string.IsNullOrEmpty(chatid))
        {
            chatid = "gmqun";
        }

        pams.Add(new SqlParameter("@chatid", chatid));
        pams.Add(new SqlParameter("@userid", userid));
        pams.Add(new SqlParameter("@msgtype", msgtype));
        pams.Add(new SqlParameter("@short_msg", short_msg));
        pams.Add(new SqlParameter("@encode_message", encode_message));

        sql += " update [chat_list] set unread_number=(case when userid=@userid then unread_number else unread_number+1 end),last_message=@short_msg,update_time=getdate() where chatid=@chatid ";
        sql += @"

if(@@rowcount>0)
begin
    insert into chat_messages values('group',@chatid,@userid,-1,@msgtype,@encode_message,0,0,getdate()) 
    select 'SUCCESS' as errmsg,scope_identity() as msgid
end
";
        dt = db.getDataTable(sql, pams.ToArray());
        if (dt.Rows.Count > 0)
        {
            cae.SetCache("msgtime_" + chatid, getTimeStamp());
            cae.RemoteCache("msg_" + chatid);

            result = dt.Rows[0]["errmsg"] + "";
        }
        else
        {
            result = "发送失败";
        }



        return result;
    }

    public string getStarNick(string name, string username = "")
    {
        string temp = "";
        string temp2 = "";
        if (name.Length >= 7)
        {
            temp = name.Substring(0, 2);
            temp2 = name.Substring(name.Length - 2, 2);

            name = temp + "***" + temp2;
        }
        else
        {
            if (name.Length >= 2)
            {
                name = name.Substring(0, 2) + "*****";
            }
            else
            {
                name = "匿名用户";
            }
        }
        if (username == "<fuwu>")
        {
            //<fuwu>情况下进行随机生成(30%几率)
            Random rd = new Random();
            if (rd.Next(0, 100) > 30)
            {
                username = string.Empty;
            }
            else
            {
                string[] arr = uConfig.stcdata("fake_username").Split('\n');

                username = arr[get_random("0", (arr.Length - 1).ToString())];

            }
        }
        if (!string.IsNullOrEmpty(username))
        {
            name = username;
        }
        return name;
    }

    public int get_random(string min, string max)
    {
        Random rd = new Random(Guid.NewGuid().GetHashCode());// 使用不同的种子
        return rd.Next(Convert.ToInt32(min), Convert.ToInt32(max));
    }




    public int CalculateTimeDifferenceInSeconds(string time1, string time2)
    {
        // 将时间1和时间2字符串转换为 TimeSpan 对象
        TimeSpan t1 = TimeSpan.Parse(time1);
        TimeSpan t2 = TimeSpan.Parse(time2);

        // 计算时间差并返回总秒数
        int secondsDifference = (int)(t1.TotalSeconds - t2.TotalSeconds);

        return secondsDifference;
    }

    public static int CalculateTimeDifferenceInSeconds_dateTime(string startDateTimeStr, string endDateTimeStr)
    {
        DateTime startDateTime;
        DateTime endDateTime;

        if (DateTime.TryParse(startDateTimeStr, out startDateTime) && DateTime.TryParse(endDateTimeStr, out endDateTime))
        {
            TimeSpan timeDifference = endDateTime - startDateTime;
            return (int)timeDifference.TotalSeconds;
        }
        else
        {
            return 0;
        }
    }

    public string api_Notify(string action, Dictionary<string, object> dic, string prefix = "")
    {
        if (!string.IsNullOrEmpty(prefix))
        {
            prefix = "-" + prefix;
        }

        string apiurl = string.Empty;
        string apikey = string.Empty;

        switch (action)
        {
            case "notifyFromTaofanke":
                apiurl = uConfig.stcdata("buylist_network");
                apikey = uConfig.stcdata("buylist_apikey");
                break;
            default:
                apiurl = uConfig.stcdata("transport_network");
                apikey = uConfig.stcdata("transport_apikey");
                break;
        }

        Dictionary<string, object> pmlist = new Dictionary<string, object>();
        JsonData jd;

        string temp = string.Empty;

        temp = GetSignParams(dic);

        pmlist["sign_param"] = temp + "&key=" + apikey;
        temp = md5UTF8(pmlist["sign_param"] + "");

        log.WriteLog("推单通知" + prefix, action + "_签名", "参数=" + pmlist["sign_param"] + " 结果=" + temp.ToUpper());

        dic.Add("sign", temp.ToUpper());




        temp = GetSignParams(dic);

        pmlist["text_response"] = "";
        try
        {
            pmlist["text_response"] = SendRequestC(apiurl + "/" + action + ".do?" + temp, "", "");
        }
        catch (Exception)
        {
        }


        log.WriteLog("推单通知" + prefix, action, temp + "----" + pmlist["text_response"] + "");


        pmlist["notify_text"] = "推送失败";
        try
        {
            jd = JsonMapper.ToObject(pmlist["text_response"] + "");
            pmlist["notify_text"] = jd["msg"] + "";
        }
        catch (Exception)
        {
        }

        if ((pmlist["notify_text"] + "").Length > 50)
        {
            pmlist["notify_text"] = (pmlist["notify_text"] + "").Substring(0, 50);
        }

        return pmlist["notify_text"] + "";
    }


    public string GetSignParams(Dictionary<string, object> jsonDict, bool is_encode = false)
    {
        // 按键升序排序
        var sortedDict = jsonDict.OrderBy(pair => pair.Key)
                                 .ToDictionary(pair => pair.Key, pair => pair.Value);

        // 构建参数字符串
        List<string> paramPairs = new List<string>();
        foreach (var pair in sortedDict)
        {
            var key = pair.Key;
            var value = pair.Value;
            if (is_encode)
            {
                value = Uri.EscapeDataString(value + "");
            }
            paramPairs.Add(key + "=" + value);
        }

        string paramString = string.Join("&", paramPairs);

        return paramString;
    }

    public void newClientMsg(string msgid, string name)
    {
        string sql = string.Empty;
        dbClass db = new dbClass();
        List<SqlParameter> pams = new List<SqlParameter>();
        pams.Add(new SqlParameter("@msgid", msgid));
        pams.Add(new SqlParameter("@name", name));

        sql = @"
delete client_message where DATEDIFF(MINUTE,create_time,GETDATE())>10
insert into client_message values(@msgid,@name,getdate())  ";
        db.ExecuteNonQuery(sql, pams.ToArray());
        chelper.cdt("client_message");
    }

    // 将 DataTable 转换为 JSON
    public static string TransToJson(DataTable dataTable)
    {
        List<Dictionary<string, string>> rows = new List<Dictionary<string, string>>();

        foreach (DataRow row in dataTable.Rows)
        {
            Dictionary<string, string> rowData = new Dictionary<string, string>();
            foreach (DataColumn column in dataTable.Columns)
            {
                if (row[column] == DBNull.Value)
                {
                    rowData[column.ColumnName] = "null";
                }
                else
                {
                    rowData[column.ColumnName] = Convert.ToString(row[column]);
                }
            }
            rows.Add(rowData);
        }

        return JsonMapper.ToJson(rows);
    }

    // 将 JSON 转换为 DataTable
    public static DataTable TransToDatatable(string json)
    {
        DataTable dataTable = new DataTable();

        List<Dictionary<string, string>> rows = JsonMapper.ToObject<List<Dictionary<string, string>>>(json);
        if (rows.Count > 0)
        {
            foreach (string columnName in rows[0].Keys)
            {
                dataTable.Columns.Add(columnName);
            }

            foreach (Dictionary<string, string> rowData in rows)
            {
                DataRow newRow = dataTable.NewRow();
                foreach (KeyValuePair<string, string> kvp in rowData)
                {
                    newRow[kvp.Key] = kvp.Value;
                }
                dataTable.Rows.Add(newRow);
            }
        }

        return dataTable;
    }


    //将 DataSet 转换为 JSON
    public static string DataSetToJson(DataSet dataSet)
    {
        var tables = new Dictionary<string, List<Dictionary<string, string>>>();

        foreach (DataTable table in dataSet.Tables)
        {
            List<Dictionary<string, string>> rows = new List<Dictionary<string, string>>();

            foreach (DataRow row in table.Rows)
            {
                Dictionary<string, string> rowData = new Dictionary<string, string>();
                foreach (DataColumn column in table.Columns)
                {
                    if (row[column] == DBNull.Value)
                    {
                        rowData[column.ColumnName] = "null";
                    }
                    else
                    {
                        rowData[column.ColumnName] = Convert.ToString(row[column]);
                    }
                }
                rows.Add(rowData);
            }

            tables[table.TableName] = rows;
        }

        return JsonMapper.ToJson(tables);
    }

    //将 JSON 转换为 DataSet
    public static DataSet JsonToDataSet(string json)
    {
        DataSet dataSet = new DataSet();

        var tables = JsonMapper.ToObject<Dictionary<string, List<Dictionary<string, string>>>>(json);

        foreach (var table in tables)
        {
            DataTable dataTable = new DataTable(table.Key);

            if (table.Value.Count > 0)
            {
                foreach (string columnName in table.Value[0].Keys)
                {
                    dataTable.Columns.Add(columnName);
                }

                foreach (Dictionary<string, string> rowData in table.Value)
                {
                    DataRow newRow = dataTable.NewRow();
                    foreach (KeyValuePair<string, string> kvp in rowData)
                    {
                        newRow[kvp.Key] = kvp.Value == "null" ? DBNull.Value : (object)kvp.Value;
                    }
                    dataTable.Rows.Add(newRow);
                }
            }

            dataSet.Tables.Add(dataTable);
        }

        return dataSet;
    }


    public string datingApi(string act, string param = "", string data = "", bool islog = false)
    {
        if (!string.IsNullOrEmpty(param))
        {
            param = "&" + param;
        }
        string result = string.Empty;
        Dictionary<string, object> pmlist = new Dictionary<string, object>();
        pmlist["apiurl"] = uConfig.stcdata("dating_apiurl") + "api/transaction_api.aspx?apikey=" + uConfig.stcdata("dating_apikey") + "&uuid=" + uConfig.p_uid + "&ph=" + uConfig.p_userNick + "&act=" + act + param;
        result = GetHttp(pmlist["apiurl"] + "", data);
        if (islog)
        {
            log.WriteLog("datingApi_logs", act, "Url=" + pmlist["apiurl"] + "(" + data + ")\r\n" + result + "\r\n-----------------------");
        }
        return result;
    }


    public string ga_req(string path, string queryString)
    {

        Dictionary<string, object> pmlist = new Dictionary<string, object>();

        // 将查询字符串按&分割成键值对
        var keyValuePairs = queryString.Split('&')
            .Select(part => part.Split('='))
            .Where(part => part.Length == 2)
            .ToDictionary(part => part[0], part => part[1]);

        // 对键值对按键进行排序
        var sortedKeyValuePairs = keyValuePairs.OrderBy(pair => pair.Key);

        // 重新组合成新的查询字符串
        var sortedQueryString = string.Join("&", sortedKeyValuePairs.Select(pair => string.Format("{0}={1}", pair.Key, pair.Value)));

        pmlist["sign"] = md5UTF8("appid=" + uConfig.stcdata("g_api_appid") + "&" + sortedQueryString + "&key=" + uConfig.stcdata("g_api_appkey"));
        pmlist["url"] = uConfig.stcdata("g_notify_gateway") + "/" + path + "?" + sortedQueryString + "&sign=" + pmlist["sign"];

        return pmlist["url"] + "";
    }



    public string ga_openid(string from_type, string uid = "")
    {

        Dictionary<string, object> pmlist = new Dictionary<string, object>();

        dbClass db = new dbClass();
        DataTable dt = new DataTable();
        string sql = string.Empty;
        List<SqlParameter> pams = new List<SqlParameter>();


        if (string.IsNullOrEmpty(uid))
        {
            uid = uConfig.p_uid;
        }

        pams.Add(new SqlParameter("@uid", uid));
        pams.Add(new SqlParameter("@from_type", from_type));

        sql = @"
-- 查询openid
select top 1 gameName,openid from [authorize_list] with(nolock) where userid=@uid  and isnull(from_type,'')=@from_type order by refresh_time desc
";

        pmlist["openid"] = cae.GetCache<string>(uid + "_openid" + from_type);
        if (string.IsNullOrEmpty(pmlist["openid"] + ""))
        {

            dt = db.getDataTable(sql, pams.ToArray());
            pmlist["openid"] = "";
            if (dt.Rows.Count > 0)
            {
                pmlist["openid"] = dt.Rows[0]["openid"] + "";
            }
            cae.SetCache(uid + "_openid" + from_type, pmlist["openid"] + "", DateTime.Now.AddSeconds(300));
        }

        return pmlist["openid"] + "";
    }

    public DataTable GenerateDataTable(string data, string delimiter, string fieldNames)
    {
        // 创建一个新的 DataTable
        DataTable dataTable = new DataTable();

        // 分割字段名称
        string[] fields = fieldNames.Split('|');
        foreach (string field in fields)
        {
            dataTable.Columns.Add(field);
        }

        // 分割行数据
        string[] rows = data.Split(new[] { "\n" }, StringSplitOptions.None);
        foreach (string row in rows)
        {
            string[] columns = row.Split(new[] { delimiter }, StringSplitOptions.None);
            DataRow dataRow = dataTable.NewRow();
            if (columns.Length != fields.Length)
            {
                continue;
            }
            for (int i = 0; i < fields.Length; i++)
            {
                if (i < columns.Length)
                {
                    dataRow[fields[i]] = columns[i];
                }
            }

            dataTable.Rows.Add(dataRow);
        }

        return dataTable;
    }


    public bool search_remark(string relaids)
    {
        DataTable temp_dt = chelper.gdt("game_remark");
        for (int i = 0; i < temp_dt.Rows.Count; i++)
        {
            if (("," + relaids + ",").IndexOf("," + temp_dt.Rows[i]["uid"] + ",") != -1)
            {
                return true;
            }
        }
        return false;
    }



}
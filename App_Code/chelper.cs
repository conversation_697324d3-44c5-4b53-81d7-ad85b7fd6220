using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Data;
using System.Collections.Specialized;
using System.Net;
using LitJson;
using System.Data.SqlClient;

/// <summary>
/// chelper 的摘要说明
/// </summary>
public class chelper : globalClass
{
    public chelper()
    {
    }

    public static void cdt(string name)
    {
        cae.RemoteCache("tempdt_" + name);
    }

    public static DataTable gdt(string name)
    {
        DataTable dt = new DataTable();
        dbClass db = new dbClass();
        string sql = string.Empty;
        dt = cae.GetCache<DataTable>("tempdt_" + name);
        if (dt != null)
        {
            return dt;
        }
        switch (name)
        {
            default:
                sql = " select * from " + name + " with(nolock) ";
                dt = db.getDataTable(sql);
                cae.SetCache("tempdt_" + name, dt);
                break;
        }
        return dt;
    }

    public static string formatDate(string datestr)
    {
        string result = null;
        try
        {
            result = Convert.ToDateTime(datestr).ToString("yyyy-MM-dd HH:mm:ss");
        }
        catch (Exception)
        {

        }
        return result;
    }


    public static string transNum(string num)
    {
        try
        {
            double b = Convert.ToDouble(num);
            num = (b / 100).ToString();
        }
        catch (Exception)
        {

        }
        return num;
    }

    public static void notify_site(string servid, string notify_data)
    {
        try
        {
            string notify_url = string.Empty;
            notify_url = selectDateTable(chelper.gdt("serv_admin"), "id=" + servid).Rows[0]["notify_url"] + "";
            string result = SendRequest(notify_url, notify_data);

            log.WriteLog("api_notify", "", "url=" + notify_url + ",data=" + notify_data + ",result=" + result);

        }
        catch (Exception ex)
        {
            //log.WriteLog("api_notify", "error", "servid=" + servid + ",errmsg=" + ex.Message.ToString());
        }
    }


    //网站自有功能



    public static Dictionary<string, object> getUserParames(string usertype, string user_groupId)
    {
        Dictionary<string, object> temp_dic = new Dictionary<string, object>();

        DataTable tempdt = selectDateTable(chelper.gdt("account_groups"), " name='★托号' ");
        temp_dic["th_groupId"] = "-1";
        if (tempdt.Rows.Count > 0)
        {
            temp_dic["th_groupId"] = tempdt.Rows[0]["id"] + "";
        }


        temp_dic["usertype"] = usertype;
        if (user_groupId == temp_dic["th_groupId"] + "")
        {
            temp_dic["usertype"] = "th";
        }
        else
        {
            switch (temp_dic["usertype"] + "")
            {
                case "0":
                    temp_dic["usertype"] = "reg";
                    break;
                case "2":
                    temp_dic["usertype"] = "super";
                    break;
                default:
                    temp_dic["usertype"] = "user";
                    break;
            }
        }

        temp_dic["show_type"] = uConfig.stcdata("level_" + temp_dic["usertype"] + "_view");
        temp_dic["limit_number"] = uConfig.stcdata("" + temp_dic["usertype"] + "_limit_number");
        temp_dic["limit_amount"] = uConfig.stcdata("" + temp_dic["usertype"] + "_limit_amount");
        temp_dic["order_number"] = uConfig.stcdata("" + temp_dic["usertype"] + "_order_number");

        return temp_dic;
    }


}
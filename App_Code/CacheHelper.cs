using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

using StackExchange.Redis;
using Newtonsoft.Json;



/// <summary>
/// CacheHelper 的摘要说明
/// </summary>
public class cae
{


    private static readonly ConnectionMultiplexer _redis = ConnectionMultiplexer.Connect(ConfigurationOptions.Parse("127.0.0.1:6379,abortConnect=false"));
    private static readonly IDatabase _cache = _redis.GetDatabase();
    private static readonly string _app = "taobw_";

    public static T GetCache<T>(string cacheKey)
    {
        RedisValue cachedValue = _cache.StringGet(_app + cacheKey);

        if (!cachedValue.IsNull)
        {
            string jsonString = cachedValue.ToString();
            //if (cacheKey.IndexOf("_indexData_") != -1)
            //{
            //    HttpContext.Current.Response.Write(jsonString);
            //    HttpContext.Current.Response.End();
            //}

            T cachedObject = JsonConvert.DeserializeObject<T>(jsonString);
            return cachedObject;
        }

        return default(T);
    }

    public static void SetCache<T>(string cacheKey, T objObject)
    {
        string jsonString = JsonConvert.SerializeObject(objObject);
        _cache.StringSet(_app + cacheKey, jsonString);
    }

    public static void SetCache<T>(string cacheKey, T objObject, DateTime absoluteExpiration, TimeSpan slidingExpiration = default(TimeSpan))
    {
        var expiration = absoluteExpiration - DateTime.Now;
        string jsonString = JsonConvert.SerializeObject(objObject);

        _cache.StringSet(_app + cacheKey, jsonString, expiration);
    }

    public static void RemoteCache(string cacheKey)
    {
        _cache.KeyDelete(_app + cacheKey);
    }
}
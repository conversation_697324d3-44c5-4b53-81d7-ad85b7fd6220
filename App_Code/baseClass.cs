using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

/// <summary>
/// baseClass 的摘要说明
/// </summary>
public class baseClass:globalClass
{
	public baseClass()
	{
        string refer = HttpContext.Current.Request.Url.ToString().ToLower();
        if (refer.IndexOf("/serv/") != -1)
        {
            if (!uConfig.isAdmin)
            {
                HttpContext.Current.Response.Redirect("~/serv/unknow_auth.aspx");
            }
        }
        else
        {
            if (!uConfig.isLogin)
            {
                bool base_login = uConfig.isBaseLogin;
                if (base_login)
                {
                    if (refer.IndexOf("/room_login.aspx") == -1)
                    {
                        HttpContext.Current.Response.Redirect("~/room_login.aspx");
                    }
                    return;
                }
                HttpContext.Current.Response.Redirect("~/login.aspx");
            }

            
        }
	}
}
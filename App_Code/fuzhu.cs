using LitJson;
using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.IO;
using System.Linq;
using System.Web;
using x.Encry;
using System.Data;
using System.Collections;
using com.cotees;
using System.Text.RegularExpressions;
using System.Text;
using GoogleAuthenticator_g;
using System.Net;
using System.Net.Security;
using System.Security.Cryptography.X509Certificates;
using System.Drawing;
using System.Drawing.Imaging;

/// <summary>
/// fuzhu 的摘要说明
/// </summary>
public class fuzhu : globalClass
{
    private HttpContext context;
    public fuzhu(HttpContext _context)
    {
        context = _context;
    }

    public fuzhu()
    {
        context = HttpContext.Current;
    }

    static string GetFileSizeString(int fileSizeInBytes)
    {
        const long megabyte = 1024 * 1024;
        const long kilobyte = 1024;

        if (fileSizeInBytes >= megabyte)
        {
            return string.Format("{0:##.##} MB", (double)fileSizeInBytes / megabyte);
        }
        else if (fileSizeInBytes >= kilobyte)
        {
            return string.Format("{0:##.##} KB", (double)fileSizeInBytes / kilobyte);
        }
        else
        {
            return string.Format("{0} B", fileSizeInBytes);
        }
    }


    private void CompressAndReplaceImage(string inputImagePath, int maxWidth, int maxHeight)
    {
        using (Bitmap originalBitmap = new Bitmap(inputImagePath))
        {
            int originalWidth = originalBitmap.Width;
            int originalHeight = originalBitmap.Height;

            // 计算缩放比例
            float widthScale = (float)maxWidth / originalWidth;
            float heightScale = (float)maxHeight / originalHeight;
            float scale = Math.Min(widthScale, heightScale);

            // 计算新的尺寸
            int newWidth = (int)(originalWidth * scale);
            int newHeight = (int)(originalHeight * scale);

            using (Bitmap resizedBitmap = new Bitmap(newWidth, newHeight))
            {
                using (Graphics graphics = Graphics.FromImage(resizedBitmap))
                {
                    // 设置插值模式，以提高缩放质量
                    graphics.InterpolationMode = System.Drawing.Drawing2D.InterpolationMode.HighQualityBicubic;

                    // 缩放原始图像到新的尺寸
                    graphics.DrawImage(originalBitmap, 0, 0, newWidth, newHeight);
                }

                // 获取原始图像的格式
                ImageFormat originalFormat = originalBitmap.RawFormat;

                // 保存压缩后的图像到临时文件
                string tempPath = Path.Combine(Path.GetTempPath(), Guid.NewGuid().ToString("N") + Path.GetExtension(inputImagePath));
                resizedBitmap.Save(tempPath, originalFormat);

                // 关闭原始图像的文件流
                originalBitmap.Dispose();

                // 复制临时文件到原始文件的位置
                File.Copy(tempPath, inputImagePath, true);

                // 删除临时文件
                File.Delete(tempPath);
            }
        }
    }


    public List<string> uploadFile(string pathname = "file_upload", string limitFileTypes = ".PNG,.JPG,.GIF,.JPEG,.DOCX,.DOC,.PPTX,.XLS,.XLSX,.ZIP,.DOC,.DOCX,.MP4,.PDF,.RTF,.PPT,.TXT", bool onlyOne = false, bool httpUrl = false, int maxSize = -1, int maxPixel = -1)
    {
        List<string> result_list = new List<string>();
        string res = string.Empty;

        //文件参数
        string ext = string.Empty; // 拓展名
        string md5 = string.Empty; //文件MD5
        string path = string.Empty; // 文件存放路径


        HttpFileCollection fsc = context.Request.Files;
        HttpPostedFile file = null;
        for (int i = 0; i < fsc.Count; i++)
        {
            string filename = string.Empty;
            file = fsc[i];
            res += res == "" ? "" : ",";
            filename = file.FileName;
            if (filename == "blob")
            {
                filename = "test.png";
            }
            ext = Path.GetExtension(filename);
            md5 = GetMD5HashFromStream(file.InputStream);

            path = "~/images/upload/";
            checkPath(path);

            path += pathname + "/";
            checkPath(path);

            if (("," + limitFileTypes + ",").IndexOf("," + ext.ToUpper() + ",") == -1)
            {
                if (!string.IsNullOrEmpty(ext))
                {
                    res += ext + "|文件类型不允许上传";
                }
                continue;
            }

            if (maxSize != -1)
            {
                if (file.ContentLength > maxSize)
                {
                    sendResponse("上传图片必须小于" + GetFileSizeString(maxSize));
                }
            }

            path = path + md5 + ext;
            file.SaveAs(System.Web.HttpContext.Current.Server.MapPath(path));



            if (maxPixel != -1)
            {
                // 压缩图片并保存
                CompressAndReplaceImage(Server.MapPath(path), maxPixel, maxPixel);
            }

            if (httpUrl)
            {
                path = path.Replace("~/", getHost(HttpContext.Current.Request) + "/");
            }
            else
            {
                path = path.Replace("~/", "../");
            }
            res += path;

            result_list.Add(path);

            if (onlyOne)
            {
                break;
            }
        }
        return result_list;
    }

    public void checkCode()
    {
        string sessionCode = string.Empty;
        string code = req("code");
        if (string.IsNullOrEmpty(code)) sendResponse("请输入验证码");
        try
        {
            sessionCode = Session["CheckCode"].ToString();
        }
        catch (Exception)
        {
            sendResponse("请重新输入验证码");
        }
        if (code.ToUpper() != sessionCode.ToUpper()) sendResponse("您输入的验证码错误");
        Session["CheckCode"] = null;
    }

    public List<SqlParameter> collectReqParames()
    {
        bool checkResult = false;
        return collectReqParames(out checkResult);
    }

    public List<SqlParameter> g_pams()
    {
        return collectReqParames();
    }


    public List<SqlParameter> collectReqParames(out bool checkResult)
    {
        checkResult = true;
        string repeatText = ",";
        string text = string.Empty;
        string doname = context.Request.QueryString["do"] + "";
        SqlParameter[] p = new SqlParameter[] { };
        List<SqlParameter> list = new List<SqlParameter>();

        for (int i = 0; i < context.Request.Form.Count; i++)
        {
            text = context.Request.Form.Keys[i];
            if (text == null)
            {
                continue;
            }
            if (text.IndexOf("];") == -1 && repeatText.IndexOf("," + text + ",") == -1)
            {
                repeatText += text + ",";
                if (text.Length < 128)
                {
                    list.Add(
                        new SqlParameter("@" + text, context.Request.Form[i])
                    );
                    if (context.Request.Form[i] == string.Empty)
                    {
                        checkResult = false;
                    }
                }

            }
        }
        for (int i = 0; i < context.Request.QueryString.Count; i++)
        {
            text = context.Request.QueryString.Keys[i];
            if (text.IndexOf("];") == -1 && repeatText.IndexOf("," + text + ",") == -1)
            {
                repeatText += text + ",";
                list.Add(
                    new SqlParameter("@" + text, context.Request.QueryString[i])
                );
                if (context.Request.QueryString[i] == string.Empty)
                {
                    checkResult = false;
                }
            }
        }
        list.Add(new SqlParameter("@userid", uConfig.p_uid));
        list.Add(new SqlParameter("@servid", uConfig.p_idAD));

        string realip = context.Request.ServerVariables["HTTP_X_TENANT_FORWARD_FOR"] + "";
        string[] dnsip = (context.Request.ServerVariables["HTTP_X_TENANT_FORWARD_FOR"] + "").Split('.');
        for (int i = 0; i < dnsip.Length; i++)
        {
            if (!IsNumeric(dnsip[i]))
            {
                realip = "0.0.0.0";
                break;
            }
        }

        list.Add(new SqlParameter("@ip", realip));

        return list;
    }

    public void check_sign(string secret_key = "")
    {
        if (sign(secret_key) != req("sign"))
        {
            sendResponse("sign校验有误");
        }
    }

    public void checkTimesTampSign(string secret_key = "")
    {
        string timestamp = req("timestamp");
        string signstr = secret_key + timestamp;

        //sendResponse(signstr + "----" + md5(signstr).ToLower() + "----" + req("sign"));
        if (md5(signstr).ToLower() != req("sign"))
        {
            sendResponse("sign校验有误");
        }
    }

    public string sign(string secret_key)
    {
        string repeatText = ",";
        string text = string.Empty;
        SortedDictionary<string, object> dic = new SortedDictionary<string, object>();

        for (int i = 0; i < context.Request.Form.Count; i++)
        {
            text = context.Request.Form.Keys[i];
            if (text.IndexOf("];") == -1 && repeatText.IndexOf("," + text + ",") == -1)
            {
                repeatText += text + ",";
                dic.Add(text, context.Request.Form[i] + "");
            }
        }
        for (int i = 0; i < context.Request.QueryString.Count; i++)
        {
            text = context.Request.QueryString.Keys[i];
            if (text.IndexOf("];") == -1 && repeatText.IndexOf("," + text + ",") == -1)
            {
                repeatText += text + ",";
                dic.Add(text, context.Request.QueryString[i] + "");
            }
        }

        string str = string.Empty;
        foreach (var item in dic)
        {
            if (!string.IsNullOrEmpty(item.Value + ""))
            {
                if (item.Key == "sign")
                {
                    continue;
                }
                str += item.Key + '=' + item.Value + '&';
            }
        }
        str = str.Trim('&') + "&" + secret_key;

        int showTest = 1;
        if (showTest == 1)
        {
            Dictionary<string, object> tempd = new Dictionary<string, object>();
            tempd.Add("signstr", str);
            sendResponse(md5(str).ToLower(), 0, tempd);
        }

        str = md5(str).ToLower();
        return str;
    }


    public List<string> getCheckValues(string name)
    {
        string temp = string.Empty;
        return getCheckValues(name, out temp);
    }

    public List<string> getCheckValues(string name, out string outValue, string fgf = ",")
    {
        string key = string.Empty;
        string text = string.Empty;
        outValue = string.Empty;
        List<string> ls = new List<string>();
        for (int i = 0; i < context.Request.Form.Count; i++)
        {
            key = context.Request.Form.Keys[i];
            text = getContainer(key, name + ":[", "];");
            if (!string.IsNullOrEmpty(text))
            {
                ls.Add(text);
                outValue += string.IsNullOrEmpty(outValue) ? "" : fgf;
                outValue += text;
            }
        }
        return ls;
    }

    public static string response_msg_lable = "info";

    public void setResponseLable(string name)
    {
        response_msg_lable = name;
    }

    public void sendResponse(string message, int code = -1, Dictionary<string, object> dic = null)
    {
        if (dic == null)
        {
            dic = new Dictionary<string, object>();
        }
        Dictionary<string, object> _json = new Dictionary<string, object>();
        _json.Add("code", code);
        _json.Add(response_msg_lable, message);
        foreach (var k in dic)
        {
            _json.Add(k.Key, k.Value);
        }

        string result = JsonMapper.ToJson(_json);

        if (response_aes_encry)
        {
            result = setAesResponse(result);
        }

        context.Response.ContentType = "application/json";
        context.Response.Write(result);
        context.Response.End();
    }


    public void jsonResponse(string jsonText)
    {
        context.Response.ContentType = "application/json";
        context.Response.Write(jsonText);
        context.Response.End();
    }

    public void sendRsp(int res, string success_result, string error_result)
    {
        string[] g;
        if (res > 0)
        {
            sendResponse(success_result, 1);
        }
        else
        {
            int errcode = -1;
            if (error_result.IndexOf('|') != -1)
            {
                g = error_result.Split('|');
                errcode = Convert.ToInt16(g[1]);
                error_result = g[0];
            }
            sendResponse(error_result, errcode);
        }
    }

    public void check_selects(string[] g)
    {
        foreach (string item in g)
        {
            var g2 = item.Split(',');//id,name,select1|select2
            var tiptext = "选项有误";
            if (g2.Length == 2)
            {
                tiptext = "请选择" + g2[1];
            }
            else
            {
                tiptext = "请选择参数'" + item + "'";
            }
            var list = g2[2].Split('|');

            bool check_success = false;
            try
            {
                string __value = req(g2[0]);
                foreach (string s in list)
                {
                    if (s == __value)
                    {
                        check_success = true;
                        break;
                    }
                }
            }
            catch (Exception)
            {

            }

            if (!check_success)
            {
                sendResponse(tiptext);
            }
        }
    }
    public void check_images(string[] g)
    {
        foreach (string item in g)
        {
            var g2 = item.Split(',');
            var tiptext = "请上传图片";
            if (g2.Length == 2)
            {
                tiptext = "请上传" + g2[1];
            }
            else
            {
                tiptext = "参数'" + item + "'未填写";
            }

            bool check_success = false;
            try
            {
                string __value = req(g2[0]);
                if (!string.IsNullOrEmpty(__value))
                {
                    if (File.Exists(Server.MapPath(__value)))
                    {
                        check_success = true;
                    }
                }
            }
            catch (Exception)
            {

            }

            if (!check_success)
            {
                sendResponse(tiptext);
            }
        }
    }
    public void check_exist(string[] g)
    {
        Dictionary<string, object> dic = new Dictionary<string, object>();
        foreach (string item in g)
        {
            var g2 = item.Split(',');
            var tiptext = "请填写完整的信息";
            if (g2.Length == 2)
            {
                tiptext = g2[1] + "不能为空";
            }
            else
            {
                tiptext = "必须填写参数" + item;
            }

            bool checkint = false;
            bool checkdate = false;
            bool checkOptionint = false;
            bool checkPlusint = false;

            bool checkres = true;
            if (g2[0].IndexOf("-int") != -1) { g2[0] = g2[0].Replace("-int", ""); checkint = true; }
            if (g2[0].IndexOf("-date") != -1) { g2[0] = g2[0].Replace("-date", ""); checkdate = true; }
            if (g2[0].IndexOf("-oint") != -1) { g2[0] = g2[0].Replace("-oint", ""); checkOptionint = true; }
            if (g2[0].IndexOf("-pint") != -1) { g2[0] = g2[0].Replace("-pint", ""); checkPlusint = true; }
            string data = req(g2[0]);

            checkres = !checkres || (checkint && !IsNumeric(data)) ? false : checkres;
            checkres = !checkres || (checkdate && !IsDate(data)) ? false : checkres;
            checkres = !checkres || (checkOptionint && data != "0" && data != "1") ? false : checkres;
            checkres = !checkres || (checkPlusint && !IsNumeric(data) && Convert.ToDouble(data) <= 0) ? false : checkres;
            checkres = !checkres || string.IsNullOrEmpty(data) ? false : checkres;



            if (false == checkres)
            {
                dic.Add("code", -1);
                dic.Add(response_msg_lable, tiptext);
                context.Response.ContentType = "application/json";
                context.Response.Write(JsonMapper.ToJson(dic));
                context.Response.End();
            }
        }
    }

    public bool exists(string name, bool checkAll = true)
    {
        string res;
        if (json_data != null)
        {
            try
            {
                res = json_data[name] + "";
            }
            catch (Exception)
            {
                res = null;
            }
        }
        else
        {
            res = context.Request.Form[name];
            if (checkAll && res == null)
            {
                res = context.Request.QueryString[name];
            }
        }
        return res != null;
    }

    public string req(string name, bool checkAll = true)
    {
        string res = string.Empty;
        if (json_data != null)
        {
            try
            {
                res = json_data[name] + "";
            }
            catch (Exception)
            {
            }
        }
        else
        {
            res = context.Request.Form[name] + "";
            if (checkAll && string.IsNullOrEmpty(res))
            {
                res = context.Request.QueryString[name] + "";
            }
        }
        return res;
    }
    public string sreq(string name, bool checkAll = true)
    {
        return SafeSql(req(name, checkAll));
    }

    public bool empty(string name, bool checkAll = true)
    {
        return string.IsNullOrEmpty(req(name, checkAll));
    }
    public string select(bool check, string v1 = "", string v2 = "")
    {
        if (check)
        {
            return v1;
        }
        return v2;
    }


    public string sqlreq(string name, bool checkAll = true)
    {
        string res = string.Empty;
        res = req(name, checkAll);
        res = SafeSql(res);
        return res;
    }

    JsonData json_data;
    bool response_aes_encry = false;
    string aes_key = string.Empty;
    public string setAesResponse(string text)
    {
        Dictionary<string, object> dic = new Dictionary<string, object>();
        dic.Add("code", 1);
        dic.Add(response_msg_lable, "success");
        dic.Add("data", aes.AESEncrypt(text, aes_key, "115a8d5de3e196d1"));
        return JsonMapper.ToJson(dic);
    }
    public string getResult(string message, int code = -1, Dictionary<string, object> dic = null)
    {
        string result = getResBody(message, code, dic);
        if (response_aes_encry)
        {
            result = setAesResponse(result);
        }
        return result;
    }
    public void check_data(bool _resultIsAes = false, bool _requestIsAes = true)
    {
        response_aes_encry = _resultIsAes;
        string result = check_data_call(_requestIsAes);
        if (!string.IsNullOrEmpty(result))
        {
            context.Response.ContentType = "application/json";
            context.Response.Write(result);
            context.Response.End();
        }
    }
    List<string> _datePams = new List<string>();
    public void pamsdate(List<string> _date)
    {
        _datePams = _date;
    }
    public void pamsadd(string name, string value)
    {
        json_data[name] = value;
    }
    public SqlParameter[] pams
    {
        get
        {
            List<SqlParameter> pam = new List<SqlParameter>();
            try
            {
                IDictionary dic = json_data;
                foreach (string item in dic.Keys)
                {
                    string _name = item + "";
                    object _value;
                    if (json_data[item] == null)
                    {
                        _value = DBNull.Value;
                    }
                    else
                    {
                        _value = json_data[item] + "";
                    }
                    for (int i = 0; i < _datePams.Count; i++)
                    {
                        if (_name == _datePams[i])
                        {
                            _value = new DateTime(1970, 1, 1, 8, 0, 0, 0).AddSeconds(Convert.ToDouble(_value)).ToString();
                        }
                    }
                    pam.Add(new SqlParameter("@" + _name, _value));
                }
                pam.Add(new SqlParameter("@ip", getUserIP()));
            }
            catch (Exception s)
            {
                string temp = s.Message;
            }
            return sqlParamesCheck(needparams, pam.ToArray());
        }
    }


    public string initJsonData(string data)
    {
        try
        {
            json_data = JsonMapper.ToObject(data);
        }
        catch (Exception ex)
        {
            return getResult("请求数据解析失败");
        }
        return string.Empty;
    }


    public string check_data_call(bool _requestIsAes = true)
    {
        //1.客户端随机生成AES秘钥
        //2.通过AES秘钥加密当前时间和业务数据
        //3.通过RSA公钥加密AES秘钥
        //4.发送加密后的当前时间和加密后的业务数据和加密后的AES秘钥
        //5.服务器收到数据拿RSA私钥解密加密后的AES秘钥,得到AES秘钥
        //6.服务器拿AES秘钥即可解密客户端发来的相关数据
        //7.服务器拿AES秘钥加密返回结果后再返回给客户端
        //8.客户端收到密文数据用之前生成的AES秘钥解密

        string time = req("time");
        string data = req("data");
        string auth = req("auth");
        try
        {
            if (_requestIsAes)
            {
                try
                {
                    auth = rsa.RsaDecrypt(auth);
                    aes_key = auth;
                }
                catch (Exception ex)
                {
                    response_aes_encry = false;
                    return getResult("操作失败,请再重试~");
                    return getResult("AUTH:" + ex.Message);
                }
                try
                {
                    data = aes.AESDecrypt(data, auth, "115a8d5de3e196d1");
                }
                catch (Exception ex)
                {
                    response_aes_encry = false;
                    return getResult("操作失败,请再重试~");
                    return getResult("DATA:" + ex.Message);
                }

                try
                {
                    time = aes.AESDecrypt(time, auth, "115a8d5de3e196d1");
                }
                catch (Exception ex)
                {
                    response_aes_encry = false;
                    return getResult("操作失败,请再重试~");
                    return getResult("TIME:" + ex.Message + ":" + data + ":" + auth + ":" + time);
                }
                DateTime dtime;
                if (time.Length == 10)
                {
                    DateTime dtStart = TimeZone.CurrentTimeZone.ToLocalTime(new DateTime(1970, 1, 1));
                    long lTime = long.Parse(time + "0000000");
                    TimeSpan toNow = new TimeSpan(lTime);
                    dtime = dtStart.Add(toNow);
                }
                else
                {
                    DateTime dtStart = TimeZone.CurrentTimeZone.ToLocalTime(new DateTime(1970, 1, 1));
                    long lTime = long.Parse(time + "0000");
                    TimeSpan toNow = new TimeSpan(lTime);
                    dtime = dtStart.Add(toNow);
                }


                if (GetNowTimeSpanSec(dtime) > 600)//10分钟
                {
                    return getResult("接口已到期");
                }
            }
            else
            {
                using (var reader = new System.IO.StreamReader(context.Request.InputStream))
                {
                    data = reader.ReadToEnd();
                }
            }


            try
            {
                json_data = JsonMapper.ToObject(data);
            }
            catch (Exception ex)
            {
                return getResult("请求数据解析失败");
            }
        }
        catch (Exception)
        {

        }
        return string.Empty;
    }


    public static int GetNowTimeSpanSec(DateTime _time)
    {
        DateTime now = DateTime.Now;

        TimeSpan ts = now.Subtract(_time);

        int sec = (int)ts.TotalSeconds;

        return sec;
    }

    #region 短信模块

    //发送短信
    public string send_sms()
    {
        string phone = req("phone");
        string scene = req("scene");
        string sms_text = string.Empty;

        dbClass db = new dbClass();
        List<SqlParameter> pams = new List<SqlParameter>();
        string sql = string.Empty;
        DataTable dt = new DataTable();

        pams.Add(new SqlParameter("@phone", phone));
        pams.Add(new SqlParameter("@scene", scene));
        sql = " [send_sms] @phone,@scene ";
        dt = db.getDataTable(sql, pams.ToArray());
        if (dt.Rows.Count > 0)
        {
            if (dt.Rows[0]["code"] + "" != "1")
            {
                sendResponse(dt.Rows[0]["msg"] + "", (int)dt.Rows[0]["code"]);
            }
            sms_text = dt.Rows[0]["sms_text"] + "";
        }

        return sms_text;
    }

    //短信发送失败记录
    public int sms_error()
    {
        string phone = req("phone");
        string scene = req("scene");

        dbClass db = new dbClass();
        List<SqlParameter> pams = new List<SqlParameter>();
        string sql = string.Empty;
        DataTable dt = new DataTable();

        pams.Add(new SqlParameter("@phone", phone));
        pams.Add(new SqlParameter("@scene", scene));
        sql = " update sms_message_list set send_times=send_times-1 where phone=@phone and scene=@scene ";
        return db.ExecuteNonQuery(sql, pams.ToArray());
    }

    //短信验证
    public void sms_verify()
    {
        string phone = req("phone");
        string scene = req("scene");
        string code = req("code");
        if (string.IsNullOrEmpty(code))
        {
            sendResponse("短信码不正确", -2);
        }

        dbClass db = new dbClass();
        List<SqlParameter> pams = new List<SqlParameter>();
        string sql = string.Empty;
        DataTable dt = new DataTable();

        pams.Add(new SqlParameter("@phone", phone));
        pams.Add(new SqlParameter("@scene", scene));
        pams.Add(new SqlParameter("@code", code));
        sql = " update sms_message_list set sms_text=null where phone=@phone and scene=@scene and sms_text=@code and DATEDIFF(SECOND,send_date,GETDATE())<600 ";
        int res = db.ExecuteNonQuery(sql, pams.ToArray());
        if (res == 0)
        {
            sendResponse("短信码不正确", -2);
        }
    }

    #endregion


    List<string> pager_cols = new List<string>();
    string pager_cond = string.Empty;
    bool pager_fastResponse = false;
    int response_type = 0;
    bool response_html = false;
    bool _showNull = true;

    public void setResponseData(bool _html, bool is_show_null = true)
    {
        response_html = _html;
        _showNull = is_show_null;
    }

    public void pager_list(List<string> lists, bool fast_response = false, int rt = 0)
    {
        pager_cols = lists;
        pager_fastResponse = fast_response;
        response_type = rt;
    }
    public void pager_where(bool when_this_success, string success_cond, string fail_cond = "")
    {
        if (when_this_success)
        {
            pager_cond += pager_cond == string.Empty ? string.Empty : " and ";
            pager_cond += " " + success_cond + " ";
        }
        else
        {
            if (!string.IsNullOrEmpty(fail_cond))
            {
                pager_cond += pager_cond == string.Empty ? string.Empty : " and ";
                pager_cond += " " + fail_cond + " ";
            }
        }
    }

    public string getCond()
    {
        pager_cond = pager_cond.Replace("@{ip}", getUserIP());
        pager_cond = pager_cond.Replace("@{userid}", uConfig.p_uid);
        List<string> arr = getContainerMul(pager_cond, "@{", "}", true);
        for (int i = 0; i < arr.Count; i++)
        {
            string value = arr[i];
            value = sreq(value);
            pager_cond = pager_cond.Replace("@{" + arr[i] + "}", value);
        }

        arr = getContainerMul(pager_cond, "#{", "}", true);
        for (int i = 0; i < arr.Count; i++)
        {
            string value = arr[i];
            value = req(value);
            pager_cond = pager_cond.Replace("#{" + arr[i] + "}", value);
        }
        return pager_cond;
    }

    bool default_from_cache = false;
    int default_cache_time_second = 60;

    public void set_cache_config(bool from_cache = true, int cache_time_second = 60)
    {
        default_from_cache = from_cache;
        default_cache_time_second = cache_time_second;
    }

    public Dictionary<string, object> pager_data(string db, string select_items, string orderby, string page, string size, int v_from_cache = -1, int v_cache_time_second = -1, bool is_export = false, string export_name = "默认表格")
    {
        pager_cond = pager_cond.Replace("@{ip}", getUserIP());
        pager_cond = pager_cond.Replace("@{userid}", uConfig.p_uid);
        List<string> arr = getContainerMul(pager_cond, "@{", "}", true);
        for (int i = 0; i < arr.Count; i++)
        {
            string value = arr[i];
            value = sreq(value);
            pager_cond = pager_cond.Replace("@{" + arr[i] + "}", value);
        }

        arr = getContainerMul(pager_cond, "#{", "}", true);
        for (int i = 0; i < arr.Count; i++)
        {
            string value = arr[i];
            value = req(value);
            pager_cond = pager_cond.Replace("#{" + arr[i] + "}", value);
        }


        bool from_cache = default_from_cache;
        int cache_time_second = default_cache_time_second;
        if (v_from_cache != -1)
        {
            from_cache = (v_from_cache == 1 ? true : false);
        }
        if (v_cache_time_second != -1)
        {
            cache_time_second = v_cache_time_second;
        }



        if (is_export)
        {
            dbClass db1 = new dbClass();
            DataTable dt = new DataTable();
            string sql = string.Empty;

            sql = string.Format(" select {1} from {0} {2} {3} ", db, select_items, (pager_cond == "" ? "" : "  where " + pager_cond), (orderby == "" ? "" : "  order by " + orderby));

            //sendResponse(sql+"xxxxxxxxx");
            //return new Dictionary<string, object>();

            dt = db1.getDataTable(sql, pams.ToArray());

            //// 反向排序
            //DataTable reversedDataTable = dt.Clone(); // 创建一个与原始表结构相同的新表

            //for (int i = dt.Rows.Count - 1; i >= 0; i--)
            //{
            //    DataRow row = dt.Rows[i];
            //    reversedDataTable.ImportRow(row); // 将原始表的行导入新表
            //}

            //// 更新原始 DataTable
            //dt = reversedDataTable;


            //if (pager_cols.Count == 0)
            //{
            //    for (int i = 0; i < dt.Columns.Count; i++)
            //    {
            //        pager_cols.Add(dt.Columns[i] + "");
            //    }
            //}



            CreateExcel(dt, "application/ms-excel", export_name + "_Export_" +
            DateTime.Now.ToString("yyyyMMdd_HHmmss"), pager_cols);
            return new Dictionary<string, object>();
        }

        page = string.IsNullOrEmpty(page) ? "1" : page;
        page = (Convert.ToInt16(page) - 1).ToString();
        Dictionary<string, object> dic = jsonClass.queryPagerD(db, pager_cols, pager_cond, select_items, orderby, page, size, "{}", "", _showNull, from_cache, cache_time_second);
        dic["page"] = Convert.ToInt16(dic["page"]) + 1;
        if (pager_fastResponse)
        {
            pager_common_response(dic);
        }
        return dic;
    }

    public Dictionary<string, object> pager_data(string db, string select_items, string orderby = "")
    {
        return pager_data(db, select_items, orderby, req("page"), req("limit"));
    }

    public Dictionary<string, object> pager_common_response(Dictionary<string, object> dic, bool isread = false, Dictionary<string, object> base_dic = null)
    {
        Dictionary<string, object> _data = new Dictionary<string, object>();
        if (response_html)
        {
            _data.Add("html", dic["pageHtml"]);
        }
        if (base_dic == null)
        {
            base_dic = new Dictionary<string, object>();
        }
        switch (response_type)
        {
            case 1:
                _data.Add("data", dic["data"]);
                dic = _data;
                break;
            default:
                _data.Add("pager", dic["pager"]);
                _data.Add("page", dic["page"]);
                _data.Add("limit", dic["limit"]);
                _data.Add("count", dic["total"]);
                _data.Add("list", dic["data"]);
                dic = base_dic;
                dic.Add("data", _data);
                break;
        }
        if (isread == false)
        {
            sendResponse("成功", 1, dic);
        }
        return dic;
    }



    public Dictionary<string, object> eachSql(string sql, string cols, bool is_response = true)
    {
        List<Dictionary<string, object>> _list = new List<Dictionary<string, object>>();
        Dictionary<string, object> _dic = new Dictionary<string, object>();
        List<object> _result = new List<object>();
        string[] n_arr;
        string n1 = string.Empty;
        string n2 = string.Empty;
        dbClass db = new dbClass();
        DataTable dt = db.getDataTable(sql, pams);
        string[] g = cols.Split(',');

        globalClass gc = new globalClass();
        string temp = string.Empty;
        string tempval = string.Empty;
        object value = string.Empty;

        if (g.Length == 1)
        {
            for (int i = 0; i < dt.Rows.Count; i++)
            {
                n_arr = cols.Split(new string[] { " as " }, StringSplitOptions.None);

                temp = gc.getContainer(cols, "@", "(");
                tempval = gc.getContainer(cols, "@" + temp + "(", ")");
                cols = cols.Replace("@" + temp + "(" + tempval + ")", "");


                n1 = n_arr[0];
                n2 = n_arr.Length > 1 ? n_arr[1] : n_arr[0];
                value = dt.Rows[i][n1];
                value = funCheck(temp, value, tempval);
                _result.Add(value);

                _dic = new Dictionary<string, object>();
                _dic.Add(n2, value);
                _list.Add(_dic);
            }
            _dic = new Dictionary<string, object>();
            if (cols.IndexOf("as") != -1)
            {
                _dic.Add("data", _list);
            }
            else
            {
                _dic.Add("data", _result);
            }
        }
        else
        {
            for (int i = 0; i < dt.Rows.Count; i++)
            {
                _dic = new Dictionary<string, object>();
                for (int t = 0; t < g.Length; t++)
                {
                    temp = gc.getContainer(g[t], "@", "(");
                    tempval = gc.getContainer(g[t], "@" + temp + "(", ")");
                    g[t] = g[t].Replace("@" + temp + "(" + tempval + ")", "");

                    n_arr = g[t].Split(new string[] { " as " }, StringSplitOptions.None);
                    n1 = n_arr[0];
                    n2 = n_arr.Length > 1 ? n_arr[1] : n_arr[0];

                    _dic.Add(n2, funCheck(temp, dt.Rows[i][n1], tempval));
                }
                _list.Add(_dic);
            }
            _dic = new Dictionary<string, object>();
            _dic.Add("data", _list);
        }

        if (is_response)
        {
            sendResponse("成功", 1, _dic);
        }

        return _dic;
    }

    public object funCheck(string fun_name, object key_value, string fun_val)
    {
        object _result = string.Empty;
        string[] pam = fun_val.Split(',');
        if (key_value == DBNull.Value || key_value == null)
        {
            return null;
        }
        switch (fun_name)
        {
            case "time":
                _result = Convert.ToInt64(ConvertDateToTimeSpan(key_value.ToString()));
                break;
            default:
                _result = key_value;
                break;
        }
        return _result;
    }

    public string at(string name, string default_text = "null")
    {
        string[] arr = name.Split(new string[] { " as " }, StringSplitOptions.None);
        string atNmae = string.Empty;
        name = arr[0];
        atNmae = "@" + name;
        if (arr.Length > 1)
        {
            atNmae = arr[1];
        }
        if (exists(name))
        {
            if (req(name) != "~~def")
            {
                return atNmae;
            }
        }
        return default_text;
    }

    public string formatOrderids(string orderids, int maxnum = 0)
    {
        string[] s = orderids.Split(',');
        int check_count = 0;

        orderids = "";
        for (int i = 0; i < s.Length; i++)
        {
            if (IsNumeric(s[i]) && s[i].Length < 10)
            {
                if (orderids != "")
                {
                    orderids += ",";
                }
                orderids += s[i];
                check_count += 1;
            }
            if (maxnum > 0)
            {
                if (check_count >= maxnum)
                {
                    break;
                }
            }
        }
        return orderids;
    }

    List<string> needparams = new List<string>();
    public void existsSqlParam(List<string> p)
    {
        needparams = p;
    }

    //有缺点，自己定义的参数也会被带进去
    public static SqlParameter[] sqlParamesCheck(List<string> _needparams, SqlParameter[] parames)
    {
        List<string> _existsKeys = new List<string>();
        List<SqlParameter> p = new List<SqlParameter>();
        foreach (SqlParameter _p in parames)
        {
            _existsKeys.Add(_p.ParameterName);
            p.Add(new SqlParameter(_p.ParameterName, _p.Value));
        }
        for (int i = 0; i < _needparams.Count; i++)
        {
            if (!_existsKeys.Exists(q => q == _needparams[i]))
            {
                _existsKeys.Add(_needparams[i]);
                p.Add(new SqlParameter(_needparams[i], DBNull.Value));
            }
        }
        parames = p.ToArray();
        return parames;
    }

    public bool qx(string qxname, bool is_response = true)
    {
        return true;
        bool is_limit = true;
        DataTable dt = cache_data.role;
        string roleid = uConfig.p_roleAD;
        if (roleid != "-1")
        {
            string[] arr = qxname.Split('|');

            for (int i = 0; i < dt.Rows.Count; i++)
            {
                if (dt.Rows[i]["id"] + "" == roleid)
                {
                    string quanxian = dt.Rows[i]["quanxian"] + "";
                    for (int a = 0; a < arr.Length; a++)
                    {
                        if (("|" + quanxian + "|").IndexOf("|" + arr[a] + "|") != -1)
                        {
                            is_limit = false;
                        }
                        break;
                    }
                    if (is_limit == false)
                    {
                        break;
                    }
                }
            }
        }
        else
        {
            is_limit = false;
        }
        if (is_limit && is_response)
        {
            sendResponse("权限不足");
        }
        return is_limit == false;
    }

    DataTable where_dt = new DataTable();
    public void set_log_where_dt(DataTable dt)
    {
        where_dt = dt;
    }
    public void log(string log_text, string where_status, bool noEmpty = false)
    {
        if (where_dt.Rows.Count > 0)
        {
            string[] arr = where_status.Split('|');
            string dt_name = where_status;
            string fz_name = where_status;
            if (arr.Length > 1)
            {
                dt_name = arr[0];
                fz_name = arr[1];
            }

            object obj_value = where_dt.Rows[0][dt_name];
            if (obj_value != DBNull.Value && obj_value.GetType() == (new DateTime()).GetType())
            {
                obj_value = Convert.ToDateTime(obj_value).ToString("yyyy-MM-dd HH:mm:ss");
            }

            if (obj_value + "" != req(fz_name))
            {
                if (!noEmpty || req(fz_name) != "")
                {
                    log(log_text);
                }
            }
        }
    }
    public void log(string log_text)
    {
        DataTable dt = new DataTable();
        List<SqlParameter> pams = new List<SqlParameter>();
        dbClass db = new dbClass();
        string sql = string.Empty;
        int res = 0;
        log_text = log_text.Replace("@{ip}", getUserIP());
        List<string> arr = getContainerMul(log_text, "@{", "}", true);
        for (int i = 0; i < arr.Count; i++)
        {
            string value = arr[i];
            if (!exists(arr[i]))
            {
                try
                {
                    object obj_value = where_dt.Rows[0][arr[i]];
                    if (obj_value != DBNull.Value && obj_value.GetType() == (new DateTime()).GetType())
                    {
                        obj_value = Convert.ToDateTime(obj_value).ToString("yyyy-MM-dd HH:mm:ss");
                    }
                    value = obj_value + "";

                }
                catch (Exception)
                {

                }
            }
            else
            {
                value = req(value);
            }
            log_text = log_text.Replace("@{" + arr[i] + "}", value);
        }

        string title = log_text.Split(' ')[0];
        if (log_text.Split(' ').Length > 1)
        {
            log_text = log_text.Replace(title + " ", "");
        }
        else
        {
            title = string.Empty;
        }

        pams.Add(new SqlParameter("@servid", uConfig.p_idAD));
        pams.Add(new SqlParameter("@title", title));
        pams.Add(new SqlParameter("@log_text", log_text));
        sql = " insert into serv_admin_logs values(@servid,@title,@log_text,1,getdate()) ";
        res = db.ExecuteNonQuery(sql, pams.ToArray());
    }

    public void check(bool noresposne, string response_msg = "权限不足")
    {
        return;
        if (!noresposne)
        {
            sendResponse(response_msg);
        }
    }

    public bool IsPositiveNumber(string str)
    {
        str = req(str);
        return IsNumeric(str) && Convert.ToDouble(str) > 0;
    }




    //通过缓存检测限制
    public bool limit_check(string type_name, Int16 limit_second = 60, bool fast_response = true, string check_from = "ip")
    {
        bool is_limit = false;

        if (check_from == "ip")
        {
            check_from = getUserIP();
        }

        string _key = check_from + "_" + type_name;

        string check_val = cae.GetCache<string>(_key);

        if (!string.IsNullOrEmpty(check_val))
        {
            is_limit = true;
        }
        else
        {
            cae.SetCache(_key, "limit", DateTime.Now.AddSeconds(limit_second), System.Web.Caching.Cache.NoSlidingExpiration);
        }

        if (fast_response && is_limit)
        {
            sendResponse("操作过于频繁，请稍后再试！");
        }

        return is_limit;
    }


    public void checkGoogleActCode(string servid = "")
    {
        if (string.IsNullOrEmpty(servid))
        {
            return;
        }
        List<SqlParameter> pms = new List<SqlParameter>();
        pms.Add(new SqlParameter("@servid", servid));
        string sql = " select * from serv_admin with(nolock) where id=@servid ";
        dbClass db = new dbClass();
        DataTable dt = new DataTable();
        dt = db.getDataTable(sql, pms.ToArray());

        if (dt.Rows.Count == 0)
        {
            sendResponse("用户不存在");
        }

        string gkey = dt.Rows[0]["google_key"] + "";
        if (gkey == "")
        {
            sendResponse("请联系管理员更换谷歌验证码");
        }

        GoogleAuthenticator authenticator = new GoogleAuthenticator(30, gkey + "!!transaction_system##01800");
        if (authenticator.GenerateCode() != req("actCode"))
        {
            //sendResponse("当前验证码：" + authenticator.GenerateCode() + "[" + gkey + "]！");
            sendResponse("验证码错误，请重新输入！");
        }
    }


    public string lundroidapi(string acct_pan, string acct_name)
    {
        String host = "https://lundroid.com";
        String path = "/composite/bankvertify";
        String method = "GET";

        String querys = "bankno=" + acct_pan + "&name=" + Uri.EscapeDataString(acct_name) + "&appkey=" + uConfig.stcdata("lundroid_appkey");
        String bodys = "";
        String url = host + path;
        HttpWebRequest httpRequest = null;
        HttpWebResponse httpResponse = null;

        if (0 < querys.Length)
        {
            url = url + "?" + querys;
        }

        if (host.Contains("https://"))
        {
            ServicePointManager.ServerCertificateValidationCallback = new RemoteCertificateValidationCallback(CheckValidationResult);
            httpRequest = (HttpWebRequest)WebRequest.CreateDefault(new Uri(url));
        }
        else
        {
            httpRequest = (HttpWebRequest)WebRequest.Create(url);
        }
        httpRequest.Method = method;
        if (0 < bodys.Length)
        {
            byte[] data = Encoding.UTF8.GetBytes(bodys);
            using (Stream stream = httpRequest.GetRequestStream())
            {
                stream.Write(data, 0, data.Length);
            }
        }
        try
        {
            httpResponse = (HttpWebResponse)httpRequest.GetResponse();
        }
        catch (WebException ex)
        {
            httpResponse = (HttpWebResponse)ex.Response;
        }

        Stream st = httpResponse.GetResponseStream();
        StreamReader reader = new StreamReader(st, Encoding.GetEncoding("utf-8"));
        return reader.ReadToEnd();
    }

    public string alicloudapi(string acct_pan, string acct_name)
    {
        String host = "https://lundroid.market.alicloudapi.com";
        String path = "/lianzhuo/verifi";
        String method = "GET";
        String appcode = uConfig.stcdata("ali_appcode");

        String querys = "acct_pan=" + acct_pan + "&acct_name=" + Uri.EscapeDataString(acct_name);
        String bodys = "";
        String url = host + path;
        HttpWebRequest httpRequest = null;
        HttpWebResponse httpResponse = null;

        if (0 < querys.Length)
        {
            url = url + "?" + querys;
        }

        if (host.Contains("https://"))
        {
            ServicePointManager.ServerCertificateValidationCallback = new RemoteCertificateValidationCallback(CheckValidationResult);
            httpRequest = (HttpWebRequest)WebRequest.CreateDefault(new Uri(url));
        }
        else
        {
            httpRequest = (HttpWebRequest)WebRequest.Create(url);
        }
        httpRequest.Method = method;
        httpRequest.Headers.Add("Authorization", "APPCODE " + appcode);
        if (0 < bodys.Length)
        {
            byte[] data = Encoding.UTF8.GetBytes(bodys);
            using (Stream stream = httpRequest.GetRequestStream())
            {
                stream.Write(data, 0, data.Length);
            }
        }
        try
        {
            httpResponse = (HttpWebResponse)httpRequest.GetResponse();
        }
        catch (WebException ex)
        {
            httpResponse = (HttpWebResponse)ex.Response;
        }

        Stream st = httpResponse.GetResponseStream();
        StreamReader reader = new StreamReader(st, Encoding.GetEncoding("utf-8"));
        return reader.ReadToEnd();
    }
    public static bool CheckValidationResult(object sender, X509Certificate certificate, X509Chain chain, SslPolicyErrors errors)
    {
        return true;
    }


    public string verifyBank(string acct_pan, string acct_name)
    {
        string response_text = string.Empty;


        if (HttpContext.Current.Request.Url.Host == "localhost")
        {
            return "成功";
        }
        //response_text = alicloudapi(acct_pan, acct_name);

        //set_logs("verifyBank", "acct_pan=" + acct_pan + "&acct_name=" + acct_name + "&response_text=" + response_text, "api");

        //JsonData jd = JsonMapper.ToObject(response_text);

        //try
        //{
        //    response_text = jd["resp"]["desc"] + "";
        //    if (response_text == "OK")
        //    {
        //        response_text = "成功";
        //    }
        //}
        //catch (Exception)
        //{
        //    response_text = "卡号验证异常";
        //}


        response_text = lundroidapi(acct_pan, acct_name);
        set_logs("verifyBank_lundroid", "acct_pan=" + acct_pan + "&acct_name=" + acct_name + "&response_text=" + response_text, "api");

        JsonData jd = JsonMapper.ToObject(response_text);

        try
        {
            response_text = jd["resp"]["desc"] + "";
            if (response_text == "OK")
            {
                response_text = "成功";
            }
        }
        catch (Exception)
        {
            response_text = "卡号验证异常";
        }

        return response_text;

    }

    public string getSms(string type, string userid, string phone = "")
    {
        string response_text = string.Empty;

        string sql = string.Empty;
        dbClass db = new dbClass();
        DataTable dt = new DataTable();
        List<SqlParameter> pams = new List<SqlParameter>();

        pams.Add(new SqlParameter("@type", type));
        pams.Add(new SqlParameter("@userid", userid));
        pams.Add(new SqlParameter("@phone", phone));

        sql = @"

    if(@userid<>-1)
	begin
		set @phone=null
		select @phone=phone from accounts with(nolock) where id=@userid
	end
	if(LEN(ISNULL(@phone,''))<>11)
	begin
		select '手机号有误' as errmsg
		return
	end
	
		
		declare @RandomCode varchar(6)
		set @RandomCode= CAST(100000 + (CAST(RAND() * (999999 - 100000) AS INT)) AS VARCHAR(6))
	
	declare @number int
    set @number=0
	declare @sid int
	declare @send_time datetime
	select @sid=id,@send_time=create_time,@number=(case when datediff(day,create_time,getdate())=0 then number else 0 end) from sms_code_list with(nolock) where phone=@phone and type=@type
	
	if(@number>5)
	begin
		select '发送过于频繁（请明日再试）' as errmsg
		return
	end
	
	if(@sid is not null)
	begin
		if(DATEDIFF(SECOND,@send_time,GETDATE())<2*60)
		begin
			select '发送频繁' as errmsg
			return
		end
		
		if(DATEDIFF(SECOND,@send_time,GETDATE())<24*60*60)
		begin
			set @number=@number+1
		end
		else
		begin
			set @number=1
		end
		
		update sms_code_list set number=@number,code=@RandomCode,create_time=getdate(),verify_number=0 where id=@sid		
	end
	else
	begin
		insert into sms_code_list values(@userid,@type,@phone,@RandomCode,1,0,GETDATE())
		set @sid=scope_identity()
	end
	
	select '获取成功' as errmsg,@RandomCode as code,@sid as sid,@phone as phone
";

        dt = db.getDataTable(sql, pams.ToArray());
        if (dt.Rows.Count == 0)
        {
            return "发送失败";
        }

        response_text = dt.Rows[0]["errmsg"] + "";
        if (response_text == "获取成功")
        {
            string sid = dt.Rows[0]["sid"] + "";
            string verifycode = dt.Rows[0]["code"] + "";
            phone = dt.Rows[0]["phone"] + "";

            string appid = uConfig.stcdata("wapi_appid");
            string appsecret = uConfig.stcdata("wapi_appsecret");
            string template_id = uConfig.stcdata("wapi_template_id");


            if (HttpContext.Current.Request.Url.Host != "localhost")
            {
                ////发送验证码API
                //SortedDictionary<string, string> _sdic = new SortedDictionary<string, string>();
                //string url = "https://qayz.api.storeapi.net/pyi/86/204";
                //string body = "appid=" + appid + "&mobile=" + phone + "&template_id=" + template_id + "&template_param={\"code\":" + verifycode + "}";
                //body += "&sign=" + getMD5(body.Replace("=", "").Replace("&", "") + appsecret);
                //response_text = SendRequest(url, body);
                //JsonData jd = JsonMapper.ToObject(response_text);


                //set_logs("sendsms", body + "--" + response_text);

                //if (jd["codeid"] + "" == "10000")
                //{
                //    response_text = "成功";
                //}
                //else
                //{

                //    response_text = "返回错误：" + response_text + "[body=" + body + "]";
                //    try
                //    {
                //        response_text = jd["message"] + "";
                //    }
                //    catch (Exception)
                //    {
                //        response_text = "发送服务器失败";
                //    }

                //    pams = new List<SqlParameter>();
                //    pams.Add(new SqlParameter("@id", sid));
                //    sql = " update sms_code_list set number=number-1,create_time=dateadd(hour,-1,getdate()) where id=@id ";
                //    db.ExecuteNonQuery(sql, pams.ToArray());
                //}



                response_text = sms_send(phone, verifycode);


                if (response_text != "成功")
                {
                    pams = new List<SqlParameter>();
                    pams.Add(new SqlParameter("@id", sid));
                    sql = " update sms_code_list set number=number-1,create_time=dateadd(hour,-1,getdate()) where id=@id ";
                    db.ExecuteNonQuery(sql, pams.ToArray());
                }

            }
            else
            {
                response_text = "成功";
            }

        }

        return response_text;
    }

    public string sms_send(string phone, string verifycode, string tplid = "")
    {
        string appid = uConfig.stcdata("wapi_appid");
        string appsecret = uConfig.stcdata("wapi_appsecret");
        string response_text = string.Empty;

        SortedDictionary<string, string> _sdic = new SortedDictionary<string, string>();
        string url = string.Empty;
        string body = string.Empty;
        JsonData jd;

        switch (uConfig.stcdata("sms_channel"))
        {
            case "dxb":

                //发送验证码API
                url = "http://api.smsbao.com/sms?u=" + uConfig.stcdata("dxb_username") + "&p=" + md5(uConfig.stcdata("dxb_password")) + "&m=" + phone + "&c=" + Uri.EscapeDataString(uConfig.stcdata("dxb_model").Replace("{code}", verifycode));
                response_text = getContent(url);
                set_logs("dxb", url + "--" + response_text, "sms");
                if (response_text == "0")
                {
                    response_text = "成功";
                }
                else
                {
                    switch (response_text)
                    {
                        case "30":
                            response_text = "密码错误";
                            break;
                        case "40":
                            response_text = "账号不存在";
                            break;
                        case "41":
                            response_text = "余额不足";
                            break;
                        case "43":
                            response_text = "IP地址限制";
                            break;
                        case "50":
                            response_text = "内容含有敏感词";
                            break;
                        case "51":
                            response_text = "手机号码不正确";
                            break;
                        default:
                            break;
                    }
                    response_text = "短信发送失败:" + response_text;

                }

                break;
            default:

                if (string.IsNullOrEmpty(tplid))
                {
                    tplid = uConfig.stcdata("wapi_template_id");
                }


                //发送验证码API
                url = "https://qayz.api.storeapi.net/pyi/86/204";
                body = "appid=" + appid + "&mobile=" + phone + "&template_id=" + tplid + "&template_param={\"code\":" + verifycode + "}";
                body += "&sign=" + getMD5(body.Replace("=", "").Replace("&", "") + appsecret);
                response_text = SendRequest(url, body);
                set_logs("wa", body + "--" + response_text, "sms");
                jd = JsonMapper.ToObject(response_text);


                if (jd["codeid"] + "" == "10000")
                {
                    response_text = "成功";
                }
                else
                {

                    response_text = "返回错误：" + response_text + "[body=" + body + "]";
                    try
                    {
                        response_text = jd["message"] + "";
                    }
                    catch (Exception)
                    {
                        response_text = "发送服务器失败";
                    }
                }

                break;
        }


        return response_text;

    }


    public void set_logs(string title, string content, string type = "demo")
    {
        string debug_path = context.Request.PhysicalApplicationPath + "common_logs";
        try
        {
            string _path = debug_path;
            if (!Directory.Exists(_path))
            {
                Directory.CreateDirectory(_path);
            }
            _path += "/" + type;
            if (!Directory.Exists(_path))
            {
                Directory.CreateDirectory(_path);
            }

            string time = DateTime.Now.ToString("yyyy/MM/dd HH:mm:ss.fff");//获取当前系统时间
            string filename = _path + "/" + DateTime.Now.ToString("yyyy-MM-dd") + ".log";//用日期对日志文件命名

            //创建或打开日志文件，向日志文件末尾追加记录
            StreamWriter mySw = new StreamWriter(filename, true, Encoding.GetEncoding("GB2312"));//默认为UTF8,编码GB2312是为了防止中文乱码

            //向日志文件写入内容
            string write_content = "[" + time + "] " + "[" + title + "]" + " : " + content + "\r\n";

            mySw.Write(write_content);

            //关闭日志文件
            mySw.Close();
        }
        catch (Exception ex) { }
    }



    public string verifySms_response(string type, string code, string userid, string phone = "")
    {
        string response_text = string.Empty;
        response_text = verifySms(type, code, userid, phone);
        if (response_text != "成功")
        {
            sendResponse(response_text);
        }
        return response_text;
    }

    public string verifySms(string type, string code, string userid, string phone = "")
    {
        string response_text = string.Empty;

        if (code.Length != 6)
        {
            return "验证码错误";
        }

        string sql = string.Empty;
        dbClass db = new dbClass();
        DataTable dt = new DataTable();
        List<SqlParameter> pams = new List<SqlParameter>();

        pams.Add(new SqlParameter("@type", type));
        pams.Add(new SqlParameter("@userid", userid));
        pams.Add(new SqlParameter("@phone", phone));
        pams.Add(new SqlParameter("@code", code));
        sql = @"

    if(@userid<>-1)
	begin
		set @phone=null
		select @phone=phone from accounts with(nolock) where id=@userid
	end
	if(LEN(ISNULL(@phone,''))<>11)
	begin
		select '手机号有误' as errmsg
		return
	end
	
	declare @current_code varchar(6)
	declare @send_time datetime	
    update sms_code_list set @send_time=create_time,@current_code=isnull(code,''),verify_number=verify_number+1,code=(case when code=@code or verify_number>5 then '' else code end) where phone=@phone and type=@type
    
    set @current_code=isnull(@current_code,'')
    if(@current_code='')
    begin
		select '请发送验证码' as errmsg
		return
    end

    if(DATEDIFF(SECOND,@send_time,GETDATE())>=5*60)
	begin
		select '验证码已失效'+CONVERT(VARCHAR,@send_time, 120) as errmsg
		return
	end

    if(@code<>@current_code)
    begin
		select '验证码错误' as errmsg
		return
    end


	select '成功' as errmsg,@phone as phone
";


        dt = db.getDataTable(sql, pams.ToArray());
        if (dt.Rows.Count == 0)
        {
            return "发送失败";
        }
        response_text = dt.Rows[0]["errmsg"] + "";
        return response_text;
    }


    public void paypwd_verify()
    {
        string pwd = req("paypwd");
        if (pwd.Length != 6)
        {
            sendResponse("支付密码错误");
        }

        string sql = string.Empty;
        dbClass db = new dbClass();
        DataTable dt = new DataTable();
        List<SqlParameter> pams = new List<SqlParameter>();

        pams.Add(new SqlParameter("@userid", uConfig.p_uid));
        pams.Add(new SqlParameter("@pwd", pwd));
        sql = @" select id from accounts with(nolock) where id=@userid and secure_password=@pwd ";
        dt = db.getDataTable(sql, pams.ToArray());
        if (dt.Rows.Count == 0)
        {
            sendResponse("支付密码错误");
        }
    }
    public bool user_auths(string path)
    {
        DataTable dt = new DataTable();

        if (uConfig.p_roleAD != "-1")
        {
            string quanxian = string.Empty;
            dt = selectDateTable(chelper.gdt("serv_admin_role"), " id=" + uConfig.p_roleAD + " ");
            if (dt.Rows.Count == 0)
            {
                return false;
            }
            quanxian = dt.Rows[0]["quanxian"] + "";
            if (quanxian.Replace(",", "") == "")
            {
                return false;
            }
            dt = selectDateTable(chelper.gdt("serv_role_auths"), " id in (" + quanxian + ") ");
            bool auth_request = false;
            for (int i = 0; i < dt.Rows.Count; i++)
            {
                if (dt.Rows[i]["path"] + "" == path)
                {
                    auth_request = true;
                    break;
                }
            }
            if (!auth_request)
            {
                return false;
            }
            return auth_request;
        }
        return true;
    }


    public void limit_check(int second = 1, string name = "")
    {
        if (name == "")
        {
            name = req("type");
        }
        string temp = "limit_" + name + "_" + uConfig.p_uid;
        if (cae.GetCache<string>(temp) == "1")
        {
            sendResponse("操作过于频繁");
        }
        cae.SetCache(temp, "1", DateTime.Now.AddSeconds(second));
    }

}
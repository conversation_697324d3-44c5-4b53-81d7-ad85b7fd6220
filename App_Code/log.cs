using System;
using System.Web;
using System.IO;
using System.Text.RegularExpressions;
using System.Collections.Generic;
using System.Text;
using System.Net.Cache;
using System.Xml;

/// <summary>
/// log 的摘要说明
/// </summary>
public class log : System.Web.UI.Page
{
    //在网站根目录下创建日志目录
    public static string path = System.Web.HttpContext.Current.Request.PhysicalApplicationPath + "logs";

    /// <summary>
    /// 写入日志
    /// </summary>
    /// <param name="type">日志记录类型</param>
    /// <param name="title">标题</param>
    /// <param name="content">内容</param>
    /// <returns></returns>
    public static void WriteLog(string type, string title, string content, bool slog = false)
    {
        try
        {
            if (slog == false && System.Web.HttpContext.Current.Request.Url.Host == "localhost")
            {
                return;
            }

            if (!Directory.Exists(path))//如果日志目录不存在就创建
            {
                Directory.CreateDirectory(path);
            }
            string time = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss.fff");//获取当前系统时间
            if (type == "") { type = "Log"; }//设置默认名字为Log
            string filename = path + "/" + DateTime.Now.ToString("yyyyMMdd") + "_" + type + ".log";//用日期对日志文件命名

            //创建或打开日志文件，向日志文件末尾追加记录
            StreamWriter mySw = new StreamWriter(filename, true, Encoding.GetEncoding("GB2312"));//默认为UTF8,编码GB2312是为了防止中文乱码

            //向日志文件写入内容
            string write_content = "【" + title + "】 " + time + " : " + content + "\r\n";

            mySw.Write(write_content);

            //关闭日志文件
            mySw.Close();
        }
        catch (Exception ex) { }
    }
}
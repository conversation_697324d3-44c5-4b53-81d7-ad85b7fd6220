using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Text.RegularExpressions;
using System.Web;
public class dbClass
{
    public dbClass() { }
    private SqlConnection Cn;
    private SqlCommand cmd;
    private DataSet ds;
    #region 取得链接
    private void Open()
    {
        string s = System.Web.Configuration.WebConfigurationManager.ConnectionStrings["ConnectionSql"].ConnectionString;
        Cn = new SqlConnection(s);
        Cn.Open();
    }
    #endregion

    #region 关闭连接
    private void Close()
    {
        if (Cn != null)
        {
            Cn.Close();
            Cn.Dispose();
        }
    }
    #endregion

    #region 执行简单的SQL语句
    public int ExecuteNonQuery(string Sql)
    {
        Open();
        int res = 0;
        try
        {
            cmd = new SqlCommand(Sql, Cn);
            res = cmd.ExecuteNonQuery();
        }
        catch
        {
            Close();
            throw;
        }
        Close();
        return res;
    }
    #endregion

    #region 执行简单的SQL语句（带参数）
    public int ExecuteNonQuery(string Sql, SqlParameter[] parames)
    {
        parames = tarns_parames(parames);

        Open();
        int res = 0;
        try
        {
            cmd = new SqlCommand(Sql, Cn);
            cmd.Parameters.AddRange(parames);
            res = cmd.ExecuteNonQuery();
        }
        catch
        {
            Close();
            throw;
        }
        Close();
        return res;
    }
    #endregion

    #region 返回首列单行的SQL语句
    public string ExecuteScalar(string Sql)
    {
        string Returnstring = "";
        try
        {
            Open();
            cmd = new SqlCommand(Sql, Cn);
            Returnstring = cmd.ExecuteScalar().ToString();
        }
        catch
        {
        }
        Close();
        return Returnstring;
    }
    #endregion

    #region 返回SQL语句的DateSet数据集
    public DataSet getDataSet(string SQL)
    {
        Open();
        try
        {
            SqlDataAdapter myAdapter = new SqlDataAdapter(SQL, Cn);
            ds = new DataSet();
            myAdapter.Fill(ds);
        }
        catch
        {
            Close();
            throw;
        }
        Close();
        return ds;
    }
    #endregion

    #region 返回SQL语句的DateSet数据集（带参数）
    public DataSet getDataSet(string SQL, SqlParameter[] parames)
    {
        parames = tarns_parames(parames);

        Open();
        try
        {
            SqlDataAdapter myAdapter = new SqlDataAdapter(SQL, Cn);
            foreach (SqlParameter ps in parames)
            {
                myAdapter.SelectCommand.Parameters.Add(ps);
            }
            ds = new DataSet();
            myAdapter.Fill(ds);
        }
        catch
        {
            Close();
            throw;
        }
        Close();
        return ds;
    }
    #endregion

    #region 返回一个DataTable对象
    public DataTable getDataTable(string SQL)
    {
        DataSet ds = getDataSet(SQL);
        DataTable dt = new DataTable();
        if (ds.Tables.Count > 0)
        {
            dt = ds.Tables[0];
        }
        return dt;
    }
    #endregion

    #region 返回一个DataTable对象(带参数)
    public DataTable getDataTable(string SQL, SqlParameter[] parames)
    {
        parames = tarns_parames(parames);

        DataSet ds = getDataSet(SQL, parames);
        DataTable dt = new DataTable();
        if (ds.Tables.Count > 0)
        {
            dt = ds.Tables[0];
        }
        return dt;
    }
    #endregion


    //有缺点，自己定义的参数也会被带进去
    public static SqlParameter[] sqlParamesCheck(string sql, SqlParameter[] parames)
    {
        List<string> _sqlparams = ParseParameters(sql);
        List<string> _existsKeys = new List<string>();
        List<SqlParameter> pams = new List<SqlParameter>();
        foreach (SqlParameter _p in parames)
        {
            _existsKeys.Add(_p.ParameterName);
            pams.Add(new SqlParameter(_p.ParameterName, _p.Value));
        }
        for (int i = 0; i < _sqlparams.Count; i++)
        {
            if (!_existsKeys.Exists(q => q == _sqlparams[i]))
            {
                _existsKeys.Add(_sqlparams[i]);
                pams.Add(new SqlParameter(_sqlparams[i], DBNull.Value));
            }
        }
        parames = pams.ToArray();
        return parames;
    }

    //提取sql语句中的@参数
    public static List<string> ParseParameters(string sql)
    {
        List<string> result = new List<string>();
        //Regex paramReg = new Regex(@"@\w*");
        //2009-2-29 修正正则表达式匹配参数时，Sql中包括@@rowcount之类的变量的情况，不应该算作参数
        Regex paramReg = new Regex(@"[^@@](?<p>@\w+)");
        MatchCollection matches = paramReg.Matches(String.Concat(sql, " "));
        foreach (Match m in matches)
            result.Add(m.Groups["p"].Value);
        return result;
    }

    public SqlParameter[] tarns_parames(SqlParameter[] parameters)
    {
        return parameters;
        // 创建一个新的参数列表，用于存储更改后的参数
        List<SqlParameter> newParameters = new List<SqlParameter>();

        // 遍历原始参数列表，将数字参数转换为 decimal 类型，并创建新的参数
        foreach (SqlParameter parameter in parameters)
        {
            if (paramIsNumber(parameter.Value) || parameter.ParameterName == "@redbag_number")
            {
                SqlParameter newParameter = new SqlParameter(parameter.ParameterName, SqlDbType.Decimal)
                {
                    Value = Convert.ToDouble(parameter.Value)
                };
                newParameters.Add(newParameter);
            }
            else
            {
                newParameters.Add(parameter); // 如果不是数字参数，则保持原始参数不变
            }
        }


        return newParameters.ToArray();
    }

    // 检查是否为数字
    public bool paramIsNumber(object value)
    {
        return value is int || value is decimal || value is double || value is float || value is long || value is short;
    }

}
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Web;

/// <summary>
/// cache_data 的摘要说明
/// </summary>
public class cache_data
{
	public cache_data()
	{
		//
		// TODO: 在此处添加构造函数逻辑
		//
	}

    public static DataTable role
    {
        get
        {
            string cache_name = "admin_role";
            DataTable dt;
            dbClass db = new dbClass();
            string sql = string.Empty;
            dt = cae.GetCache<DataTable>(cache_name);
            if (dt == null)
            {
                sql = " select * from serv_admin_role with(nolock) ";
                dt = db.getDataTable(sql);
                cae.SetCache(cache_name, dt);
            }
            return dt;
        }
    }
}
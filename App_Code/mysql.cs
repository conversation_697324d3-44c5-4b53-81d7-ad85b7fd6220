using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using MySql.Data.MySqlClient;
using System.Data;
using System.Data.SqlClient;

/// <summary>
/// mysql 的摘要说明
/// </summary>
public class mysql
{
    public mysql() { }
    private MySqlConnection Cn;
    private MySqlCommand cmd;
    private DataSet ds;
    #region 取得链接
    private void Open()
    {
        string s = System.Web.Configuration.WebConfigurationManager.ConnectionStrings["ConnectionSql"].ConnectionString;
        Cn = new MySqlConnection(s);
        Cn.Open();
    }
    #endregion

    #region 关闭连接
    private void Close()
    {
        if (Cn != null)
        {
            Cn.Close();
            Cn.Dispose();
        }
    }
    #endregion

    #region 执行简单的SQL语句
    public int ExecuteNonQuery(string Sql)
    {
        Open();
        int res = 0;
        try
        {
            cmd = new MySqlCommand(Sql, Cn);
            //cmd.Parameters.Clear();
            //cmd.CommandText = Sql;
            res = cmd.ExecuteNonQuery();
        }
        catch
        {
            Close();
            throw;
        }
        Close();
        return res;
    }
    #endregion

    #region 执行简单的SQL语句（带参数）
    public int ExecuteNonQuery(string Sql, SqlParameter[] parames)
    {

        Open();
        int res = 0;
        try
        {
            cmd = new MySqlCommand(Sql, Cn);
            cmd.Parameters.AddRange(toParam(parames));
            res = cmd.ExecuteNonQuery();
        }
        catch
        {
            Close();
            throw;
        }
        Close();
        return res;
    }
    #endregion

    #region 返回SQL语句的DateSet数据集
    public DataSet getDataSet(string SQL)
    {
        Open();
        try
        {
            MySqlDataAdapter myAdapter = new MySqlDataAdapter(SQL, Cn);
            ds = new DataSet();
            myAdapter.Fill(ds);
        }
        catch
        {
            Close();
            throw;
        }
        Close();
        return ds;
    }
    #endregion

    #region 返回SQL语句的DateSet数据集（带参数）
    public DataSet getDataSet(string SQL, SqlParameter[] parames)
    {
        Open();
        try
        {
            MySqlDataAdapter myAdapter = new MySqlDataAdapter(SQL, Cn);
            foreach (MySqlParameter ps in toParam(parames))
            {
                myAdapter.SelectCommand.Parameters.Add(ps);
            }
            ds = new DataSet();
            myAdapter.Fill(ds);
        }
        catch
        {
            Close();
            throw;
        }
        Close();
        return ds;
    }
    #endregion

    #region 返回一个DataTable对象
    public DataTable getDataTable(string SQL)
    {
        return getDataSet(SQL).Tables[0];
    }
    #endregion

    #region 返回一个DataTable对象(带参数)
    public DataTable getDataTable(string SQL, SqlParameter[] parames)
    {
        DataSet ds = getDataSet(SQL, parames);
        DataTable dt = new DataTable();
        if (ds.Tables.Count > 0)
        {
            dt = ds.Tables[0];
        }
        return dt;
    }
    #endregion

    public MySqlParameter[] toParam(SqlParameter[] parames)
    {
        List<MySqlParameter> pams = new List<MySqlParameter>();

        foreach (SqlParameter _p in parames)
        {
            pams.Add(new MySqlParameter(_p.ParameterName, _p.Value));
        }

        return pams.ToArray();
    }
}
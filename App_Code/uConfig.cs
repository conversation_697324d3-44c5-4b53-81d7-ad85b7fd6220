using LitJson;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Web;

/// <summary>
/// uConfig 的摘要说明
/// </summary>
public class uConfig
{

    public static string loginParames_prefix = "apptao_";
    public static string loginParames_token = loginParames_prefix + "token";
    public static string loginParames_usernick = loginParames_prefix + "phone";
    public static string loginParames_pwd = loginParames_prefix + "password";
    public static string loginParames_uid = loginParames_prefix + "userid";
    public static string loginParames_tid = loginParames_prefix + "tid";
    public static string loginParames_roomNumber = loginParames_prefix + "roomNumber";
    public static string loginParames_loginTime = loginParames_prefix + "login_time";
    public static string loginParames_qx_admin = loginParames_prefix + "ad_limit";
    public static string loginParames_id_admin = loginParames_prefix + "ad_userid";
    public static string loginParames_role_admin = loginParames_prefix + "ad_roleid";
    public static string loginParames_nick_admin = loginParames_prefix + "ad_username";
    public static string loginParames_pwd_admin = loginParames_prefix + "ad_password";
    public static string loginParames_token_admin = loginParames_prefix + "ad_token";

    public static string p_token { get { return globalClass.getCookie(loginParames_token); } }
    public static string p_userNick { get { return globalClass.getCookie(loginParames_usernick); } }
    public static string p_passWord { get { return globalClass.getCookie(loginParames_pwd); } }
    public static string p_uid { get { return globalClass.getCookie(loginParames_uid); } }
    public static string p_tid { get { return globalClass.getCookie(loginParames_tid); } }
    public static string p_roomNumber { get { return globalClass.getCookie(loginParames_roomNumber); } }
    public static string p_loginTime { get { return globalClass.getCookie(loginParames_loginTime); } }
    public static string p_idAD { get { return globalClass.getCookie(loginParames_id_admin); } }
    public static string p_roleAD { get { return globalClass.getCookie(loginParames_role_admin); } }
    public static string p_userNickAD { get { return globalClass.getCookie(loginParames_nick_admin); } }
    public static string p_passWordAD { get { return globalClass.getCookie(loginParames_pwd_admin); } }
    public static string p_tokenAD { get { return globalClass.getCookie(loginParames_token_admin); } }
    public static string p_qxAD { get { return globalClass.getCookie(loginParames_qx_admin); } }


    public uConfig()
    { }

    public static bool isBaseLogin
    {
        get
        {
            if (string.IsNullOrEmpty(uConfig.p_uid) || string.IsNullOrEmpty(uConfig.p_token))
            {
                return false;
            }

            string check_pams = uConfig.p_uid + "_" + uConfig.p_token + "_" + uConfig.p_userNick + "_" + uConfig.p_loginTime;
            string check_pams_md5 = new globalClass().getMD5(check_pams);

            string login_cache = cae.GetCache<string>("login_cache_" + check_pams_md5);

            if (!string.IsNullOrEmpty(login_cache))
            {
                return login_cache == "login";
            }
            dbClass db = new dbClass();
            DataTable dt = new DataTable();
            SqlParameter[] parames = new SqlParameter[]{
                    new SqlParameter("@userid",uConfig.p_uid),
                    new SqlParameter("@token",uConfig.p_token),
                    new SqlParameter("@phone",uConfig.p_userNick),
                    new SqlParameter("@password",uConfig.p_passWord)
                };
            string sql = string.Empty;
            sql = " select top 1 * from accounts with(nolock) where id=@userid and token=@token and phone=@phone and password=@password and state=1 ";
            dt = db.getDataTable(sql, parames);

            cae.SetCache("login_cache_" + check_pams_md5, (dt.Rows.Count > 0 ? "login" : "unlogin"), DateTime.Now.AddSeconds(60));
            if (dt.Rows.Count == 0)
            {
                return false;
            }
            return true;
        }
    }

    //已登录未设置房间状态
    public static bool isLogin
    {
        get
        {
            if (string.IsNullOrEmpty(uConfig.p_uid) || string.IsNullOrEmpty(uConfig.p_token))
            {
                return false;
            }

            string check_pams = uConfig.p_uid + "_" + uConfig.p_token + "_" + uConfig.p_userNick + "_" + uConfig.p_roomNumber + "_" + uConfig.p_tid + "_" + uConfig.p_loginTime;
            string check_pams_md5 = new globalClass().getMD5(check_pams);

            string login_cache = cae.GetCache<string>("login_cache_2roomid_" + check_pams_md5);

            if (!string.IsNullOrEmpty(login_cache))
            {
                return login_cache == "login";
            }
            dbClass db = new dbClass();
            DataTable dt = new DataTable();
            SqlParameter[] parames = new SqlParameter[]{
                    new SqlParameter("@userid",uConfig.p_uid),
                    new SqlParameter("@token",uConfig.p_token),
                    new SqlParameter("@phone",uConfig.p_userNick),
                    new SqlParameter("@password",uConfig.p_passWord),
                    new SqlParameter("@roomNumber",uConfig.p_roomNumber),
                    new SqlParameter("@tid",uConfig.p_tid)
                };
            string sql = string.Empty;
            sql = " select top 1 * from accounts with(nolock) where id=@userid and token=@token and phone=@phone and password=@password and roomNumber is not null  and (case when isnull(roomNumber,'')='' then -99999 else roomNumber end)=@roomNumber and state=1 and topid=@tid ";
            dt = db.getDataTable(sql, parames);

            cae.SetCache("login_cache_2roomid_" + check_pams_md5, (dt.Rows.Count > 0 ? "login" : "unlogin"), DateTime.Now.AddSeconds(60));
            if (dt.Rows.Count == 0)
            {
                return false;
            }
            return true;
        }
    }

    public static bool isAdmin
    {
        get
        {
            dbClass db = new dbClass();
            DataTable dt = new DataTable();
            SqlParameter[] parames = new SqlParameter[]{
                new SqlParameter("@id",uConfig.p_idAD),
                new SqlParameter("@roleid",uConfig.p_roleAD),
                new SqlParameter("@u",uConfig.p_userNickAD),
                new SqlParameter("@p",uConfig.p_passWordAD)
            };
            string sql = " select top 1 * from serv_admin with(nolock) where id=@id and nick=@u and pwd=@p and roleid=(case when @roleid='' then null else @roleid end) and state=1  ";

            dt = db.getDataTable(sql, parames);
            if (dt.Rows.Count == 0)
            {
                return false;
            }
            return true;
        }
    }


    public static bool super
    {
        get
        {
            return p_roleAD == "-1";
        }
    }

    public static DataTable get_sitemap()
    {
        DataTable dt;
        dt = cae.GetCache<DataTable>("stc");
        if (dt == null)
        {
            dbClass db = new dbClass();
            string sql = " select * from [sitemaps] with(nolock) ";
            dt = db.getDataTable(sql);
            cae.SetCache("stc", dt);
        }
        return dt;

    }
    public static string stcdata(string name, string site_id = "", string def = "")
    {
        string res = "";
        try
        {
            DataTable dt = get_sitemap();
            site_id = string.IsNullOrEmpty(site_id) ? uConfig.p_idAD : site_id;
            dt = globalClass.selectDateTable(dt, "servid=1");
            for (int i = 0; i < dt.Rows.Count; i++)
            {
                if ((dt.Rows[i]["name"] + "") == name)
                {
                    return dt.Rows[i]["data"] + "";
                }
            }
        }
        catch (Exception)
        {
            res = "";
        }
        if (string.IsNullOrEmpty(res))
        {
            res = def;
        }
        return res;
    }

    public static string gd(DataTable dt, string name, string defcontent = "")
    {
        string res = "";
        try
        {
            res = dt.Rows[0][name] + "";
        }
        catch (Exception)
        {
            res = "";

            //extra获取
            try
            {
                string extra = dt.Rows[0]["extra"] + "";
                JsonData jd = JsonMapper.ToObject(extra);
                res = jd[name] + "";
            }
            catch (Exception ex)
            {
                res = "";
            }
        }
        if (res == "")
        {
            res = defcontent;
        }
        return res;
    }

    public static string gnumber(DataTable dt, string name, int numb = 2)
    {
        string result = string.Empty;
        try
        {
            double a = Convert.ToDouble(gd(dt, name));
            result = a.ToString("0.00");
        }
        catch (Exception)
        {
            result = gd(dt, name);
        }
        if (result == "")
        {
            result = "0.00";
        }
        return result;
    }
}
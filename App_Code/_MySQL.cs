*sing System;
*sing System.Collections.Generic;
*sing System.Linq;
*sing System.Web;
*sing MySql.Data.MySqlClient;
*sing System.Data;
*sing System.Text;

namespace TCalendar.*tils
{
    p*blic class WebDBServiceMySQL
    {
        p*blic static string ConnStr = "server=127.0.0.1;port=3306;*ser id=*;password=*;database=qq;charset=*tf8";
        p*blic static DataTable GetDataTable(string SQL)
        {
            DataTable ADt = new DataTable();
            MySqlConnection AConn = new MySqlConnection(ConnStr);
            try
            {
                MySqlDataAdapter ADp = new MySqlDataAdapter(SQL, AConn);
                ADp.Fill(ADt);
            }
            catch
            {
                ;
            }
            finally
            {
                AConn.Close();
                AConn.Dispose();
                AConn = n*ll;
            }

            ret*rn ADt;
        }
        p*blic static DataRow GetDataRow(string SQL)
        {
            DataRow ARow = n*ll;
            DataTable ADt = GetDataTable(SQL);
            try
            {
                if (ADt.Rows.Co*nt > 0)
                    ARow = ADt.Rows[0];
            }
            finally
            {
                ADt.Dispose(); ADt = n*ll;
            }
            ret*rn ARow;
        }
        /// <s*mmary>
        /// 执行 Insert Delete Update
        /// </s*mmary>
        /// <param name="SQL">SQL语句</param>
        /// <param name="AParaNames">参数名数组</param>
        /// <param name="AParaVal*es">参数值数组</param>
        /// <ret*rns>成功-1;失败-0</ret*rns>
        p*blic static int DoExec*te(string SQL, string[] AParaNames, object[] AParaVal*es)
        {
            if (AParaNames != n*ll)
            {
                if (AParaNames.Length != AParaVal*es.Length)
                {
                    ret*rn 0;
                }
            }
            int Ares = 0;
            MySqlConnection AConn = new MySqlConnection(ConnStr);
            MySqlCommand Acmd = new MySqlCommand(SQL, AConn);
            Acmd.Parameters.Clear();
            Acmd.CommandText = SQL;
            try
            {
                if (AParaNames != n*ll)
                {
                    for (int i = 0; i < AParaNames.Length; i++)
                    {
                        MySqlParameter Ap = n*ll;
                        if (AParaVal*es[i] != n*ll)
                        {
                            Ap = new MySqlParameter(AParaNames[i], AParaVal*es[i]);
                        }
                        else
                        {
                            if (AParaNames[i].ToUpper().Contains("F_SYMBOL"))
                            {
                                Ap = new MySqlParameter(AParaNames[i], SqlDbType.Image);
                                Ap.Val*e = DBN*ll.Val*e;
                            }
                            else if (AParaNames[i].ToUpper().Contains("F_PHOTO"))
                            {
                                Ap = new MySqlParameter(AParaNames[i], SqlDbType.Image);
                                Ap.Val*e = DBN*ll.Val*e;
                            }
                            else
                            {
                                Ap = new MySqlParameter(AParaNames[i], DBN*ll.Val*e);
                            }
                        }
                        Acmd.Parameters.Add(Ap);
                    }
                }
                AConn.Open();
                Acmd.Exec*teNonQ*ery();
                Ares = 1;
            }
            catch (Exception err) { ;}
            finally
            {
                AConn.Close();
                Acmd.Dispose();
            }
            ret*rn Ares;
        }
        /// <s*mmary>
        /// 执行Insert
        /// </s*mmary>
        /// <param name="ATable"></param>
        /// <param name="AFields"></param>
        /// <param name="AVal*es"></param>
        /// <ret*rns></ret*rns>
        p*blic static int DoInsert(string ATable, string[] AFields, object[] AVal*es)
        {
            string SQL = "Insert into " + ATable + "(";
            for (int i = 0; i < AFields.Length; i++)
            {
                SQL += AFields[i] + " ,";
            }
            SQL = SQL.S*bstring(0, SQL.Length - 1) + ") val*es (";
            string[] APs = new string[AFields.Length];

            for (int i = 0; i < AFields.Length; i++)
            {
                APs[i] = "@AP_" + AFields[i];

                SQL += APs[i] + " ,";

            }
            SQL = SQL.S*bstring(0, SQL.Length - 1) + ") ";
            ret*rn DoExec*te(SQL, APs, AVal*es);
        }
        /// <s*mmary>
        /// 更新数据表
        /// </s*mmary>
        /// <param name="ATable"></param>
        /// <param name="AFields"></param>
        /// <param name="AVal*es"></param>
        /// <param name="ACondFields"></param>
        /// <param name="ACondVal*es"></param>
        /// <ret*rns></ret*rns>
        p*blic static int DoUpdate(string ATable, string[] AFields, object[] AVal*es,
                string[] ACondFields, object[] ACondVal*es
            )
        {
            string[] APs = new string[AFields.Length + ACondFields.Length];
            object[] AVs = new object[AVal*es.Length + ACondVal*es.Length];
            string SQL = "Update " + ATable + " Set ";
            for (int i = 0; i < AFields.Length; i++)
            {
                APs[i] = "@AF_" + AFields[i];
                AVs[i] = AVal*es[i];
                SQL += AFields[i] + " =" + APs[i] + " ,";
            }
            SQL = SQL.S*bstring(0, SQL.Length - 1);
            if (ACondVal*es != n*ll)
            {
                SQL += " where (1>0) ";
                for (int i = 0; i < ACondFields.Length; i++)
                {
                    APs[i + AFields.Length] = "@AP_" + ACondFields[i];
                    AVs[i + AFields.Length] = ACondVal*es[i];
                    SQL += " and " + ACondFields[i] + " =" + APs[i + AFields.Length];
                }
            }
            ret*rn DoExec*te(SQL, APs, AVs);
        }
        p*blic static int DoDelete(string ATable, string[] ACondFields, object[] ACondVal*es)
        {
            string[] APs = new string[ACondFields.Length];
            object[] AVs = new object[ACondVal*es.Length];
            string SQL = "Delete From  " + ATable + "  ";

            if (ACondVal*es != n*ll)
            {
                SQL += " where (1>0) ";
                for (int i = 0; i < ACondFields.Length; i++)
                {
                    APs[i] = "@AP_" + ACondFields[i];
                    AVs[i] = ACondVal*es[i];
                    SQL += " and " + ACondFields[i] + " =" + APs[i];
                }
            }
            ret*rn DoExec*te(SQL, APs, AVs);
        }
    }
}
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Data;
using System.Data.SqlClient;
using LitJson;

public partial class games_authorize : baseClass
{
    public string authUrlPath = "Public.gamestart.do";
    public Dictionary<string, object> pmlist = new Dictionary<string, object>();


    protected void Page_Load(object sender, EventArgs e)
    {
        string sql = string.Empty;
        dbClass db = new dbClass();
        DataTable dt = new DataTable();
        List<SqlParameter> pams = new List<SqlParameter>();

        //string[] games = uConfig.stcdata("game_list").Split('\n');
        //string game_name = Request.QueryString["gameName"] + "";
        string gameId = Request.QueryString["gameId"] + "";
        string temp = string.Empty;



        if (Request.Url.Host == "localhost")
        {
            authUrlPath = "games/gamestart.aspx";
        }

        //string[] g = (pmlist["authData"] + "").Split(new string[] { "----" }, StringSplitOptions.None);

        //dt = selectDateTable(chelper.gdt("auth_list"), " state=1 and code='" + game_name + "' ");
        dt = selectDateTable(chelper.gdt("auth_list"), " state=1 ");

        List<string> def_dic = new List<string>();
        List<string> list2 = new List<string>();

        for (int i = 0; i < dt.Rows.Count; i++)
        {
            temp = dt.Rows[i]["view_users"] + "";

            if (temp == "")
            {
                def_dic.Add(dt.Rows[i]["id"] + "");
            }
            else if (("\n" + temp.Replace("\r", "") + "\n").IndexOf("\n" + uConfig.p_userNick + "\n") != -1)
            {
                list2.Add(dt.Rows[i]["id"] + "");
            }
        }

        if (list2.Count == 0)
        {
            list2.AddRange(def_dic);
        }


        dt = selectDateTable(dt, " id='" + gameId + "' ");

        if (dt.Rows.Count == 0)
        {
            Response.Write("授权不存在::" + gameId + "");
            Response.End();
        }

        temp = dt.Rows[0]["view_users"] + "";


        if (!list2.Contains(gameId))
        {
            Response.Write("授权未开放");
            Response.End();

        }



        //List<string> slist = new List<string>();
        //string[] g;

        //g = uConfig.stcdata("g_api_domains").Split('\n');
        //slist = new List<string>();
        //for (int i = 0; i < g.Length; i++)
        //{
        //    slist.Add(g[i]);
        //}
        //apiDomains = JsonMapper.ToJson(slist);

        //g = uConfig.stcdata("g_api_domains_nssl").Split('\n');
        //slist = new List<string>();
        //for (int i = 0; i < g.Length; i++)
        //{
        //    slist.Add(g[i]);
        //}
        //apiDomains_nssl = JsonMapper.ToJson(slist);


        //-----------------------------------------------------------------


        pmlist["weburl"] = dt.Rows[0]["weburl_start"] + "";
        pmlist["game_appid"] = dt.Rows[0]["appid"] + "";
        pmlist["game_appkey"] = dt.Rows[0]["appkey"] + "";
        pmlist["game_code"] = dt.Rows[0]["authcode"] + "";
        pmlist["gameName"] = dt.Rows[0]["code"] + "";

        pmlist["from_type"] = Request.QueryString["from_type"] + "";
        if (!string.IsNullOrEmpty(pmlist["from_type"] + ""))
        {
            if (!"onetouch".Contains(pmlist["from_type"] + ""))
            {
                Response.Write("授权类型错误");
                Response.End();
            }
        }


        //Response.Write("tp = " + pmlist["from_type"]);
        //Response.End();


        pams.Add(new SqlParameter("@userid", uConfig.p_uid));
        pams.Add(new SqlParameter("@gameName", pmlist["gameName"] + ""));
        pams.Add(new SqlParameter("@expire_second", uConfig.stcdata("expire_second")));
        pams.Add(new SqlParameter("@from_type", pmlist["from_type"] + ""));




        sql = @"  
declare @second int
set @second=cast(@expire_second as int)

declare @code varchar(32)
set @code=LOWER(replace(newid(),'-',''))   

declare @openid varchar(32)
set @openid=LOWER(replace(newid(),'-',''))   

declare @expire_time datetime
set @expire_time=dateadd(second,@second,getdate())
        
update authorize_list set refresh_time=getdate(),code=@code,expire_time=@expire_time where userid=@userid and gameName=@gameName and isnull(from_type,'')=@from_type
if(@@rowcount=0)
begin
    insert into authorize_list(gameName,userid,openid,code,create_time,expire_time,refresh_time,from_type) select top 1 @gameName,@userid,@openid,@code,getdate(),@expire_time,getdate(),@from_type from authorize_list where not exists(select id from authorize_list where userid=@userid and gameName=@gameName and isnull(from_type,'')=@from_type) 
end

select * from authorize_list with(nolock) where userid=@userid and gameName=@gameName and isnull(from_type,'')=@from_type

";

        dt = db.getDataTable(sql, pams.ToArray());


        //Response.ContentType = "text/plain";
        //Response.Write(getPamsSql(pams));
        //Response.Write(sql);
        //Response.End();

        //签名使用appid+appkey $encodeFrom = "appid=".$appid."&code=".$code."&openid=".$openid."&token=".$token."&key=".$key;  $csign = md5($encodeFrom);

        if (dt.Rows.Count > 0)
        {
            string sign = md5UTF8("appid=" + uConfig.stcdata("g_api_appid") + "&code=" + dt.Rows[0]["code"] + "&openid=" + dt.Rows[0]["openid"] + "&key=" + uConfig.stcdata("g_api_appkey"));

            pmlist["redirect_parames"] = "?siteid=" + uConfig.stcdata("siteid") + "&sitecode=" + uConfig.stcdata("sitecode") + "&code=" + dt.Rows[0]["code"] + "&gamecode=" + pmlist["game_code"] + "&openid=" + dt.Rows[0]["openid"] + "&return_url=" + Uri.EscapeDataString(getHost(Request).Replace("http://", "https://") + "/chat.aspx") + "&sign=" + sign;

            pmlist["redirect_url"] = pmlist["weburl"] + "" + pmlist["redirect_parames"] + "";


            //Response.Redirect(pmlist["redirect_url"] + "");
            //Response.Write(pmlist["redirect_url"] + "");
        }
        else
        {
            Response.Write("授权失败，请返回重试");
            Response.End();
        }

        dt = chelper.gdt("serverUrls");
        int current_server = 0;
        Random rda = new Random();
        //current_server = get_random("1", (dt.Rows.Count).ToString()) - 1;

        current_server = cae.GetCache<int>("csindex");
        if (current_server == null)
        {
            current_server = 0;
        }
        current_server += 1;
        if (current_server >= dt.Rows.Count)
        {
            current_server = 0;
        }
        cae.SetCache("csindex", current_server);

        //Response.Write("aa");
        //Response.Write(current_server.ToString() + '-' + (dt.Rows.Count ).ToString());
        //Response.End();

        pmlist["cdnurls"] = dt.Rows[current_server]["cdnurls"] + "";
        pmlist["directurls"] = dt.Rows[current_server]["directurls"] + "";
        pmlist["floaturls"] = dt.Rows[current_server]["floaturls"] + "";


        List<string> slist = new List<string>();
        string[] g;

        g = (pmlist["cdnurls"] + "").Split('\n');
        slist = new List<string>();
        for (int i = 0; i < g.Length; i++)
        {
            slist.Add(g[i].Replace("\r", ""));
        }
        pmlist["apiDomains_cdn"] = JsonMapper.ToJson(slist);

        g = (pmlist["directurls"] + "").Split('\n');
        slist = new List<string>();
        for (int i = 0; i < g.Length; i++)
        {
            slist.Add(g[i].Replace("\r", ""));
        }
        pmlist["apiDomains_direct"] = JsonMapper.ToJson(slist);

        g = (pmlist["floaturls"] + "").Split('\n');
        slist = new List<string>();
        for (int i = 0; i < g.Length; i++)
        {
            slist.Add(g[i].Replace("\r", ""));
        }
        pmlist["apiDomains_float"] = JsonMapper.ToJson(slist);

    }


    public string getPamsSql(List<SqlParameter> parameters)
    {
        string sql = string.Empty;
        // 遍历参数列表，构建 SQL 语句
        foreach (var parameter in parameters)
        {
            string sqlParameterDeclaration = "DECLARE " + parameter.ParameterName + " " + GetSqlType(parameter.SqlDbType);
            string sqlParameterValueAssignment = "SET " + parameter.ParameterName + " = '" + parameter.Value + "'";

            // 输出声明参数和赋值语句
            sql += sqlParameterDeclaration + @"
";
            sql += sqlParameterValueAssignment + @"
";
        }
        return sql;
    }


    // 获取 SqlParameter 的 SqlDbType 对应的 SQL 数据类型
    static string GetSqlType(SqlDbType sqlDbType)
    {
        switch (sqlDbType)
        {
            case SqlDbType.VarChar:
                return "VARCHAR(100)"; // 假设长度为 100
            case SqlDbType.Int:
                return "INT";
            case SqlDbType.Bit:
                return "BIT";
            case SqlDbType.NVarChar:
                return "VARCHAR(100)";
            // 其他数据类型自行添加
            default:
                throw new ArgumentException("Unsupported SqlDbType: " + sqlDbType);
        }
    }


}
<%@ Page Language="C#" AutoEventWireup="true" CodeFile="test_authorize.aspx.cs" Inherits="games_authorize" %>

<!DOCTYPE html>

<html xmlns="http://www.w3.org/1999/xhtml">
<head runat="server">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title>登录中</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <script src="../js/jquery_1.9.1.min.js"></script>
    <script type="text/javascript">
        window.history.forward();
    </script>

    <style>
        /*body {
            background:linear-gradient(178deg, #E8EBF4, #FAF9FE);
        }*/
        .network_sel {
            background: #fff !important;
            color: #2a2b2c;
            text-align: center;
        }

            .network_sel #title1 {
                font-size: 3em;
            }

        .list {
            width: 100%;
            min-height: 45%;
            display: flex;
            align-items: center;
            flex-direction: column;
            padding-top: 30px;
            display: flex;
            max-width: 200px;
            margin: 0 auto;
            padding-top: 30px;
        }

            .list a {
                display: block;
                width: 72%;
                position: relative;
            }

            .list .network_item {
                background: #64e964;
                color: #127512;
                border-radius: 60px;
                padding: 16px 50px;
                margin-bottom: 22px;
                cursor: pointer;
                width: 100%;
                text-decoration: none;
            }
    </style>

</head>
<body>
    <form id="form1" runat="server">

        <h2 style="margin-bottom: 0; text-align: center;" id="title1">正在进入，请不要刷新页面</h2>
        <div id="select_tip" style="font-size: 18px; margin-top: 8px; margin-bottom: 18px; display: none; font-size: 14px;">
            <div style="color: rgb(217, 77, 77); margin-bottom: 3px;" id="check_text">
                正在检测线路中
            </div>
            <div style="">
                可尝试选择以下线路
            </div>
        </div>

        <div style="display: none;" id="nslist">
            <div class="list" id="list-wrap" style="display: flex;">
            </div>
        </div>



        <div id="jumpErrorMsg" style="display: none;">
            <div id="jump_url" style="display: none;">
                <div style="display: flex; margin-top: 9px; font-size: 13px; align-items: center; background: #f1f1f1; padding: 5px 8px; border-radius: 6px;">
                    <svg t="" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="31574" width="13" height="13">
                        <path d="M855.458909 138.379636a622.312727 622.312727 0 0 1-197.818182-48.546909c-37.608727-16.989091-95.883636-55.854545-129.861818-80.058182a51.153455 51.153455 0 0 0-58.274909 0c-33.978182 24.203636-92.206545 63.069091-131.072 80.058182a622.312727 622.312727 0 0 1-197.818182 48.546909C113.943273 139.589818 93.277091 162.629818 93.277091 189.346909v305.803636a456.657455 456.657455 0 0 0 214.807273 387.165091l94.673454 59.485091 67.956364 42.496a52.596364 52.596364 0 0 0 53.434182 0l67.956363-42.496 94.673455-59.485091a456.657455 456.657455 0 0 0 214.807273-387.118545V188.136727a47.662545 47.662545 0 0 0-46.126546-49.757091z" fill="#45BE89" p-id="31575"></path><path d="M498.641455 638.370909a34.909091 34.909091 0 0 1-25.460364 10.938182h-3.677091a38.306909 38.306909 0 0 1-26.670545-15.778909l-134.749091-133.492364c-10.891636-12.101818-15.732364-36.398545 1.256727-53.387636 15.778909-14.568727 40.029091-12.148364 58.228364 6.050909l111.662545 103.144727 200.238545-200.238545a37.794909 37.794909 0 0 1 52.177455 1.210182c10.938182 14.568727 10.938182 37.655273-2.420364 50.967272l-230.586181 230.586182z" fill="#FFFFFF" p-id="31576"></path></svg><span style="color: #45be89; font-weight: bold; margin-left: 5px;">-</span>
                </div>
            </div>
            <div style="color: gray; margin-top: 8px;">如未正常跳转请截图联系客服</div>


            <div style="border: 1px solid #ddd; background: #fff; padding: 18px; margin-top: 18px; border-radius: 8px;">
                <p style="color: gray; font-size: 14px; margin-top: 0;">无效的网络（ip <%=System.Web.HttpContext.Current.Request.ServerVariables["HTTP_X_TENANT_FORWARD_FOR"] %>）</p>
                <div style="" id="errorLists">
                </div>
            </div>
        </div>

        <%--SSL域名--%>
        <script type="text/javascript" id="apiDomains_cdn">            
            <%=pmlist["apiDomains_cdn"] %>
        </script>
        <%--无SSL域名（资源加载较慢）--%>
        <script type="text/javascript" id="apiDomains_direct">            
            <%=pmlist["apiDomains_direct"] %>
        </script>
        <%--泛解析（多线路）--%>
        <script type="text/javascript" id="apiDomains_float">            
            <%=pmlist["apiDomains_float"] %>
        </script>

        <script>
            var routename = "优质线路";
            var ss = 0;
            var se = 1;
            var tt1 = function () {
                setTimeout(function () {
                    ss += 1;
                    $('#title1').html('正在进入<span style="color: #2a2b2c;" class="routename">' + routename + '</span>，请不要刷新页面(' + (se - ss) + ')');
                    if (ss >= se) {
                        $('#title1').html('正在进入<span style="color: #2a2b2c;" class="routename">' + routename + '</span>，如无法进入请<b style="color:red;">截图联系客服</b>！！！');
                        //$('#jumpErrorMsg').show();

                        $('#title1').html('请选择线路');
                        $('#select_tip').show();
                        $('body').addClass('network_sel');
                        $('#nslist').show();

                        return;
                    }
                    tt1();
                }, 1000);
            }
            tt1();
        </script>
        <script>
            var __point = '';
            setInterval(function () {
                __point += '.';
                if (__point == '....') {
                    __point = '.';
                }
                $('#check_text').html('正在检测' + routename + '中' + __point);
            }, 1000);

            function randomString(length) {
                var result = '';
                var characters = 'abcdefghijklmnopqrstuvwxyz0123456789';
                var charactersLength = characters.length;
                for (var i = 0; i < length; i++) {
                    result += characters.charAt(Math.floor(Math.random() * charactersLength));
                }
                return result;
            }

            function extractDomain(url) {
                // 移除http://或https://
                url = url.replace(/^https?:\/\//, '');

                // 移除所有路径
                url = url.split('/')[0];

                return url;
            }


            function get_param(paramName) {
                var urlParams = new URLSearchParams(window.location.search);
                return urlParams.get(paramName) || "";
            }
            //置顶数组指定元素
            function moveElementToFirst(arr, element) {
                var index = arr.indexOf(element);
                if (index !== -1) {
                    arr.splice(index, 1);
                    arr.unshift(element);
                }
                return arr;
            }

            var apiDomains_cdn = JSON.parse($('#apiDomains_cdn').html());
            var apiDomains_direct = JSON.parse($('#apiDomains_direct').html());
            var apiDomains_float = JSON.parse($('#apiDomains_float').html());

            var redirect_parames = '<%=pmlist["redirect_parames"] %>';
            var testurl = '<%=Request.QueryString["testurl"] %>';


            //打乱排序
            apiDomains_cdn = shuffleArray(apiDomains_cdn);
            apiDomains_direct = shuffleArray(apiDomains_direct);
            apiDomains_float = shuffleArray(apiDomains_float);

            //检测上次是否有可进入域名（有则靠前）
            var g_last_visit_url = localStorage.getItem("g_last_visit_url");
            if (g_last_visit_url != null && g_last_visit_url != "") {
                moveElementToFirst(apiDomains_direct, g_last_visit_url);//在非SSL域名中排第一位检测

                //没有在SSL域名中,但是在非SSL中（此检测是为了防止域名在列表中去掉后还被调用的情况）
                // 满足条件→（附加到SLL域名中，方便用户快速打开）
                if (apiDomains_cdn.indexOf(g_last_visit_url) == -1 && apiDomains_direct.indexOf(g_last_visit_url) !== -1) {
                    apiDomains_cdn.unshift(g_last_visit_url);
                }
            }



            if (get_param('test') == "1") {
                console.log('优质线路', apiDomains_cdn);
                console.log('快速线路', apiDomains_direct);
                console.log('多线路', apiDomains_float);
            }

            var __prefix = '<%=uConfig.p_uid %>';
            var s2 = ['一', '二', '三', '四', '五', '六', '七', '八', '九', "十", "十一", "十二", "十三", "十四", "十五", "十六", "十七", "十八", "十九"];
            $('#list-wrap').html('');

            var total_network = 0;
            if (apiDomains_float.length > 0) {

                while (total_network < 8) {
                    for (var i = 0; i < apiDomains_float.length; i++) {
                        total_network++;
                        if (total_network > 10) {
                            break;
                        }
                        var __domain = apiDomains_float[i];
                        var __target = __domain.replace(/\/\//g, "//" + randomString(5) + __prefix + ".");
                        var __rootDoamin = extractDomain(__target);

                        var temp_url = __target;
                        temp_url = (temp_url + '').replace(/\/test.html/g, "");
                        temp_url = temp_url + '/<%=authUrlPath %>' + redirect_parames;

                        $('#list-wrap').append('<a class="network_item" href="' + temp_url + '"><div>线路' + s2[total_network - 1] + '</div><div>' + __rootDoamin + '</div></a>');
                    }
                }

            }

            var residue_testNumber = 0;
            var startTime = new Date().getTime();
            var test_domains = function (_dm) {
                residue_testNumber = _dm.length;
                for (var i = 0; i < _dm.length; i++) {
                    var __domain = _dm[i];
                    this.index = i;
                    $.ajax({
                        url: __domain + '/test.html',
                        type: "GET",
                        data: {},
                        datatype: "json",
                        success: function (data) {
                            checkUrl();
                            openUrl(this.url);
                        },
                        error: function (xhr, status, errorThrown) {
                            var endTime = new Date().getTime(); // 记录请求结束时间
                            var elapsedTime = endTime - startTime; // 计算请求耗时
                            var formattedElapsedTime = formatElapsedTime(elapsedTime);
                            checkUrl();

                            var statusCode = xhr.status;
                            console.log("HTTP状态码：" + statusCode);
                            console.log("错误摘要：" + errorThrown);

                            console.log('异常', this.url, formattedElapsedTime, xhr, status);
                            pushError(this.url, formattedElapsedTime, statusCode + ' ' + errorThrown);
                        }
                    });
                }
            }
            if (apiDomains_cdn.length > 0) {
                test_domains(apiDomains_cdn);
            } else if (apiDomains_direct.length > 0) {
                routename = "快速线路";
                test_domains(apiDomains_direct);
            } else {
                $('#check_text').hide();
                document.title = '请选择线路';
            }

            var checkUrl = function () {
                residue_testNumber -= 1;
                if (residue_testNumber < 10) {
                    console.log('residue_testNumber', residue_testNumber);
                }
                if (residue_testNumber <= 0 && routename == "优质线路") {
                    routename = "快速线路";
                    $('.routename').html(routename);
                    console.log('优质线路无可用,切换快速线路~');
                    if (apiDomains_direct.length == 0) {
                        $('#check_text').hide();
                        document.title = '请选择线路';
                    } else {
                        test_domains(apiDomains_direct);
                    }
                } else if (residue_testNumber <= 0 && routename == "快速线路") {
                    $('#check_text').hide();
                    document.title = '请选择线路';
                }
            }


            // 格式化耗时
            function formatElapsedTime(elapsedTime) {
                var elapsedTimeInSeconds = (elapsedTime / 1000).toFixed(2);
                return elapsedTimeInSeconds + "s";
            }

            var tourl = false;
            var openUrl = function (url, isopen) {
                if (tourl) {
                    return;
                }
                tourl = true;
                url = (url + '').replace(/\/test.html/g, "");
                localStorage.setItem("g_last_visit_url", url);//将域名列入缓存，下次优先打开
                $('#jump_url').show().find("span").html(url);
                url = url + '/<%=authUrlPath %>' + redirect_parames;
            console.log('%c打开域名 ' + url, 'color: green;    font-weight: bold;;background:#eee; font-size: 20px;');
            if (testurl == "1" && isopen != 'open') {
                return
            }
            if (get_param('test') != "1" || isopen == 'open') {
                location.href = url;
            }

        }

        // 随机排序函数
        function shuffleArray(array) {
            for (var i = array.length - 1; i > 0; i--) {
                var j = Math.floor(Math.random() * (i + 1));
                // 交换元素位置
                [array[i], array[j]] = [array[j], array[i]];
            }
            return array;
        }

        var pushError = function (url, hs, errmsg) {
            url = (url + '').replace(/\/test.html/g, "");
            var data = {}
            data.url = url;
            data.hs = hs;
            data.errmsg = errmsg;
            data.ua = navigator.userAgent;
            $.ajax({
                url: '../api/v3.aspx?type=network_error',
                type: "POST",
                data: data,
                datatype: "json",
                success: function (json) {
                    console.log(' this.data.url', json.url);
                    $('[url="' + json.url + '"]').css("color", "#000").css("font-weight", "500");
                },
                error: function (ex) {
                    console.log('异常', ex);
                    //tp('网络异常');
                    //setTimeout(function () {
                    //    pushError(url, hs, errmsg);
                    //}, 1000)
                }
            });

            $('#errorLists').append('<div style="display: flex;padding: 5px 0;color: #c11c1c;align-items: center;font-weight:bold;">                    <svg t="" class="icon" viewBox="0 0 1041 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="1740" width="18" height="18">                        <path d="M506.215991 55.273078c261.983529 0 474.329892 212.366932 474.329892 474.329892 0 261.971187-212.346363 474.325778-474.329892 474.325778-261.96296 0-474.329892-212.354591-474.329892-474.325778 0.008228-261.96296 212.371046-474.329892 474.329892-474.329892z" fill="#B8D9FF" p-id="1741"></path><path d="M511.584506 48.600487c-22.473714 0-44.52782 1.666091-66.166433 4.697965 230.611655 32.21109 408.138777 230.134454 408.138777 469.631927 0 239.501587-177.527121 437.424951-408.138777 469.619585 21.638612 3.02776 43.692718 4.71442 66.166433 4.71442 205.258278 0 380.070288-130.382921 446.125648-312.863063 18.248837-50.402333 28.196016-104.782716 28.196016-161.475056 0-261.954732-212.371046-474.325778-474.321664-474.325778z" fill="#6E6E96" opacity=".15" p-id="1742"></path><path d="M161.018424 525.76479c0-244.664412 172.121582-446.022803 393.131617-471.536618a479.49683 479.49683 0 0 0-51.142818-2.78916C241.036036 51.443126 28.673218 263.810058 28.673218 525.76479c0 261.967073 212.358704 474.325778 474.334005 474.325778 17.277979 0 34.333813-0.970858 51.142818-2.780932-221.010035-25.509702-393.131618-226.872207-393.131617-471.544846z" fill="#FFFFFF" opacity=".21" p-id="1743"></path><path d="M506.220105 1020.379852c-270.618404 0-490.78511-220.166706-490.78511-490.78511 0-270.618404 220.166706-490.780996 490.78511-490.780996s490.78511 220.166706 490.78511 490.780996c0 55.462313-9.177898 109.892061-27.29098 161.775364a16.451104 16.451104 0 0 1-20.959834 10.111731 16.451104 16.451104 0 0 1-10.111732-20.959834c16.887168-48.390683 25.452109-99.167372 25.452109-150.927261 0-252.472413-205.402261-457.87056-457.874673-457.87056S48.349545 277.126443 48.349545 529.594742c0 252.472413 205.402261 457.874674 457.874673 457.874674 50.965925 0 101.006243-8.309885 148.73049-24.695169a16.455218 16.455218 0 0 1 10.687664 31.125045c-51.175729 17.570059-104.811512 26.48056-159.422267 26.48056z" fill="#6E6E96" p-id="1744"></path><path d="M499.938325 64.22883s-100.747073 105.69598-100.747073 461.889747c-0.728143 138.968431 16.080862 379.465559 100.747073 464.127657 0 0-264.690412-59.736556-264.690412-464.119429 0-436.174355 264.690412-461.897974 264.690412-461.897975z" fill="#FFFFFF" opacity=".21" p-id="1745"></path><path d="M469.590789 709.610715l-0.440177-0.004114c-120.962309-3.184085-233.553025-22.481942-317.026234-54.331016-90.314465-34.457227-138.051053-79.598004-138.051053-130.539246 0-52.599105 53.249086-100.512586 149.931721-134.920448 91.359371-32.511397 212.490346-50.414675 341.083762-50.414675 128.589303 0 249.720277 17.907391 341.079649 50.414675 96.686748 34.407861 149.935834 82.325457 149.935834 134.920448a16.455218 16.455218 0 0 1-32.910436 0c0-37.07772-46.67934-74.953519-128.062736-103.914703-87.903776-31.28137-205.118408-48.509983-330.046424-48.509983-124.928016 0-242.142649 17.228613-330.046425 48.509983-81.383395 28.961184-128.058622 66.836982-128.058622 103.914703 0 67.729678 161.734226 145.093886 423.034863 151.968054a16.455218 16.455218 0 0 1-0.423722 32.906322z" fill="#6E6E96" p-id="1746"></path><path d="M500.415526 1020.252324c-118.831358 0-211.914414-216.122836-211.914413-492.027479 0-275.908757 93.083055-492.035707 211.914413-492.035706 92.350798 0 171.010855 129.444974 200.395761 329.783141a16.455218 16.455218 0 1 1-32.564877 4.776127c-26.04861-177.605284-95.061795-301.648832-167.830884-301.648832-86.447489 0-179.003977 184.487679-179.003977 459.12527 0 274.633478 92.556488 459.117043 179.003977 459.117043a16.455218 16.455218 0 0 1 0 32.910436z" fill="#6E6E96" p-id="1747"></path><path d="M719.656626 465.143766c0.296194 0 0.588274 0.024683 0.884468 0.032911 0.209804 0 0.41138-0.024683 0.600615-0.024683l-1.485083-0.008228z m213.531139 539.644767c109.250307 0 87.549988-88.02719 67.968278-119.71994l-238.946223-393.699323c-9.544027-15.434995-23.847725-25.863489-41.664612-26.200821-19.659872 0.320877-34.823355 15.636571-44.412634 31.170297l-236.477941 388.729847c-19.585823 31.688636-41.277915 119.71994 67.968279 119.71994h425.564853z" fill="#FFD500" p-id="1748"></path><path d="M724.004917 482.631549l-1.485083-0.008227c0.296194 0 0.588274 0.020569 0.884468 0.028796 0.201576 0 0.407267-0.020569 0.600615-0.020569z m280.006107 419.920713l-238.946224-393.699322c-9.544027-15.434995-23.843611-25.863489-41.664612-26.200822-19.659872 0.320877-34.827469 15.636571-44.416748 31.170297l-236.469713 388.729847c-11.740798 18.993436-24.201512 58.210334-8.803541 86.607927 2.64929-7.129223 5.746985-13.435686 8.803541-18.376365l236.469713-388.721619c9.589278-15.533726 24.756876-30.84942 44.416748-31.170297 17.821001 0.337332 32.120586 10.761713 41.664612 26.200821l238.946224 393.691095c3.048329 4.932452 6.150138 11.238914 8.799428 18.372251 15.39797-28.389365 2.94137-67.614492-8.799428-86.603813z m-280.61495-351.664468c-0.296194-0.008228-0.588274-0.03291-0.884468-0.03291l1.485084 0.008228c-0.185121 0-0.390811 0.024683-0.600616 0.024682z" fill="#FFE76E" p-id="1749"></path><path d="M1001.156043 885.068593l-238.946223-393.699323a67.207225 67.207225 0 0 0-7.499466-9.963634l219.265783 361.274315c17.98144 29.088712 37.900481 109.920857-62.410529 109.920857h-390.737383c-73.320338 0-82.38305-43.170265-74.941178-77.767361l-6.232414 10.235146c-19.585823 31.688636-41.277915 119.71994 67.968279 119.71994h425.560739c109.246194 0 87.554102-88.02719 67.972392-119.71994z" fill="#FF9900" opacity=".52" p-id="1750"></path><path d="M705.282993 787.353393l-6.656136-100.097092c-1.250597-19.507661-1.867667-33.506938-1.867667-42.014286 0-11.563905 3.011305-20.57725 9.046256-27.060606 6.034951-6.475128 13.97048-9.708579 23.823042-9.708579 11.921806 0 19.902586 4.146715 23.913546 12.440145 4.027415 8.289316 6.043179 20.231691 6.043179 35.827124 0 9.198467-0.485429 18.536803-1.456287 28.023237l-8.943411 103.017893c-0.974972 12.255024-3.056557 21.663295-6.244755 28.20013-3.196426 6.549177-8.457982 9.819651-15.809351 9.819652-7.491238 0-12.699315-3.16763-15.611889-9.507003-2.904346-6.343487-4.985931-15.986244-6.236527-28.940615z m23.090785 137.23652c-8.462096 0-15.850489-2.748021-22.165179-8.223495-6.298235-5.479588-9.457637-13.151833-9.457637-23.008509 0-8.610193 3.011305-15.932765 9.046256-21.988285 6.034951-6.034951 13.423344-9.046256 22.165179-9.046256 8.733607 0 16.196048 3.019533 22.358528 9.046256 6.170707 6.05552 9.264288 13.378092 9.264288 21.988285 0 9.708579-3.134719 17.356141-9.358906 22.90155-6.240641 5.561864-13.530303 8.330454-21.852529 8.330454z" fill="#666666" p-id="1751"></path><path d="M933.187765 1021.243751H507.622912c-45.136663 0-75.714573-13.7977-90.886284-41.006404-20.725347-37.172338-3.229337-84.164327 8.914615-103.815971l236.420347-388.631116c14.673941-23.77779 34.206285-37.476759 55.301874-38.945387 0.765168-0.102845 1.517994-0.143983 2.332528-0.156325h0.090503l1.431604 0.008228c0.588274 0.004114 1.164207 0.037024 1.736026 0.098731 21.515198 1.118955 40.36465 13.106581 53.236744 33.922432l239.020272 393.814509c12.07813 19.544685 29.570027 66.540789 8.840566 103.709013-15.163484 27.208703-45.741393 41.00229-90.873942 41.00229zM720.532866 481.631895c-14.151488 0.415494-25.110663 14.789127-30.401015 23.354068l-236.420348 388.639343c-10.424381 16.866599-19.807969 49.818173-8.227609 70.584659 8.922842 16.010927 29.829197 24.12335 62.139018 24.12335h425.560739c32.309821 0 53.216176-8.116536 62.139018-24.119236 11.576246-20.766485 2.192658-53.726287-8.165903-70.494155L748.136495 499.909528c-4.985931-8.067171-13.888204-17.878595-27.603629-18.277633z" fill="#6E6E96" p-id="1752"></path></svg>                    <span style="margin-left: 10px;" url=' + url + '>' + url + '</span>       <span style="color: #b92d2d;margin-left: 8px;font-size: 13px;">[耗时:' + hs + ']</span>         </div>')
        }
        </script>

    </form>
</body>
</html>

using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Data;
using System.Data.SqlClient;

public partial class games_authorize : globalClass
{
    protected void Page_Load(object sender, EventArgs e)
    {
        string sql = string.Empty;
        dbClass db = new dbClass();
        DataTable dt = new DataTable();
        List<SqlParameter> pams = new List<SqlParameter>();
        Dictionary<string, object> pmlist = new Dictionary<string, object>();


        //Response.Write("授权结果 ---------- <br><br>" + getHost(Request) + "/games/api.aspx?key=game_KkhSe0Aq&act=userinfo&openid=" + Request.QueryString["openid"] + "&code=" + Request.QueryString["code"]);
        //Response.End();


        if (Request.Url.Host != "localhost")
        {
            Response.Write("请求失败");
            Response.End();
            return;
        }

        Random rd = new Random();

        pmlist["text_response"] = SendRequestC(getHost(Request) + "/games/api.aspx?key=game_KkhSe0Aq&act=gameclose&openid=" + Request.QueryString["openid"] + "&token=" + Request.QueryString["token"] + "&balance=" + rd.Next(0, 10000), "", "");



        Response.Write("授权结果 ---------- <br><br>" + pmlist["text_response"]);
        Response.End();

    }
}
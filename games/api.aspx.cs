using com.cotees;
using LitJson;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.IO;
using System.Linq;
using System.Text;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

public partial class Api_admin : globalClass
{
    fuzhu fz = new fuzhu(HttpContext.Current);
    protected void Page_Load(object sender, EventArgs e)
    {
        Response.ContentType = "application/json";
        string result = main();
        Response.Write(result);
        Response.End();
    }

    public string main()
    {

        string _result = "";

        //数据库相关参数
        string sql = string.Empty;
        dbClass db = new dbClass();
        DataTable dt = new DataTable();
        DataTable temp_dt = new DataTable();
        DataRow[] rows;
        DataSet ds;
        DataRow[] dr;
        Dictionary<string, object> pmlist = new Dictionary<string, object>();
        Dictionary<string, object> dic = new Dictionary<string, object>();
        Dictionary<string, object> _data = new Dictionary<string, object>();
        List<Dictionary<string, object>> list = new List<Dictionary<string, object>>();

        //临时参数
        Random rd = new Random();
        string temp = string.Empty;
        string temp_val = string.Empty;
        bool result = false;
        int res = 0;
        string[] g;
        string[] g2;
        string[] arrayList;
        List<string> text_array = new List<string>();
        List<string> temp_array = new List<string>();
        JsonData jd;
        List<SqlParameter> pams = fz.collectReqParames(out result);

        fz.setResponseLable("msg");


        pmlist["userip"] = System.Web.HttpContext.Current.Request.ServerVariables["HTTP_X_TENANT_FORWARD_FOR"] + "";
        if (string.IsNullOrEmpty(pmlist["userip"] + ""))
        {
            pmlist["userip"] = getUserIP();
        }

        pams.Add(new SqlParameter("@ips", pmlist["userip"] + ""));

        switch (fz.req("key"))
        {
            case "game_KkhSe0Aq":
            case "game_TopFlower0Sh":
                break;
            default:
                fz.sendResponse("null");
                break;
        }



        if (Request.Url.Host != "localhost")
        {
            g = uConfig.stcdata("game_ips").Split('\n');
            if (!g.Contains(pmlist["userip"] + ""))
            {
                fz.sendResponse("ip不在白名单内：" + pmlist["userip"]);
            }
        }

        switch (fz.req("act"))
        {
            case "req_user":
                fz.check_exist(new string[] { "openid" });
                sql = @"

                -- OPENID获取UID
                declare @uid int
                select @uid=userid from authorize_list with(nolock) where openid=@openid
                if(@uid is null)
                begin
                    select 'OpenId不存在' as errmsg
                    return
                end

select 'success' as errmsg,* from accounts where id=@uid

";


                dt = db.getDataTable(sql, pams.ToArray());
                if (dt.Rows.Count == 0)
                {
                    fz.sendResponse("请求出错");
                }

                temp = dt.Rows[0]["errmsg"] + "";

                if (temp != "success")
                {
                    fz.sendResponse(temp);
                }

                temp_dt = selectDateTable(chelper.gdt("account_groups"), " name='★托号' ");
                pmlist["th_groupId"] = "-1";
                if (temp_dt.Rows.Count > 0)
                {
                    pmlist["th_groupId"] = temp_dt.Rows[0]["id"] + "";
                }

                dic = new Dictionary<string, object>();

                dic.Add("nickname", dt.Rows[0]["parent_code"] + "");
                dic.Add("username", dt.Rows[0]["username"] + "");
                dic.Add("headimgurl", dt.Rows[0]["avatar"] + "");
                dic.Add("phone", dt.Rows[0]["phone"] + "");
                dic.Add("userid", dt.Rows[0]["id"] + "");
                dic.Add("isth", pmlist["th_groupId"] + "" == dt.Rows[0]["groupid"] + "" ? 1 : 0);


                fz.sendResponse("success", 1, dic);
                break;
            case "userinfo":
                pams.Add(new SqlParameter("@area", ip_area(pmlist["userip"] + "")));

                fz.check_exist(new string[] { "openid", "code" });

                pams.Add(new SqlParameter("@FF", fz.req("from")));
                sql = @" 

-- 授权开始

declare @gameName varchar(10)
declare @uid int
declare @from_type varchar(22)

if(@FF='openid')
begin
    -- 直接获取openid信息（免授权）
    select @gameName=gameName,@uid=userid,@from_type=isnull(from_type,'') from [authorize_list] with(nolock) where openid=@openid
end
else
begin

    update [authorize_list] set @gameName=gameName,@uid=userid,@from_type=isnull(from_type,'') where openid=@openid and code=@code and expire_time>getdate()
    if(@uid is null)
    begin
        select '授权已过期或发生变化，请重新授权！' as errmsg
        return
    end

end

-- 获取信息
declare @parentid int
declare @parent_code varchar(10)
declare @phone varchar(11)
declare @groupid int
declare @amount decimal(18,2)
declare @current_amount decimal(18,6)
declare @current_freeze_amount decimal(18,6)
declare @roomNumber int
declare @user_token varchar(32)
declare @parent_user_token varchar(32)
declare @game_remark varchar(20)
declare @relaids varchar(1000)

select @game_remark=isnull(a.game_remark,''),@user_token=a.token,@parent_user_token=pa.token,@parentid=a.parentid,@roomNumber=a.roomNumber,@parent_code=a.parent_code,@phone=a.phone,@amount=a.amount,@groupid=a.groupid,@relaids=a.relaids from accounts a left join accounts pa on a.parentid=pa.id where a.id=@uid and a.state=1
if(@amount is null)
begin
    select '账户状态异常' as errmsg
    return
end

declare @realname varchar(30)
select @realname=name from [payment_list] with(nolock) where userid=@uid

declare @token varchar(32)
set @token=LOWER(replace(newid(),'-',''))  


if(@FF<>'openid')
begin
    insert into [authorize_records](gameName,userid,token,freeze_amount,code,openid,userip,log_text,state,create_time) values(@gameName,@uid,@token,@amount,@code,@openid,@ips,@area,0,getdate())
end

select 'success' as errmsg,@amount as balance,@parent_code as parent_code,@token as token,@uid as userid,@phone as phone,@groupid as groupid,@realname as realname,@roomNumber as roomNumber,@parentid as parentid,@user_token as user_token,@parent_user_token as parent_user_token,@from_type as from_type,@game_remark as game_remark,@relaids as relaids

";
                dt = db.getDataTable(sql, pams.ToArray());
                log.WriteLog("授权", "userinfo", fz.req("code") + "-" + fz.req("openid") + "-" + ToJson(dt));
                if (dt.Rows.Count == 0)
                {
                    fz.sendResponse("授权出错");
                }

                temp = dt.Rows[0]["errmsg"] + "";

                if (temp != "success")
                {
                    fz.sendResponse(temp);
                }


                temp_dt = selectDateTable(chelper.gdt("account_groups"), " name='★托号' ");
                pmlist["th_groupId"] = "-1";
                if (temp_dt.Rows.Count > 0)
                {
                    pmlist["th_groupId"] = temp_dt.Rows[0]["id"] + "";
                }


                dic = new Dictionary<string, object>();
                dic.Add("siteid", uConfig.stcdata("siteid"));
                dic.Add("sitecode", uConfig.stcdata("sitecode"));
                dic.Add("user_token", dt.Rows[0]["user_token"] + "");
                dic.Add("parent_user_token", dt.Rows[0]["parent_user_token"] + "");
                dic.Add("openid", fz.req("openid"));
                dic.Add("token", dt.Rows[0]["token"] + "");
                dic.Add("balance", dt.Rows[0]["balance"] + "");
                dic.Add("nickname", dt.Rows[0]["parent_code"] + "");
                dic.Add("parentid", dt.Rows[0]["parentid"] + "");
                dic.Add("realname", dt.Rows[0]["realname"] + "");
                dic.Add("headimgurl", "");
                dic.Add("phone", dt.Rows[0]["phone"] + "");
                dic.Add("userid", dt.Rows[0]["userid"] + "");
                dic.Add("roomNumber", dt.Rows[0]["roomNumber"] + "");
                dic.Add("isth", pmlist["th_groupId"] + "" == dt.Rows[0]["groupid"] + "" ? 1 : 0);
                dic.Add("from_type", dt.Rows[0]["from_type"] + "");
                //dic.Add("game_remark", dt.Rows[0]["game_remark"] + "");
                dic.Add("sf", search_remark(dt.Rows[0]["relaids"] + "") ? 1 : 0);
                dic.Add("sign", "-");


                fz.sendResponse("success", 1, dic);
                break;
            case "gamebalance":
                fz.check_exist(new string[] { "openid" });



                log.WriteLog("GAMEAPI", "gamebalance_req", fz.req("openid"));

                sql = @"

                -- OPENID获取UID
                declare @uid int
declare @from_type varchar(22)
                select @from_type=isnull(from_type,''),@uid=userid from authorize_list with(nolock) where openid=@openid
                if(@uid is null)
                begin
                    select 'OpenId不存在' as errmsg
                    return
                end

declare @amount decimal(18,2)

select @amount=(case when isnull(@from_type,'')='onetouch' then isnull(onetouch_balance,0) else amount end) from accounts where id=@uid and state=1
if(@amount is null)
begin
    select '账户状态异常' as errmsg
    return
end
select 'success' as errmsg,@amount as balance,@from_type as from_type

";


                dt = db.getDataTable(sql, pams.ToArray());
                if (dt.Rows.Count == 0)
                {
                    fz.sendResponse("请求出错");
                }

                temp = dt.Rows[0]["errmsg"] + "";

                if (temp != "success")
                {
                    fz.sendResponse(temp);
                }

                dic = new Dictionary<string, object>();
                dic.Add("balance", dt.Rows[0]["balance"] + "");
                dic.Add("from_type", dt.Rows[0]["from_type"] + "");


                log.WriteLog("GAMEAPI", "gamebalance_res", JsonMapper.ToJson(dic));

                fz.sendResponse("success", 1, dic);
                break;
            case "gamerecharge":
                fz.check_exist(new string[] { "appid", "openid", "orderNo", "orderAmt", "sign" });


                pmlist["sign"] = md5UTF8("appid=" + uConfig.stcdata("g_api_appid") + "&openid=" + fz.req("openid") + "&orderAmt=" + fz.req("orderAmt") + "&orderNo=" + fz.req("orderNo") + "&key=" + uConfig.stcdata("g_api_appkey"));


                if (Request.Url.Host != "localhost")
                {
                    if (pmlist["sign"] + "" != fz.req("sign"))
                    {
                        fz.sendResponse("sign验证失败");
                    }
                }

                sql = @"

-- OPENID获取UID
declare @uid int
declare @gameName varchar(10)
declare @code varchar(32)
declare @from_type varchar(22)
select @from_type=isnull(from_type,''),@gameName=gameName,@code=code,@uid=userid from authorize_list with(nolock) where openid=@openid
if(@uid is null)
begin
    select 'OpenId不存在' as errmsg,'' as gameName,'' as code,@openid as openid
    return
end



declare @amount decimal(18,2)
declare @current_amount decimal(18,6)
declare @token varchar(32)

select @token=tokenOrderNo from recwit_records where type='recharge' and orderNo=@orderNo
if(@token is not null)
begin
    select '订单已充值' as errmsg,0 as balance,@token as token,@gameName as gameName,@code as code,@openid as openid
    return
end


declare @total_amount decimal(18,2)
set @total_amount=cast(@orderAmt as decimal(18,2))

if(@from_type='onetouch')
begin

-- （…）资金池扣款
declare @current_onetouch_balance decimal(18,2)
update accounts set @current_onetouch_balance=isnull(onetouch_balance,0)-@total_amount ,onetouch_balance=isnull(onetouch_balance,0)-@total_amount where id=@uid and isnull(onetouch_balance,0)-@total_amount>0

if(@@rowcount=0)
begin
    select '账户额度不足' as errmsg,@gameName as gameName,@code as code,@openid as openid
    return
end
set @token=lower(replace(newid(),'-',''))
insert into onetouch_balance_records values(@uid,'游戏内转入',-1*@total_amount,@current_onetouch_balance,@token,getdate())

set @total_amount=-1*@total_amount
exec('[onetouch_balance_reset] '+@uid+','''+@token+'''')


set @current_amount=@current_onetouch_balance



end
else
begin
-- （…）账户余额扣款
            
update accounts set amount=amount-cast(@orderAmt as decimal(18,6)),@current_amount=amount-cast(@orderAmt as decimal(18,6)) where id=@uid and amount>=@orderAmt and state=1
if(@current_amount is null)
begin
    select '账户余额不足或状态异常' as errmsg,@gameName as gameName,@code as code,@openid as openid
    return
end
            
            
set @token=LOWER(replace(newid(),'-',''))  
       
insert into recwit_records values('recharge',@uid,@orderAmt,@orderNo,@token,getdate())     
insert into transaction_list values(@uid,'游戏转入',-1*cast(@orderAmt as decimal(18,6)),@current_amount,@token,@orderNo,1,'',GETDATE(),GETDATE())

end


select 'success' as errmsg,@current_amount as balance,@token as token,@gameName as gameName,@code as code,@openid as openid,@from_type as from_type

";


                pmlist["orderAmt"] = fz.req("orderAmt");
                pmlist["orderNo"] = fz.req("orderNo");
                pmlist["orderStatus"] = "99";
                pmlist["token"] = "0000";

                dt = db.getDataTable(sql, pams.ToArray());
                if (dt.Rows.Count == 0)
                {
                    pmlist["errmsg"] = "请求出错";
                }
                else
                {
                    temp = dt.Rows[0]["errmsg"] + "";
                    pmlist["errmsg"] = temp;
                    if (temp == "success" || temp == "订单已充值")
                    {
                        pmlist["orderStatus"] = "00";
                    }
                }

                pmlist["gameName"] = "";
                try
                {
                    pmlist["gameName"] = dt.Rows[0]["gameName"];
                }
                catch (Exception)
                {
                }
                //temp_dt = selectDateTable(chelper.gdt("auth_list"), " code='" + pmlist["gameName"] + "' ");

                pmlist["sign"] = md5UTF8("appid=" + uConfig.stcdata("g_api_appid") + "&code=" + dt.Rows[0]["code"] + "&openid=" + dt.Rows[0]["openid"] + "&orderAmt=" + pmlist["orderAmt"] + "&orderNo=" + pmlist["orderNo"] + "&orderStatus=" + pmlist["orderStatus"] + "&token=" + pmlist["token"] + "&key=" + uConfig.stcdata("g_api_appkey"));
                pmlist["url"] = uConfig.stcdata("g_notify_gateway") + "/Public.rechargefromtaofanke.do?code=" + dt.Rows[0]["code"] + "&openid=" + dt.Rows[0]["openid"] + "&orderAmt=" + pmlist["orderAmt"] + "&orderNo=" + pmlist["orderNo"] + "&orderStatus=" + pmlist["orderStatus"] + "&token=" + pmlist["token"] + "&sign=" + pmlist["sign"];
                pmlist["text_response"] = getContent(pmlist["url"] + "", true);

                log.WriteLog("授权", "回调通知_gamerecharge", pmlist["gameName"] + "----" + dt.Rows[0]["openid"] + "----" + pmlist["orderNo"] + "----" + pmlist["orderAmt"] + "----" + pmlist["url"] + "----" + pmlist["text_response"] + "");


                dic = new Dictionary<string, object>();
                if (pmlist["errmsg"] + "" == "success" || pmlist["errmsg"] + "" == "订单已充值")
                {
                    dic.Add("balance", dt.Rows[0]["balance"] + "");
                    dic.Add("orderAmt", fz.req("orderAmt"));
                    dic.Add("tfkOrderNo", dt.Rows[0]["token"] + "");
                }
                try
                {
                    dic.Add("from_type", dt.Rows[0]["from_type"] + "");
                }
                catch (Exception)
                {
                }
                fz.sendResponse(pmlist["errmsg"] + "", (pmlist["errmsg"] + "" == "success" || pmlist["errmsg"] + "" == "订单已充值" ? 1 : 0), dic);
                break;
            case "gamewithdraw":
                fz.check_exist(new string[] { "appid", "openid", "orderNo", "orderAmt" });



                pmlist["sign"] = md5UTF8("appid=" + uConfig.stcdata("g_api_appid") + "&openid=" + fz.req("openid") + "&orderAmt=" + fz.req("orderAmt") + "&orderNo=" + fz.req("orderNo") + "&key=" + uConfig.stcdata("g_api_appkey"));


                if (Request.Url.Host != "localhost")
                {
                    if (pmlist["sign"] + "" != fz.req("sign"))
                    {
                        fz.sendResponse("sign验证失败");
                    }
                }

                sql = @"

-- OPENID获取UID
declare @uid int
declare @gameName varchar(10)
declare @code varchar(32)
declare @from_type varchar(22)
select @from_type=isnull(from_type,''),@gameName=gameName,@code=code,@uid=userid from authorize_list with(nolock) where openid=@openid
if(@uid is null)
begin
    select 'OpenId不存在' as errmsg,'' as gameName,'' as code,@openid as openid
    return
end


declare @amount decimal(18,2)
declare @current_amount decimal(18,6)
declare @token varchar(32)

select @token=tokenOrderNo from recwit_records where type='withdraw' and orderNo=@orderNo
if(@token is not null)
begin
    select '订单已提现' as errmsg,0 as balance,@token as token,@gameName as gameName,@code as code,@openid as openid
    return
end


declare @total_amount decimal(18,2)
set @total_amount=cast(@orderAmt as decimal(18,2))

if(@from_type='onetouch')
begin

-- （…）加款到资金池

set @token=LOWER(replace(newid(),'-',''))  

declare @current_onetouch_balance decimal(18,2)
update accounts set @current_onetouch_balance=isnull(onetouch_balance,0)+@total_amount ,onetouch_balance=isnull(onetouch_balance,0)+@total_amount where id=@uid
insert into onetouch_balance_records values(@uid,'游戏内转出',@total_amount,@current_onetouch_balance,@token,getdate())

exec('[onetouch_balance_reset] '+@uid+','''+@token+'''')

set @current_amount=@current_onetouch_balance

end
else
begin
-- （…）加款到账户

update accounts set amount=amount+cast(@orderAmt as decimal(18,6)),@current_amount=amount+cast(@orderAmt as decimal(18,6)) where id=@uid
if(@current_amount is null)
begin
    select '账户不存在' as errmsg,@gameName as gameName,@code as code,@openid as openid
    return
end
            
            
set @token=LOWER(replace(newid(),'-',''))  
      
insert into recwit_records values('withdraw',@uid,@orderAmt,@orderNo,@token,getdate())      
insert into transaction_list values(@uid,'游戏转出',cast(@orderAmt as decimal(18,6)),@current_amount,@token,@orderNo,1,'',GETDATE(),GETDATE())

end


select 'success' as errmsg,@current_amount as balance,@token as token,@gameName as gameName,@code as code,@openid as openid,@from_type as from_type

";


                pmlist["orderAmt"] = fz.req("orderAmt");
                pmlist["orderNo"] = fz.req("orderNo");
                pmlist["orderStatus"] = "99";
                pmlist["token"] = "0000";

                dt = db.getDataTable(sql, pams.ToArray());
                if (dt.Rows.Count == 0)
                {
                    pmlist["errmsg"] = "请求出错";
                }
                else
                {
                    temp = dt.Rows[0]["errmsg"] + "";
                    pmlist["errmsg"] = temp;
                    if (temp == "success" || temp == "订单已提现")
                    {
                        pmlist["orderStatus"] = "00";
                    }
                }

                pmlist["gameName"] = "";
                try
                {
                    pmlist["gameName"] = dt.Rows[0]["gameName"];
                }
                catch (Exception)
                {
                }

                pmlist["sign"] = md5UTF8("appid=" + uConfig.stcdata("g_api_appid") + "&code=" + dt.Rows[0]["code"] + "&openid=" + dt.Rows[0]["openid"] + "&orderAmt=" + pmlist["orderAmt"] + "&orderNo=" + pmlist["orderNo"] + "&orderStatus=" + pmlist["orderStatus"] + "&token=" + pmlist["token"] + "&key=" + uConfig.stcdata("g_api_appkey"));
                pmlist["url"] = uConfig.stcdata("g_notify_gateway") + "/Public.withdrawfromtaofanke.do?code=" + dt.Rows[0]["code"] + "&openid=" + dt.Rows[0]["openid"] + "&orderAmt=" + pmlist["orderAmt"] + "&orderNo=" + pmlist["orderNo"] + "&orderStatus=" + pmlist["orderStatus"] + "&token=" + pmlist["token"] + "&sign=" + pmlist["sign"];
                pmlist["text_response"] = getContent(pmlist["url"] + "", true);

                log.WriteLog("授权", "回调通知_gamewithdraw", pmlist["gameName"] + "----" + dt.Rows[0]["openid"] + "----" + pmlist["orderNo"] + "----" + pmlist["orderAmt"] + "----" + pmlist["url"] + "----" + pmlist["text_response"] + "");


                dic = new Dictionary<string, object>();
                if (pmlist["errmsg"] + "" == "success" || pmlist["errmsg"] + "" == "订单已提现")
                {
                    dic.Add("balance", dt.Rows[0]["balance"] + "");
                    dic.Add("orderAmt", fz.req("orderAmt"));
                    dic.Add("tfkOrderNo", dt.Rows[0]["token"] + "");
                }

                try
                {
                    dic.Add("from_type", dt.Rows[0]["from_type"] + "");
                }
                catch (Exception)
                {
                }
                fz.sendResponse(pmlist["errmsg"] + "", (pmlist["errmsg"] + "" == "success" || pmlist["errmsg"] + "" == "订单已提现" ? 1 : 0), dic);
                break;
            default:
                fz.sendResponse("not found", 404);
                break;
        }
        return _result;
    }


    public string GetSignParams(Dictionary<string, object> jsonDict, bool is_encode = false)
    {
        // 按键升序排序
        var sortedDict = jsonDict.OrderBy(pair => pair.Key)
                                 .ToDictionary(pair => pair.Key, pair => pair.Value);

        // 构建参数字符串
        List<string> paramPairs = new List<string>();
        foreach (var pair in sortedDict)
        {
            var key = pair.Key;
            var value = pair.Value;
            if (is_encode)
            {
                value = Uri.EscapeDataString(value + "");
            }
            paramPairs.Add(key + "=" + value);
        }

        string paramString = string.Join("&", paramPairs);

        return paramString;
    }

    static void ShuffleList<T>(List<T> list)
    {
        Random random = new Random();
        int n = list.Count;

        for (int i = n - 1; i > 0; i--)
        {
            int j = random.Next(0, i + 1);
            T temp = list[i];
            list[i] = list[j];
            list[j] = temp;
        }
    }

    public static string GetCurrentTimeRange(string input)
    {
        // 获取当前时间的小时部分
        int currentHour = DateTime.Now.Hour;

        // 解析输入字符串，将其拆分成每行
        string[] lines = input.Split('\n');

        foreach (string line in lines)
        {
            string[] parts = line.Split('：');
            if (parts.Length == 2)
            {
                int rangeHour;
                if (int.TryParse(parts[0], out rangeHour))
                {
                    // 检查当前小时是否与范围小时匹配
                    if (currentHour == rangeHour)
                    {
                        return parts[0] + "：" + parts[1].Trim();
                    }
                }
            }
        }

        // 默认返回空字符串表示未找到匹配的时间范围
        return string.Empty;
    }
}
<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <title></title>
</head>
<body>

    <script src="../js/jquery_1.9.1.min.js"></script>
    <script>

        function get_param(paramName) {
            var urlParams = new URLSearchParams(window.location.search);
            return urlParams.get(paramName) || "";
        }

        var __domain = get_param("url");

        var startTime = new Date().getTime();
        $.ajax({
            url: __domain + '/test.html',
            type: "GET",
            data: {},
            datatype: "json",
            success: function (data) {
                console.log('%cURL正常：' + this.url, 'color: green;');
            },
            error: function (xhr, status, errorThrown) {
                var endTime = new Date().getTime(); // 记录请求结束时间
                var elapsedTime = endTime - startTime; // 计算请求耗时
                var formattedElapsedTime = formatElapsedTime(elapsedTime);

                console.log('异常', this.url, formattedElapsedTime, xhr, status);
                pushError(this.url, formattedElapsedTime, statusCode + ' ' + errorThrown);
            }
        });
    </script>
</body>
</html>

using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Data;
using System.Data.SqlClient;

public partial class games_authorize : baseClass
{
    protected void Page_Load(object sender, EventArgs e)
    {
        string sql = string.Empty;
        dbClass db = new dbClass();
        DataTable dt = new DataTable();
        List<SqlParameter> pams = new List<SqlParameter>();
        Dictionary<string, object> pmlist = new Dictionary<string, object>();


        //Response.Write("授权结果 ---------- <br><br>" + getHost(Request) + "/games/api.aspx?key=game_KkhSe0Aq&act=userinfo&openid=" + Request.QueryString["openid"] + "&code=" + Request.QueryString["code"]);
        //Response.End();

        if (Request.Url.Host != "localhost")
        {
            return;
        }

        pmlist["text_response"] = SendRequestC(getHost(Request) + "/games/api.aspx?key=game_KkhSe0Aq&act=userinfo&openid=" + Request.QueryString["openid"] + "&code=" + Request.QueryString["code"], "", "");



        Response.ContentType = "text/plain";

        Response.Write("授权结果 ---------- \r\n\r\n" + "openid:" + Request.QueryString["openid"] + "\r\n\r\n" + "code:" + Request.QueryString["code"] + "\r\n\r\n" + pmlist["text_response"]);
        Response.End();

    }
}
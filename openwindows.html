<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>弹出窗口示例</title>
<style>
    body {
        font-family: Arial, sans-serif;
        overflow-x: hidden; /* 防止水平滚动条出现 */
    }
    .rightbox_overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5); /* 半透明黑色背景 */
        z-index: 1000;
        display: none; /* 初始隐藏 */
    }
    .rightBoxpopup {
        position: fixed;
        top: 0;
        right: -100%; /* 初始位置在屏幕外 */
        max-width: 380px; /* 最大宽度为380px */
        width: 90%; /* 初始宽度为90% */
        height: 100%; /* 窗口高度为浏览器视窗的 100% */
            box-sizing: border-box;
        background-color: white;
        z-index: 1001001;
        padding: 20px 0px;
        box-shadow: 0 0 10px rgba(0, 0, 0, 0.3);
        transition: right 0.3s, width 0.3s; /* 添加过渡效果 */
    }
    .rightbox_close {
        position: absolute;
        top: 10px;
        right: 10px;
        cursor: pointer;
    }
    #rightbox_iframe {
        width: 100%;
        height: 100%;
        border: none;
    }
</style>
</head>
<body>

<button onclick="openrightBoxpopup()">点击打开窗口</button>

<div class="rightbox_overlay" onclick="closerightBoxpopup()"></div>

<div class="rightBoxpopup" id="rightBoxpopup">
    <span class="rightbox_close" onclick="closerightBoxpopup()">X</span>
    <iframe id="rightbox_iframe" src="../serv/accounts_baseinfo.aspx"></iframe>
</div>

<script>
    function openrightBoxpopup() {
        var rightbox_overlay = document.querySelector('.rightbox_overlay');
        var rightBoxpopup = document.getElementById('rightBoxpopup');
        rightbox_overlay.style.display = 'block'; // 显示蒙版
        rightBoxpopup.style.right = '0'; // 显示弹出窗口
    }

    function closerightBoxpopup() {
        var rightbox_overlay = document.querySelector('.rightbox_overlay');
        var rightBoxpopup = document.getElementById('rightBoxpopup');
        rightbox_overlay.style.display = 'none'; // 隐藏蒙版
        rightBoxpopup.style.right = '-100%'; // 隐藏弹出窗口，移出屏幕
    }

    // 页面加载时隐藏右侧窗口
    document.addEventListener('DOMContentLoaded', function () {
        var rightBoxpopup = document.getElementById('rightBoxpopup');
        rightBoxpopup.style.right = '-100%';
    });
</script>

</body>
</html>

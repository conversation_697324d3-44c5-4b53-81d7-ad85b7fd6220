@media screen and (max-width:1680px){
	.signin.signup {padding: 115px 0px;}
	.signin.popup-in {padding: 133px 0;}
}/*max-width:1680px*/

@media screen and (max-width:1440px){
	.signin {padding: 138px 0;}
	.signin.signup {padding:40px 0px;}
	.signin.popup-in {padding:58px 0;}
}/*max-width:1440px*/

@media screen and (max-width:1370px){
	
}/*max-width:1370px*/

@media screen and (max-width:1200px){
	
}/*max-width:1200px*/

@media screen and (max-width:1199px){
	
}/*max-width:1199px*/
@media screen and (max-width:1100px){
    
}/*max-width:1100px*/
@media screen and (max-width:1024px){
	.signin {padding: 118px 0;}
	.signin.signup {padding:20px 0px;}
	.signin.popup-in {padding:38px 0;}
}/*max-width:1024px*/


@media screen and (max-width:992px){
    
}/*max-width:992px*/


@media screen and (max-width:991px){
	.modal-dialog {width:700px;}
}/*max-width:991px*/

@media screen and (max-width:768px){
    
}/*max-width:768px*/


@media screen and (max-width:767px){

	.btn.signin_btn {max-width: 200px;}
	.modal-dialog {width:auto;}

	.signin.signup .unstyled li:last-child {float: none;display: none;}

	.signin-footer {text-align: center;}
	
}/*max-width:767px*/

@media screen and (max-width: 660px){
	
}/*max-width:660px*/

@media screen and (max-width: 640px){

}/*max-width:640px*/

@media screen and (max-width: 580px){
	.modal-content .sign-content {max-width: 350px;margin: 0 auto;}
	.modal-content .single-forgot-password-modal-circle {float: none;}

	.popup-in-txt {max-width: 250px; margin: 0 auto;}
	.popup-in-txt  .unstyled li:last-child {float: none;}
}/*max-width:580px*/

@media screen and (max-width: 540px){
	
}/*max-width:540px*/

@media screen and (max-width: 480px){
	.signin-form .form-col,.signin-form .form-col1 {width: 100%;float: none;}
}/*max-width:480px*/

@media screen and (max-width: 440px){
	.single-forgot-password-circle,.single-forgot-password-modal-circle {float: left;}
	.modal-content .sign-content {max-width: 200px;margin: 0 auto;}
	.signin {padding: 60px 0;}
	.single-password-modal-circle input[type="radio"] + label {font-size: 14px;}
	.single-forgot-password-modal-circle.text-right a {font-size: 14px;}
	.unstyled li:last-child {float: left;}
}/*max-width:400px*/

@media screen and (max-width:350px){

}/*max-width:350px*/


@media screen and  (max-width: 320px){
	
	.popup-in-txt {max-width: 200px; margin: 0 auto;}
	
}/*max-width:320px*/


<%@ Page Title="用户列表" Language="C#" MasterPageFile="~/serv/m.master" AutoEventWireup="true" CodeFile="account_list.aspx.cs" Inherits="w_index" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="Server">
    <link href="../css/element-ui.css" rel="stylesheet" />
    <style>
        .el-tag.el-tag--test {
            background-color: #d5d5f5;
            border-color: #7d7dc7;
            color: #6d3333;
        }

        .button-group {
            display: flex;
            justify-content: left;
        }

        .button {
            display: inline-block;
            padding: 8px 20px;
            margin-right: 10px;
            font-size: 12px;
            text-align: center;
            text-decoration: none;
            color: #333;
            background-color: #eee;
            border-radius: 5px;
            transition: background-color 0.3s;
            border: 1px solid #eee;
        }


            .button:hover, .selected {
                background-color: #fff;
                color: #222!important;
                font-weight: bold;
                border: 1px solid #333;
            }
    </style>


    <script>
        var relationshop_query = function () {
            location.href = 'account_list.aspx?user=relationship&uid=' + $('#uid').val();
        }
    </script>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="Server">
    <div class="ibox float-e-margins">
        <div class="ibox-title">
            <h5><%= (Request.QueryString["user"] + "" == "relationship" ? "用户下级关系查询" : "用户列表") %></h5>
        </div>
        <div class="ibox-content">

            <%if (Request.QueryString["user"] + "" == "relationship")
              {
            %>

            <div class="row layui-form">
                <div class="col-md-12">
                    <div class="form-group form-buttom" style="position: relative;">
                        <input type="text" id="uid" class="form-control" placeholder="要搜索的用户ID/手机号码" style="padding-right: 80px;" value="<%=Request.QueryString["uid"] %>">
                        <a class="input_button" onclick="relationshop_query()">关系查询</a>
                    </div>
                </div>
            </div>

            <%
              } %>


            <div class="row layui-form">

                <div class="col-md-3">
                    <div class="form-group form-buttom" style="position: relative;">
                        <select id="levelid">
                            <option value="">用户等级</option>
                            <asp:Repeater runat="server" ID="levelLst">
                                <ItemTemplate>
                                    <option value="<%#Eval("id") %>"><%#Eval("name") %></option>
                                </ItemTemplate>
                            </asp:Repeater>
                        </select>
                    </div>
                </div>


                <div class="col-md-3">
                    <div class="form-group form-buttom" style="position: relative;">
                        <select id="groupid">
                            <option value="">账号分组</option>
                            <asp:Repeater runat="server" ID="groupLst">
                                <ItemTemplate>
                                    <option value="<%#Eval("id") %>"><%#Eval("name") %></option>
                                </ItemTemplate>
                            </asp:Repeater>
                        </select>
                    </div>
                </div>

                <div class="col-md-3">
                    <div class="form-group form-buttom" style="position: relative;">
                        <input type="text" id="award_filter" class="form-control" placeholder="奖励筛选">
                    </div>
                </div>


                <div class="col-md-3">
                    <div class="form-group form-buttom" style="position: relative;">
                        <input type="text" id="roomNumber" class="form-control" placeholder="房间号">
                    </div>
                </div>


            </div>
            <div class="row layui-form">


                <div class="col-md-3">
                    <div class="form-group form-buttom" style="position: relative;">
                        <input type="text" id="search-keyword" name="ordername" class="form-control" placeholder="请输入搜索的信息" style="padding-right: 50px;">
                        <a class="input_button" onclick="searchList()">查询</a>
                    </div>
                </div>


                <div class="col-md-3">
                    <div class="form-group form-buttom" id="twoInputs">
                        <input type="text" id="start_time" name="ordername" class="form-control" placeholder="开始时间" style="width: 48%; display: inline-block;" value="" autocomplete="off">
                        <input type="text" id="end_time" name="ordername" class="form-control" placeholder="结束时间" style="width: 48%; display: inline-block;" autocomplete="off">

                        <script>
                            $(document).ready(function () {
                                $('#start_time').datepicker({
                                    format: 'yyyy-mm-dd',
                                    autoclose: true
                                });

                                $('#end_time').datepicker({
                                    format: 'yyyy-mm-dd',
                                    autoclose: true
                                });
                            });
                        </script>
                    </div>
                </div>


                <div class="col-md-6">
                    <div class="form-group form-buttom">
                        <a class="btn btn-success" onclick="javascript:location.href=location.href;">
                            <svg t="1688237949108" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2211" width="16" height="16">
                                <path d="M512 192a298.24 298.24 0 0 0-245.162667 128 42.666667 42.666667 0 1 1-69.973333-48.810667 384.042667 384.042667 0 0 1 697.173333 180.48l36.608-34.645333a42.666667 42.666667 0 0 1 58.709334 61.952l-112.64 106.666667a42.666667 42.666667 0 0 1-71.253334-38.912A298.666667 298.666667 0 0 0 512 192z m0.170667 640a298.24 298.24 0 0 0 245.162666-128 42.666667 42.666667 0 1 1 69.973334 48.810667 384.042667 384.042667 0 0 1-697.173334-180.48l-36.650666 34.645333a42.666667 42.666667 0 0 1-58.666667-61.952l112.64-106.666667a42.666667 42.666667 0 0 1 71.253333 38.912 298.666667 298.666667 0 0 0 293.418667 354.730667z" fill="#14101C" p-id="2212"></path></svg>
                            刷新</a>

                        <a class="btn btn-success" onclick="opennew()">
                            <svg t="1688237854283" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="15169" width="16" height="16">
                                <path d="M725.333333 213.333333H298.666667a85.333333 85.333333 0 0 0-85.333334 85.333334v341.333333a42.666667 42.666667 0 1 1-85.333333 0V298.666667a170.666667 170.666667 0 0 1 170.666667-170.666667h426.666666a170.709333 170.709333 0 0 1 167.850667 139.733333v488.533334A170.709333 170.709333 0 0 1 725.333333 896H298.666667a170.538667 170.538667 0 0 1-144.469334-79.744l1.194667-0.682667a42.666667 42.666667 0 0 1 76.501333-37.034666c15.658667 19.584 39.765333 32.128 66.773334 32.128h426.666666a85.333333 85.333333 0 0 0 85.333334-85.333334V298.666667a85.333333 85.333333 0 0 0-85.333334-85.333334z m-213.333333 512a42.666667 42.666667 0 0 1-42.666667-42.666666v-128H341.333333a42.666667 42.666667 0 1 1 0-85.333334h128V341.333333a42.666667 42.666667 0 1 1 85.333334 0v128h128a42.666667 42.666667 0 1 1 0 85.333334h-128v128a42.666667 42.666667 0 0 1-42.666667 42.666666z" fill="#14101C" p-id="15170"></path></svg>&nbsp;添加</a>

                        <a class="btn btn-success" onclick="recharge_forusers()">
                            <svg t="" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="22140" width="16" height="16" style="position: relative; top: 3px;">
                                <path d="M739.1744 155.1872a425.02144 425.02144 0 0 1 65.4848 227.1744c0 235.8272-191.1808 427.0592-427.0592 427.0592-84.224 0-162.6624-24.4736-228.8128-66.56 75.5712 120.064 209.2032 199.8848 361.5744 199.8848 235.8272 0 427.0592-191.1808 427.0592-427.0592 0-151.6544-79.1552-284.7232-198.2464-360.4992z" fill="#ffa115" p-id="22141"></path><path d="M510.3616 973.4656c-252.416 0-457.7792-205.3632-457.7792-457.7792s205.3632-457.7792 457.7792-457.7792 457.7792 205.3632 457.7792 457.7792-205.3632 457.7792-457.7792 457.7792z m0-854.1184c-218.5216 0-396.3392 177.7664-396.3392 396.3392S291.84 912.0256 510.3616 912.0256s396.3392-177.7664 396.3392-396.3392-177.8176-396.3392-396.3392-396.3392z" fill="#474A54" p-id="22142"></path><path d="M655.6672 585.216h-116.6336v-49.9712h116.6336c16.9472 0 30.72-13.7728 30.72-30.72s-13.7728-30.72-30.72-30.72h-80.3328l102.3488-104.96c11.8272-12.1344 11.6224-31.5904-0.5632-43.4176-12.1344-11.8272-31.5904-11.6224-43.4176 0.5632L508.3136 454.5536 382.9248 325.9392a30.72 30.72 0 0 0-43.4176-0.5632 30.72 30.72 0 0 0-0.5632 43.4176l102.3488 104.96H360.96c-16.9472 0-30.72 13.7728-30.72 30.72s13.7728 30.72 30.72 30.72h116.6336v49.9712H360.96c-16.9472 0-30.72 13.7728-30.72 30.72s13.7728 30.72 30.72 30.72h116.6336v84.6336c0 16.9472 13.7728 30.72 30.72 30.72s30.72-13.7728 30.72-30.72V646.656h116.6336c16.9472 0 30.72-13.7728 30.72-30.72s-13.7728-30.72-30.72-30.72z" fill="#474A54" p-id="22143"></path></svg>&nbsp;批量充值</a>
                        <a class="btn btn-success" onclick="changePassword_forusers()">
                            <svg t="" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="9676" width="16" height="16" style="position: relative; top: 3px;">
                                <path d="M338.933902 615.285667a311.56387 311.56387 0 0 1-54.321654-4.938332 307.523416 307.523416 0 1 1 305.727659-479.018226 307.523416 307.523416 0 0 1-251.406005 483.956558z m0-548.154877a240.631461 240.631461 0 1 0 197.533289 102.807099 240.631461 240.631461 0 0 0-198.880106-102.807099z" fill="#515151" p-id="9677"></path><path d="M445.691666 534.65617l62.178093-43.457323 342.136635 489.343829-62.178092 43.457324z" fill="#515151" p-id="9678"></path><path d="M602.999995 760.023696l178.857414-124.9847 43.457324 62.178092-178.857415 124.9847zM747.109508 957.73656l202.381833-141.415877 43.457324 62.178092-202.381834 141.415877z" fill="#515151" p-id="9679"></path><path d="M270.181383 210.13663m-51.87802 36.271432a63.30044 63.30044 0 1 0 103.75604-72.542865 63.30044 63.30044 0 1 0-103.75604 72.542865Z" fill="#515151" p-id="9680"></path></svg>&nbsp;批量改密</a>
                        <a class="btn btn-success" onclick="adjust_forusers()">
                            <svg t="" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="9676" width="16" height="16" style="position: relative; top: 3px;">
                                <path d="M338.933902 615.285667a311.56387 311.56387 0 0 1-54.321654-4.938332 307.523416 307.523416 0 1 1 305.727659-479.018226 307.523416 307.523416 0 0 1-251.406005 483.956558z m0-548.154877a240.631461 240.631461 0 1 0 197.533289 102.807099 240.631461 240.631461 0 0 0-198.880106-102.807099z" fill="#515151" p-id="9677"></path><path d="M445.691666 534.65617l62.178093-43.457323 342.136635 489.343829-62.178092 43.457324z" fill="#515151" p-id="9678"></path><path d="M602.999995 760.023696l178.857414-124.9847 43.457324 62.178092-178.857415 124.9847zM747.109508 957.73656l202.381833-141.415877 43.457324 62.178092-202.381834 141.415877z" fill="#515151" p-id="9679"></path><path d="M270.181383 210.13663m-51.87802 36.271432a63.30044 63.30044 0 1 0 103.75604-72.542865 63.30044 63.30044 0 1 0-103.75604 72.542865Z" fill="#515151" p-id="9680"></path></svg>&nbsp;批量调整</a>

                        <%if (Request.QueryString["group"] + "" == "1")
                          {
                        %>
                        <a class="btn btn-success" onclick="clear_money()">
                            <svg t="1700320287421" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="3963" width="16" height="16">
                                <path d="M274.5344 582.0928h480.9728v265.0624H274.5344z" fill="#D9FFEC" p-id="3964"></path><path d="M852.6336 539.9552H177.408c-43.9296 0-79.6672-35.7376-79.6672-79.6672V335.8208c0-43.9296 35.7376-79.6672 79.6672-79.6672h159.8976V175.7184c0-48.5376 39.4752-88.064 88.064-88.064h179.3536c48.5376 0 88.064 39.4752 88.064 88.064v80.4864h159.8976c43.9296 0 79.6672 35.7376 79.6672 79.6672v124.5184c-0.0512 43.8784-35.7888 79.5648-79.7184 79.5648z m-672.9728-81.92h670.72V338.1248H651.776c-22.6304 0-40.96-18.3296-40.96-40.96V175.7184c0-3.3792-2.7648-6.144-6.144-6.144H425.3696c-3.3792 0-6.144 2.7648-6.144 6.144v121.4464c0 22.6304-18.3296 40.96-40.96 40.96H179.6608v119.9104z" fill="#34333A" p-id="3965"></path><path d="M390.5536 889.9072c-22.6304 0-40.96-18.3296-40.96-40.96v-173.6704c0-22.6304 18.3296-40.96 40.96-40.96s40.96 18.3296 40.96 40.96v173.6704c0 22.6304-18.3296 40.96-40.96 40.96z" fill="#3AD285" p-id="3966"></path><path d="M797.7472 932.1472H232.2944c-53.1456 0-96.3584-43.2128-96.3584-96.3584v-255.9488h81.92v255.9488c0 7.9872 6.5024 14.4384 14.4384 14.4384H797.696c7.9872 0 14.4384-6.5024 14.4384-14.4384v-255.9488h81.92v255.9488c0.0512 53.0944-43.1616 96.3584-96.3072 96.3584z" fill="#34333A" p-id="3967"></path></svg>&nbsp;清除余额</a>
                        <%
                          } %>
                    </div>
                </div>



            </div>


            <%--<div style="margin-bottom:10px;">
                <input type="checkbox" id="filter_data" />&nbsp;只看在线
            </div>--%>


            <div style="margin-bottom: 10px;">
                <input type="checkbox" id="filter_active" />&nbsp;按活跃度排序
            </div>


            <div class="table-responsive">
                <table class="table">
                    <thead>
                        <tr role="head">
                            <%--<th sort="true">
                                <input type="checkbox" onclick="javascript: select_all(this)" />
                                #ID</th>--%>
                            <%--<th sort="true" class=""></th>--%>
                            <th sort="true" class="">
                                <input type="checkbox" onclick="javascript: select_all(this)" />房间</th>
                            <th sort="true" class="">分组</th>
                            <th sort="true" class="">姓名</th>
                            <%--<th sort="true" class="">银行卡</th>--%>
                            <%--<th sort="true" class="">当日出借次数</th>--%>
                            <%--<th sort="true" class="">当日出借额度</th>--%>
                            <th sort="true" class="">当日可抢</th>
                            <th sort="true" class="">当日额度</th>
                            <th sort="true" class="">等级</th>
                            <th sort="true" class="">下级有效</th>
                            <th sort="true" class="">受邀人</th>
                            <%--<th sort="true" class="">邀请人</th>--%>
                            <th sort="true" class="">游戏消耗</th>
                            <th sort="true" class="">状态</th>
                            <th sort="true" class="">挂单</th>
                            <%--<th sort="true" class="">超时返还</th>--%>
                            <th sort="true" class="">今日</th>
                            <th sort="true" class="">历史</th>
                            <th sort="true" class="">余额</th>
                            <th sort="true" class="">挂单盈利</th>
                            <th sort="true" class="">体验金</th>
                            <th sort="true" class="">今日卖币</th>
                            <th sort="true" class="">历史卖币</th>
                            <th sort="true" class="">今日任务</th>
                            <th sort="true" class="">历史任务</th>
                            <th sort="true" class="">充提差</th>
                            <th sort="true" class="">已交易</th>
                            <th sort="true" class="">冻结</th>
                            <%--           <th sort="true" class="">奖励</th>
                            <th sort="true" class="">任务奖励</th>
                            <th sort="true" class="">转盘奖励</th>
                            <th sort="true" class="">活动充值</th>
                            <th sort="true" class="">红包金额</th>
                            <th sort="true" class="">佣金（含未领）</th>
                            <th sort="true" class="">未领佣</th>
                            <th sort="true" class="">游戏佣金</th>
                            <th sort="true" class="">利息</th>--%>
                            <th sort="true" class="">总和</th>
                            <th sort="true" class="">最后登录</th>
                            <th sort="true" class="">注册时间</th>
                            <th sort="true"></th>
                        </tr>
                    </thead>
                    <tbody id="list-display">
                    </tbody>
                </table>
            </div>


            <div class="layui-table-page">
                <div id="layui-table-page1">
                    <div class="layui-box layui-laypage layui-laypage-default" id="layui-laypage-1">
                        <div style="display: inline-block;" class="cpages">
                            <!--page grow -->
                        </div>
                        <span class="layui-laypage-skip">到第<input type="text" min="1" value="1" class="layui-input target-page" />页
                            <button type="button" class="layui-laypage-btn" onclick="javascript:pager_index=$('.target-page').val()-1;getPager();">确定</button></span>
                        <span class="layui-laypage-count">共 <span class="page_count"></span>条</span>
                        <span class="layui-laypage-limits">
                            <select class="s_pages" lay-ignore="" onchange="javascript:pager_size=$(this).val();getPager();">
                                <option value="10">10 条/页</option>
                                <option value="20" selected="">20 条/页</option>
                                <option value="30">30 条/页</option>
                                <option value="50">50 条/页</option>
                                <option value="100">100 条/页</option>
                                <option value="200">200 条/页</option>
                            </select></span>
                    </div>
                </div>
            </div>

        </div>
    </div>
    <script>
        var _action = 'accounts';
        var _actionName = '用户';
    </script>



    <script id="account_groups" type="text/javascript">
        <%=ToJson(chelper.gdt("account_groups")) %>
    </script>
    <script id="levels_list" type="text/javascript">
        <%=ToJson(SortDataTable(chelper.gdt("levels_list"),"minNumber ASC")) %>
    </script>
    <script>

        var temp_list = JSON.parse($('#account_groups').html());
        var account_groups = [['', '无']];
        for (var i = 0; i < temp_list.length; i++) {
            account_groups.push([temp_list[i].id, temp_list[i].name]);
        }

        temp_list = JSON.parse($('#levels_list').html());
        var levels_list = [['', '无']];
        for (var i = 0; i < temp_list.length; i++) {
            levels_list.push([temp_list[i].id, temp_list[i].name]);
        }



        function opennew() {
            var modify_model = [];

            modify_model.push({ name: '手机号列表', id: 'userlist', value: '', type: 'textarea', height: 100 });
            modify_model.push({ name: '上级邀请码', id: 'parent_code', value: '' });
            modify_model.push({ name: '用户分组', id: 'groupid', value: '', type: 'select', data: account_groups });
            modify_model.push({ name: '生成数量', id: 'random_number', value: '1' });
            modify_model.push({ name: '登录密码', id: 'password', value: '随机' });
            modify_model.push({ name: '支付密码', id: 'secure_password', value: '随机' });
            modify_model.push({ name: '绑卡-姓名', id: 'name', value: '', type: 'hidden' });
            modify_model.push({ name: '绑卡-卡号', id: 'bankid', value: '', type: 'hidden' });
            modify_model.push({ name: '绑卡-银行', id: 'bankname', value: '', type: 'hidden' });
            modify_model.push({ name: '绑卡-城市', id: 'bankaddr', value: '', type: 'hidden' });
            modify_model.push({ name: '账号类型', id: 'usertype', value: 0, type: 'option', data: [[2, '高级用户'], [1, '普通用户'], [0, '新注册用户']] });
            modify_model.push({ name: '创建模式', id: 'createtype', value: 0, type: 'option', data: [[0, '默认'], [1, '数据账号']] });
            modify_model.push({ name: '团队人数', id: 'param_teamnum', value: '' });
            modify_model.push({ name: '团队总收入', id: 'param_teamincome', value: '' });
            modify_model.push({ name: '直属下级人数', id: 'param_invitenum', value: '' });
            modify_model.push({ name: '直属一键佣金', id: 'param_agentbork', value: '' });

            edit({ aname: 'admin', title: '添加' + _actionName, action: _action, id: '', data: modify_model });
        }




        function recharge_forusers() {
            var modify_model = [];

            modify_model.push({ name: '', id: 'action_type', value: 'recharge_forusers', type: 'hidden' });
            modify_model.push({ name: '手机号列表', id: 'userlist', value: '', type: 'textarea' });
            //modify_model.push({ name: '充值类型', id: 'recharge_type', type: 'option', value: '买币', data: [["买币", "买币"], ["刷单", "刷单"], ["刷单奖励", "刷单奖励"], ["合伙人佣金", "合伙人佣金"], ["转账", "转账"], ["活动充值", "活动充值"]] });
            modify_model.push({ name: '充值类型', id: 'recharge_type', type: 'option', value: '买币', data: [["活动充值", "活动充值"]] });
            modify_model.push({ name: '充值说明', id: 'name', value: '活动奖励' });
            modify_model.push({ name: '余额变动', id: 'amount', value: '' });
            modify_model.push({ name: '验证码', id: 'actCode', value: '' });
            modify_model.push({ name: '操作方式', id: 'type', value: 0, type: 'option', data: [[0, '充值']] });
            modify_model.push({ name: '充值备注', id: 'recharge_remark', value: '' });

            edit({ aname: 'admin', title: '批量充值' + _actionName, action: _action, id: '', data: modify_model });
        }

        function changePassword_forusers() {
            var modify_model = [];

            modify_model.push({ name: '', id: 'action_type', value: 'changePassword_forusers', type: 'hidden' });
            modify_model.push({ name: '手机号列表', id: 'userlist', value: '', type: 'textarea' });
            modify_model.push({ name: '密码', id: 'pwd', value: '' });
            modify_model.push({ name: '验证码', id: 'actCode', value: '' });

            edit({ aname: 'admin', title: '批量改密' + _actionName, action: _action, id: '', data: modify_model });
        }

        function adjust_forusers() {
            var modify_model = [];

            modify_model.push({ name: '', id: 'action_type', value: 'adjust_forusers', type: 'hidden' });
            modify_model.push({ name: '手机号列表', id: 'userlist', value: '', type: 'textarea' });
            modify_model.push({ name: '账号类型', id: 'usertype', value: 1, type: 'option', data: [[2, '高级用户'], [1, '普通用户'], [0, '新注册用户']] });
            //modify_model.push({ name: '验证码', id: 'actCode', value: '' });

            edit({ aname: 'admin', title: '批量调整' + _actionName, action: _action, id: '', data: modify_model });
        }

        function clear_money() {
            var modify_model = [];

            modify_model.push({ name: '', id: 'action_type', value: 'clear_money', type: 'hidden' });
            modify_model.push({ name: '清除说明', id: 'recharge_type', type: 'option', value: '转账', data: [["转账", "转账"], ["刷单", "刷单"], ["活动充值", "活动充值"]] });
            modify_model.push({ name: '清除详情', id: 'name', value: '' });
            modify_model.push({ name: '验证码', id: 'actCode', value: '' });

            edit({ aname: 'admin', title: '清除托号余额', action: _action, id: '', data: modify_model });
        }
    </script>
    <style>
        .gradient-status-item {
            position: relative;
            display: inline-block;
            background: #1ea71e;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            margin-right: 3px;
            background: #1ea71e; /* 绿色背景 */
            margin-left: 8px;
        }

        .gradient-offline {
            background: gray;
        }

        .gradient-online {
            background: #1ea71e; /* 绿色背景 */
            animation: brightAndDim 0.8s linear infinite alternate;
        }

        @keyframes brightAndDim {
            0% {
                opacity: 0.5; /* 开始时变暗 */
            }

            100% {
                opacity: 1; /* 结束时变亮 */
            }
        }

        .card-button {
            padding: 0px 8px;
            background: #efefef!important;
        }
    </style>

    <script>
        var set_online = function (online) {
            var online_html = '<span class="gradient-status-item gradient-online"></span><span style="color: #1ea71e;">在线</span>';
            if (online != "在线") {
                online_html = '<span class="gradient-status-item gradient-offline"></span><span style="color: gray;" title="最后在线时间 ' + online + '">离线</span>';
            }
            return online_html;
        }
    </script>

    <script>

        var pager_size = $(".s_pages").val();
        var pager_index = 0;
        var pager_key = "";

        $("#pagesize").val(pager_key);
        function setPageSize(obj) {
            pager_index = 0;
            pager_key = $("#search-keyword").val();
            opt = $(obj).val();
            getPager();
        }

        function searchList() {
            opt = $("#opt1").val();
            pager_index = 0;
            pager_key = $("#search-keyword").val();
            getPager();
        }

        function getParam() {

            var params = { limit: pager_size, p: pager_index, page: _action, keyword: pager_key, audit_state: $("#audit_state").val(), settle_state: $("#settle_state").val(), start_time: $("#start_time").val(), end_time: $("#end_time").val(), register_from: get_param('rv'), groupid: $("#groupid").val(), filter_data: "<%=Request.QueryString["online"] %>", uid: $('#uid').val(), roomNumber: $('#roomNumber').val(), award_filter: $('#award_filter').val(), filter_active: $('#filter_active').prop('checked'), levelid: $('#levelid').val() };

            //将请求URL的参数加入到params中
            var urlParams = new URLSearchParams(window.location.search);
            urlParams.forEach(function (value, key) {
                if (!(key in params)) {
                    params[key] = value;
                }
            });

            return params
        }


        function getPager() {

            $.ajax({
                type: "POST",
                url: "../api/admin.aspx?do=page",
                data: getParam(),
                datatype: "json",
                success: function (json) {
                    $("#list-display").html("");
                    if (json.code == 1) {
                        var data = json.data.list;
                        for (var i = 0; i < data.length; i++) {
                            var tr = "";



                            var bankname = "";
                            var bankcity = "";
                            if (data[i].payment_bankname != "") {
                                var temp_array = data[i].payment_bankname.split(' ');
                                if (temp_array.length == 2) {

                                    bankname = temp_array[0];
                                    bankcity = temp_array[1];
                                } else {
                                    bankname = data[i].payment_bankname;
                                }
                            }


                            var modify_model = new Array();

                            modify_model.push({ name: '验证码', id: 'actCode', value: '' });
                            modify_model.push({ name: '用户等级', id: 'level', value: '', type: 'select', data: levels_list, value: data[i].levelid });
                            modify_model.push({ name: '分组', id: 'groupid', type: 'select', value: data[i].groupid, data: account_groups });
                            modify_model.push({ name: '手机号', id: 'phone', value: data[i].phone, type: 'disabled' });
                            modify_model.push({ name: '密码', id: 'password', value: data[i].password });
                            modify_model.push({ name: '支付宝姓名', id: 'alipay_name', value: data[i].alipay_name });
                            modify_model.push({ name: '支付宝账号', id: 'alipay_account', value: data[i].alipay_account });
                            modify_model.push({ name: '姓名', id: 'payment_name', value: data[i].payment_name });
                            modify_model.push({ name: '银行卡号', id: 'payment_bankid', value: data[i].payment_bankid });
                            modify_model.push({ name: '开户行', id: 'bankname', value: bankname });
                            modify_model.push({ name: '开户地址', id: 'bankcity', value: bankcity });
                            modify_model.push({ name: '新增次数', id: 'daily_number', value: data[i].daily_number });
                            modify_model.push({ name: '新增额度', id: 'daily_amount', value: data[i].daily_amount });
                            modify_model.push({ name: '出借次数', id: 'daily_lend_count', value: data[i].daily_lend_count });
                            //modify_model.push({ name: '新增出借额度', id: 'daily_lend_amount', value: data[i].daily_lend_amount });
                            modify_model.push({ name: '出借额度', id: 'lend_quota', value: data[i].lend_quota });
                            modify_model.push({ name: '超时返还', id: 'timeout_second', value: data[i].timeout_second });
                            modify_model.push({ name: '账号状态', id: 'state', value: data[i].state, type: 'option', data: [[1, '正常使用'], [-1, '冻结']] });
                            //modify_model.push({ name: '出借状态', id: 'lend_state', value: data[i].lend_state, type: 'option', data: [[0, '默认'], [1, '免审核'], [-1, '需要审核']] });
                            modify_model.push({ name: '出借状态', id: 'lend_state', value: -1, type: 'hidden', data: [[0, '默认'], [1, '免审核'], [-1, '需要审核']] });
                            //modify_model.push({ name: '免审成功率', id: 'succ_rate', value: (data[i].succ_rate == "" ? '100~100' : data[i].succ_rate) });
                            modify_model.push({ name: '任务审核', id: 'task_state', value: data[i].task_state, type: 'option', data: [[0, '默认'], [1, '免审核'], [-1, '需要审核']] });

                            modify_model.push({ name: '账号类型', id: 'usertype', value: data[i].usertype, type: 'option', data: [[2, '高级用户'], [1, '普通用户'], [0, '新注册用户']] });
                            modify_model.push({ name: '转盘赠送', id: 'zp_number', value: data[i].zp_number });
                            modify_model.push({ name: '禁止挂单', id: 'limit_ontouch', value: data[i].limit_ontouch, type: 'option', data: [[1, '禁止'], [0, '允许']] });
                            modify_model.push({ name: '最低出借', id: 'min_lendamount', value: data[i].min_lendamount });
                            modify_model.push({ name: '游戏标记', id: 'game_remark', value: data[i].game_remark, type: 'option', data: [['', '无'], ['DF', 'DF']] });



                            var modify_level = new Array();
                            modify_level.push({ name: '', id: 'action_type', value: 'modify_level', type: 'hidden' });
                            //modify_level.push({ name: '分组', id: 'groupid', type: 'select', value: data[i].groupid, data: account_groups });
                            modify_level.push({ name: '分组', id: 'groupid', value: data[i].groupid, type: 'hidden' });
                            modify_level.push({ name: '账号类型', id: 'usertype', value: data[i].usertype, type: 'option', data: [[2, '高级用户'], [1, '普通用户'], [0, '新注册用户']] });

                            var point_model = new Array();
                            point_model.push({ name: '', id: 'typename', value: data[i].typename, type: 'hidden' });
                            point_model.push({ name: '', id: 'action_type', value: 'user_recharge', type: 'hidden' });
                            point_model.push({ name: '充值说明', id: 'recharge_type', type: 'option', value: '买币', data: [["买币", "买币"], ["刷单", "刷单"], ["刷单奖励", "刷单奖励"], ["合伙人佣金", "合伙人佣金"], ["转账", "转账"], ["活动充值", "活动充值"]] });
                            point_model.push({ name: '充值说明', id: 'name', value: '系统操作' });
                            point_model.push({ name: '余额变动', id: 'amount', value: '0' });
                            //point_model.push({ name: '验证码', id: 'actCode', value: '' });
                            point_model.push({ name: '操作方式', id: 'type', value: data[i].register_from, type: 'option', data: [[0, '充值'], [-1, '清空【确认后再操作！！】'], [9, '冻结'], [-9, '全冻【确认后再操作！！】']] });
                            point_model.push({ name: '充值备注', id: 'recharge_remark', value: '' });
                            point_model.push({ name: '验证码', id: 'actCode', value: '' });


                            var sms_model = new Array();

                            sms_model.push({ name: '', id: 'action_type', value: 'send_sms', type: 'hidden' });
                            sms_model.push({ name: '', id: 'phone', value: data[i].phone, type: 'hidden' });
                            sms_model.push({ name: '验证码', id: 'code', value: '随机' });


                            var modify_userdata = new Array();
                            modify_userdata.push({ name: '', id: 'action_type', value: 'modify_userdata', type: 'hidden' });

                            var datalist = ["teamnum", "teamincome", "invitenum", "agentbork"];
                            var dataname = ["团队人数", "团队总收入", "直属下级人数", "直属一键佣金"];
                            for (var tt = 0; tt < datalist.length; tt++) {
                                modify_userdata.push({ name: dataname[tt], id: 'param_' + datalist[tt], value: data[i]["data_" + datalist[tt]] + "," + data[i]["grow_" + datalist[tt] + "_min"] + "-" + data[i]["grow_" + datalist[tt] + "_max"] });
                            }



                            var modify_datas = new Array();
                            modify_datas.push({ name: '', id: 'action_type', value: 'modify_datas', type: 'hidden' });
                            modify_datas.push({ name: '任务成功率', id: 'succ_rate', value: data[i].succ_rate });



                            tr += "<tr style='background:#efefef;'>";
                            tr += "<td colspan='8' style=' padding:2px  8px;position: sticky;left:0;'>"

                                + '<div style="display: flex;align-items: center;">   '

                                + '<input class="good_select" type="checkbox" data-id="' + data[i].id + '" id="checkid_' + data[i].id + '" />'
                            + '<a  ondblclick="openrightBoxpopup(\'accounts_baseinfo.aspx?id=' + data[i].id + '\')" >' + data[i].parent_code + '<span style="color: #b53d10;margin-left: 2px;margin-right:3px;">[' + data[i].id + ']</span></a>'

                                + (('<span style="background: #f3ecec;color: #956b0a;font-size: 12px;border-radius: 3px;padding: 1px 4px;margin-right: 5px;font-weight: bold;border: 1px solid #ccc;">' + (data[i].typename == 'th' ? '托号' : data[i].typename == 'user' ? '普通' : data[i].typename == 'super' ? '高级' : data[i].typename == 'reg' ? '新' : '其他') + '</span>')) 

                                +(data[i].remarkuser == 1 ? '<span style="background: #c7f1e2;color: #489f92;font-size: 12px;border-radius: 3px;padding: 1px 4px;margin-right: 5px;font-weight: bold;border: 1px solid #6addb4;">标</span>' : '')

                                +(get_param('user') == 'relationship' ? data[i].phone : '')

                                + (data[i].relationshop_name == '' ? '' : (data[i].relationshop_name == '直接' ? '<div style="border-radius: 10px;font-size: 12px;font-weight: bold;margin-top: 4px;display: inline-block;padding: 0 5px;background: #61736b;color: yellow;margin-left: 6px;">直属</div>' : data[i].relationshop_name == '间接' ? '<div style="border-radius: 10px;font-size: 12px;font-weight: bold;margin-top: 4px;display: inline-block;padding: 0 5px;background: #e9e9e9;color: #535b57;margin-left: 6px;">间接</div>' : '<div style="border-radius: 10px;font-size: 12px;font-weight: bold;margin-top: 4px;display: inline-block;padding: 0 5px;background: #212020;color: #c3ffaf;margin-left: 6px;">' + data[i].relationshop_name + '</div>') + ('<i class="" style="background: rgb(43 30 22 / 10%);color: #a339e3;white-space: nowrap;border: 1px solid transparent;border-radius: 0.28571429em;font-style: normal;margin-left: 6px;padding: 0 5px;font-weight: bold;">' + data[i].level + '</i>'))

                                + (data[i].createtype == '1' ? '<span style="background: linear-gradient(130deg, #a1f7b3, #ffd589);color: #374d65;padding: 1px 10px;border-radius: 3px;margin-right: 3px;font-weight: bold;display: flex;cursor:pointer;" ' + "onclick='edit(" + edtext({ aname: 'admin', title: _actionName + "数据调整", action: _action, id: data[i].id, data: modify_userdata }) + ")'" + '><svg t="" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="3629" width="16" height="16" style="margin-right: 1px;"><path d="M472.592 166.096l-27.36 70.992a32 32 0 0 1-20.112 18.976 281.568 281.568 0 0 0-68.32 32.544 32 32 0 0 1-27.168 3.744l-73.6-23.248a358.688 358.688 0 0 0-48.864 60.352l39.008 65.184a32 32 0 0 1 2.448 27.824 272.496 272.496 0 0 0-16.768 72.8 32 32 0 0 1-14.272 23.888l-64 42.144c3.04 25.968 8.912 51.056 17.28 75.008l76.48 10.592a32 32 0 0 1 22.992 15.12 279.44 279.44 0 0 0 47.248 58.464 32 32 0 0 1 9.92 26.032l-6.528 75.584a361.36 361.36 0 0 0 70.816 33.696l56.128-52.512a32 32 0 0 1 26.08-8.352 287.808 287.808 0 0 0 76 0 32 32 0 0 1 26.08 8.352l56.128 52.512a361.328 361.328 0 0 0 70.816-33.696l-6.528-75.584a32 32 0 0 1 9.92-26.032 279.472 279.472 0 0 0 47.248-58.464 32 32 0 0 1 22.992-15.12l76.48-10.592a349.76 349.76 0 0 0 17.28-75.008l-64-42.144a32 32 0 0 1-14.272-23.872 272.56 272.56 0 0 0-16.768-72.816 32 32 0 0 1 2.448-27.84l39.008-65.168a358.592 358.592 0 0 0-48.864-60.352l-73.6 23.248a32 32 0 0 1-27.152-3.744 281.6 281.6 0 0 0-68.336-32.544 32 32 0 0 1-20.112-18.976l-27.36-70.992a370.4 370.4 0 0 0-78.816 0z m-28.032-60.832A432.464 432.464 0 0 1 512 100c22.928 0 45.44 1.792 67.44 5.28a32 32 0 0 1 24.864 20.096l28.832 74.832a345.472 345.472 0 0 1 56 26.672l77.488-24.48a32 32 0 0 1 31.024 6.72 422.48 422.48 0 0 1 84.32 104.32 32 32 0 0 1-0.32 32.336l-41.184 68.8c6.272 19.12 10.88 38.96 13.664 59.36l67.472 44.448a32 32 0 0 1 14.336 28.784 414.016 414.016 0 0 1-30.016 130.352 32 32 0 0 1-25.232 19.568l-80.608 11.168a343.472 343.472 0 0 1-38.528 47.696l6.88 79.712a32 32 0 0 1-13.728 29.088 425.2 425.2 0 0 1-121.616 57.952 32 32 0 0 1-30.88-7.344l-59.104-55.296a353.328 353.328 0 0 1-62.208 0l-59.104 55.296a32 32 0 0 1-30.88 7.344 425.28 425.28 0 0 1-121.632-57.952 32 32 0 0 1-13.712-29.088l6.88-79.712a343.36 343.36 0 0 1-38.528-47.68l-80.608-11.184a32 32 0 0 1-25.232-19.568 414.016 414.016 0 0 1-30.016-130.352 32 32 0 0 1 14.336-28.8l67.472-44.432c2.784-20.4 7.392-40.24 13.664-59.36l-41.184-68.8a32 32 0 0 1-0.32-32.32 422.56 422.56 0 0 1 84.32-104.32 32 32 0 0 1 31.04-6.72l77.488 24.48a345.728 345.728 0 0 1 55.984-26.688l28.832-74.832a32 32 0 0 1 24.864-20.112z m73.216 284.672c-70.56 0-127.104 56.4-127.104 125.168 0 68.752 56.528 125.168 127.104 125.168s127.12-56.416 127.12-125.168c0-68.768-56.544-125.168-127.12-125.168z m-191.104 125.168c0-104.848 85.92-189.168 191.104-189.168 105.184 0 191.12 84.32 191.12 189.168 0 104.832-85.936 189.168-191.12 189.168-105.168 0-191.104-84.32-191.104-189.168z" fill="#374d65" p-id="3630"></path></svg>数据号</span>' : '')

                                + (get_param('audit') == 1 && data[i].upgrade_state == 1 ? '' : (
                                //这里是非审核出现的

                                 '<a class="card-button common-button" ' + "onclick=\"" + "get_action({name:'edit_create',data:{id:" + data[i].id + ",_app:'" + _action + "',action_type:'recharge_expamount',islock:1},inputs:[{id:'amount',name:'充值金额',value:''},{id:'day',name:'充值天数',value:''},{id:'remark',name:'充值备注',value:''}],n:'体验金充值',e:'体验金充值到账户  " + data[i].phone + "'});" + "\"" + '  style="color:#775818!important;"><svg t="" class="limit_color" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="12113" width="12" height="12"><path d="M512 485.74359m-459.487179 0a459.487179 459.487179 0 1 0 918.974358 0 459.487179 459.487179 0 1 0-918.974358 0Z" fill="#FFCD30" p-id="12114"></path><path d="M812.635897 504.123077c13.128205 208.738462-148.348718 357.087179-334.76923 361.025641v-45.948718c-177.230769 0-325.579487-135.220513-334.769231-316.389744h39.384615c9.189744 154.912821 139.158974 277.005128 295.384616 278.317949v-38.071795c-135.220513 0-246.810256-106.338462-254.68718-240.246154h40.697436c6.564103 112.902564 101.087179 203.487179 213.989744 203.48718v-39.384615c-91.897436 0-169.353846-72.205128-173.292308-164.102565h508.061538z m78.769231 3.938461c18.379487 18.379487 18.379487 47.261538 0 66.953847l-78.769231-70.892308 78.769231-70.892308c18.379487 18.379487 18.379487 47.261538 0 66.953846l-2.625641 2.625641c-1.312821 1.312821-1.312821 1.312821-2.625641 1.312821 1.312821 0 1.312821 1.312821 2.625641 1.31282 1.312821 1.312821 2.625641 1.312821 2.625641 2.625641z m-430.605128 98.461539c10.502564 0 18.379487-7.876923 18.379487-18.379487 0-10.502564-7.876923-18.379487-18.379487-18.379487-10.502564 0-18.379487 7.876923-18.379487 18.379487-1.312821 9.189744 7.876923 18.379487 18.379487 18.379487z" fill="#FFAC00" p-id="12115"></path><path d="M505.435897 367.589744v44.635897h-45.948718v14.441026h45.948718v45.948718h13.128206v-45.948718h45.948718v-14.441026h-45.948718v-44.635897zM620.964103 249.435897v56.451282h-56.451282v18.379488h56.451282v56.451282h18.379487v-56.451282h56.451282v-18.379488h-56.451282v-56.451282zM430.605128 105.025641v102.4h-102.4v31.507692h102.4v102.4h31.507693v-102.4h102.4v-31.507692h-102.4v-102.4z" fill="#402916" p-id="12116"></path></svg>&nbsp;体验金</a>'

                                //+ (data[i].islock != 1 ? '<a class="card-button common-button" ' + "onclick=\"" + "get_action({name:'event_create',data:{id:" + data[i].id + ",aname:'admin',name:'" + _action + "',event:'islock',islock:1},n:'锁定用户',e:'锁定用户 " + data[i].phone + "'});" + "\"" + '  style="color:#888!important;"><svg t="1698082343502" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="4855" width="12" height="12"><path d="M968.499 676.076l-28.112 0c0.113-1.197 0.339-2.303 0.339-3.522l0-60.605c0-77.404-62.93-140.357-140.334-140.357-77.359 0-140.312 62.953-140.312 140.357l0 60.605c0 1.242 0.248 2.348 0.361 3.522l-16.574 0c-25.809 0-46.786 20.999-46.786 46.786l0 223.157c0 25.854 20.999 46.831 46.786 46.831l324.654 0c25.786 0 46.786-20.999 46.786-46.831l0-223.157c-0.023-25.786-21.044-46.786-46.808-46.786l0 0zM731.703 672.553l0-60.605c0-37.934 30.799-68.756 68.666-68.756 37.889 0 68.711 30.822 68.711 68.756l0 60.605c0 1.242 0.271 2.348 0.384 3.522l-138.099 0c0.09-1.197 0.339-2.281 0.339-3.522l0 0zM979.45 946.019c0 6.097-4.922 10.974-10.951 10.974l-324.654 0c-6.051 0-10.974-4.9-10.974-10.974l0-223.157c0-6.051 4.945-10.974 10.974-10.974l324.654 0c6.051 0 10.951 4.945 10.951 10.974l0 223.157zM806.149 822.574c-14.79 0-26.825 11.99-26.825 26.825l0 51.64 53.627 0 0-51.64c0.023-14.812-11.99-26.825-26.802-26.825l0 0zM655.902 617.3c-17.025-6.006-34.299-11.2-51.821-15.558-2.552-1.332-5.442-2.123-8.377-2.123l-9.19 0c-2.868 0-5.735 0.7-8.309 2.077-6.187 3.342-13.187 6.164-20.232 8.919-5.442 2.168-11.2 3.884-17.003 5.645-2.642 0.7-5.306 1.535-7.993 2.303-5.035 1.377-10.251 2.461-15.467 3.477-4.606 0.926-9.303 1.671-14.022 2.348-4.042 0.564-8.016 1.197-12.125 1.581-9.28 0.971-18.741 1.581-28.225 1.581-3.545 0-7.045-0.452-10.567-0.61-38.837-1.377-76.523-10.138-104.59-25.222-2.506-1.377-5.419-2.077-8.309-2.077l-6.684 0c-1.355 0-2.732 0.135-4.087 0.452-103.958 24.612-200.578 78.194-279.54 154.763-3.364 3.297-5.284 7.948-5.284 12.667l0 62.93c0 9.755 7.858 161.356 17.612 161.356l522.613 0c-20.525-42.112-32.064-89.394-32.064-139.409 0-93.097 39.966-176.891 103.664-235.102l0 0zM265.111 320.758c0 18.945 14.519 34.299 32.38 34.299l0.587 0c6.706 90.5 77.969 181.023 165.059 181.023 4.968 0 9.845-0.361 14.722-0.971 1.716-0.181 3.455-0.519 5.216-0.79 3.003-0.474 5.984-1.061 8.942-1.716 2.168-0.519 4.245-1.152 6.435-1.716 2.326-0.655 4.629-1.445 6.932-2.213 2.484-0.881 4.968-1.716 7.384-2.777 1.423-0.519 2.822-1.242 4.2-1.852 61.011-27.457 105.968-98.11 111.251-168.966l0.474 0c17.928 0 32.425-15.354 32.425-34.299 0-13.977-7.903-26.035-19.125-31.386 6.458-20.322 10.071-42.021 10.071-64.759 0-91.742-58.211-168.966-137.625-192.72-1.31-0.361-2.552-0.723-3.839-1.084-6.074-1.626-12.238-2.913-18.47-4.019-1.919-0.316-3.771-0.655-5.713-0.903-7.655-0.971-15.4-1.671-23.28-1.671-104.387 0-188.994 89.755-188.994 200.397 0 22.738 3.59 44.437 10.161 64.759-11.29 5.329-19.193 17.364-19.193 31.364l0 0zM265.111 320.758z" fill="#41416E" p-id="4856"></path></svg>&nbsp;锁屏</a>' : '<a class="card-button common-button" ' + "onclick=\"" + "get_action({name:'event_create',data:{id:" + data[i].id + ",aname:'admin',name:'" + _action + "',event:'islock',islock:0},n:'用户解除锁定',e:'解除锁定用户 " + data[i].phone + "'});" + "\"" + ' style="color:red!important;"><svg t="1698082343502" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="4855" width="12" height="12"><path d="M968.499 676.076l-28.112 0c0.113-1.197 0.339-2.303 0.339-3.522l0-60.605c0-77.404-62.93-140.357-140.334-140.357-77.359 0-140.312 62.953-140.312 140.357l0 60.605c0 1.242 0.248 2.348 0.361 3.522l-16.574 0c-25.809 0-46.786 20.999-46.786 46.786l0 223.157c0 25.854 20.999 46.831 46.786 46.831l324.654 0c25.786 0 46.786-20.999 46.786-46.831l0-223.157c-0.023-25.786-21.044-46.786-46.808-46.786l0 0zM731.703 672.553l0-60.605c0-37.934 30.799-68.756 68.666-68.756 37.889 0 68.711 30.822 68.711 68.756l0 60.605c0 1.242 0.271 2.348 0.384 3.522l-138.099 0c0.09-1.197 0.339-2.281 0.339-3.522l0 0zM979.45 946.019c0 6.097-4.922 10.974-10.951 10.974l-324.654 0c-6.051 0-10.974-4.9-10.974-10.974l0-223.157c0-6.051 4.945-10.974 10.974-10.974l324.654 0c6.051 0 10.951 4.945 10.951 10.974l0 223.157zM806.149 822.574c-14.79 0-26.825 11.99-26.825 26.825l0 51.64 53.627 0 0-51.64c0.023-14.812-11.99-26.825-26.802-26.825l0 0zM655.902 617.3c-17.025-6.006-34.299-11.2-51.821-15.558-2.552-1.332-5.442-2.123-8.377-2.123l-9.19 0c-2.868 0-5.735 0.7-8.309 2.077-6.187 3.342-13.187 6.164-20.232 8.919-5.442 2.168-11.2 3.884-17.003 5.645-2.642 0.7-5.306 1.535-7.993 2.303-5.035 1.377-10.251 2.461-15.467 3.477-4.606 0.926-9.303 1.671-14.022 2.348-4.042 0.564-8.016 1.197-12.125 1.581-9.28 0.971-18.741 1.581-28.225 1.581-3.545 0-7.045-0.452-10.567-0.61-38.837-1.377-76.523-10.138-104.59-25.222-2.506-1.377-5.419-2.077-8.309-2.077l-6.684 0c-1.355 0-2.732 0.135-4.087 0.452-103.958 24.612-200.578 78.194-279.54 154.763-3.364 3.297-5.284 7.948-5.284 12.667l0 62.93c0 9.755 7.858 161.356 17.612 161.356l522.613 0c-20.525-42.112-32.064-89.394-32.064-139.409 0-93.097 39.966-176.891 103.664-235.102l0 0zM265.111 320.758c0 18.945 14.519 34.299 32.38 34.299l0.587 0c6.706 90.5 77.969 181.023 165.059 181.023 4.968 0 9.845-0.361 14.722-0.971 1.716-0.181 3.455-0.519 5.216-0.79 3.003-0.474 5.984-1.061 8.942-1.716 2.168-0.519 4.245-1.152 6.435-1.716 2.326-0.655 4.629-1.445 6.932-2.213 2.484-0.881 4.968-1.716 7.384-2.777 1.423-0.519 2.822-1.242 4.2-1.852 61.011-27.457 105.968-98.11 111.251-168.966l0.474 0c17.928 0 32.425-15.354 32.425-34.299 0-13.977-7.903-26.035-19.125-31.386 6.458-20.322 10.071-42.021 10.071-64.759 0-91.742-58.211-168.966-137.625-192.72-1.31-0.361-2.552-0.723-3.839-1.084-6.074-1.626-12.238-2.913-18.47-4.019-1.919-0.316-3.771-0.655-5.713-0.903-7.655-0.971-15.4-1.671-23.28-1.671-104.387 0-188.994 89.755-188.994 200.397 0 22.738 3.59 44.437 10.161 64.759-11.29 5.329-19.193 17.364-19.193 31.364l0 0zM265.111 320.758z" fill="#41416E" p-id="4856"></path></svg>&nbsp;解锁</a>')
                                + '<a class="card-button common-button" ' + "onclick='edit(" + edtext({ aname: 'admin', title: _actionName + "充值", action: _action, id: data[i].id, data: point_model }) + ")'" + '><svg t="1688831098290" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="22140" width="16" height="16" style="position:relative;top:3px;"><path d="M739.1744 155.1872a425.02144 425.02144 0 0 1 65.4848 227.1744c0 235.8272-191.1808 427.0592-427.0592 427.0592-84.224 0-162.6624-24.4736-228.8128-66.56 75.5712 120.064 209.2032 199.8848 361.5744 199.8848 235.8272 0 427.0592-191.1808 427.0592-427.0592 0-151.6544-79.1552-284.7232-198.2464-360.4992z" fill="#ffa115" p-id="22141"></path><path d="M510.3616 973.4656c-252.416 0-457.7792-205.3632-457.7792-457.7792s205.3632-457.7792 457.7792-457.7792 457.7792 205.3632 457.7792 457.7792-205.3632 457.7792-457.7792 457.7792z m0-854.1184c-218.5216 0-396.3392 177.7664-396.3392 396.3392S291.84 912.0256 510.3616 912.0256s396.3392-177.7664 396.3392-396.3392-177.8176-396.3392-396.3392-396.3392z" fill="#474A54" p-id="22142"></path><path d="M655.6672 585.216h-116.6336v-49.9712h116.6336c16.9472 0 30.72-13.7728 30.72-30.72s-13.7728-30.72-30.72-30.72h-80.3328l102.3488-104.96c11.8272-12.1344 11.6224-31.5904-0.5632-43.4176-12.1344-11.8272-31.5904-11.6224-43.4176 0.5632L508.3136 454.5536 382.9248 325.9392a30.72 30.72 0 0 0-43.4176-0.5632 30.72 30.72 0 0 0-0.5632 43.4176l102.3488 104.96H360.96c-16.9472 0-30.72 13.7728-30.72 30.72s13.7728 30.72 30.72 30.72h116.6336v49.9712H360.96c-16.9472 0-30.72 13.7728-30.72 30.72s13.7728 30.72 30.72 30.72h116.6336v84.6336c0 16.9472 13.7728 30.72 30.72 30.72s30.72-13.7728 30.72-30.72V646.656h116.6336c16.9472 0 30.72-13.7728 30.72-30.72s-13.7728-30.72-30.72-30.72z" fill="#474A54" p-id="22143"></path></svg>&nbsp;充值</a>'
                                + '<a class="card-button common-button" ' + "onclick='edit(" + edtext({ aname: 'admin', title: _actionName + "修改", action: _action, id: data[i].id, data: modify_model }) + ")'" + '><svg  class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg"  width="16" height="16" style="position:relative;top:3px;"><path d="M358.165868 554.624796c-0.533143 0.680499-1.066285 1.391696-1.303692 2.251274l-41.104163 150.700257c-2.400676 8.772804 0.059352 18.226107 6.550183 24.892947 4.860704 4.742001 11.261485 7.350408 18.077727 7.350408 2.252297 0 4.504594-0.267083 6.727215-0.860601l149.630902-40.808428c0.23843 0 0.357134 0.207731 0.534166 0.207731 1.718131 0 3.408633-0.62217 4.683672-1.927909l400.113747-400.054395c11.883655-11.897981 18.404162-28.109198 18.404162-45.74281 0-19.989263-8.476045-39.963177-23.324218-54.767348l-37.786605-37.844933c-14.81645-14.848173-34.822087-23.338544-54.797024-23.338544-17.631566 0-33.842783 6.520507-45.75816 18.388812L358.758362 553.232077C358.344946 553.615816 358.462626 554.179658 358.165868 554.624796M862.924953 257.19778l-39.742143 39.71349-64.428382-65.451688 39.180348-39.179324c6.193049-6.222725 18.194384-5.318122 25.308409 1.822508l37.813211 37.845956c3.943822 3.941775 6.195096 9.18622 6.195096 14.372336C867.223862 250.574942 865.710392 254.42769 862.924953 257.19778M429.322487 560.907896l288.712541-288.728914 64.459081 65.49569L494.314711 625.838721 429.322487 560.907896zM376.718409 677.970032l20.863167-76.580143 55.656601 55.657624L376.718409 677.970032z" fill="#e6e6e6" p-id="1994"></path><path d="M888.265084 415.735539c-15.144932 0-27.562752 12.313443-27.620058 27.665083l0 372.98283c0 19.559475-15.885805 35.444257-35.475979 35.444257L194.220958 851.827709c-19.559475 0-35.504632-15.883759-35.504632-35.444257L158.716326 207.602222c0-19.575848 15.945157-35.474956 35.504632-35.474956l406.367171 0c15.231913 0 27.592428-12.371772 27.592428-27.606755 0-15.202237-12.360516-27.590382-27.592428-27.590382L190.013123 116.930129c-47.684022 0-86.49291 38.779212-86.49291 86.49291L103.520213 820.59231c0 47.713698 38.808888 86.47756 86.49291 86.47756l639.334083 0c47.715745 0 86.509283-38.763862 86.509283-86.47756L915.856489 443.222567C915.794068 428.048983 903.408993 415.735539 888.265084 415.735539" fill="#e6e6e6" p-id="1995"></path></svg>&nbsp;编辑</a>'
                                //+ '&nbsp;<a class="card-button common-button" ' + "onclick=\"" + "get_action({name:'event_create',data:{id:" + data[i].id + ",aname:'admin',name:'accounts',event:'delete'},n:'删除用户',e:'删除用户 " + data[i].phone + "<br>【账户余额】：" + data[i].amount + "（确认将完成订单）'});" + "\"" + '><svg t="*************" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="1699" width="16" height="16" style="position:relative;top:3px;"><path d="M612.693333 170.666667l8.533334 42.666666H402.773333l8.533334-42.666666h201.386666z m-293.632 25.941333a86.186667 86.186667 0 0 0-1.706666 16.725333H170.666667a42.666667 42.666667 0 0 0 0 85.333334h682.666666a42.666667 42.666667 0 1 0 0-85.333334h-146.730666c0-5.504-0.554667-11.093333-1.706667-16.725333l-8.533333-42.666667A85.333333 85.333333 0 0 0 612.778667 85.333333H411.306667a85.333333 85.333333 0 0 0-83.712 68.608l-8.533334 42.666667zM426.666667 469.333333a42.666667 42.666667 0 0 0-42.666667 42.666667v170.666667a42.666667 42.666667 0 1 0 85.333333 0v-170.666667a42.666667 42.666667 0 0 0-42.666666-42.666667z m128 42.666667a42.666667 42.666667 0 1 1 85.333333 0v170.666667a42.666667 42.666667 0 1 1-85.333333 0v-170.666667zM255.402667 384A42.666667 42.666667 0 0 1 298.666667 426.069333L301.738667 640H301.653333a42.666667 42.666667 0 1 1-85.290666-2.176l-2.986667-210.56A42.666667 42.666667 0 0 1 255.36 384z m51.925333 396.586667h0.042667A103.381333 103.381333 0 0 0 406.698667 853.333333h210.602666c54.186667 0 98.56-40.789333 103.210667-92.757333L725.333333 426.069333a42.666667 42.666667 0 0 1 85.333334 1.194667l-4.864 337.365333-0.128 1.365334C798.08 863.872 715.562667 938.666667 617.301333 938.666667H406.698667a188.885333 188.885333 0 0 1-180.096-130.389334 42.666667 42.666667 0 0 1 80.725333-27.648z" fill="#14101C" p-id="1700"></path></svg>&nbsp;删除</a>'
                                  + '<a class="card-button common-button" ' + "onclick=\"" + "open_new_page('transaction_list.aspx?userid=" + data[i].id + "','" + data[i].phone + " - 交易明细');" + "\"" + '><svg t="1695233208464" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="3803" width="12" height="12"><path d="M963.584 1024H60.416c-13.897143-4.900571-27.501714-10.020571-38.4-20.699429-11.410286-11.190857-17.115429-25.161143-22.016-39.716571V60.416C4.900571 46.518857 10.020571 32.914286 20.699429 22.016 31.890286 10.605714 45.860571 4.900571 60.416 0h903.168c13.897143 4.900571 27.501714 10.020571 38.4 20.699429 11.410286 11.190857 17.115429 25.161143 22.016 39.716571v903.168c-4.900571 14.555429-10.605714 28.525714-21.942857 39.716571-10.971429 10.678857-24.649143 15.798857-38.473143 20.699429z m-13.165714-950.125714H73.874286v876.397714h876.397714V73.874286z" fill="#2E4C91" p-id="3804"></path><path d="M805.522286 218.477714v68.242286H368.64v-68.242286h436.882286M805.522286 368.64v68.242286H368.64V368.64h436.882286M805.522286 587.117714v68.242286H368.64v-68.242286h436.882286M368.64 805.522286v-68.242286h436.882286v68.242286H368.64M286.72 286.72h-68.242286v-68.242286h68.242286v68.242286M286.72 737.28v68.242286h-68.242286v-68.242286h68.242286M286.72 368.64v68.242286h-68.242286V368.64h68.242286M286.72 655.36h-68.242286v-68.242286h68.242286v68.242286" fill="#2E4C91" p-id="3805"></path></svg>&nbsp;明细</a>'

                                   //+ '<a class="card-button common-button" ' + "onclick='edit(" + edtext({ aname: 'admin', title: _actionName + "验证码发送", action: _action, id: data[i].id, data: sms_model }) + ")'" + "\"" + '><svg t="1695736389328" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="5624" width="12" height="12"><path d="M112.658963 273.73037c1.232593 1.289481 2.522074 2.522074 3.887407 3.735704l310.423704 275.911111a128 128 0 0 0 170.059852 0l310.423704-275.911111c1.365333-1.21363 2.654815-2.446222 3.868444-3.716741 0.455111 3.299556 0.682667 6.674963 0.682667 10.10726v444.378074a75.851852 75.851852 0 0 1-75.851852 75.851852H187.847111a75.851852 75.851852 0 0 1-75.851852-75.851852V283.856593c0-2.541037 0.132741-5.044148 0.37926-7.528297z" fill="#279CFF" p-id="5625"></path><path d="M562.384593 476.918519L864.938667 208.023704H159.061333L461.615407 476.918519a75.851852 75.851852 0 0 0 100.769186 0z" fill="#279CFF" fill-opacity=".5" p-id="5626"></path></svg>&nbsp;短信</a>'

                                + (data[i].chat_nospeak != 1 ? '<a class="card-button common-button" ' + "onclick=\"" + "get_action({name:'event_create',data:{id:" + data[i].id + ",aname:'admin',name:'" + _action + "',event:'chat_speak',chat_nospeak:1},n:'禁言用户',e:'禁言用户 " + data[i].phone + "'});" + "\"" + '  style="color:#888!important;"><svg t="1697104177962" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="6718" width="12" height="12"><path d="M0 0h1024v1024H0z" fill="#FFFFFF" fill-opacity="0" p-id="6719"></path><path d="M825.728 588.8a180.48 180.48 0 0 1-25.6-93.141333v-112.213334c0-33.109333-5.76-66.005333-17.066667-97.109333l93.184-93.184a32 32 0 1 0-45.269333-45.269333l-682.666667 682.666666a32 32 0 1 0 45.226667 45.269334l75.946667-75.946667h126.805333c-0.341333 3.584-1.408 7.04-1.408 10.666667a117.333333 117.333333 0 1 0 234.666667 0c0-3.626667-1.066667-7.082667-1.408-10.666667H797.866667a87.424 87.424 0 0 0 74.837333-132.266667L825.728 588.8z m-260.266667 221.866667a53.333333 53.333333 0 0 1-106.666666 0c0.085333-3.584 0.554667-7.168 1.408-10.666667h103.850666c0.810667 3.498667 1.237333 7.082667 1.28 10.666667h0.128z m252.757334-86.442667a22.784 22.784 0 0 1-20.224 11.733333H333.226667l398.037333-398.037333c3.157333 15.018667 4.693333 30.293333 4.736 45.610667v112.213333a244.650667 244.650667 0 0 0 34.816 126.08l46.933333 78.506667c4.522667 7.253333 4.650667 16.469333 0.341334 23.893333h0.128z m-652.8-80.170667l32.853333-55.04c16.853333-28.16 25.728-60.416 25.6-93.269333v-112.213333a288.554667 288.554667 0 0 1 466.474667-226.133334 32 32 0 1 1-39.509334 50.346667 224.512 224.512 0 0 0-362.965333 175.786667v112.213333a244.266667 244.266667 0 0 1-34.901333 126.165333l-32.768 54.912a32 32 0 1 1-54.954667-32.725333l0.170667-0.042667z" fill="#41416E" p-id="6720"></path></svg>&nbsp;禁言</a>' : '<a class="card-button common-button" ' + "onclick=\"" + "get_action({name:'event_create',data:{id:" + data[i].id + ",aname:'admin',name:'" + _action + "',event:'chat_speak',chat_nospeak:0},n:'用户解除禁言',e:'解除禁言用户 " + data[i].phone + "'});" + "\"" + ' style="color:red!important;"><svg t="1697104177962" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="6718" width="12" height="12"><path d="M0 0h1024v1024H0z" fill="#FFFFFF" fill-opacity="0" p-id="6719"></path><path d="M825.728 588.8a180.48 180.48 0 0 1-25.6-93.141333v-112.213334c0-33.109333-5.76-66.005333-17.066667-97.109333l93.184-93.184a32 32 0 1 0-45.269333-45.269333l-682.666667 682.666666a32 32 0 1 0 45.226667 45.269334l75.946667-75.946667h126.805333c-0.341333 3.584-1.408 7.04-1.408 10.666667a117.333333 117.333333 0 1 0 234.666667 0c0-3.626667-1.066667-7.082667-1.408-10.666667H797.866667a87.424 87.424 0 0 0 74.837333-132.266667L825.728 588.8z m-260.266667 221.866667a53.333333 53.333333 0 0 1-106.666666 0c0.085333-3.584 0.554667-7.168 1.408-10.666667h103.850666c0.810667 3.498667 1.237333 7.082667 1.28 10.666667h0.128z m252.757334-86.442667a22.784 22.784 0 0 1-20.224 11.733333H333.226667l398.037333-398.037333c3.157333 15.018667 4.693333 30.293333 4.736 45.610667v112.213333a244.650667 244.650667 0 0 0 34.816 126.08l46.933333 78.506667c4.522667 7.253333 4.650667 16.469333 0.341334 23.893333h0.128z m-652.8-80.170667l32.853333-55.04c16.853333-28.16 25.728-60.416 25.6-93.269333v-112.213333a288.554667 288.554667 0 0 1 466.474667-226.133334 32 32 0 1 1-39.509334 50.346667 224.512 224.512 0 0 0-362.965333 175.786667v112.213333a244.266667 244.266667 0 0 1-34.901333 126.165333l-32.768 54.912a32 32 0 1 1-54.954667-32.725333l0.170667-0.042667z" fill="#41416E" p-id="6720"></path></svg>&nbsp;解禁</a>')


                                + (data[i].chatad == 0 ? '<a class="card-button common-button" ' + "onclick=\"" + "get_action({name:'event_create',data:{id:" + data[i].id + ",aname:'admin',name:'" + _action + "',event:'chatad',isad:1},n:'身份升级',e:'切换为群管理员 " + data[i].phone + "（备注/禁言功能）'});" + "\"" + '  style="color:#888!important;"><svg t="" class="" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="" width="12" height="12"><path d="M502.496 63.136c125.888 0 227.936 100.384 227.936 224.192 0 123.84-102.048 224.224-227.936 224.224-125.888 0-227.936-100.384-227.936-224.224C274.56 163.488 376.64 63.136 502.496 63.136L502.496 63.136zM502.496 63.136c125.888 0 227.936 100.384 227.936 224.192 0 123.84-102.048 224.224-227.936 224.224-125.888 0-227.936-100.384-227.936-224.224C274.56 163.488 376.64 63.136 502.496 63.136L502.496 63.136zM417.024 586.304l189.984 0c162.624 0 294.432 129.632 294.432 289.6l0 18.656c0 63.04-131.84 65.44-294.432 65.44l-189.984 0c-162.624 0-294.432-0.096-294.432-65.44l0-18.656C122.592 715.936 254.4 586.304 417.024 586.304L417.024 586.304zM417.024 586.304" fill="#272636" p-id="13713"></path></svg>&nbsp;群成员</a>' : '<a class="card-button common-button" ' + "onclick=\"" + "get_action({name:'event_create',data:{id:" + data[i].id + ",aname:'admin',name:'" + _action + "',event:'chatad',isad:0},n:'身份解除',e:'取消群管理员 " + data[i].phone + "'});" + "\"" + ' style="color: #265fc9 !important;font-weight: bold;"><svg t="" class="limit_color" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="" width="12" height="12"><path d="M588.8 785H421.4c-6.6 0-12-5.4-12-12V564.1c0-6.6 5.4-12 12-12h167.4c6.6 0 12 5.4 12 12V773c0 6.6-5.4 12-12 12z" fill="#FFFFFF" p-id="11573"></path><path d="M285.9 363.7s-31.9-168 33.7-191.6c0 0-18.1-58.4 64-66.1 0 0 53.5-70.5 252.5-6.8 0 0 32.5 14.2 52.2 45.8 0 0 50.9 31.3 55.6 49.2 4.7 17.9 23.4 97 3.4 122.1 0 0 3.7 33.9-26.9 49.2-33 16.3-130.4 13.3-434.5-1.8z m0 0" fill="#6A4607" p-id="11574"></path><path d="M899.2 789.2s-87.6-195.5-298.6-187.9c0 0-25.4 176.9-93.2 176.9-67.8 0-97.6-141.3-97.9-176.9 0 0-147.4-23.7-284.6 182.6 0 0 118.9 166.1 400.9 167.7C716 952.7 853.9 834 891.3 795.7" fill="#0681F8" p-id="11575"></path><path d="M596.5 593.7s47.7 8 91.7-113.7c0 0 114-59.5 36.3-116.8 0 0-24.3-89.1-93.2-134.2s-144.2 6.7-148.5 44.2c0 0-145-9.9-196 83.7 0 0-83.3 44.8 31.6 115.2 0 0 48.8 110.1 94.8 120 0 0-10.2 118.4 83.8 118.4 93.8 0 99.5-90.6 99.5-116.8z m0 0" fill="#EFD7B5" p-id="11576"></path></svg>&nbsp;群管理</a>')


                                + '<a class="card-button common-button" ' + "onclick='open_new_page(\"user_logs.aspx?uid=" + data[i].id + "\",\"用户" + data[i].phone + " - 登录日志\")'" + '><svg t="1698343844878" class="limit_color" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="3966" width="12" height="12"><path d="M291.328 377.344h391.68c33.28 0 60.416 27.136 60.416 60.416v361.472c0 33.28-27.136 60.416-60.416 60.416H291.328c-33.28 0-60.416-27.136-60.416-60.416V437.76c0.512-33.28 27.136-60.416 60.416-60.416z" fill="#CAE4FF" p-id="3967"></path><path d="M869.888 248.832l-154.112-150.528c-12.8-12.8-29.696-19.456-47.616-19.456H178.688c-52.736 0.512-94.72 43.008-94.72 95.744v683.008c0 52.736 42.496 95.232 94.72 95.232h616.448c52.736 0 95.232-43.008 95.232-95.232V297.984c0-18.432-7.168-35.84-20.48-49.152z m-46.592 608.768c0 15.36-12.8 28.16-28.16 28.16H178.688c-15.36 0-27.648-12.8-27.648-28.16V174.08c0-15.36 12.288-28.16 27.648-28.16l442.88 0.512v118.784c0 55.808 45.056 100.864 100.352 100.864h100.864l0.512 491.52z m0-558.08h-100.864c-18.432 0-33.28-14.336-33.792-32.768V165.888l134.656 131.584v2.048z" fill="#0972E7" p-id="3968"></path><path d="M655.36 479.744H318.976c-18.432 0-33.792 14.848-33.792 33.792s14.848 33.792 33.792 33.792H655.36c18.432 0 33.792-14.848 33.792-33.792s-15.36-33.792-33.792-33.792z m0 202.24H318.976c-18.432 0-33.792 14.848-33.792 33.28s14.848 33.792 33.28 33.792h336.384c18.432 0 33.792-14.848 33.792-33.28 0-18.944-14.848-33.792-33.28-33.792z" fill="#0972E7" p-id="3969"></path></svg>&nbsp;日志</a>'


                                         + (data[i].redbag_amount == "" && json.redbag_poll_token != null && data[i].group_name == "★托号" ? '<a class="card-button common-button" ' + "onclick=\"" + "get_action({name:'event_create',data:{id:" + data[i].id + ",aname:'admin',name:'" + _action + "',event:'get_redbag'},n:'领取红包雨',e:'领取红包雨 " + data[i].phone + "<br>【账户余额】：" + data[i].amount + "（领取后增加余额）'});" + "\"" + ' style="color:red!important;"><svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" t="1608100632456" class="limit_color" viewBox="0 0 1024 1024" version="1.1" p-id="14864" width="12" height="12"><defs><style type="text/css"></style></defs><path d="M57.046912 0m76.8 0l716.8 0q76.8 0 76.8 76.8l0 870.4q0 76.8-76.8 76.8l-716.8 0q-76.8 0-76.8-76.8l0-870.4q0-76.8 76.8-76.8Z" fill="#D73C1E" p-id="14865"></path><path d="M850.646912 0a76.8 76.8 0 0 1 76.8 76.8l0.0256 275.8144c-122.2912 51.968-272.64 82.5856-435.2 82.5856-162.5856 0-312.96-30.6432-435.2512-82.5856L57.046912 76.8a76.8 76.8 0 0 1 76.8-76.8h716.8z" fill="#F14C2E" p-id="14866"></path><path d="M517.846912 409.6m-153.6 0a153.6 153.6 0 1 0 307.2 0 153.6 153.6 0 1 0-307.2 0Z" fill="#F7D49A" p-id="14867"></path><path d="M528.906112 512h-21.9136v-51.2h-65.7664v-20.48h65.7664V409.6h-65.7664v-20.48h53.504L441.046912 318.3104l18.816-11.1104 54.3232 71.68h12.4672l48.5632-67.84 18.2272 11.392-47.7696 66.688H594.646912v20.48h-63.6416l-2.0992 2.944V440.32H594.646912v20.48h-65.7408v51.2z" fill="#E98337" p-id="14868"></path></svg>&nbsp;领红包</a>' : '')

                               + ('<a class="card-button common-button" ' + "onclick=\"" + "get_action({name:'event_create',data:{id:" + data[i].id + ",aname:'admin',name:'" + _action + "',event:'reset_password'},n:'重置密码',e:'重置用户 " + data[i].phone + " 密码',inputs:[{id:'newPwd',name:'新密码',value:''},{id:'actCode',name:'谷歌验证码',value:''}]});" + "\"" + '  style="color:#888!important;"><svg t="" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="4482" width="12" height="12"><path d="M932.2 388c0.5-1.7 0.8-3.3 0.8-4.9 0-10.4-8.4-18.8-18.8-18.8-4.2 0-8.1 1.4-11.4 4.1l-1 1.2-52.9 77.7c-14.4-84.6-56.3-162.3-120-221.5C656.1 158.2 561.5 121 462.4 121 246 121 69.9 297.1 69.9 513.5S246 906 462.4 906c67.9 0 134.9-17.8 193.8-51.5C713 821.6 761.4 774.8 796 718.8c0.2-0.3 0.5-0.8 0.6-1.5 1-2.4 1.5-4.8 1.5-7.2 0-10.4-8.4-18.8-18.8-18.8-5.3 0-10.2 1.6-13.8 5.6l-0.6 0.8-1.8 2.9C699.1 803 590.4 865.2 471 868c-203.2 4.9-369.8-163.1-363.2-366.3 6.1-190.3 162.9-343.2 354.6-343.2 172.7 0 320.9 127.6 349.5 295.4L729.7 404l-2.7-1.3c-1.5-0.4-3.1-0.7-4.6-0.7-10.4 0-18.8 8.4-18.8 18.8 0 4 1.4 7.9 3.8 11.1 0.3 0.3 0.6 0.5 0.8 0.8l2.3 1.5 109.2 67.1c5.7 3.6 13.1 6.3 19.2 6.3 10.6 0 18.2-5 24.4-14.4L931.1 390l1.1-2z" fill="#4E30DC" p-id="4483"></path><path d="M462.3 911.3c-219.4 0-398-178.5-398-398 0-219.4 178.5-398 398-398 100.5 0 196.5 37.7 270.3 106.3 32.1 29.8 58.9 64.4 79.6 102.6C830.7 358.4 844 395 851.8 433l45.5-66.9 1.5-1.8 0.4-0.3c4.3-3.5 9.4-5.3 14.9-5.3 13.4 0 24.3 10.9 24.3 24.3 0 2.2-0.3 4.4-1 6.6l-0.1 0.4-1.6 3.1L868 496.4c-7.6 11.3-17.1 16.8-29 16.8-6.7 0-14.9-2.7-22.1-7.1-2.2-1.3-16-9.8-109.2-67.1l-0.1-0.1-2.7-1.8-1.5-1.4-0.3-0.4c-3.2-4.2-5-9.2-5-14.4 0-13.4 10.9-24.3 24.3-24.3 2 0 4.1 0.3 6.1 0.9l0.6 0.2 3.4 1.7 71.7 43.5C771 283.1 626.2 164 462.3 164c-189.6 0-343 148.4-349.1 337.9-3.1 96.7 32.9 188 101.4 257.1 68.5 69.1 159.5 105.8 256.2 103.5 117.4-2.8 224.9-64.4 287.6-164.9l2.1-3.2 0.9-1.2c4.3-4.9 10.5-7.4 17.9-7.4 13.4 0 24.3 10.9 24.3 24.3 0 3-0.6 6.1-1.8 9-0.3 0.9-0.6 1.8-1.1 2.5C765.6 778.5 716.5 826 658.8 859c-59.6 34.2-127.6 52.3-196.5 52.3z m0-784.9c-213.4 0-387 173.6-387 387s173.6 387 387 387c67 0 133-17.6 191-50.8 56.1-32.1 103.8-78.3 137.9-133.5l0.1-0.4 0.2-0.4c0.7-1.7 1.1-3.4 1.1-5.1 0-7.4-6-13.3-13.3-13.3-4.2 0-7.3 1.2-9.5 3.6l-0.4 0.5-1.7 2.7c-31.8 51-75.8 93.5-127.2 122.9-51.3 29.4-109.9 45.7-169.5 47.1-50.4 1.2-99.4-7.9-145.6-27-44.6-18.4-84.5-45.3-118.7-79.7-34.2-34.5-60.7-74.6-78.7-119.3-18.8-46.4-27.4-95.5-25.8-145.9 3-93.6 41.9-181.2 109.4-246.7C279.3 189.2 368.3 153 462.3 153c84.5 0 167 30.6 232.4 86.1 64.9 55.2 108.5 131.1 122.6 213.9l0.7 3.4-5.8 4.1-3.1-1.8-82.1-49.8-2-1c-0.9-0.2-1.7-0.3-2.6-0.3-7.4 0-13.3 6-13.3 13.3 0 2.6 0.8 5.2 2.4 7.4l0.1 0.1 1.9 1.2c2.9 1.8 108.8 66.8 109.1 67.1 5.4 3.3 11.7 5.5 16.3 5.5 8.2 0 14.4-3.7 19.9-11.9l67.6-102.9 0.7-1.4c0.3-1 0.4-1.9 0.4-2.9 0-7.4-6-13.3-13.3-13.3-2.7 0-5.3 0.9-7.6 2.6l-0.5 0.5-54.8 80.5-7.4-1.6-0.5-3.6c-14.4-84.3-55.3-159.8-118.3-218.4-71.7-66.7-165-103.4-262.8-103.4z" fill="#4E30DC" p-id="4484"></path><path d="M415.8 268.8c-2.6-7.8-8.1-14-15.6-17.4-7.4-3.4-16-3.6-23.5-0.6-24.8 10-48.6 23-70.5 38.6-31 22-57.9 48.8-80 79.5-4.7 6.6-6.6 14.9-5 22.9 1.6 8.1 6.2 14.9 13.2 19.3l0.1 0.1c4.7 2.9 10 4.5 15.6 4.5h0.8c9.3-0.3 17.8-4.7 23.2-12.3 18.3-25.4 40.6-47.6 66.3-65.8 18.2-12.9 37.9-23.7 58.5-32 14.4-6 21.8-22 16.9-36.8z" fill="#FF4E7D" p-id="4485"></path></svg>&nbsp;重置</a>' +
                                 '<a class="card-button common-button" ' + "onclick=\"" + "get_action({name:'edit_create',data:{id:" + data[i].id + ",_app:'" + _action + "',action_type:'switch_parent'},inputs:[{id:'phone',name:'新上级账号',value:''},{id:'actCode',name:'谷歌验证码',value:''}],n:'更换上级',e:'原上级  <span style=\\'color: #2a2b2c;font-weight: bold;\\'>" + data[i].parent_phone + "</span><span style=\\'color: #b53d10;margin-left: 2px;margin-right:3px;\\'>[" + data[i].parent_userid + "]</span><br>本次更换后，您的所有相关下级会同步调整（请确认无误后再调整）" + "'});" + "\"" + '  style="color:#a15f3f !important;"><svg t="" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="10279" width="12" height="12"><path d="M929.890718 818.320898C865.89332 767.361903 681.410156 682.680547 681.410156 682.680547l-119.734864 225.339103-17.748612-53.68955 32.766668-67.751912-65.601599-67.751911-65.635731 67.751911 32.834932 67.751912-21.161807 54.952432L340.705078 682.680547s-184.449032 84.715488-248.480562 135.674483C28.295383 869.279892 0 1024 0 1024h1023.958359s-18.77257-145.675143-94.067641-205.679102z" fill="#2C82FC" p-id="10280"></path><path d="M771.040644 311.46151c2.28684 2.218576 6.860521 4.437153 11.434202 6.655729l9.113229 6.689862v11.161146c0 33.415174-4.539549 60.140488-11.400069 82.428648-9.147361 20.035452-20.547431 33.415174-36.521182 42.323612a280.632854 280.632854 0 0 1-84.510696 129.223545c-41.128994 35.633751-91.371218 55.703335-141.613441 55.703334s-100.484447-20.069584-141.57931-55.703334c-36.521181-33.449306-68.536946-77.991495-84.510696-129.223545a89.596356 89.596356 0 0 1-38.842154-42.323612C243.497298 396.108734 238.923617 369.349289 238.923617 335.968247v-11.161146l9.147361-6.689862a7.167709 7.167709 0 0 1 4.539549-2.218576c-20.547431-140.350559-2.252708-207.180908 59.389585-256.194382 98.197607-77.991495 283.19275-77.991495 383.677197-8.942569 68.502814 46.794897 93.658058 133.69483 75.363335 260.699798z m-29.524132 30.787015c-13.721042 0-29.694792 2.218576-36.555314 4.437152-6.826389-17.816875-11.434202-35.633751-18.26059-53.450626-86.797537 17.816875-184.995144 22.28816-315.174383-11.161146-13.68691 20.069584-25.121112 42.323612-38.808022 62.393196-11.434202-2.218576-25.121112-2.218576-41.128994-4.437153-2.28684 0-4.573681 0-6.826389 2.218577-2.28684 0-4.573681 2.218576-6.860521 2.218576 0 22.28816 4.573681 42.323612 9.113229 55.703335 4.573681 13.379723 13.721042 20.069584 22.868403 24.506736l11.40007 2.218577 2.28684 11.161146a250.90393 250.90393 0 0 0 73.076495 118.096531c34.268473 28.94389 73.076495 46.760765 114.205489 46.760765 41.094862 0 79.902884-17.816875 114.171357-46.760765a228.547506 228.547506 0 0 0 73.076495-118.096531l2.286841-11.161146 11.434201-4.437153c9.113229-2.218576 15.97375-11.161146 22.834272-24.506737 6.826389-13.379723 9.147361-33.449306 6.826389-55.703334 4.573681 2.218576 2.320972 2.218576 0 0z" fill="#2C82FC" p-id="10281"></path></svg>&nbsp;更换上级</a>'

                                + '<a class="card-button common-button" ' + "onclick='edit(" + edtext({ aname: 'admin', title: _actionName + "更换层级", action: _action, id: data[i].id, data: modify_level }) + ")'" + '><svg  class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg"  width="16" height="16" style="position:relative;top:3px;"><path d="M358.165868 554.624796c-0.533143 0.680499-1.066285 1.391696-1.303692 2.251274l-41.104163 150.700257c-2.400676 8.772804 0.059352 18.226107 6.550183 24.892947 4.860704 4.742001 11.261485 7.350408 18.077727 7.350408 2.252297 0 4.504594-0.267083 6.727215-0.860601l149.630902-40.808428c0.23843 0 0.357134 0.207731 0.534166 0.207731 1.718131 0 3.408633-0.62217 4.683672-1.927909l400.113747-400.054395c11.883655-11.897981 18.404162-28.109198 18.404162-45.74281 0-19.989263-8.476045-39.963177-23.324218-54.767348l-37.786605-37.844933c-14.81645-14.848173-34.822087-23.338544-54.797024-23.338544-17.631566 0-33.842783 6.520507-45.75816 18.388812L358.758362 553.232077C358.344946 553.615816 358.462626 554.179658 358.165868 554.624796M862.924953 257.19778l-39.742143 39.71349-64.428382-65.451688 39.180348-39.179324c6.193049-6.222725 18.194384-5.318122 25.308409 1.822508l37.813211 37.845956c3.943822 3.941775 6.195096 9.18622 6.195096 14.372336C867.223862 250.574942 865.710392 254.42769 862.924953 257.19778M429.322487 560.907896l288.712541-288.728914 64.459081 65.49569L494.314711 625.838721 429.322487 560.907896zM376.718409 677.970032l20.863167-76.580143 55.656601 55.657624L376.718409 677.970032z" fill="#e6e6e6" p-id="1994"></path><path d="M888.265084 415.735539c-15.144932 0-27.562752 12.313443-27.620058 27.665083l0 372.98283c0 19.559475-15.885805 35.444257-35.475979 35.444257L194.220958 851.827709c-19.559475 0-35.504632-15.883759-35.504632-35.444257L158.716326 207.602222c0-19.575848 15.945157-35.474956 35.504632-35.474956l406.367171 0c15.231913 0 27.592428-12.371772 27.592428-27.606755 0-15.202237-12.360516-27.590382-27.592428-27.590382L190.013123 116.930129c-47.684022 0-86.49291 38.779212-86.49291 86.49291L103.520213 820.59231c0 47.713698 38.808888 86.47756 86.49291 86.47756l639.334083 0c47.715745 0 86.509283-38.763862 86.509283-86.47756L915.856489 443.222567C915.794068 428.048983 903.408993 415.735539 888.265084 415.735539" fill="#e6e6e6" p-id="1995"></path></svg>&nbsp;更换层级</a>'
                                + '<a class="card-button common-button" ' + "onclick='edit(" + edtext({ aname: 'admin', title: _actionName + "数据调整", action: _action, id: data[i].id, data: modify_datas }) + ")'" + '><svg t="" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="" width="16" height="16" style="position:relative;top:3px;"><path d="M0 0h1024v1024H0z" fill="#202425" opacity=".01" p-id="8441"></path><path d="M238.933333 68.266667a170.666667 170.666667 0 0 0-170.666666 170.666666v546.133334a170.666667 170.666667 0 0 0 170.666666 170.666666h546.133334a170.666667 170.666667 0 0 0 170.666666-170.666666V273.237333 238.933333a170.666667 170.666667 0 0 0-170.666666-170.666666H238.933333z m648.533334 191.829333l-245.282134 275.933867-248.6272-155.374934a34.133333 34.133333 0 0 0-38.570666 1.6384L136.533333 546.133333V238.933333a102.4 102.4 0 0 1 102.4-102.4h546.133334a102.4 102.4 0 0 1 102.4 102.4v21.162667zM136.533333 631.466667l204.8-153.6v409.6H238.933333a102.4 102.4 0 0 1-102.4-102.4v-153.6z m273.066667 256V471.176533l204.8 128V887.466667h-204.8z m273.066667 0v-294.229334l204.8-230.4V785.066667a102.4 102.4 0 0 1-102.4 102.4h-102.4z" fill="#202425" p-id="8442"></path></svg>&nbsp;数据修改</a>'
                                + '<a class="card-button common-button" ' + "onclick='getGroupList(" + data[i].id + ")'" + '><svg t="" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="" width="16" height="16" style="position:relative;top:3px;"><path d="M0 0h1024v1024H0z" fill="#202425" opacity=".01" p-id="8441"></path><path d="M238.933333 68.266667a170.666667 170.666667 0 0 0-170.666666 170.666666v546.133334a170.666667 170.666667 0 0 0 170.666666 170.666666h546.133334a170.666667 170.666667 0 0 0 170.666666-170.666666V273.237333 238.933333a170.666667 170.666667 0 0 0-170.666666-170.666666H238.933333z m648.533334 191.829333l-245.282134 275.933867-248.6272-155.374934a34.133333 34.133333 0 0 0-38.570666 1.6384L136.533333 546.133333V238.933333a102.4 102.4 0 0 1 102.4-102.4h546.133334a102.4 102.4 0 0 1 102.4 102.4v21.162667zM136.533333 631.466667l204.8-153.6v409.6H238.933333a102.4 102.4 0 0 1-102.4-102.4v-153.6z m273.066667 256V471.176533l204.8 128V887.466667h-204.8z m273.066667 0v-294.229334l204.8-230.4V785.066667a102.4 102.4 0 0 1-102.4 102.4h-102.4z" fill="#202425" p-id="8442"></path></svg>&nbsp;聊天室分配</a>'

                                 )

                                         ))

                            + (get_param('audit') == 1 && data[i].upgrade_state == 1 ?

                                '<a style="color: #22AC38!important;text-shadow: 5px 5px 5px #********;font-weight: bold;" class="card-button common-button" onclick="' + "get_action({name:'event_create',data:{id:" + data[i].id + ",aname:'admin',name:'accounts',event:'upgrade_user',upgrade:1},n:'审核通过',e:'转入普通用户 " + data[i].phone + "'});" + '"><svg t="*************" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="1449" width="12" height="12"><path d="M449.998901 696.046736 269.13567 515.369747 337.436242 446.99959 449.998901 559.448662 685.163875 324.520071 753.466494 392.890228 449.998901 696.046736Z" fill="#272536" p-id="1450"></path><path d="M511.301082 67.156516c-244.732887 0-443.1274 198.394513-443.1274 443.1274s198.394513 443.1274 443.1274 443.1274 443.1274-198.395536 443.1274-443.1274S756.033969 67.156516 511.301082 67.156516zM511.301082 888.575658c-208.925357 0-378.292766-169.367409-378.292766-378.292766s169.367409-378.291742 378.292766-378.291742c208.925357 0 378.291742 169.367409 378.291742 378.291742S720.226438 888.575658 511.301082 888.575658z" fill="#272536" p-id="1451"></path></svg>&nbsp;通过</a>'
                                +
                                '<a style="color: #ac2247!important;text-shadow: 5px 5px 5px #********;font-weight: bold;" class="card-button common-button" onclick="' + "get_action({name:'event_create',data:{id:" + data[i].id + ",aname:'admin',name:'accounts',event:'upgrade_user',upgrade:0},n:'拒绝转入',e:'取消转入 " + data[i].phone + "'});" + '"><svg t="*************" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="1748" width="12" height="12"><path d="M512.078795 67.718311c-244.732887 0-443.1274 198.394513-443.1274 443.1274s198.394513 443.1274 443.1274 443.1274 443.1274-198.395536 443.1274-443.1274S756.811682 67.718311 512.078795 67.718311zM512.078795 889.138476c-208.925357 0-378.292766-169.367409-378.292766-378.292766s169.367409-378.291742 378.292766-378.291742 378.292766 169.367409 378.292766 378.291742S721.004151 889.138476 512.078795 889.138476z" fill="#272536" p-id="1749"></path><path d="M592.82891 510.592954 706.648189 623.650893c5.572926 5.536087 6.77531 13.363356 2.684132 17.48114l-65.652256 66.092277c-4.091178 4.118807-11.92561 2.969635-17.498536-2.566452L512.361227 591.600943 399.327848 705.393615c-5.535063 5.572926-13.361309 6.774287-17.480116 2.683109l-66.086138-65.645093c-4.118807-4.090155-2.969635-11.924587 2.566452-17.497513l113.033379-113.792673L317.508377 398.052808c-5.572926-5.536087-6.774287-13.362332-2.683109-17.48114l65.652256-66.092277c4.092201-4.118807 11.92561-2.969635 17.499559 2.566452l113.852024 113.089661 113.076358-113.836675c5.535063-5.572926 13.361309-6.774287 17.479093-2.683109l66.086138 65.645093c4.117784 4.091178 2.969635 11.924587-2.566452 17.497513L592.82891 510.592954 592.82891 510.592954z" fill="#272536" p-id="1750"></path></svg>&nbsp;拒绝</a>'


                                : '')



                                + '  </div>'

                                + "</td>";
                            tr += "</tr>";


                            if (data[i].createtype == '1') {

                                tr += "<tr style='background:#efefef;'>";
                                tr += "<td colspan='8' style=' padding:2px  8px;position: sticky;left:0;'>"
                                    + '<span style="background: #fbfaee; padding: 2px 5px; border-radius: 3px;">团人数：' + data[i].data_teamnum + '(' + data[i].grow_teamnum_min + '-' + data[i].grow_teamnum_max + '%) 团收入：' + data[i].data_teamincome + '(' + data[i].grow_teamincome_min + '-' + data[i].grow_teamincome_max + '%) 下级人数：' + data[i].data_invitenum + '(' + data[i].grow_invitenum_min + '-' + data[i].grow_invitenum_max + '%) 直属佣金：' + data[i].data_agentbork + '(' + data[i].grow_agentbork_min + '-' + data[i].grow_agentbork_max + '%)</span>'
                                    + "</td>";
                                tr += "</tr>";

                            }




                            tr += "<tr>";
                            //tr += ("<td>" + data[i].group_id + "</td>");

                            //tr += ('<td style="cursor:pointer;"><input class="good_select" type="checkbox" data-id="' + data[i].id + '" id="checkid_' + data[i].id + '" /><a  ondblclick="openrightBoxpopup(\'accounts_baseinfo.aspx?id=' + data[i].id + '\')" >' + data[i].parent_code + '<span style="color: #b53d10;margin-left: 2px;margin-right:3px;">[' + data[i].id + ']</span></a>'
                            //    + (('<span style="background: #f3ecec;color: #956b0a;font-size: 12px;border-radius: 3px;padding: 1px 4px;margin-right: 5px;font-weight: bold;border: 1px solid #ccc;">' + (data[i].typename == 'th' ? '托号' : data[i].typename == 'user' ? '普通' : data[i].typename == 'super' ? '高级' : data[i].typename == 'reg' ? '新' : '其他') + '</span>'))
                            //    //+ (('<span style="background: #f3ecec;color: #956b0a;font-size: 12px;border-radius: 3px;padding: 1px 4px;margin-right: 5px;font-weight: bold;border: 1px solid #ccc;">' + (data[i].typename == 'th' ? '托号' : data[i].typename == 'user' ? '普通' : data[i].typename == 'super' ? '高级' : data[i].typename == 'reg' ? '新' : '其他') + '</span>') + (typeof (data[i].usertype) == "number" && data[i].usertype == 0 ? '<span style="background: linear-gradient(130deg, #6dc17c, #43c5c0);color: #fff;padding: 1px 10px;border-radius: 3px;margin-right: 3px;font-weight: bold;">新注册</span>' : '') + '<strong style="text-shadow: 5px 5px 5px #0000005c;">' + data[i].phone + '</strong>' + (data[i].relationshop_name == '' ? '' : (data[i].relationshop_name == '直接' ? '<div style="border-radius: 10px;font-size: 12px;font-weight: bold;margin-top: 4px;display: inline-block;padding: 0 5px;background: #61736b;color: yellow;margin-left: 6px;">直属</div>' : data[i].relationshop_name == '间接' ? '<div style="border-radius: 10px;font-size: 12px;font-weight: bold;margin-top: 4px;display: inline-block;padding: 0 5px;background: #e9e9e9;color: #535b57;margin-left: 6px;">间接</div>' : '<div style="border-radius: 10px;font-size: 12px;font-weight: bold;margin-top: 4px;display: inline-block;padding: 0 5px;background: #212020;color: #c3ffaf;margin-left: 6px;">' + data[i].relationshop_name + '</div>') + ('<i class="" style="background: rgb(43 30 22 / 10%);color: #a339e3;white-space: nowrap;border: 1px solid transparent;border-radius: 0.28571429em;font-style: normal;margin-left: 6px;padding: 0 5px;font-weight: bold;">' + data[i].level + '</i>')) + set_online(data[i].online) + (data[i].last_time_text == '' || data[i].online == "在线" ? '' : ' <span style="color:gray;">' + data[i].last_time_text + '</span>'))
                            //    + set_online(data[i].online) + (data[i].last_time_text == '' || data[i].online == "在线" ? '' : ' <span style="color:gray;">' + data[i].last_time_text + '</span>')

                            //    + (get_param('audit') == 1 && data[i].upgrade_state == 1 ?

                            //    '<a style="color: #22AC38!important;text-shadow: 5px 5px 5px #********;font-weight: bold;" class="card-button common-button" onclick="' + "get_action({name:'event_create',data:{id:" + data[i].id + ",aname:'admin',name:'accounts',event:'upgrade_user',upgrade:1},n:'审核通过',e:'转入普通用户 " + data[i].phone + "'});" + '"><svg t="*************" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="1449" width="12" height="12"><path d="M449.998901 696.046736 269.13567 515.369747 337.436242 446.99959 449.998901 559.448662 685.163875 324.520071 753.466494 392.890228 449.998901 696.046736Z" fill="#272536" p-id="1450"></path><path d="M511.301082 67.156516c-244.732887 0-443.1274 198.394513-443.1274 443.1274s198.394513 443.1274 443.1274 443.1274 443.1274-198.395536 443.1274-443.1274S756.033969 67.156516 511.301082 67.156516zM511.301082 888.575658c-208.925357 0-378.292766-169.367409-378.292766-378.292766s169.367409-378.291742 378.292766-378.291742c208.925357 0 378.291742 169.367409 378.291742 378.291742S720.226438 888.575658 511.301082 888.575658z" fill="#272536" p-id="1451"></path></svg>&nbsp;通过</a>'
                            //    +
                            //    '<a style="color: #ac2247!important;text-shadow: 5px 5px 5px #********;font-weight: bold;" class="card-button common-button" onclick="' + "get_action({name:'event_create',data:{id:" + data[i].id + ",aname:'admin',name:'accounts',event:'upgrade_user',upgrade:0},n:'拒绝转入',e:'取消转入 " + data[i].phone + "'});" + '"><svg t="*************" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="1748" width="12" height="12"><path d="M512.078795 67.718311c-244.732887 0-443.1274 198.394513-443.1274 443.1274s198.394513 443.1274 443.1274 443.1274 443.1274-198.395536 443.1274-443.1274S756.811682 67.718311 512.078795 67.718311zM512.078795 889.138476c-208.925357 0-378.292766-169.367409-378.292766-378.292766s169.367409-378.291742 378.292766-378.291742 378.292766 169.367409 378.292766 378.291742S721.004151 889.138476 512.078795 889.138476z" fill="#272536" p-id="1749"></path><path d="M592.82891 510.592954 706.648189 623.650893c5.572926 5.536087 6.77531 13.363356 2.684132 17.48114l-65.652256 66.092277c-4.091178 4.118807-11.92561 2.969635-17.498536-2.566452L512.361227 591.600943 399.327848 705.393615c-5.535063 5.572926-13.361309 6.774287-17.480116 2.683109l-66.086138-65.645093c-4.118807-4.090155-2.969635-11.924587 2.566452-17.497513l113.033379-113.792673L317.508377 398.052808c-5.572926-5.536087-6.774287-13.362332-2.683109-17.48114l65.652256-66.092277c4.092201-4.118807 11.92561-2.969635 17.499559 2.566452l113.852024 113.089661 113.076358-113.836675c5.535063-5.572926 13.361309-6.774287 17.479093-2.683109l66.086138 65.645093c4.117784 4.091178 2.969635 11.924587-2.566452 17.497513L592.82891 510.592954 592.82891 510.592954z" fill="#272536" p-id="1750"></path></svg>&nbsp;拒绝</a>'


                            //    : '') + '</td>');



                            //tr += ("<td class=''><a target='_blank' href='" + data[i].avatar + "'><img class='default-image' src='" + data[i].avatar + "' style='width:28px;height:28px;border-radius: 50%;border: 1px dashed #999;'></a></td>");
                            tr += ("<td class=''>" + data[i].roomNumber + "</td>");
                            tr += ("<td style='color:gray;'>" + (data[i].group_name == '-' ? data[i].group_name : "<span class='button-style style-js'>" + data[i].group_name + "</span>") + "</td>");
                            tr += ("<td class=''>" + data[i].payment_name + "</td>");
                            //tr += ("<td class=''>" + data[i].payment_bankid + "</td>");


                            //tr += ("<td class=''>" + "<b " + (data[i].stock_lend_number <= 0 ? 'style="color:red"' : '') + ">" + data[i].stock_lend_number + "</b>" + (data[i].daily_lend_count != "" ? '<span style="font-weight: bold; font-size: 13px; text-shadow: 5px 5px 5px #4faf6e7d; color: #4faf6e;"> +' + data[i].daily_lend_count + '</span>' : '') + "</td>");
                            //tr += ("<td class=''>" + '<span style="' + (data[i].lend_state == 1 ? 'background: #e7ffec;color: #4a9d19;border: 1px solid #5bb327;' : data[i].lend_state == -1 ? 'background: #fff4f4;color: #b15e5e;border: 1px solid #ab7d7d;' : 'background: #e7e7e7;color: #545554;border: 1px solid #a5a5a5;') + 'font-size: 12px;border-radius: 3px;padding: 1px 4px;margin-left: 5px;font-weight: bold;margin-right: 3px;">' + (data[i].lend_state == 1 ? '免' : data[i].lend_state == -1 ? '审' : '默') + '</span>' + "<b " + (data[i].stock_lend_amount <= 0 ? 'style="color:red"' : '') + ">" + data[i].stock_lend_amount + "</b>" + (data[i].lend_quota != "" ? '<span style="font-weight: bold; font-size: 13px; text-shadow: 5px 5px 5px #4faf6e7d; color: #4faf6e;"> +' + data[i].lend_quota + '</span>' : '') + "</td>");

                            tr += ("<td class=''>" + "<b " + (data[i].stock_number <= 0 ? 'style="color:red"' : '') + ">" + data[i].stock_number + "</b>" + (data[i].daily_number != "" ? '<span style="font-weight: bold; font-size: 13px; text-shadow: 5px 5px 5px #4faf6e7d; color: #4faf6e;"> +' + data[i].daily_number + '</span>' : '') + (data[i].show_type == "number" ? '<span style="background: #f3ecec;color: #956b0a;font-size: 12px;border-radius: 3px;padding: 1px 4px;margin-left: 5px;font-weight: bold;border: 1px solid #ccc;">显示</span>' : '') + "</td>");
                            tr += ("<td class=''>" + "<b " + (data[i].stock_amount <= 0 ? 'style="color:red"' : '') + ">" + parseFloat(data[i].stock_amount).toFixed(0) + "</b>" + (data[i].daily_amount != "" ? '<span style="font-weight: bold; font-size: 13px; text-shadow: 5px 5px 5px #4faf6e7d; color: #4faf6e;"> +' + data[i].daily_amount + '</span>' : '') + (data[i].show_type == "amount" ? '<span style="background: #f3ecec;color: #956b0a;font-size: 12px;border-radius: 3px;padding: 1px 4px;margin-left: 5px;font-weight: bold;border: 1px solid #ccc;">显示</span>' : '') + "</td>");
                            tr += ("<td class=''>" + (data[i].level_name == "" ? '<span style="color:gray;">无</span>' : '<span style="font-size: 12px;color: #eee;padding: 2px 8px;border-radius: 4px;margin-left: 5px;' + (data[i].level_from == "ups" ? 'background: linear-gradient(117deg, ' + (stringToColor(data[i].level_name)) + ', #898071);' : 'background: linear-gradient(117deg, #6c61dd, #2dc9b3);') + '">' + data[i].level_name + '</span>') + "</td>");
                            tr += ("<td class=''>" + data[i].vaild_user_number + "人</td>");
                            tr += ("<td class=''>" + ('<span style="background: #f3ecec;color: #956b0a;font-size: 12px;border-radius: 3px;padding: 1px 4px;margin-right: 5px;font-weight: bold;border: 1px solid #ccc;">' + (data[i].parent_typename == 'th' ? '托号' : data[i].parent_typename == 'user' ? '普通' : data[i].parent_typename == 'super' ? '高级' : data[i].parent_typename == 'reg' ? '新' : '其他') + '</span>') + (typeof (data[i].parent_usertype) == "number" && data[i].parent_usertype == 0 ? '<span style="background: linear-gradient(130deg, #6dc17c, #43c5c0);color: #fff;padding: 1px 10px;border-radius: 3px;margin-right: 3px;font-weight: bold;">新注册</span>' : '') + '<strong style="text-shadow: 5px 5px 5px #0000005c;">' + data[i].parent_phone + '</strong>' + '<span style="color: #b53d10;margin-left: 2px;margin-right:3px;">[' + data[i].parent_userid + ']</span>' + "</td>");
                            //tr += ("<td class=''>" + data[i].parent_user + "</td>");
                            tr += ("<td class='' onclick=\"_modal('结果','游戏金额：" + data[i].upr_total_amount + "，中奖金额：" + data[i].upr_total_okamount + "')\">" + (parseNumber(data[i].upr_total_amount) - parseNumber(data[i].upr_total_okamount)).toFixed(2) + "</td>");
                            tr += ("<td style='color:gray;'>" + (data[i].state == 1 ? "<span  style='font-size: 12px;background: #e9e9e9;color: #3f8d6d;font-weight: bold;padding: 3px 4px;margin-left: 5px;'>正常</span>" : "<span class='button-style style-hs'>冻结</span>") + (data[i].task_state == 1 ? '<span style="font-size: 12px;background: #4b4b4b;color: #eded4d;font-weight: bold;padding: 3px 4px;margin-left: 5px;">免审 ' + data[i].succ_rate + '%</span>' : '') + "</td>");
                            tr += ("<td style='color:gray;'>" + (data[i].limit_ontouch == 1 ? "<span style='color:red;'>不允许</span>" : "<span style='color:green;'>允许</span>") + "</td>");
                            //tr += ("<td class=''>" + (data[i].timeout_second == "" ? "<span style='color:gray;'>无</span>" : data[i].timeout_second + "秒") + "</td>");
                            tr += ("<td class=''>" + '<span style="font-weight: bold; font-size: 13px; text-shadow: 5px 5px 5px #4faf6e7d; color: #4faf6e;">' + (data[i].api_today_amount + data[i].serv_today_amount + data[i].dating_today_amount).toFixed(2) + '</span>' + "</td>");
                            tr += ("<td class=''>" + '<span style="font-weight: bold;font-size: 13px;text-shadow: 5px 5px 5px #7f87817d;color: #a1ada5;">' + (data[i].api_total_amount + data[i].serv_total_amount + data[i].dating_total_amount).toFixed(2) + '</span>' + "</td>");
                            tr += ("<td class=''>" + '<b style="">' + data[i].amount + '</b>' + "</td>");
                            tr += ("<td class=''>" + '<b style="color: #bf4f22;display: flex;align-items: center;">' + data[i].total_award_amount + '<svg t="" style="margin-left: 2px;" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="8646" width="12" height="12"><path d="M494.592 79.36h36.352c3.072 1.024 6.656 0.512 9.728 1.024 28.16 2.048 56.32 6.144 83.456 13.824 55.296 14.848 105.472 39.424 150.528 74.24 27.648 21.504 52.736 45.568 74.24 72.704 28.16 34.816 50.176 72.704 66.048 114.176 15.872 40.96 25.088 83.456 28.16 127.488 0 3.072 0 6.144 1.024 9.216v36.864c-0.512 0.512-0.512 1.024-0.512 1.536-0.512 8.704-1.024 17.408-2.048 26.624-2.56 25.088-7.68 49.664-14.848 74.24-14.848 51.2-38.912 98.304-71.168 140.8-20.48 27.136-44.544 51.2-70.656 72.704-35.328 28.672-74.24 51.712-116.736 68.096-40.96 15.872-83.456 25.088-127.488 28.16-3.072 0-6.144 0-9.216 1.024h-36.864c-0.512-0.512-1.024-0.512-1.536-0.512-8.704-0.512-17.408-1.024-26.624-2.048-28.672-3.072-56.832-9.216-84.48-17.92-47.616-15.36-91.136-37.888-130.56-68.096-27.136-20.992-51.712-44.544-73.216-70.656-28.672-35.328-51.712-74.24-68.096-116.736-15.872-40.96-25.088-83.456-28.16-127.488 0-3.072 0-6.144-1.024-8.704v-37.376c0.512-0.512 0.512-1.024 0.512-1.536 0.512-8.704 1.024-17.408 2.048-26.112 3.072-29.184 9.216-58.368 18.432-86.528 24.064-74.24 65.536-137.216 123.904-189.44 38.912-34.816 82.944-61.952 131.584-80.384 40.96-15.872 83.456-25.088 127.488-27.648 3.584-0.512 6.656-0.512 9.728-1.536z m18.432 649.728h236.544c9.216-0.512 17.92-7.168 21.504-15.872 3.072-9.216 0.512-19.968-6.656-26.112-5.632-5.12-12.8-6.656-19.968-6.656H305.664c-2.56 0-3.072-0.512-3.072-3.584V324.608c0-3.584 0-7.168-0.512-10.752-1.024-13.312-14.336-24.064-28.16-21.504-12.288 2.048-20.48 12.288-20.48 25.088v388.096c0.512 12.8 11.264 23.04 23.552 23.04 78.848 0.512 157.696 0.512 236.032 0.512z m-32.768-230.4v133.12c0 13.312 10.24 24.576 23.552 24.576h49.664c9.728 0 16.896-5.12 21.504-13.824 2.048-4.096 2.048-8.704 2.048-13.312V367.104 363.52c-0.512-11.776-10.24-22.016-22.016-22.016-17.408-0.512-35.328-0.512-52.736 0-12.288 0-22.016 11.264-22.016 23.04v134.144z m234.496 48.64V461.824c0-12.288-10.752-23.552-23.552-23.552h-49.152c-3.584 0-7.168 0.512-10.24 2.56-9.728 5.12-14.336 13.312-14.336 24.576v163.84c0 2.56 0 4.608 0.512 7.168 1.536 11.776 11.776 20.992 23.04 20.992h50.176c12.288 0 23.04-11.264 23.04-23.552 0.512-29.184 0.512-57.856 0.512-86.528z m-323.584 109.056h23.04c14.848 0 25.6-10.24 25.6-25.6v-45.568c0-15.872-10.24-26.112-25.6-26.112H368.64c-8.192 0-15.36 2.56-20.48 9.216-3.584 4.608-5.12 9.728-5.12 15.872 0.512 8.704 0 17.408 0 26.112v23.04c0 11.776 11.264 22.528 23.552 22.528 8.192 0.512 16.384 0.512 24.576 0.512z" fill="#FE9A00" p-id="8647"></path><path d="M513.024 729.088h-235.52c-12.288 0-23.04-10.24-23.552-23.04v-2.56-385.536c0-13.312 8.192-23.04 20.48-25.088 13.312-2.56 26.624 7.68 28.16 21.504 0.512 3.584 0.512 7.168 0.512 10.752v352.256c0 2.56 0.512 3.584 3.072 3.584h438.784c7.168 0 14.336 1.536 19.968 6.656 7.168 6.144 9.728 16.896 6.656 26.112-3.072 9.216-11.776 15.872-21.504 15.872h-5.12c-77.824-0.512-154.624-0.512-231.936-0.512z" fill="#FEFEFE" p-id="8648"></path><path d="M480.256 498.688V364.544c0-11.776 10.24-23.04 22.016-23.04 17.408-0.512 35.328-0.512 52.736 0 11.776 0 21.504 10.24 22.016 22.016V629.248c0 4.608 0 9.216-2.048 13.312-4.608 8.704-11.776 13.824-21.504 13.824h-49.664c-13.312 0-23.552-11.264-23.552-24.576v-87.552-45.568zM714.752 547.328v85.504c0 12.288-10.752 23.552-23.04 23.552h-50.176c-11.776 0-22.016-9.216-23.04-20.992-0.512-2.56-0.512-4.608-0.512-7.168v-163.84c0-10.752 4.096-19.456 14.336-24.576 3.072-1.536 6.656-2.56 10.24-2.56h49.152c12.8 0 23.552 11.264 23.552 23.552-0.512 29.696-0.512 57.856-0.512 86.528zM391.168 656.384h-25.088c-12.288 0-23.552-10.752-23.552-22.528v-23.04c0-8.704 0.512-17.408 0-26.112-0.512-6.144 1.536-11.264 5.12-15.872 5.12-6.656 12.288-9.216 20.48-9.216h45.568c15.872 0 25.6 10.752 25.6 26.112v45.568c0 15.36-10.24 25.6-25.6 25.6-7.168-0.512-14.848-0.512-22.528-0.512z" fill="#FEFEFE" p-id="8649"></path></svg></b>' + "</td>");
                            tr += ("<td class=''>" + '<b style="color:#bd6c6c;">' + data[i].exp_amount + '</b>' + "</td>");
                            tr += ("<td class=''>" + '<b style="color:#5a5b5c;">' + data[i].today_sell_amount + '</b><img src="/static/images/coin.png" alt="" height="12" width="12" style="margin-left: 5px;">' + "</td>");
                            tr += ("<td class=''>" + '<b style="color:#5a5b5c;">' + data[i].sell_amount + '</b><img src="/static/images/coin.png" alt="" height="12" width="12" style="margin-left: 5px;">' + "</td>");
                            tr += ("<td class=''>" + '<b style="color:#5a5b5c;">' + data[i].tixian_daily_amount + '</b>' + "</td>");
                            tr += ("<td class=''>" + '<b style="color:#5a5b5c;">' + data[i].tixian_total_amount + '</b>' + "</td>");
                            tr += ("<td class=''>" + '<b style="color:#5a5b5c;">' + (parseFloat((data[i].api_total_amount + data[i].serv_total_amount + data[i].dating_total_amount)) - parseFloat(data[i].sell_amount)).toFixed(2) + '</b>' + "</td>");
                            tr += ("<td class=''>" + '<b style="color:#b91515;">' + data[i].trans_amount + '</b>' + "</td>");
                            tr += ("<td class=''>" + '<b style="color:#b91515;">' + data[i].freeze_amount + '</b>' + "</td>");
                            //tr += ("<td class=''>" + '<b style="color:#b91515;">' + data[i].reward_amount + '</b>' + "</td>");
                            //tr += ("<td class=''>" + '<b style="color:#b18918;">' + data[i].task_amount + '</b>' + "</td>");
                            //tr += ("<td class=''>" + '<b style="color:#b18918;">' + data[i].wheel_amount + (data[i].zp_number == "" || data[i].zp_number == 0 ? "" : "<span style='color:green;'>(+" + data[i].zp_number + "次)</span>") + '</b>' + "</td>");
                            //tr += ("<td class=''>" + '<b style="color:#b18918;">' + data[i].activity_amount + '</b>' + "</td>");
                            //tr += ("<td class=''>" + '<b style="color:#b18918;">' + data[i].receive_redbag_amount + '</b>' + "</td>");
                            //tr += ("<td class=''>" + '<b style="color:#5415b9;">' + parseFloat(data[i].brok_amount).toFixed(2) + '</b>' + "</td>");
                            //tr += ("<td class=''>" + '<b style="color:gray;">' + (data[i].partners_amount ? parseFloat(data[i].partners_amount).toFixed(2) : 0.00) + '</b>' + "</td>");
                            //tr += ("<td class='' style='font-weight: bold; color: #c56231; text-shadow: 5px 5px 5px #00000059; '>" + (parseNumber(data[i].play_brok) + parseNumber(data[i].rec_play_brok)).toFixed(2) + "</td>");

                            //tr += ("<td class=''>" + '<b style="color:#5a5b5c;">' + data[i].lend_reward + '</b>' + "</td>");
                            tr += ("<td class=''>" + '<b style="color:#5a5b5c;">' + '<span style="font-weight: bold;font-size: 13px;text-shadow: 5px 5px 5px #af4f897d;color: #af4f76;">' + (parseNumber(data[i].reward_amount) + parseNumber(data[i].task_amount) + parseNumber(data[i].wheel_amount) + parseNumber(data[i].activity_amount) + parseNumber(data[i].receive_redbag_amount) + parseNumber(data[i].brok_amount) + parseNumber(data[i].partners_amount) + parseNumber(data[i].lend_reward) + parseNumber(data[i].play_brok) + parseNumber(data[i].rec_play_brok) + parseNumber(data[i].total_award_amount)).toFixed(2) + '</span>' + '</b>' + "</td>");
                            tr += ("<td class=''>" + '<b style="color:#5a5b5c;">' + data[i].login_time + '</b>' + "</td>");
                            tr += ("<td class=''>" + '<b style="color:#5a5b5c;">' + data[i].create_time + '</b>' + "</td>");




                            tr += ("<td class='action-cell '>"
                                + ' <div class="card-toolbar">'
                               // + '<a class="card-button common-button" ' + "onclick=\"" + "get_action({name:'event_create',data:{id:" + data[i].id + ",aname:'admin',name:'" + _action + "',event:'islock',islock:1},n:'锁定用户',e:'锁定用户 " + data[i].phone + "'});" + "\"" + '  style="color:#888!important;"><svg t="1698082343502" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="4855" width="12" height="12"><path d="M968.499 676.076l-28.112 0c0.113-1.197 0.339-2.303 0.339-3.522l0-60.605c0-77.404-62.93-140.357-140.334-140.357-77.359 0-140.312 62.953-140.312 140.357l0 60.605c0 1.242 0.248 2.348 0.361 3.522l-16.574 0c-25.809 0-46.786 20.999-46.786 46.786l0 223.157c0 25.854 20.999 46.831 46.786 46.831l324.654 0c25.786 0 46.786-20.999 46.786-46.831l0-223.157c-0.023-25.786-21.044-46.786-46.808-46.786l0 0zM731.703 672.553l0-60.605c0-37.934 30.799-68.756 68.666-68.756 37.889 0 68.711 30.822 68.711 68.756l0 60.605c0 1.242 0.271 2.348 0.384 3.522l-138.099 0c0.09-1.197 0.339-2.281 0.339-3.522l0 0zM979.45 946.019c0 6.097-4.922 10.974-10.951 10.974l-324.654 0c-6.051 0-10.974-4.9-10.974-10.974l0-223.157c0-6.051 4.945-10.974 10.974-10.974l324.654 0c6.051 0 10.951 4.945 10.951 10.974l0 223.157zM806.149 822.574c-14.79 0-26.825 11.99-26.825 26.825l0 51.64 53.627 0 0-51.64c0.023-14.812-11.99-26.825-26.802-26.825l0 0zM655.902 617.3c-17.025-6.006-34.299-11.2-51.821-15.558-2.552-1.332-5.442-2.123-8.377-2.123l-9.19 0c-2.868 0-5.735 0.7-8.309 2.077-6.187 3.342-13.187 6.164-20.232 8.919-5.442 2.168-11.2 3.884-17.003 5.645-2.642 0.7-5.306 1.535-7.993 2.303-5.035 1.377-10.251 2.461-15.467 3.477-4.606 0.926-9.303 1.671-14.022 2.348-4.042 0.564-8.016 1.197-12.125 1.581-9.28 0.971-18.741 1.581-28.225 1.581-3.545 0-7.045-0.452-10.567-0.61-38.837-1.377-76.523-10.138-104.59-25.222-2.506-1.377-5.419-2.077-8.309-2.077l-6.684 0c-1.355 0-2.732 0.135-4.087 0.452-103.958 24.612-200.578 78.194-279.54 154.763-3.364 3.297-5.284 7.948-5.284 12.667l0 62.93c0 9.755 7.858 161.356 17.612 161.356l522.613 0c-20.525-42.112-32.064-89.394-32.064-139.409 0-93.097 39.966-176.891 103.664-235.102l0 0zM265.111 320.758c0 18.945 14.519 34.299 32.38 34.299l0.587 0c6.706 90.5 77.969 181.023 165.059 181.023 4.968 0 9.845-0.361 14.722-0.971 1.716-0.181 3.455-0.519 5.216-0.79 3.003-0.474 5.984-1.061 8.942-1.716 2.168-0.519 4.245-1.152 6.435-1.716 2.326-0.655 4.629-1.445 6.932-2.213 2.484-0.881 4.968-1.716 7.384-2.777 1.423-0.519 2.822-1.242 4.2-1.852 61.011-27.457 105.968-98.11 111.251-168.966l0.474 0c17.928 0 32.425-15.354 32.425-34.299 0-13.977-7.903-26.035-19.125-31.386 6.458-20.322 10.071-42.021 10.071-64.759 0-91.742-58.211-168.966-137.625-192.72-1.31-0.361-2.552-0.723-3.839-1.084-6.074-1.626-12.238-2.913-18.47-4.019-1.919-0.316-3.771-0.655-5.713-0.903-7.655-0.971-15.4-1.671-23.28-1.671-104.387 0-188.994 89.755-188.994 200.397 0 22.738 3.59 44.437 10.161 64.759-11.29 5.329-19.193 17.364-19.193 31.364l0 0zM265.111 320.758z" fill="#41416E" p-id="4856"></path></svg>&nbsp;体验金</a>'
                               // + (get_param('audit') == 1 && data[i].upgrade_state == 1 ? '' : (
                               // (data[i].islock != 1 ? '<a class="card-button common-button" ' + "onclick=\"" + "get_action({name:'event_create',data:{id:" + data[i].id + ",aname:'admin',name:'" + _action + "',event:'islock',islock:1},n:'锁定用户',e:'锁定用户 " + data[i].phone + "'});" + "\"" + '  style="color:#888!important;"><svg t="1698082343502" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="4855" width="12" height="12"><path d="M968.499 676.076l-28.112 0c0.113-1.197 0.339-2.303 0.339-3.522l0-60.605c0-77.404-62.93-140.357-140.334-140.357-77.359 0-140.312 62.953-140.312 140.357l0 60.605c0 1.242 0.248 2.348 0.361 3.522l-16.574 0c-25.809 0-46.786 20.999-46.786 46.786l0 223.157c0 25.854 20.999 46.831 46.786 46.831l324.654 0c25.786 0 46.786-20.999 46.786-46.831l0-223.157c-0.023-25.786-21.044-46.786-46.808-46.786l0 0zM731.703 672.553l0-60.605c0-37.934 30.799-68.756 68.666-68.756 37.889 0 68.711 30.822 68.711 68.756l0 60.605c0 1.242 0.271 2.348 0.384 3.522l-138.099 0c0.09-1.197 0.339-2.281 0.339-3.522l0 0zM979.45 946.019c0 6.097-4.922 10.974-10.951 10.974l-324.654 0c-6.051 0-10.974-4.9-10.974-10.974l0-223.157c0-6.051 4.945-10.974 10.974-10.974l324.654 0c6.051 0 10.951 4.945 10.951 10.974l0 223.157zM806.149 822.574c-14.79 0-26.825 11.99-26.825 26.825l0 51.64 53.627 0 0-51.64c0.023-14.812-11.99-26.825-26.802-26.825l0 0zM655.902 617.3c-17.025-6.006-34.299-11.2-51.821-15.558-2.552-1.332-5.442-2.123-8.377-2.123l-9.19 0c-2.868 0-5.735 0.7-8.309 2.077-6.187 3.342-13.187 6.164-20.232 8.919-5.442 2.168-11.2 3.884-17.003 5.645-2.642 0.7-5.306 1.535-7.993 2.303-5.035 1.377-10.251 2.461-15.467 3.477-4.606 0.926-9.303 1.671-14.022 2.348-4.042 0.564-8.016 1.197-12.125 1.581-9.28 0.971-18.741 1.581-28.225 1.581-3.545 0-7.045-0.452-10.567-0.61-38.837-1.377-76.523-10.138-104.59-25.222-2.506-1.377-5.419-2.077-8.309-2.077l-6.684 0c-1.355 0-2.732 0.135-4.087 0.452-103.958 24.612-200.578 78.194-279.54 154.763-3.364 3.297-5.284 7.948-5.284 12.667l0 62.93c0 9.755 7.858 161.356 17.612 161.356l522.613 0c-20.525-42.112-32.064-89.394-32.064-139.409 0-93.097 39.966-176.891 103.664-235.102l0 0zM265.111 320.758c0 18.945 14.519 34.299 32.38 34.299l0.587 0c6.706 90.5 77.969 181.023 165.059 181.023 4.968 0 9.845-0.361 14.722-0.971 1.716-0.181 3.455-0.519 5.216-0.79 3.003-0.474 5.984-1.061 8.942-1.716 2.168-0.519 4.245-1.152 6.435-1.716 2.326-0.655 4.629-1.445 6.932-2.213 2.484-0.881 4.968-1.716 7.384-2.777 1.423-0.519 2.822-1.242 4.2-1.852 61.011-27.457 105.968-98.11 111.251-168.966l0.474 0c17.928 0 32.425-15.354 32.425-34.299 0-13.977-7.903-26.035-19.125-31.386 6.458-20.322 10.071-42.021 10.071-64.759 0-91.742-58.211-168.966-137.625-192.72-1.31-0.361-2.552-0.723-3.839-1.084-6.074-1.626-12.238-2.913-18.47-4.019-1.919-0.316-3.771-0.655-5.713-0.903-7.655-0.971-15.4-1.671-23.28-1.671-104.387 0-188.994 89.755-188.994 200.397 0 22.738 3.59 44.437 10.161 64.759-11.29 5.329-19.193 17.364-19.193 31.364l0 0zM265.111 320.758z" fill="#41416E" p-id="4856"></path></svg>&nbsp;锁屏</a>' : '<a class="card-button common-button" ' + "onclick=\"" + "get_action({name:'event_create',data:{id:" + data[i].id + ",aname:'admin',name:'" + _action + "',event:'islock',islock:0},n:'用户解除锁定',e:'解除锁定用户 " + data[i].phone + "'});" + "\"" + ' style="color:red!important;"><svg t="1698082343502" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="4855" width="12" height="12"><path d="M968.499 676.076l-28.112 0c0.113-1.197 0.339-2.303 0.339-3.522l0-60.605c0-77.404-62.93-140.357-140.334-140.357-77.359 0-140.312 62.953-140.312 140.357l0 60.605c0 1.242 0.248 2.348 0.361 3.522l-16.574 0c-25.809 0-46.786 20.999-46.786 46.786l0 223.157c0 25.854 20.999 46.831 46.786 46.831l324.654 0c25.786 0 46.786-20.999 46.786-46.831l0-223.157c-0.023-25.786-21.044-46.786-46.808-46.786l0 0zM731.703 672.553l0-60.605c0-37.934 30.799-68.756 68.666-68.756 37.889 0 68.711 30.822 68.711 68.756l0 60.605c0 1.242 0.271 2.348 0.384 3.522l-138.099 0c0.09-1.197 0.339-2.281 0.339-3.522l0 0zM979.45 946.019c0 6.097-4.922 10.974-10.951 10.974l-324.654 0c-6.051 0-10.974-4.9-10.974-10.974l0-223.157c0-6.051 4.945-10.974 10.974-10.974l324.654 0c6.051 0 10.951 4.945 10.951 10.974l0 223.157zM806.149 822.574c-14.79 0-26.825 11.99-26.825 26.825l0 51.64 53.627 0 0-51.64c0.023-14.812-11.99-26.825-26.802-26.825l0 0zM655.902 617.3c-17.025-6.006-34.299-11.2-51.821-15.558-2.552-1.332-5.442-2.123-8.377-2.123l-9.19 0c-2.868 0-5.735 0.7-8.309 2.077-6.187 3.342-13.187 6.164-20.232 8.919-5.442 2.168-11.2 3.884-17.003 5.645-2.642 0.7-5.306 1.535-7.993 2.303-5.035 1.377-10.251 2.461-15.467 3.477-4.606 0.926-9.303 1.671-14.022 2.348-4.042 0.564-8.016 1.197-12.125 1.581-9.28 0.971-18.741 1.581-28.225 1.581-3.545 0-7.045-0.452-10.567-0.61-38.837-1.377-76.523-10.138-104.59-25.222-2.506-1.377-5.419-2.077-8.309-2.077l-6.684 0c-1.355 0-2.732 0.135-4.087 0.452-103.958 24.612-200.578 78.194-279.54 154.763-3.364 3.297-5.284 7.948-5.284 12.667l0 62.93c0 9.755 7.858 161.356 17.612 161.356l522.613 0c-20.525-42.112-32.064-89.394-32.064-139.409 0-93.097 39.966-176.891 103.664-235.102l0 0zM265.111 320.758c0 18.945 14.519 34.299 32.38 34.299l0.587 0c6.706 90.5 77.969 181.023 165.059 181.023 4.968 0 9.845-0.361 14.722-0.971 1.716-0.181 3.455-0.519 5.216-0.79 3.003-0.474 5.984-1.061 8.942-1.716 2.168-0.519 4.245-1.152 6.435-1.716 2.326-0.655 4.629-1.445 6.932-2.213 2.484-0.881 4.968-1.716 7.384-2.777 1.423-0.519 2.822-1.242 4.2-1.852 61.011-27.457 105.968-98.11 111.251-168.966l0.474 0c17.928 0 32.425-15.354 32.425-34.299 0-13.977-7.903-26.035-19.125-31.386 6.458-20.322 10.071-42.021 10.071-64.759 0-91.742-58.211-168.966-137.625-192.72-1.31-0.361-2.552-0.723-3.839-1.084-6.074-1.626-12.238-2.913-18.47-4.019-1.919-0.316-3.771-0.655-5.713-0.903-7.655-0.971-15.4-1.671-23.28-1.671-104.387 0-188.994 89.755-188.994 200.397 0 22.738 3.59 44.437 10.161 64.759-11.29 5.329-19.193 17.364-19.193 31.364l0 0zM265.111 320.758z" fill="#41416E" p-id="4856"></path></svg>&nbsp;解锁</a>')
                               // + '<a class="card-button common-button" ' + "onclick='edit(" + edtext({ aname: 'admin', title: _actionName + "充值", action: _action, id: data[i].id, data: point_model }) + ")'" + '><svg t="1688831098290" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="22140" width="16" height="16" style="position:relative;top:3px;"><path d="M739.1744 155.1872a425.02144 425.02144 0 0 1 65.4848 227.1744c0 235.8272-191.1808 427.0592-427.0592 427.0592-84.224 0-162.6624-24.4736-228.8128-66.56 75.5712 120.064 209.2032 199.8848 361.5744 199.8848 235.8272 0 427.0592-191.1808 427.0592-427.0592 0-151.6544-79.1552-284.7232-198.2464-360.4992z" fill="#ffa115" p-id="22141"></path><path d="M510.3616 973.4656c-252.416 0-457.7792-205.3632-457.7792-457.7792s205.3632-457.7792 457.7792-457.7792 457.7792 205.3632 457.7792 457.7792-205.3632 457.7792-457.7792 457.7792z m0-854.1184c-218.5216 0-396.3392 177.7664-396.3392 396.3392S291.84 912.0256 510.3616 912.0256s396.3392-177.7664 396.3392-396.3392-177.8176-396.3392-396.3392-396.3392z" fill="#474A54" p-id="22142"></path><path d="M655.6672 585.216h-116.6336v-49.9712h116.6336c16.9472 0 30.72-13.7728 30.72-30.72s-13.7728-30.72-30.72-30.72h-80.3328l102.3488-104.96c11.8272-12.1344 11.6224-31.5904-0.5632-43.4176-12.1344-11.8272-31.5904-11.6224-43.4176 0.5632L508.3136 454.5536 382.9248 325.9392a30.72 30.72 0 0 0-43.4176-0.5632 30.72 30.72 0 0 0-0.5632 43.4176l102.3488 104.96H360.96c-16.9472 0-30.72 13.7728-30.72 30.72s13.7728 30.72 30.72 30.72h116.6336v49.9712H360.96c-16.9472 0-30.72 13.7728-30.72 30.72s13.7728 30.72 30.72 30.72h116.6336v84.6336c0 16.9472 13.7728 30.72 30.72 30.72s30.72-13.7728 30.72-30.72V646.656h116.6336c16.9472 0 30.72-13.7728 30.72-30.72s-13.7728-30.72-30.72-30.72z" fill="#474A54" p-id="22143"></path></svg>&nbsp;充值</a>'
                               // + '<a class="card-button common-button" ' + "onclick='edit(" + edtext({ aname: 'admin', title: _actionName + "修改", action: _action, id: data[i].id, data: modify_model }) + ")'" + '><svg  class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg"  width="16" height="16" style="position:relative;top:3px;"><path d="M358.165868 554.624796c-0.533143 0.680499-1.066285 1.391696-1.303692 2.251274l-41.104163 150.700257c-2.400676 8.772804 0.059352 18.226107 6.550183 24.892947 4.860704 4.742001 11.261485 7.350408 18.077727 7.350408 2.252297 0 4.504594-0.267083 6.727215-0.860601l149.630902-40.808428c0.23843 0 0.357134 0.207731 0.534166 0.207731 1.718131 0 3.408633-0.62217 4.683672-1.927909l400.113747-400.054395c11.883655-11.897981 18.404162-28.109198 18.404162-45.74281 0-19.989263-8.476045-39.963177-23.324218-54.767348l-37.786605-37.844933c-14.81645-14.848173-34.822087-23.338544-54.797024-23.338544-17.631566 0-33.842783 6.520507-45.75816 18.388812L358.758362 553.232077C358.344946 553.615816 358.462626 554.179658 358.165868 554.624796M862.924953 257.19778l-39.742143 39.71349-64.428382-65.451688 39.180348-39.179324c6.193049-6.222725 18.194384-5.318122 25.308409 1.822508l37.813211 37.845956c3.943822 3.941775 6.195096 9.18622 6.195096 14.372336C867.223862 250.574942 865.710392 254.42769 862.924953 257.19778M429.322487 560.907896l288.712541-288.728914 64.459081 65.49569L494.314711 625.838721 429.322487 560.907896zM376.718409 677.970032l20.863167-76.580143 55.656601 55.657624L376.718409 677.970032z" fill="#e6e6e6" p-id="1994"></path><path d="M888.265084 415.735539c-15.144932 0-27.562752 12.313443-27.620058 27.665083l0 372.98283c0 19.559475-15.885805 35.444257-35.475979 35.444257L194.220958 851.827709c-19.559475 0-35.504632-15.883759-35.504632-35.444257L158.716326 207.602222c0-19.575848 15.945157-35.474956 35.504632-35.474956l406.367171 0c15.231913 0 27.592428-12.371772 27.592428-27.606755 0-15.202237-12.360516-27.590382-27.592428-27.590382L190.013123 116.930129c-47.684022 0-86.49291 38.779212-86.49291 86.49291L103.520213 820.59231c0 47.713698 38.808888 86.47756 86.49291 86.47756l639.334083 0c47.715745 0 86.509283-38.763862 86.509283-86.47756L915.856489 443.222567C915.794068 428.048983 903.408993 415.735539 888.265084 415.735539" fill="#e6e6e6" p-id="1995"></path></svg>&nbsp;编辑</a>'
                               // //+ '&nbsp;<a class="card-button common-button" ' + "onclick=\"" + "get_action({name:'event_create',data:{id:" + data[i].id + ",aname:'admin',name:'accounts',event:'delete'},n:'删除用户',e:'删除用户 " + data[i].phone + "<br>【账户余额】：" + data[i].amount + "（确认将完成订单）'});" + "\"" + '><svg t="*************" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="1699" width="16" height="16" style="position:relative;top:3px;"><path d="M612.693333 170.666667l8.533334 42.666666H402.773333l8.533334-42.666666h201.386666z m-293.632 25.941333a86.186667 86.186667 0 0 0-1.706666 16.725333H170.666667a42.666667 42.666667 0 0 0 0 85.333334h682.666666a42.666667 42.666667 0 1 0 0-85.333334h-146.730666c0-5.504-0.554667-11.093333-1.706667-16.725333l-8.533333-42.666667A85.333333 85.333333 0 0 0 612.778667 85.333333H411.306667a85.333333 85.333333 0 0 0-83.712 68.608l-8.533334 42.666667zM426.666667 469.333333a42.666667 42.666667 0 0 0-42.666667 42.666667v170.666667a42.666667 42.666667 0 1 0 85.333333 0v-170.666667a42.666667 42.666667 0 0 0-42.666666-42.666667z m128 42.666667a42.666667 42.666667 0 1 1 85.333333 0v170.666667a42.666667 42.666667 0 1 1-85.333333 0v-170.666667zM255.402667 384A42.666667 42.666667 0 0 1 298.666667 426.069333L301.738667 640H301.653333a42.666667 42.666667 0 1 1-85.290666-2.176l-2.986667-210.56A42.666667 42.666667 0 0 1 255.36 384z m51.925333 396.586667h0.042667A103.381333 103.381333 0 0 0 406.698667 853.333333h210.602666c54.186667 0 98.56-40.789333 103.210667-92.757333L725.333333 426.069333a42.666667 42.666667 0 0 1 85.333334 1.194667l-4.864 337.365333-0.128 1.365334C798.08 863.872 715.562667 938.666667 617.301333 938.666667H406.698667a188.885333 188.885333 0 0 1-180.096-130.389334 42.666667 42.666667 0 0 1 80.725333-27.648z" fill="#14101C" p-id="1700"></path></svg>&nbsp;删除</a>'
                               //   + '<a class="card-button common-button" ' + "onclick=\"" + "open_new_page('transaction_list.aspx?userid=" + data[i].id + "','" + data[i].phone + " - 交易明细');" + "\"" + '><svg t="1695233208464" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="3803" width="12" height="12"><path d="M963.584 1024H60.416c-13.897143-4.900571-27.501714-10.020571-38.4-20.699429-11.410286-11.190857-17.115429-25.161143-22.016-39.716571V60.416C4.900571 46.518857 10.020571 32.914286 20.699429 22.016 31.890286 10.605714 45.860571 4.900571 60.416 0h903.168c13.897143 4.900571 27.501714 10.020571 38.4 20.699429 11.410286 11.190857 17.115429 25.161143 22.016 39.716571v903.168c-4.900571 14.555429-10.605714 28.525714-21.942857 39.716571-10.971429 10.678857-24.649143 15.798857-38.473143 20.699429z m-13.165714-950.125714H73.874286v876.397714h876.397714V73.874286z" fill="#2E4C91" p-id="3804"></path><path d="M805.522286 218.477714v68.242286H368.64v-68.242286h436.882286M805.522286 368.64v68.242286H368.64V368.64h436.882286M805.522286 587.117714v68.242286H368.64v-68.242286h436.882286M368.64 805.522286v-68.242286h436.882286v68.242286H368.64M286.72 286.72h-68.242286v-68.242286h68.242286v68.242286M286.72 737.28v68.242286h-68.242286v-68.242286h68.242286M286.72 368.64v68.242286h-68.242286V368.64h68.242286M286.72 655.36h-68.242286v-68.242286h68.242286v68.242286" fill="#2E4C91" p-id="3805"></path></svg>&nbsp;明细</a>'

                               //    //+ '<a class="card-button common-button" ' + "onclick='edit(" + edtext({ aname: 'admin', title: _actionName + "验证码发送", action: _action, id: data[i].id, data: sms_model }) + ")'" + "\"" + '><svg t="1695736389328" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="5624" width="12" height="12"><path d="M112.658963 273.73037c1.232593 1.289481 2.522074 2.522074 3.887407 3.735704l310.423704 275.911111a128 128 0 0 0 170.059852 0l310.423704-275.911111c1.365333-1.21363 2.654815-2.446222 3.868444-3.716741 0.455111 3.299556 0.682667 6.674963 0.682667 10.10726v444.378074a75.851852 75.851852 0 0 1-75.851852 75.851852H187.847111a75.851852 75.851852 0 0 1-75.851852-75.851852V283.856593c0-2.541037 0.132741-5.044148 0.37926-7.528297z" fill="#279CFF" p-id="5625"></path><path d="M562.384593 476.918519L864.938667 208.023704H159.061333L461.615407 476.918519a75.851852 75.851852 0 0 0 100.769186 0z" fill="#279CFF" fill-opacity=".5" p-id="5626"></path></svg>&nbsp;短信</a>'

                               // + (data[i].chat_nospeak != 1 ? '<a class="card-button common-button" ' + "onclick=\"" + "get_action({name:'event_create',data:{id:" + data[i].id + ",aname:'admin',name:'" + _action + "',event:'chat_speak',chat_nospeak:1},n:'禁言用户',e:'禁言用户 " + data[i].phone + "'});" + "\"" + '  style="color:#888!important;"><svg t="1697104177962" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="6718" width="12" height="12"><path d="M0 0h1024v1024H0z" fill="#FFFFFF" fill-opacity="0" p-id="6719"></path><path d="M825.728 588.8a180.48 180.48 0 0 1-25.6-93.141333v-112.213334c0-33.109333-5.76-66.005333-17.066667-97.109333l93.184-93.184a32 32 0 1 0-45.269333-45.269333l-682.666667 682.666666a32 32 0 1 0 45.226667 45.269334l75.946667-75.946667h126.805333c-0.341333 3.584-1.408 7.04-1.408 10.666667a117.333333 117.333333 0 1 0 234.666667 0c0-3.626667-1.066667-7.082667-1.408-10.666667H797.866667a87.424 87.424 0 0 0 74.837333-132.266667L825.728 588.8z m-260.266667 221.866667a53.333333 53.333333 0 0 1-106.666666 0c0.085333-3.584 0.554667-7.168 1.408-10.666667h103.850666c0.810667 3.498667 1.237333 7.082667 1.28 10.666667h0.128z m252.757334-86.442667a22.784 22.784 0 0 1-20.224 11.733333H333.226667l398.037333-398.037333c3.157333 15.018667 4.693333 30.293333 4.736 45.610667v112.213333a244.650667 244.650667 0 0 0 34.816 126.08l46.933333 78.506667c4.522667 7.253333 4.650667 16.469333 0.341334 23.893333h0.128z m-652.8-80.170667l32.853333-55.04c16.853333-28.16 25.728-60.416 25.6-93.269333v-112.213333a288.554667 288.554667 0 0 1 466.474667-226.133334 32 32 0 1 1-39.509334 50.346667 224.512 224.512 0 0 0-362.965333 175.786667v112.213333a244.266667 244.266667 0 0 1-34.901333 126.165333l-32.768 54.912a32 32 0 1 1-54.954667-32.725333l0.170667-0.042667z" fill="#41416E" p-id="6720"></path></svg>&nbsp;禁言</a>' : '<a class="card-button common-button" ' + "onclick=\"" + "get_action({name:'event_create',data:{id:" + data[i].id + ",aname:'admin',name:'" + _action + "',event:'chat_speak',chat_nospeak:0},n:'用户解除禁言',e:'解除禁言用户 " + data[i].phone + "'});" + "\"" + ' style="color:red!important;"><svg t="1697104177962" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="6718" width="12" height="12"><path d="M0 0h1024v1024H0z" fill="#FFFFFF" fill-opacity="0" p-id="6719"></path><path d="M825.728 588.8a180.48 180.48 0 0 1-25.6-93.141333v-112.213334c0-33.109333-5.76-66.005333-17.066667-97.109333l93.184-93.184a32 32 0 1 0-45.269333-45.269333l-682.666667 682.666666a32 32 0 1 0 45.226667 45.269334l75.946667-75.946667h126.805333c-0.341333 3.584-1.408 7.04-1.408 10.666667a117.333333 117.333333 0 1 0 234.666667 0c0-3.626667-1.066667-7.082667-1.408-10.666667H797.866667a87.424 87.424 0 0 0 74.837333-132.266667L825.728 588.8z m-260.266667 221.866667a53.333333 53.333333 0 0 1-106.666666 0c0.085333-3.584 0.554667-7.168 1.408-10.666667h103.850666c0.810667 3.498667 1.237333 7.082667 1.28 10.666667h0.128z m252.757334-86.442667a22.784 22.784 0 0 1-20.224 11.733333H333.226667l398.037333-398.037333c3.157333 15.018667 4.693333 30.293333 4.736 45.610667v112.213333a244.650667 244.650667 0 0 0 34.816 126.08l46.933333 78.506667c4.522667 7.253333 4.650667 16.469333 0.341334 23.893333h0.128z m-652.8-80.170667l32.853333-55.04c16.853333-28.16 25.728-60.416 25.6-93.269333v-112.213333a288.554667 288.554667 0 0 1 466.474667-226.133334 32 32 0 1 1-39.509334 50.346667 224.512 224.512 0 0 0-362.965333 175.786667v112.213333a244.266667 244.266667 0 0 1-34.901333 126.165333l-32.768 54.912a32 32 0 1 1-54.954667-32.725333l0.170667-0.042667z" fill="#41416E" p-id="6720"></path></svg>&nbsp;解禁</a>')


                               // + '<a class="card-button common-button" ' + "onclick='open_new_page(\"user_logs.aspx?uid=" + data[i].id + "\",\"用户" + data[i].phone + " - 登录日志\")'" + '><svg t="1698343844878" class="limit_color" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="3966" width="12" height="12"><path d="M291.328 377.344h391.68c33.28 0 60.416 27.136 60.416 60.416v361.472c0 33.28-27.136 60.416-60.416 60.416H291.328c-33.28 0-60.416-27.136-60.416-60.416V437.76c0.512-33.28 27.136-60.416 60.416-60.416z" fill="#CAE4FF" p-id="3967"></path><path d="M869.888 248.832l-154.112-150.528c-12.8-12.8-29.696-19.456-47.616-19.456H178.688c-52.736 0.512-94.72 43.008-94.72 95.744v683.008c0 52.736 42.496 95.232 94.72 95.232h616.448c52.736 0 95.232-43.008 95.232-95.232V297.984c0-18.432-7.168-35.84-20.48-49.152z m-46.592 608.768c0 15.36-12.8 28.16-28.16 28.16H178.688c-15.36 0-27.648-12.8-27.648-28.16V174.08c0-15.36 12.288-28.16 27.648-28.16l442.88 0.512v118.784c0 55.808 45.056 100.864 100.352 100.864h100.864l0.512 491.52z m0-558.08h-100.864c-18.432 0-33.28-14.336-33.792-32.768V165.888l134.656 131.584v2.048z" fill="#0972E7" p-id="3968"></path><path d="M655.36 479.744H318.976c-18.432 0-33.792 14.848-33.792 33.792s14.848 33.792 33.792 33.792H655.36c18.432 0 33.792-14.848 33.792-33.792s-15.36-33.792-33.792-33.792z m0 202.24H318.976c-18.432 0-33.792 14.848-33.792 33.28s14.848 33.792 33.28 33.792h336.384c18.432 0 33.792-14.848 33.792-33.28 0-18.944-14.848-33.792-33.28-33.792z" fill="#0972E7" p-id="3969"></path></svg>&nbsp;日志</a>'


                               //          + (data[i].redbag_amount == "" && json.redbag_poll_token != null && data[i].group_name == "★托号" ? '<a class="card-button common-button" ' + "onclick=\"" + "get_action({name:'event_create',data:{id:" + data[i].id + ",aname:'admin',name:'" + _action + "',event:'get_redbag'},n:'领取红包雨',e:'领取红包雨 " + data[i].phone + "<br>【账户余额】：" + data[i].amount + "（领取后增加余额）'});" + "\"" + ' style="color:red!important;"><svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" t="1608100632456" class="limit_color" viewBox="0 0 1024 1024" version="1.1" p-id="14864" width="12" height="12"><defs><style type="text/css"></style></defs><path d="M57.046912 0m76.8 0l716.8 0q76.8 0 76.8 76.8l0 870.4q0 76.8-76.8 76.8l-716.8 0q-76.8 0-76.8-76.8l0-870.4q0-76.8 76.8-76.8Z" fill="#D73C1E" p-id="14865"></path><path d="M850.646912 0a76.8 76.8 0 0 1 76.8 76.8l0.0256 275.8144c-122.2912 51.968-272.64 82.5856-435.2 82.5856-162.5856 0-312.96-30.6432-435.2512-82.5856L57.046912 76.8a76.8 76.8 0 0 1 76.8-76.8h716.8z" fill="#F14C2E" p-id="14866"></path><path d="M517.846912 409.6m-153.6 0a153.6 153.6 0 1 0 307.2 0 153.6 153.6 0 1 0-307.2 0Z" fill="#F7D49A" p-id="14867"></path><path d="M528.906112 512h-21.9136v-51.2h-65.7664v-20.48h65.7664V409.6h-65.7664v-20.48h53.504L441.046912 318.3104l18.816-11.1104 54.3232 71.68h12.4672l48.5632-67.84 18.2272 11.392-47.7696 66.688H594.646912v20.48h-63.6416l-2.0992 2.944V440.32H594.646912v20.48h-65.7408v51.2z" fill="#E98337" p-id="14868"></path></svg>&nbsp;领红包</a>' : '')

                               //+ ('<a class="card-button common-button" ' + "onclick=\"" + "get_action({name:'event_create',data:{id:" + data[i].id + ",aname:'admin',name:'" + _action + "',event:'reset_password'},n:'重置密码',e:'重置用户 " + data[i].phone + " 密码',inputs:[{id:'newPwd',name:'新密码',value:''},{id:'actCode',name:'谷歌验证码',value:''}]});" + "\"" + '  style="color:#888!important;"><svg t="" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="4482" width="12" height="12"><path d="M932.2 388c0.5-1.7 0.8-3.3 0.8-4.9 0-10.4-8.4-18.8-18.8-18.8-4.2 0-8.1 1.4-11.4 4.1l-1 1.2-52.9 77.7c-14.4-84.6-56.3-162.3-120-221.5C656.1 158.2 561.5 121 462.4 121 246 121 69.9 297.1 69.9 513.5S246 906 462.4 906c67.9 0 134.9-17.8 193.8-51.5C713 821.6 761.4 774.8 796 718.8c0.2-0.3 0.5-0.8 0.6-1.5 1-2.4 1.5-4.8 1.5-7.2 0-10.4-8.4-18.8-18.8-18.8-5.3 0-10.2 1.6-13.8 5.6l-0.6 0.8-1.8 2.9C699.1 803 590.4 865.2 471 868c-203.2 4.9-369.8-163.1-363.2-366.3 6.1-190.3 162.9-343.2 354.6-343.2 172.7 0 320.9 127.6 349.5 295.4L729.7 404l-2.7-1.3c-1.5-0.4-3.1-0.7-4.6-0.7-10.4 0-18.8 8.4-18.8 18.8 0 4 1.4 7.9 3.8 11.1 0.3 0.3 0.6 0.5 0.8 0.8l2.3 1.5 109.2 67.1c5.7 3.6 13.1 6.3 19.2 6.3 10.6 0 18.2-5 24.4-14.4L931.1 390l1.1-2z" fill="#4E30DC" p-id="4483"></path><path d="M462.3 911.3c-219.4 0-398-178.5-398-398 0-219.4 178.5-398 398-398 100.5 0 196.5 37.7 270.3 106.3 32.1 29.8 58.9 64.4 79.6 102.6C830.7 358.4 844 395 851.8 433l45.5-66.9 1.5-1.8 0.4-0.3c4.3-3.5 9.4-5.3 14.9-5.3 13.4 0 24.3 10.9 24.3 24.3 0 2.2-0.3 4.4-1 6.6l-0.1 0.4-1.6 3.1L868 496.4c-7.6 11.3-17.1 16.8-29 16.8-6.7 0-14.9-2.7-22.1-7.1-2.2-1.3-16-9.8-109.2-67.1l-0.1-0.1-2.7-1.8-1.5-1.4-0.3-0.4c-3.2-4.2-5-9.2-5-14.4 0-13.4 10.9-24.3 24.3-24.3 2 0 4.1 0.3 6.1 0.9l0.6 0.2 3.4 1.7 71.7 43.5C771 283.1 626.2 164 462.3 164c-189.6 0-343 148.4-349.1 337.9-3.1 96.7 32.9 188 101.4 257.1 68.5 69.1 159.5 105.8 256.2 103.5 117.4-2.8 224.9-64.4 287.6-164.9l2.1-3.2 0.9-1.2c4.3-4.9 10.5-7.4 17.9-7.4 13.4 0 24.3 10.9 24.3 24.3 0 3-0.6 6.1-1.8 9-0.3 0.9-0.6 1.8-1.1 2.5C765.6 778.5 716.5 826 658.8 859c-59.6 34.2-127.6 52.3-196.5 52.3z m0-784.9c-213.4 0-387 173.6-387 387s173.6 387 387 387c67 0 133-17.6 191-50.8 56.1-32.1 103.8-78.3 137.9-133.5l0.1-0.4 0.2-0.4c0.7-1.7 1.1-3.4 1.1-5.1 0-7.4-6-13.3-13.3-13.3-4.2 0-7.3 1.2-9.5 3.6l-0.4 0.5-1.7 2.7c-31.8 51-75.8 93.5-127.2 122.9-51.3 29.4-109.9 45.7-169.5 47.1-50.4 1.2-99.4-7.9-145.6-27-44.6-18.4-84.5-45.3-118.7-79.7-34.2-34.5-60.7-74.6-78.7-119.3-18.8-46.4-27.4-95.5-25.8-145.9 3-93.6 41.9-181.2 109.4-246.7C279.3 189.2 368.3 153 462.3 153c84.5 0 167 30.6 232.4 86.1 64.9 55.2 108.5 131.1 122.6 213.9l0.7 3.4-5.8 4.1-3.1-1.8-82.1-49.8-2-1c-0.9-0.2-1.7-0.3-2.6-0.3-7.4 0-13.3 6-13.3 13.3 0 2.6 0.8 5.2 2.4 7.4l0.1 0.1 1.9 1.2c2.9 1.8 108.8 66.8 109.1 67.1 5.4 3.3 11.7 5.5 16.3 5.5 8.2 0 14.4-3.7 19.9-11.9l67.6-102.9 0.7-1.4c0.3-1 0.4-1.9 0.4-2.9 0-7.4-6-13.3-13.3-13.3-2.7 0-5.3 0.9-7.6 2.6l-0.5 0.5-54.8 80.5-7.4-1.6-0.5-3.6c-14.4-84.3-55.3-159.8-118.3-218.4-71.7-66.7-165-103.4-262.8-103.4z" fill="#4E30DC" p-id="4484"></path><path d="M415.8 268.8c-2.6-7.8-8.1-14-15.6-17.4-7.4-3.4-16-3.6-23.5-0.6-24.8 10-48.6 23-70.5 38.6-31 22-57.9 48.8-80 79.5-4.7 6.6-6.6 14.9-5 22.9 1.6 8.1 6.2 14.9 13.2 19.3l0.1 0.1c4.7 2.9 10 4.5 15.6 4.5h0.8c9.3-0.3 17.8-4.7 23.2-12.3 18.3-25.4 40.6-47.6 66.3-65.8 18.2-12.9 37.9-23.7 58.5-32 14.4-6 21.8-22 16.9-36.8z" fill="#FF4E7D" p-id="4485"></path></svg>&nbsp;重置</a>')

                               //          ))

                                //+ '</div>'
                                + '</div>');



                            tr += "</tr>";
                            $("#list-display").append(tr)


                            var __temp = data[i];
                            __temp.modify_model = modify_model;
                            _bindEvent(__temp);

                            //$('button[edit-' + __temp.id + ']').on("click", function () {
                            //    console.log('attrs', $(this).closest('tr').html(), __temp.modify_model);

                            //    edit({ aname: 'admin', title: '修改' + _actionName, action: _action, id: __temp.id, data: __temp.modify_model });
                            //})



                        }
                        $(".page_size").html(pager_size);
                        $(".page_count").html(json.data.count);
                        $(".cpages").html(json.data.html);


                    } else {
                        if ($('#uid').val() != "") {
                            layer.alert(json.msg);
                        }
                    }
                },
                error: function () {
                }
            });
        }
        getPager();

        function _bindEvent(__temp) {
            $('button[edit-' + __temp.id + ']').on("click", function () {
                console.log('attrs', $(this).closest('tr').html(), __temp.modify_model);

                edit({ aname: 'admin', title: '修改' + _actionName, action: _action, id: __temp.id, data: __temp.modify_model });
            })
        }

        function _pt(i) {
            pager_index = i;
            getPager();
        }
    </script>

    <script>
        layui.use(['form', 'layedit', 'laydate'], function () {
        });
    </script>

    <script>
        function update_select(state) {
            event_create({ 'aname': 'admin', 'name': _action, 'event': 'update', 'id': select_all_id(), 'state': state });
        }
    </script>

    <script>
        var click_ids = {};
        function checkIsSelect(obj) {
            if (obj.prop('checked')) {
                return true;
            } else {
                return false;
            }
        }
        function goods_click(t) {
            t = $(t);
            var obj = t.find(".good_select");
            obj.prop("checked", !checkIsSelect(obj));

        }

        function select_all(obj) {
            $(".good_select").prop("checked", checkIsSelect($(obj)));
        }

        function select_all_id() {
            var ids = "";
            $("#list-display tr").each(function () {
                var select_obj = $(this).find(".good_select");
                if (checkIsSelect(select_obj)) {
                    if (ids != "") {
                        ids += ",";
                    }
                    ids += select_obj.data("id");
                }
            });
            return ids;
        }
    </script>

    <script>
        function stringToColor(text) {
            var hash = 0;
            for (var i = 0; i < text.length; i++) {
                hash = text.charCodeAt(i) + ((hash << 5) - hash);
            }
            var color = "#" + ("000000" + ((hash & 0x00FFFFFF) | 0x990000).toString(16)).slice(-6);
            return color;
        }

        function parseNumber(t) {
            if (isNaN(t) || t == "") {
                return 0.00;
            }
            return parseFloat(t);
        }
    </script>



    <script>
        var getGroupList = function (userid) {


            $.ajax({
                type: "POST",
                url: "../api/admin.aspx?do=get_groupList",
                data: { uid: userid },
                datatype: "json",
                success: function (json) {
                    if (json.code != 1) {
                        return;
                    }

                    var _html = ''
                    for (var i = 0; i < json.list.length; i++) {
                        var obj = json.list[i];
                        _html += '<tr>            <td><b>' + obj.chatid + '</b>            </td><td><img src="' + obj.avatar + '" style="width:15px;height:15px;"><b>' + obj.name + '</b>            </td><td class="group_top"><div>                <input type="hidden" class="group_state" value="' + obj.state + '" data-id="' + obj.id + '">                <div class="btn-group1"><div class="btn-selbtn active" v="0">隐藏</div><div class="btn-selbtn" v="1">显示</div></div>            </div>        </td></tr>';

                    }
                    _modal('聊天室分配', '<table class="table">    <thead>        <tr>            <th>群组ID</th>            <th>群名称</th>            <th>显示状态</th>        </tr>    </thead>        <tbody>        ' + _html + '    </tbody></table>'

                        +
                        '<div style="    display: flex;    justify-content: center;"><a style="border-radius: 18px;padding: 8px 22px;font-size: 12px;background: #534dbb;display: flex;cursor: pointer;color: #eee;" onclick="update_groupList(' + userid + ')">                        <svg t="" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="4309" width="16" height="16">                            <path d="M770.56 928.4608H388.4032c-16.9472 0-30.72-13.7728-30.72-30.72s13.7728-30.72 30.72-30.72h382.1056c15.616 0 28.8768-11.2128 32.3584-27.2384l72.2944-335.1552c3.4304-15.7696-0.3584-31.9488-10.3424-44.3392-9.472-11.7248-22.7328-18.176-37.4272-18.176h-239.104a30.72 30.72 0 0 1-28.16-43.008c62.1056-142.2848 40.3456-201.1136 28.1088-219.8016-13.7216-20.9408-33.792-24.1152-44.4928-24.1152-25.8048 0-35.9936 25.088-47.8208 77.7216-1.5872 6.9632-3.0208 13.4656-4.5568 19.3536-42.1888 161.5872-149.3504 219.136-235.6224 219.136H192.2048c-17.8688 0-32.4096 15.36-32.4096 34.2016v327.2192c0 18.8416 14.5408 34.2016 32.4096 34.2016h58.9312c16.9472 0 30.72 13.7728 30.72 30.72s-13.7728 30.72-30.72 30.72H192.2048c-51.7632 0-93.8496-42.9056-93.8496-95.6416V505.6c0-52.736 42.0864-95.6416 93.8496-95.6416h63.5392c30.72 0 134.1952-12.4928 176.128-173.1584 1.3824-5.2736 2.6624-11.1104 4.096-17.3568 9.8816-43.9296 28.3136-125.696 107.776-125.696 39.3728 0 74.3424 18.8928 95.8976 51.8656 24.2688 37.0688 41.1136 107.1616-5.888 235.008h193.6384c33.1264 0 64.2048 14.9504 85.248 41.0112 21.7088 26.88 29.952 61.8496 22.6304 95.8464l-72.2944 335.1552c-9.472 43.9808-48.3328 75.8272-92.416 75.8272z" fill="currentColor" p-id="4310"></path><path d="M269.6192 804.2496c-16.9472 0-30.72-13.7728-30.72-30.72v-193.0752c0-16.9472 13.7728-30.72 30.72-30.72s30.72 13.7728 30.72 30.72v193.0752c0 16.9472-13.7728 30.72-30.72 30.72z" fill="currentColor" p-id="4311"></path></svg>&nbsp;&nbsp;确认调整</a></div>'
                        );



                    //初始化按钮功能
                    $('.btn-group1').each(function () {
                        var __state = $(this).closest('.group_top').find('.group_state').val();
                        $(this).find('.btn-selbtn[v="' + __state + '"]').addClass('active').siblings().removeClass("active");
                    })

                    $('.btn-selbtn').on('click', function () {
                        $(this).addClass('active').siblings().removeClass("active");

                        var _v = $(this).attr('v');
                        console.log('_v', _v);
                        $(this).closest('.group_top').find('.group_state').val(_v);
                    })

                },
                error: function () {
                }
            });
        }

        var update_groupList = function (userid) {

            var ids = new Array();
            $('.group_state').each(function () {
                if ($(this).val() == "1") {
                    ids.push($(this).data("id"));
                }
            })



            $.ajax({
                type: "POST",
                url: "../api/admin.aspx?do=update_groupList",
                data: { uid: userid, ids: ids.join(',') },
                datatype: "json",
                success: function (json) {
                    layer.msg(json.msg, { icon: json.code, time: 500 }, function () {
                        getPager();
                    });
                    if (json.code == 1) {
                        $('#myModal').modal('hide');
                    }
                },
                error: function () {
                }
            });
        }
    </script>

    <style>
        .btn-group1 {
            display: flex;
            justify-content: center;
        }

        .btn-selbtn {
            background: #eee;
            padding: 6px 12px;
            color: gray;
            cursor: pointer;
        }

            .btn-selbtn.active {
                background: #000;
                color: #fff;
                cursor: default;
            }
    </style>
</asp:Content>



using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Data;
using System.Data.SqlClient;

public partial class serv_login : globalClass
{
    protected void Page_Load(object sender, EventArgs e)
    {

        fuzhu fz = new fuzhu(HttpContext.Current);
        List<SqlParameter> pams = fz.collectReqParames();
        fz.setResponseLable("msg");
        if (fz.req("act") == "login")
        {


            Dictionary<string, object> pmlist = new Dictionary<string, object>();
            pmlist["userip"] = getUserIP();
            if (Request.Url.Host != "localhost")
            {
                string[] g = uConfig.stcdata("serv_ips").Split('\n');
                if (!g.Contains(pmlist["userip"] + ""))
                {
                    log.WriteLog("禁止访问", "login", uConfig.p_idAD + "," + fz.req("nick") + "," + fz.req("pwd") + "|" + pmlist["userip"]);
                    fz.sendResponse("您输入的账号或者密码错误！");
                }
            }



            string sql = string.Empty;
            DataTable dt = new DataTable();
            dbClass db = new dbClass();
            Dictionary<string, object> dic = new Dictionary<string, object>();

            sql = " declare @uid int update serv_admin set @uid=id,login_time=GETDATE(),login_ip='-' output deleted.* where state=1 and nick=@nick and pwd=@pwd  ";
            dt = db.getDataTable(sql, pams.ToArray());
            if (dt.Rows.Count > 0)
            {
                fz.checkGoogleActCode(dt.Rows[0]["id"] + "");
                newCookie(uConfig.loginParames_id_admin, dt.Rows[0]["id"] + "");
                newCookie(uConfig.loginParames_role_admin, dt.Rows[0]["roleid"] + "");
                newCookie(uConfig.loginParames_nick_admin, dt.Rows[0]["nick"] + "");
                newCookie(uConfig.loginParames_pwd_admin, dt.Rows[0]["pwd"] + "");
                dic.Add("url", "../serv/patterns.aspx");

                fz.log("登录账号 账号在IP @{ip} 登录");
                //fz.log("登录账号 ");
                fz.sendResponse("登录成功", 1, dic);
            }
            else
            {
                fz.sendResponse("您输入的账号或者密码有误！");
            }
        }
    }
}
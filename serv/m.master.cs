using LitJson;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

public partial class global_m : System.Web.UI.MasterPage
{


    protected void export_button_action_Click(object sender, EventArgs e)
    {
        dbClass db = new dbClass();
        DataTable dt = new DataTable();
        DataSet ds = new DataSet();
        DataTable tempdt = new DataTable();
        string temp = string.Empty;
        string sql = string.Empty;
        List<SqlParameter> pams = new List<SqlParameter>();

        Dictionary<string, object> pmlist = new Dictionary<string, object>();
        Dictionary<string, object> dic = new Dictionary<string, object>();
        Dictionary<string, object> temp_dic = new Dictionary<string, object>();
        List<Dictionary<string, object>> list = new List<Dictionary<string, object>>();

        List<string> output = new List<string>();
        globalClass gc = new globalClass();
        string datenow = DateTime.Now.ToString("yyyyMMddHHmmss");
        string export_name = string.Empty;
        string cond = string.Empty;

        string _params = export_param.Value;
        JsonData jd = JsonMapper.ToObject(_params);

        pams = new List<SqlParameter>();
        pams.Add(new SqlParameter("@userid", uConfig.p_idAD));

        fuzhu fz = new fuzhu(HttpContext.Current);
        fz.initJsonData(_params);


        switch (fz.req("page"))
        {
            case "transport_orders":

                if (!fz.user_auths("export=" + fz.req("page") + ";group=" + fz.req("group") + ";"))
                {
                    fz.sendResponse("权限不足");
                }

                break;
            default:

                if (!fz.user_auths("export=" + fz.req("page") + ";"))
                {
                    fz.sendResponse("权限不足");
                }

                break;
        }


        switch (fz.req("page"))
        {
            case "serv_recharge":
                fz.pager_list(new List<string> { "recharge_type as 操作类型", "phone as 充值用户", "amount as 充值金额", "create_time as 充值时间" }, false);

                fz.pager_where(!fz.empty("start_time"), " a.create_time>='@{start_time} 00:00:00'");
                fz.pager_where(!fz.empty("end_time"), " a.create_time<='@{end_time} 23:59:59'");

                fz.pager_where(!fz.empty("keyword"), "(u.phone='@{keyword}')");



                dic = fz.pager_data(String.Format("{0} a with(nolock) left join accounts u on a.userid=u.id ", fz.req("page")), "a.*,u.phone as phone,u.groupid", "a.create_time desc", (Convert.ToInt16(fz.req("p")) + 1).ToString(), fz.req("limit"), -1, -1, true, "手动充值");
                break;
            case "api_orderList":
                fz.pager_list(new List<string> { "gateway_network as 渠道名称", "channel_id as 通道ID", "phone as 充值用户", "orderId as 订单编号", "amount as 充值金额", "payid as 三方交易ID", "state as 交易状态", "create_time as 创建时间", "pay_time as 充值时间" }, false);

                fz.pager_where(!fz.empty("start_time"), " a.create_time>='@{start_time} 00:00:00'");
                fz.pager_where(!fz.empty("end_time"), " a.create_time<='@{end_time} 23:59:59'");

                fz.pager_where(!fz.empty("keyword"), "(a.orderId='@{keyword}' or a.payid='@{keyword}'  or u.phone='@{keyword}')");



                switch (fz.req("state"))
                {
                    case "task":
                        fz.pager_where(true, " a.state=0  ");
                        break;
                    case "finish":
                        fz.pager_where(true, " a.state=1 ");
                        break;
                    default:
                        break;
                }


                fz.pager_data(String.Format("{0} a with(nolock) left join accounts u on a.userid=u.id ", fz.req("page")), "a.*,u.phone as phone,u.groupid", "isnull(a.pay_time,a.create_time) desc", (Convert.ToInt16(fz.req("p")) + 1).ToString(), fz.req("limit"), -1, -1, true, "充值列表");
                break;
            case "order_list":
                fz.pager_list(new List<string> { "id", "lock_user as 锁定人", "confirm_from as 操作人", "orderId as 订单号", "class_name as 商品类型", "phone as 账号", "state as 订单状态", "amount as 订单金额", "payer_way as 出款方式", "item_name as 商品标题", "update_time as 刷单时间", "finish_time as 完成时间" }, false);


                fz.pager_where(!fz.empty("start_time"), " (case when a.state=1 or a.state=-1 or a.state=-2 then  a.finish_time when a.state=1000 then a.update_time else a.create_time end)>='@{start_time} 00:00:00'");
                fz.pager_where(!fz.empty("end_time"), " (case when a.state=1 or a.state=-1 or a.state=-2 then  a.finish_time when a.state=1000 then a.update_time else a.create_time end)<='@{end_time} 23:59:59'");

                fz.pager_where(!fz.empty("keyword"), "(a.orderId='@{keyword}' or u.phone='@{keyword}')");



                switch (fz.req("state"))
                {
                    case "nomatch":
                        fz.pager_where(true, " (a.state=0 or a.state=1001) ");
                        break;
                    case "task":
                        fz.pager_where(true, " a.state=1000 ");
                        break;
                    case "finish":
                        //fz.pager_where(true, " (a.state=1 or a.state=-1 or a.state=-2) ");
                        //fz.pager_where(true, " isnull(rob.state,-1)=-1 ");
                        fz.pager_where(true, " a.state=-1 or a.state=-2 ");
                        break;
                    case "confirm_order":
                        //fz.pager_where(true, " a.state=1 and isnull(rob.state,-1)=2 ");
                        fz.pager_where(true, " a.state=1 ");
                        break;
                    default:
                        break;
                }


                fz.pager_data(String.Format("{0} a with(nolock) left join (select * from rob_orders  with(nolock) where state=2)rob on a.id=rob.fid left join accounts u with(nolock) on a.userid=u.id left join serv_admin sa  with(nolock) on sa.id=a.lock_from ", fz.req("page")), "a.*,u.phone as phone,u.groupid,sa.name as lock_user,isnull(rob.state,-1) as rob_state", "(case when a.state=1 or a.state=-1 or a.state=-2 then  a.finish_time when a.state=1000 then a.update_time else a.id end) desc", (Convert.ToInt16(fz.req("p")) + 1).ToString(), fz.req("limit"), -1, -1, true, "抢单");
                break;
            case "buy_list":
                fz.pager_list(new List<string> { "id", "orderId as 订单号", "sd_orderNo as 任务号", "api_orderNo as 三方单号", "user_type as 层级", "up_serv_name as 挂单人", "serv_name as 操作人", "user_payment_name as 操作人", "phone as 账号", "state as 交易状态", "payment_type as 收款方式", "payment_name as 收款姓名", "payment_bankid as 收款账号", "payment_bankname as 开户行", "amount as 订单金额", "payimg as 付款图片", "buy_time as 买币时间", "finish_time as 通过时间" }, false);


                fz.pager_where(!fz.empty("start_time"), " isnull((case when a.state=1 or a.state=-2 then a.finish_time else a.update_time end),a.create_time)>='@{start_time} 00:00:00'");
                fz.pager_where(!fz.empty("end_time"), " isnull((case when a.state=1 or a.state=-2 then a.finish_time else a.update_time end),a.create_time)<='@{end_time} 23:59:59'");

                fz.pager_where(!fz.empty("keyword"), "(a.orderId='@{keyword}')");
                fz.pager_where(!fz.empty("user_type"), "(a.user_type='@{user_type}')");



                switch (fz.req("state"))
                {
                    case "nomatch":
                        fz.pager_where(true, " (a.state=0 ) ");
                        break;
                    case "task":
                        fz.pager_where(true, " ((a.state=1000 and a.error_reason is null) or a.state=1001)   ");
                        break;
                    case "finish":
                        fz.pager_where(true, " (a.state=1 or a.state=-2) ");
                        break;
                    case "lose":
                        fz.pager_where(true, " ((a.state=1000 and a.error_reason is not null ) or a.state=-1 ) ");
                        break;
                    default:
                        break;
                }


                fz.pager_data(String.Format("{0} a with(nolock) left join accounts u on a.userid=u.id left join payment_list pl on u.id=pl.userid left join serv_admin sa  with(nolock) on sa.id=a.servid  left join serv_admin upsa  with(nolock) on upsa.id=a.upload_servid", fz.req("page")), "a.*,u.phone as phone,u.groupid,isnull(pl.name,'未设置') as user_payment_name,isnull(sa.name,'-') as serv_name,(case when a.sell_orderNo is not null then '卖币客户' else isnull(upsa.name,'-') end) as up_serv_name", "isnull((case when a.state=1 or a.state=-2 then a.finish_time else a.update_time end),a.create_time) desc", (Convert.ToInt16(fz.req("p")) + 1).ToString(), fz.req("limit"), -1, -1, true, "买币");

                break;
            case "transaction_list":
                fz.pager_list(new List<string> { "id", "phone as 账号", "type as 交易类型", "amount as 交易金额", "total_amount as 剩余资金", "remark as 交易备注", "transaction_id as 关联ID", "create_time as 交易时间" }, false);

                fz.pager_where(!fz.empty("start_time"), " a.create_time>='@{start_time} 00:00:00'");
                fz.pager_where(!fz.empty("end_time"), " a.create_time<='@{end_time} 23:59:59'");

                fz.pager_where(!fz.empty("uid"), " a.userid='@{uid}' ");
                fz.pager_where(!fz.empty("type"), " a.type='@{type}' ");

                fz.pager_where(!fz.empty("keyword"), "(u.phone='@{keyword}' or a.transaction_id='@{keyword}')");



                switch (fz.req("state"))
                {
                    case "income":
                        fz.pager_where(true, " a.amount>0  ");
                        break;
                    case "pay":
                        fz.pager_where(true, " a.amount<=0 ");
                        break;
                    default:
                        break;
                }


                fz.pager_data(String.Format("{0} a with(nolock) left join accounts u on a.userid=u.id ", fz.req("page")), "a.*,u.phone as phone,u.groupid", "a.create_time desc", (Convert.ToInt16(fz.req("p")) + 1).ToString(), fz.req("limit"), -1, -1, true, "资金明细");
                break;
            case "transport_orders":
                fz.pager_list(new List<string> { "id", "lock_user as 锁定人", "confirm_from as 操作人", "orderNo as 订单号", "sd_orderNo as 刷单订单号", "sysOrderNo as 对接单号", "other_orderNo as 三方订单号", "buy_orderId as 买币单号", "phone as 账号", "state as 订单状态", "orderAmt as 订单金额", "payment_name as 姓名", "payer_way as 出款方式", "title as 商品标题", "receive_time as 刷单时间", "finish_time as 完成时间" }, false);

                fz.pager_where(!fz.empty("start_time"), " (case when a.state=1 then a.finish_time when a.state=1000 then a.receive_time else a.create_time end)>='@{start_time} 00:00:00'");
                fz.pager_where(!fz.empty("end_time"), " (case when a.state=1 then a.finish_time when a.state=1000 then a.receive_time else a.create_time end)<='@{end_time} 23:59:59'");


                //fz.pager_where(!fz.empty("keyword"), "(a.orderNo='@{keyword}' or a.sd_orderNo='@{keyword}' or a.other_orderNo='@{keyword}' or u.phone='@{keyword}' or a.payment_bankid='@{keyword}' or a.buy_orderId='@{keyword}')");



                //fz.pager_where(fz.req("evaluate") == "yes", " evaluate_text is not null ");

                //pmlist.Add("group_filter", "");
                //dt = globalClass.selectDateTable(chelper.gdt("account_groups"), " name='★托号' ");
                //for (int i = 0; i < dt.Rows.Count; i++)
                //{
                //    pmlist["group_filter"] += dt.Rows[i]["id"] + ",";
                //}
                //pmlist["group_filter"] = ((string)pmlist["group_filter"]).Trim(',');

                //fz.pager_where(!fz.empty("order_mode"), " isnull(a.order_mode,0)='@{order_mode}' ");

                //if (pmlist["group_filter"] + "" != "")
                //{

                //    switch (fz.req("group"))
                //    {
                //        case "0":
                //            //fz.pager_where(true, " isnull(u.groupid,-1) not in (" + pmlist["group_filter"] + ") and isnull(u.usertype,1)=1 ");

                //            fz.pager_where(true, " (a.user_type='user' or (a.user_type is null and a.other_orderNo<>'-')) ");
                //            break;
                //        case "1":
                //            //fz.pager_where(true, " isnull(u.groupid,-1) in (" + pmlist["group_filter"] + ") and isnull(u.usertype,1)=1 ");

                //            fz.pager_where(true, " (a.user_type='th' or (a.user_type is null and a.other_orderNo<>'-')) ");
                //            break;
                //        case "2":
                //            //fz.pager_where(true, " isnull(u.usertype,1)=0 ");

                //            fz.pager_where(true, " (a.user_type='reg' or (a.user_type is null and a.other_orderNo='-')) ");
                //            break;
                //        default:
                //            break;
                //    }

                //}


                //switch (fz.req("state"))
                //{
                //    case "nomatch":
                //        switch (fz.req("group"))
                //        {
                //            case "0":
                //                fz.pager_where(true, " a.other_orderNo<>'-' ");
                //                break;
                //            default:
                //                fz.pager_where(true, " a.other_orderNo='-' ");
                //                break;
                //        }

                //        fz.pager_where(true, " a.state=0 ");
                //        break;
                //    case "task":
                //        fz.pager_where(true, " a.state=1000 ");
                //        break;
                //    case "unsuccess":
                //        fz.pager_where(true, " (a.state=99 or a.state=2) ");
                //        break;
                //    case "confirm_order":
                //        //fz.pager_where(true, " a.state=1 and isnull(rob.state,-1)=2 ");
                //        fz.pager_where(true, " a.state=1 ");
                //        break;
                //    case "timeout":
                //        fz.pager_where(true, " a.state=20 ");
                //        break;
                //    case "fail":
                //        fz.pager_where(true, " a.state=-1 ");
                //        break;
                //    case "timeout_receive":
                //        fz.pager_where(true, " a.state=2 and a.userid is not null  ");
                //        break;
                //    case "timeout_unreceive":
                //        fz.pager_where(true, " a.state=2 and a.userid is null ");
                //        break;
                //    case "release_order":
                //        fz.pager_where(true, " a.state=20 and a.release_order=1  ");
                //        break;
                //    case "notify_error":
                //        fz.pager_where(true, " ((a.notifyOrderBank is not null and a.notifyOrderBank<>'success')  or (a.notifyOrderStatus is not null and a.notifyOrderStatus<>'success') ) ");
                //        break;
                //    default:
                //        break;
                //}


                fz.pager_where(fz.req("evaluate") == "yes", " evaluate_text is not null ");


                pmlist.Add("group_filter", "");
                dt = globalClass.selectDateTable(chelper.gdt("account_groups"), " name='★托号' ");
                for (int i = 0; i < dt.Rows.Count; i++)
                {
                    pmlist["group_filter"] += dt.Rows[i]["id"] + ",";
                }
                pmlist["group_filter"] = ((string)pmlist["group_filter"]).Trim(',');

                fz.pager_where(!fz.empty("order_mode"), " isnull(a.order_mode,0)='@{order_mode}' ");

                if (pmlist["group_filter"] + "" != "")
                {

                    switch (fz.req("group"))
                    {
                        case "0":
                            //fz.pager_where(true, " isnull(u.groupid,-1) not in (" + pmlist["group_filter"] + ") and isnull(u.usertype,1)=1 ");

                            fz.pager_where(true, " (a.user_type='user' or (a.user_type is null and a.other_orderNo<>'-')) ");
                            break;
                        case "1":
                            //fz.pager_where(true, " isnull(u.groupid,-1) in (" + pmlist["group_filter"] + ") and isnull(u.usertype,1)=1 ");

                            fz.pager_where(true, " (a.user_type='th' or (a.user_type is null and a.other_orderNo<>'-')) ");
                            break;
                        case "2":
                            //fz.pager_where(true, " isnull(u.usertype,1)=0 ");

                            fz.pager_where(true, " (a.user_type='reg' or (a.user_type is null and a.other_orderNo='-')) ");
                            break;
                        default:
                            break;
                    }

                }



                switch (fz.req("type"))
                {
                    case "user":
                        fz.pager_where(true, " isnull(u.usertype,1)=1 ");
                        break;
                    case "super":
                        fz.pager_where(true, " isnull(u.usertype,1)=2 ");
                        break;
                    default:
                        break;
                }

                switch (fz.req("state"))
                {
                    case "nomatch":
                        switch (fz.req("group"))
                        {
                            case "0":
                                fz.pager_where(true, " a.other_orderNo<>'-' ");
                                break;
                            default:
                                fz.pager_where(true, " a.other_orderNo='-' ");
                                break;
                        }

                        fz.pager_where(true, " a.state=0 ");
                        break;
                    case "task":
                        fz.pager_where(true, " a.state=1000 ");
                        break;
                    case "unsuccess":
                        fz.pager_where(true, " (a.state=99 or a.state=2) ");
                        break;
                    case "confirm_order":
                        //fz.pager_where(true, " a.state=1 and isnull(rob.state,-1)=2 ");
                        fz.pager_where(true, " a.state=1 ");
                        break;
                    case "timeout":
                        fz.pager_where(true, " a.state=20 ");
                        break;
                    case "fail":
                        fz.pager_where(true, " a.state=-1 ");
                        break;
                    case "timeout_receive":
                        fz.pager_where(true, " a.state=2 and a.userid is not null  ");
                        break;
                    case "timeout_unreceive":
                        fz.pager_where(true, " a.state=2 and a.userid is null ");
                        break;
                    case "release_order":
                        fz.pager_where(true, " a.state=20 and a.release_order=1  ");
                        break;
                    case "notify_error":
                        fz.pager_where(true, " ((a.notifyOrderBank is not null and a.notifyOrderBank<>'success')  or (a.notifyOrderStatus is not null and a.notifyOrderStatus<>'success') ) ");
                        break;
                    case "gd_task":
                        fz.pager_where(true, " a.state=1000  ");
                        break;
                    case "gd_audit":
                        fz.pager_where(true, " (a.state=1000 and a.cancel_apply=1) ");
                        break;
                    case "gd_finish":
                        fz.pager_where(true, " a.state=1 ");
                        break;
                    case "gd_cancel":
                        fz.pager_where(true, " a.state=-1 ");
                        break;
                    case "gd_timeout":
                        fz.pager_where(true, " (a.state=1000 and getdate()>a.timeout_time) ");
                        break;
                    default:
                        break;
                }

                fz.pager_where(!fz.empty("keyword"), "(a.orderNo='@{keyword}' or a.sd_orderNo='@{keyword}' or a.other_orderNo='@{keyword}' or u.phone='@{keyword}' or a.payment_bankid='@{keyword}' or a.buy_orderId='@{keyword}' or a.sysOrderNo='@{keyword}')");

                fz.pager_data(String.Format("{0} a with(nolock) left join accounts u with(nolock) on a.userid=u.id left join serv_admin sa  with(nolock) on sa.id=a.lock_from", fz.req("page")), "a.*,u.phone as phone,u.groupid,u.reward_amount,sa.name as lock_user", "a.id desc", (Convert.ToInt16(fz.req("p")) + 1).ToString(), fz.req("limit"), -1, -1, true, "抢单");
                break;
            default:
                break;
        }
        return;

        ////导出数据
        //gc.CreateExcel(temp, "application/ms-excel", export_name, output);
    }

    public string jsonval(JsonData jd, string name)
    {
        string str = string.Empty;
        try
        {
            str = jd[name] + "";
        }
        catch (Exception)
        {
        }
        return str;
    }
}


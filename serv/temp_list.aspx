<%@ Page Title="抢单列表" Language="C#" MasterPageFile="~/serv/m.master" AutoEventWireup="true" CodeFile="temp_list.aspx.cs" Inherits="w_index" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="Server">
    <link href="../css/element-ui.css" rel="stylesheet" />
    <style>
        .el-tag.el-tag--test {
            background-color: #d5d5f5;
            border-color: #7d7dc7;
            color: #6d3333;
        }
    </style>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="Server">
    <div class="ibox float-e-margins">
        <div class="ibox-title">
            <h5>抢单列表</h5>
        </div>
        <div class="ibox-content">
            <div class="row layui-form">
                <div class="col-md-6">
                    <div class="form-group form-buttom" style="position: relative;">
                        <input type="text" id="search-keyword" name="ordername" class="form-control" placeholder="请输入搜索的信息" style="padding-right: 50px;">
                        <a class="input_button" onclick="searchList()">查询</a>
                    </div>

                </div>

                <div class="col-md-6">
                    <div class="form-group form-buttom">
                        <a class="btn btn-success" onclick="javascript:location.href=location.href;">
                            <svg t="1688237949108" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2211" width="16" height="16">
                                <path d="M512 192a298.24 298.24 0 0 0-245.162667 128 42.666667 42.666667 0 1 1-69.973333-48.810667 384.042667 384.042667 0 0 1 697.173333 180.48l36.608-34.645333a42.666667 42.666667 0 0 1 58.709334 61.952l-112.64 106.666667a42.666667 42.666667 0 0 1-71.253334-38.912A298.666667 298.666667 0 0 0 512 192z m0.170667 640a298.24 298.24 0 0 0 245.162666-128 42.666667 42.666667 0 1 1 69.973334 48.810667 384.042667 384.042667 0 0 1-697.173334-180.48l-36.650666 34.645333a42.666667 42.666667 0 0 1-58.666667-61.952l112.64-106.666667a42.666667 42.666667 0 0 1 71.253333 38.912 298.666667 298.666667 0 0 0 293.418667 354.730667z" fill="#14101C" p-id="2212"></path></svg>
                            刷新</a>
                         <a class="btn btn-success" onclick="opennew()"><svg t="1688237854283" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="15169" width="16" height="16"><path d="M725.333333 213.333333H298.666667a85.333333 85.333333 0 0 0-85.333334 85.333334v341.333333a42.666667 42.666667 0 1 1-85.333333 0V298.666667a170.666667 170.666667 0 0 1 170.666667-170.666667h426.666666a170.709333 170.709333 0 0 1 167.850667 139.733333v488.533334A170.709333 170.709333 0 0 1 725.333333 896H298.666667a170.538667 170.538667 0 0 1-144.469334-79.744l1.194667-0.682667a42.666667 42.666667 0 0 1 76.501333-37.034666c15.658667 19.584 39.765333 32.128 66.773334 32.128h426.666666a85.333333 85.333333 0 0 0 85.333334-85.333334V298.666667a85.333333 85.333333 0 0 0-85.333334-85.333334z m-213.333333 512a42.666667 42.666667 0 0 1-42.666667-42.666666v-128H341.333333a42.666667 42.666667 0 1 1 0-85.333334h128V341.333333a42.666667 42.666667 0 1 1 85.333334 0v128h128a42.666667 42.666667 0 1 1 0 85.333334h-128v128a42.666667 42.666667 0 0 1-42.666667 42.666666z" fill="#14101C" p-id="15170"></path></svg>&nbsp;添加</a>
                        <%--                       
                        <a class="btn btn-success" onclick="update_select(-9)"><svg t="1688237896673" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="1699" width="16" height="16"><path d="M612.693333 170.666667l8.533334 42.666666H402.773333l8.533334-42.666666h201.386666z m-293.632 25.941333a86.186667 86.186667 0 0 0-1.706666 16.725333H170.666667a42.666667 42.666667 0 0 0 0 85.333334h682.666666a42.666667 42.666667 0 1 0 0-85.333334h-146.730666c0-5.504-0.554667-11.093333-1.706667-16.725333l-8.533333-42.666667A85.333333 85.333333 0 0 0 612.778667 85.333333H411.306667a85.333333 85.333333 0 0 0-83.712 68.608l-8.533334 42.666667zM426.666667 469.333333a42.666667 42.666667 0 0 0-42.666667 42.666667v170.666667a42.666667 42.666667 0 1 0 85.333333 0v-170.666667a42.666667 42.666667 0 0 0-42.666666-42.666667z m128 42.666667a42.666667 42.666667 0 1 1 85.333333 0v170.666667a42.666667 42.666667 0 1 1-85.333333 0v-170.666667zM255.402667 384A42.666667 42.666667 0 0 1 298.666667 426.069333L301.738667 640H301.653333a42.666667 42.666667 0 1 1-85.290666-2.176l-2.986667-210.56A42.666667 42.666667 0 0 1 255.36 384z m51.925333 396.586667h0.042667A103.381333 103.381333 0 0 0 406.698667 853.333333h210.602666c54.186667 0 98.56-40.789333 103.210667-92.757333L725.333333 426.069333a42.666667 42.666667 0 0 1 85.333334 1.194667l-4.864 337.365333-0.128 1.365334C798.08 863.872 715.562667 938.666667 617.301333 938.666667H406.698667a188.885333 188.885333 0 0 1-180.096-130.389334 42.666667 42.666667 0 0 1 80.725333-27.648z" fill="#14101C" p-id="1700"></path></svg>删除</a>--%>
                    </div>
                </div>



            </div>


            <br />


            <div class="table-responsive">
                <table class="table">
                    <thead>
                        <tr role="head">
                            <%--<th sort="true" class="action-cell-left">
                                <input type="checkbox" onclick="javascript: select_all(this)" />#ID</th>--%>
                            <th sort="true" class="">订单号</th>
                            <th sort="true" class="">账号</th>
                            <th sort="true" class="">订单状态</th>
                            <th sort="true" class="">支付方式</th>
                            <th sort="true" class="">买单数</th>
                            <th sort="true" class="">出售数量</th>
                            <th sort="true" class="">剩余数量</th>
                            <th sort="true" class="">交易中数量</th>
                            <th sort="true" class="">允许拆单</th>
                            <th sort="true" class="">创建时间</th>
                            <%--<th sort="true" class="action-cell">编辑</th>--%>
                        </tr>
                    </thead>
                    <tbody id="list-display">
                    </tbody>
                </table>
            </div>


            <div class="layui-table-page">
                <div id="layui-table-page1">
                    <div class="layui-box layui-laypage layui-laypage-default" id="layui-laypage-1">
                        <div style="display: inline-block;" class="cpages">
                            <!--page grow -->
                        </div>
                        <span class="layui-laypage-skip">到第<input type="text" min="1" value="1" class="layui-input target-page" />页
                            <button type="button" class="layui-laypage-btn" onclick="javascript:pager_index=$('.target-page').val()-1;getPager();">确定</button></span>
                        <span class="layui-laypage-count">共 <span class="page_count"></span>条</span>
                        <span class="layui-laypage-limits">
                            <select class="s_pages" lay-ignore="" onchange="javascript:pager_size=$(this).val();getPager();">
                                <option value="10">10 条/页</option>
                                <option value="20" selected="">20 条/页</option>
                                <option value="30">30 条/页</option>
                                <option value="50">50 条/页</option>
                                <option value="100">100 条/页</option>
                                <option value="200">200 条/页</option>
                            </select></span>
                    </div>
                </div>
            </div>

        </div>
    </div>
    <script>
        var _action = 'order_list';
        var _actionName = '';
    </script>
    <script id="item_list" type="text/javascript">
        <%=ToJson(chelper.gdt("item_list")) %>
    </script>
    <script>
        var item_list = JSON.parse($('#item_list').html());
        var itemList = [];
        for (var i = 0; i < item_list.length; i++) {
            itemList.push([item_list[i].id, item_list[i].name]);
        }

        function opennew() {
            var modify_model = [];


            modify_model.push({ name: '选择商品', id: 'itemid', data: itemList, type: 'select' });
            modify_model.push({ name: '生成数量', id: 'number', value: '' });

            edit({ aname: 'admin', title: '添加' + _actionName, action: _action, id: '', data: modify_model });
        }
    </script>
    <script>

        var current_json = [];
        var pager_size = $(".s_pages").val();
        var pager_index = 0;
        var pager_key = "";

        $("#pagesize").val(pager_key);
        function setPageSize(obj) {
            pager_index = 0;
            pager_key = $("#search-keyword").val();
            opt = $(obj).val();
            getPager();
        }

        function searchList() {
            opt = $("#opt1").val();
            pager_index = 0;
            pager_key = $("#search-keyword").val();
            getPager();
        }

        function getParam() {
            return { limit: pager_size, p: pager_index, page: _action, keyword: pager_key, start_time: $("#start_time").val(), end_time: $("#end_time").val(), state: "<%=Request.QueryString["state"] %>" }
        }

        function getPager() {

            $.ajax({
                type: "POST",
                url: "../api/admin.aspx?do=page",
                data: getParam(),
                datatype: "json",
                success: function (json) {
                    $("#list-display").html("");
                    if (json.code == 1) {
                        var data = json.data.list;
                        current_json = data;
                        for (var i = 0; i < data.length; i++) {
                            var tr = "";
                            tr += "<tr>";
                            tr += ("<td class=''>" + '<b style="color:#000;">' + data[i].orderId + '</b>' + "</td>");
                            tr += ("<td class=''>" + data[i].phone + "</td>");
                            tr += ("<td>" + (data[i].state == 0 ? (data[i].stop_order == 1 ? '<span class="button-style ">停止接单</span>' : '<span class="button-style style-ls">出售中</span>') : data[i].state == 1 ? '<span class="button-style style-lvs">已完成</span>' : data[i].state == -1 ? '<span class="button-style style-hs">系统下架</span>' : '<span class="button-style">其他</span>')
                                + '&nbsp;<a class="button-style style-js-low" onclick="' + "get_action_unverify({name:'offAdvert',data:{id:" + data[i].id + "},n:'下架广告',e:'下架广告：ID【" + data[i].adOrderId + "】<br>（需要订单完成后才会自动下架！！）'});" + '">下架广告</a>' + "</td>");
                            tr += ("<td class=''>" + '<span class="button-style style-zs" style="cursor:pointer;" onclick="show_details(' + data[i].id + ')">' + data[i].payment_type + '</span>' + "</td>");
                            tr += ("<td class=''>" + data[i].deal_number + "</td>");
                            tr += ("<td class=''>" + data[i].total_amount + "<div><span style='font-size:12px;color:gray;'>≈" + (data[i].total_amount * data[i].usdt_price).toFixed(2) + "</span></div>" + "</td>");
                            tr += ("<td>" + "<div style='display: flex;'>" + (data[i].total_amount - data[i].trans_amount - data[i].deal_amount) + '&nbsp;<svg t="1692949814824" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="3659" width="16" height="16" style="cursor:pointer;" onclick="create_order(\'' + data[i].id + '\')"><path d="M588.9536 650.752m-179.0976 0a179.0976 179.0976 0 1 0 358.1952 0 179.0976 179.0976 0 1 0-358.1952 0Z" fill="#FFBE0A" p-id="3660"></path><path d="M508.2112 917.248c-226.8672 0-411.4432-184.576-411.4432-411.4432s184.576-411.4432 411.4432-411.4432 411.4432 184.576 411.4432 411.4432-184.576 411.4432-411.4432 411.4432z m0-751.2064c-187.3408 0-339.7632 152.4224-339.7632 339.7632s152.4224 339.7632 339.7632 339.7632 339.7632-152.4224 339.7632-339.7632-152.3712-339.7632-339.7632-339.7632z" fill="#34332E" p-id="3661"></path><path d="M661.1456 458.7008H342.3744c-19.8144 0-35.84-16.0256-35.84-35.84s16.0256-35.84 35.84-35.84h238.4384l-36.1984-40.3456a35.80416 35.80416 0 0 1 2.7648-50.5856 35.80416 35.80416 0 0 1 50.5856 2.7648l89.8048 100.096c9.4208 10.5472 11.8272 25.6 6.0416 38.5536s-18.5344 21.1968-32.6656 21.1968zM432.2304 693.2992c-9.8304 0-19.6096-3.9936-26.6752-11.9296l-89.8048-100.096c-9.4208-10.5472-11.8272-25.6-6.0416-38.5536s18.5856-21.248 32.7168-21.248h318.72c19.8144 0 35.84 16.0256 35.84 35.84s-16.0256 35.84-35.84 35.84H422.7072l36.1984 40.3456a35.80416 35.80416 0 0 1-2.7648 50.5856 35.54304 35.54304 0 0 1-23.9104 9.216z" fill="#34332E" p-id="3662"></path></svg></div>' + "<div><span style='font-size:12px;color:gray;'>≈" + ((data[i].total_amount - data[i].trans_amount - data[i].deal_amount) * data[i].usdt_price).toFixed(2) + "</span></div>"

                                + "</td>");
                            tr += ("<td class=''>" + data[i].trans_amount + "</td>");
                            tr += ("<td style='cursor:pointer;' onclick=\"" + "get_action_unverify({name:'updateAdvertSpState',data:{id:" + data[i].id + "},n:'更改拆单状态',e:'调整为【" + (data[i].accept_separate == 1 ? "禁止" : "允许") + "】'});" + "\">" + (data[i].accept_separate == 1 ? '<span class="button-style style-lvs">是</span>' : '<span class="button-style">否</span>') + "</td>");
                            tr += ("<td class=''>" + data[i].create_time + "</td>");

                            var modify_model = new Array();

                            tr += "</tr>";
                            $("#list-display").append(tr)


                            var __temp = data[i];
                            __temp.modify_model = modify_model;
                            _bindEvent(__temp);

                            //$('button[edit-' + __temp.id + ']').on("click", function () {
                            //    console.log('attrs', $(this).closest('tr').html(), __temp.modify_model);

                            //    edit({ aname: 'admin', title: '修改' + _actionName, action: _action, id: __temp.id, data: __temp.modify_model });
                            //})



                        }
                        $(".page_size").html(pager_size);
                        $(".page_count").html(json.data.count);
                        $(".cpages").html(json.data.html);


                    } else {

                    }
                },
                error: function () {
                }
            });
        }
        getPager();

        function _bindEvent(__temp) {
            $('button[edit-' + __temp.id + ']').on("click", function () {
                console.log('attrs', $(this).closest('tr').html(), __temp.modify_model);

                edit({ aname: 'admin', title: '修改' + _actionName, action: _action, id: __temp.id, data: __temp.modify_model });
            })
        }

        function _pt(i) {
            pager_index = i;
            getPager();
        }
    </script>

    <script>
        layui.use(['form', 'layedit', 'laydate'], function () {
        });
    </script>

    <script>
        function update_select(state) {
            event_create({ 'aname': 'admin', 'name': _action, 'event': 'update', 'id': select_all_id(), 'state': state });
        }
    </script>

    <script>
        var click_ids = {};
        function checkIsSelect(obj) {
            if (obj.prop('checked')) {
                return true;
            } else {
                return false;
            }
        }
        function goods_click(t) {
            t = $(t);
            var obj = t.find(".good_select");
            obj.prop("checked", !checkIsSelect(obj));

        }

        function select_all(obj) {
            $(".good_select").prop("checked", checkIsSelect($(obj)));
        }

        function select_all_id() {
            var ids = "";
            $("#list-display tr").each(function () {
                var select_obj = $(this).find(".good_select");
                if (checkIsSelect(select_obj)) {
                    if (ids != "") {
                        ids += ",";
                    }
                    ids += select_obj.data("id");
                }
            });
            return ids;
        }
    </script>


    <script>
        var show_details = function (id) {
            var details_info = "";
            for (var i = 0; i < current_json.length; i++) {
                var obj = current_json[i];

                if (obj.id == id) {
                    details_info += ' <div style="display: flex; margin: 20px 0; padding-bottom: 20px; border-bottom: 1px solid #eee;">            <div>                <b>支付方式：</b>            </div>            <div style="margin-left: 10px;">                ' + obj.payment_type + '            </div>        </div>';
                    details_info += ' <div style="display: flex; margin: 20px 0; padding-bottom: 20px; border-bottom: 1px solid #eee;">            <div>                <b>收款姓名：</b>            </div>            <div style="margin-left: 10px;">                ' + obj.payment_name + '            </div>        </div>';

                    if (obj.payment_type == "银行卡") {
                        details_info += ' <div style="display: flex; margin: 20px 0; padding-bottom: 20px; border-bottom: 1px solid #eee;">            <div>                <b>收款卡号：</b>            </div>            <div style="margin-left: 10px;">                ' + obj.payment_bankid + '            </div>        </div>';
                        details_info += ' <div style="display: flex; margin: 20px 0; padding-bottom: 20px; border-bottom: 1px solid #eee;">            <div>                <b>开户行：</b>            </div>            <div style="margin-left: 10px;">                ' + obj.payment_bankname + '            </div>        </div>';
                    } else {
                        details_info += ' <div style="display: flex; margin: 20px 0; padding-bottom: 20px; border-bottom: 1px solid #eee;">            <div>                <b>' + obj.payment_type + '账号：</b>            </div>            <div style="margin-left: 10px;">                ' + obj.payment_bankid + '            </div>        </div>';
                    }

                    _modal('收款信息', details_info);
                    break;
                }
            }
        }


        var create_order = function (id) {

            _modal("创建订单", "<div id='model-page' aid='" + id + "'>" + $("#buy_page").html() + "</div>");

            var details_info = "";
            for (var i = 0; i < current_json.length; i++) {
                var obj = current_json[i];
                if (obj.id == id) {
                    //$('#model-page .bank_item').hide();
                    //$('#model-page').attr();

                    $('#model-page .buyer_type').on('change', function () {
                        var name = $(this).val();
                        if (name == "银行卡") {
                            $('#model-page .bank_item').show();
                        } else {
                            $('#model-page .bank_item').hide();
                        }
                    });

                    $('#model-page .buyer_number').val((obj.total_amount - obj.deal_amount - obj.trans_amount));

                    if (obj.accept_separate != 1) {
                        $('#model-page .buyer_number').css({ "background": "#f1f1f1" }).attr({ "disabled": "disabled" });
                    }

                    break;
                }
            }
        }

        var new_order = function () {
            var post_data = {
                id: $('#model-page').attr("aid"),
                buyer_type: $('#model-page .buyer_type').val(),
                buyer_amount: $('#model-page .buyer_number').val(),
                buyer_name: $('#model-page .buyer_name').val(),
                buyer_bankname: $('#model-page .buyer_bankname').val(),
                buyer_bankid: $('#model-page .buyer_bankid').val(),
                actCode: $('#model-page .actCode').val()
            }

            $.ajax({
                type: "POST",
                url: "../Api/admin.aspx?do=new_order",
                data: post_data,
                datatype: "json",
                success: function (data) {
                    if (data.code == 1) {
                        layer.msg(data.msg, { icon: 1, time: 1000 }, function () {
                            $('#myModal').modal('hide');
                            getPager();
                        });
                    } else {
                        layer.msg(data.msg, { icon: 2, time: 1500 });
                    }
                },
                error: function () {
                    layer.msg("抱歉，操作有误", { icon: 2, time: 1500 });
                }
            });

        }
    </script>

    <style>
        .buy_list {
            display: flex;
            align-items: center;
            font-weight: bold;
            margin-top: 30px;
        }

            .buy_list .name {
                width: 100px;
                flex-shrink: 0;
            }

            .buy_list .item {
                width: 100%;
            }

                .buy_list .item input {
                    width: 100%;
                }
    </style>
    <div style="background: #fff; padding: 18px; display: none;" id="buy_page">

        <div class="buy_list">
            <div class="name">
                支付方式：
           
            </div>
            <div class="item">
                <select class="form-control buyer_type">
                    <option>银行卡</option>
                    <option>支付宝</option>
                    <option>微信</option>
                </select>
            </div>
        </div>

        <div class="buy_list">
            <div class="name" style="color: blue;">
                购买数量：           
            </div>
            <div class="item">
                <input class="form-control buyer_number" />
            </div>
        </div>

        <div class="buy_list">
            <div class="name">
                姓名：           
            </div>
            <div class="item">
                <input class="form-control buyer_name" />
            </div>
        </div>

        <div class="buy_list">
            <div class="name">
                账号：           
            </div>
            <div class="item">
                <input class="form-control buyer_bankid" />
            </div>
        </div>

        <div class="buy_list bank_item">
            <div class="name">
                开户行：           
            </div>
            <div class="item">
                <input class="form-control buyer_bankname" />
            </div>
        </div>

        <div class="buy_list bank_item">
            <div class="name">
                验证码：           
            </div>
            <div class="item">
                <input class="form-control actCode" />
            </div>
        </div>

        <a class="btn btn-success" onclick="new_order()" style="margin-top: 24px; margin-left: 100px;">
            <svg t="*************" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="4127" width="16" height="16">
                <path d="M512 170.666667a341.333333 341.333333 0 0 0-290.133333 521.216 42.666667 42.666667 0 0 1-72.533334 45.013333A424.874667 424.874667 0 0 1 85.333333 512C85.333333 276.352 276.352 85.333333 512 85.333333s426.666667 191.018667 426.666667 426.666667-191.018667 426.666667-426.666667 426.666667a424.874667 424.874667 0 0 1-234.666667-70.314667 42.666667 42.666667 0 1 1 46.933334-71.210667A341.333333 341.333333 0 1 0 512 170.666667z m222.165333 264.832a42.666667 42.666667 0 0 0-60.330666-60.330667L469.333333 579.669333l-119.168-119.168a42.666667 42.666667 0 0 0-60.330666 60.330667l149.333333 149.333333a42.666667 42.666667 0 0 0 60.330667 0l234.666666-234.666666z" fill="#14101C" p-id="4128"></path></svg>&nbsp;创建订单</a>

    </div>

</asp:Content>



<%@ Page Title="游戏记录" Language="C#" MasterPageFile="~/serv/m.master" AutoEventWireup="true" CodeFile="play_records.aspx.cs" Inherits="w_index" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="Server">
    <link href="../css/element-ui.css" rel="stylesheet" />
    <style>
        .el-tag.el-tag--test {
            background-color: #d5d5f5;
            border-color: #7d7dc7;
            color: #6d3333;
        }
    </style>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="Server">
    <div class="ibox float-e-margins">
        <div class="ibox-title">
            <h5>游戏记录</h5>
        </div>
        <div class="ibox-content">
            <%if (Request.QueryString["teamid"] + "" != "")
              {
            %>
            <a class="btn btn-success" onclick="history.go(-1)" style="margin-bottom: 18px;">返回结算数据</a>

            <%
              } %>



            
            <style>
                .switch-1 {
                    padding: 7px 12px;
                    border-radius: 3px;
                    font-weight: bold;
                    display: flex;
                    align-items: center;
                    margin-bottom: 10px;
                    margin-right: 10px;
                    cursor: pointer;
                    background: #eee;
                    color: #5a5b5c;
                    border: 1px solid #ddd;
                }

                    .switch-1.active {
                        background: linear-gradient(119deg, #6799ed, #67bf8b);
                        color: #fff;
                        border: 1px solid transparent;
                    }
            </style>

            <div style="display: flex;">
                
                <div class="switch-1" from_type="filter_th">
                    <svg t="" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="6932" width="23" height="23"  style="margin-right: 5px;"><path d="M810.0864 210.7904H261.6832a56.32 56.32 0 0 0-35.4816 99.9424l204.8 166.5536a10.5472 10.5472 0 0 1 3.8912 8.192V803.84a36.4032 36.4032 0 0 0 18.2784 31.5392l113.5104 64.8704a47.0016 47.0016 0 0 0 70.3488-40.96v-373.76a10.5472 10.5472 0 0 1 3.8912-8.192l204.8-166.5536a56.32 56.32 0 0 0-35.6352-99.9936z" fill="#9FA7FF" p-id="6933"></path><path d="M845.6192 310.7328a56.832 56.832 0 0 0 15.7696-20.0192 51.2 51.2 0 0 0-47.3088-30.0544H303.8208A52.5312 52.5312 0 0 0 256 335.104l174.9504 142.1824a10.5472 10.5472 0 0 1 3.8912 8.192v1.6384l26.4192 21.4528a9.9328 9.9328 0 0 1 3.6352 7.6288v294.7584a33.9456 33.9456 0 0 0 16.9984 29.3888l105.6256 60.3648a45.4656 45.4656 0 0 0 12.1344 4.7104 46.8992 46.8992 0 0 0 37.3248-46.08v-373.76a10.5472 10.5472 0 0 1 3.8912-8.192z" fill="#8891FF" p-id="6934"></path><path d="M818.432 316.2112H350.72A48.0768 48.0768 0 0 0 303.5136 373.76l127.4368 103.5264a10.5472 10.5472 0 0 1 3.8912 8.192v9.0112l60.2112 48.9984a9.0624 9.0624 0 0 1 3.328 7.0144V819.2a30.72 30.72 0 0 0 15.36 26.88L610.7648 901.12h0.3584a46.5408 46.5408 0 0 0 25.6-41.8816v-373.76a10.5472 10.5472 0 0 1 3.8912-8.192l194.56-158.1056a47.9744 47.9744 0 0 0-16.7424-2.9696z" fill="#6E75FF" p-id="6935"></path><path d="M559.9744 937.3184a82.432 82.432 0 0 1-40.96-11.0592l-125.8496-71.68a71.1168 71.1168 0 0 1-35.84-61.44V446.6176l-220.16-178.8928A93.0816 93.0816 0 0 1 196.096 102.4H803.84a93.0816 93.0816 0 0 1 58.88 165.3248l-219.904 178.8928v407.7568a81.92 81.92 0 0 1-41.2672 71.68 82.8416 82.8416 0 0 1-41.5744 11.264zM196.096 163.84a31.6416 31.6416 0 0 0-19.968 56.32l226.9696 184.32a42.2912 42.2912 0 0 1 15.6672 32.9216v355.072a9.5744 9.5744 0 0 0 4.8128 8.2944l125.7984 71.936a21.3504 21.3504 0 0 0 32-18.5344V437.6064a42.1376 42.1376 0 0 1 15.6672-33.1264l226.9184-184.32A31.6416 31.6416 0 0 0 803.84 163.84z" fill="#2E3138" p-id="6936"></path><path d="M889.7536 527.7696h-175.7696a30.72 30.72 0 0 1 0-61.44h175.7696a30.72 30.72 0 0 1 0 61.44zM889.7536 652.2368h-175.7696a30.72 30.72 0 0 1 0-61.44h175.7696a30.72 30.72 0 0 1 0 61.44zM889.7536 773.6832h-175.7696a30.72 30.72 0 0 1 0-61.44h175.7696a30.72 30.72 0 0 1 0 61.44z" fill="#2E3138" p-id="6937"></path></svg>排除托号
                </div>

            </div>
            <input id="from_type" type="hidden" />
            <script>
                $('.switch-1').on('click', function () {
                    from_type = $(this).attr('from_type');
                    if ($(this).hasClass("active")) {
                        $(this).removeClass("active");
                        from_type = "";
                    } else {
                        $(this).addClass("active").siblings().removeClass("active");
                    }
                    $('#from_type').val(from_type);
                    searchList();
                });

                $('[from_type="filter_th"]').addClass("active");
                $('#from_type').val("filter_th");
            </script>




            <div class="row layui-form">
                

                <div class="col-md-2">
                    <select id="isdraw">
                        <option value="">全部</option>
                        <option value="-2">撤单</option>
                        <option value="1">中奖</option>
                        <option value="-1">未中奖</option>
                    </select>
                </div>
                
                <div class="col-md-2">
                    <select id="gamecode">
                        <option value="">游戏类型</option>
                        <asp:Repeater ID="authLists" runat="server">
                            <ItemTemplate>
                                <option value="<%#Eval("authcode") %>"><%#Eval("name") %></option>
                            </ItemTemplate>
                        </asp:Repeater>
                    </select>
                </div>

           
                <div class="col-md-3">
                    <div class="form-group form-buttom" id="twoInputs">
                        <input type="text" id="start_time" name="ordername" class="form-control" placeholder="开始时间" style="width: 48%; display: inline-block;"  value="<%=Request.QueryString["date"] + "" != "" ?  Convert.ToDateTime(Request.QueryString["date"] + "").ToString("yyyy-MM-dd"):DateTime.Now.ToString("yyyy-MM-dd")  %>" autocomplete="off">
                        <input type="text" id="end_time" name="ordername" class="form-control" placeholder="结束时间" style="width: 48%; display: inline-block;" autocomplete="off">

                        <script>
                            $(document).ready(function () {
                                $('#start_time').datepicker({
                                    format: 'yyyy-mm-dd',
                                    autoclose: true
                                });

                                $('#end_time').datepicker({
                                    format: 'yyyy-mm-dd',
                                    autoclose: true
                                });
                            });
                        </script>
                    </div>
                </div>

                <div class="col-md-4">
                    <div class="form-group form-buttom">
                        <input type="text" id="search-keyword" name="ordername" class="form-control" placeholder="请输入搜索的信息">
                    </div>
                </div>

                <div class="col-md-6">
                    <div class="form-group form-buttom">
                        <a class="btn btn-success" onclick="searchList()">
                            <svg t="1688237921441" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="1949" width="16" height="16">
                                <path d="M361.130667 658.688a256 256 0 1 1 282.794666-44.672 42.496 42.496 0 0 0-10.368 14.933333c-0.853333 0.682667-1.621333 1.450667-2.389333 2.218667a42.666667 42.666667 0 0 0 0 60.330667l170.666667 170.666666a42.666667 42.666667 0 0 0 60.330666-60.330666l-142.762666-142.762667a341.333333 341.333333 0 1 0-169.344 99.242667 42.666667 42.666667 0 0 0-20.224-82.901334 254.762667 254.762667 0 0 1-168.704-16.725333z" fill="#14101C" p-id="1950"></path></svg>
                            查询</a>
                        <a class="btn btn-success" onclick="javascript:location.href=location.href;">
                            <svg t="1688237949108" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2211" width="16" height="16">
                                <path d="M512 192a298.24 298.24 0 0 0-245.162667 128 42.666667 42.666667 0 1 1-69.973333-48.810667 384.042667 384.042667 0 0 1 697.173333 180.48l36.608-34.645333a42.666667 42.666667 0 0 1 58.709334 61.952l-112.64 106.666667a42.666667 42.666667 0 0 1-71.253334-38.912A298.666667 298.666667 0 0 0 512 192z m0.170667 640a298.24 298.24 0 0 0 245.162666-128 42.666667 42.666667 0 1 1 69.973334 48.810667 384.042667 384.042667 0 0 1-697.173334-180.48l-36.650666 34.645333a42.666667 42.666667 0 0 1-58.666667-61.952l112.64-106.666667a42.666667 42.666667 0 0 1 71.253333 38.912 298.666667 298.666667 0 0 0 293.418667 354.730667z" fill="#14101C" p-id="2212"></path></svg>
                            刷新</a>
                        <%--<a class="btn btn-success" onclick="opennew()">
                            <svg t="1688237854283" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="15169" width="16" height="16">
                                <path d="M725.333333 213.333333H298.666667a85.333333 85.333333 0 0 0-85.333334 85.333334v341.333333a42.666667 42.666667 0 1 1-85.333333 0V298.666667a170.666667 170.666667 0 0 1 170.666667-170.666667h426.666666a170.709333 170.709333 0 0 1 167.850667 139.733333v488.533334A170.709333 170.709333 0 0 1 725.333333 896H298.666667a170.538667 170.538667 0 0 1-144.469334-79.744l1.194667-0.682667a42.666667 42.666667 0 0 1 76.501333-37.034666c15.658667 19.584 39.765333 32.128 66.773334 32.128h426.666666a85.333333 85.333333 0 0 0 85.333334-85.333334V298.666667a85.333333 85.333333 0 0 0-85.333334-85.333334z m-213.333333 512a42.666667 42.666667 0 0 1-42.666667-42.666666v-128H341.333333a42.666667 42.666667 0 1 1 0-85.333334h128V341.333333a42.666667 42.666667 0 1 1 85.333334 0v128h128a42.666667 42.666667 0 1 1 0 85.333334h-128v128a42.666667 42.666667 0 0 1-42.666667 42.666666z" fill="#14101C" p-id="15170"></path></svg>&nbsp;添加</a>--%>
                        <%--<a class="btn btn-success" onclick="update_select(-9)">
                            <svg t="1688237896673" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="1699" width="16" height="16">
                                <path d="M612.693333 170.666667l8.533334 42.666666H402.773333l8.533334-42.666666h201.386666z m-293.632 25.941333a86.186667 86.186667 0 0 0-1.706666 16.725333H170.666667a42.666667 42.666667 0 0 0 0 85.333334h682.666666a42.666667 42.666667 0 1 0 0-85.333334h-146.730666c0-5.504-0.554667-11.093333-1.706667-16.725333l-8.533333-42.666667A85.333333 85.333333 0 0 0 612.778667 85.333333H411.306667a85.333333 85.333333 0 0 0-83.712 68.608l-8.533334 42.666667zM426.666667 469.333333a42.666667 42.666667 0 0 0-42.666667 42.666667v170.666667a42.666667 42.666667 0 1 0 85.333333 0v-170.666667a42.666667 42.666667 0 0 0-42.666666-42.666667z m128 42.666667a42.666667 42.666667 0 1 1 85.333333 0v170.666667a42.666667 42.666667 0 1 1-85.333333 0v-170.666667zM255.402667 384A42.666667 42.666667 0 0 1 298.666667 426.069333L301.738667 640H301.653333a42.666667 42.666667 0 1 1-85.290666-2.176l-2.986667-210.56A42.666667 42.666667 0 0 1 255.36 384z m51.925333 396.586667h0.042667A103.381333 103.381333 0 0 0 406.698667 853.333333h210.602666c54.186667 0 98.56-40.789333 103.210667-92.757333L725.333333 426.069333a42.666667 42.666667 0 0 1 85.333334 1.194667l-4.864 337.365333-0.128 1.365334C798.08 863.872 715.562667 938.666667 617.301333 938.666667H406.698667a188.885333 188.885333 0 0 1-180.096-130.389334 42.666667 42.666667 0 0 1 80.725333-27.648z" fill="#14101C" p-id="1700"></path></svg>删除</a>--%>
                    </div>
                </div>

            </div>
            <div style="display: flex; margin-bottom: 10px;    flex-wrap: wrap;">


                <div style="background: #fff; padding: 17px; border-radius: 8px; width: 100%; margin: 0 10px; max-width: 250px;">
                    <div style="display: flex; align-items: center;">
                        <svg t="" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="7153" width="20" height="20">
                            <path d="M513.0752 510.8224m-450.816 0a450.816 450.816 0 1 0 901.632 0 450.816 450.816 0 1 0-901.632 0Z" fill="#FFF7DC" p-id="7154"></path><path d="M863.0784 503.7056c0-52.7872-56.3712-67.0208-86.016-77.1072-29.6448-10.0864-176.0768-64.2048-176.0768-64.2048s-100.1472-163.328-190.0032-101.888c-21.8112 13.7728-18.2272 63.6416 12.032 84.5312-41.8304-7.5776-110.7968 7.9872-107.264 47.616 3.584 39.6288 58.3168 60.0576 155.2896 43.6224 16.896-3.584 40.4992 8.9088 12.032 32.9216-15.1552 10.7008-80.9984 28.928-225.1776 3.1232-102.6048-18.3808-162.816-32.2048-189.0304-38.7072a451.6864 451.6864 0 0 0-6.6048 77.2608c0 149.504 72.8064 282.0096 184.8832 364.032 22.016-6.5536 45.0048-15.104 68.608-26.112 60.5184-28.4672 42.7008-57.856 28.4672-68.096s-42.7008-50.7392 36.9152-98.3552 178.7392-90.1632 296.8064-105.0112c37.9904-5.3248 83.6608-5.9392 105.6256-7.1168 21.9648-1.2288 79.5136-13.6704 79.5136-66.5088z m-260.5056-41.8304c-15.872 0-28.7744-12.9024-28.7744-28.7744s12.9024-28.7744 28.7744-28.7744 28.7744 12.9024 28.7744 28.7744-12.8512 28.7744-28.7744 28.7744z" fill="#FF8E12" p-id="7155"></path><path d="M600.9856 362.3424s-100.1472-163.328-190.0032-101.888c-21.8112 13.7728-18.2272 63.6416 12.032 84.5312-41.8304-7.5776-110.7968 7.9872-107.264 47.616 3.584 39.6288 58.3168 60.0576 155.2896 43.6224 16.896-3.584 40.4992 8.9088 12.032 32.9216-15.1552 10.7008-80.9984 28.928-225.1776 3.1232-102.6048-18.3808-162.816-32.2048-189.0304-38.7072a451.6864 451.6864 0 0 0-6.6048 77.2608c0 104.3456 35.5328 200.3968 95.0784 276.8384a426.51136 426.51136 0 0 0 163.84 32.5632c11.1616 0 22.1696-0.5632 33.1264-1.3824 9.6768-17.1008-0.9216-31.5904-10.0864-38.1952-14.2336-10.24-42.7008-50.7392 36.9152-98.3552s178.7392-90.1632 296.8064-105.0112c9.8304-1.3824 20.1728-2.4576 30.464-3.2768a425.0112 425.0112 0 0 0 39.68-157.696c-52.48-18.944-147.0976-53.9648-147.0976-53.9648z m1.5872 99.5328c-15.872 0-28.7744-12.9024-28.7744-28.7744s12.9024-28.7744 28.7744-28.7744 28.7744 12.9024 28.7744 28.7744-12.8512 28.7744-28.7744 28.7744z" fill="#FCA315" p-id="7156"></path><path d="M410.9312 260.4544C389.12 274.2272 392.704 324.096 422.9632 344.9856c-41.8304-7.5776-110.7968 7.9872-107.264 47.616 3.584 39.6288 58.3168 60.0576 155.2896 43.6224 16.896-3.584 40.4992 8.9088 12.032 32.9216-15.1552 10.7008-80.9984 28.928-225.1776 3.1232-102.6048-18.3808-162.816-32.2048-189.0304-38.7072a451.6864 451.6864 0 0 0-6.6048 77.2608c0 49.0496 7.8848 96.256 22.3744 140.4416a428.34944 428.34944 0 0 0 119.552 17.0496c160.8704 0 300.9536-88.8832 373.9648-220.16-2.7136-4.4032-4.3008-9.5232-4.3008-15.0528 0-14.7456 11.1104-26.88 25.3952-28.5696 5.12-12.3392 9.728-24.9344 13.6704-37.7856-7.424-2.7648-11.9296-4.4032-11.9296-4.4032s-100.096-163.2768-190.0032-101.888z" fill="#FCB138" p-id="7157"></path><path d="M257.8944 472.2688c-102.6048-18.3808-162.816-32.2048-189.0304-38.7072a451.6864 451.6864 0 0 0-6.6048 77.2608c0 1.5872 0.0512 3.1232 0.0512 4.6592 11.3152 0.9216 22.7328 1.4848 34.2528 1.4848 65.4848 0 127.5392-14.7968 183.04-41.1136-7.0656-1.024-14.2336-2.2528-21.7088-3.584zM422.9632 344.9856c-41.8304-7.5776-110.7968 7.9872-107.264 47.616 1.5872 17.92 13.7216 31.8976 35.4816 40.3456 61.184-45.4144 109.824-106.752 139.7248-177.8688-25.6512-11.264-53.248-12.8512-79.9744 5.376-21.76 13.824-18.2272 63.6416 12.032 84.5312z" fill="#FFC65E" p-id="7158"></path></svg>
                        <span style="color: #fba722; font-weight: bold; margin-left: 8px;">游戏总额</span>
                    </div>

                    <div style="font-size: 22px; margin-top: 6px; font-weight: bold; color: #7e868f;">
                        <span id="total_amount">0.00</span><span style="font-size: 12px; font-weight: 100; margin-left: 10px;">[<span id="total_number">0</span>单]</span>
                    </div>
                </div>


                <div style="background: #fff; padding: 17px; border-radius: 8px; width: 100%; margin: 0 10px; max-width: 250px;">
                    <div style="display: flex; align-items: center;">
                        <svg t="" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="" width="20" height="20"><path d="M821.5 981h-452C258.1 981 167 889.9 167 778.5v-284C167 383.1 258.1 292 369.5 292h452c111.4 0 202.5 91.1 202.5 202.5v284c0 111.4-91.1 202.5-202.5 202.5z" fill="#9BBCFA" p-id="1768"></path><path d="M784 928.4H241c-105.1 0-191-85.9-191-191V286.6c0-105 85.9-191 191-191h543c105 0 191 86 191 191V737.4c0 105.1-85.9 191-191 191z" fill="#0057FC" p-id="1769"></path><path d="M241 928.4c-105.1 0-191-85.9-191-191V286.6c0-105 85.9-191 191-191h543c105 0 191 86 191 191C934.1 711 655.2 889.5 241 928.4z" fill="#0066FD" p-id="1770"></path><path d="M50 708.4V286.6c0-105 85.9-191 191-191h472C782 456 477 733 50 708.4z" fill="#0080FE" p-id="1771"></path><path d="M50 466.5V286.6c0-105 85.9-191 191-191h204.5C454.4 421.7 351 438 50 466.5z" fill="#008DFF" p-id="1772"></path><path d="M723.8 547.2h-90.6V440.1l-115-73.7v-46.8c32.8 5.4 92.2 20.5 118.7 1.5 0-20 0.8-53.3 0-65.7-39.1 15-141.4 0-141.4 0v111.1l-115 73.6v107.1h-90.6c-11.6 0-20.9 8.6-20.9 19.1v218h159.3v-22.1c0-37.8 30.2-71.8 71.4-75.1 46.5-3.7 85.5 29.7 85.5 71.3v25.9h159.3v-218c0.2-10.4-9.1-19.1-20.7-19.1z m-216.9-4.8c-22.2 0-42.2-12.2-50.7-31-8.5-18.7-3.8-40.3 11.9-54.6 15.7-14.4 39.4-18.6 59.9-10.9 20.5 7.8 33.9 26 33.9 46.3 0 27.7-24.7 50.2-55 50.2z" fill="#FFFFFF" p-id="1773"></path></svg>
                        <span style="color: #005efd; font-weight: bold; margin-left: 8px;">有效总额</span>
                    </div>

                    <div style="font-size: 22px; margin-top: 6px; font-weight: bold; color: #7e868f;">
                        <span id="total_really_amount">0.00</span><span style="font-size: 12px; font-weight: 100; margin-left: 10px;">[<span id="total_really_number">0</span>单]</span>
                    </div>
                </div>

                

                <div style="background: #fff; padding: 17px; border-radius: 8px; width: 100%; margin: 0 10px; max-width: 250px;">
                    <div style="display: flex; align-items: center;">
                        <svg t="" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="" width="20" height="20"><path d="M821.5 981h-452C258.1 981 167 889.9 167 778.5v-284C167 383.1 258.1 292 369.5 292h452c111.4 0 202.5 91.1 202.5 202.5v284c0 111.4-91.1 202.5-202.5 202.5z" fill="#FCEDB0" p-id="1088"></path><path d="M784 928.4H241c-105.1 0-191-85.9-191-191V286.6c0-105 85.9-191 191-191h543c105 0 191 86 191 191V737.4c0 105.1-85.9 191-191 191z" fill="#FF9001" p-id="1089"></path><path d="M241 928.4c-105.1 0-191-85.9-191-191V286.6c0-105 85.9-191 191-191h543c105 0 191 86 191 191C934.1 711 655.2 889.5 241 928.4z" fill="#FFA61F" p-id="1090"></path><path d="M50 708.4V286.6c0-105 85.9-191 191-191h472C782 456 477 733 50 708.4z" fill="#FFD055" p-id="1091"></path><path d="M50 466.5V286.6c0-105 85.9-191 191-191h204.5C454.4 421.7 351 438 50 466.5z" fill="#FFE774" p-id="1092"></path><path d="M387 791.1s-30-33.7-58.4-61.6c-30.3-8.3-60.8-16.1-91.6-23.3l62.4-108c33 42.8 90.8 79.8 145.4 93.2L387 791.1z m284.7-66.9C643.3 752 608 791.1 608 791.1l-57.3-99.7c54.5-13.3 107-50.1 140-93.2l67.5 108c-0.3 0.2-46.1 6.7-86.5 18zM497.4 255.4c-108.8 0-202.2 98.9-202.2 207.9s93.5 197.1 202.2 197.1 202.2-93.5 202.2-202.5S606 255.4 497.4 255.4z m116.7 193.7l-35.4 36.7c-4.8 4.8-7.2 11.8-5.6 18.8v0.8l8 49.8c0.8 4.8 0 9.9-2.4 14-6.2 11-19.9 15.3-30.8 9.1l-40.7-21.7c-6.4-3.5-14.4-3.5-20.9 0.8l-39.6 21.2c-4.6 2.4-9.3 3-14.4 2.4-12.1-1.9-20.6-13.7-18.8-25.7l8.3-51.7c0.8-6.2-1.1-12.1-5.3-16.6l-35.6-37.7c-3-3.5-5.3-8-6.2-12.6-1.9-12.1 7-23.5 19-25.4l45.5-7.2c7.2-0.2 13.7-4.8 16.6-11.5l20.9-44.5c3.8-7.5 11.8-12.9 20.6-12.9 8.9 0 16.6 4.8 20.6 12.9l20.6 44.2c3 7 9.3 11.5 17.1 12.1l45.5 7c4.6 0.8 9.1 3 12.6 6.1 8.7 8.6 9 23 0.4 31.6z" fill="#FFFFFF" p-id="1093"></path></svg>
                        <span style="color: #fba722; font-weight: bold; margin-left: 8px;">中奖总额</span>
                    </div>

                    <div style="font-size: 22px; margin-top: 6px; font-weight: bold; color: #7e868f;">
                        <span id="total_okamount">0.00</span><span style="font-size: 12px; font-weight: 100; margin-left: 10px;">[<span id="total_oknumber">0</span>单]</span>
                    </div>
                </div>

                

                <div style="background: #fff; padding: 17px; border-radius: 8px; width: 100%; margin: 0 10px; max-width: 250px;">
                    <div style="display: flex; align-items: center;">
                        <svg t="" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="" width="20" height="20"><path d="M821.5 981h-452C258.1 981 167 889.9 167 778.5v-284C167 383.1 258.1 292 369.5 292h452c111.4 0 202.5 91.1 202.5 202.5v284c0 111.4-91.1 202.5-202.5 202.5z" fill="#F9B2AB" p-id="1311"></path><path d="M784 928.4H241c-105.1 0-191-85.9-191-191V286.6c0-105 85.9-191 191-191h543c105 0 191 86 191 191V737.4c0 105.1-85.9 191-191 191z" fill="#FA3F2A" p-id="1312"></path><path d="M241 928.4c-105.1 0-191-85.9-191-191V286.6c0-105 85.9-191 191-191h543c105 0 191 86 191 191C934.1 711 655.2 889.5 241 928.4z" fill="#FD5E4D" p-id="1313"></path><path d="M50 708.4V286.6c0-105 85.9-191 191-191h472C782 456 477 733 50 708.4z" fill="#FD776A" p-id="1314"></path><path d="M50 466.5V286.6c0-105 85.9-191 191-191h204.5C454.4 421.7 351 438 50 466.5z" fill="#FE837A" p-id="1315"></path><path d="M768.46484375 254.12890625H258.25976562c-27.24609375 0-49.48242188 22.32421875-49.48242187 49.48242188v280.10742187c0 9.31640625 11.42578125 13.88671875 17.84179688 7.20703125 5.625-5.88867188 12.74414063-11.07421875 21.62109375-15.02929688 4.83398438-2.19726563 7.29492188-7.734375 5.53710937-12.83203124-27.0703125-80.06835938 9.58007813-133.2421875 28.828125-155.30273438 28.91601563-33.13476563 100.81054688-57.83203125 165.9375-34.453125 64.77539063 22.41210938 99.05273438 95.00976563 87.36328125 146.25-3.07617188 13.44726563 13.62304688 16.69921875 18.89648438 4.83398438 0.17578125-0.3515625 0.3515625-0.703125 0.52734374-0.96679688 2.90039063-5.00976563 25.75195313-41.57226563 75.41015626-24.78515625 53.17382813 18.01757813 38.40820313 69.08203125 37.35351562 73.56445313l-39.81445312 180.96679687c-1.40625 6.50390625 3.515625 12.56835938 10.10742187 12.56835938h130.16601563c27.24609375 0 49.48242188-22.32421875 49.48242187-49.48242188V303.61132812c0-27.15820313-22.32421875-49.48242188-49.5703125-49.48242187z m-23.99414063 179.47265625H640.3203125c-9.58007813 0-17.31445313-9.22851563-17.31445313-20.7421875 0-11.42578125 7.734375-20.7421875 17.31445313-20.7421875h104.15039063c9.58007813 0 17.31445313 9.22851563 17.31445312 20.7421875 0 11.51367188-7.734375 20.7421875-17.31445313 20.7421875z m1e-8-80.06835938H501.453125c-9.58007813 0-17.31445313-9.22851563-17.31445313-20.74218749 0-11.42578125 7.734375-20.7421875 17.31445313-20.7421875h242.9296875c9.58007813 0 17.31445313 9.22851563 17.31445313 20.7421875 0.08789063 11.51367188-7.64648438 20.7421875-17.22656251 20.7421875z" fill="#FFFFFF" p-id="1316"></path><path d="M611.40429688 549.17773438c-7.47070313-2.02148438-15.1171875 2.4609375-17.13867188 9.84375L557.703125 695.69140625c-20.12695313-48.1640625-60.38085938-85.78125-110.390625-102.39257813 24.08203125-17.40234375 39.90234375-45.3515625 40.60546875-77.08007812C489.1484375 461.0234375 443.88476562 415.40820312 388.77734375 416.19921875 335.33984375 416.90234375 292.2734375 460.40820312 292.2734375 514.02148438c0 32.60742188 15.99609375 61.43554688 40.60546875 79.27734374-67.5 22.5-117.68554688 83.40820313-123.83789063 156.97265626-0.703125 8.26171875 5.71289063 15.38085938 13.97460938 15.38085937 7.20703125 0 334.95117188-0.17578125 335.21484375-0.17578125 1.0546875-0.08789063 9.05273438 0.43945313 9.05273438 0.43945312l54.22851562-199.59960937c1.84570313-7.47070313-2.63671875-15.20507813-10.10742188-17.13867188z" fill="#FFFFFF" p-id="1317"></path></svg>
                        <span style="color: #fd5e4d; font-weight: bold; margin-left: 8px;">本次消耗</span>
                    </div>

                    <div style="font-size: 22px; margin-top: 6px; font-weight: bold; color: #7e868f;">
                        <span id="total_cost_amount">0.00</span>
                    </div>
                </div>


            </div>



            <div class="table-responsive">
                <table class="table">
                    <thead>
                        <tr role="head">
                            <th sort="true">用户</th>
                            <th sort="true">直属上级</th>
                            <th sort="true">团队ID</th>
                            <th sort="true">游戏类型</th>
                            <th sort="true">游戏金额</th>
                            <th sort="true">状态</th>
                            <th sort="true">订单ID</th>
                            <th sort="true">日期</th>
                            <th sort="true"></th>
                        </tr>
                    </thead>
                    <tbody id="list-display">
                    </tbody>
                </table>
            </div>


            <div class="layui-table-page">
                <div id="layui-table-page1">
                    <div class="layui-box layui-laypage layui-laypage-default" id="layui-laypage-1">
                        <div style="display: inline-block;" class="cpages">
                            <!--page grow -->
                        </div>
                        <span class="layui-laypage-skip">到第<input type="text" min="1" value="1" class="layui-input target-page" />页
                            <button type="button" class="layui-laypage-btn" onclick="javascript:pager_index=$('.target-page').val()-1;getPager();">确定</button></span>
                        <span class="layui-laypage-count">共 <span class="page_count"></span>条</span>
                        <span class="layui-laypage-limits">
                            <select class="s_pages" lay-ignore="" onchange="javascript:pager_size=$(this).val();getPager();">
                                <option value="10">10 条/页</option>
                                <option value="20" selected="">20 条/页</option>
                                <option value="30">30 条/页</option>
                                <option value="50">50 条/页</option>
                                <option value="100">100 条/页</option>
                                <option value="200">200 条/页</option>
                            </select></span>
                    </div>
                </div>
            </div>

        </div>
    </div>
    <script>
        var _action = 'play_records';
        var _actionName = '游戏记录';
    </script>
    <script>
        function opennew() {
            var modify_model = [];

            modify_model.push({ name: '返佣备注', id: 'name', value: '' });
            modify_model.push({ name: '达成业绩', id: 'team_amount', value: '' });
            modify_model.push({ name: '返佣金额', id: 'brok_amount', value: '' });

            edit({ aname: 'admin', title: '添加' + _actionName, action: _action, id: '', data: modify_model });
        }
    </script>
    <script>

        var pager_size = 20;
        var pager_index = 0;
        var pager_key = "";


        $("#pagesize").val(pager_key);
        function setPageSize(obj) {
            pager_index = 0;
            pager_key = $("#search-keyword").val();
            opt = $(obj).val();
            getPager();
        }

        function searchList() {
            opt = $("#opt1").val();
            pager_index = 0;
            pager_key = $("#search-keyword").val();
            getPager();
        }

        function getPager() {

            $.ajax({
                type: "POST",
                url: "../api/admin.aspx?do=page",
                data: { limit: pager_size, p: pager_index, page: _action, keyword: pager_key, start_time: $("#start_time").val(), end_time: $("#end_time").val(), teamid: '<%=Request.QueryString["teamid"] %>', isdraw: $("#isdraw").val(), from_type: $('#from_type').val(), gamecode: $('#gamecode').val() },
                datatype: "json",
                success: function (json) {
                    $("#list-display").html("");
                    if (json.code == 1) {
                        var data = json.data.list;
                        for (var i = 0; i < data.length; i++) {
                            var tr = "";
                            tr += "<tr class='test1'>";
                            tr += ("<td class=''>" + ('<span style="background: #f3ecec;color: #956b0a;font-size: 12px;border-radius: 3px;padding: 1px 4px;margin-right: 5px;font-weight: bold;border: 1px solid #ccc;">' + (data[i].typename == 'th' ? '托号' : data[i].typename == 'user' ? '普通' : data[i].typename == 'super' ? '高级' : data[i].typename == 'reg' ? '新' : '其他') + '</span>') + (data[i].group_name == '-' ? '' : "<span class='button-style style-js'>" + data[i].group_name + "</span>") + data[i].phone + '<span style="color: #b53d10;margin-left: 2px;">[' + data[i].userid + ']</span>' + "</td>");

                            tr += ("<td class=''>" + ('<span style="background: #f3ecec;color: #956b0a;font-size: 12px;border-radius: 3px;padding: 1px 4px;margin-right: 5px;font-weight: bold;border: 1px solid #ccc;">' + (data[i].parent_typename == 'th' ? '托号' : data[i].parent_typename == 'user' ? '普通' : data[i].parent_typename == 'super' ? '高级' : data[i].parent_typename == 'reg' ? '新' : '其他') + '</span>') + data[i].parent_phone + '<span style="color: #b53d10;margin-left: 2px;">[' + data[i].parentid + ']</span>' + "</td>");
                            tr += ("<td class=''>" + data[i].relaids + "</td>");
                            tr += ("<td class=''>" + data[i].gamename + "</td>");
                            tr += ("<td class=''>" + data[i].amount + "</td>");
                            tr += ("<td class=''>" + (data[i].isdraw == -2 ? '<span style="color:#aaa">撤单</span>' : data[i].isdraw == 1 ? '<span style="color:green;">中奖 ' + data[i].okamount + '</span>' : data[i].isdraw == -1 ? '<span style="color:#a9554d;">未中奖</span>' : '<span>其他' + data[i].isdraw + '</span>') + "</td>");
                            tr += ("<td class=''>" + data[i].oid + "</td>");
                            tr += ("<td class=''><b>" + data[i].create_time + "</b></td>");

                            var modify_model = new Array();

                            tr += ("<td class='action-cell '>"
                                + ' <div class="card-toolbar">'
                                //+ '<a class="card-button common-button" ' + "onclick='edit(" + edtext({ aname: 'admin', title: _actionName + "修改", action: _action, id: data[i].id, data: modify_model }) + ")'" + '><svg  class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg"  width="16" height="16" style="position:relative;top:3px;"><path d="M358.165868 554.624796c-0.533143 0.680499-1.066285 1.391696-1.303692 2.251274l-41.104163 150.700257c-2.400676 8.772804 0.059352 18.226107 6.550183 24.892947 4.860704 4.742001 11.261485 7.350408 18.077727 7.350408 2.252297 0 4.504594-0.267083 6.727215-0.860601l149.630902-40.808428c0.23843 0 0.357134 0.207731 0.534166 0.207731 1.718131 0 3.408633-0.62217 4.683672-1.927909l400.113747-400.054395c11.883655-11.897981 18.404162-28.109198 18.404162-45.74281 0-19.989263-8.476045-39.963177-23.324218-54.767348l-37.786605-37.844933c-14.81645-14.848173-34.822087-23.338544-54.797024-23.338544-17.631566 0-33.842783 6.520507-45.75816 18.388812L358.758362 553.232077C358.344946 553.615816 358.462626 554.179658 358.165868 554.624796M862.924953 257.19778l-39.742143 39.71349-64.428382-65.451688 39.180348-39.179324c6.193049-6.222725 18.194384-5.318122 25.308409 1.822508l37.813211 37.845956c3.943822 3.941775 6.195096 9.18622 6.195096 14.372336C867.223862 250.574942 865.710392 254.42769 862.924953 257.19778M429.322487 560.907896l288.712541-288.728914 64.459081 65.49569L494.314711 625.838721 429.322487 560.907896zM376.718409 677.970032l20.863167-76.580143 55.656601 55.657624L376.718409 677.970032z" fill="#e6e6e6" p-id="1994"></path><path d="M888.265084 415.735539c-15.144932 0-27.562752 12.313443-27.620058 27.665083l0 372.98283c0 19.559475-15.885805 35.444257-35.475979 35.444257L194.220958 851.827709c-19.559475 0-35.504632-15.883759-35.504632-35.444257L158.716326 207.602222c0-19.575848 15.945157-35.474956 35.504632-35.474956l406.367171 0c15.231913 0 27.592428-12.371772 27.592428-27.606755 0-15.202237-12.360516-27.590382-27.592428-27.590382L190.013123 116.930129c-47.684022 0-86.49291 38.779212-86.49291 86.49291L103.520213 820.59231c0 47.713698 38.808888 86.47756 86.49291 86.47756l639.334083 0c47.715745 0 86.509283-38.763862 86.509283-86.47756L915.856489 443.222567C915.794068 428.048983 903.408993 415.735539 888.265084 415.735539" fill="#e6e6e6" p-id="1995"></path></svg>&nbsp;编辑</a>'
                                //+ '&nbsp;<a class="card-button common-button" ' + "onclick=\"event_create({'aname':'admin','name':'" + _action + "','event':'delete','id':" + data[i].id + "})\"" + '><svg t="" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="1699" width="16" height="16" style="position:relative;top:3px;"><path d="M612.693333 170.666667l8.533334 42.666666H402.773333l8.533334-42.666666h201.386666z m-293.632 25.941333a86.186667 86.186667 0 0 0-1.706666 16.725333H170.666667a42.666667 42.666667 0 0 0 0 85.333334h682.666666a42.666667 42.666667 0 1 0 0-85.333334h-146.730666c0-5.504-0.554667-11.093333-1.706667-16.725333l-8.533333-42.666667A85.333333 85.333333 0 0 0 612.778667 85.333333H411.306667a85.333333 85.333333 0 0 0-83.712 68.608l-8.533334 42.666667zM426.666667 469.333333a42.666667 42.666667 0 0 0-42.666667 42.666667v170.666667a42.666667 42.666667 0 1 0 85.333333 0v-170.666667a42.666667 42.666667 0 0 0-42.666666-42.666667z m128 42.666667a42.666667 42.666667 0 1 1 85.333333 0v170.666667a42.666667 42.666667 0 1 1-85.333333 0v-170.666667zM255.402667 384A42.666667 42.666667 0 0 1 298.666667 426.069333L301.738667 640H301.653333a42.666667 42.666667 0 1 1-85.290666-2.176l-2.986667-210.56A42.666667 42.666667 0 0 1 255.36 384z m51.925333 396.586667h0.042667A103.381333 103.381333 0 0 0 406.698667 853.333333h210.602666c54.186667 0 98.56-40.789333 103.210667-92.757333L725.333333 426.069333a42.666667 42.666667 0 0 1 85.333334 1.194667l-4.864 337.365333-0.128 1.365334C798.08 863.872 715.562667 938.666667 617.301333 938.666667H406.698667a188.885333 188.885333 0 0 1-180.096-130.389334 42.666667 42.666667 0 0 1 80.725333-27.648z" fill="#14101C" p-id="1700"></path></svg>&nbsp;删除</a>'
                                + '</div></td>');


                            tr += "</tr>";
                            $("#list-display").append(tr)
                        }
                        $(".page_size").html(pager_size);
                        $(".page_count").html(json.data.count);
                        $(".cpages").html(json.data.html);



                        $("#total_amount").html(json.total_amount);
                        $("#total_number").html(json.total_number);
                        $("#total_teambork").html(json.total_teambork);
                        $("#total_userbork").html(json.total_userbork);


                        $("#total_really_amount").html(json.total_really_amount);
                        $("#total_really_number").html(json.total_really_number);

                        $("#total_okamount").html(json.total_okamount);
                        $("#total_oknumber").html(json.total_oknumber);
                        $("#total_cost_amount").html((parseNumber(json.total_really_amount) - parseNumber(json.total_okamount)).toFixed(2));
                        

                    } else {

                    }
                },
                error: function () {
                }
            });
        }
        getPager();

        function _pt(i) {
            pager_index = i;
            getPager();
        }
    </script>

    <script>
        layui.use(['form', 'layedit', 'laydate'], function () {
        });
    </script>

    <script>
        function update_select(state) {
            event_create({ 'aname': 'admin', 'name': _action, 'event': 'update', 'id': select_all_id(), 'state': state });
        }

        function _event(evt) {
            event_create({ 'aname': 'admin', 'name': _action, 'event': evt, 'id': select_all_id() });
        }

        //function report_exl() {
        //    var start_time = "";
        //    var end_time = "";
        //    var opt1 = req("id");
        //    var opt2 = $("#opt1").val();
        //    var checktime = $("#checktime").val();
        //    var g = checktime.split(' - ');
        //    console.log(g);
        //    if (g.length > 1) {
        //        start_time = g[0];
        //        end_time = g[1];
        //    }

        //    export_data({ name: "cdks", key: pager_key, start_time: start_time, end_time: end_time, opt1: opt1, opt2: opt2 });
        //}
    </script>

    <script>
        var click_ids = {};
        function checkIsSelect(obj) {
            if (obj.prop('checked')) {
                return true;
            } else {
                return false;
            }
        }
        function goods_click(t) {
            t = $(t);
            var obj = t.find(".good_select");
            obj.prop("checked", !checkIsSelect(obj));

        }

        function select_all(obj) {
            $(".good_select").prop("checked", checkIsSelect($(obj)));
        }

        function select_all_id() {
            var ids = "";
            $("#list-display tr").each(function () {
                var select_obj = $(this).find(".good_select");
                if (checkIsSelect(select_obj)) {
                    if (ids != "") {
                        ids += ",";
                    }
                    ids += select_obj.data("id");
                }
            });
            return ids;
        }
    </script>

    <%-- <script src="https://www.jq22.com/demo/jquerytuozhuai202005141436/js/Sortable.min.js"></script>

    
<script type="text/javascript">
    new Sortable(document.getElementById('list-display'), {
        handle: '.good_select', // handle's class
        animation: 150,
        // direction: 'horizontal',,

        // 结束拖拽
        onEnd: function (/**Event*/evt) {
            var itemEl = evt.item;  // dragged HTMLElement
            evt.to;    // target list
            evt.from;  // previous list
            evt.oldIndex;  // element's old index within old parent
            evt.newIndex;  // element's new index within new parent
            evt.clone // the clone element
            evt.pullMode;  // when item is in another sortable: `"clone"` if cloning, `true` if moving

            console.log('evt', evt.to, evt.from, evt.oldIndex, evt.newIndex);
        }
    });

    //$(".list_con .iconshanchu").bind('touchstart', function () {
    //    $(this).parent().parent().remove();
    //    event.preventDefault();
    //    event.stopPropagation();
    //});
</script>
    --%>
</asp:Content>


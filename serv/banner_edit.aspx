<%@ Page Title="轮播图编辑" Language="C#" MasterPageFile="~/serv/m.master" AutoEventWireup="true" CodeFile="banner_edit.aspx.cs" Inherits="w_index" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="Server">
    <style>
        .el-tag.el-tag--test {
            background-color: #d5d5f5;
            border-color: #7d7dc7;
            color: #6d3333;
        }
    </style>

    <link href="../serv/wangeditor/normalize.min.css" rel="stylesheet" />
    <link href="../serv/wangeditor/style.css" rel="stylesheet" />

    <link href="../serv/wangeditor/layout.css" rel="stylesheet" />
    <link href="../serv/wangeditor/view.css" rel="stylesheet" />

    <script src="../serv/wangeditor/custom-elem.js"></script>

</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="Server">
    <div class="ibox float-e-margins">
        <div class="ibox-title">
            <h5><%=Request.QueryString["id"] + "" == "" ? "添加轮播图" : "编辑轮播图" %></h5>
        </div>
        <div class="ibox-content">

            <h1>轮播图名称：
            </h1>

            <input id="name" style="width: 100%; padding: 12px; border-radius: 5px; border: 1px solid #ddd; font-size: 15px; font-weight: bold; color: #333; background: #f5f5f5;" value="<%=uConfig.gd(dt, "name") %>" />

            <br />
            <br />

            
            <h1>预览图片：
            </h1>

             <div id="imgurl" style="background: #eee; width: 598px;min-height:130px; cursor: pointer; display: flex; align-items: center; justify-content: center; color: #aaa; font-size: 17px;" onclick="upload_images()"><%=uConfig.gd(dt, "imgurl") == "" ? "<b>请选择图片</b>" : "<img style='width:100%;max-height:100%;' src='"+uConfig.gd(dt, "imgurl")+"'>" %></div>

            <br />
            <br />

            <h1>跳转地址：
            </h1>

            <input id="linkurl" style="width: 100%; padding: 12px; border-radius: 5px; border: 1px solid #ddd; font-size: 15px; font-weight: bold; color: #333; background: #f5f5f5;" value="<%=uConfig.gd(dt, "linkurl") %>" />

            <div style="margin-top: 20px;">
                <a class="btn btn-success" onclick="art_submit()" style="padding: 12px 25px; font-size: 15px;">
                    <svg t="1690395092292" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="7899" width="16" height="16">
                        <path d="M840.533333 130.133333l-682.666666 215.466667c-34.133333 10.666667-40.533333 55.466667-12.8 74.666667l174.933333 123.733333c10.666667 6.4 23.466667 4.266667 29.866667-4.266667l25.6-34.133333c6.4-10.666667 4.266667-23.466667-4.266667-29.866667l-104.533333-74.666666 454.4-142.933334-290.133334 326.4-8.533333 264.533334c-2.133333 38.4 46.933333 59.733333 72.533333 32l121.6-123.733334 125.866667 87.466667c25.6 19.2 61.866667 2.133333 66.133333-29.866667l85.333334-637.866666c4.266667-29.866667-23.466667-55.466667-53.333334-46.933334z m-104.533333 603.733334l-115.2-81.066667c-10.666667-6.4-23.466667-4.266667-29.866667 4.266667l-14.933333 21.333333-64 66.133333 4.266667-123.733333 275.2-313.6-55.466667 426.666667z" fill="#297AFF" p-id="7900"></path></svg>
                    &nbsp;<%=Request.QueryString["id"] + "" == "" ? "提交" : "提交" %></a>
            </div>

        </div>
    </div>


    <script>
        var upload_images = function () {
            uploadImages(function (e) {
                if (e.success) {
                    $('#imgurl').html('<img style="width:100%;max-height:100%;" src="' + e.imgurl + '">')
                }

            })
        }


        var art_submit = function () {
            var post_data = {
                id:'<%=Request.QueryString["id"] + "" %>',
                name: document.getElementById("name").value,
                imgurl: $('#imgurl').find('img').attr('src'),
                linkurl: document.getElementById("linkurl").value
            }

            $.ajax({
                type: "POST",
                url: "../Api/admin.aspx?do=new_banner",
                data: post_data,
                datatype: "json",
                success: function (data) {
                    if (data.code == 1) {
                        layer.msg(data.msg, { icon: 1, time: 1000 });
                    } else {
                        layer.msg(data.msg, { icon: 2, time: 1500 });
                    }

                },
                error: function () {
                    layer.msg("抱歉，操作有误", { icon: 2, time: 1500 });
                }
            });

        }

        var replace_html=function(html){
            //html=html
            //    .replace(/<br>/g, "\r\n")
            //    .replace(/<p>/g, "\r\n")
            //    .replace(/<\/p>/g, "");
            return html;
        }
    </script>
</asp:Content>



<%@ Page Title="角色权限管理" Language="C#" MasterPageFile="~/serv/m.master" AutoEventWireup="true" CodeFile="role_auths.aspx.cs" Inherits="serv_Default" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="Server">
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="Server">
    <h2 style="font-size: 20px; color: #000; margin: 20px 18px;">角色权限设置</h2>

    <style>
        .list_qx_item {
            padding: 12px 20px;
            width: 200px;
            margin-bottom: 10px;
            cursor: pointer;
        }


            .list_qx_item.active {
                background: #f1f2f3;
            }

        .qx_details_name {
            font-size: 16px;
            color: #000;
            margin-bottom: 18px;
            margin-top: 30px;
        }

        .qx_select_list {
            display: flex;
            flex-wrap: wrap;
        }

            .qx_select_list .select_list_item {
                width: 25%;
                margin-bottom: 10px;
            }
    </style>

    <div style="background: #fff; margin: 10px; padding: 18px; border-radius: 8px;">

        <div style="display: flex; width: 100%;">

            <div style="border-right: 1px solid #f1f2f3; padding-right: 18px;">
                <div id="roles">
                </div>

                <div class="list_qx_item action_button" style="border: 1px dashed #ddd; display: flex; align-items: center;" onclick="new_role()">
                    <div>
                        <svg t="1695439551823" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="7029" width="13" height="13">
                            <path d="M1024 972.8c0 28.275-22.925 51.2-51.2 51.2H51.2C22.922 1024 0 1001.075 0 972.8V51.2C0 22.922 22.922 0 51.2 0h921.6c28.275 0 51.2 22.922 51.2 51.2v921.6z m-51.2-870.4c0-28.277-22.925-51.2-51.2-51.2H102.4c-28.275 0-51.2 22.923-51.2 51.2v819.2c0 28.275 22.925 51.2 51.2 51.2h819.2c28.275 0 51.2-22.925 51.2-51.2V102.4z" p-id="7030"></path><path d="M768 512c0 14.14-11.46 25.6-25.6 25.6H537.6v209.176c0 11.72-11.46 21.225-25.6 21.225s-25.6-9.505-25.6-21.225V537.6H281.6c-14.14 0-25.6-11.46-25.6-25.6s11.46-25.6 25.6-25.6h204.8V277.225C486.4 265.5 497.86 256 512 256s25.6 9.5 25.6 21.225V486.4h204.8c14.14 0 25.6 11.46 25.6 25.6z" p-id="7031"></path></svg>
                    </div>
                    <div style="margin-left: 5px;">添加更多</div>
                </div>

            </div>

            <div style="width: 100%; padding: 20px;">

                <div style="font-size: 20px; color: #000; margin-bottom: 8px;"><span class="role_name">-</span></div>
                <div style="color: gray;">
                    <span class="role_name">-</span>相关权限功能&nbsp;
                    <%if (Request.QueryString["edit"] + "" == "true")
                      {
                    %>

                    <a onclick="refresh_auths()" style="color: blue;">刷新权限</a>
                    <a onclick="add_auths()" style="color: blue;">新增分类</a>

                    <%
                      } %>
                </div>


                <div class="qx_details" style="margin-top: 30px;">
                    <%--<div class="qx_details_name">商品管理</div>

                    <div class="qx_select_list">

                        <div class="select_list_item">
                            <input type="checkbox" />&nbsp;全选
                        </div>


                        <div class="select_list_item">
                            <input type="checkbox" />&nbsp;添加商品分类
                        </div>

                        <div class="select_list_item">
                            <input type="checkbox" />&nbsp;编辑商品分类
                        </div>

                        <div class="select_list_item">
                            <input type="checkbox" />&nbsp;编辑商品分类
                        </div>

                        <div class="select_list_item">
                            <input type="checkbox" />&nbsp;编辑商品分类
                        </div>

                        <div class="select_list_item">
                            <input type="checkbox" />&nbsp;编辑商品分类
                        </div>

                    </div>--%>
                </div>


            </div>

        </div>


    </div>





    <script>
        var current_id = -1;

        var refresh_auths = function () {
            $.ajax({
                type: "POST",
                url: "../api/admin.aspx?do=refresh_auths",
                data: {},
                datatype: "json",
                success: function (json) {
                    if (json.code == 1) {
                        location.href = location.href;
                    }

                },
                error: function () {
                }
            });
        }



        var authLists = [];
        var get_authList = function () {

            $.ajax({
                type: "POST",
                url: "../api/admin.aspx?do=get_authList",
                data: {},
                datatype: "json",
                success: function (json) {
                    authLists = JSON.parse(json.data);

                    if (current_id != -1) {
                        get_current_auth_list();
                    }
                },
                error: function () {
                }
            });

        }

        get_authList();


        var get_roles = function () {

            $.ajax({
                type: "POST",
                url: "../api/admin.aspx?do=get_roles",
                data: {},
                datatype: "json",
                success: function (json) {
                    $('#roles').html('');
                    var lst = JSON.parse(json.data);
                    for (var i = 0; i < lst.length; i++) {
                        $('#roles').append('<div class="list_qx_item" roleid="' + lst[i].id + '" style="display: flex;">' + lst[i].name + '<div style="margin-left: auto;">        <svg onclick="role_edit(' + lst[i].id + ',\'' + lst[i].name + '\')" t="1695456640833" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2586" width="16" height="16">            <path d="M640 512 448 704l-102.4 19.2c0-12.8-6.4-25.6-19.2-38.4s-25.6-12.8-38.4-19.2l19.2-102.4 198.4-198.4c12.8-12.8 12.8-32 0-44.8s-32-12.8-44.8 0L256 531.2C256 537.6 249.6 544 249.6 550.4l-44.8 224c0 12.8 0 19.2 6.4 32 6.4 6.4 12.8 6.4 25.6 6.4 0 0 6.4 0 6.4 0L467.2 768c6.4 0 12.8-6.4 19.2-6.4l204.8-204.8c12.8-12.8 12.8-32 0-44.8S652.8 499.2 640 512z" fill="#272636" p-id="2587"></path><path d="M576 262.4c-12.8-12.8-32-12.8-44.8 0s-12.8 32 0 44.8l179.2 179.2c6.4 6.4 12.8 6.4 25.6 6.4s19.2 0 25.6-6.4c12.8-12.8 12.8-32 0-44.8L576 262.4z" fill="#272636" p-id="2588"></path><path d="M857.6 294.4l-134.4-134.4c-25.6-25.6-64-25.6-89.6 0L588.8 198.4C576 217.6 576 236.8 588.8 256l172.8 172.8c12.8 12.8 38.4 12.8 51.2 0L857.6 384C883.2 358.4 883.2 320 857.6 294.4z" fill="#272636" p-id="2589"></path><path d="M371.2 646.4c6.4 6.4 12.8 6.4 25.6 6.4s19.2 0 25.6-6.4l179.2-179.2c12.8-12.8 12.8-32 0-44.8s-32-12.8-44.8 0L371.2 601.6C358.4 614.4 358.4 633.6 371.2 646.4z" fill="#272636" p-id="2590"></path></svg>        <svg onclick="role_delete(' + lst[i].id + ',\'' + lst[i].name + '\')" t="1695456423552" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="4087" width="16" height="16">            <path d="M840 288H688v-56c0-40-32-72-72-72h-208C368 160 336 192 336 232V288h-152c-12.8 0-24 11.2-24 24s11.2 24 24 24h656c12.8 0 24-11.2 24-24s-11.2-24-24-24zM384 288v-56c0-12.8 11.2-24 24-24h208c12.8 0 24 11.2 24 24V288H384zM758.4 384c-12.8 0-24 11.2-24 24v363.2c0 24-19.2 44.8-44.8 44.8H332.8c-24 0-44.8-19.2-44.8-44.8V408c0-12.8-11.2-24-24-24s-24 11.2-24 24v363.2c0 51.2 41.6 92.8 92.8 92.8h358.4c51.2 0 92.8-41.6 92.8-92.8V408c-1.6-12.8-12.8-24-25.6-24z" fill="#272636" p-id="4088"></path><path d="M444.8 744v-336c0-12.8-11.2-24-24-24s-24 11.2-24 24v336c0 12.8 11.2 24 24 24s24-11.2 24-24zM627.2 744v-336c0-12.8-11.2-24-24-24s-24 11.2-24 24v336c0 12.8 11.2 24 24 24s24-11.2 24-24z" fill="#272636" p-id="4089"></path></svg>    </div>' + '</div>');
                    }

                    $('.list_qx_item:not(.action_button)').on('click', function () {
                        $(this).addClass("active").siblings().removeClass("active");
                        var roleid = $(this).attr("roleid");
                        current_id = roleid;
                        $('.role_name').html($(this).text());
                        get_current_auth_list();




                    })


                },
                error: function () {
                }
            });

        }

        get_roles();

        var get_current_auth_list = function () {
            $.ajax({
                type: "POST",
                url: "../api/admin.aspx?do=get_role_auths",
                data: { id: current_id },
                datatype: "json",
                success: function (json) {
                    if (json.code != 1) {
                        alert(json.msg);
                        $('.qx_details').html('');
                        return;
                    }

                    get_list(json.data);


                },
                error: function () {
                }
            });
        }

        var getPager = function () {
            get_roles();
            get_authList();
        }

        var new_role = function () {
            var cg = [];
            cg.push({ name: '角色名称', id: 'name', value: '' });
            edit({ aname: 'admin', title: '角色创建', action: 'serv_admin_role', id: '', data: cg, time: 0 });
        }

        var role_edit = function (id, name) {
            event.stopPropagation();
            console.log('编辑', id);


            var modify_model = new Array();
            modify_model.push({ name: '角色名称', id: 'name', value: name });

            edit({ aname: 'admin', title: "修改角色", action: 'serv_admin_role', id: id, data: modify_model, time: 0 });
        }

        var role_delete = function (id, name) {
            event.stopPropagation();
            console.log('删除', id);

            get_action({ name: 'event_create', data: { id: id, aname: 'admin', name: 'serv_admin_role', event: 'delete' }, n: '删除角色', e: '删除角色 ' + name + '<br>删除角色后，绑定此角色所有权限将失效', time: 0 });
        }

        var add_auths = function (fid) {
            var cg = [];
            fid = typeof (fid) == "undefined" ? "" : fid;
            cg.push({ name: 'fid', id: 'fid', value: fid, type: 'hidden' });
            cg.push({ name: '权限名称', id: 'name', value: '' });
            cg.push({ name: '权限路由', id: 'path', value: '', type: (fid == "" ? "hidden" : "") });
            edit({ aname: 'admin', title: (fid == "" ? "新增分类" : "新增权限"), action: 'serv_role_auths', id: '', data: cg, time: 0 });
        }



        var edit_auths = function (id) {
            var cg = [];
            var name = null;
            var path = null;
            var fid = null;
            for (var i = 0; i < authLists.length; i++) {
                var bobj = authLists[i];
                if (bobj.id == id) {
                    fid = bobj.fid;
                    name = bobj.name;
                    path = bobj.path;
                }
            }

            if (name == null || path == null) {
                alert("获取权限失败");
                return;
            }

            cg.push({ name: '原名称', id: 'origin_name', value: name, type: 'disabled' });
            if (fid != "") {
                cg.push({ name: '原权限', id: 'origin_path', value: path, type: 'disabled' });
            }
            cg.push({ name: '权限名称', id: 'name', value: name });
            if (fid != "") {
                cg.push({ name: '权限路由', id: 'path', value: path });
            }
            cg.push({ name: '删除权限', id: 'isdelete', value: '', type: 'check', data: [["确认删除", "确认删除"]] });
            edit({ aname: 'admin', title: (fid == "" ? "编辑分类" : "编辑权限"), action: "serv_role_auths", id: id, data: cg, time: 0 });
        }


        var get_list = function (data) {
            var lst = data.split(',');

            $('.qx_details').html('');

            for (var i = 0; i < authLists.length; i++) {
                var obj = authLists[i];
                var fid = obj.id;
                if (obj.fid != "") {
                    continue;
                }
                $('.qx_details').append('<div class="qx_details_name" rid="' + fid + '">' + '<span ' + (get_param('edit') == "true" ? ' onclick="edit_auths(' + obj.id + ')" ' : '') + '>' + obj.name + '</span>' + (get_param('edit') == "true" ? '<a onclick="add_auths(' + fid + ')" style="color: blue;font-size: 12px;margin-left: 8px;">新增权限</a>' : '') + '</div>');
                var qx_select_list = "";



                qx_select_list += '<div class="select_list_item"><input type="checkbox" value="" id="select_all_' + fid + '" onclick="select_all_auth(this)" />&nbsp;全选</div>';
                var isAllSelect = true;
                var cItems_number = 0;
                for (var b = 0; b < authLists.length; b++) {
                    var bobj = authLists[b];
                    if (bobj.fid == fid) {
                        cItems_number++;
                        var authId = bobj.id;
                        var checkstr = "  checked='checked' ";
                        if (lst.indexOf(authId) == -1) {
                            isAllSelect = false;
                            checkstr = "";
                        }
                        qx_select_list += '<div class="select_list_item" title="' + bobj.path + '"  ><input type="checkbox" value="' + authId + '" class="authItem" ' + checkstr + '/>&nbsp;' + '<span ' + (get_param('edit') == "true" ? ' onclick="edit_auths(' + bobj.id + ')" ' : '') + '>' + bobj.name + '</span>' + '</div>';
                    }
                }
                if (cItems_number == 0) {
                    isAllSelect = false;
                }

                $('.qx_details').append('<div class="qx_select_list" fid="' + fid + '"> ' + qx_select_list + ' </div>');

                $('#select_all_' + fid).prop("checked", isAllSelect);
            }



            $('.authItem').on('click', function () {
                save_all_auth();

            })

        }
        var save_all_auth = function () {
            var lst = [];
            $('.authItem').each(function () {
                if ($(this).prop('checked')) {
                    lst.push($(this).val());
                }
            });

            console.log('lst', lst);

            $.ajax({
                type: "POST",
                url: "../api/admin.aspx?do=update_role_auths",
                data: { id: current_id, quanxian: lst.join(',') },
                datatype: "json",
                success: function (json) {
                    console.log("save status:", json);
                },
                error: function () {
                }
            });
        }

        var select_all_auth = function (obj) {
            $(obj).closest(".qx_select_list").find('.authItem').prop("checked", $(obj).prop('checked'));
            save_all_auth();
        }


    </script>

</asp:Content>


<%@ Page Title="一键任务" Language="C#" MasterPageFile="~/serv/m.master" AutoEventWireup="true" CodeFile="task_onetouch.aspx.cs" Inherits="w_index" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="Server">
    <style>
        .buy1 .button-style {
            border: 0px;
            background: none;
        }

        .design-button-full {
            background: #3255FF;
            color: #fff!important;
            padding: 3px 10px;
            border-radius: 3px;
            font-size: 12px;
            font-weight: bold;
        }

        .design-button-hollow {
            border: 1px solid #3255FF;
            color: #3255FF!important;
            padding: 3px 10px;
            border-radius: 3px;
            font-size: 12px;
            font-weight: bold;
        }

        .design-button-common {
            background: #E0E6FF;
            color: #556cdf!important;
            padding: 3px 10px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
        }
    </style>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="Server">
    <div class="ibox float-e-margins">
        <div class="ibox-title">
            <h5 style="color: #2a2b2c; text-shadow: 5px 5px 5px yellow; font-weight: bold; display: flex; align-items: center;">
                <svg t="" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="15149" width="38" height="38" style="margin-right: 6px;">
                    <path d="M511.0272 12.4416a501.76 501.76 0 1 0 501.76 501.76 501.76 501.76 0 0 0-501.76-501.76z m100.3008 805.8368l-17.3568-92.16-43.3664-230.144h-0.4608l-29.9008-156.928H501.76l-147.3536 387.072h162.4064l17.3568 92.16h-290.304L464.9984 209.92h92.16L778.24 818.2784z" fill="#E61F19" p-id="15150" data-spm-anchor-id="a313x.search_index.0.i5.2d123a81WeaD2m" class="selected"></path></svg>
                <%= (Request.QueryString["user"] + "" == "relationship" ? "一键任务-团队查询" : "一键任务") %>
            </h5>
        </div>
        <div class="ibox-content">


            <div class="row layui-form">
                <div class="col-md-12">
                    <div class="form-group form-buttom" style="position: relative;">
                        <span style="position: absolute; top: 0; height: 100%; background: #f7e61a; display: flex; align-items: center; padding: 0 18px; font-weight: bold; color: #2d2a2a; border-right: 2px solid #d9cc31;">上级ID</span>
                        <input type="text" id="team_uid" class="form-control" placeholder="要搜索的用户ID/手机号码" style="padding-left: 85px;" value="<%=Request.QueryString["uid"] %>">
                    </div>
                </div>
            </div>


            <div class="row layui-form">

                <div class="col-md-2">
                    <select id="days">
                        <option value="">选择周期</option>
                        <asp:Repeater ID="task_onetouch_items" runat="server">
                            <ItemTemplate>
                                <option value="<%#Eval("days") %>">任务<%#Eval("days") %>天</option>
                            </ItemTemplate>
                        </asp:Repeater>
                    </select>
                </div>

                <div class="col-md-2">
                    <div class="form-group form-buttom" style="position: relative;">
                        <input type="text" id="roomNumber" class="form-control" placeholder="房间号">
                    </div>
                </div>

                <div class="col-md-2">
                    <div class="form-group form-buttom">
                        <input type="text" id="search-keyword" name="ordername" class="form-control" placeholder="请输入搜索的信息">
                    </div>
                </div>

                <div class="col-md-2">
                    <div class="form-group form-buttom" id="twoInputs">
                        <input type="text" id="start_time" name="ordername" class="form-control" placeholder="开始时间" style="width: 48%; display: inline-block;" value="" autocomplete="off">
                        <input type="text" id="end_time" name="ordername" class="form-control" placeholder="结束时间" style="width: 48%; display: inline-block;" autocomplete="off" value="">

                        <script>
                            $(document).ready(function () {
                                $('#start_time').datepicker({
                                    format: 'yyyy-mm-dd',
                                    autoclose: true
                                });

                                $('#end_time').datepicker({
                                    format: 'yyyy-mm-dd',
                                    autoclose: true
                                });
                            });
                        </script>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="form-group form-buttom">

                        <a class="btn btn-success" onclick="searchList()">
                            <svg t="1688237921441" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="1949" width="16" height="16">
                                <path d="M361.130667 658.688a256 256 0 1 1 282.794666-44.672 42.496 42.496 0 0 0-10.368 14.933333c-0.853333 0.682667-1.621333 1.450667-2.389333 2.218667a42.666667 42.666667 0 0 0 0 60.330667l170.666667 170.666666a42.666667 42.666667 0 0 0 60.330666-60.330666l-142.762666-142.762667a341.333333 341.333333 0 1 0-169.344 99.242667 42.666667 42.666667 0 0 0-20.224-82.901334 254.762667 254.762667 0 0 1-168.704-16.725333z" fill="#14101C" p-id="1950"></path></svg>
                            查询</a>
                        <a class="btn btn-success" onclick="javascript:location.href=location.href;">
                            <svg t="1688237949108" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2211" width="16" height="16">
                                <path d="M512 192a298.24 298.24 0 0 0-245.162667 128 42.666667 42.666667 0 1 1-69.973333-48.810667 384.042667 384.042667 0 0 1 697.173333 180.48l36.608-34.645333a42.666667 42.666667 0 0 1 58.709334 61.952l-112.64 106.666667a42.666667 42.666667 0 0 1-71.253334-38.912A298.666667 298.666667 0 0 0 512 192z m0.170667 640a298.24 298.24 0 0 0 245.162666-128 42.666667 42.666667 0 1 1 69.973334 48.810667 384.042667 384.042667 0 0 1-697.173334-180.48l-36.650666 34.645333a42.666667 42.666667 0 0 1-58.666667-61.952l112.64-106.666667a42.666667 42.666667 0 0 1 71.253333 38.912 298.666667 298.666667 0 0 0 293.418667 354.730667z" fill="#14101C" p-id="2212"></path></svg>
                            刷新</a>


                        <a class="btn btn-success" onclick="report_exl()">
                            <svg t="1695489540076" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="5479" width="16" height="16">
                                <path d="M686.08 989.866667H165.546667c-51.2 0-92.16-40.96-92.16-92.16V126.293333c0-51.2 40.96-92.16 92.16-92.16h573.44c6.826667 0 11.946667 1.706667 17.066666 6.826667l187.733334 174.08c5.12 5.12 8.533333 11.946667 8.533333 18.773333v256c0 13.653333-11.946667 25.6-25.6 25.6s-25.6-11.946667-25.6-25.6V244.053333L728.746667 85.333333h-563.2C141.653333 85.333333 122.88 104.106667 122.88 126.293333v769.706667c0 22.186667 18.773333 40.96 40.96 40.96h520.533333c13.653333 0 25.6 11.946667 25.6 25.6S699.733333 989.866667 686.08 989.866667z" fill="#333333" p-id="5480"></path><path d="M919.893333 259.413333h-121.173333c-51.2 0-92.16-40.96-92.16-92.16V59.733333c0-13.653333 11.946667-25.6 25.6-25.6s25.6 11.946667 25.6 25.6v105.813334c0 22.186667 18.773333 40.96 40.96 40.96h121.173333c13.653333 0 25.6 11.946667 25.6 25.6s-11.946667 27.306667-25.6 27.306666zM500.053333 305.493333H208.213333c-13.653333 0-25.6-11.946667-25.6-25.6s11.946667-25.6 25.6-25.6h291.84c13.653333 0 25.6 11.946667 25.6 25.6s-11.946667 25.6-25.6 25.6zM616.106667 575.146667H208.213333c-13.653333 0-25.6-11.946667-25.6-25.6s11.946667-25.6 25.6-25.6h407.893334c13.653333 0 25.6 11.946667 25.6 25.6s-11.946667 25.6-25.6 25.6zM380.586667 440.32H208.213333c-13.653333 0-25.6-11.946667-25.6-25.6s11.946667-25.6 25.6-25.6h172.373334c13.653333 0 25.6 11.946667 25.6 25.6s-11.946667 25.6-25.6 25.6z" fill="#333333" p-id="5481"></path><path d="M914.773333 779.946667H600.746667c-13.653333 0-25.6-11.946667-25.6-25.6s11.946667-25.6 25.6-25.6h314.026666c13.653333 0 25.6 11.946667 25.6 25.6s-11.946667 25.6-25.6 25.6z" fill="#333333" p-id="5482"></path><path d="M831.146667 873.813333c-6.826667 0-13.653333-1.706667-18.773334-6.826666-10.24-10.24-10.24-25.6 0-35.84l75.093334-75.093334-75.093334-75.093333c-10.24-10.24-10.24-25.6 0-35.84 10.24-10.24 25.6-10.24 35.84 0l93.866667 93.866667c5.12 5.12 6.826667 11.946667 6.826667 18.773333s-3.413333 13.653333-6.826667 18.773333L848.213333 870.4c-5.12 1.706667-11.946667 3.413333-17.066666 3.413333z" fill="#333333" p-id="5483"></path><path d="M694.613333 824.32h-529.066666c-37.546667 0-68.266667-30.72-68.266667-68.266667V121.173333c0-37.546667 30.72-68.266667 68.266667-68.266666h529.066666c37.546667 0 68.266667 30.72 68.266667 68.266666v634.88c0 37.546667-30.72 68.266667-68.266667 68.266667z" fill="#333333" opacity=".3" p-id="5484"></path></svg>导出</a>

                    </div>
                </div>
            </div>

            <div style="display: flex; flex-wrap: wrap; margin-bottom: 20px;">
                <div class="timesel_lab">全部</div>
                <div class="timesel_lab">今天</div>
                <div class="timesel_lab">昨天</div>
                <div class="timesel_lab">本周</div>
                <div class="timesel_lab">上周</div>
                <div class="timesel_lab">本月</div>
                <div class="timesel_lab">上月</div>
            </div>



            <div style="margin-bottom: 20px;">

                <input type="checkbox" id="filter_th" <%=Request.QueryString["userid"] + "" == "" ? "checked='checked'" : "" %> />&nbsp;过滤托号
                <input type="checkbox" id="filter_exp" />&nbsp;体验金项目


                <input type="checkbox" onclick="set_autoAlarm(this)" />&nbsp;自动监控订单（5s刷新一次）
                
                <div style="display: inline-block; font-weight: bold; background: #e5ded887; padding: 2px 18px; border-radius: 5px; color: #000; font-size: 20px;">
                    <span class="total_amount">0</span> 总金额<span style="font-size: 18px; background: linear-gradient(152deg, #ff5412d4, #f3cdbe); color: #fff8e0; border-radius: 6px; font-weight: 100; padding: 2px 9px; margin-left: 5px; font-weight: bold;"><span class="total_number">0</span> 单</span>
                </div>
            </div>


            <div class="table-responsive">
                <table class="table">
                    <thead>
                        <tr role="head">
                            <%--<th sort="true">订单号</th>--%>
                            <th sort="true">操作员</th>
                            <th sort="true">用户</th>
                            <th sort="true">订单金额</th>
                            <th sort="true">累计佣金</th>
                            <th sort="true">成功率</th>
                            <%--<th sort="true">收款方式</th>--%>
                            <th sort="true">天数</th>
                            <th sort="true">完成天数</th>
                            <th sort="true">佣金说明</th>
                            <th sort="true">状态</th>
                            <th sort="true">日期</th>
                        </tr>
                    </thead>
                    <tbody id="list-display">
                    </tbody>
                </table>
            </div>



            <div class="layui-table-page">
                <div id="layui-table-page1">
                    <div class="layui-box layui-laypage layui-laypage-default" id="layui-laypage-1">
                        <div style="display: inline-block;" class="cpages">
                            <!--page grow -->
                        </div>
                        <span class="layui-laypage-skip">到第<input type="text" min="1" value="1" class="layui-input target-page" />页
                            <button type="button" class="layui-laypage-btn" onclick="javascript:pager_index=$('.target-page').val()-1;getPager();">确定</button></span>
                        <span class="layui-laypage-count">共 <span class="page_count"></span>条</span>
                        <span class="layui-laypage-limits">
                            <select class="s_pages" lay-ignore="" onchange="javascript:pager_size=$(this).val();getPager();">
                                <option value="10">10 条/页</option>
                                <option value="20" selected="">20 条/页</option>
                                <option value="30">30 条/页</option>
                                <option value="50">50 条/页</option>
                                <option value="100">100 条/页</option>
                                <option value="200">200 条/页</option>
                                <option value="500">500 条/页</option>
                                <option value="1000">1000 条/页</option>
                                <option value="2000">2000 条/页</option>
                            </select></span>
                    </div>
                </div>
            </div>

        </div>
    </div>


    <script>
        var _action = 'task_onetouch';
        var _actionName = '一键任务';
    </script>
    <script>
        function openuser() {
            var cg = [];

            cg.push({ name: '黑名单', id: 'black_list', value: '', type: 'textarea' });

            edit({ aname: 'admin', title: '黑名单添加', action: _action, id: '', data: cg });
        }


        var startnow = [];
        startnow.push(['次日开始']);
        startnow.push(['立即生效']);
    </script>
    <script>

        var current_json = [];
        var pager_size = $(".s_pages").val();
        var pager_index = 0;
        var pager_key = "";

        var opt = get_param("opt");
        var opt2 = get_param("fid");

        $("#pagesize").val(pager_key);
        function setPageSize(obj) {
            pager_index = 0;
            pager_key = $("#search-keyword").val();
            opt = $(obj).val();
            getPager();
        }

        function searchList() {
            opt = $("#opt1").val();
            pager_index = 0;
            pager_key = $("#search-keyword").val();
            getPager();
        }

        function getParam() {
            return { limit: pager_size, p: pager_index, page: _action, keyword: pager_key, opt: opt, opt2: opt2, serv_id: get_param("serv_id"), start_time: $("#start_time").val(), end_time: $("#end_time").val(), state: '<%=Request.QueryString["state"] %>', uid: '<%=Request.QueryString["userid"] %>', type: $("#typeid").val(), filter_th: $('#filter_th').prop('checked'), filter_exp: $('#filter_exp').prop('checked'), days: $('#days').val(), roomNumber: $('#roomNumber').val(), team_uid: $('#team_uid').val() };
        }

        function getPager() {

            $.ajax({
                type: "POST",
                url: "../api/admin.aspx?do=page",
                data: getParam(),
                datatype: "json",
                success: function (json) {
                    $("#list-display").html("");
                    if (json.code == 1) {
                        var data = json.data.list;
                        current_json = data;
                        for (var i = 0; i < data.length; i++) {
                            var tr = "";
                            tr += "<tr style='background:#efefef;'>";
                            tr += "<td colspan='10' style=' padding:8px  8px;'>"

                                + '<div style="display: flex;">   '
                            + '<a style="margin-right:10px;">' + '<b>' + data[i].orderNo + '</b>' + '<span style="color: #b53d10;margin-left: 2px;">[' + data[i].id + ']</span>' + '</a>'
                            + '     <a style="display: flex; color: #1679ff; font-weight: bold;font-size:12px; " href="task_onetouch_records.aspx?fid=' + data[i].id + '">            <svg t="" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="17697" width="16" height="16">                <path d="M512 0c282.760533 0 512 229.239467 512 512 0 282.760533-229.239467 512-512 512-282.760533 0-512-229.239467-512-512C0 229.239467 229.239467 0 512 0z m-34.4064 240.0256H233.0624v41.1648h37.4784v376.6272l-38.7072 6.144 11.0592 41.7792a2539.451733 2539.451733 0 0 0 152.3712-33.792v109.3632h42.3936v-121.0368c13.5168-3.6864 27.648-7.9872 41.1648-12.288v-43.008c-13.5168 4.3008-27.0336 8.6016-41.1648 13.5168V281.1904h39.936V240.0256z m223.0272-23.9616a815.786667 815.786667 0 0 1-60.2112 132.7104H487.424v41.7792h120.4224v28.2624a1005.124267 1005.124267 0 0 1-4.9152 77.4144H478.208v42.3936h118.5792c-19.0464 95.8464-63.2832 164.6592-132.096 206.4384l26.4192 35.0208c70.0416-45.4656 117.3504-115.5072 141.312-210.1248 17.8176 68.1984 62.0544 137.6256 132.7104 207.6672l30.72-32.5632c-76.1856-70.0416-121.6512-138.8544-136.3968-206.4384h127.1808v-42.3936h-141.312c3.072-27.648 4.9152-56.5248 4.9152-87.2448v-18.432h125.952v-41.7792h-91.5456c19.6608-31.9488 38.0928-70.656 56.5248-117.3504l-40.5504-15.36z m-305.3568 323.7888v90.9312c-27.0336 6.7584-54.0672 12.9024-82.3296 19.0464v-109.9776h82.3296z m0-128.4096v87.8592H312.9344v-87.8592h82.3296z m0-130.2528v90.3168H312.9344V281.1904h82.3296z m152.9856-61.44l-33.792 21.504c24.576 35.6352 43.6224 67.584 57.7536 97.0752l33.1776-22.7328c-13.5168-27.648-32.5632-59.5968-57.1392-95.8464z" fill="#1679FF" p-id="17698"></path></svg>&nbsp;关联订单</a>  '

                                + '<a style="display: flex;color: #1f1f1f;font-weight: bold;font-size:12px;margin-left: 10px; " href="task_onetouch_preset.aspx?fid=' + data[i].id + '">            <svg t="1713453746674" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="35513" width="16" height="16"><path d="M896 0H128C57.30816 0 0 57.30816 0 128v768c0 70.69184 57.30816 128 128 128h768c70.69184 0 128-57.30816 128-128V128c0-70.69184-57.30816-128-128-128zM458.34752 202.38848h403.41504v49.7152h-182.53312l-20.59264 97.30048h170.45504v349.43488h-51.84512V398.4128h-241.47968v300.43136H483.91168V349.40928h120.02816l17.76128-97.30048H458.34752v-49.72032zM326.94784 480.80384v299.01312c0 53.97504-26.51648 80.96256-79.54944 80.96256H172.11904c-2.36544-17.04448-5.67808-36.21888-9.94304-57.52832 23.66976 2.8416 47.58528 4.25984 71.73632 4.25984 26.51136 0 39.77216-15.15008 39.77216-45.45536V480.80384H159.3344v-49.7152h139.91424c-30.77632-36.93056-61.55776-70.07744-92.32896-99.43552l35.51232-31.25248a1544.3968 1544.3968 0 0 1 49.00352 45.45536l75.28448-91.6224H166.43584v-49.7152h265.6256v49.7152l-105.1136 127.13472 23.43936 24.8576-28.41088 24.8576h129.26464v47.58528c-7.5776 36.46464-19.41504 78.60224-35.51232 126.42304a1482.12224 1482.12224 0 0 0-49.00864-16.3328c11.83744-29.82912 22.25152-65.81248 31.25248-107.9552H326.94784z m497.16736 387.07712c-57.76384-49.24416-119.79264-97.536-186.08128-144.88576a800.33792 800.33792 0 0 1-14.20288 20.59264c-38.35392 48.768-98.48832 92.09344-180.39808 129.97632-15.1552-19.41504-28.41088-35.04128-39.77728-46.8736 84.75136-35.04128 144.1792-75.04896 178.26816-120.02816 31.25248-40.71936 46.8736-99.67104 46.8736-176.84992V427.53536h52.56192v105.82528c0 55.3984-7.10656 102.98368-21.30944 142.76096 74.33728 49.7152 142.04928 98.24256 203.13088 145.59744l-39.0656 46.16192z" fill="#231815" p-id="35514"></path></svg>&nbsp;预设单</a> '

                                + '<a style="display: flex;color: #bf9d5b;font-weight: bold;font-size:12px;margin-left: 10px; " href="transport_orders.aspx?order_type=onetouch&fid=' + data[i].id + '">            <svg t="1713453656171" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="34234" width="16" height="16"><path d="M63.3856 0m85.333333 0l682.666667 0q85.333333 0 85.333333 85.333333l0 853.333334q0 85.333333-85.333333 85.333333l-682.666667 0q-85.333333 0-85.333333-85.333333l0-853.333334q0-85.333333 85.333333-85.333333Z" fill="#FAAD14" p-id="34235"></path><path d="M488.004267 810.325333c28.672-9.557333 38.229333-28.672 38.229333-67.584V556.373333H797.252267v-63.488H526.2336V430.08C604.0576 389.12 688.0256 327.68 746.052267 271.701333l-47.786667-36.864-14.336 3.413334H252.484267v62.122666h361.813333a843.946667 843.946667 0 0 1-154.282667 96.256v96.256H184.900267v63.488h275.114666v185.002667c0 12.288-4.096 15.701333-18.432 16.384-15.018667 0.682667-66.218667 0.682667-118.101333-1.365333 10.922667 17.749333 23.210667 47.104 27.306667 65.536 63.488 0 108.544-1.365333 137.216-11.605334z" fill="#FFFFFF" p-id="34236"></path></svg>&nbsp;子订单</a> '

                                + (data[i].state == 100 ? '<a style="display: flex;color: #d81e06;font-weight: bold;font-size:12px;margin-left: 10px; " ' + "onclick=\"" + "get_action({name:'event_create',data:{id:" + data[i].id + ",aname:'admin',name:'" + _action + "',event:'stop_onetouch_order'},n:'结束订单',e:'结束订单 " + data[i].orderNo + "<br>【任务佣金】：" + data[i].award_amount + "（将发放到账户）'});" + "\"" + '  >           <svg t="1713514994621" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="9545" width="16" height="16"><path d="M718.9248 239.328c24.1984 17.2096 45.7152 36.704 64.5376 58.4896s34.9568 45.312 48.4032 70.592c13.4464 25.28 23.6608 52.032 30.6496 80.2688 6.9952 28.2368 10.4896 56.8704 10.4896 85.9136 0 50.016-9.5424 96.9408-28.64 140.7744-19.0848 43.8336-44.9088 82.016-77.4464 114.5536s-70.7264 58.3552-114.5536 77.4464C608.5376 886.4576 561.6192 896 511.5968 896c-49.472 0-96.1344-9.5424-139.9616-28.64-43.8336-19.0848-82.1504-44.9088-114.9568-77.4464s-58.6176-70.7264-77.4464-114.5536-28.2368-90.7584-28.2368-140.7744c0-28.4992 3.36-56.4736 10.0864-83.8976 6.72-27.4304 16.2624-53.5104 28.64-78.2464 12.3712-24.7424 27.6928-47.872 45.984-69.376 18.2848-21.5168 38.72-40.8768 61.3056-58.0928 11.84-8.6016 24.608-11.8272 38.3232-9.6768 13.7152 2.1504 24.8704 8.8768 33.472 20.1664 8.608 11.296 11.84 23.936 9.6896 37.92-2.1568 13.984-8.8768 25.28-20.1728 33.8816C324.4352 352 298.4896 382.3872 280.4736 418.4192c-18.016 36.032-27.0336 74.7584-27.0336 116.1664 0 35.5008 6.7264 68.9728 20.1728 100.4352 13.4464 31.4624 31.8656 58.8928 55.2576 82.2848 23.3984 23.392 50.8224 41.952 82.2848 55.6608 31.4624 13.7216 64.9344 20.576 100.4288 20.576 35.5008 0 68.9792-6.8544 100.4352-20.576 31.4624-13.7152 58.8928-32.2688 82.2848-55.6608 23.3984-23.392 41.952-50.8224 55.6608-82.2848 13.7216-31.4624 20.576-64.9344 20.576-100.4352 0-41.952-9.6832-81.6128-29.0432-118.9888s-46.5216-68.1664-81.472-92.3712c-11.84-8.064-18.9632-19.0912-21.3824-33.0688-2.4192-13.9904 0.4032-26.8928 8.4672-38.7264 8.0704-11.296 19.0912-18.1504 33.0752-20.5696C694.1824 228.4352 707.0912 231.264 718.9248 239.328L718.9248 239.328zM511.5968 537.0048c-13.984 0-25.9456-4.9728-35.8912-14.9184-9.952-9.952-14.9248-21.92-14.9248-35.904L460.7808 179.6288c0-13.984 4.9728-26.08 14.9248-36.3008S497.6128 128 511.5968 128c14.528 0 26.7648 5.1072 36.704 15.328 9.9584 10.2208 14.9312 22.3168 14.9312 36.3008l0 306.5536c0 13.984-4.9728 25.952-14.9312 35.904C538.3552 532.032 526.1184 537.0048 511.5968 537.0048L511.5968 537.0048zM511.5968 537.0048" fill="#d81e06" p-id="9546"></path></svg>&nbsp;结束订单</a> ' : '')






                                 + (data[i].expire_time == "" ? "" : "<div style='color:black;margin-left:8px;'>结束 " + data[i].expire_time + "</div>")
                              + (data[i].start_time == "" ? "" : "<div style='color:green;margin-left:8px;'>开始 " + data[i].start_time + "</div>")





                                + '  </div>'

                                + "</td>";
                            tr += "</tr>";

                            tr += "<tr style='" + (data[i].orderLock == 1 ? "background:#f5f5f5;" : "") + "'>";
                            //tr += ("<td>" + data[i].orderNo + '<span style="color: #b53d10;margin-left: 2px;">[' + data[i].id + ']</span>'

                            //    + '<div style="margin-top: 8px;display: flex;">        <a style="display: flex; color: #1679ff; font-weight: bold;font-size:12px; " href="task_onetouch_records.aspx?fid=' + data[i].id + '">            <svg t="" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="17697" width="16" height="16">                <path d="M512 0c282.760533 0 512 229.239467 512 512 0 282.760533-229.239467 512-512 512-282.760533 0-512-229.239467-512-512C0 229.239467 229.239467 0 512 0z m-34.4064 240.0256H233.0624v41.1648h37.4784v376.6272l-38.7072 6.144 11.0592 41.7792a2539.451733 2539.451733 0 0 0 152.3712-33.792v109.3632h42.3936v-121.0368c13.5168-3.6864 27.648-7.9872 41.1648-12.288v-43.008c-13.5168 4.3008-27.0336 8.6016-41.1648 13.5168V281.1904h39.936V240.0256z m223.0272-23.9616a815.786667 815.786667 0 0 1-60.2112 132.7104H487.424v41.7792h120.4224v28.2624a1005.124267 1005.124267 0 0 1-4.9152 77.4144H478.208v42.3936h118.5792c-19.0464 95.8464-63.2832 164.6592-132.096 206.4384l26.4192 35.0208c70.0416-45.4656 117.3504-115.5072 141.312-210.1248 17.8176 68.1984 62.0544 137.6256 132.7104 207.6672l30.72-32.5632c-76.1856-70.0416-121.6512-138.8544-136.3968-206.4384h127.1808v-42.3936h-141.312c3.072-27.648 4.9152-56.5248 4.9152-87.2448v-18.432h125.952v-41.7792h-91.5456c19.6608-31.9488 38.0928-70.656 56.5248-117.3504l-40.5504-15.36z m-305.3568 323.7888v90.9312c-27.0336 6.7584-54.0672 12.9024-82.3296 19.0464v-109.9776h82.3296z m0-128.4096v87.8592H312.9344v-87.8592h82.3296z m0-130.2528v90.3168H312.9344V281.1904h82.3296z m152.9856-61.44l-33.792 21.504c24.576 35.6352 43.6224 67.584 57.7536 97.0752l33.1776-22.7328c-13.5168-27.648-32.5632-59.5968-57.1392-95.8464z" fill="#1679FF" p-id="17698"></path></svg>&nbsp;关联订单</a>  '

                            //    + '<a style="display: flex;color: #1f1f1f;font-weight: bold;font-size:12px;margin-left: 10px; " href="task_onetouch_preset.aspx?fid=' + data[i].id + '">            <svg t="1713453746674" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="35513" width="16" height="16"><path d="M896 0H128C57.30816 0 0 57.30816 0 128v768c0 70.69184 57.30816 128 128 128h768c70.69184 0 128-57.30816 128-128V128c0-70.69184-57.30816-128-128-128zM458.34752 202.38848h403.41504v49.7152h-182.53312l-20.59264 97.30048h170.45504v349.43488h-51.84512V398.4128h-241.47968v300.43136H483.91168V349.40928h120.02816l17.76128-97.30048H458.34752v-49.72032zM326.94784 480.80384v299.01312c0 53.97504-26.51648 80.96256-79.54944 80.96256H172.11904c-2.36544-17.04448-5.67808-36.21888-9.94304-57.52832 23.66976 2.8416 47.58528 4.25984 71.73632 4.25984 26.51136 0 39.77216-15.15008 39.77216-45.45536V480.80384H159.3344v-49.7152h139.91424c-30.77632-36.93056-61.55776-70.07744-92.32896-99.43552l35.51232-31.25248a1544.3968 1544.3968 0 0 1 49.00352 45.45536l75.28448-91.6224H166.43584v-49.7152h265.6256v49.7152l-105.1136 127.13472 23.43936 24.8576-28.41088 24.8576h129.26464v47.58528c-7.5776 36.46464-19.41504 78.60224-35.51232 126.42304a1482.12224 1482.12224 0 0 0-49.00864-16.3328c11.83744-29.82912 22.25152-65.81248 31.25248-107.9552H326.94784z m497.16736 387.07712c-57.76384-49.24416-119.79264-97.536-186.08128-144.88576a800.33792 800.33792 0 0 1-14.20288 20.59264c-38.35392 48.768-98.48832 92.09344-180.39808 129.97632-15.1552-19.41504-28.41088-35.04128-39.77728-46.8736 84.75136-35.04128 144.1792-75.04896 178.26816-120.02816 31.25248-40.71936 46.8736-99.67104 46.8736-176.84992V427.53536h52.56192v105.82528c0 55.3984-7.10656 102.98368-21.30944 142.76096 74.33728 49.7152 142.04928 98.24256 203.13088 145.59744l-39.0656 46.16192z" fill="#231815" p-id="35514"></path></svg>&nbsp;预设单</a> '

                            //    + '<a style="display: flex;color: #bf9d5b;font-weight: bold;font-size:12px;margin-left: 10px; " href="transport_orders.aspx?order_type=onetouch&fid=' + data[i].id + '">            <svg t="1713453656171" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="34234" width="16" height="16"><path d="M63.3856 0m85.333333 0l682.666667 0q85.333333 0 85.333333 85.333333l0 853.333334q0 85.333333-85.333333 85.333333l-682.666667 0q-85.333333 0-85.333333-85.333333l0-853.333334q0-85.333333 85.333333-85.333333Z" fill="#FAAD14" p-id="34235"></path><path d="M488.004267 810.325333c28.672-9.557333 38.229333-28.672 38.229333-67.584V556.373333H797.252267v-63.488H526.2336V430.08C604.0576 389.12 688.0256 327.68 746.052267 271.701333l-47.786667-36.864-14.336 3.413334H252.484267v62.122666h361.813333a843.946667 843.946667 0 0 1-154.282667 96.256v96.256H184.900267v63.488h275.114666v185.002667c0 12.288-4.096 15.701333-18.432 16.384-15.018667 0.682667-66.218667 0.682667-118.101333-1.365333 10.922667 17.749333 23.210667 47.104 27.306667 65.536 63.488 0 108.544-1.365333 137.216-11.605334z" fill="#FFFFFF" p-id="34236"></path></svg>&nbsp;子订单</a> '

                            //    + '<a style="display: flex;color: #d81e06;font-weight: bold;font-size:12px;margin-left: 10px; " ' + "onclick=\"" + "get_action({name:'event_create',data:{id:" + data[i].id + ",aname:'admin',name:'" + _action + "',event:'stop_onetouch_order'},n:'结束订单',e:'结束订单 " + data[i].orderNo + "<br>【任务佣金】：" + data[i].award_amount + "（将发放到账户）'});" + "\"" + '  >           <svg t="1713514994621" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="9545" width="16" height="16"><path d="M718.9248 239.328c24.1984 17.2096 45.7152 36.704 64.5376 58.4896s34.9568 45.312 48.4032 70.592c13.4464 25.28 23.6608 52.032 30.6496 80.2688 6.9952 28.2368 10.4896 56.8704 10.4896 85.9136 0 50.016-9.5424 96.9408-28.64 140.7744-19.0848 43.8336-44.9088 82.016-77.4464 114.5536s-70.7264 58.3552-114.5536 77.4464C608.5376 886.4576 561.6192 896 511.5968 896c-49.472 0-96.1344-9.5424-139.9616-28.64-43.8336-19.0848-82.1504-44.9088-114.9568-77.4464s-58.6176-70.7264-77.4464-114.5536-28.2368-90.7584-28.2368-140.7744c0-28.4992 3.36-56.4736 10.0864-83.8976 6.72-27.4304 16.2624-53.5104 28.64-78.2464 12.3712-24.7424 27.6928-47.872 45.984-69.376 18.2848-21.5168 38.72-40.8768 61.3056-58.0928 11.84-8.6016 24.608-11.8272 38.3232-9.6768 13.7152 2.1504 24.8704 8.8768 33.472 20.1664 8.608 11.296 11.84 23.936 9.6896 37.92-2.1568 13.984-8.8768 25.28-20.1728 33.8816C324.4352 352 298.4896 382.3872 280.4736 418.4192c-18.016 36.032-27.0336 74.7584-27.0336 116.1664 0 35.5008 6.7264 68.9728 20.1728 100.4352 13.4464 31.4624 31.8656 58.8928 55.2576 82.2848 23.3984 23.392 50.8224 41.952 82.2848 55.6608 31.4624 13.7216 64.9344 20.576 100.4288 20.576 35.5008 0 68.9792-6.8544 100.4352-20.576 31.4624-13.7152 58.8928-32.2688 82.2848-55.6608 23.3984-23.392 41.952-50.8224 55.6608-82.2848 13.7216-31.4624 20.576-64.9344 20.576-100.4352 0-41.952-9.6832-81.6128-29.0432-118.9888s-46.5216-68.1664-81.472-92.3712c-11.84-8.064-18.9632-19.0912-21.3824-33.0688-2.4192-13.9904 0.4032-26.8928 8.4672-38.7264 8.0704-11.296 19.0912-18.1504 33.0752-20.5696C694.1824 228.4352 707.0912 231.264 718.9248 239.328L718.9248 239.328zM511.5968 537.0048c-13.984 0-25.9456-4.9728-35.8912-14.9184-9.952-9.952-14.9248-21.92-14.9248-35.904L460.7808 179.6288c0-13.984 4.9728-26.08 14.9248-36.3008S497.6128 128 511.5968 128c14.528 0 26.7648 5.1072 36.704 15.328 9.9584 10.2208 14.9312 22.3168 14.9312 36.3008l0 306.5536c0 13.984-4.9728 25.952-14.9312 35.904C538.3552 532.032 526.1184 537.0048 511.5968 537.0048L511.5968 537.0048zM511.5968 537.0048" fill="#d81e06" p-id="9546"></path></svg>&nbsp;结束订单</a> '

                            //    + '  </div>'



                            //    + "</td>");



                            tr += ("<td>" + '<span class="button-style style-hs">' + data[i].serv_name + '</span>' + "</td>");
                            tr += ("<td class='' " + ' ondblclick="openrightBoxpopup(\'accounts_baseinfo.aspx?id=' + data[i].userid + '\')" style="cursor:pointer;"' + ">" + (('<span style="background: #f3ecec;color: #956b0a;font-size: 12px;border-radius: 3px;padding: 1px 4px;margin-right: 5px;font-weight: bold;border: 1px solid #ccc;">' + (data[i].typename == 'th' ? '托号' : data[i].typename == 'user' ? '普通' : data[i].typename == 'super' ? '高级' : data[i].typename == 'reg' ? '新' : '其他') + '</span>') + (data[i].group_name == '-' ? '' : "<span class='button-style style-js'>" + data[i].group_name) + "</span>") + data[i].phone + '<div style="color:gray;font-size:12px;margin-top:3px;">今日剩余任务次数：<b style="color:#c53d3d;font-size:15px;">' + data[i].stock_number + '</b></div>' + "</td>");

                            tr += ("<td style='font-weight: bold;'>" + data[i].amount.toFixed(2)

                                + (parseNumber(data[i].exp_amount).toFixed(2) > 0 ? '<span style="font-size: 12px;margin-left: 8px;font-weight: bold;color: #816427;">体验金 ' + parseNumber(data[i].exp_amount).toFixed(2) + '</span>' : '')
                                 + (parseNumber(data[i].exp_amount).toFixed(2) > 0 ? ('<div>'
                            + '<span style="color:gray;font-size:12px;margin-top:3px;margin-left:3px;font-weight: 500;">(实际金额' + (parseNumber(data[i].amount) - parseNumber(data[i].exp_amount)).toFixed(2) + ')</span>'
                            + '</div>'):'')


                            //+ '<div>'
                            //+ '<span style="' + (parseNumber(data[i].balance) != parseNumber(data[i].amount) ? 'color:#7000ff;' : 'color:gray;') + 'font-size:12px;margin-top:3px;font-weight: 100;">(额度' + parseNumber(data[i].balance).toFixed(2) + ')</span>'
                            //+ '<span title="额度更新时间' + data[i].game_update_time + '" style="' + (isSameDay(data[i].game_update_time) ? 'color:#177761;' : 'color:gray;') + 'font-size:12px;margin-top:3px;margin-left:3px;font-weight: 500;">(游戏额度' + parseNumber(data[i].game_balance).toFixed(2) + ')</span>'
                            //+ '</div>'
                            + "</td>");

                            tr += ("<td >" + "<span style='font-weight: bold;font-size: 18px;color: #2a2b2c;text-shadow: 3px 3px 7px #ff3b00;'>" + data[i].award_amount.toFixed(2) + "</span>" + '<span style="font-size: 12px; margin-left: 8px;">已到账 ' + (data[i].finish_award_amount ? data[i].finish_award_amount : 0).toFixed(2) + '</span>' + '<div style="color:gray;font-size:12px;margin-top:3px;">(任务' + (data[i].award_amount + data[i].serve_amount).toFixed(2) + ' 服务' + data[i].serve_amount.toFixed(2) + ')</div>' + "</td>");





                            var rate_model = new Array();

                            rate_model.push({ name: '', id: 'action_type', value: 'succ_rate', type: 'hidden' });
                            rate_model.push({ name: '成功率', id: 'succ_rate', value: data[i].succ_rate });

                            tr += ("<td style='color:#a72323;'><a " + (data[i].state == 100 ? "onclick='edit(" + edtext({ aname: 'admin', title: _actionName + "成功率调整", action: _action, id: data[i].id, data: rate_model }) + ")'" : "") + " >" + data[i].succ_rate + "%</a></td>");
                            //tr += ("<td class=''>" + '<span class="button-style style-zs" style="cursor:pointer;" onclick="show_details(' + data[i].id + ')">' + data[i].payment_type + '</span>' + "</td>");
                            tr += ("<td style='color:gray;'>" + data[i].days + "天</td>");
                            tr += ("<td><b>" + data[i].task_days + "</b>" + ((data[i].days - data[i].task_days) <= 0 ? '<span style="font-size:12px;color:#a72323;margin-left:2px;">(今日结束)</span>' : '<span style="font-size:12px;color:gray;margin-left:2px;">(剩余' + (data[i].days - data[i].task_days) + '天)</span>') + "</td>");
                            tr += ("<td>" + '<div >任务佣金' + data[i].rate_award + '%</div>' + '<span  style="color:gray;">房主佣金 ' + data[i].serve_fee + '%</span>' + "</td>");
                            tr += ("<td>" + (data[i].state == 1 ? '<span style="color:green;">已完成</span>' : data[i].state == 0 ? '<span style="color:gray;">审核中</span>' : data[i].state == -1 ? '<span style="color:gray;">已撤销</span>' : data[i].state == 100 ? '<span style="color:green;">进行中</span>' : '其他')
                                //+ (data[i].state == 100 ? '<a style="margin-left:5px;"  class="design-button-common" onclick="' + "get_action({name:'finish_sellorder',data:{id:" + data[i].id + "},n:'完成卖币交易',e:'确认后订单 " + data[i].orderNo + " " + "直接完成'});" + '">结束订单</a>' : '')
                                //selects:[{id:'startnow',name:'生效方式',list:" + JSON.stringify(startnow).replace(/"/g, "'") + "}],
+ (data[i].state == 0 ? ('<a style="margin-left:5px;"  class="design-button-common" onclick="' + "get_action({name:'onetouch_verify_success',data:{id:" + data[i].id + "},n:'通过审核',e:'订单将在 " + data[i].create_time + " " + "次日开始生效',inputs:[{id:'succ_rate',name:'成功率',value:'" + data[i].succ_rate + "'}]});" + '">通过</a><a style="margin-left:5px;"  class="design-button-full" onclick="' + "get_action({name:'onetouch_verify_fail',data:{id:" + data[i].id + "},n:'交易撤销',e:'订单 " + data[i].orderNo + " " + " 交易撤销原路返还'});" + '">取消</a>') : '')
                                + "</td>");

                            tr += ("<td class='' style='font-size:12px;'>"
                              + (data[i].last_task_time == "" ? "" : "<div style='color:#a72323;margin-top:2px;'>派单 " + data[i].last_task_time + "</div>")
                              //+ (data[i].expire_time == "" ? "" : "<div style='color:black;margin-top:2px;'>结束 " + data[i].expire_time + "</div>")
                              //+ (data[i].start_time == "" ? "" : "<div style='color:green;margin-top:2px;'>开始 " + data[i].start_time + "</div>")
                              + ('<div style="color:gray;">' + data[i].create_time + ' 创建</div>')
                              + "</td>");

                            tr += "</tr>";
                            $("#list-display").append(tr)
                        }
                        $(".page_size").html(pager_size);
                        $(".page_count").html(json.data.count);
                        $(".cpages").html(json.data.html);


                        pop_mp3tip();

                        $(".total_amount").html(parseFloat(json.total_amount).toFixed(2));
                        $(".total_number").html(json.total_number);


                    } else {
                        _modal("提示", json.msg);
                    }
                },
                error: function () {
                }
            });
        }
        getPager();

        function _pt(i) {
            pager_index = i;
            getPager();
        }
    </script>

    <audio id="myAudio" controls style="display: none;">
        <source src="../static/files/新订单.mp3" type="audio/mpeg">
        Your browser does not support the audio element.
    </audio>
    <script>

        function checkIsSelect(obj) {
            if (obj.prop('checked')) {
                return true;
            } else {
                return false;
            }
        }

        var autoTimer = 0;
        var lastList = null;

        var set_autoAlarm = function (e) {
            if (checkIsSelect($(e))) {
                console.log('启动');
                lastList = null;

                autoTimer = setInterval(function () {
                    getPager();
                }, 5000);

            } else {
                console.log('关闭');
                clearInterval(autoTimer);
                autoTimer = 0;
            }
        }

        var pop_mp3tip = function (json) {
            if (autoTimer == 0) {
                return;
            }

            if (current_json.length > 0) {
                play_mp3tip();
                setRbtip('待处理卖币订单' + current_json.length + '条', '<div style="text-align:center;margin-top:18px;"><a style="color: #f73030;font-size:18px;background: #e1e1e1;display: inline-block;padding: 9px 28px;border-radius: 2px;font-weight: bold;" onclick="open_new_page(\'../serv/sell_dating_list.aspx?state=verify\',\'待审核\');layer.closeAll();">立即查看</a></div>')
            }

        }

        var mp3_audio = document.getElementById("myAudio"); // 获取音频元素
        var play_mp3tip = function () {
            console.log('播放订单提示');
            mp3_audio.play();
        }



        layui.use(['form', 'layedit', 'laydate'], function () {
        });


        function report_exl() {
            export_data(getParam());
        }


        function parseDate(dateString) {
            var parts = (dateString + '').split(/[- :]/);
            return new Date(parts[0], parts[1] - 1, parts[2], parts[3], parts[4], parts[5]);
        }

        function isSameDay(dateString) {
            // 解析传入的日期字符串
            var inputDate = parseDate(dateString);

            // 获取当前日期
            var now = new Date();

            // 比较年份、月份和日期是否相同
            return inputDate.getFullYear() === now.getFullYear() &&
                   inputDate.getMonth() === now.getMonth() &&
                   inputDate.getDate() === now.getDate();
        }


        var show_details = function (id, type) {
            var details_info = "";
            for (var i = 0; i < current_json.length; i++) {
                var obj = current_json[i];

                if (obj.id == id) {
                    details_info += ' <div style="display: flex; margin: 20px 0; padding-bottom: 20px; border-bottom: 1px solid #eee;">            <div>                <b>支付方式：</b>            </div>            <div style="margin-left: 10px;">                ' + obj.payment_type + '            </div>        </div>';
                    details_info += ' <div style="display: flex; margin: 20px 0; padding-bottom: 20px; border-bottom: 1px solid #eee;">            <div>                <b>收款姓名：</b>            </div>            <div style="margin-left: 10px;">                ' + obj.payment_name + '            </div>        </div>';

                    if (obj.payment_type == "银行卡") {
                        details_info += ' <div style="display: flex; margin: 20px 0; padding-bottom: 20px; border-bottom: 1px solid #eee;">            <div>                <b>收款卡号：</b>            </div>            <div style="margin-left: 10px;">                ' + obj.payment_bankid + '            </div>        </div>';
                        details_info += ' <div style="display: flex; margin: 20px 0; padding-bottom: 20px; border-bottom: 1px solid #eee;">            <div>                <b>开户行：</b>            </div>            <div style="margin-left: 10px;">                ' + obj.payment_bankname + '            </div>        </div>';
                    } else {
                        details_info += ' <div style="display: flex; margin: 20px 0; padding-bottom: 20px; border-bottom: 1px solid #eee;">            <div>                <b>' + obj.payment_type + '账号：</b>            </div>            <div style="margin-left: 10px;">                ' + obj.payment_bankid + '            </div>        </div>';
                    }
                    details_info += ' <div style="display: flex; margin: 20px 0; padding-bottom: 20px; border-bottom: 1px solid #eee;">            <div>                <b>交易金额：</b>            </div>            <div style="margin-left: 10px;">                ' + obj.amount + '            </div>        </div>';

                    //details_info += '<div style="display: flex; margin: 20px 0; padding-bottom: 20px; border-bottom: 1px solid #eee;">' + (obj.state == 1000 && obj.orderLock != 1 ? '&nbsp;<a style="display: flex;align-items: center;padding: 10px 39px;margin-left: 6px;line-height: 19px;color: #5a5a5c;border: 1px solid #5a5a5c;" onclick="' + "get_action({name:'lock_order',data:{id:" + obj.id + "},n:'锁定交易',e:'锁定 " + obj.orderId + " 的交易<br>【订单金额】：" + obj.amount + "'});" + '"><svg t="*************" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="7884" width="16" height="16"><path d="M500.224 694.528c-56.576 0-102.912-46.08-102.912-102.912a102.912 102.912 0 0 1 205.824 0c0 56.832-46.336 102.912-102.912 102.912z m0-146.944a44.032 44.032 0 1 0 0 88.064 44.032 44.032 0 0 0 0-88.064z" fill="#005CFF" p-id="7885"></path><path d="M733.44 318.72h-84.224c-1.28-77.056-64-139.264-141.312-139.264-77.312 0-140.032 62.208-141.312 139.264h-84.224c-64.512 0-116.992 52.48-116.992 116.992V747.52c0 64.512 52.48 116.992 116.992 116.992h450.816c64.512 0 116.992-52.48 116.992-116.992V435.712a116.736 116.736 0 0 0-116.736-116.992z m-225.536-80.384c44.8 0 81.408 35.84 82.432 80.384h-165.12c1.536-44.544 37.888-80.384 82.688-80.384zM791.808 747.52c0 32.256-26.112 58.368-58.368 58.368H282.624c-32.256 0-58.368-26.112-58.368-58.368V435.712c0-32.256 26.112-58.368 58.368-58.368h450.816c32.256 0 58.368 26.112 58.368 58.368V747.52z" fill="#1B1F28" p-id="7886"></path></svg>&nbsp;锁定</a>' : '') + '</div>';

                    _modal('收款信息', details_info);
                    break;
                }
            }
        }
    </script>
</asp:Content>


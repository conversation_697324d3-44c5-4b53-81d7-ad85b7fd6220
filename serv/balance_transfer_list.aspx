<%@ Page Title="额度转余额申请" Language="C#" MasterPageFile="~/serv/m.master" AutoEventWireup="true" CodeFile="balance_transfer_list.aspx.cs" Inherits="w_index" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="Server">
    <link href="../css/element-ui.css" rel="stylesheet" />
    <style>
        .el-tag.el-tag--test {
            background-color: #d5d5f5;
            border-color: #7d7dc7;
            color: #6d3333;
        }

        .button-group {
            display: flex;
            justify-content: left;
        }

        .button {
            display: inline-block;
            padding: 8px 20px;
            margin-right: 10px;
            font-size: 12px;
            text-align: center;
            text-decoration: none;
            color: #333;
            background-color: #eee;
            border-radius: 5px;
            transition: background-color 0.3s;
            border: 1px solid #eee;
        }


            .button:hover, .selected {
                background-color: #fff;
                color: #222!important;
                font-weight: bold;
                border: 1px solid #333;
            }
    </style>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="Server">
    <div class="ibox float-e-margins">
        <div class="ibox-title">
            <h5>额度转余额申请</h5>
        </div>
        <div class="ibox-content">
            <div class="row layui-form">

                <div class="col-md-3">
                    <div class="form-group form-buttom" style="position: relative;">
                        <input type="text" id="search-keyword" name="ordername" class="form-control" placeholder="请输入搜索的信息" style="padding-right: 50px;">
                        <a class="input_button" onclick="searchList()">查询</a>
                    </div>
                </div>


                <div class="col-md-4">
                    <div class="form-group form-buttom" id="twoInputs">
                        <input type="text" id="start_time" name="ordername" class="form-control" placeholder="开始时间" style="width: 48%; display: inline-block;" value="" autocomplete="off">
                        <input type="text" id="end_time" name="ordername" class="form-control" placeholder="结束时间" style="width: 48%; display: inline-block;" autocomplete="off">

                        <script>
                            $(document).ready(function () {
                                $('#start_time').datepicker({
                                    format: 'yyyy-mm-dd',
                                    autoclose: true
                                });

                                $('#end_time').datepicker({
                                    format: 'yyyy-mm-dd',
                                    autoclose: true
                                });
                            });
                        </script>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="form-group form-buttom">
                        <a class="btn btn-success" onclick="javascript:location.href=location.href;">
                            <svg t="1688237949108" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2211" width="16" height="16">
                                <path d="M512 192a298.24 298.24 0 0 0-245.162667 128 42.666667 42.666667 0 1 1-69.973333-48.810667 384.042667 384.042667 0 0 1 697.173333 180.48l36.608-34.645333a42.666667 42.666667 0 0 1 58.709334 61.952l-112.64 106.666667a42.666667 42.666667 0 0 1-71.253334-38.912A298.666667 298.666667 0 0 0 512 192z m0.170667 640a298.24 298.24 0 0 0 245.162666-128 42.666667 42.666667 0 1 1 69.973334 48.810667 384.042667 384.042667 0 0 1-697.173334-180.48l-36.650666 34.645333a42.666667 42.666667 0 0 1-58.666667-61.952l112.64-106.666667a42.666667 42.666667 0 0 1 71.253333 38.912 298.666667 298.666667 0 0 0 293.418667 354.730667z" fill="#14101C" p-id="2212"></path></svg>
                            刷新</a>
                    </div>
                </div>



            </div>



            <div>
                
                <input type="checkbox" id="filter_th" checked="checked" />&nbsp;过滤托号
            </div>



            <div class="table-responsive">
                <table class="table">
                    <thead>
                        <tr role="head">
                            <th sort="true">
                                <input type="checkbox" onclick="javascript: select_all(this)" />
                                #ID</th>
                            <th sort="true" class="">审核人员</th>
                            <th sort="true" class="">用户</th>
                            <th sort="true" class="">转入金额</th>
                            <th sort="true" class="">交易凭证</th>
                            <th sort="true" class="">状态</th>
                            <th sort="true" class="">时间</th>
                            <th sort="true"></th>
                        </tr>
                    </thead>
                    <tbody id="list-display">
                    </tbody>
                </table>
            </div>


            <div class="layui-table-page">
                <div id="layui-table-page1">
                    <div class="layui-box layui-laypage layui-laypage-default" id="layui-laypage-1">
                        <div style="display: inline-block;" class="cpages">
                            <!--page grow -->
                        </div>
                        <span class="layui-laypage-skip">到第<input type="text" min="1" value="1" class="layui-input target-page" />页
                            <button type="button" class="layui-laypage-btn" onclick="javascript:pager_index=$('.target-page').val()-1;getPager();">确定</button></span>
                        <span class="layui-laypage-count">共 <span class="page_count"></span>条</span>
                        <span class="layui-laypage-limits">
                            <select class="s_pages" lay-ignore="" onchange="javascript:pager_size=$(this).val();getPager();">
                                <option value="10">10 条/页</option>
                                <option value="20" selected="">20 条/页</option>
                                <option value="30">30 条/页</option>
                                <option value="50">50 条/页</option>
                                <option value="100">100 条/页</option>
                                <option value="200">200 条/页</option>
                            </select></span>
                    </div>
                </div>
            </div>

        </div>
    </div>
    <script>
        var _action = 'balance_transfer_list';
        var _actionName = '额度转入余额申请';
    </script>

    <style>
        .clr {
            font-weight: bold;
            font-size: 13px;
        }

            .clr.c_succ {
                text-shadow: 5px 5px 5px #4faf6e7d;
                color: #4faf6e;
            }

            .clr.c_info {
                text-shadow: 5px 5px 5px #7f91857d;
                color: #7a7d7b;
            }

            .clr.c_error {
                text-shadow: 5px 5px 5px #cf656563;
                color: #a13434;
            }

            .clr.c_shake {
                text-shadow: 5px 5px 5px #6577cf63;
                color: #3446a1;
            }


            .clr.nshadow {
                text-shadow: none!important;
            }
    </style>

    <script id="account_groups" type="text/javascript">
        <%=ToJson(chelper.gdt("account_groups")) %>
    </script>
    <script>

        var temp_list = JSON.parse($('#account_groups').html());
        var account_groups = [['', '无']];
        for (var i = 0; i < temp_list.length; i++) {
            account_groups.push([temp_list[i].id, temp_list[i].name]);
        }


        function opennew() {
            var modify_model = [];

            modify_model.push({ name: '上级邀请码', id: 'parent_code', value: '' });

            edit({ aname: 'admin', title: '添加' + _actionName, action: _action, id: '', data: modify_model });
        }
    </script>
    <script>

        var pager_size = $(".s_pages").val();
        var pager_index = 0;
        var pager_key = "";

        $("#pagesize").val(pager_key);
        function setPageSize(obj) {
            pager_index = 0;
            pager_key = $("#search-keyword").val();
            opt = $(obj).val();
            getPager();
        }

        function searchList() {
            opt = $("#opt1").val();
            pager_index = 0;
            pager_key = $("#search-keyword").val();
            getPager();
        }

        function getParam() {
            return { limit: pager_size, p: pager_index, page: _action, keyword: pager_key, audit_state: $("#audit_state").val(), settle_state: $("#settle_state").val(), start_time: $("#start_time").val(), end_time: $("#end_time").val(), tarea: "<%=Request.QueryString["area"] %>", state: "<%=Request.QueryString["state"] %>", register_from: get_param('rv'), groupid: $("#groupid").val(), group: "<%=Request.QueryString["group"] %>", filter_th: $('#filter_th').prop('checked') }
        }

        function getPager() {

            $.ajax({
                type: "POST",
                url: "../api/admin.aspx?do=page",
                data: getParam(),
                datatype: "json",
                success: function (json) {
                    $("#list-display").html("");
                    if (json.code == 1) {
                        var data = json.data.list;
                        for (var i = 0; i < data.length; i++) {
                            var tr = "";
                            tr += "<tr>";
                            //tr += ("<td>" + data[i].group_id + "</td>");

                            tr += ('<td class=""><input class="good_select" type="checkbox" data-id="' + data[i].id + '" id="checkid_' + data[i].id + '" />' + data[i].id + '</td>');

                            tr += ("<td class=''>" + (data[i].servid == "" ? "<span style='color:gray;'>无</span>" : ('<span class="button-style  style-zs">' + data[i].serv_name + '</span>')) + "</td>");

                            tr += ("<td class='' " + ' ondblclick="openrightBoxpopup(\'accounts_baseinfo.aspx?id=' + data[i].userid + '\')" style="cursor:pointer;"' + ">" + (('<span style="background: #f3ecec;color: #956b0a;font-size: 12px;border-radius: 3px;padding: 1px 4px;margin-right: 5px;font-weight: bold;border: 1px solid #ccc;">' + (data[i].typename == 'th' ? '托号' : data[i].typename == 'user' ? '普通' : data[i].typename == 'super' ? '高级' : data[i].typename == 'reg' ? '新' : '其他') + '</span>') + (data[i].group_name == '-' ? '' : "<span class='button-style style-js'>" + data[i].group_name) + "</span>") + data[i].phone  + "</td>");
                            tr += ("<td class=''>" + '<span class="clr c_shake">' + parseFloat(data[i].amount).toFixed(2) + '</span>' + "</td>");
                            tr += ("<td class='' style='color:gray;'>" + data[i].token + "</td>");
                            tr += ("<td class=''>" + (data[i].state == 0 ? '<span class="clr c_info nshadow">等待审核</span>' : data[i].state == -1 ? '<span class="clr c_error nshadow">未通过</span>' : data[i].state == 1 ? '<span class="clr c_succ nshadow">转账成功</span>' : '') + "</td>");
                            tr += ("<td class=''>" + data[i].create_time + "</td>");



                            var modify_model = new Array();
                            var point_model = new Array();
                            var sms_model = new Array();

                            tr += ("<td class='action-cell '>"
                                + ' <div class="card-toolbar">'

                                + (data[i].state == 0 ? (

                                '<a class="card-button common-button" ' + "onclick=\"" + "get_action({name:'audit_balance_transfer',data:{id:" + data[i].id + ",aname:'admin',state:1},n:'转入通过',e:'通过" + data[i].phone + "的额度 " + parseFloat(data[i].amount).toFixed(2) + " 转入余额的请求'});" + "\"" + '  style="color:green!important;"><svg t="1697636602806" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2038" width="12" height="12"><path d="M68 528.4s229.6 188 252.4 347.6c0 0 301.2-492.8 635.6-536.8 0 0-102-74.4-68.4-191.6 0 0-185.6 18.4-535.2 561.2l-164-278.4L68 528.4z m0 0" fill="#22AC38" p-id="2039"></path></svg>&nbsp;通过</a>'
                                + '<a class="card-button common-button" ' + "onclick=\"" + "get_action({name:'audit_balance_transfer',data:{id:" + data[i].id + ",aname:'admin',state:-1},n:'转入驳回',e:'驳回" + data[i].phone + "的额度 " + parseFloat(data[i].amount).toFixed(2) + " 转入余额的请求'});" + "\"" + ' style="color:red!important;"><svg t="1697636646496" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="10540" width="12" height="12"><path d="M758.464 188.2496l81.4592 81.4592-244.3776 244.3776 244.3776 244.3776-81.4592 81.4592-244.3776-244.3776-244.3776 244.3776-81.4592-81.4592L432.64 514.0864 188.2496 269.7088l81.4592-81.4592L514.0864 432.64l244.3776-244.3776z" fill="#FF4D4F" p-id="10541"></path></svg>&nbsp;驳回</a>'

                               ) : '')






                                //+ '<div class="card-popup">'

                                //+ "<div class='menu-item px-3'><a onclick='edit(" + edtext({ aname: 'admin', title: _actionName + "修改", action: _action, id: data[i].id, data: modify_model }) + ")' class='menu-link px-3'>编辑</a></div>"

                                //+ "<div class='menu-item px-3'><a onclick=\"event_create({'aname':'admin','name':'" + _action + "','event':'delete','id':" + data[i].id + "})\" class='menu-link px-3'>删除</a></div>"

                                //+ '</div>'
                                + '</div>');



                            tr += "</tr>";
                            $("#list-display").append(tr)


                            var __temp = data[i];
                            __temp.modify_model = modify_model;
                            _bindEvent(__temp);

                            //$('button[edit-' + __temp.id + ']').on("click", function () {
                            //    console.log('attrs', $(this).closest('tr').html(), __temp.modify_model);

                            //    edit({ aname: 'admin', title: '修改' + _actionName, action: _action, id: __temp.id, data: __temp.modify_model });
                            //})



                        }
                        $(".page_size").html(pager_size);
                        $(".page_count").html(json.data.count);
                        $(".cpages").html(json.data.html);


                    } else {

                    }
                },
                error: function () {
                }
            });
        }
        getPager();

        function _bindEvent(__temp) {
            $('button[edit-' + __temp.id + ']').on("click", function () {
                console.log('attrs', $(this).closest('tr').html(), __temp.modify_model);

                edit({ aname: 'admin', title: '修改' + _actionName, action: _action, id: __temp.id, data: __temp.modify_model });
            })
        }

        function _pt(i) {
            pager_index = i;
            getPager();
        }
    </script>

    <script>
        layui.use(['form', 'layedit', 'laydate'], function () {
        });
    </script>

    <script>
        function update_select(state) {
            event_create({ 'aname': 'admin', 'name': _action, 'event': 'update', 'id': select_all_id(), 'state': state });
        }
    </script>

    <script>
        var click_ids = {};
        function checkIsSelect(obj) {
            if (obj.prop('checked')) {
                return true;
            } else {
                return false;
            }
        }
        function goods_click(t) {
            t = $(t);
            var obj = t.find(".good_select");
            obj.prop("checked", !checkIsSelect(obj));

        }

        function select_all(obj) {
            $(".good_select").prop("checked", checkIsSelect($(obj)));
        }

        function select_all_id() {
            var ids = "";
            $("#list-display tr").each(function () {
                var select_obj = $(this).find(".good_select");
                if (checkIsSelect(select_obj)) {
                    if (ids != "") {
                        ids += ",";
                    }
                    ids += select_obj.data("id");
                }
            });
            return ids;
        }
    </script>
</asp:Content>



<%@ Page Title="首页统计" Language="C#" MasterPageFile="~/serv/m.master" AutoEventWireup="true" CodeFile="base_data.aspx.cs" Inherits="serv_index" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="Server">
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="Server">


    <style>
        .card_container {
            padding: 5px;
            width: 25%;
            position: relative;
        }

        @media screen and (max-width:1300px) {
            .card_container {
                width: 33.33%;
            }
        }

        @media screen and (max-width:1000px) {
            .card_container {
                width: 50%;
            }
        }

        @media screen and (max-width:650px) {
            .card_container {
                width: 100%;
            }
        }



        .card_item {
            background: linear-gradient(45deg, #289edb, #c8d5db);
            color: #fff;
            padding: 22px;
            font-size: 14px;
            position: relative;
            width: 100%;
            padding: 10px 22px;
            border-radius: 5px;
        }

            .card_item .number {
                font-size: 23px;
                font-weight: bold;
                margin: 5px 0;
                font-size: 18px;
            }

            .card_item .today_card {
                margin-bottom: 10px;
                margin-bottom: 2px;
            }

                .card_item .today_card .today_data {
                    font-size: 20px;
                    font-weight: bold;
                    margin-left: 5px;
                    font-size: 16px;
                }

                .card_item .today_card .yesterday_number {
                    margin-left: 5px;
                }

            .card_item .card_icon {
                position: absolute;
                top: calc( 50% - 35px );
                right: 15px;
            }

                .card_item .card_icon svg {
                    color: #e7e7e7;
                }

                    .card_item .card_icon svg:not(.limit_fill) path {
                        fill: currentColor;
                    }
    </style>








    <div style="padding: 0 0px; padding-top: 18px; margin-bottom: -22px;">
        <div class="col-md-12">
            <div class="form-group form-buttom" style="position: relative;">
                <span style="position: absolute; top: 0; height: 100%; background: #f7e61a; display: flex; align-items: center; padding: 0 18px; font-weight: bold; color: #2d2a2a; border-right: 2px solid #d9cc31;">上级ID</span>
                <input type="text" id="team_uid" class="form-control" placeholder="要搜索的用户ID/手机号码" style="padding-left: 85px;" value="<%=Request.QueryString["uid"] %>">
            </div>
        </div>
    </div>





    <div style="padding-top: 20px;">
        <div class="col-md-3">
            <div class="form-group form-buttom" id="twoInputs">
                <input type="text" id="start_time" name="ordername" class="form-control" placeholder="开始时间" style="width: 48%; display: inline-block;" value="" autocomplete="off">
                <input type="text" id="end_time" name="ordername" class="form-control" placeholder="结束时间" style="width: 48%; display: inline-block;" autocomplete="off">

                <script>
                    $(document).ready(function () {
                        $('#start_time').datepicker({
                            format: 'yyyy-mm-dd',
                            autoclose: true
                        });

                        $('#end_time').datepicker({
                            format: 'yyyy-mm-dd',
                            autoclose: true
                        });
                    });
                </script>
            </div>
        </div>


        <div class="col-md-1">
            <div class="form-group form-buttom" style="position: relative;">
                <input type="text" id="roomNumber" class="form-control" placeholder="房间号" value="<%=Request.QueryString["roomNumber"] + "" %>">
            </div>
        </div>



        <div class="col-md-8">
            <div style="display: flex; flex-wrap: wrap;">

                <div class="timesel_lab">全部</div>
                <div class="timesel_lab">今天</div>
                <div class="timesel_lab">昨天</div>
                <div class="timesel_lab">本周</div>
                <div class="timesel_lab">上周</div>
                <div class="timesel_lab">本月</div>
                <div class="timesel_lab">上月</div>
                <div class="timesel_lab search" style="margin-left: 20px; background: #17B492; color: #fff;">查询</div>



                <div class="timer-wrap" style="position: relative; display: none;">

                    <div class="wrap">
                        <div class="wrap-l">
                            <div class="wrap-l-starTime">
                                <input name="startTime" type="text" placeholder="开始日期">
                            </div>
                        </div>
                        <div class="wrap-r">
                            <div class="wrap-l-endTime">
                                <input name="endTime" type="text" placeholder="结束日期">
                            </div>
                        </div>
                    </div>

                </div>


            </div>
        </div>





    </div>



    <div style="display: flex; flex-wrap: wrap; width: 100%;">




        <div class="card_container" id="accounts">
            <div class="card_item" style="background: linear-gradient(45deg, #FC418F, #FF6B83);">

                <div>总用户量</div>
                <div class="number">
                    <span class="total_number">0</span>
                </div>
                <div class="today_card" style="display: flex;">
                    <div style="cursor: pointer;" onclick="open_new_page('account_list.aspx?newuser=<%=DateTime.Now.ToString("yyyy-MM-dd") %>','【今日】新增用户');">
                        今日新增用户<span class="today_data"><span class="today_number">0</span> </span>
                    </div>
                    <div style="margin-left: 8px; cursor: pointer;" onclick="open_new_page('account_list.aspx?newbuy=<%=DateTime.Now.ToString("yyyy-MM-dd") %>','【今日】新增首存');">
                        首存<span class="today_data"><span class="firstbuy_number">0</span> </span>
                    </div>
                </div>
                <div style="display: flex;">
                    <div style="cursor: pointer;" onclick="open_new_page('account_list.aspx?newuser=<%=DateTime.Now.AddDays(-1).ToString("yyyy-MM-dd") %>','【昨日】新增用户');">昨日新增用户&nbsp;<span class="yesterday_data"><span class="yesterday_number">0</span> </span></div>
                    <div style="margin-left: 8px; cursor: pointer;" onclick="open_new_page('account_list.aspx?newbuy=<%=DateTime.Now.AddDays(-1).ToString("yyyy-MM-dd") %>','【昨日】新增首存');">
                        首存&nbsp;<span class="today_data"><span class="yesterday_firstbuy_number">0</span> </span>
                    </div>
                </div>

                <div class="card_icon">
                    <svg t="" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="3738" width="66" height="66">
                        <path d="M767.23 482.61c35.1-31.02 57.35-75.93 57.35-126 0-86.84-65.08-158.94-151.38-167.72-17.47-1.77-33.28 11.03-35.08 28.61-1.78 17.58 11.03 33.28 28.61 35.06 53.5 5.45 93.84 50.17 93.84 104.05 0 53.31-39.88 97.98-92.75 103.92-11.64 1.3-20.63 8.96-25.12 18.91-0.04 0.08-0.08 0.15-0.12 0.22-1.24 2.8-1.94 5.7-2.33 8.82-0.2 1.16-0.82 2.15-0.88 3.35-0.05 0.89 0.35 1.66 0.37 2.53 0.03 0.53-0.19 1-0.13 1.53 0.09 0.8 0.53 1.44 0.68 2.21 2.18 14.73 13.84 26.58 29.28 27.44 119.3 6.59 216.69 101.45 226.55 220.66 1.38 16.72 15.38 29.36 31.86 29.36 0.88 0 1.77-0.03 2.67-0.11 17.61-1.45 30.7-16.91 29.25-34.53-9.75-117.84-87.27-216.63-192.67-258.31z" fill="#333333" p-id="3739"></path><path d="M536.12 499.11c44.15-35.34 72.55-89.57 72.55-150.39 0-106.25-86.44-192.69-192.67-192.69s-192.67 86.44-192.67 192.69c0 60.81 28.4 115.05 72.55 150.39C170 545.37 75.68 661.55 64.11 801.33c-1.45 17.61 11.64 33.08 29.25 34.53 18.06 1.56 33.08-11.66 34.53-29.25C140.19 657.89 266.75 541.39 416 541.39s275.81 116.5 288.11 265.22c1.38 16.72 15.38 29.36 31.86 29.36 0.88 0 1.77-0.03 2.67-0.11 17.61-1.45 30.7-16.92 29.25-34.53C756.32 661.55 662 545.37 536.12 499.11zM287.33 348.72c0-70.95 57.72-128.69 128.67-128.69s128.67 57.73 128.67 128.69S486.95 477.39 416 477.39s-128.67-57.72-128.67-128.67z" fill="#333333" p-id="3740"></path></svg>
                </div>
            </div>
        </div>




        <div class="card_container" id="online_users">
            <div class="card_item" style="background: linear-gradient(45deg, #61dfab, #4fa507);">

                <div>在线用户数</div>
                <div class="number">
                    <span class="total_number">0</span>
                </div>
                <div class="today_card">
                    普通在线账户<span class="today_data"><span class="today_number">0</span> </span>
                </div>
                <div>
                    托号在线账户<span class="yesterday_data"><span class="yesterday_number">0</span> </span>
                </div>

                <div class="card_icon">
                    <svg t="" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="25570" width="60" height="60">
                        <path d="M508.928 589.824c-149.504 0-270.336-121.856-270.336-270.336S359.424 48.128 508.928 48.128s270.336 121.856 270.336 270.336-120.832 271.36-270.336 271.36z m0-463.872c-106.496 0-192.512 86.016-192.512 192.512S403.456 512 508.928 512c106.496 0 192.512-86.016 192.512-192.512s-86.016-193.536-192.512-193.536zM772.096 989.184H246.784c-97.28 0-176.128-78.848-176.128-176.128s78.848-176.128 176.128-176.128h525.312c97.28 0 176.128 78.848 176.128 176.128s-78.848 176.128-176.128 176.128zM246.784 711.68c-56.32 0-102.4 46.08-102.4 102.4s46.08 102.4 102.4 102.4h525.312c56.32 0 102.4-46.08 102.4-102.4s-46.08-102.4-102.4-102.4H246.784z" fill="#4C4C4C" p-id="25571"></path><path d="M756.736 836.608h-93.184c-21.504 0-38.912-17.408-38.912-38.912s17.408-38.912 38.912-38.912h93.184c21.504 0 38.912 17.408 38.912 38.912s-17.408 38.912-38.912 38.912z" fill="#FFA028" p-id="25572"></path></svg>
                </div>
            </div>
        </div>


        <div class="card_container" id="recharge_total">
            <div class="card_item" style="background: linear-gradient(45deg, #9B59C7, #BE47D5);">

                <div>充值总量</div>
                <div class="number">
                    <span class="total_amount">0</span> / <span class="total_number">0</span>人    
                </div>
                <div class="today_card">
                    今日充值总量<span class="today_data"><span class="today_amount">0</span> / <span class="today_number">0</span>人</span>
                </div>
                <div>
                    昨日充值总量<span class="yesterday_data"><span class="yesterday_amount">0</span> / <span class="yesterday_number">0</span>人</span>
                </div>

                <div class="card_icon">
                    <svg t="" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="4874" width="66" height="66">
                        <path d="M515.669333 130.133333c17.28 0.853333 34.090667 5.674667 49.28 13.994667 101.546667 60.458667 202.026667 122.88 301.482667 186.752 34.645333 23.850667 36.224 81.450667 0.170667 106.453333-98.432 63.658667-198.826667 124.16-298.24 186.24a110.208 110.208 0 0 1-109.653334 1.834667 10494.037333 10494.037333 0 0 1-301.098666-188.074667c-34.773333-24.192-35.498667-81.834667 0.213333-106.453333 98.389333-63.189333 198.741333-123.306667 298.112-184.874667 18.005333-10.709333 38.4-16.213333 59.733333-15.872z m-5.845333 42.666667c-10.368 0.554667-20.48 3.413333-29.568 8.405333-101.034667 60.202667-205.226667 116.181333-299.904 185.984-10.752 8.490667-10.496 26.666667 1.024 34.645334a10428.672 10428.672 0 0 0 298.666667 186.624 66.133333 66.133333 0 0 0 63.872 0c101.162667-60.672 205.397333-117.077333 300.074666-187.392a22.186667 22.186667 0 0 0-1.194666-34.688 10401.28 10401.28 0 0 0-299.008-185.173334 67.669333 67.669333 0 0 0-33.962667-8.448z" fill="#0400FF" p-id="4875"></path><path d="M875.093333 491.221333c29.653333 5.461333 18.944 54.485333-9.813333 74.410667-98.389333 63.658667-198.784 124.16-298.24 186.24a110.208 110.208 0 0 1-109.568 1.834667 10599.509333 10599.509333 0 0 1-301.141333-188.074667C135.210667 550.912 128 512.682667 128 512.682667c1.066667-25.472 32.597333-25.856 43.946667 7.04 1.493333 3.84 3.84 7.082667 7.04 9.642666 94.634667 70.272 198.784 126.72 299.776 187.392a66.133333 66.133333 0 0 0 63.872 0c101.162667-60.586667 205.397333-117.077333 300.032-187.392 18.346667-14.592 7.552-39.893333 32.426666-38.144z" fill="#0400FF" p-id="4876"></path><path d="M876.373333 619.221333c29.653333 5.461333 18.944 54.485333-9.813333 74.410667-98.389333 63.658667-198.741333 124.16-298.24 186.24a110.208 110.208 0 0 1-109.568 1.834667 10599.509333 10599.509333 0 0 1-301.141333-188.074667c-21.162667-14.72-28.330667-52.949333-28.330667-52.949333 1.066667-25.472 32.597333-25.856 43.946667 7.04 1.493333 3.84 3.84 7.082667 7.04 9.642666 94.634667 70.314667 198.784 126.72 299.776 187.392a66.133333 66.133333 0 0 0 63.872 0c101.162667-60.586667 205.397333-117.077333 300.074666-187.392 18.602667-14.762667 3.968-40.192 32.341334-38.144z" fill="#0400FF" p-id="4877"></path></svg>
                </div>
            </div>
        </div>

        <div class="card_container" id="cash_total">
            <div class="card_item" style="background: linear-gradient(45deg, #4a6ea5, #58bda2);">

                <div>提现总量</div>
                <div class="number">
                    <span class="total_amount">0</span> / <span class="total_number">0</span>人    
                </div>
                <div class="today_card">
                    今日提现总量<span class="today_data"><span class="today_amount">0</span> / <span class="today_number">0</span>人</span>
                </div>
                <div>
                    昨日提现总量<span class="yesterday_data"><span class="yesterday_amount">0</span> / <span class="yesterday_number">0</span>人</span>
                </div>

                <div class="card_icon">
                    <svg t="" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="11789" width="66" height="66">
                        <path d="M771.46112 512A259.46112 259.46112 0 1 1 512 252.53888 259.46112 259.46112 0 0 1 771.46112 512" fill="#dbdbdb" opacity=".2" p-id="11790"></path><path d="M273.4336 250.4192a27.52 27.52 0 0 0 17.4848-6.28736 344.9856 344.9856 0 0 1 18.944-14.57664 27.47392 27.47392 0 0 0-32.0512-44.63616 412.19072 412.19072 0 0 0-21.85216 16.85504 27.45856 27.45856 0 0 0 17.47968 48.64M512 109.75744a402.85184 402.85184 0 0 0-112.90624 16.07168 27.46368 27.46368 0 1 0 15.39072 52.736 347.04896 347.04896 0 1 1-222.06464 197.20192 27.46368 27.46368 0 0 0-50.51392-21.56032A402.29376 402.29376 0 1 0 512 109.75744m181.06368 426.23488a18.944 18.944 0 0 1-15.45728 6.35904 16.59392 16.59392 0 0 1-14.56128-6.35904l-28.24704-38.13888v84.38272c0 7.02464-11.60192 10.52672-23.1936 10.52672s-23.1936-3.50208-23.1936-10.52672V449.6384c0-12.50816 11.59168-17.1008 23.1936-17.1008 16.64 0 23.78752 1.3056 35.96288 17.1008l30.63296 39.45472 30.62272-39.45472c12.19584-15.7952 19.328-17.1008 35.97824-17.1008 11.89888 0 23.19872 4.608 23.19872 17.1008v132.608c0 7.02464-11.60704 10.52672-23.19872 10.52672s-23.1936-3.50208-23.1936-10.52672V500.04992zM555.99616 432.5376a13.92128 13.92128 0 0 1 13.98784 15.11936 14.25408 14.25408 0 0 1-13.98784 15.56992h-41.0112v119.0144c0 7.02464-11.61216 10.52672-23.19872 10.52672s-23.1936-3.50208-23.1936-10.52672V463.22688h-41.03168a14.23872 14.23872 0 0 1-13.96736-15.56992 13.91104 13.91104 0 0 1 13.96736-15.11936h128.4352zM363.9296 532.69504L342.2208 473.9584l-21.7088 58.73664h43.4176zM256 578.73408a9.04192 9.04192 0 0 1 0.5888-2.4064l55.296-133.71392c3.28192-7.68 16.65536-11.39712 30.32064-11.39712s27.0592 3.72224 30.34112 11.39712l55.59296 133.71392a8.84736 8.84736 0 0 1 0.5888 2.4064c0 8.12032-16.93696 14.04928-29.72672 14.04928-7.43424 0-13.34272-1.75616-15.25248-6.36416l-10.09664-27.40224H310.99904l-10.10176 27.40224c-1.78688 4.608-7.73632 6.36416-15.24224 6.36416-12.71808 0-29.65504-5.92896-29.65504-14.04928" fill="#dbdbdb" p-id="11791"></path></svg>
                </div>
            </div>
        </div>

        <div class="card_container" id="recharge_dating">
            <div class="card_item" style="background: linear-gradient(45deg, #F0AB22, #EEB320);">

                <div>大厅买币总额</div>
                <div class="number">
                    <span class="total_amount">0</span> / <span class="total_number">0</span>人       
                </div>
                <div class="today_card">
                    今日买币充值<span class="today_data"><span class="today_amount">0</span> / <span class="today_number">0</span>人</span>
                </div>
                <div>
                    昨日买币充值<span class="yesterday_data"><span class="yesterday_amount">0</span> / <span class="yesterday_number">0</span>人</span>
                </div>

                <div class="card_icon">
                    <svg t="" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="3814" width="66" height="66">
                        <path d="M517.418444 965.859231c-9.63279 0-19.437594-0.344028-29.070385-0.860071-59.00084-3.612296-115.593482-18.577524-168.2298-43.863598-54.700487-26.318159-103.036452-62.78515-143.631782-108.884932-40.59533-45.927768-70.869814-98.392071-90.135394-156.016798-18.577524-55.560558-26.146145-113.529313-22.533848-172.530153s18.577524-115.593482 43.863598-168.2298c26.318159-54.700487 62.78515-103.036452 108.884932-143.631782 45.927768-40.59533 98.392071-70.869814 156.016798-90.135394 46.271796-15.48127 94.607761-23.393919 143.459768-23.393919 9.63279 0 19.437594 0.344028 29.070385 0.860071 58.828826 3.612296 115.593482 18.577524 168.401814 43.863598 54.700487 26.318159 103.036452 62.78515 143.631782 108.884932 47.991937 54.356459 81.19066 118.345708 99.252142 189.903578l0 0.172014 0 0.172014c3.612296 11.180917 0.344028 23.393919-8.256677 31.134554-5.332437 4.644381-12.213002 7.396607-19.26558 7.396607-8.428691 0-16.341341-3.612296-22.017806-9.976818-1.720141-1.892155-2.752226-3.956325-3.612296-5.676466l-0.172014-0.344028-0.860071-1.032085-0.516042-2.236183c-14.793214-61.409037-44.379641-117.657652-88.071225-166.853687-69.665715-78.610449-165.649588-125.570301-270.062154-131.934823-8.256677-0.516042-16.685369-0.860071-24.942046-0.860071-47.647909 0-94.263733 8.428691-138.299345 25.11406-44.207626 16.685369-84.974971 41.283387-120.92592 72.933983-78.610449 69.321687-125.570301 165.133546-131.934823 269.890139-6.536536 104.584579 28.210314 205.728876 97.532001 284.339325 69.149672 78.610449 165.133546 125.570301 269.890139 132.106837 8.256677 0.516042 16.685369 0.860071 24.942046 0.860071 47.647909 0 94.263733-8.428691 138.299345-25.11406 44.207626-16.685369 84.974971-41.283387 120.92592-72.933983 62.78515-55.560558 105.44465-128.322526 123.506131-211.061314l0.172014-0.344028 0-0.344028c0.688056-6.70855 3.78431-12.901058 8.77272-17.889467 0.172014-0.172014 0.344028-0.344028 0.344028-0.344028 0.172014-0.172014 0.344028-0.344028 0.516042-0.516042 5.332437-4.644381 12.213002-7.224593 19.437594-7.224593 7.568621 0 14.793214 2.752226 20.125651 7.912649 5.504452 5.160423 8.77272 12.55703 8.944734 20.985721 0 0.860071-0.172014 1.720141-0.172014 2.408198-19.953637 98.392071-69.665715 184.743155-143.631782 249.764488-45.755753 40.423316-98.220057 70.697799-156.016798 90.135394C614.606417 957.946582 566.270452 965.859231 517.418444 965.859231zM470.630606 729.339829 470.630606 485.939862l-3.612296 1.032085c-21.501764 6.192508-40.939358 12.213002-59.172854 18.233496-7.912649 2.580212-18.233496 5.332437-29.070385 8.084663-14.965228 3.956325-30.618512 7.912649-40.423316 12.040988l-1.720141 0.688056 0 127.806484-70.525785 0 0-199.536368 70.353771 0 0 29.414413 3.78431-1.376113c5.84848-2.236183 16.513355-4.644381 27.866286-7.224593 11.69696-2.752226 24.942046-5.84848 35.090879-9.116748 19.437594-6.192508 41.455401-12.213002 65.365362-17.373425l2.236183-0.516042 0-51.088191-2.92424 0.172014c-38.531161 2.064169-74.654124 3.268268-106.992777 3.440282-16.685369 0.172014-32.510667 0.860071-46.271796 1.376113-11.69696 0.516042-22.877877 1.032085-32.854695 1.032085l-12.901058-50.572148c17.029397 0.172014 34.402822 1.376113 51.260205 2.408198 17.717453 1.204099 35.950949 2.408198 53.15236 2.408198 5.676466 0 10.836889-0.172014 15.997312-0.344028 38.359147-2.064169 77.062322-5.504452 115.07744-10.492861 38.015118-4.988409 75.858223-11.352931 112.325214-19.26558 35.778935-7.396607 72.761969-16.857383 110.261045-27.694272 6.020494 14.793214 10.836889 26.146145 14.965228 36.466991 0.860071 2.064169 1.720141 4.128339 2.408198 6.020494 1.204099 2.752226 2.236183 5.504452 3.440282 8.084663 1.548127 3.78431 3.096254 7.396607 4.472367 10.492861l0.688056 1.720141c1.032085 2.92424 2.408198 6.020494 3.956325 9.116748-32.682681 5.332437-66.569461 9.804804-100.456241 13.245087-24.942046 2.580212-56.764656 4.816395-84.802956 6.880564-9.804804 0.688056-18.921552 1.376113-27.006215 2.064169l-2.580212 0.172014 0 35.950949 3.096254-0.344028c11.008903-1.032085 25.630102-1.548127 41.283387-1.548127 15.653284 0 30.446498 0.516042 41.627415 1.548127 31.82261 2.752226 57.452713 8.428691 76.202251 17.029397 18.749538 8.428691 33.198723 17.889467 43.003528 27.866286 9.804804 10.148833 16.169326 20.469679 19.26558 30.618512 3.440282 10.836889 5.332437 24.081975 6.020494 30.274483 0.344028 8.77272 0.860071 18.921552 1.204099 30.274483 0.344028 11.008903 0 22.361834-1.204099 33.714766-1.204099 11.352931-3.440282 22.361834-6.70855 32.682681-3.268268 10.492861-7.912649 19.26558-13.761129 26.490173-6.192508 7.224593-13.933143 12.55703-23.909961 16.169326-5.84848 2.064169-12.901058 3.096254-20.469679 3.096254-5.332437 0-11.008903-0.516042-17.029397-1.376113-13.589115-2.064169-25.630102-4.644381-36.122963-7.912649-11.352931-3.440282-20.469679-6.70855-27.694272-9.804804-8.600706-3.612296-15.653284-6.880564-21.32975-9.976818l3.268268-31.650596c13.073072 6.364522 24.942046 10.492861 35.090879 11.868974 2.580212 0.344028 5.160423 0.516042 7.740635 0.516042 2.408198 0 4.988409-0.172014 7.396607-0.688056 4.644381-0.860071 9.116748-2.752226 13.589115-5.676466 4.300353-2.92424 7.912649-7.396607 10.664875-13.417101 2.92424-6.020494 4.644381-13.417101 5.504452-22.18982 2.064169-23.393919 2.580212-41.455401 1.720141-53.324374-0.344028-6.536536-0.860071-8.600706-1.204099-9.63279 0-3.440282-0.344028-7.740635-0.860071-12.901058-0.516042-5.160423-2.064169-9.804804-4.300353-13.761129-4.300353-7.396607-12.385016-14.277171-24.598018-20.813707-9.976818-5.332437-23.909961-7.912649-40.939358-7.912649-3.440282 0-6.880564 0.172014-10.492861 0.344028l-0.172014 0c-7.912649 0.860071-15.997312 1.720141-23.565933 2.408198-2.92424 0.344028-7.568621 0.688056-13.073072 1.032085-9.804804 0.860071-20.985721 1.548127-26.834201 2.752226l-2.236183 0.344028 0 252.860742-70.869814 0L470.630606 729.339829 470.630606 729.339829 470.630606 729.339829z" fill="#575B66" p-id="3815"></path></svg>
                </div>
            </div>
        </div>




        <div class="card_container" id="recharge_action">
            <div class="card_item" style="background: linear-gradient(45deg, #3129CC, #8380B6);">

                <div>活动充值总额</div>
                <div class="number">
                    <span class="total_amount">0</span> / <span class="total_number">0</span>人    
                </div>
                <div class="today_card">
                    今日新增活动总额<span class="today_data"><span class="today_amount">0</span> / <span class="today_number">0</span>人</span>
                </div>
                <div>
                    昨日新增活动总额<span class="yesterday_data"><span class="yesterday_amount">0</span> / <span class="yesterday_number">0</span>人</span>
                </div>

                <div class="card_icon">
                    <svg t="" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="8198" width="66" height="66">
                        <path d="M161.6 897.6c-24-6.4-43.2-27.2-43.2-52.8V409.6c0-28.8 24-52.8 52.8-52.8h30.4L278.4 176c11.2-27.2 36.8-44.8 65.6-44.8 9.6 0 19.2 1.6 28.8 6.4 6.4 3.2 14.4 6.4 22.4 8l14.4 4.8 9.6 3.2c4.8 1.6 9.6 3.2 16 6.4 16 6.4 25.6 9.6 32 12.8l40-40c14.4-14.4 33.6-22.4 54.4-22.4 20.8 0 40 8 56 24L792 308.8c12.8 12.8 20.8 30.4 22.4 49.6h40c28.8 0 52.8 24 52.8 52.8V848c0 28.8-24 52.8-52.8 52.8H161.6zM840 832V424H185.6v408H840zM345.6 200c-1.6 0-3.2 1.6-3.2 3.2l-65.6 155.2h8L416 227.2l-3.2-1.6-24-9.6-4.8-1.6c-11.2-4.8-22.4-8-33.6-12.8-3.2-1.6-3.2-1.6-4.8-1.6z m393.6 158.4L566.4 185.6c-1.6-1.6-3.2-1.6-4.8-1.6s-3.2 0-4.8 1.6L385.6 358.4h353.6z" p-id="8199"></path><path d="M611.2 667.2h-70.4v-40h70.4c12.8 0 24-11.2 24-24s-11.2-24-24-24h-70.4l73.6-73.6c9.6-9.6 9.6-24 0-33.6-9.6-9.6-24-9.6-33.6 0l-64 64-64-64c-9.6-9.6-24-9.6-33.6 0-9.6 9.6-9.6 24 0 33.6l73.6 73.6h-70.4c-12.8 0-24 11.2-24 24s11.2 24 24 24h70.4v40h-70.4c-12.8 0-24 11.2-24 24s11.2 24 24 24h70.4V784c0 12.8 11.2 24 24 24s24-11.2 24-24v-68.8h70.4c12.8 0 24-11.2 24-24s-11.2-24-24-24z" p-id="8200"></path></svg>
                </div>
            </div>
        </div>


        <div class="card_container" id="recharge_sanfang">
            <div class="card_item" style="background: linear-gradient(45deg, #0d4056, #42a59c);">

                <div>三方充值总额</div>
                <div class="number">
                    <span class="total_amount">0</span> / <span class="total_number">0</span>人    
                </div>
                <div class="today_card">
                    今日新增三方总额<span class="today_data"><span class="today_amount">0</span> / <span class="today_number">0</span>人</span>
                </div>
                <div>
                    昨日新增三方总额<span class="yesterday_data"><span class="yesterday_amount">0</span> / <span class="yesterday_number">0</span>人</span>
                </div>

                <div class="card_icon">
                    <svg t="" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="24027" width="66" height="66">
                        <path d="M74.3 525.2c-7.6 0.8-22 4.1-29.8 11.1-23.6 20.4-9.4 57.8 38.1 57.8 27.7 0 55.3-17.6 77-45.9-30.8-15-57-25.8-85.3-23zM251.9 551c44.1 14.7 54 15.5 54 15.5V408.3c0-13-5.2-25.4-14.3-34.6-9.2-9.2-21.6-14.3-34.6-14.3H48.9c-13 0-25.4 5.2-34.6 14.3C5.2 382.9 0 395.4 0 408.3v207.3c0 13 5.2 25.4 14.3 34.6 9.2 9.2 21.6 14.3 34.6 14.3h208c13 0 25.4-5.2 34.6-14.3 9.2-9.2 14.3-21.6 14.3-34.6v-2s-79.6-33.1-119.8-52.3c-26.9 33.1-61.7 53.1-97.9 53.1-61 0-81.8-53.3-52.9-88.3 6.3-7.6 17-14.9 33.7-19 26-6.4 67.4 4 106.3 16.8 7.2-13.3 13-27.4 17.2-42H72.9v-12.1h61.6V448H60v-12h74.7v-30.9c0-1.4 0.5-2.8 1.5-3.7 1-1 2.4-1.5 3.8-1.5h30.2V436H244v12.1h-73.9v21.7h60.3c-5.4 22.5-14 44.2-25.5 64.3 18.3 6.6 34.7 12.8 47 16.9z m0 0" fill="#25ABEE" p-id="24028"></path><path d="M885.3 397.3h99.2v19.6h35.4v-23.3-0.1c0.1-5.2-4-9.4-9.2-9.5h-57.4v-18.3h-37.1V384h-66.4v33h35.4v-19.7z m-240-30L609 453.2h27.9v102.2h30.5V419.9h-11l23-52.5h-34.1z m367.6 145.9c-0.7-2.4-2.9-4.1-5.4-4.1H978l8.6 28.9h-34.7v-56h68.1v-13.3h-68.1v-26.3h68.1V429H850v13.3h68v26.3h-68.1V482H918v55.9h-68.1v13.3H1024l-0.1-0.5h0.1l-11.1-37.5z m-289.6-75.5c-1.2-2.4-3.6-4-6.3-4h-26.4l20.5 64.7h31.2l-19-60.7z m0 0" fill="#0C0400" p-id="24029"></path><path d="M790 368.9h-31.7V402h-78v13.3h78v119c-0.2 2.7-2.4 4.8-5.1 5h-16.1v13.8h41c6.6-0.4 11.8-5.8 11.8-12.3V415.3h12.9V402H790v-33.1zM571.6 543c-24.4-8.7-47.7-19.8-69.8-33.3 26.1-19.3 48.9-45.5 58-80.5H499v-21.7h70.5v-13.6H499v-27.6h-31.4c-1.7 0-3.3 0.7-4.5 1.9-1.2 1.2-1.8 2.8-1.8 4.5v21.2h-70v13.6h70v21.7h-59.1v13.6H520s-6.5 25.5-38.8 52.9c-29-21.9-38.6-39.3-38.6-39.3H411c12.8 22.1 30.9 39.9 50.5 54-18 11.7-41.3 22.9-71.4 31.9v16.3c32.1-7 62.9-18.8 91.4-35.2 28.1 16.1 58.5 28 90.2 35.2V543z m-146.4 40.3l-29 78.8H411l5.4-15.9h29l5.3 15.9h14.8l-29-78.8h-11.3z m-5.3 53l11.3-32.6 10.9 32.6h-22.2z m124.4-53h-14.2v78.8h47.7v-10.7h-33.6v-68.1z m103.7 0h14.2v78.8H648v-78.8z m120.4 0h-30.6v78.8H752v-30.4h16.5c15.3 0 26-10 26-24.3-0.1-14.2-10.8-24.1-26.1-24.1z m-0.8 37.7H752v-27h15.7c7.9 0 12.6 6.9 12.6 13.5-0.1 6.7-4.5 13.5-12.7 13.5z m107.9-37.7l-29 78.8h14.8l5.4-15.9h29l5.3 15.9h14.8l-29-78.8h-11.3z m-5.3 53l11.3-32.6 10.9 32.6h-22.2z m124.6-53l-15.5 33.6-15.7-33.6h-15.3l23.9 46.5v32.4h14.1v-32.4l0.1-0.2 23.8-46.3h-15.4z m0 0" fill="#0C0400" p-id="24030"></path></svg>
                </div>
            </div>
        </div>



        <div class="card_container" id="recharge_admin">
            <div class="card_item" style="background: linear-gradient(45deg, #6e89a2, #31d1bb);">

                <div>手动充值总额</div>
                <div class="number">
                    <span class="total_amount">0</span> / <span class="total_number">0</span>人    
                </div>
                <div class="today_card">
                    今日新增手充总额<span class="today_data"><span class="today_amount">0</span> / <span class="today_number">0</span>人</span>
                </div>
                <div>
                    昨日新增手充总额<span class="yesterday_data"><span class="yesterday_amount">0</span> / <span class="yesterday_number">0</span>人</span>
                </div>

                <div class="card_icon">
                    <svg t="" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="12046" width="66" height="66">
                        <path d="M906.666667 266.666667h-85.333334v85.333333a32 32 0 0 1-64 0v-85.333333h-85.333333a32 32 0 0 1 0-64h85.333333v-85.333334a32 32 0 0 1 64 0v85.333334h85.333334a32 32 0 0 1 0 64zM512 192h-21.333333a341.333333 341.333333 0 1 0 341.333333 341.333333c0-7.210667-0.64-14.250667-1.066667-21.333333a33.578667 33.578667 0 0 1 33.066667-32A35.712 35.712 0 0 1 896 512v21.333333A405.333333 405.333333 0 1 1 490.666667 128h21.333333a33.6 33.6 0 0 1 32 32A32 32 0 0 1 512 192z" fill="#5B8DF0" p-id="12047"></path><path d="M650.666667 512h-128v64h85.333333a32 32 0 0 1 0 64h-85.333333v74.666667a32 32 0 0 1-64 0V640h-85.333334a32 32 0 0 1 0-64h85.333334v-64h-128a32 32 0 0 1 0-64h95.146666l-35.626666-61.674667a26.709333 26.709333 0 0 1 46.293333-26.666666l37.333333 64.661333a26.496 26.496 0 0 1 0.853334 23.68h42.666666a26.496 26.496 0 0 1 0.853334-23.68l37.333333-64.661333a26.709333 26.709333 0 0 1 46.293333 26.666666L566.186667 448h84.48a32 32 0 0 1 0 64z" fill="#F2A355" p-id="12048"></path></svg>
                </div>
            </div>
        </div>




        <div class="card_container" id="deduct_admin">
            <div class="card_item" style="background: linear-gradient(45deg, #a26e90, #d17631);">

                <div>手动扣除总额</div>
                <div class="number">
                    <span class="total_amount">0</span> / <span class="total_number">0</span>人    
                </div>
                <div class="today_card">
                    今日新增扣除总额<span class="today_data"><span class="today_amount">0</span> / <span class="today_number">0</span>人</span>
                </div>
                <div>
                    昨日新增扣除总额<span class="yesterday_data"><span class="yesterday_amount">0</span> / <span class="yesterday_number">0</span>人</span>
                </div>

                <div class="card_icon">
                    <svg t="" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="4887" width="60" height="60">
                        <path d="M682.666667 507.12381v-97.52381h-87.771429l73.142857-131.657143-82.895238-48.761905-78.019047 131.657143-73.142858-131.657143-82.895238 48.761905 73.142857 131.657143H336.457143v97.52381h121.904762v39.009523H336.457143v97.52381h121.904762v131.657143h97.523809v-131.657143H682.666667v-97.52381h-126.780953v-39.009523zM658.285714 697.295238h336.457143v97.52381h-336.457143z" p-id="4888" fill="#5B8DF0"></path><path d="M512 902.095238c-214.552381 0-390.095238-175.542857-390.095238-390.095238s175.542857-390.095238 390.095238-390.095238 390.095238 175.542857 390.095238 390.095238c0 24.380952 0 48.761905-4.87619 68.266667l97.523809 19.504762c4.87619-29.257143 9.752381-58.514286 9.752381-87.771429 0-268.190476-219.428571-487.619048-487.619048-487.619048S29.257143 243.809524 29.257143 512s219.428571 487.619048 487.619047 487.619048c87.771429 0 170.666667-24.380952 243.809524-63.390477l-48.761904-82.895238c-63.390476 29.257143-131.657143 48.761905-199.92381 48.761905z" p-id="4889" fill="#5B8DF0"></path></svg>
                </div>
            </div>
        </div>



        <div class="card_container" id="account_money">
            <div class="card_item" style="background: linear-gradient(45deg, #47441F, #34372C);">

                <div>用户总余额</div>
                <div class="number">
                    <span class="total_amount">0</span> / <span class="total_number">0</span>人  
                </div>
                <div class="today_card">
                    今日新增余额<span class="today_data"><span class="today_amount">0</span> / <span class="today_number">0</span>人</span>
                </div>
                <div>
                    昨日新增余额<span class="yesterday_data"><span class="yesterday_amount">0</span> / <span class="yesterday_number">0</span>人</span>
                </div>

                <div class="card_icon">
                    <svg t="" class="icon" viewBox="0 0 1066 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="9849" width="66" height="66">
                        <path d="M42.667 512C72.533 640 174.933 738.133 345.6 810.667s371.2 76.8 593.067 12.8v157.866H256c-119.467 0-213.333-93.866-213.333-213.333V512z" fill="#C7CFFF" p-id="9850"></path><path d="M85.333 298.667h384C494.933 298.667 512 281.6 512 256s-17.067-42.667-42.667-42.667h-384v-42.666c0-46.934 38.4-85.334 85.334-85.334h298.666c25.6 0 42.667-17.066 42.667-42.666S494.933 0 469.333 0H170.667C76.8 0 0 76.8 0 170.667v682.666C0 947.2 76.8 1024 170.667 1024h682.666C947.2 1024 1024 947.2 1024 853.333V682.667c0-25.6-17.067-42.667-42.667-42.667s-42.666 17.067-42.666 42.667v170.666c0 46.934-38.4 85.334-85.334 85.334H170.667c-46.934 0-85.334-38.4-85.334-85.334V298.667z m550.4-17.067c-55.466 25.6-93.866 64-115.2 119.467-4.266 12.8-8.533 38.4-8.533 72.533 0 21.333 17.067 38.4 38.4 38.4h8.533c21.334 0 38.4-17.067 38.4-38.4s0-38.4 4.267-46.933c25.6-59.734 85.333-85.334 166.4-85.334s140.8 25.6 166.4 85.334c4.267 8.533 4.267 21.333 4.267 42.666s17.066 38.4 38.4 38.4h4.266c21.334 0 38.4-17.066 38.4-38.4 0-29.866 0-55.466-8.533-72.533-21.333-55.467-64-93.867-119.467-119.467 29.867-25.6 46.934-64 46.934-106.666C938.667 76.8 861.867 0 768 0S597.333 76.8 597.333 170.667c0 42.666 17.067 81.066 38.4 110.933zM768 256c-46.933 0-85.333-38.4-85.333-85.333s38.4-85.334 85.333-85.334 85.333 38.4 85.333 85.334S814.933 256 768 256z" fill="#3689F5" p-id="9851"></path><path d="M256 469.333h128c25.6 0 42.667 17.067 42.667 42.667S409.6 554.667 384 554.667H256c-25.6 0-42.667-17.067-42.667-42.667s21.334-42.667 42.667-42.667zM256 640h426.667c25.6 0 42.666 17.067 42.666 42.667s-17.066 42.666-42.666 42.666H256c-25.6 0-42.667-17.066-42.667-42.666S234.667 640 256 640z" fill="#F4B300" p-id="9852"></path></svg>
                </div>
            </div>
        </div>


        <div class="card_container" id="user_cash_money">
            <div class="card_item" style="background: linear-gradient(45deg, #F17422, #EC4E1F);">

                <div>提现总额</div>
                <div class="number">
                    <span class="total_amount">0</span> / <span class="total_number">0</span>人 / <span class="total_daily_number">0</span>笔
                </div>
                <div class="today_card">
                    今日新增提现<span class="today_data"><span class="today_amount">0</span> / <span class="today_number">0</span>人 / <span class="today_daily_number">0</span>笔</span>
                </div>
                <div>
                    昨日新增提现<span class="yesterday_data"><span class="yesterday_amount">0</span> / <span class="yesterday_number">0</span>人 / <span class="yesterday_daily_number">0</span>笔</span>
                </div>

                <div class="card_icon">
                    <svg t="" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="11859" width="66" height="66">
                        <path d="M941.1584 438.1184l-396.288-338.2272c-20.4288-17.408-51.3536-17.408-71.7824 0L76.8 438.1184c-36.6592 31.2832-13.3632 88.9856 35.8912 88.9856h88.8832v316.0576c0 48.64 41.5232 88.064 92.7232 88.064h429.3632c51.2 0 92.7232-39.424 92.7232-88.064v-316.0576h88.8832c49.2544 0.0512 72.5504-57.7024 35.8912-88.9856z m-302.7456 231.8848c24.1664 0 43.776 18.6368 43.776 41.5744s-19.6096 41.5744-43.776 41.5744H556.032v41.0624c0 22.9888-19.6096 41.5744-43.776 41.5744-24.1664 0-43.776-18.6368-43.776-41.5744v-41.0624H386.048c-24.1664 0-43.776-18.6368-43.776-41.5744s19.6096-41.5744 43.776-41.5744h82.3808v-26.8288H386.048c-24.1664 0-43.776-18.6368-43.776-41.5744s19.6096-41.5744 43.776-41.5744h40.9088l-56.832-67.7376c-15.0528-17.9712-11.9808-44.1344 6.9632-58.4704 18.8928-14.336 46.4384-11.3664 61.4912 6.6048l72.192 86.0672 72.0384-89.2416c14.6944-18.2272 42.1888-21.6576 61.3888-7.68 19.2 13.9776 22.784 40.0896 8.0896 58.3168l-58.1632 72.0384h44.2368c24.1664 0 43.776 18.6368 43.776 41.5744s-19.6096 41.5744-43.776 41.5744h-82.3808v26.8288h82.432z" fill="#6C6CEA" p-id="11860"></path><path d="M544.8704 99.8912c-20.4288-17.408-51.3536-17.408-71.7824 0L76.8 438.1184c-36.6592 31.2832-13.3632 88.9856 35.8912 88.9856h88.8832v316.0576c0 14.5408 3.7376 28.16 10.2912 40.2432 37.888 8.192 77.1584 12.6464 117.4528 12.6464 210.944 0 394.1376-118.4256 487.0144-292.352v-76.5952H849.92c15.7696-44.8512 25.8048-92.3648 29.44-141.6704l-334.4896-285.5424z m93.5424 570.112c24.1664 0 43.776 18.6368 43.776 41.5744 0 22.9888-19.6096 41.5744-43.776 41.5744H556.032v41.0624c0 22.9888-19.6096 41.5744-43.776 41.5744-24.1664 0-43.776-18.6368-43.776-41.5744v-41.0624H386.048c-24.1664 0-43.776-18.6368-43.776-41.5744 0-22.9888 19.6096-41.5744 43.776-41.5744h82.3808v-26.8288H386.048c-24.1664 0-43.776-18.6368-43.776-41.5744s19.6096-41.5744 43.776-41.5744h40.9088l-56.832-67.7376c-15.0528-17.9712-11.9808-44.1344 6.9632-58.4704 18.8928-14.336 46.4384-11.3664 61.4912 6.6048l72.192 86.0672 72.0384-89.2416c14.6944-18.2272 42.1888-21.6576 61.3888-7.68 19.2 13.9776 22.784 40.0896 8.0896 58.3168l-58.1632 72.0384h44.2368c24.1664 0 43.776 18.6368 43.776 41.5744s-19.6096 41.5744-43.776 41.5744h-82.3808v26.8288h82.432z" fill="#757BF2" p-id="11861"></path><path d="M544.8704 99.8912c-20.4288-17.408-51.3536-17.408-71.7824 0L76.8 438.1184c-36.6592 31.2832-13.3632 88.9856 35.8912 88.9856h88.8832v172.4416c79.1552-3.2768 154.0096-23.1936 221.184-56.4224H386.048c-24.1664 0-43.776-18.6368-43.776-41.5744s19.6096-41.5744 43.776-41.5744h40.9088l-56.832-67.6864c-15.0528-17.9712-11.9808-44.1344 6.9632-58.4704 18.8928-14.336 46.4384-11.3664 61.4912 6.6048l72.192 86.0672 72.0384-89.2416c14.6944-18.2272 42.1888-21.6576 61.3888-7.68 2.2016 1.5872 4.1472 3.3792 5.9392 5.2224 33.9968-55.8592 58.2144-118.272 70.6048-184.8832l-175.872-150.016z" fill="#8486F8" p-id="11862"></path><path d="M473.088 99.8912L76.8 438.1184c-21.76 18.5856-22.272 46.4896-8.8064 66.048 234.8032-11.8784 430.848-170.5472 498.2784-386.048l-21.4016-18.2784c-20.3776-17.3568-51.3536-17.3568-71.7824 0.0512z" fill="#8D92F8" p-id="11863"></path></svg>
                </div>
            </div>
        </div>



        <div class="card_container" id="sell_dating_total">
            <div class="card_item" style="background: linear-gradient(45deg, #F17422, #EC4E1F);">

                <div>卖币总量</div>
                <div class="number">
                    <span class="total_amount">0</span> / <span class="total_number">0</span>人 / <span class="total_daily_number">0</span>笔
                </div>
                <div class="today_card">
                    今日卖币总量<span class="today_data"><span class="today_amount">0</span> / <a style="color: #fff; text-decoration: none;"><span class="today_number">0</span>人</a> / <span class="today_daily_number">0</span>笔</span>
                </div>
                <div>
                    昨日卖币总量<span class="yesterday_data"><span class="yesterday_amount">0</span> / <a style="color: #fff; text-decoration: none;"><span class="yesterday_number">0</span>人</a> / <span class="yesterday_daily_number">0</span>笔</span>
                </div>

                <div class="card_icon">
                    <svg t="" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="11859" width="66" height="66">
                        <path d="M941.1584 438.1184l-396.288-338.2272c-20.4288-17.408-51.3536-17.408-71.7824 0L76.8 438.1184c-36.6592 31.2832-13.3632 88.9856 35.8912 88.9856h88.8832v316.0576c0 48.64 41.5232 88.064 92.7232 88.064h429.3632c51.2 0 92.7232-39.424 92.7232-88.064v-316.0576h88.8832c49.2544 0.0512 72.5504-57.7024 35.8912-88.9856z m-302.7456 231.8848c24.1664 0 43.776 18.6368 43.776 41.5744s-19.6096 41.5744-43.776 41.5744H556.032v41.0624c0 22.9888-19.6096 41.5744-43.776 41.5744-24.1664 0-43.776-18.6368-43.776-41.5744v-41.0624H386.048c-24.1664 0-43.776-18.6368-43.776-41.5744s19.6096-41.5744 43.776-41.5744h82.3808v-26.8288H386.048c-24.1664 0-43.776-18.6368-43.776-41.5744s19.6096-41.5744 43.776-41.5744h40.9088l-56.832-67.7376c-15.0528-17.9712-11.9808-44.1344 6.9632-58.4704 18.8928-14.336 46.4384-11.3664 61.4912 6.6048l72.192 86.0672 72.0384-89.2416c14.6944-18.2272 42.1888-21.6576 61.3888-7.68 19.2 13.9776 22.784 40.0896 8.0896 58.3168l-58.1632 72.0384h44.2368c24.1664 0 43.776 18.6368 43.776 41.5744s-19.6096 41.5744-43.776 41.5744h-82.3808v26.8288h82.432z" fill="#6C6CEA" p-id="11860"></path><path d="M544.8704 99.8912c-20.4288-17.408-51.3536-17.408-71.7824 0L76.8 438.1184c-36.6592 31.2832-13.3632 88.9856 35.8912 88.9856h88.8832v316.0576c0 14.5408 3.7376 28.16 10.2912 40.2432 37.888 8.192 77.1584 12.6464 117.4528 12.6464 210.944 0 394.1376-118.4256 487.0144-292.352v-76.5952H849.92c15.7696-44.8512 25.8048-92.3648 29.44-141.6704l-334.4896-285.5424z m93.5424 570.112c24.1664 0 43.776 18.6368 43.776 41.5744 0 22.9888-19.6096 41.5744-43.776 41.5744H556.032v41.0624c0 22.9888-19.6096 41.5744-43.776 41.5744-24.1664 0-43.776-18.6368-43.776-41.5744v-41.0624H386.048c-24.1664 0-43.776-18.6368-43.776-41.5744 0-22.9888 19.6096-41.5744 43.776-41.5744h82.3808v-26.8288H386.048c-24.1664 0-43.776-18.6368-43.776-41.5744s19.6096-41.5744 43.776-41.5744h40.9088l-56.832-67.7376c-15.0528-17.9712-11.9808-44.1344 6.9632-58.4704 18.8928-14.336 46.4384-11.3664 61.4912 6.6048l72.192 86.0672 72.0384-89.2416c14.6944-18.2272 42.1888-21.6576 61.3888-7.68 19.2 13.9776 22.784 40.0896 8.0896 58.3168l-58.1632 72.0384h44.2368c24.1664 0 43.776 18.6368 43.776 41.5744s-19.6096 41.5744-43.776 41.5744h-82.3808v26.8288h82.432z" fill="#757BF2" p-id="11861"></path><path d="M544.8704 99.8912c-20.4288-17.408-51.3536-17.408-71.7824 0L76.8 438.1184c-36.6592 31.2832-13.3632 88.9856 35.8912 88.9856h88.8832v172.4416c79.1552-3.2768 154.0096-23.1936 221.184-56.4224H386.048c-24.1664 0-43.776-18.6368-43.776-41.5744s19.6096-41.5744 43.776-41.5744h40.9088l-56.832-67.6864c-15.0528-17.9712-11.9808-44.1344 6.9632-58.4704 18.8928-14.336 46.4384-11.3664 61.4912 6.6048l72.192 86.0672 72.0384-89.2416c14.6944-18.2272 42.1888-21.6576 61.3888-7.68 2.2016 1.5872 4.1472 3.3792 5.9392 5.2224 33.9968-55.8592 58.2144-118.272 70.6048-184.8832l-175.872-150.016z" fill="#8486F8" p-id="11862"></path><path d="M473.088 99.8912L76.8 438.1184c-21.76 18.5856-22.272 46.4896-8.8064 66.048 234.8032-11.8784 430.848-170.5472 498.2784-386.048l-21.4016-18.2784c-20.3776-17.3568-51.3536-17.3568-71.7824 0.0512z" fill="#8D92F8" p-id="11863"></path></svg>
                </div>
            </div>
        </div>

        <%--<div class="card_container" id="gd_user_money">
            <div class="card_item" style="background: linear-gradient(45deg, #e79864, #d78167);">

                <div>挂单总额</div>
                <div class="number">
                    <span class="total_amount">0</span> / <span class="total_number">0</span>人 / <span class="total_daily_number">0</span>笔
                </div>
                <div class="today_card">
                    今日挂单提现<span class="today_data"><span class="today_amount">0</span> / <span class="today_number">0</span>人 / <span class="today_daily_number">0</span>笔</span>
                </div>
                <div>
                    昨日挂单提现<span class="yesterday_data"><span class="yesterday_amount">0</span> / <span class="yesterday_number">0</span>人 / <span class="yesterday_daily_number">0</span>笔</span>
                </div>

                <div class="card_icon">
                    <svg t="" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="11859" width="66" height="66">
                        <path d="M941.1584 438.1184l-396.288-338.2272c-20.4288-17.408-51.3536-17.408-71.7824 0L76.8 438.1184c-36.6592 31.2832-13.3632 88.9856 35.8912 88.9856h88.8832v316.0576c0 48.64 41.5232 88.064 92.7232 88.064h429.3632c51.2 0 92.7232-39.424 92.7232-88.064v-316.0576h88.8832c49.2544 0.0512 72.5504-57.7024 35.8912-88.9856z m-302.7456 231.8848c24.1664 0 43.776 18.6368 43.776 41.5744s-19.6096 41.5744-43.776 41.5744H556.032v41.0624c0 22.9888-19.6096 41.5744-43.776 41.5744-24.1664 0-43.776-18.6368-43.776-41.5744v-41.0624H386.048c-24.1664 0-43.776-18.6368-43.776-41.5744s19.6096-41.5744 43.776-41.5744h82.3808v-26.8288H386.048c-24.1664 0-43.776-18.6368-43.776-41.5744s19.6096-41.5744 43.776-41.5744h40.9088l-56.832-67.7376c-15.0528-17.9712-11.9808-44.1344 6.9632-58.4704 18.8928-14.336 46.4384-11.3664 61.4912 6.6048l72.192 86.0672 72.0384-89.2416c14.6944-18.2272 42.1888-21.6576 61.3888-7.68 19.2 13.9776 22.784 40.0896 8.0896 58.3168l-58.1632 72.0384h44.2368c24.1664 0 43.776 18.6368 43.776 41.5744s-19.6096 41.5744-43.776 41.5744h-82.3808v26.8288h82.432z" fill="#6C6CEA" p-id="11860"></path><path d="M544.8704 99.8912c-20.4288-17.408-51.3536-17.408-71.7824 0L76.8 438.1184c-36.6592 31.2832-13.3632 88.9856 35.8912 88.9856h88.8832v316.0576c0 14.5408 3.7376 28.16 10.2912 40.2432 37.888 8.192 77.1584 12.6464 117.4528 12.6464 210.944 0 394.1376-118.4256 487.0144-292.352v-76.5952H849.92c15.7696-44.8512 25.8048-92.3648 29.44-141.6704l-334.4896-285.5424z m93.5424 570.112c24.1664 0 43.776 18.6368 43.776 41.5744 0 22.9888-19.6096 41.5744-43.776 41.5744H556.032v41.0624c0 22.9888-19.6096 41.5744-43.776 41.5744-24.1664 0-43.776-18.6368-43.776-41.5744v-41.0624H386.048c-24.1664 0-43.776-18.6368-43.776-41.5744 0-22.9888 19.6096-41.5744 43.776-41.5744h82.3808v-26.8288H386.048c-24.1664 0-43.776-18.6368-43.776-41.5744s19.6096-41.5744 43.776-41.5744h40.9088l-56.832-67.7376c-15.0528-17.9712-11.9808-44.1344 6.9632-58.4704 18.8928-14.336 46.4384-11.3664 61.4912 6.6048l72.192 86.0672 72.0384-89.2416c14.6944-18.2272 42.1888-21.6576 61.3888-7.68 19.2 13.9776 22.784 40.0896 8.0896 58.3168l-58.1632 72.0384h44.2368c24.1664 0 43.776 18.6368 43.776 41.5744s-19.6096 41.5744-43.776 41.5744h-82.3808v26.8288h82.432z" fill="#757BF2" p-id="11861"></path><path d="M544.8704 99.8912c-20.4288-17.408-51.3536-17.408-71.7824 0L76.8 438.1184c-36.6592 31.2832-13.3632 88.9856 35.8912 88.9856h88.8832v172.4416c79.1552-3.2768 154.0096-23.1936 221.184-56.4224H386.048c-24.1664 0-43.776-18.6368-43.776-41.5744s19.6096-41.5744 43.776-41.5744h40.9088l-56.832-67.6864c-15.0528-17.9712-11.9808-44.1344 6.9632-58.4704 18.8928-14.336 46.4384-11.3664 61.4912 6.6048l72.192 86.0672 72.0384-89.2416c14.6944-18.2272 42.1888-21.6576 61.3888-7.68 2.2016 1.5872 4.1472 3.3792 5.9392 5.2224 33.9968-55.8592 58.2144-118.272 70.6048-184.8832l-175.872-150.016z" fill="#8486F8" p-id="11862"></path><path d="M473.088 99.8912L76.8 438.1184c-21.76 18.5856-22.272 46.4896-8.8064 66.048 234.8032-11.8784 430.848-170.5472 498.2784-386.048l-21.4016-18.2784c-20.3776-17.3568-51.3536-17.3568-71.7824 0.0512z" fill="#8D92F8" p-id="11863"></path></svg>
                </div>
            </div>
        </div>--%>

        <div class="card_container" id="yongjing_qd">
            <div class="card_item" style="background: linear-gradient(45deg, #8B0B24, #952E62);">

                <div>佣金</div>
                <div class="number">
                    <span class="total_amount">0</span> / <span class="total_number">0</span>人  
                </div>
                <div class="today_card">
                    今日新增佣金<span class="today_data"><span class="today_amount">0</span> / <span class="today_number">0</span>人</span>
                </div>
                <div>
                    昨日新增佣金<span class="yesterday_data"><span class="yesterday_amount">0</span> / <span class="yesterday_number">0</span>人</span>
                </div>

                <div class="card_icon">
                    <svg t="" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="13913" width="66" height="66">
                        <path d="M943.104 469.504c-22.528 0-40.96 18.432-40.96 40.96 0 214.528-174.592 389.12-389.12 389.12s-389.12-174.592-389.12-389.12 174.592-389.12 389.12-389.12c129.024 0 249.344 64 322.048 170.496 12.8 18.944 38.4 23.552 56.832 10.752 18.944-12.8 23.552-38.4 10.752-56.832-88.064-129.536-233.472-206.336-389.632-206.336-259.584 0-471.04 211.456-471.04 471.04s211.456 471.04 471.04 471.04 471.04-211.456 471.04-471.04c0-22.528-18.432-40.96-40.96-40.96z" fill="#333333" p-id="13914"></path><path d="M681.472 493.568c22.528 0 40.96-18.432 40.96-40.96s-18.432-40.96-40.96-40.96h-69.632l95.232-95.232c15.872-15.872 15.872-41.984 0-57.856-15.872-15.872-41.984-15.872-57.856 0l-136.192 136.192-136.192-136.704c-15.872-15.872-41.984-15.872-57.856 0-15.872 15.872-15.872 41.984 0 57.856l95.232 95.232H344.576c-22.528 0-40.96 18.432-40.96 40.96s18.432 40.96 40.96 40.96h127.488v44.544H344.576c-22.528 0-40.96 18.432-40.96 40.96s18.432 40.96 40.96 40.96h127.488v114.176c0 22.528 18.432 40.96 40.96 40.96s40.96-18.432 40.96-40.96V619.52h127.488c22.528 0 40.96-18.432 40.96-40.96s-18.432-40.96-40.96-40.96h-127.488v-44.544h127.488z" fill="#333333" p-id="13915"></path></svg>
                </div>
            </div>
        </div>


        <%--<div class="card_container" id="yongjing_yj">
            <div class="card_item" style="background: linear-gradient(45deg, #ADD2C4, #E77977);">

                <div>一级</div>
                <div class="number">
                    <span class="total_amount">0</span> / <span class="total_number">0</span>人  
                </div>
                <div class="today_card">
                    今日新增佣金<span class="today_data"><span class="today_amount">0</span> / <span class="today_number">0</span>人</span>
                </div>
                <div>
                    昨日新增佣金<span class="yesterday_data"><span class="yesterday_amount">0</span> / <span class="yesterday_number">0</span>人</span>
                </div>

                <div class="card_icon">
                    <svg t="" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="13539" width="66" height="66">
                        <path d="M484.664889 113.777778a227.555556 227.555556 0 0 1 125.326222 417.507555c7.623111 2.474667 14.734222 5.233778 21.134222 8.248889 13.710222 6.456889 22.926222 21.134222 17.92 36.039111-5.006222 14.904889-23.125333 25.116444-36.010666 17.92-31.800889-17.806222-93.212444-24.519111-127.658667-24.604444H483.896889h0.768a313.031111 313.031111 0 0 0-307.768889 256h440.092444c20.565333 0 37.233778 12.743111 37.233778 28.444444 0 14.592-14.364444 26.624-32.881778 28.245334l-4.352 0.199111-459.804444 0.028444a28.444444 28.444444 0 0 1-39.936-16.440889A23.210667 23.210667 0 0 1 113.777778 853.333333c0-4.181333 1.166222-8.135111 3.271111-11.690666l0.568889-4.892445c17.180444-141.852444 114.375111-258.104889 244.252444-303.843555A227.555556 227.555556 0 0 1 484.664889 113.777778z m0 56.888889a170.666667 170.666667 0 1 0 0 341.333333h-0.711111 1.365333a170.666667 170.666667 0 0 0 170.012445-170.666667 170.666667 170.666667 0 0 0-170.666667-170.666666z" fill="#0648A1" p-id="13540"></path><path d="M556.202667 566.044444l-57.144889 45.511112 45.511111 128.483555-56.32 47.502222-60.16-47.502222 47.559111-125.724444L411.591111 566.044444h144.64z" fill="#0648A1" p-id="13541"></path><path d="M796.444444 512a28.444444 28.444444 0 0 1 28.444445 28.444444v28.444445l-0.113778 1.934222c16.64 2.929778 32.426667 9.187556 46.165333 18.432 21.020444 19.057778 32.881778 44.657778 33.052445 71.395556h-42.012445a76.231111 76.231111 0 0 0-21.504-43.946667 80.668444 80.668444 0 0 0-47.388444-11.662222 87.381333 87.381333 0 0 0-42.012444 7.992889c-11.946667 5.973333-18.944 17.578667-17.92 29.752889 0.113778 12.373333 8.049778 23.608889 20.48 29.070222 15.473778 6.826667 31.573333 12.430222 48.156444 16.696889 25.656889 6.599111 50.460444 15.559111 74.012444 26.794666 22.584889 12.970667 35.669333 35.754667 34.332445 59.733334 1.024 22.129778-9.699556 43.434667-28.956445 57.429333a137.244444 137.244444 0 0 1-56.32 19.768889A11.52 11.52 0 0 1 824.888889 853.333333v28.444445a28.444444 28.444444 0 0 1-56.888889 0v-28.444445l0.142222-2.133333a136.732444 136.732444 0 0 1-47.786666-17.777778c-24.746667-21.191111-38.4-50.631111-37.660445-81.237333h42.012445c0.853333 18.801778 8.988444 36.778667 23.04 50.801778 14.933333 9.386667 33.024 13.824 51.2 12.600889a104.647111 104.647111 0 0 0 51.256888-10.524445c11.605333-5.774222 18.773333-16.753778 18.688-28.615111-0.227556-15.36-10.154667-29.269333-25.6-35.925333a556.828444 556.828444 0 0 0-55.324444-18.318223 535.381333 535.381333 0 0 1-63.516444-22.869333c-20.878222-11.690667-33.109333-32.483556-32.028445-54.471111-1.137778-22.016 10.012444-43.093333 29.724445-56.32a126.321778 126.321778 0 0 1 45.966222-17.578667A15.217778 15.217778 0 0 1 768 568.888889v-28.444445a28.444444 28.444444 0 0 1 28.444444-28.444444z" fill="#0648A1" p-id="13542"></path></svg>
                </div>
            </div>
        </div>


        <div class="card_container" id="yongjing_ej">
            <div class="card_item" style="background: linear-gradient(45deg, #ADD2C4, #E77977);">

                <div>二级</div>
                <div class="number">
                    <span class="total_amount">0</span> / <span class="total_number">0</span>人   
                </div>
                <div class="today_card">
                    今日新增佣金<span class="today_data"><span class="today_amount">0</span> / <span class="today_number">0</span>人</span>
                </div>
                <div>
                    昨日新增佣金<span class="yesterday_data"><span class="yesterday_amount">0</span> / <span class="yesterday_number">0</span>人</span>
                </div>

                <div class="card_icon">
                    <svg t="" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="13539" width="66" height="66">
                        <path d="M484.664889 113.777778a227.555556 227.555556 0 0 1 125.326222 417.507555c7.623111 2.474667 14.734222 5.233778 21.134222 8.248889 13.710222 6.456889 22.926222 21.134222 17.92 36.039111-5.006222 14.904889-23.125333 25.116444-36.010666 17.92-31.800889-17.806222-93.212444-24.519111-127.658667-24.604444H483.896889h0.768a313.031111 313.031111 0 0 0-307.768889 256h440.092444c20.565333 0 37.233778 12.743111 37.233778 28.444444 0 14.592-14.364444 26.624-32.881778 28.245334l-4.352 0.199111-459.804444 0.028444a28.444444 28.444444 0 0 1-39.936-16.440889A23.210667 23.210667 0 0 1 113.777778 853.333333c0-4.181333 1.166222-8.135111 3.271111-11.690666l0.568889-4.892445c17.180444-141.852444 114.375111-258.104889 244.252444-303.843555A227.555556 227.555556 0 0 1 484.664889 113.777778z m0 56.888889a170.666667 170.666667 0 1 0 0 341.333333h-0.711111 1.365333a170.666667 170.666667 0 0 0 170.012445-170.666667 170.666667 170.666667 0 0 0-170.666667-170.666666z" fill="#0648A1" p-id="13540"></path><path d="M556.202667 566.044444l-57.144889 45.511112 45.511111 128.483555-56.32 47.502222-60.16-47.502222 47.559111-125.724444L411.591111 566.044444h144.64z" fill="#0648A1" p-id="13541"></path><path d="M796.444444 512a28.444444 28.444444 0 0 1 28.444445 28.444444v28.444445l-0.113778 1.934222c16.64 2.929778 32.426667 9.187556 46.165333 18.432 21.020444 19.057778 32.881778 44.657778 33.052445 71.395556h-42.012445a76.231111 76.231111 0 0 0-21.504-43.946667 80.668444 80.668444 0 0 0-47.388444-11.662222 87.381333 87.381333 0 0 0-42.012444 7.992889c-11.946667 5.973333-18.944 17.578667-17.92 29.752889 0.113778 12.373333 8.049778 23.608889 20.48 29.070222 15.473778 6.826667 31.573333 12.430222 48.156444 16.696889 25.656889 6.599111 50.460444 15.559111 74.012444 26.794666 22.584889 12.970667 35.669333 35.754667 34.332445 59.733334 1.024 22.129778-9.699556 43.434667-28.956445 57.429333a137.244444 137.244444 0 0 1-56.32 19.768889A11.52 11.52 0 0 1 824.888889 853.333333v28.444445a28.444444 28.444444 0 0 1-56.888889 0v-28.444445l0.142222-2.133333a136.732444 136.732444 0 0 1-47.786666-17.777778c-24.746667-21.191111-38.4-50.631111-37.660445-81.237333h42.012445c0.853333 18.801778 8.988444 36.778667 23.04 50.801778 14.933333 9.386667 33.024 13.824 51.2 12.600889a104.647111 104.647111 0 0 0 51.256888-10.524445c11.605333-5.774222 18.773333-16.753778 18.688-28.615111-0.227556-15.36-10.154667-29.269333-25.6-35.925333a556.828444 556.828444 0 0 0-55.324444-18.318223 535.381333 535.381333 0 0 1-63.516444-22.869333c-20.878222-11.690667-33.109333-32.483556-32.028445-54.471111-1.137778-22.016 10.012444-43.093333 29.724445-56.32a126.321778 126.321778 0 0 1 45.966222-17.578667A15.217778 15.217778 0 0 1 768 568.888889v-28.444445a28.444444 28.444444 0 0 1 28.444444-28.444444z" fill="#0648A1" p-id="13542"></path></svg>
                </div>
            </div>
        </div>


        <div class="card_container" id="yongjing_sj">
            <div class="card_item" style="background: linear-gradient(45deg, #ADD2C4, #E77977);">

                <div>三级</div>
                <div class="number">
                    <span class="total_amount">0</span> / <span class="total_number">0</span>人  
                </div>
                <div class="today_card">
                    今日新增佣金<span class="today_data"><span class="today_amount">0</span> / <span class="today_number">0</span>人</span>
                </div>
                <div>
                    昨日新增佣金<span class="yesterday_data"><span class="yesterday_amount">0</span> / <span class="yesterday_number">0</span>人</span>
                </div>

                <div class="card_icon">
                    <svg t="" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="13539" width="66" height="66">
                        <path d="M484.664889 113.777778a227.555556 227.555556 0 0 1 125.326222 417.507555c7.623111 2.474667 14.734222 5.233778 21.134222 8.248889 13.710222 6.456889 22.926222 21.134222 17.92 36.039111-5.006222 14.904889-23.125333 25.116444-36.010666 17.92-31.800889-17.806222-93.212444-24.519111-127.658667-24.604444H483.896889h0.768a313.031111 313.031111 0 0 0-307.768889 256h440.092444c20.565333 0 37.233778 12.743111 37.233778 28.444444 0 14.592-14.364444 26.624-32.881778 28.245334l-4.352 0.199111-459.804444 0.028444a28.444444 28.444444 0 0 1-39.936-16.440889A23.210667 23.210667 0 0 1 113.777778 853.333333c0-4.181333 1.166222-8.135111 3.271111-11.690666l0.568889-4.892445c17.180444-141.852444 114.375111-258.104889 244.252444-303.843555A227.555556 227.555556 0 0 1 484.664889 113.777778z m0 56.888889a170.666667 170.666667 0 1 0 0 341.333333h-0.711111 1.365333a170.666667 170.666667 0 0 0 170.012445-170.666667 170.666667 170.666667 0 0 0-170.666667-170.666666z" fill="#0648A1" p-id="13540"></path><path d="M556.202667 566.044444l-57.144889 45.511112 45.511111 128.483555-56.32 47.502222-60.16-47.502222 47.559111-125.724444L411.591111 566.044444h144.64z" fill="#0648A1" p-id="13541"></path><path d="M796.444444 512a28.444444 28.444444 0 0 1 28.444445 28.444444v28.444445l-0.113778 1.934222c16.64 2.929778 32.426667 9.187556 46.165333 18.432 21.020444 19.057778 32.881778 44.657778 33.052445 71.395556h-42.012445a76.231111 76.231111 0 0 0-21.504-43.946667 80.668444 80.668444 0 0 0-47.388444-11.662222 87.381333 87.381333 0 0 0-42.012444 7.992889c-11.946667 5.973333-18.944 17.578667-17.92 29.752889 0.113778 12.373333 8.049778 23.608889 20.48 29.070222 15.473778 6.826667 31.573333 12.430222 48.156444 16.696889 25.656889 6.599111 50.460444 15.559111 74.012444 26.794666 22.584889 12.970667 35.669333 35.754667 34.332445 59.733334 1.024 22.129778-9.699556 43.434667-28.956445 57.429333a137.244444 137.244444 0 0 1-56.32 19.768889A11.52 11.52 0 0 1 824.888889 853.333333v28.444445a28.444444 28.444444 0 0 1-56.888889 0v-28.444445l0.142222-2.133333a136.732444 136.732444 0 0 1-47.786666-17.777778c-24.746667-21.191111-38.4-50.631111-37.660445-81.237333h42.012445c0.853333 18.801778 8.988444 36.778667 23.04 50.801778 14.933333 9.386667 33.024 13.824 51.2 12.600889a104.647111 104.647111 0 0 0 51.256888-10.524445c11.605333-5.774222 18.773333-16.753778 18.688-28.615111-0.227556-15.36-10.154667-29.269333-25.6-35.925333a556.828444 556.828444 0 0 0-55.324444-18.318223 535.381333 535.381333 0 0 1-63.516444-22.869333c-20.878222-11.690667-33.109333-32.483556-32.028445-54.471111-1.137778-22.016 10.012444-43.093333 29.724445-56.32a126.321778 126.321778 0 0 1 45.966222-17.578667A15.217778 15.217778 0 0 1 768 568.888889v-28.444445a28.444444 28.444444 0 0 1 28.444444-28.444444z" fill="#0648A1" p-id="13542"></path></svg>
                </div>
            </div>
        </div>--%>

        <div class="card_container" id="playbrok_send">
            <div class="card_item" style="background: linear-gradient(45deg, #ADD2C4, #009FFF);">

                <div>佣金派发人数</div>
                <div class="number">
                    <span class="total_amount">0</span> / <span class="total_number">0</span>人  
                </div>
                <div class="today_card">
                    今日新增佣金<span class="today_data"><span class="today_amount">0</span> / <span class="today_number">0</span>人</span>
                </div>
                <div>
                    昨日新增佣金<span class="yesterday_data"><span class="yesterday_amount">0</span> / <span class="yesterday_number">0</span>人</span>
                </div>

                <div class="card_icon">
                    <svg t="" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="23676" width="66" height="66">
                        <path d="M936.4 706.5c-10.8-72.7-38-255.8-40.8-275.8-0.5-71-28.6-137.9-79.2-188.5-51.1-51.1-118.8-79.2-190.6-79.2H398.2c-71.8 0-139.5 28.1-190.6 79.2-50.6 50.6-78.7 117.4-79.2 188.4-2.9 20.1-30 203.1-40.8 275.8-6.8 33.3-0.3 67.3 18.4 95.9 18.8 28.9 47.6 48.7 81.2 55.8 69.3 14.7 137.6-29.6 152.6-98.7l22.7-88.5c4.1-16.1-5.6-32.4-21.6-36.5-16.1-4.1-32.4 5.6-36.5 21.6l-22.8 89-0.3 1.2c-7.9 37.1-44.5 60.9-81.6 53.1-17.9-3.8-33.3-14.4-43.3-29.9-10.1-15.5-13.5-33.8-9.7-51.7l0.3-1.8c41.6-280.1 41.6-280.2 41.6-283.3 0-115.7 94.1-209.8 209.8-209.8h227.7c115.7 0 209.8 94.1 209.8 209.8 0 3.1 0 3.4 41.6 283.3l0.3 1.8c3.8 17.9 0.3 36.2-9.7 51.7s-25.5 26.1-43.3 29.9c-17.9 3.8-36.2 0.3-51.7-9.7-15.5-10.1-26.1-25.5-29.9-43.3l-0.3-1.2-22.8-89c-2-7.8-7.1-14.5-14.1-18.6-7-4-15.4-5.1-23.1-2.9-18.4 5.2-37.5 7.8-56.7 7.8H435.4c-16.6 0-30 13.4-30 30s13.4 30 30 30h190.4c14.4 0 28.8-1.2 43-3.5l15.5 60.4c7.2 33.3 27 61.9 55.7 80.5 21.2 13.8 45.4 21 70.1 21 8.9 0 17.9-0.9 26.7-2.8 33.6-7.1 62.4-26.9 81.2-55.8 18.7-28.5 25.2-62.5 18.4-95.7z" fill="#e6e6e6" p-id="23677"></path><path d="M729.6 456.8c0-42.1-34.2-76.3-76.3-76.3S577 414.7 577 456.8c0 42.1 34.2 76.3 76.3 76.3s76.3-34.2 76.3-76.3z m-112.6 0c0-20 16.3-36.3 36.3-36.3s36.3 16.3 36.3 36.3c0 20-16.3 36.3-36.3 36.3S617 476.8 617 456.8zM422.4 479.1c11.1 0 20.2-9.1 20.2-20.2v-9.6c0-11.1-9.1-20.2-20.2-20.2h-39.3v-40.2c0-10.6-8.7-19.3-19.3-19.3h-11.5c-10.6 0-19.3 8.7-19.3 19.3v40.2h-39.3c-11.1 0-20.2 9.1-20.2 20.2v9.6c0 11.1 9.1 20.2 20.2 20.2H333v40.2c0 10.6 8.7 19.3 19.3 19.3h11.5c10.6 0 19.3-8.7 19.3-19.3v-40.2h39.3z" fill="#e6e6e6" p-id="23678"></path></svg>
                </div>
            </div>
        </div>

        <div class="card_container" id="playbrok_receive">
            <div class="card_item" style="background: linear-gradient(45deg, #ADD2C4, #009FFF);">

                <div>佣金领取人数</div>
                <div class="number">
                    <span class="total_amount">0</span> / <span class="total_number">0</span>人  
                </div>
                <div class="today_card">
                    今日新增佣金<span class="today_data"><span class="today_amount">0</span> / <span class="today_number">0</span>人</span>
                </div>
                <div>
                    昨日新增佣金<span class="yesterday_data"><span class="yesterday_amount">0</span> / <span class="yesterday_number">0</span>人</span>
                </div>

                <div class="card_icon">
                    <svg t="" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="23676" width="66" height="66">
                        <path d="M936.4 706.5c-10.8-72.7-38-255.8-40.8-275.8-0.5-71-28.6-137.9-79.2-188.5-51.1-51.1-118.8-79.2-190.6-79.2H398.2c-71.8 0-139.5 28.1-190.6 79.2-50.6 50.6-78.7 117.4-79.2 188.4-2.9 20.1-30 203.1-40.8 275.8-6.8 33.3-0.3 67.3 18.4 95.9 18.8 28.9 47.6 48.7 81.2 55.8 69.3 14.7 137.6-29.6 152.6-98.7l22.7-88.5c4.1-16.1-5.6-32.4-21.6-36.5-16.1-4.1-32.4 5.6-36.5 21.6l-22.8 89-0.3 1.2c-7.9 37.1-44.5 60.9-81.6 53.1-17.9-3.8-33.3-14.4-43.3-29.9-10.1-15.5-13.5-33.8-9.7-51.7l0.3-1.8c41.6-280.1 41.6-280.2 41.6-283.3 0-115.7 94.1-209.8 209.8-209.8h227.7c115.7 0 209.8 94.1 209.8 209.8 0 3.1 0 3.4 41.6 283.3l0.3 1.8c3.8 17.9 0.3 36.2-9.7 51.7s-25.5 26.1-43.3 29.9c-17.9 3.8-36.2 0.3-51.7-9.7-15.5-10.1-26.1-25.5-29.9-43.3l-0.3-1.2-22.8-89c-2-7.8-7.1-14.5-14.1-18.6-7-4-15.4-5.1-23.1-2.9-18.4 5.2-37.5 7.8-56.7 7.8H435.4c-16.6 0-30 13.4-30 30s13.4 30 30 30h190.4c14.4 0 28.8-1.2 43-3.5l15.5 60.4c7.2 33.3 27 61.9 55.7 80.5 21.2 13.8 45.4 21 70.1 21 8.9 0 17.9-0.9 26.7-2.8 33.6-7.1 62.4-26.9 81.2-55.8 18.7-28.5 25.2-62.5 18.4-95.7z" fill="#e6e6e6" p-id="23677"></path><path d="M729.6 456.8c0-42.1-34.2-76.3-76.3-76.3S577 414.7 577 456.8c0 42.1 34.2 76.3 76.3 76.3s76.3-34.2 76.3-76.3z m-112.6 0c0-20 16.3-36.3 36.3-36.3s36.3 16.3 36.3 36.3c0 20-16.3 36.3-36.3 36.3S617 476.8 617 456.8zM422.4 479.1c11.1 0 20.2-9.1 20.2-20.2v-9.6c0-11.1-9.1-20.2-20.2-20.2h-39.3v-40.2c0-10.6-8.7-19.3-19.3-19.3h-11.5c-10.6 0-19.3 8.7-19.3 19.3v40.2h-39.3c-11.1 0-20.2 9.1-20.2 20.2v9.6c0 11.1 9.1 20.2 20.2 20.2H333v40.2c0 10.6 8.7 19.3 19.3 19.3h11.5c10.6 0 19.3-8.7 19.3-19.3v-40.2h39.3z" fill="#e6e6e6" p-id="23678"></path></svg>
                </div>
            </div>
        </div>



        <%--<div class="card_container" id="gd_th_money">
            <div class="card_item" style="background: linear-gradient(45deg, #7b7be7, #219f8c);">

                <div>托号挂单总额</div>
                <div class="number">
                    <span class="total_amount">0</span> / <span class="total_number">0</span>人 / <span class="total_daily_number">0</span>笔  
                </div>
                <div class="today_card">
                    今日挂单总额<span class="today_data"><span class="today_amount">0</span> / <span class="today_number">0</span>人 / <span class="today_daily_number">0</span>笔</span>
                </div>
                <div>
                    昨日挂单总额<span class="yesterday_data"><span class="yesterday_amount">0</span> / <span class="yesterday_number">0</span>人 / <span class="yesterday_daily_number">0</span>笔</span>
                </div>

                <div class="card_icon">
                    <svg t="" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="11859" width="66" height="66">
                        <path d="M941.1584 438.1184l-396.288-338.2272c-20.4288-17.408-51.3536-17.408-71.7824 0L76.8 438.1184c-36.6592 31.2832-13.3632 88.9856 35.8912 88.9856h88.8832v316.0576c0 48.64 41.5232 88.064 92.7232 88.064h429.3632c51.2 0 92.7232-39.424 92.7232-88.064v-316.0576h88.8832c49.2544 0.0512 72.5504-57.7024 35.8912-88.9856z m-302.7456 231.8848c24.1664 0 43.776 18.6368 43.776 41.5744s-19.6096 41.5744-43.776 41.5744H556.032v41.0624c0 22.9888-19.6096 41.5744-43.776 41.5744-24.1664 0-43.776-18.6368-43.776-41.5744v-41.0624H386.048c-24.1664 0-43.776-18.6368-43.776-41.5744s19.6096-41.5744 43.776-41.5744h82.3808v-26.8288H386.048c-24.1664 0-43.776-18.6368-43.776-41.5744s19.6096-41.5744 43.776-41.5744h40.9088l-56.832-67.7376c-15.0528-17.9712-11.9808-44.1344 6.9632-58.4704 18.8928-14.336 46.4384-11.3664 61.4912 6.6048l72.192 86.0672 72.0384-89.2416c14.6944-18.2272 42.1888-21.6576 61.3888-7.68 19.2 13.9776 22.784 40.0896 8.0896 58.3168l-58.1632 72.0384h44.2368c24.1664 0 43.776 18.6368 43.776 41.5744s-19.6096 41.5744-43.776 41.5744h-82.3808v26.8288h82.432z" fill="#6C6CEA" p-id="11860"></path><path d="M544.8704 99.8912c-20.4288-17.408-51.3536-17.408-71.7824 0L76.8 438.1184c-36.6592 31.2832-13.3632 88.9856 35.8912 88.9856h88.8832v316.0576c0 14.5408 3.7376 28.16 10.2912 40.2432 37.888 8.192 77.1584 12.6464 117.4528 12.6464 210.944 0 394.1376-118.4256 487.0144-292.352v-76.5952H849.92c15.7696-44.8512 25.8048-92.3648 29.44-141.6704l-334.4896-285.5424z m93.5424 570.112c24.1664 0 43.776 18.6368 43.776 41.5744 0 22.9888-19.6096 41.5744-43.776 41.5744H556.032v41.0624c0 22.9888-19.6096 41.5744-43.776 41.5744-24.1664 0-43.776-18.6368-43.776-41.5744v-41.0624H386.048c-24.1664 0-43.776-18.6368-43.776-41.5744 0-22.9888 19.6096-41.5744 43.776-41.5744h82.3808v-26.8288H386.048c-24.1664 0-43.776-18.6368-43.776-41.5744s19.6096-41.5744 43.776-41.5744h40.9088l-56.832-67.7376c-15.0528-17.9712-11.9808-44.1344 6.9632-58.4704 18.8928-14.336 46.4384-11.3664 61.4912 6.6048l72.192 86.0672 72.0384-89.2416c14.6944-18.2272 42.1888-21.6576 61.3888-7.68 19.2 13.9776 22.784 40.0896 8.0896 58.3168l-58.1632 72.0384h44.2368c24.1664 0 43.776 18.6368 43.776 41.5744s-19.6096 41.5744-43.776 41.5744h-82.3808v26.8288h82.432z" fill="#757BF2" p-id="11861"></path><path d="M544.8704 99.8912c-20.4288-17.408-51.3536-17.408-71.7824 0L76.8 438.1184c-36.6592 31.2832-13.3632 88.9856 35.8912 88.9856h88.8832v172.4416c79.1552-3.2768 154.0096-23.1936 221.184-56.4224H386.048c-24.1664 0-43.776-18.6368-43.776-41.5744s19.6096-41.5744 43.776-41.5744h40.9088l-56.832-67.6864c-15.0528-17.9712-11.9808-44.1344 6.9632-58.4704 18.8928-14.336 46.4384-11.3664 61.4912 6.6048l72.192 86.0672 72.0384-89.2416c14.6944-18.2272 42.1888-21.6576 61.3888-7.68 2.2016 1.5872 4.1472 3.3792 5.9392 5.2224 33.9968-55.8592 58.2144-118.272 70.6048-184.8832l-175.872-150.016z" fill="#8486F8" p-id="11862"></path><path d="M473.088 99.8912L76.8 438.1184c-21.76 18.5856-22.272 46.4896-8.8064 66.048 234.8032-11.8784 430.848-170.5472 498.2784-386.048l-21.4016-18.2784c-20.3776-17.3568-51.3536-17.3568-71.7824 0.0512z" fill="#8D92F8" p-id="11863"></path></svg>
                </div>
            </div>
        </div>--%>


        <div class="card_container new_items" id="newuser_recharge_dating">
            <div class="card_item" style="background: linear-gradient(45deg, #F0AB22, #EEB320);">

                <div>新用户买币总额</div>
                <div class="number">
                    <span class="total_amount">0</span> / <span class="total_number">0</span>人       
                </div>
                <div class="today_card">
                    今日买币充值<span class="today_data"><span class="today_amount">0</span> / <span class="today_number">0</span>人</span>
                </div>
                <div>
                    昨日买币充值<span class="yesterday_data"><span class="yesterday_amount">0</span> / <span class="yesterday_number">0</span>人</span>
                </div>

                <div class="card_icon">
                    <svg t="" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="3814" width="66" height="66">
                        <path d="M517.418444 965.859231c-9.63279 0-19.437594-0.344028-29.070385-0.860071-59.00084-3.612296-115.593482-18.577524-168.2298-43.863598-54.700487-26.318159-103.036452-62.78515-143.631782-108.884932-40.59533-45.927768-70.869814-98.392071-90.135394-156.016798-18.577524-55.560558-26.146145-113.529313-22.533848-172.530153s18.577524-115.593482 43.863598-168.2298c26.318159-54.700487 62.78515-103.036452 108.884932-143.631782 45.927768-40.59533 98.392071-70.869814 156.016798-90.135394 46.271796-15.48127 94.607761-23.393919 143.459768-23.393919 9.63279 0 19.437594 0.344028 29.070385 0.860071 58.828826 3.612296 115.593482 18.577524 168.401814 43.863598 54.700487 26.318159 103.036452 62.78515 143.631782 108.884932 47.991937 54.356459 81.19066 118.345708 99.252142 189.903578l0 0.172014 0 0.172014c3.612296 11.180917 0.344028 23.393919-8.256677 31.134554-5.332437 4.644381-12.213002 7.396607-19.26558 7.396607-8.428691 0-16.341341-3.612296-22.017806-9.976818-1.720141-1.892155-2.752226-3.956325-3.612296-5.676466l-0.172014-0.344028-0.860071-1.032085-0.516042-2.236183c-14.793214-61.409037-44.379641-117.657652-88.071225-166.853687-69.665715-78.610449-165.649588-125.570301-270.062154-131.934823-8.256677-0.516042-16.685369-0.860071-24.942046-0.860071-47.647909 0-94.263733 8.428691-138.299345 25.11406-44.207626 16.685369-84.974971 41.283387-120.92592 72.933983-78.610449 69.321687-125.570301 165.133546-131.934823 269.890139-6.536536 104.584579 28.210314 205.728876 97.532001 284.339325 69.149672 78.610449 165.133546 125.570301 269.890139 132.106837 8.256677 0.516042 16.685369 0.860071 24.942046 0.860071 47.647909 0 94.263733-8.428691 138.299345-25.11406 44.207626-16.685369 84.974971-41.283387 120.92592-72.933983 62.78515-55.560558 105.44465-128.322526 123.506131-211.061314l0.172014-0.344028 0-0.344028c0.688056-6.70855 3.78431-12.901058 8.77272-17.889467 0.172014-0.172014 0.344028-0.344028 0.344028-0.344028 0.172014-0.172014 0.344028-0.344028 0.516042-0.516042 5.332437-4.644381 12.213002-7.224593 19.437594-7.224593 7.568621 0 14.793214 2.752226 20.125651 7.912649 5.504452 5.160423 8.77272 12.55703 8.944734 20.985721 0 0.860071-0.172014 1.720141-0.172014 2.408198-19.953637 98.392071-69.665715 184.743155-143.631782 249.764488-45.755753 40.423316-98.220057 70.697799-156.016798 90.135394C614.606417 957.946582 566.270452 965.859231 517.418444 965.859231zM470.630606 729.339829 470.630606 485.939862l-3.612296 1.032085c-21.501764 6.192508-40.939358 12.213002-59.172854 18.233496-7.912649 2.580212-18.233496 5.332437-29.070385 8.084663-14.965228 3.956325-30.618512 7.912649-40.423316 12.040988l-1.720141 0.688056 0 127.806484-70.525785 0 0-199.536368 70.353771 0 0 29.414413 3.78431-1.376113c5.84848-2.236183 16.513355-4.644381 27.866286-7.224593 11.69696-2.752226 24.942046-5.84848 35.090879-9.116748 19.437594-6.192508 41.455401-12.213002 65.365362-17.373425l2.236183-0.516042 0-51.088191-2.92424 0.172014c-38.531161 2.064169-74.654124 3.268268-106.992777 3.440282-16.685369 0.172014-32.510667 0.860071-46.271796 1.376113-11.69696 0.516042-22.877877 1.032085-32.854695 1.032085l-12.901058-50.572148c17.029397 0.172014 34.402822 1.376113 51.260205 2.408198 17.717453 1.204099 35.950949 2.408198 53.15236 2.408198 5.676466 0 10.836889-0.172014 15.997312-0.344028 38.359147-2.064169 77.062322-5.504452 115.07744-10.492861 38.015118-4.988409 75.858223-11.352931 112.325214-19.26558 35.778935-7.396607 72.761969-16.857383 110.261045-27.694272 6.020494 14.793214 10.836889 26.146145 14.965228 36.466991 0.860071 2.064169 1.720141 4.128339 2.408198 6.020494 1.204099 2.752226 2.236183 5.504452 3.440282 8.084663 1.548127 3.78431 3.096254 7.396607 4.472367 10.492861l0.688056 1.720141c1.032085 2.92424 2.408198 6.020494 3.956325 9.116748-32.682681 5.332437-66.569461 9.804804-100.456241 13.245087-24.942046 2.580212-56.764656 4.816395-84.802956 6.880564-9.804804 0.688056-18.921552 1.376113-27.006215 2.064169l-2.580212 0.172014 0 35.950949 3.096254-0.344028c11.008903-1.032085 25.630102-1.548127 41.283387-1.548127 15.653284 0 30.446498 0.516042 41.627415 1.548127 31.82261 2.752226 57.452713 8.428691 76.202251 17.029397 18.749538 8.428691 33.198723 17.889467 43.003528 27.866286 9.804804 10.148833 16.169326 20.469679 19.26558 30.618512 3.440282 10.836889 5.332437 24.081975 6.020494 30.274483 0.344028 8.77272 0.860071 18.921552 1.204099 30.274483 0.344028 11.008903 0 22.361834-1.204099 33.714766-1.204099 11.352931-3.440282 22.361834-6.70855 32.682681-3.268268 10.492861-7.912649 19.26558-13.761129 26.490173-6.192508 7.224593-13.933143 12.55703-23.909961 16.169326-5.84848 2.064169-12.901058 3.096254-20.469679 3.096254-5.332437 0-11.008903-0.516042-17.029397-1.376113-13.589115-2.064169-25.630102-4.644381-36.122963-7.912649-11.352931-3.440282-20.469679-6.70855-27.694272-9.804804-8.600706-3.612296-15.653284-6.880564-21.32975-9.976818l3.268268-31.650596c13.073072 6.364522 24.942046 10.492861 35.090879 11.868974 2.580212 0.344028 5.160423 0.516042 7.740635 0.516042 2.408198 0 4.988409-0.172014 7.396607-0.688056 4.644381-0.860071 9.116748-2.752226 13.589115-5.676466 4.300353-2.92424 7.912649-7.396607 10.664875-13.417101 2.92424-6.020494 4.644381-13.417101 5.504452-22.18982 2.064169-23.393919 2.580212-41.455401 1.720141-53.324374-0.344028-6.536536-0.860071-8.600706-1.204099-9.63279 0-3.440282-0.344028-7.740635-0.860071-12.901058-0.516042-5.160423-2.064169-9.804804-4.300353-13.761129-4.300353-7.396607-12.385016-14.277171-24.598018-20.813707-9.976818-5.332437-23.909961-7.912649-40.939358-7.912649-3.440282 0-6.880564 0.172014-10.492861 0.344028l-0.172014 0c-7.912649 0.860071-15.997312 1.720141-23.565933 2.408198-2.92424 0.344028-7.568621 0.688056-13.073072 1.032085-9.804804 0.860071-20.985721 1.548127-26.834201 2.752226l-2.236183 0.344028 0 252.860742-70.869814 0L470.630606 729.339829 470.630606 729.339829 470.630606 729.339829z" fill="#575B66" p-id="3815"></path></svg>
                </div>
            </div>
        </div>


        <div class="card_container new_items" id="newuser_cash_money">
            <div class="card_item" style="background: linear-gradient(45deg, #9492d1, #5ec7b8);">

                <div>新用户提现总额</div>
                <div class="number">
                    <span class="total_amount">0</span> / <span class="total_number">0</span>人 / <span class="total_daily_number">0</span>笔  
                </div>
                <div class="today_card">
                    今日提现总额<span class="today_data"><span class="today_amount">0</span> / <a style="color: #fff; text-decoration: none;"><span class="today_number">0</span>人</a> / <span class="today_daily_number">0</span>笔</span>
                </div>
                <div>
                    昨日提现总额<span class="yesterday_data"><span class="yesterday_amount">0</span> / <a style="color: #fff; text-decoration: none;"><span class="yesterday_number">0</span>人</a> / <span class="yesterday_daily_number">0</span>笔</span>
                </div>

                <div class="card_icon">
                    <svg t="" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="11859" width="66" height="66">
                        <path d="M941.1584 438.1184l-396.288-338.2272c-20.4288-17.408-51.3536-17.408-71.7824 0L76.8 438.1184c-36.6592 31.2832-13.3632 88.9856 35.8912 88.9856h88.8832v316.0576c0 48.64 41.5232 88.064 92.7232 88.064h429.3632c51.2 0 92.7232-39.424 92.7232-88.064v-316.0576h88.8832c49.2544 0.0512 72.5504-57.7024 35.8912-88.9856z m-302.7456 231.8848c24.1664 0 43.776 18.6368 43.776 41.5744s-19.6096 41.5744-43.776 41.5744H556.032v41.0624c0 22.9888-19.6096 41.5744-43.776 41.5744-24.1664 0-43.776-18.6368-43.776-41.5744v-41.0624H386.048c-24.1664 0-43.776-18.6368-43.776-41.5744s19.6096-41.5744 43.776-41.5744h82.3808v-26.8288H386.048c-24.1664 0-43.776-18.6368-43.776-41.5744s19.6096-41.5744 43.776-41.5744h40.9088l-56.832-67.7376c-15.0528-17.9712-11.9808-44.1344 6.9632-58.4704 18.8928-14.336 46.4384-11.3664 61.4912 6.6048l72.192 86.0672 72.0384-89.2416c14.6944-18.2272 42.1888-21.6576 61.3888-7.68 19.2 13.9776 22.784 40.0896 8.0896 58.3168l-58.1632 72.0384h44.2368c24.1664 0 43.776 18.6368 43.776 41.5744s-19.6096 41.5744-43.776 41.5744h-82.3808v26.8288h82.432z" fill="#6C6CEA" p-id="11860"></path><path d="M544.8704 99.8912c-20.4288-17.408-51.3536-17.408-71.7824 0L76.8 438.1184c-36.6592 31.2832-13.3632 88.9856 35.8912 88.9856h88.8832v316.0576c0 14.5408 3.7376 28.16 10.2912 40.2432 37.888 8.192 77.1584 12.6464 117.4528 12.6464 210.944 0 394.1376-118.4256 487.0144-292.352v-76.5952H849.92c15.7696-44.8512 25.8048-92.3648 29.44-141.6704l-334.4896-285.5424z m93.5424 570.112c24.1664 0 43.776 18.6368 43.776 41.5744 0 22.9888-19.6096 41.5744-43.776 41.5744H556.032v41.0624c0 22.9888-19.6096 41.5744-43.776 41.5744-24.1664 0-43.776-18.6368-43.776-41.5744v-41.0624H386.048c-24.1664 0-43.776-18.6368-43.776-41.5744 0-22.9888 19.6096-41.5744 43.776-41.5744h82.3808v-26.8288H386.048c-24.1664 0-43.776-18.6368-43.776-41.5744s19.6096-41.5744 43.776-41.5744h40.9088l-56.832-67.7376c-15.0528-17.9712-11.9808-44.1344 6.9632-58.4704 18.8928-14.336 46.4384-11.3664 61.4912 6.6048l72.192 86.0672 72.0384-89.2416c14.6944-18.2272 42.1888-21.6576 61.3888-7.68 19.2 13.9776 22.784 40.0896 8.0896 58.3168l-58.1632 72.0384h44.2368c24.1664 0 43.776 18.6368 43.776 41.5744s-19.6096 41.5744-43.776 41.5744h-82.3808v26.8288h82.432z" fill="#757BF2" p-id="11861"></path><path d="M544.8704 99.8912c-20.4288-17.408-51.3536-17.408-71.7824 0L76.8 438.1184c-36.6592 31.2832-13.3632 88.9856 35.8912 88.9856h88.8832v172.4416c79.1552-3.2768 154.0096-23.1936 221.184-56.4224H386.048c-24.1664 0-43.776-18.6368-43.776-41.5744s19.6096-41.5744 43.776-41.5744h40.9088l-56.832-67.6864c-15.0528-17.9712-11.9808-44.1344 6.9632-58.4704 18.8928-14.336 46.4384-11.3664 61.4912 6.6048l72.192 86.0672 72.0384-89.2416c14.6944-18.2272 42.1888-21.6576 61.3888-7.68 2.2016 1.5872 4.1472 3.3792 5.9392 5.2224 33.9968-55.8592 58.2144-118.272 70.6048-184.8832l-175.872-150.016z" fill="#8486F8" p-id="11862"></path><path d="M473.088 99.8912L76.8 438.1184c-21.76 18.5856-22.272 46.4896-8.8064 66.048 234.8032-11.8784 430.848-170.5472 498.2784-386.048l-21.4016-18.2784c-20.3776-17.3568-51.3536-17.3568-71.7824 0.0512z" fill="#8D92F8" p-id="11863"></path></svg>
                </div>
            </div>
        </div>



        <%--<div class="card_container" id="th_cash_money">
            <div class="card_item" style="background: linear-gradient(45deg, #9492d1, #5ec7b8);">

                <div>托号提现总额</div>
                <div class="number">
                    <span class="total_amount">0</span> / <span class="total_number">0</span>人 / <span class="total_daily_number">0</span>笔  
                </div>
                <div class="today_card">
                    今日提现总额<span class="today_data"><span class="today_amount">0</span> / <span class="today_number">0</span>人 / <span class="today_daily_number">0</span>笔</span>
                </div>
                <div>
                    昨日提现总额<span class="yesterday_data"><span class="yesterday_amount">0</span> / <span class="yesterday_number">0</span>人 / <span class="yesterday_daily_number">0</span>笔</span>
                </div>

                <div class="card_icon">
                    <svg t="" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="11859" width="66" height="66">
                        <path d="M941.1584 438.1184l-396.288-338.2272c-20.4288-17.408-51.3536-17.408-71.7824 0L76.8 438.1184c-36.6592 31.2832-13.3632 88.9856 35.8912 88.9856h88.8832v316.0576c0 48.64 41.5232 88.064 92.7232 88.064h429.3632c51.2 0 92.7232-39.424 92.7232-88.064v-316.0576h88.8832c49.2544 0.0512 72.5504-57.7024 35.8912-88.9856z m-302.7456 231.8848c24.1664 0 43.776 18.6368 43.776 41.5744s-19.6096 41.5744-43.776 41.5744H556.032v41.0624c0 22.9888-19.6096 41.5744-43.776 41.5744-24.1664 0-43.776-18.6368-43.776-41.5744v-41.0624H386.048c-24.1664 0-43.776-18.6368-43.776-41.5744s19.6096-41.5744 43.776-41.5744h82.3808v-26.8288H386.048c-24.1664 0-43.776-18.6368-43.776-41.5744s19.6096-41.5744 43.776-41.5744h40.9088l-56.832-67.7376c-15.0528-17.9712-11.9808-44.1344 6.9632-58.4704 18.8928-14.336 46.4384-11.3664 61.4912 6.6048l72.192 86.0672 72.0384-89.2416c14.6944-18.2272 42.1888-21.6576 61.3888-7.68 19.2 13.9776 22.784 40.0896 8.0896 58.3168l-58.1632 72.0384h44.2368c24.1664 0 43.776 18.6368 43.776 41.5744s-19.6096 41.5744-43.776 41.5744h-82.3808v26.8288h82.432z" fill="#6C6CEA" p-id="11860"></path><path d="M544.8704 99.8912c-20.4288-17.408-51.3536-17.408-71.7824 0L76.8 438.1184c-36.6592 31.2832-13.3632 88.9856 35.8912 88.9856h88.8832v316.0576c0 14.5408 3.7376 28.16 10.2912 40.2432 37.888 8.192 77.1584 12.6464 117.4528 12.6464 210.944 0 394.1376-118.4256 487.0144-292.352v-76.5952H849.92c15.7696-44.8512 25.8048-92.3648 29.44-141.6704l-334.4896-285.5424z m93.5424 570.112c24.1664 0 43.776 18.6368 43.776 41.5744 0 22.9888-19.6096 41.5744-43.776 41.5744H556.032v41.0624c0 22.9888-19.6096 41.5744-43.776 41.5744-24.1664 0-43.776-18.6368-43.776-41.5744v-41.0624H386.048c-24.1664 0-43.776-18.6368-43.776-41.5744 0-22.9888 19.6096-41.5744 43.776-41.5744h82.3808v-26.8288H386.048c-24.1664 0-43.776-18.6368-43.776-41.5744s19.6096-41.5744 43.776-41.5744h40.9088l-56.832-67.7376c-15.0528-17.9712-11.9808-44.1344 6.9632-58.4704 18.8928-14.336 46.4384-11.3664 61.4912 6.6048l72.192 86.0672 72.0384-89.2416c14.6944-18.2272 42.1888-21.6576 61.3888-7.68 19.2 13.9776 22.784 40.0896 8.0896 58.3168l-58.1632 72.0384h44.2368c24.1664 0 43.776 18.6368 43.776 41.5744s-19.6096 41.5744-43.776 41.5744h-82.3808v26.8288h82.432z" fill="#757BF2" p-id="11861"></path><path d="M544.8704 99.8912c-20.4288-17.408-51.3536-17.408-71.7824 0L76.8 438.1184c-36.6592 31.2832-13.3632 88.9856 35.8912 88.9856h88.8832v172.4416c79.1552-3.2768 154.0096-23.1936 221.184-56.4224H386.048c-24.1664 0-43.776-18.6368-43.776-41.5744s19.6096-41.5744 43.776-41.5744h40.9088l-56.832-67.6864c-15.0528-17.9712-11.9808-44.1344 6.9632-58.4704 18.8928-14.336 46.4384-11.3664 61.4912 6.6048l72.192 86.0672 72.0384-89.2416c14.6944-18.2272 42.1888-21.6576 61.3888-7.68 2.2016 1.5872 4.1472 3.3792 5.9392 5.2224 33.9968-55.8592 58.2144-118.272 70.6048-184.8832l-175.872-150.016z" fill="#8486F8" p-id="11862"></path><path d="M473.088 99.8912L76.8 438.1184c-21.76 18.5856-22.272 46.4896-8.8064 66.048 234.8032-11.8784 430.848-170.5472 498.2784-386.048l-21.4016-18.2784c-20.3776-17.3568-51.3536-17.3568-71.7824 0.0512z" fill="#8D92F8" p-id="11863"></path></svg>
                </div>
            </div>
        </div>--%>

        <%--<div class="card_container new_items" id="gd_newuser_money">
            <div class="card_item" style="background: linear-gradient(45deg, #444365, #2b5952);">

                <div>新用户挂单总额</div>
                <div class="number">
                    <span class="total_amount">0</span> / <span class="total_number">0</span>人 / <span class="total_daily_number">0</span>笔  
                </div>
                <div class="today_card">
                    今日挂单总额<span class="today_data"><span class="today_amount">0</span> / <span class="today_number">0</span>人 / <span class="today_daily_number">0</span>笔</span>
                </div>
                <div>
                    昨日挂单总额<span class="yesterday_data"><span class="yesterday_amount">0</span> / <span class="yesterday_number">0</span>人 / <span class="yesterday_daily_number">0</span>笔</span>
                </div>

                <div class="card_icon">
                    <svg t="" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="11859" width="66" height="66">
                        <path d="M941.1584 438.1184l-396.288-338.2272c-20.4288-17.408-51.3536-17.408-71.7824 0L76.8 438.1184c-36.6592 31.2832-13.3632 88.9856 35.8912 88.9856h88.8832v316.0576c0 48.64 41.5232 88.064 92.7232 88.064h429.3632c51.2 0 92.7232-39.424 92.7232-88.064v-316.0576h88.8832c49.2544 0.0512 72.5504-57.7024 35.8912-88.9856z m-302.7456 231.8848c24.1664 0 43.776 18.6368 43.776 41.5744s-19.6096 41.5744-43.776 41.5744H556.032v41.0624c0 22.9888-19.6096 41.5744-43.776 41.5744-24.1664 0-43.776-18.6368-43.776-41.5744v-41.0624H386.048c-24.1664 0-43.776-18.6368-43.776-41.5744s19.6096-41.5744 43.776-41.5744h82.3808v-26.8288H386.048c-24.1664 0-43.776-18.6368-43.776-41.5744s19.6096-41.5744 43.776-41.5744h40.9088l-56.832-67.7376c-15.0528-17.9712-11.9808-44.1344 6.9632-58.4704 18.8928-14.336 46.4384-11.3664 61.4912 6.6048l72.192 86.0672 72.0384-89.2416c14.6944-18.2272 42.1888-21.6576 61.3888-7.68 19.2 13.9776 22.784 40.0896 8.0896 58.3168l-58.1632 72.0384h44.2368c24.1664 0 43.776 18.6368 43.776 41.5744s-19.6096 41.5744-43.776 41.5744h-82.3808v26.8288h82.432z" fill="#6C6CEA" p-id="11860"></path><path d="M544.8704 99.8912c-20.4288-17.408-51.3536-17.408-71.7824 0L76.8 438.1184c-36.6592 31.2832-13.3632 88.9856 35.8912 88.9856h88.8832v316.0576c0 14.5408 3.7376 28.16 10.2912 40.2432 37.888 8.192 77.1584 12.6464 117.4528 12.6464 210.944 0 394.1376-118.4256 487.0144-292.352v-76.5952H849.92c15.7696-44.8512 25.8048-92.3648 29.44-141.6704l-334.4896-285.5424z m93.5424 570.112c24.1664 0 43.776 18.6368 43.776 41.5744 0 22.9888-19.6096 41.5744-43.776 41.5744H556.032v41.0624c0 22.9888-19.6096 41.5744-43.776 41.5744-24.1664 0-43.776-18.6368-43.776-41.5744v-41.0624H386.048c-24.1664 0-43.776-18.6368-43.776-41.5744 0-22.9888 19.6096-41.5744 43.776-41.5744h82.3808v-26.8288H386.048c-24.1664 0-43.776-18.6368-43.776-41.5744s19.6096-41.5744 43.776-41.5744h40.9088l-56.832-67.7376c-15.0528-17.9712-11.9808-44.1344 6.9632-58.4704 18.8928-14.336 46.4384-11.3664 61.4912 6.6048l72.192 86.0672 72.0384-89.2416c14.6944-18.2272 42.1888-21.6576 61.3888-7.68 19.2 13.9776 22.784 40.0896 8.0896 58.3168l-58.1632 72.0384h44.2368c24.1664 0 43.776 18.6368 43.776 41.5744s-19.6096 41.5744-43.776 41.5744h-82.3808v26.8288h82.432z" fill="#757BF2" p-id="11861"></path><path d="M544.8704 99.8912c-20.4288-17.408-51.3536-17.408-71.7824 0L76.8 438.1184c-36.6592 31.2832-13.3632 88.9856 35.8912 88.9856h88.8832v172.4416c79.1552-3.2768 154.0096-23.1936 221.184-56.4224H386.048c-24.1664 0-43.776-18.6368-43.776-41.5744s19.6096-41.5744 43.776-41.5744h40.9088l-56.832-67.6864c-15.0528-17.9712-11.9808-44.1344 6.9632-58.4704 18.8928-14.336 46.4384-11.3664 61.4912 6.6048l72.192 86.0672 72.0384-89.2416c14.6944-18.2272 42.1888-21.6576 61.3888-7.68 2.2016 1.5872 4.1472 3.3792 5.9392 5.2224 33.9968-55.8592 58.2144-118.272 70.6048-184.8832l-175.872-150.016z" fill="#8486F8" p-id="11862"></path><path d="M473.088 99.8912L76.8 438.1184c-21.76 18.5856-22.272 46.4896-8.8064 66.048 234.8032-11.8784 430.848-170.5472 498.2784-386.048l-21.4016-18.2784c-20.3776-17.3568-51.3536-17.3568-71.7824 0.0512z" fill="#8D92F8" p-id="11863"></path></svg>
                </div>
            </div>
        </div>--%>

        <div class="card_container new_items" id="newuser_yongjing_qd">
            <div class="card_item" style="background: linear-gradient(45deg, #8B0B24, #952E62);">

                <div>新用户佣金</div>
                <div class="number">
                    <span class="total_amount">0</span> / <span class="total_number">0</span>人  
                </div>
                <div class="today_card">
                    今日新增佣金<span class="today_data"><span class="today_amount">0</span> / <span class="today_number">0</span>人</span>
                </div>
                <div>
                    昨日新增佣金<span class="yesterday_data"><span class="yesterday_amount">0</span> / <span class="yesterday_number">0</span>人</span>
                </div>

                <div class="card_icon">
                    <svg t="" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="13913" width="66" height="66">
                        <path d="M943.104 469.504c-22.528 0-40.96 18.432-40.96 40.96 0 214.528-174.592 389.12-389.12 389.12s-389.12-174.592-389.12-389.12 174.592-389.12 389.12-389.12c129.024 0 249.344 64 322.048 170.496 12.8 18.944 38.4 23.552 56.832 10.752 18.944-12.8 23.552-38.4 10.752-56.832-88.064-129.536-233.472-206.336-389.632-206.336-259.584 0-471.04 211.456-471.04 471.04s211.456 471.04 471.04 471.04 471.04-211.456 471.04-471.04c0-22.528-18.432-40.96-40.96-40.96z" fill="#333333" p-id="13914"></path><path d="M681.472 493.568c22.528 0 40.96-18.432 40.96-40.96s-18.432-40.96-40.96-40.96h-69.632l95.232-95.232c15.872-15.872 15.872-41.984 0-57.856-15.872-15.872-41.984-15.872-57.856 0l-136.192 136.192-136.192-136.704c-15.872-15.872-41.984-15.872-57.856 0-15.872 15.872-15.872 41.984 0 57.856l95.232 95.232H344.576c-22.528 0-40.96 18.432-40.96 40.96s18.432 40.96 40.96 40.96h127.488v44.544H344.576c-22.528 0-40.96 18.432-40.96 40.96s18.432 40.96 40.96 40.96h127.488v114.176c0 22.528 18.432 40.96 40.96 40.96s40.96-18.432 40.96-40.96V619.52h127.488c22.528 0 40.96-18.432 40.96-40.96s-18.432-40.96-40.96-40.96h-127.488v-44.544h127.488z" fill="#333333" p-id="13915"></path></svg>
                </div>
            </div>
        </div>



<%--        <div class="card_container new_items" id="newuser_yongjing_yj">
            <div class="card_item" style="background: linear-gradient(45deg, #ADD2C4, #E77977);">

                <div>新用户一级</div>
                <div class="number">
                    <span class="total_amount">0</span> / <span class="total_number">0</span>人  
                </div>
                <div class="today_card">
                    今日新增佣金<span class="today_data"><span class="today_amount">0</span> / <span class="today_number">0</span>人</span>
                </div>
                <div>
                    昨日新增佣金<span class="yesterday_data"><span class="yesterday_amount">0</span> / <span class="yesterday_number">0</span>人</span>
                </div>

                <div class="card_icon">
                    <svg t="" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="13539" width="66" height="66">
                        <path d="M484.664889 113.777778a227.555556 227.555556 0 0 1 125.326222 417.507555c7.623111 2.474667 14.734222 5.233778 21.134222 8.248889 13.710222 6.456889 22.926222 21.134222 17.92 36.039111-5.006222 14.904889-23.125333 25.116444-36.010666 17.92-31.800889-17.806222-93.212444-24.519111-127.658667-24.604444H483.896889h0.768a313.031111 313.031111 0 0 0-307.768889 256h440.092444c20.565333 0 37.233778 12.743111 37.233778 28.444444 0 14.592-14.364444 26.624-32.881778 28.245334l-4.352 0.199111-459.804444 0.028444a28.444444 28.444444 0 0 1-39.936-16.440889A23.210667 23.210667 0 0 1 113.777778 853.333333c0-4.181333 1.166222-8.135111 3.271111-11.690666l0.568889-4.892445c17.180444-141.852444 114.375111-258.104889 244.252444-303.843555A227.555556 227.555556 0 0 1 484.664889 113.777778z m0 56.888889a170.666667 170.666667 0 1 0 0 341.333333h-0.711111 1.365333a170.666667 170.666667 0 0 0 170.012445-170.666667 170.666667 170.666667 0 0 0-170.666667-170.666666z" fill="#0648A1" p-id="13540"></path><path d="M556.202667 566.044444l-57.144889 45.511112 45.511111 128.483555-56.32 47.502222-60.16-47.502222 47.559111-125.724444L411.591111 566.044444h144.64z" fill="#0648A1" p-id="13541"></path><path d="M796.444444 512a28.444444 28.444444 0 0 1 28.444445 28.444444v28.444445l-0.113778 1.934222c16.64 2.929778 32.426667 9.187556 46.165333 18.432 21.020444 19.057778 32.881778 44.657778 33.052445 71.395556h-42.012445a76.231111 76.231111 0 0 0-21.504-43.946667 80.668444 80.668444 0 0 0-47.388444-11.662222 87.381333 87.381333 0 0 0-42.012444 7.992889c-11.946667 5.973333-18.944 17.578667-17.92 29.752889 0.113778 12.373333 8.049778 23.608889 20.48 29.070222 15.473778 6.826667 31.573333 12.430222 48.156444 16.696889 25.656889 6.599111 50.460444 15.559111 74.012444 26.794666 22.584889 12.970667 35.669333 35.754667 34.332445 59.733334 1.024 22.129778-9.699556 43.434667-28.956445 57.429333a137.244444 137.244444 0 0 1-56.32 19.768889A11.52 11.52 0 0 1 824.888889 853.333333v28.444445a28.444444 28.444444 0 0 1-56.888889 0v-28.444445l0.142222-2.133333a136.732444 136.732444 0 0 1-47.786666-17.777778c-24.746667-21.191111-38.4-50.631111-37.660445-81.237333h42.012445c0.853333 18.801778 8.988444 36.778667 23.04 50.801778 14.933333 9.386667 33.024 13.824 51.2 12.600889a104.647111 104.647111 0 0 0 51.256888-10.524445c11.605333-5.774222 18.773333-16.753778 18.688-28.615111-0.227556-15.36-10.154667-29.269333-25.6-35.925333a556.828444 556.828444 0 0 0-55.324444-18.318223 535.381333 535.381333 0 0 1-63.516444-22.869333c-20.878222-11.690667-33.109333-32.483556-32.028445-54.471111-1.137778-22.016 10.012444-43.093333 29.724445-56.32a126.321778 126.321778 0 0 1 45.966222-17.578667A15.217778 15.217778 0 0 1 768 568.888889v-28.444445a28.444444 28.444444 0 0 1 28.444444-28.444444z" fill="#0648A1" p-id="13542"></path></svg>
                </div>
            </div>
        </div>


        <div class="card_container new_items" id="newuser_yongjing_ej">
            <div class="card_item" style="background: linear-gradient(45deg, #ADD2C4, #E77977);">

                <div>新用户二级</div>
                <div class="number">
                    <span class="total_amount">0</span> / <span class="total_number">0</span>人   
                </div>
                <div class="today_card">
                    今日新增佣金<span class="today_data"><span class="today_amount">0</span> / <span class="today_number">0</span>人</span>
                </div>
                <div>
                    昨日新增佣金<span class="yesterday_data"><span class="yesterday_amount">0</span> / <span class="yesterday_number">0</span>人</span>
                </div>

                <div class="card_icon">
                    <svg t="" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="13539" width="66" height="66">
                        <path d="M484.664889 113.777778a227.555556 227.555556 0 0 1 125.326222 417.507555c7.623111 2.474667 14.734222 5.233778 21.134222 8.248889 13.710222 6.456889 22.926222 21.134222 17.92 36.039111-5.006222 14.904889-23.125333 25.116444-36.010666 17.92-31.800889-17.806222-93.212444-24.519111-127.658667-24.604444H483.896889h0.768a313.031111 313.031111 0 0 0-307.768889 256h440.092444c20.565333 0 37.233778 12.743111 37.233778 28.444444 0 14.592-14.364444 26.624-32.881778 28.245334l-4.352 0.199111-459.804444 0.028444a28.444444 28.444444 0 0 1-39.936-16.440889A23.210667 23.210667 0 0 1 113.777778 853.333333c0-4.181333 1.166222-8.135111 3.271111-11.690666l0.568889-4.892445c17.180444-141.852444 114.375111-258.104889 244.252444-303.843555A227.555556 227.555556 0 0 1 484.664889 113.777778z m0 56.888889a170.666667 170.666667 0 1 0 0 341.333333h-0.711111 1.365333a170.666667 170.666667 0 0 0 170.012445-170.666667 170.666667 170.666667 0 0 0-170.666667-170.666666z" fill="#0648A1" p-id="13540"></path><path d="M556.202667 566.044444l-57.144889 45.511112 45.511111 128.483555-56.32 47.502222-60.16-47.502222 47.559111-125.724444L411.591111 566.044444h144.64z" fill="#0648A1" p-id="13541"></path><path d="M796.444444 512a28.444444 28.444444 0 0 1 28.444445 28.444444v28.444445l-0.113778 1.934222c16.64 2.929778 32.426667 9.187556 46.165333 18.432 21.020444 19.057778 32.881778 44.657778 33.052445 71.395556h-42.012445a76.231111 76.231111 0 0 0-21.504-43.946667 80.668444 80.668444 0 0 0-47.388444-11.662222 87.381333 87.381333 0 0 0-42.012444 7.992889c-11.946667 5.973333-18.944 17.578667-17.92 29.752889 0.113778 12.373333 8.049778 23.608889 20.48 29.070222 15.473778 6.826667 31.573333 12.430222 48.156444 16.696889 25.656889 6.599111 50.460444 15.559111 74.012444 26.794666 22.584889 12.970667 35.669333 35.754667 34.332445 59.733334 1.024 22.129778-9.699556 43.434667-28.956445 57.429333a137.244444 137.244444 0 0 1-56.32 19.768889A11.52 11.52 0 0 1 824.888889 853.333333v28.444445a28.444444 28.444444 0 0 1-56.888889 0v-28.444445l0.142222-2.133333a136.732444 136.732444 0 0 1-47.786666-17.777778c-24.746667-21.191111-38.4-50.631111-37.660445-81.237333h42.012445c0.853333 18.801778 8.988444 36.778667 23.04 50.801778 14.933333 9.386667 33.024 13.824 51.2 12.600889a104.647111 104.647111 0 0 0 51.256888-10.524445c11.605333-5.774222 18.773333-16.753778 18.688-28.615111-0.227556-15.36-10.154667-29.269333-25.6-35.925333a556.828444 556.828444 0 0 0-55.324444-18.318223 535.381333 535.381333 0 0 1-63.516444-22.869333c-20.878222-11.690667-33.109333-32.483556-32.028445-54.471111-1.137778-22.016 10.012444-43.093333 29.724445-56.32a126.321778 126.321778 0 0 1 45.966222-17.578667A15.217778 15.217778 0 0 1 768 568.888889v-28.444445a28.444444 28.444444 0 0 1 28.444444-28.444444z" fill="#0648A1" p-id="13542"></path></svg>
                </div>
            </div>
        </div>


        <div class="card_container new_items" id="newuser_yongjing_sj">
            <div class="card_item" style="background: linear-gradient(45deg, #ADD2C4, #E77977);">

                <div>新用户三级</div>
                <div class="number">
                    <span class="total_amount">0</span> / <span class="total_number">0</span>人  
                </div>
                <div class="today_card">
                    今日新增佣金<span class="today_data"><span class="today_amount">0</span> / <span class="today_number">0</span>人</span>
                </div>
                <div>
                    昨日新增佣金<span class="yesterday_data"><span class="yesterday_amount">0</span> / <span class="yesterday_number">0</span>人</span>
                </div>

                <div class="card_icon">
                    <svg t="" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="13539" width="66" height="66">
                        <path d="M484.664889 113.777778a227.555556 227.555556 0 0 1 125.326222 417.507555c7.623111 2.474667 14.734222 5.233778 21.134222 8.248889 13.710222 6.456889 22.926222 21.134222 17.92 36.039111-5.006222 14.904889-23.125333 25.116444-36.010666 17.92-31.800889-17.806222-93.212444-24.519111-127.658667-24.604444H483.896889h0.768a313.031111 313.031111 0 0 0-307.768889 256h440.092444c20.565333 0 37.233778 12.743111 37.233778 28.444444 0 14.592-14.364444 26.624-32.881778 28.245334l-4.352 0.199111-459.804444 0.028444a28.444444 28.444444 0 0 1-39.936-16.440889A23.210667 23.210667 0 0 1 113.777778 853.333333c0-4.181333 1.166222-8.135111 3.271111-11.690666l0.568889-4.892445c17.180444-141.852444 114.375111-258.104889 244.252444-303.843555A227.555556 227.555556 0 0 1 484.664889 113.777778z m0 56.888889a170.666667 170.666667 0 1 0 0 341.333333h-0.711111 1.365333a170.666667 170.666667 0 0 0 170.012445-170.666667 170.666667 170.666667 0 0 0-170.666667-170.666666z" fill="#0648A1" p-id="13540"></path><path d="M556.202667 566.044444l-57.144889 45.511112 45.511111 128.483555-56.32 47.502222-60.16-47.502222 47.559111-125.724444L411.591111 566.044444h144.64z" fill="#0648A1" p-id="13541"></path><path d="M796.444444 512a28.444444 28.444444 0 0 1 28.444445 28.444444v28.444445l-0.113778 1.934222c16.64 2.929778 32.426667 9.187556 46.165333 18.432 21.020444 19.057778 32.881778 44.657778 33.052445 71.395556h-42.012445a76.231111 76.231111 0 0 0-21.504-43.946667 80.668444 80.668444 0 0 0-47.388444-11.662222 87.381333 87.381333 0 0 0-42.012444 7.992889c-11.946667 5.973333-18.944 17.578667-17.92 29.752889 0.113778 12.373333 8.049778 23.608889 20.48 29.070222 15.473778 6.826667 31.573333 12.430222 48.156444 16.696889 25.656889 6.599111 50.460444 15.559111 74.012444 26.794666 22.584889 12.970667 35.669333 35.754667 34.332445 59.733334 1.024 22.129778-9.699556 43.434667-28.956445 57.429333a137.244444 137.244444 0 0 1-56.32 19.768889A11.52 11.52 0 0 1 824.888889 853.333333v28.444445a28.444444 28.444444 0 0 1-56.888889 0v-28.444445l0.142222-2.133333a136.732444 136.732444 0 0 1-47.786666-17.777778c-24.746667-21.191111-38.4-50.631111-37.660445-81.237333h42.012445c0.853333 18.801778 8.988444 36.778667 23.04 50.801778 14.933333 9.386667 33.024 13.824 51.2 12.600889a104.647111 104.647111 0 0 0 51.256888-10.524445c11.605333-5.774222 18.773333-16.753778 18.688-28.615111-0.227556-15.36-10.154667-29.269333-25.6-35.925333a556.828444 556.828444 0 0 0-55.324444-18.318223 535.381333 535.381333 0 0 1-63.516444-22.869333c-20.878222-11.690667-33.109333-32.483556-32.028445-54.471111-1.137778-22.016 10.012444-43.093333 29.724445-56.32a126.321778 126.321778 0 0 1 45.966222-17.578667A15.217778 15.217778 0 0 1 768 568.888889v-28.444445a28.444444 28.444444 0 0 1 28.444444-28.444444z" fill="#0648A1" p-id="13542"></path></svg>
                </div>
            </div>
        </div>--%>


        <div class="card_container new_items" id="onetouch_award">
            <div class="card_item" style="background: linear-gradient(45deg, #8B0B24, #952E62);">

                <div>一键任务佣金</div>
                <div class="number">
                    <span class="total_amount" onclick="open_new_page('task_onetouch_preset.aspx','一键任务佣金');" style="cursor: pointer;">0</span> / <span class="total_number">0</span>人  
                </div>
                <div class="today_card">
                    今日新增佣金<span class="today_data"><span class="today_amount" onclick="open_new_page('task_onetouch_preset.aspx?date=<%=DateTime.Now.ToString("yyyy-MM-dd") %>','【今日】一键任务新增佣金');" style="cursor: pointer;">0</span> / <span class="today_number">0</span>人</span>
                </div>
                <div>
                    昨日新增佣金<span class="yesterday_data"><span class="yesterday_amount" onclick="open_new_page('task_onetouch_preset.aspx?date=<%=DateTime.Now.AddDays(-1).ToString("yyyy-MM-dd") %>','【昨日】一键任务新增佣金');" style="cursor: pointer;">0</span> / <span class="yesterday_number">0</span>人</span>
                </div>

                <div class="card_icon">
                    <svg t="" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="13913" width="66" height="66">
                        <path d="M943.104 469.504c-22.528 0-40.96 18.432-40.96 40.96 0 214.528-174.592 389.12-389.12 389.12s-389.12-174.592-389.12-389.12 174.592-389.12 389.12-389.12c129.024 0 249.344 64 322.048 170.496 12.8 18.944 38.4 23.552 56.832 10.752 18.944-12.8 23.552-38.4 10.752-56.832-88.064-129.536-233.472-206.336-389.632-206.336-259.584 0-471.04 211.456-471.04 471.04s211.456 471.04 471.04 471.04 471.04-211.456 471.04-471.04c0-22.528-18.432-40.96-40.96-40.96z" fill="#333333" p-id="13914"></path><path d="M681.472 493.568c22.528 0 40.96-18.432 40.96-40.96s-18.432-40.96-40.96-40.96h-69.632l95.232-95.232c15.872-15.872 15.872-41.984 0-57.856-15.872-15.872-41.984-15.872-57.856 0l-136.192 136.192-136.192-136.704c-15.872-15.872-41.984-15.872-57.856 0-15.872 15.872-15.872 41.984 0 57.856l95.232 95.232H344.576c-22.528 0-40.96 18.432-40.96 40.96s18.432 40.96 40.96 40.96h127.488v44.544H344.576c-22.528 0-40.96 18.432-40.96 40.96s18.432 40.96 40.96 40.96h127.488v114.176c0 22.528 18.432 40.96 40.96 40.96s40.96-18.432 40.96-40.96V619.52h127.488c22.528 0 40.96-18.432 40.96-40.96s-18.432-40.96-40.96-40.96h-127.488v-44.544h127.488z" fill="#333333" p-id="13915"></path></svg>
                </div>
            </div>
        </div>

        <div class="card_container new_items" id="onetouch_total">
            <div class="card_item" style="background: linear-gradient(45deg, #6C00FF, #AF53FF);">

                <div>一键任务</div>
                <div class="number">
                    <span class="total_amount">0</span> / <span expand="total_award_amount">0</span>佣 / <span class="total_number">0</span>人  
                </div>
                <div class="today_card">
                    今日新增<span class="today_data"><span class="today_amount">0</span> / <span expand="today_award_amount">0</span>佣 / <span class="today_number">0</span>人</span>
                </div>
                <div>
                    昨日新增<span class="yesterday_data"><span class="yesterday_amount">0</span> / <span expand="yesterday_award_amount">0</span>佣 / <span class="yesterday_number">0</span>人</span>
                </div>

                <div class="card_icon">
                    <svg t="" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="13913" width="66" height="66">
                        <path d="M943.104 469.504c-22.528 0-40.96 18.432-40.96 40.96 0 214.528-174.592 389.12-389.12 389.12s-389.12-174.592-389.12-389.12 174.592-389.12 389.12-389.12c129.024 0 249.344 64 322.048 170.496 12.8 18.944 38.4 23.552 56.832 10.752 18.944-12.8 23.552-38.4 10.752-56.832-88.064-129.536-233.472-206.336-389.632-206.336-259.584 0-471.04 211.456-471.04 471.04s211.456 471.04 471.04 471.04 471.04-211.456 471.04-471.04c0-22.528-18.432-40.96-40.96-40.96z" fill="#333333" p-id="13914"></path><path d="M681.472 493.568c22.528 0 40.96-18.432 40.96-40.96s-18.432-40.96-40.96-40.96h-69.632l95.232-95.232c15.872-15.872 15.872-41.984 0-57.856-15.872-15.872-41.984-15.872-57.856 0l-136.192 136.192-136.192-136.704c-15.872-15.872-41.984-15.872-57.856 0-15.872 15.872-15.872 41.984 0 57.856l95.232 95.232H344.576c-22.528 0-40.96 18.432-40.96 40.96s18.432 40.96 40.96 40.96h127.488v44.544H344.576c-22.528 0-40.96 18.432-40.96 40.96s18.432 40.96 40.96 40.96h127.488v114.176c0 22.528 18.432 40.96 40.96 40.96s40.96-18.432 40.96-40.96V619.52h127.488c22.528 0 40.96-18.432 40.96-40.96s-18.432-40.96-40.96-40.96h-127.488v-44.544h127.488z" fill="#333333" p-id="13915"></path></svg>
                </div>
            </div>
        </div>



        <div class="card_container new_items" id="onetouch_task">
            <div class="card_item" style="background: linear-gradient(45deg, #6C00FF, #AF53FF);">

                <div>当前任务未结束</div>
                <div class="number">
                    <span class="total_amount">0</span> / <span expand="total_award_amount">0</span>佣 / <span class="total_number">0</span>人  
                </div>
                <div class="today_card">
                    今日新增<span class="today_data"><span class="today_amount">0</span> / <span expand="today_award_amount">0</span>佣 / <span class="today_number">0</span>人</span>
                </div>
                <div>
                    昨日新增<span class="yesterday_data"><span class="yesterday_amount">0</span> / <span expand="yesterday_award_amount">0</span>佣 / <span class="yesterday_number">0</span>人</span>
                </div>

                <div class="card_icon">
                    <svg t="" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="13913" width="66" height="66">
                        <path d="M943.104 469.504c-22.528 0-40.96 18.432-40.96 40.96 0 214.528-174.592 389.12-389.12 389.12s-389.12-174.592-389.12-389.12 174.592-389.12 389.12-389.12c129.024 0 249.344 64 322.048 170.496 12.8 18.944 38.4 23.552 56.832 10.752 18.944-12.8 23.552-38.4 10.752-56.832-88.064-129.536-233.472-206.336-389.632-206.336-259.584 0-471.04 211.456-471.04 471.04s211.456 471.04 471.04 471.04 471.04-211.456 471.04-471.04c0-22.528-18.432-40.96-40.96-40.96z" fill="#333333" p-id="13914"></path><path d="M681.472 493.568c22.528 0 40.96-18.432 40.96-40.96s-18.432-40.96-40.96-40.96h-69.632l95.232-95.232c15.872-15.872 15.872-41.984 0-57.856-15.872-15.872-41.984-15.872-57.856 0l-136.192 136.192-136.192-136.704c-15.872-15.872-41.984-15.872-57.856 0-15.872 15.872-15.872 41.984 0 57.856l95.232 95.232H344.576c-22.528 0-40.96 18.432-40.96 40.96s18.432 40.96 40.96 40.96h127.488v44.544H344.576c-22.528 0-40.96 18.432-40.96 40.96s18.432 40.96 40.96 40.96h127.488v114.176c0 22.528 18.432 40.96 40.96 40.96s40.96-18.432 40.96-40.96V619.52h127.488c22.528 0 40.96-18.432 40.96-40.96s-18.432-40.96-40.96-40.96h-127.488v-44.544h127.488z" fill="#333333" p-id="13915"></path></svg>
                </div>
            </div>
        </div>


        <div class="card_container new_items" id="onetouch_finish">
            <div class="card_item" style="background: linear-gradient(45deg, #6C00FF, #AF53FF);">

                <div>已结束任务</div>
                <div class="number">
                    <span class="total_amount">0</span> / <span expand="total_award_amount">0</span>佣 / <span class="total_number">0</span>人  
                </div>
                <div class="today_card">
                    今日新增<span class="today_data"><span class="today_amount">0</span> / <span expand="today_award_amount">0</span>佣 / <span class="today_number">0</span>人</span>
                </div>
                <div>
                    昨日新增<span class="yesterday_data"><span class="yesterday_amount">0</span> / <span expand="yesterday_award_amount">0</span>佣 / <span class="yesterday_number">0</span>人</span>
                </div>

                <div class="card_icon">
                    <svg t="" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="13913" width="66" height="66">
                        <path d="M943.104 469.504c-22.528 0-40.96 18.432-40.96 40.96 0 214.528-174.592 389.12-389.12 389.12s-389.12-174.592-389.12-389.12 174.592-389.12 389.12-389.12c129.024 0 249.344 64 322.048 170.496 12.8 18.944 38.4 23.552 56.832 10.752 18.944-12.8 23.552-38.4 10.752-56.832-88.064-129.536-233.472-206.336-389.632-206.336-259.584 0-471.04 211.456-471.04 471.04s211.456 471.04 471.04 471.04 471.04-211.456 471.04-471.04c0-22.528-18.432-40.96-40.96-40.96z" fill="#333333" p-id="13914"></path><path d="M681.472 493.568c22.528 0 40.96-18.432 40.96-40.96s-18.432-40.96-40.96-40.96h-69.632l95.232-95.232c15.872-15.872 15.872-41.984 0-57.856-15.872-15.872-41.984-15.872-57.856 0l-136.192 136.192-136.192-136.704c-15.872-15.872-41.984-15.872-57.856 0-15.872 15.872-15.872 41.984 0 57.856l95.232 95.232H344.576c-22.528 0-40.96 18.432-40.96 40.96s18.432 40.96 40.96 40.96h127.488v44.544H344.576c-22.528 0-40.96 18.432-40.96 40.96s18.432 40.96 40.96 40.96h127.488v114.176c0 22.528 18.432 40.96 40.96 40.96s40.96-18.432 40.96-40.96V619.52h127.488c22.528 0 40.96-18.432 40.96-40.96s-18.432-40.96-40.96-40.96h-127.488v-44.544h127.488z" fill="#333333" p-id="13915"></path></svg>
                </div>
            </div>
        </div>




        <div class="card_container new_items" id="game_recharge">
            <div class="card_item" style="background: linear-gradient(45deg, #e35151, #e3977f);">

                <div>游戏转入人数</div>
                <div class="number">
                    <span class="total_number">0</span>人  / <span class="total_amount">0</span>
                </div>
                <div class="today_card">
                    今日转入人数<span class="today_data"><span class="today_number">0</span>人 / <span class="today_amount">0</span></span>
                </div>
                <div>
                    昨日转入人数<span class="yesterday_data"><span class="yesterday_number">0</span>人 / <span class="yesterday_amount">0</span></span>
                </div>

                <div class="card_icon">
                    <svg t="" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="4188" width="66" height="66">
                        <path d="M814.027289 958.506357c-79.889515 0-192.97713-88.539522-238.921531-184.639231L448.879915 773.867126c-45.677319 96.098685-158.709675 182.654017-238.909252 182.654017-145.678894 0-178.471764-284.726763-178.471764-384.381437 0-130.87677 104.693434-237.594303 233.385445-237.594303l494.228241 0c128.692011 0 233.385445 106.20895 233.385445 236.468666C992.500077 671.473061 959.707207 958.506357 814.027289 958.506357zM430.941358 718.952422l162.115238 0c11.31572 0 21.463848 7.427156 25.580608 17.966187 33.476439 85.829807 138.452305 166.886915 195.389062 166.886915 82.264609 0 123.558084-213.403345 123.558084-332.670705 0-99.973946-80.065524-181.674713-178.471764-181.674713L264.885368 389.460106c-96.744391 0-178.471764 84.033905-178.471764 182.922123 0 134.550439 44.001143 329.439104 123.55706 329.439104 57.273425 0 162.315806-80.199577 195.390086-165.0378C409.47751 726.245526 419.626661 718.952422 430.941358 718.952422z" fill="#dbdbdb" p-id="4189"></path><path d="M491.406474 691.488931l-13.728676 0c-7.588839 0-13.728676-6.141884-13.728676-13.728676 0-7.588839 6.139837-13.728676 13.728676-13.728676l13.728676 0c7.588839 0 13.728676 6.139837 13.728676 13.728676C505.13515 685.34807 498.995313 691.488931 491.406474 691.488931z" fill="#dbdbdb" p-id="4190"></path><path d="M211.284589 865.301582c-7.588839 0-13.728676-5.925966-13.728676-13.514805 0-7.586792 6.139837-13.594623 13.728676-13.594623 39.416731 0 136.200008-98.835006 161.792896-164.976402 2.051729-5.282307 7.132444-9.184173 12.803607-9.184173l50.610678 0c7.588839 0 13.728676 6.139837 13.728676 13.728676 0 7.586792-6.139837 13.728676-13.728676 13.728676l-41.507346 0C364.175745 760.13845 267.915377 865.301582 211.284589 865.301582z" fill="#dbdbdb" p-id="4191"></path><path d="M511.744685 386.350279c-7.909134 0-15.766079-3.405563-21.195741-9.975189-9.639544-11.704576-7.990998-29.012777 3.700275-38.664601 22.966061-18.944468 38.08848-44.310182 38.517245-64.60746 0.2415-11.288091-3.914146-20.391423-13.098319-28.650527-30.727839-27.658943-94.65173-85.213777-34.562167-168.054507 8.915044-12.254092 26.063609-14.975063 38.356586-6.099928 12.267395 8.916067 15.001669 26.075889 6.099928 38.356586-24.695448 34.025954-19.170618 53.559846 26.840298 94.987375 21.036106 18.931165 31.855522 43.344181 31.278377 70.627571-0.791016 36.681434-22.644743 76.231195-58.4666 105.806791C524.091898 384.285247 517.898849 386.350279 511.744685 386.350279z" fill="#dbdbdb" p-id="4192"></path><path d="M263.249101 692.440605c-7.588839 0-13.728676-6.139837-13.728676-13.728676L249.520425 486.512513c0-7.588839 6.139837-13.728676 13.728676-13.728676s13.728676 6.139837 13.728676 13.728676l0 192.199417C276.977777 686.300768 270.83794 692.440605 263.249101 692.440605z" fill="#dbdbdb" p-id="4193"></path><path d="M354.120738 595.388199 161.921321 595.388199c-7.588839 0-13.728676-6.139837-13.728676-13.728676s6.139837-13.728676 13.728676-13.728676l192.199417 0c7.588839 0 13.728676 6.139837 13.728676 13.728676S361.709577 595.388199 354.120738 595.388199z" fill="#dbdbdb" p-id="4194"></path><path d="M768.6191 583.871911c-12.57541 0-24.38641-4.906753-33.276894-13.808494-8.874111-8.861832-13.768585-20.673855-13.768585-33.249265s4.894474-24.38641 13.782911-33.275871c17.751293-17.777899 48.773843-17.777899 66.525136 0 8.901741 8.889461 13.795191 20.700461 13.795191 33.275871s-4.89345 24.387433-13.781888 33.261545C793.00551 578.966181 781.19451 583.871911 768.6191 583.871911zM768.6191 517.227048c-5.228071 0-10.149151 2.025123-13.849426 5.724375-3.699252 3.700275-5.737678 8.620331-5.737678 13.862729 0 5.228071 2.038426 10.149151 5.737678 13.848403 7.40055 7.429203 20.283975 7.429203 27.712155 0 3.700275-3.699252 5.737678-8.620331 5.737678-13.848403s-2.037403-10.148128-5.750981-13.862729C778.768251 519.252171 773.848195 517.227048 768.6191 517.227048z" fill="#dbdbdb" p-id="4195"></path><path d="M843.899644 659.137105c-12.576433 0-24.40176-4.906753-33.289174-13.808494-8.876158-8.861832-13.768585-20.671809-13.768585-33.249265 0-12.57541 4.892427-24.385387 13.781888-33.261545 17.751293-17.777899 48.759517-17.777899 66.537416 0 18.35402 18.327414 18.35402 48.171116 0.014326 66.538439C868.272751 654.244678 856.460728 659.137105 843.899644 659.137105zM843.885318 592.492242c-5.228071 0-10.149151 2.038426-13.849426 5.737678-3.700275 3.701299-5.737678 8.620331-5.737678 13.848403 0 5.230118 2.037403 10.149151 5.737678 13.850449 7.413853 7.41283 20.311605 7.41283 27.726481 0.01228 7.627724-7.641027 7.627724-20.068058-0.014326-27.711132C854.047772 594.530668 849.127715 592.492242 843.885318 592.492242z" fill="#dbdbdb" p-id="4196"></path><path d="M693.352883 659.137105c-12.57541 0-24.387433-4.892427-33.275871-13.794168-8.889461-8.888438-13.782911-20.700461-13.768585-33.263591 0-12.57541 4.892427-24.385387 13.782911-33.261545 17.750269-17.777899 48.77282-17.777899 66.524113 0 8.889461 8.861832 13.795191 20.673855 13.795191 33.249265 0.014326 12.57541-4.881171 24.399713-13.782911 33.289174C717.741339 654.244678 705.929316 659.137105 693.352883 659.137105zM693.352883 592.492242c-5.228071 0-10.149151 2.038426-13.848403 5.737678-3.701299 3.701299-5.737678 8.620331-5.737678 13.848403 0 5.230118 2.025123 10.149151 5.724375 13.850449 7.427156 7.440459 20.323884 7.40055 27.725458 0.01228 3.700275-3.699252 5.737678-8.632611 5.737678-13.862729 0-5.228071-2.037403-10.147104-5.737678-13.8351C703.502034 594.530668 698.581977 592.492242 693.352883 592.492242z" fill="#dbdbdb" p-id="4197"></path><path d="M768.6191 734.396159c-12.57541 0-24.38641-4.879124-33.262568-13.780865-8.888438-8.876158-13.782911-20.694321-13.782911-33.269731s4.894474-24.387433 13.782911-33.263591c17.751293-17.775852 48.773843-17.775852 66.525136 0 8.888438 8.861832 13.795191 20.673855 13.795191 33.249265 0.013303 12.57541-4.880147 24.407899-13.781888 33.283034C793.00551 729.518059 781.19451 734.396159 768.6191 734.396159zM768.6191 667.759483c-5.228071 0-10.149151 2.036379-13.849426 5.737678-3.699252 3.699252-5.737678 8.620331-5.737678 13.848403 0 5.242398 2.038426 10.163477 5.737678 13.862729 7.414877 7.421017 20.324908 7.393387 27.712155 0 3.700275-3.699252 5.737678-8.634658 5.737678-13.862729s-2.037403-10.149151-5.737678-13.836123C778.768251 669.795862 773.848195 667.759483 768.6191 667.759483z" fill="#dbdbdb" p-id="4198"></path></svg>
                </div>
            </div>
        </div>


        <div class="card_container new_items" id="game_withdraw">
            <div class="card_item" style="background: linear-gradient(45deg, #e35151, #e3977f);">

                <div>游戏转出人数</div>
                <div class="number">
                    <span class="total_number">0</span>人  / <span class="total_amount">0</span>
                </div>
                <div class="today_card">
                    今日转出人数<span class="today_data"><span class="today_number">0</span>人 / <span class="today_amount">0</span></span>
                </div>
                <div>
                    昨日转出人数<span class="yesterday_data"><span class="yesterday_number">0</span>人 / <span class="yesterday_amount">0</span></span>
                </div>

                <div class="card_icon">
                    <svg t="" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="4188" width="66" height="66">
                        <path d="M814.027289 958.506357c-79.889515 0-192.97713-88.539522-238.921531-184.639231L448.879915 773.867126c-45.677319 96.098685-158.709675 182.654017-238.909252 182.654017-145.678894 0-178.471764-284.726763-178.471764-384.381437 0-130.87677 104.693434-237.594303 233.385445-237.594303l494.228241 0c128.692011 0 233.385445 106.20895 233.385445 236.468666C992.500077 671.473061 959.707207 958.506357 814.027289 958.506357zM430.941358 718.952422l162.115238 0c11.31572 0 21.463848 7.427156 25.580608 17.966187 33.476439 85.829807 138.452305 166.886915 195.389062 166.886915 82.264609 0 123.558084-213.403345 123.558084-332.670705 0-99.973946-80.065524-181.674713-178.471764-181.674713L264.885368 389.460106c-96.744391 0-178.471764 84.033905-178.471764 182.922123 0 134.550439 44.001143 329.439104 123.55706 329.439104 57.273425 0 162.315806-80.199577 195.390086-165.0378C409.47751 726.245526 419.626661 718.952422 430.941358 718.952422z" fill="#dbdbdb" p-id="4189"></path><path d="M491.406474 691.488931l-13.728676 0c-7.588839 0-13.728676-6.141884-13.728676-13.728676 0-7.588839 6.139837-13.728676 13.728676-13.728676l13.728676 0c7.588839 0 13.728676 6.139837 13.728676 13.728676C505.13515 685.34807 498.995313 691.488931 491.406474 691.488931z" fill="#dbdbdb" p-id="4190"></path><path d="M211.284589 865.301582c-7.588839 0-13.728676-5.925966-13.728676-13.514805 0-7.586792 6.139837-13.594623 13.728676-13.594623 39.416731 0 136.200008-98.835006 161.792896-164.976402 2.051729-5.282307 7.132444-9.184173 12.803607-9.184173l50.610678 0c7.588839 0 13.728676 6.139837 13.728676 13.728676 0 7.586792-6.139837 13.728676-13.728676 13.728676l-41.507346 0C364.175745 760.13845 267.915377 865.301582 211.284589 865.301582z" fill="#dbdbdb" p-id="4191"></path><path d="M511.744685 386.350279c-7.909134 0-15.766079-3.405563-21.195741-9.975189-9.639544-11.704576-7.990998-29.012777 3.700275-38.664601 22.966061-18.944468 38.08848-44.310182 38.517245-64.60746 0.2415-11.288091-3.914146-20.391423-13.098319-28.650527-30.727839-27.658943-94.65173-85.213777-34.562167-168.054507 8.915044-12.254092 26.063609-14.975063 38.356586-6.099928 12.267395 8.916067 15.001669 26.075889 6.099928 38.356586-24.695448 34.025954-19.170618 53.559846 26.840298 94.987375 21.036106 18.931165 31.855522 43.344181 31.278377 70.627571-0.791016 36.681434-22.644743 76.231195-58.4666 105.806791C524.091898 384.285247 517.898849 386.350279 511.744685 386.350279z" fill="#dbdbdb" p-id="4192"></path><path d="M263.249101 692.440605c-7.588839 0-13.728676-6.139837-13.728676-13.728676L249.520425 486.512513c0-7.588839 6.139837-13.728676 13.728676-13.728676s13.728676 6.139837 13.728676 13.728676l0 192.199417C276.977777 686.300768 270.83794 692.440605 263.249101 692.440605z" fill="#dbdbdb" p-id="4193"></path><path d="M354.120738 595.388199 161.921321 595.388199c-7.588839 0-13.728676-6.139837-13.728676-13.728676s6.139837-13.728676 13.728676-13.728676l192.199417 0c7.588839 0 13.728676 6.139837 13.728676 13.728676S361.709577 595.388199 354.120738 595.388199z" fill="#dbdbdb" p-id="4194"></path><path d="M768.6191 583.871911c-12.57541 0-24.38641-4.906753-33.276894-13.808494-8.874111-8.861832-13.768585-20.673855-13.768585-33.249265s4.894474-24.38641 13.782911-33.275871c17.751293-17.777899 48.773843-17.777899 66.525136 0 8.901741 8.889461 13.795191 20.700461 13.795191 33.275871s-4.89345 24.387433-13.781888 33.261545C793.00551 578.966181 781.19451 583.871911 768.6191 583.871911zM768.6191 517.227048c-5.228071 0-10.149151 2.025123-13.849426 5.724375-3.699252 3.700275-5.737678 8.620331-5.737678 13.862729 0 5.228071 2.038426 10.149151 5.737678 13.848403 7.40055 7.429203 20.283975 7.429203 27.712155 0 3.700275-3.699252 5.737678-8.620331 5.737678-13.848403s-2.037403-10.148128-5.750981-13.862729C778.768251 519.252171 773.848195 517.227048 768.6191 517.227048z" fill="#dbdbdb" p-id="4195"></path><path d="M843.899644 659.137105c-12.576433 0-24.40176-4.906753-33.289174-13.808494-8.876158-8.861832-13.768585-20.671809-13.768585-33.249265 0-12.57541 4.892427-24.385387 13.781888-33.261545 17.751293-17.777899 48.759517-17.777899 66.537416 0 18.35402 18.327414 18.35402 48.171116 0.014326 66.538439C868.272751 654.244678 856.460728 659.137105 843.899644 659.137105zM843.885318 592.492242c-5.228071 0-10.149151 2.038426-13.849426 5.737678-3.700275 3.701299-5.737678 8.620331-5.737678 13.848403 0 5.230118 2.037403 10.149151 5.737678 13.850449 7.413853 7.41283 20.311605 7.41283 27.726481 0.01228 7.627724-7.641027 7.627724-20.068058-0.014326-27.711132C854.047772 594.530668 849.127715 592.492242 843.885318 592.492242z" fill="#dbdbdb" p-id="4196"></path><path d="M693.352883 659.137105c-12.57541 0-24.387433-4.892427-33.275871-13.794168-8.889461-8.888438-13.782911-20.700461-13.768585-33.263591 0-12.57541 4.892427-24.385387 13.782911-33.261545 17.750269-17.777899 48.77282-17.777899 66.524113 0 8.889461 8.861832 13.795191 20.673855 13.795191 33.249265 0.014326 12.57541-4.881171 24.399713-13.782911 33.289174C717.741339 654.244678 705.929316 659.137105 693.352883 659.137105zM693.352883 592.492242c-5.228071 0-10.149151 2.038426-13.848403 5.737678-3.701299 3.701299-5.737678 8.620331-5.737678 13.848403 0 5.230118 2.025123 10.149151 5.724375 13.850449 7.427156 7.440459 20.323884 7.40055 27.725458 0.01228 3.700275-3.699252 5.737678-8.632611 5.737678-13.862729 0-5.228071-2.037403-10.147104-5.737678-13.8351C703.502034 594.530668 698.581977 592.492242 693.352883 592.492242z" fill="#dbdbdb" p-id="4197"></path><path d="M768.6191 734.396159c-12.57541 0-24.38641-4.879124-33.262568-13.780865-8.888438-8.876158-13.782911-20.694321-13.782911-33.269731s4.894474-24.387433 13.782911-33.263591c17.751293-17.775852 48.773843-17.775852 66.525136 0 8.888438 8.861832 13.795191 20.673855 13.795191 33.249265 0.013303 12.57541-4.880147 24.407899-13.781888 33.283034C793.00551 729.518059 781.19451 734.396159 768.6191 734.396159zM768.6191 667.759483c-5.228071 0-10.149151 2.036379-13.849426 5.737678-3.699252 3.699252-5.737678 8.620331-5.737678 13.848403 0 5.242398 2.038426 10.163477 5.737678 13.862729 7.414877 7.421017 20.324908 7.393387 27.712155 0 3.700275-3.699252 5.737678-8.634658 5.737678-13.862729s-2.037403-10.149151-5.737678-13.836123C778.768251 669.795862 773.848195 667.759483 768.6191 667.759483z" fill="#dbdbdb" p-id="4198"></path></svg>
                </div>
            </div>
        </div>



        <div class="card_container new_items" id="game_newuser">
            <div class="card_item" style="background: linear-gradient(45deg, #e35151, #e3977f);">

                <div>新增游戏人数</div>
                <div class="number">
                    <span class="total_number">0</span>人  / <span class="total_amount">0</span>
                </div>
                <div class="today_card">
                    今日新增游戏人数<span class="today_data"><span class="today_number">0</span>人 / <span class="today_amount">0</span></span>
                </div>
                <div>
                    昨日新增游戏人数<span class="yesterday_data"><span class="yesterday_number">0</span>人 / <span class="yesterday_amount">0</span></span>
                </div>

                <div class="card_icon">
                    <svg t="" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="4188" width="66" height="66">
                        <path d="M814.027289 958.506357c-79.889515 0-192.97713-88.539522-238.921531-184.639231L448.879915 773.867126c-45.677319 96.098685-158.709675 182.654017-238.909252 182.654017-145.678894 0-178.471764-284.726763-178.471764-384.381437 0-130.87677 104.693434-237.594303 233.385445-237.594303l494.228241 0c128.692011 0 233.385445 106.20895 233.385445 236.468666C992.500077 671.473061 959.707207 958.506357 814.027289 958.506357zM430.941358 718.952422l162.115238 0c11.31572 0 21.463848 7.427156 25.580608 17.966187 33.476439 85.829807 138.452305 166.886915 195.389062 166.886915 82.264609 0 123.558084-213.403345 123.558084-332.670705 0-99.973946-80.065524-181.674713-178.471764-181.674713L264.885368 389.460106c-96.744391 0-178.471764 84.033905-178.471764 182.922123 0 134.550439 44.001143 329.439104 123.55706 329.439104 57.273425 0 162.315806-80.199577 195.390086-165.0378C409.47751 726.245526 419.626661 718.952422 430.941358 718.952422z" fill="#dbdbdb" p-id="4189"></path><path d="M491.406474 691.488931l-13.728676 0c-7.588839 0-13.728676-6.141884-13.728676-13.728676 0-7.588839 6.139837-13.728676 13.728676-13.728676l13.728676 0c7.588839 0 13.728676 6.139837 13.728676 13.728676C505.13515 685.34807 498.995313 691.488931 491.406474 691.488931z" fill="#dbdbdb" p-id="4190"></path><path d="M211.284589 865.301582c-7.588839 0-13.728676-5.925966-13.728676-13.514805 0-7.586792 6.139837-13.594623 13.728676-13.594623 39.416731 0 136.200008-98.835006 161.792896-164.976402 2.051729-5.282307 7.132444-9.184173 12.803607-9.184173l50.610678 0c7.588839 0 13.728676 6.139837 13.728676 13.728676 0 7.586792-6.139837 13.728676-13.728676 13.728676l-41.507346 0C364.175745 760.13845 267.915377 865.301582 211.284589 865.301582z" fill="#dbdbdb" p-id="4191"></path><path d="M511.744685 386.350279c-7.909134 0-15.766079-3.405563-21.195741-9.975189-9.639544-11.704576-7.990998-29.012777 3.700275-38.664601 22.966061-18.944468 38.08848-44.310182 38.517245-64.60746 0.2415-11.288091-3.914146-20.391423-13.098319-28.650527-30.727839-27.658943-94.65173-85.213777-34.562167-168.054507 8.915044-12.254092 26.063609-14.975063 38.356586-6.099928 12.267395 8.916067 15.001669 26.075889 6.099928 38.356586-24.695448 34.025954-19.170618 53.559846 26.840298 94.987375 21.036106 18.931165 31.855522 43.344181 31.278377 70.627571-0.791016 36.681434-22.644743 76.231195-58.4666 105.806791C524.091898 384.285247 517.898849 386.350279 511.744685 386.350279z" fill="#dbdbdb" p-id="4192"></path><path d="M263.249101 692.440605c-7.588839 0-13.728676-6.139837-13.728676-13.728676L249.520425 486.512513c0-7.588839 6.139837-13.728676 13.728676-13.728676s13.728676 6.139837 13.728676 13.728676l0 192.199417C276.977777 686.300768 270.83794 692.440605 263.249101 692.440605z" fill="#dbdbdb" p-id="4193"></path><path d="M354.120738 595.388199 161.921321 595.388199c-7.588839 0-13.728676-6.139837-13.728676-13.728676s6.139837-13.728676 13.728676-13.728676l192.199417 0c7.588839 0 13.728676 6.139837 13.728676 13.728676S361.709577 595.388199 354.120738 595.388199z" fill="#dbdbdb" p-id="4194"></path><path d="M768.6191 583.871911c-12.57541 0-24.38641-4.906753-33.276894-13.808494-8.874111-8.861832-13.768585-20.673855-13.768585-33.249265s4.894474-24.38641 13.782911-33.275871c17.751293-17.777899 48.773843-17.777899 66.525136 0 8.901741 8.889461 13.795191 20.700461 13.795191 33.275871s-4.89345 24.387433-13.781888 33.261545C793.00551 578.966181 781.19451 583.871911 768.6191 583.871911zM768.6191 517.227048c-5.228071 0-10.149151 2.025123-13.849426 5.724375-3.699252 3.700275-5.737678 8.620331-5.737678 13.862729 0 5.228071 2.038426 10.149151 5.737678 13.848403 7.40055 7.429203 20.283975 7.429203 27.712155 0 3.700275-3.699252 5.737678-8.620331 5.737678-13.848403s-2.037403-10.148128-5.750981-13.862729C778.768251 519.252171 773.848195 517.227048 768.6191 517.227048z" fill="#dbdbdb" p-id="4195"></path><path d="M843.899644 659.137105c-12.576433 0-24.40176-4.906753-33.289174-13.808494-8.876158-8.861832-13.768585-20.671809-13.768585-33.249265 0-12.57541 4.892427-24.385387 13.781888-33.261545 17.751293-17.777899 48.759517-17.777899 66.537416 0 18.35402 18.327414 18.35402 48.171116 0.014326 66.538439C868.272751 654.244678 856.460728 659.137105 843.899644 659.137105zM843.885318 592.492242c-5.228071 0-10.149151 2.038426-13.849426 5.737678-3.700275 3.701299-5.737678 8.620331-5.737678 13.848403 0 5.230118 2.037403 10.149151 5.737678 13.850449 7.413853 7.41283 20.311605 7.41283 27.726481 0.01228 7.627724-7.641027 7.627724-20.068058-0.014326-27.711132C854.047772 594.530668 849.127715 592.492242 843.885318 592.492242z" fill="#dbdbdb" p-id="4196"></path><path d="M693.352883 659.137105c-12.57541 0-24.387433-4.892427-33.275871-13.794168-8.889461-8.888438-13.782911-20.700461-13.768585-33.263591 0-12.57541 4.892427-24.385387 13.782911-33.261545 17.750269-17.777899 48.77282-17.777899 66.524113 0 8.889461 8.861832 13.795191 20.673855 13.795191 33.249265 0.014326 12.57541-4.881171 24.399713-13.782911 33.289174C717.741339 654.244678 705.929316 659.137105 693.352883 659.137105zM693.352883 592.492242c-5.228071 0-10.149151 2.038426-13.848403 5.737678-3.701299 3.701299-5.737678 8.620331-5.737678 13.848403 0 5.230118 2.025123 10.149151 5.724375 13.850449 7.427156 7.440459 20.323884 7.40055 27.725458 0.01228 3.700275-3.699252 5.737678-8.632611 5.737678-13.862729 0-5.228071-2.037403-10.147104-5.737678-13.8351C703.502034 594.530668 698.581977 592.492242 693.352883 592.492242z" fill="#dbdbdb" p-id="4197"></path><path d="M768.6191 734.396159c-12.57541 0-24.38641-4.879124-33.262568-13.780865-8.888438-8.876158-13.782911-20.694321-13.782911-33.269731s4.894474-24.387433 13.782911-33.263591c17.751293-17.775852 48.773843-17.775852 66.525136 0 8.888438 8.861832 13.795191 20.673855 13.795191 33.249265 0.013303 12.57541-4.880147 24.407899-13.781888 33.283034C793.00551 729.518059 781.19451 734.396159 768.6191 734.396159zM768.6191 667.759483c-5.228071 0-10.149151 2.036379-13.849426 5.737678-3.699252 3.699252-5.737678 8.620331-5.737678 13.848403 0 5.242398 2.038426 10.163477 5.737678 13.862729 7.414877 7.421017 20.324908 7.393387 27.712155 0 3.700275-3.699252 5.737678-8.634658 5.737678-13.862729s-2.037403-10.149151-5.737678-13.836123C778.768251 669.795862 773.848195 667.759483 768.6191 667.759483z" fill="#dbdbdb" p-id="4198"></path></svg>
                </div>
            </div>
        </div>


        <div class="card_container new_items" id="experience_amount">
            <div class="card_item" style="background: linear-gradient(45deg, #d3ac2e, #ffb559);">

                <div>体验金</div>
                <div class="number">
                    <span class="total_amount">0</span> / <span class="total_number">0</span>人  
                </div>
                <div class="today_card">
                    今日新增<span class="today_data"><span class="today_amount">0</span> / <span class="today_number">0</span>人</span>
                </div>
                <div>
                    昨日新增<span class="yesterday_data"><span class="yesterday_amount">0</span> / <span class="yesterday_number">0</span>人</span>
                </div>

                <div class="card_icon">
                    <svg t="" class="limit_fill" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="12113" width="66" height="66" style="margin-right: 6px;">
                        <path d="M512 485.74359m-459.487179 0a459.487179 459.487179 0 1 0 918.974358 0 459.487179 459.487179 0 1 0-918.974358 0Z" fill="#FFCD30" p-id="12114"></path><path d="M812.635897 504.123077c13.128205 208.738462-148.348718 357.087179-334.76923 361.025641v-45.948718c-177.230769 0-325.579487-135.220513-334.769231-316.389744h39.384615c9.189744 154.912821 139.158974 277.005128 295.384616 278.317949v-38.071795c-135.220513 0-246.810256-106.338462-254.68718-240.246154h40.697436c6.564103 112.902564 101.087179 203.487179 213.989744 203.48718v-39.384615c-91.897436 0-169.353846-72.205128-173.292308-164.102565h508.061538z m78.769231 3.938461c18.379487 18.379487 18.379487 47.261538 0 66.953847l-78.769231-70.892308 78.769231-70.892308c18.379487 18.379487 18.379487 47.261538 0 66.953846l-2.625641 2.625641c-1.312821 1.312821-1.312821 1.312821-2.625641 1.312821 1.312821 0 1.312821 1.312821 2.625641 1.31282 1.312821 1.312821 2.625641 1.312821 2.625641 2.625641z m-430.605128 98.461539c10.502564 0 18.379487-7.876923 18.379487-18.379487 0-10.502564-7.876923-18.379487-18.379487-18.379487-10.502564 0-18.379487 7.876923-18.379487 18.379487-1.312821 9.189744 7.876923 18.379487 18.379487 18.379487z" fill="#FFAC00" p-id="12115"></path><path d="M505.435897 367.589744v44.635897h-45.948718v14.441026h45.948718v45.948718h13.128206v-45.948718h45.948718v-14.441026h-45.948718v-44.635897zM620.964103 249.435897v56.451282h-56.451282v18.379488h56.451282v56.451282h18.379487v-56.451282h56.451282v-18.379488h-56.451282v-56.451282zM430.605128 105.025641v102.4h-102.4v31.507692h102.4v102.4h31.507693v-102.4h102.4v-31.507692h-102.4v-102.4z" fill="#402916" p-id="12116"></path></svg>
                </div>
            </div>
        </div>

        <%--<div class="card_container new_items" copyfrom="experience_amount" stateid="task">
            <div class="card_item" style="background: linear-gradient(45deg, #d3ac2e, #ffb559);">

                <div>当前体验金未结束</div>
                <div class="number">
                    <span class="total_amount">0</span> / <span class="total_number">0</span>人  
                </div>
                <div class="today_card">
                    今日新增<span class="today_data"><span class="today_amount">0</span> / <span class="today_number">0</span>人</span>
                </div>
                <div>
                    昨日新增<span class="yesterday_data"><span class="yesterday_amount">0</span> / <span class="yesterday_number">0</span>人</span>
                </div>

                <div class="card_icon">
                    <svg t="" class="limit_fill" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="12113" width="66" height="66" style="margin-right: 6px;">
                        <path d="M512 485.74359m-459.487179 0a459.487179 459.487179 0 1 0 918.974358 0 459.487179 459.487179 0 1 0-918.974358 0Z" fill="#FFCD30" p-id="12114"></path><path d="M812.635897 504.123077c13.128205 208.738462-148.348718 357.087179-334.76923 361.025641v-45.948718c-177.230769 0-325.579487-135.220513-334.769231-316.389744h39.384615c9.189744 154.912821 139.158974 277.005128 295.384616 278.317949v-38.071795c-135.220513 0-246.810256-106.338462-254.68718-240.246154h40.697436c6.564103 112.902564 101.087179 203.487179 213.989744 203.48718v-39.384615c-91.897436 0-169.353846-72.205128-173.292308-164.102565h508.061538z m78.769231 3.938461c18.379487 18.379487 18.379487 47.261538 0 66.953847l-78.769231-70.892308 78.769231-70.892308c18.379487 18.379487 18.379487 47.261538 0 66.953846l-2.625641 2.625641c-1.312821 1.312821-1.312821 1.312821-2.625641 1.312821 1.312821 0 1.312821 1.312821 2.625641 1.31282 1.312821 1.312821 2.625641 1.312821 2.625641 2.625641z m-430.605128 98.461539c10.502564 0 18.379487-7.876923 18.379487-18.379487 0-10.502564-7.876923-18.379487-18.379487-18.379487-10.502564 0-18.379487 7.876923-18.379487 18.379487-1.312821 9.189744 7.876923 18.379487 18.379487 18.379487z" fill="#FFAC00" p-id="12115"></path><path d="M505.435897 367.589744v44.635897h-45.948718v14.441026h45.948718v45.948718h13.128206v-45.948718h45.948718v-14.441026h-45.948718v-44.635897zM620.964103 249.435897v56.451282h-56.451282v18.379488h56.451282v56.451282h18.379487v-56.451282h56.451282v-18.379488h-56.451282v-56.451282zM430.605128 105.025641v102.4h-102.4v31.507692h102.4v102.4h31.507693v-102.4h102.4v-31.507692h-102.4v-102.4z" fill="#402916" p-id="12116"></path></svg>
                </div>
            </div>
        </div>--%>

        <%--<div class="card_container new_items" copyfrom="experience_amount" stateid="finish">
            <div class="card_item" style="background: linear-gradient(45deg, #d3ac2e, #ffb559);">

                <div>已结束体验金</div>
                <div class="number">
                    <span class="total_amount">0</span> / <span class="total_number">0</span>人  
                </div>
                <div class="today_card">
                    今日新增<span class="today_data"><span class="today_amount">0</span> / <span class="today_number">0</span>人</span>
                </div>
                <div>
                    昨日新增<span class="yesterday_data"><span class="yesterday_amount">0</span> / <span class="yesterday_number">0</span>人</span>
                </div>

                <div class="card_icon">
                    <svg t="" class="limit_fill" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="12113" width="66" height="66" style="margin-right: 6px;">
                        <path d="M512 485.74359m-459.487179 0a459.487179 459.487179 0 1 0 918.974358 0 459.487179 459.487179 0 1 0-918.974358 0Z" fill="#FFCD30" p-id="12114"></path><path d="M812.635897 504.123077c13.128205 208.738462-148.348718 357.087179-334.76923 361.025641v-45.948718c-177.230769 0-325.579487-135.220513-334.769231-316.389744h39.384615c9.189744 154.912821 139.158974 277.005128 295.384616 278.317949v-38.071795c-135.220513 0-246.810256-106.338462-254.68718-240.246154h40.697436c6.564103 112.902564 101.087179 203.487179 213.989744 203.48718v-39.384615c-91.897436 0-169.353846-72.205128-173.292308-164.102565h508.061538z m78.769231 3.938461c18.379487 18.379487 18.379487 47.261538 0 66.953847l-78.769231-70.892308 78.769231-70.892308c18.379487 18.379487 18.379487 47.261538 0 66.953846l-2.625641 2.625641c-1.312821 1.312821-1.312821 1.312821-2.625641 1.312821 1.312821 0 1.312821 1.312821 2.625641 1.31282 1.312821 1.312821 2.625641 1.312821 2.625641 2.625641z m-430.605128 98.461539c10.502564 0 18.379487-7.876923 18.379487-18.379487 0-10.502564-7.876923-18.379487-18.379487-18.379487-10.502564 0-18.379487 7.876923-18.379487 18.379487-1.312821 9.189744 7.876923 18.379487 18.379487 18.379487z" fill="#FFAC00" p-id="12115"></path><path d="M505.435897 367.589744v44.635897h-45.948718v14.441026h45.948718v45.948718h13.128206v-45.948718h45.948718v-14.441026h-45.948718v-44.635897zM620.964103 249.435897v56.451282h-56.451282v18.379488h56.451282v56.451282h18.379487v-56.451282h56.451282v-18.379488h-56.451282v-56.451282zM430.605128 105.025641v102.4h-102.4v31.507692h102.4v102.4h31.507693v-102.4h102.4v-31.507692h-102.4v-102.4z" fill="#402916" p-id="12116"></path></svg>
                </div>
            </div>
        </div>--%>


        
        <div class="card_container new_items" id="game_total_amount">
            <div class="card_item" style="background: linear-gradient(45deg, #e35151, #e3977f);">

                <div>游戏金额</div>
                <div class="number">
                    <span class="total_number">0</span>人  / <span class="total_amount">0</span>
                </div>
                <div class="today_card">
                    今日游戏金额<span class="today_data"><span class="today_amount">0</span> / <span class="today_number">0</span>人</span>
                </div>
                <div>
                    昨日游戏金额<span class="yesterday_data"><span class="yesterday_amount">0</span> / <span class="yesterday_number">0</span>人</span>
                </div>

                <div class="card_icon">
                    <svg t="" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="4188" width="66" height="66">
                        <path d="M814.027289 958.506357c-79.889515 0-192.97713-88.539522-238.921531-184.639231L448.879915 773.867126c-45.677319 96.098685-158.709675 182.654017-238.909252 182.654017-145.678894 0-178.471764-284.726763-178.471764-384.381437 0-130.87677 104.693434-237.594303 233.385445-237.594303l494.228241 0c128.692011 0 233.385445 106.20895 233.385445 236.468666C992.500077 671.473061 959.707207 958.506357 814.027289 958.506357zM430.941358 718.952422l162.115238 0c11.31572 0 21.463848 7.427156 25.580608 17.966187 33.476439 85.829807 138.452305 166.886915 195.389062 166.886915 82.264609 0 123.558084-213.403345 123.558084-332.670705 0-99.973946-80.065524-181.674713-178.471764-181.674713L264.885368 389.460106c-96.744391 0-178.471764 84.033905-178.471764 182.922123 0 134.550439 44.001143 329.439104 123.55706 329.439104 57.273425 0 162.315806-80.199577 195.390086-165.0378C409.47751 726.245526 419.626661 718.952422 430.941358 718.952422z" fill="#dbdbdb" p-id="4189"></path><path d="M491.406474 691.488931l-13.728676 0c-7.588839 0-13.728676-6.141884-13.728676-13.728676 0-7.588839 6.139837-13.728676 13.728676-13.728676l13.728676 0c7.588839 0 13.728676 6.139837 13.728676 13.728676C505.13515 685.34807 498.995313 691.488931 491.406474 691.488931z" fill="#dbdbdb" p-id="4190"></path><path d="M211.284589 865.301582c-7.588839 0-13.728676-5.925966-13.728676-13.514805 0-7.586792 6.139837-13.594623 13.728676-13.594623 39.416731 0 136.200008-98.835006 161.792896-164.976402 2.051729-5.282307 7.132444-9.184173 12.803607-9.184173l50.610678 0c7.588839 0 13.728676 6.139837 13.728676 13.728676 0 7.586792-6.139837 13.728676-13.728676 13.728676l-41.507346 0C364.175745 760.13845 267.915377 865.301582 211.284589 865.301582z" fill="#dbdbdb" p-id="4191"></path><path d="M511.744685 386.350279c-7.909134 0-15.766079-3.405563-21.195741-9.975189-9.639544-11.704576-7.990998-29.012777 3.700275-38.664601 22.966061-18.944468 38.08848-44.310182 38.517245-64.60746 0.2415-11.288091-3.914146-20.391423-13.098319-28.650527-30.727839-27.658943-94.65173-85.213777-34.562167-168.054507 8.915044-12.254092 26.063609-14.975063 38.356586-6.099928 12.267395 8.916067 15.001669 26.075889 6.099928 38.356586-24.695448 34.025954-19.170618 53.559846 26.840298 94.987375 21.036106 18.931165 31.855522 43.344181 31.278377 70.627571-0.791016 36.681434-22.644743 76.231195-58.4666 105.806791C524.091898 384.285247 517.898849 386.350279 511.744685 386.350279z" fill="#dbdbdb" p-id="4192"></path><path d="M263.249101 692.440605c-7.588839 0-13.728676-6.139837-13.728676-13.728676L249.520425 486.512513c0-7.588839 6.139837-13.728676 13.728676-13.728676s13.728676 6.139837 13.728676 13.728676l0 192.199417C276.977777 686.300768 270.83794 692.440605 263.249101 692.440605z" fill="#dbdbdb" p-id="4193"></path><path d="M354.120738 595.388199 161.921321 595.388199c-7.588839 0-13.728676-6.139837-13.728676-13.728676s6.139837-13.728676 13.728676-13.728676l192.199417 0c7.588839 0 13.728676 6.139837 13.728676 13.728676S361.709577 595.388199 354.120738 595.388199z" fill="#dbdbdb" p-id="4194"></path><path d="M768.6191 583.871911c-12.57541 0-24.38641-4.906753-33.276894-13.808494-8.874111-8.861832-13.768585-20.673855-13.768585-33.249265s4.894474-24.38641 13.782911-33.275871c17.751293-17.777899 48.773843-17.777899 66.525136 0 8.901741 8.889461 13.795191 20.700461 13.795191 33.275871s-4.89345 24.387433-13.781888 33.261545C793.00551 578.966181 781.19451 583.871911 768.6191 583.871911zM768.6191 517.227048c-5.228071 0-10.149151 2.025123-13.849426 5.724375-3.699252 3.700275-5.737678 8.620331-5.737678 13.862729 0 5.228071 2.038426 10.149151 5.737678 13.848403 7.40055 7.429203 20.283975 7.429203 27.712155 0 3.700275-3.699252 5.737678-8.620331 5.737678-13.848403s-2.037403-10.148128-5.750981-13.862729C778.768251 519.252171 773.848195 517.227048 768.6191 517.227048z" fill="#dbdbdb" p-id="4195"></path><path d="M843.899644 659.137105c-12.576433 0-24.40176-4.906753-33.289174-13.808494-8.876158-8.861832-13.768585-20.671809-13.768585-33.249265 0-12.57541 4.892427-24.385387 13.781888-33.261545 17.751293-17.777899 48.759517-17.777899 66.537416 0 18.35402 18.327414 18.35402 48.171116 0.014326 66.538439C868.272751 654.244678 856.460728 659.137105 843.899644 659.137105zM843.885318 592.492242c-5.228071 0-10.149151 2.038426-13.849426 5.737678-3.700275 3.701299-5.737678 8.620331-5.737678 13.848403 0 5.230118 2.037403 10.149151 5.737678 13.850449 7.413853 7.41283 20.311605 7.41283 27.726481 0.01228 7.627724-7.641027 7.627724-20.068058-0.014326-27.711132C854.047772 594.530668 849.127715 592.492242 843.885318 592.492242z" fill="#dbdbdb" p-id="4196"></path><path d="M693.352883 659.137105c-12.57541 0-24.387433-4.892427-33.275871-13.794168-8.889461-8.888438-13.782911-20.700461-13.768585-33.263591 0-12.57541 4.892427-24.385387 13.782911-33.261545 17.750269-17.777899 48.77282-17.777899 66.524113 0 8.889461 8.861832 13.795191 20.673855 13.795191 33.249265 0.014326 12.57541-4.881171 24.399713-13.782911 33.289174C717.741339 654.244678 705.929316 659.137105 693.352883 659.137105zM693.352883 592.492242c-5.228071 0-10.149151 2.038426-13.848403 5.737678-3.701299 3.701299-5.737678 8.620331-5.737678 13.848403 0 5.230118 2.025123 10.149151 5.724375 13.850449 7.427156 7.440459 20.323884 7.40055 27.725458 0.01228 3.700275-3.699252 5.737678-8.632611 5.737678-13.862729 0-5.228071-2.037403-10.147104-5.737678-13.8351C703.502034 594.530668 698.581977 592.492242 693.352883 592.492242z" fill="#dbdbdb" p-id="4197"></path><path d="M768.6191 734.396159c-12.57541 0-24.38641-4.879124-33.262568-13.780865-8.888438-8.876158-13.782911-20.694321-13.782911-33.269731s4.894474-24.387433 13.782911-33.263591c17.751293-17.775852 48.773843-17.775852 66.525136 0 8.888438 8.861832 13.795191 20.673855 13.795191 33.249265 0.013303 12.57541-4.880147 24.407899-13.781888 33.283034C793.00551 729.518059 781.19451 734.396159 768.6191 734.396159zM768.6191 667.759483c-5.228071 0-10.149151 2.036379-13.849426 5.737678-3.699252 3.699252-5.737678 8.620331-5.737678 13.848403 0 5.242398 2.038426 10.163477 5.737678 13.862729 7.414877 7.421017 20.324908 7.393387 27.712155 0 3.700275-3.699252 5.737678-8.634658 5.737678-13.862729s-2.037403-10.149151-5.737678-13.836123C778.768251 669.795862 773.848195 667.759483 768.6191 667.759483z" fill="#dbdbdb" p-id="4198"></path></svg>
                </div>
            </div>
        </div>


        <div class="card_container new_items" id="game_cost_amount">
            <div class="card_item" style="background: linear-gradient(45deg, #e35151, #e3977f);">

                <div>游戏消耗金额</div>
                <div class="number">
                    <span class="total_number">0</span>人  / <span class="total_amount">0</span>
                </div>
                <div class="today_card">
                    今日游戏消耗金额<span class="today_data"><span class="today_amount">0</span> / <span class="today_number">0</span>人</span>
                </div>
                <div>
                    昨日游戏消耗金额<span class="yesterday_data"><span class="yesterday_amount">0</span> / <span class="yesterday_number">0</span>人</span>
                </div>

                <div class="card_icon">
                    <svg t="" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="4188" width="66" height="66">
                        <path d="M814.027289 958.506357c-79.889515 0-192.97713-88.539522-238.921531-184.639231L448.879915 773.867126c-45.677319 96.098685-158.709675 182.654017-238.909252 182.654017-145.678894 0-178.471764-284.726763-178.471764-384.381437 0-130.87677 104.693434-237.594303 233.385445-237.594303l494.228241 0c128.692011 0 233.385445 106.20895 233.385445 236.468666C992.500077 671.473061 959.707207 958.506357 814.027289 958.506357zM430.941358 718.952422l162.115238 0c11.31572 0 21.463848 7.427156 25.580608 17.966187 33.476439 85.829807 138.452305 166.886915 195.389062 166.886915 82.264609 0 123.558084-213.403345 123.558084-332.670705 0-99.973946-80.065524-181.674713-178.471764-181.674713L264.885368 389.460106c-96.744391 0-178.471764 84.033905-178.471764 182.922123 0 134.550439 44.001143 329.439104 123.55706 329.439104 57.273425 0 162.315806-80.199577 195.390086-165.0378C409.47751 726.245526 419.626661 718.952422 430.941358 718.952422z" fill="#dbdbdb" p-id="4189"></path><path d="M491.406474 691.488931l-13.728676 0c-7.588839 0-13.728676-6.141884-13.728676-13.728676 0-7.588839 6.139837-13.728676 13.728676-13.728676l13.728676 0c7.588839 0 13.728676 6.139837 13.728676 13.728676C505.13515 685.34807 498.995313 691.488931 491.406474 691.488931z" fill="#dbdbdb" p-id="4190"></path><path d="M211.284589 865.301582c-7.588839 0-13.728676-5.925966-13.728676-13.514805 0-7.586792 6.139837-13.594623 13.728676-13.594623 39.416731 0 136.200008-98.835006 161.792896-164.976402 2.051729-5.282307 7.132444-9.184173 12.803607-9.184173l50.610678 0c7.588839 0 13.728676 6.139837 13.728676 13.728676 0 7.586792-6.139837 13.728676-13.728676 13.728676l-41.507346 0C364.175745 760.13845 267.915377 865.301582 211.284589 865.301582z" fill="#dbdbdb" p-id="4191"></path><path d="M511.744685 386.350279c-7.909134 0-15.766079-3.405563-21.195741-9.975189-9.639544-11.704576-7.990998-29.012777 3.700275-38.664601 22.966061-18.944468 38.08848-44.310182 38.517245-64.60746 0.2415-11.288091-3.914146-20.391423-13.098319-28.650527-30.727839-27.658943-94.65173-85.213777-34.562167-168.054507 8.915044-12.254092 26.063609-14.975063 38.356586-6.099928 12.267395 8.916067 15.001669 26.075889 6.099928 38.356586-24.695448 34.025954-19.170618 53.559846 26.840298 94.987375 21.036106 18.931165 31.855522 43.344181 31.278377 70.627571-0.791016 36.681434-22.644743 76.231195-58.4666 105.806791C524.091898 384.285247 517.898849 386.350279 511.744685 386.350279z" fill="#dbdbdb" p-id="4192"></path><path d="M263.249101 692.440605c-7.588839 0-13.728676-6.139837-13.728676-13.728676L249.520425 486.512513c0-7.588839 6.139837-13.728676 13.728676-13.728676s13.728676 6.139837 13.728676 13.728676l0 192.199417C276.977777 686.300768 270.83794 692.440605 263.249101 692.440605z" fill="#dbdbdb" p-id="4193"></path><path d="M354.120738 595.388199 161.921321 595.388199c-7.588839 0-13.728676-6.139837-13.728676-13.728676s6.139837-13.728676 13.728676-13.728676l192.199417 0c7.588839 0 13.728676 6.139837 13.728676 13.728676S361.709577 595.388199 354.120738 595.388199z" fill="#dbdbdb" p-id="4194"></path><path d="M768.6191 583.871911c-12.57541 0-24.38641-4.906753-33.276894-13.808494-8.874111-8.861832-13.768585-20.673855-13.768585-33.249265s4.894474-24.38641 13.782911-33.275871c17.751293-17.777899 48.773843-17.777899 66.525136 0 8.901741 8.889461 13.795191 20.700461 13.795191 33.275871s-4.89345 24.387433-13.781888 33.261545C793.00551 578.966181 781.19451 583.871911 768.6191 583.871911zM768.6191 517.227048c-5.228071 0-10.149151 2.025123-13.849426 5.724375-3.699252 3.700275-5.737678 8.620331-5.737678 13.862729 0 5.228071 2.038426 10.149151 5.737678 13.848403 7.40055 7.429203 20.283975 7.429203 27.712155 0 3.700275-3.699252 5.737678-8.620331 5.737678-13.848403s-2.037403-10.148128-5.750981-13.862729C778.768251 519.252171 773.848195 517.227048 768.6191 517.227048z" fill="#dbdbdb" p-id="4195"></path><path d="M843.899644 659.137105c-12.576433 0-24.40176-4.906753-33.289174-13.808494-8.876158-8.861832-13.768585-20.671809-13.768585-33.249265 0-12.57541 4.892427-24.385387 13.781888-33.261545 17.751293-17.777899 48.759517-17.777899 66.537416 0 18.35402 18.327414 18.35402 48.171116 0.014326 66.538439C868.272751 654.244678 856.460728 659.137105 843.899644 659.137105zM843.885318 592.492242c-5.228071 0-10.149151 2.038426-13.849426 5.737678-3.700275 3.701299-5.737678 8.620331-5.737678 13.848403 0 5.230118 2.037403 10.149151 5.737678 13.850449 7.413853 7.41283 20.311605 7.41283 27.726481 0.01228 7.627724-7.641027 7.627724-20.068058-0.014326-27.711132C854.047772 594.530668 849.127715 592.492242 843.885318 592.492242z" fill="#dbdbdb" p-id="4196"></path><path d="M693.352883 659.137105c-12.57541 0-24.387433-4.892427-33.275871-13.794168-8.889461-8.888438-13.782911-20.700461-13.768585-33.263591 0-12.57541 4.892427-24.385387 13.782911-33.261545 17.750269-17.777899 48.77282-17.777899 66.524113 0 8.889461 8.861832 13.795191 20.673855 13.795191 33.249265 0.014326 12.57541-4.881171 24.399713-13.782911 33.289174C717.741339 654.244678 705.929316 659.137105 693.352883 659.137105zM693.352883 592.492242c-5.228071 0-10.149151 2.038426-13.848403 5.737678-3.701299 3.701299-5.737678 8.620331-5.737678 13.848403 0 5.230118 2.025123 10.149151 5.724375 13.850449 7.427156 7.440459 20.323884 7.40055 27.725458 0.01228 3.700275-3.699252 5.737678-8.632611 5.737678-13.862729 0-5.228071-2.037403-10.147104-5.737678-13.8351C703.502034 594.530668 698.581977 592.492242 693.352883 592.492242z" fill="#dbdbdb" p-id="4197"></path><path d="M768.6191 734.396159c-12.57541 0-24.38641-4.879124-33.262568-13.780865-8.888438-8.876158-13.782911-20.694321-13.782911-33.269731s4.894474-24.387433 13.782911-33.263591c17.751293-17.775852 48.773843-17.775852 66.525136 0 8.888438 8.861832 13.795191 20.673855 13.795191 33.249265 0.013303 12.57541-4.880147 24.407899-13.781888 33.283034C793.00551 729.518059 781.19451 734.396159 768.6191 734.396159zM768.6191 667.759483c-5.228071 0-10.149151 2.036379-13.849426 5.737678-3.699252 3.699252-5.737678 8.620331-5.737678 13.848403 0 5.242398 2.038426 10.163477 5.737678 13.862729 7.414877 7.421017 20.324908 7.393387 27.712155 0 3.700275-3.699252 5.737678-8.634658 5.737678-13.862729s-2.037403-10.149151-5.737678-13.836123C778.768251 669.795862 773.848195 667.759483 768.6191 667.759483z" fill="#dbdbdb" p-id="4198"></path></svg>
                </div>
            </div>
        </div>



    </div>

    <style>
        .summary_data {
            background: #fff;
            border: 1px solid #eee;
            padding: 18px;
            border-radius: 8px;
        }

            .summary_data .sbox {
                font-size: 15px;
                width: 25%;
            }

        .sbox_title {
            color: #6a6b6c;
        }

        .sbox_data {
            color: #000;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .sbox_ty {
            font-size: 12px;
        }

        .sbox_today_name {
            color: gray;
            margin-right: 5px;
        }

        .sbox_today_value {
            color: #2a2b2c;
        }
    </style>

    <div class="summary_data">

        <div style="display: flex; border-bottom: 1px solid #eee; padding-bottom: 10px; margin-bottom: 10px;">
            <div class="sbox" typename="买币赠送">
                <span class="sbox_title">买币赠送</span>
                <div class="sbox_data">
                    0
                </div>
                <div class="sbox_ty sbox_today">
                    <span class="sbox_today_name">今日</span><strong class="sbox_today_value">0</strong>
                </div>
                <div class="sbox_ty sbox_yesterday">
                    <span class="sbox_today_name">昨日</span><strong class="sbox_today_value">0</strong>
                </div>
            </div>

            <div class="sbox" typename="红包领取金额">
                <span class="sbox_title">红包领取金额</span>
                <div class="sbox_data">
                    0
                </div>
                <div class="sbox_ty sbox_today">
                    <span class="sbox_today_name">今日</span><strong class="sbox_today_value">0</strong>
                </div>
                <div class="sbox_ty sbox_yesterday">
                    <span class="sbox_today_name">昨日</span><strong class="sbox_today_value">0</strong>
                </div>
            </div>

            <div class="sbox" typename="任务奖励">
                <span class="sbox_title">任务奖励</span>
                <div class="sbox_data">
                    0
                </div>
                <div class="sbox_ty sbox_today">
                    <span class="sbox_today_name">今日</span><strong class="sbox_today_value">0</strong>
                </div>
                <div class="sbox_ty sbox_yesterday">
                    <span class="sbox_today_name">昨日</span><strong class="sbox_today_value">0</strong>
                </div>
            </div>

            <div class="sbox" typename="转盘奖励">
                <span class="sbox_title">转盘奖励</span>
                <div class="sbox_data">
                    0
                </div>
                <div class="sbox_ty sbox_today">
                    <span class="sbox_today_name">今日</span><strong class="sbox_today_value">0</strong>
                </div>
                <div class="sbox_ty sbox_yesterday">
                    <span class="sbox_today_name">昨日</span><strong class="sbox_today_value">0</strong>
                </div>
            </div>

            <div class="sbox" typename="出借利息" style="display: none;">
                <span class="sbox_title">出借利息</span>
                <div class="sbox_data">
                    0
                </div>
                <div class="sbox_ty sbox_today">
                    <span class="sbox_today_name">今日</span><strong class="sbox_today_value">0</strong>
                </div>
                <div class="sbox_ty sbox_yesterday">
                    <span class="sbox_today_name">昨日</span><strong class="sbox_today_value">0</strong>
                </div>
            </div>

        </div>


        <div style="display: flex;">
            <div style="font-size: 14px;" id="gift_total">
                <span class="sbox_today_name">累计赠送</span><strong class="sbox_today_value">0</strong>
            </div>
            <div style="font-size: 14px; margin-left: 18px;" id="today_gift_total">
                <span class="sbox_today_name">今日赠送</span><strong class="sbox_today_value">0</strong>
            </div>
            <div style="font-size: 14px; margin-left: 18px;" id="yesterday_gift_total">
                <span class="sbox_today_name">昨日赠送</span><strong class="sbox_today_value">0</strong>
            </div>
        </div>

    </div>



    <script>
        $('.new_items').append('<svg t="" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="7477" width="50" height="50" style="position: absolute; top: 5px; right: 5px; z-index: 99;"><path d="M1023.701333 0v1023.658667L0-0.042667h1023.658667z m-249.045333 338.688c-3.413333 8.533333-7.552 16.682667-15.701333 31.573333l-82.474667 145.749334c-11.136 19.882667-11.136 19.882667-15.104 25.813333l26.453333 26.453333c5.333333-4.181333 13.269333-8.96 24.618667-15.488l95.018667-52.906666c8.106667-4.565333 17.877333-10.325333 31.402666-19.456-8.362667 11.946667-14.122667 21.248-19.669333 31.189333l-52.693333 95.232a285.141333 285.141333 0 0 1-15.317334 24.832l26.666667 26.624c4.736-3.541333 4.736-3.541333 11.946667-7.552 2.133333-1.365333 6.741333-3.968 13.653333-8.106667l145.706667-82.133333c14.72-8.32 21.888-11.946667 31.402666-15.872l-31.786666-31.829333a149.76 149.76 0 0 1-24.661334 17.92l-93.44 56.448c-2.133333 1.365333-11.52 7.552-15.274666 10.112-8.106667 5.76-8.106667 5.76-13.696 9.386666 11.733333-18.133333 14.677333-23.082667 18.474666-29.653333l54.272-98.773333c5.12-9.557333 11.093333-19.114667 15.274667-24.448l-24.448-24.448a178.773333 178.773333 0 0 1-24.618667 15.872l-98.816 54.656c-7.125333 4.010667-14.08 8.149333-29.013333 17.92a750.933333 750.933333 0 0 0 18.474667-28.032l57.258666-94.208c6.570667-10.965333 11.52-17.92 17.92-25.045334z m-123.648-124.8a248.661333 248.661333 0 0 1-24.021333 27.221333l-107.52 107.52a227.413333 227.413333 0 0 1-27.861334 24.661334c8.362667 7.552 13.909333 12.714667 25.856 24.661333l61.994667 61.994667c12.117333 12.117333 18.090667 18.901333 24.277333 27.050666l26.453334-26.453333a268.8 268.8 0 0 1-26.88-24.064l-59.605334-59.605333 46.933334-46.933334 48.469333 48.512c12.330667 12.330667 17.493333 17.92 24.448 27.221334l26.026667-26.026667a292.608 292.608 0 0 1-27.221334-24.448l-48.298666-48.298667 40.96-40.96 57.216 57.258667c11.733333 11.733333 17.706667 18.474667 24.277333 27.008l26.24-26.197333a300.245333 300.245333 0 0 1-27.050667-24.277334l-60.032-60.032c-12.117333-12.117333-17.066667-17.493333-24.661333-25.813333zM487.253333 51.285333a233.941333 233.941333 0 0 1-24.064 27.221334L360.618667 181.077333a304.213333 304.213333 0 0 1-31.573334 28.416l29.013334 29.013334c6.144-8.106667 14.890667-18.090667 28.202666-31.402667l65.194667-65.194667c4.565333-4.565333 13.141333-13.909333 16.682667-17.92l9.386666-10.88c-4.608 14.08-8.192 27.605333-10.368 37.376l-26.026666 128.597334c-2.986667 14.506667-5.162667 23.850667-8.533334 33.962666l31.402667 31.402667c5.76-8.106667 12.885333-16.085333 24.021333-27.221333l105.173334-105.130667c12.117333-12.117333 19.882667-19.114667 29.013333-25.856l-29.013333-29.013333c-6.4 8.746667-14.122667 17.28-25.856 29.013333L500.565333 253.013333c-8.362667 8.362667-19.669333 20.906667-26.624 29.44 3.541333-12.330667 7.552-29.013333 9.941334-40.96l25.429333-125.994666c3.370667-16.896 5.76-26.453333 8.96-33.194667z" fill="#FFF" p-id="7478"></path></svg>');


        function isDecimal(str) {
            var number = Number(str);
            return !isNaN(number) && !Number.isInteger(number);
        }

        var push_data = function (k, json, show_last) {

            if (isDecimal(json.total_amount)) {
                json.total_amount = parseFloat(json.total_amount).toFixed(2);
            }
            if (isDecimal(json.today_amount)) {
                json.today_amount = parseFloat(json.today_amount).toFixed(2);
            }
            if (isDecimal(json.yesterday_amount)) {
                json.yesterday_amount = parseFloat(json.yesterday_amount).toFixed(2);
            }

            var targetId = k;
            var stateid = "";
            if (targetId.indexOf('copyfrom') != -1) {
                stateid = $(targetId).attr('stateid') + '_';
                console.log('copyfrom', targetId, stateid);
            } else {
                targetId = '#' + k;
            }


            $(targetId + '  .total_amount').html(json[stateid + 'total_amount']);
            $(targetId + '  .total_number').html(json[stateid + 'total_number']);
            if (!show_last) {
                json[stateid + 'today_amount'] = "-";
                json[stateid + 'today_number'] = "-";
                json[stateid + 'yesterday_amount'] = "-";
                json[stateid + 'yesterday_number'] = "-";
            }


            $(targetId + '  .today_amount').html(json[stateid + 'today_amount']);
            $(targetId + '  .today_number').html(json[stateid + 'today_number']);
            $(targetId + '  .yesterday_amount').html(json[stateid + 'yesterday_amount']);
            $(targetId + '  .yesterday_number').html(json[stateid + 'yesterday_number']);

            $(targetId + '  .today_daily_number').html(json[stateid + 'today_daily_number']);
            $(targetId + '  .total_daily_number').html(json[stateid + 'total_daily_number']);
            $(targetId + '  .yesterday_daily_number').html(json[stateid + 'yesterday_daily_number']);


            $(targetId + ' [expand]').each(function () {
                //try {
                //    $('#' + k + '  .expand').html(json[$(this).attr('expand')]);
                //} catch (e) {

                //}
                var key = $(this).attr('expand');
                console.log('expand', k, key, json[$(this).attr('expand')]);
                $(targetId + '  [expand="' + key + '"]').html(json[stateid + key]);
            })




        }


        var add_data = function (json, json_add) {
            var res = $.extend({}, json);
            for (var k in res) {
                res[k] = parseFloat(res[k]) + parseFloat(json_add[k]);
            }
            return res;
        }





        var get_totalData = function () {

            var start_time = $('#start_time').val();
            var end_time = $('#end_time').val();
            if (start_time != "") {
                start_time += " 00:00:00";
            }
            if (end_time != "") {
                end_time += " 23:59:59";
            }



            var data = {};
            data.start_time = start_time;
            data.end_time = end_time;
            data.roomNumber = $('#roomNumber').val();
            data.refresh = get_param("refresh");
            data.team_uid = $('#team_uid').val();

            $.ajax({
                type: "POST",
                url: "../api/admin.aspx?do=get_toatal_data",
                data: data,
                datatype: "json",
                success: function (json) {
                    if (json.code != 1) {
                        _modal("提示", json.msg);
                    }
                    //$('.firstbuy_number').html(json.list.firstbuy_number.today_number + json.list.firstapi_number.today_number);
                    //$('.yesterday_firstbuy_number').html(json.list.firstbuy_number.yesterday_number + json.list.firstapi_number.yesterday_number);

                    $('.firstbuy_number').html(json.list.firstbuy_number.today_number);
                    $('.yesterday_firstbuy_number').html(json.list.firstbuy_number.yesterday_number);


                    var show_last = json.start_time == "" && json.end_time == "";

                    var recharge_total = {
                        today_amount: 0,
                        today_number: 0,
                        total_amount: 0,
                        total_number: 0,
                        yesterday_amount: 0,
                        yesterday_number: 0,
                        today_daily_number: 0,
                        total_daily_number: 0,
                        yesterday_daily_number: 0
                    }


                    var cash_total = {
                        today_amount: 0,
                        today_number: 0,
                        total_amount: 0,
                        total_number: 0,
                        yesterday_amount: 0,
                        yesterday_number: 0,
                        today_daily_number: 0,
                        total_daily_number: 0,
                        yesterday_daily_number: 0
                    }

                    //console.log('recharge_yongjin', json.list.yongjing_qd, json.list.recharge_yongjin, add_data(json.list.yongjing_qd, json.list.recharge_yongjin));
                    json.list.yongjing_qd = add_data(json.list.yongjing_qd, json.list.recharge_yongjin);


                    for (var k in json.list) {
                        push_data(k, json.list[k], show_last);
                        $('[copyfrom="' + k + '"]').each(function () {
                            push_data('[copyfrom="' + k + '"][stateid="' + $(this).attr('stateid') + '"]', json.list[k], show_last);
                        })

                        //if (k == "recharge_dating" || k == "recharge_sanfang" || k == "recharge_admin" || k == "deduct_admin") {
                        if (k == "recharge_dating" || k == "recharge_admin" || k == "recharge_sanfang" || k == "newuser_recharge_dating") {
                            for (var e in json.list[k]) {
                                var v = parseFloat(json.list[k][e]);
                                if (k == "deduct_admin") {
                                    v = v * -1;
                                }
                                recharge_total[e] += v
                            }
                        }

                        if (k == "deduct_admin" || k == "sell_dating_total") {
                            for (var e in json.list[k]) {
                                var v = parseFloat(json.list[k][e]);
                                cash_total[e] += v
                            }
                        }
                    }

                    recharge_total.total_number = json.list.recharge_total_number.total_number;
                    recharge_total.today_number = json.list.recharge_total_number.today_number;
                    recharge_total.yesterday_number = json.list.recharge_total_number.yesterday_number;



                    cash_total.total_number = json.list.cash_total_number.total_number;
                    cash_total.today_number = json.list.cash_total_number.today_number;
                    cash_total.yesterday_number = json.list.cash_total_number.yesterday_number;

                    //recharge_total.total_amount = recharge_total.total_amount.toFixed(2);

                    push_data('recharge_total', recharge_total, show_last);
                    push_data('cash_total', cash_total, show_last);

                    var gift_total = {
                        total: 0,
                        today: 0,
                        yesterday: 0
                    }
                    var key = "";

                    var array = ["买币赠送", "红包领取金额", "任务奖励", "转盘奖励", "出借利息"];

                    for (var i = 0; i < array.length; i++) {
                        key = array[i];
                        $('[typename="' + key + '"]').find(".sbox_data").html(json.list.gift_total[key]);
                        $('[typename="' + key + '"]').find(".sbox_ty").eq(0).find(".sbox_today_value").html(json.list.gift_total['今日_' + key]);
                        $('[typename="' + key + '"]').find(".sbox_ty").eq(1).find(".sbox_today_value").html(json.list.gift_total['昨日_' + key]);


                        gift_total.total += parseFloat(json.list.gift_total[key]);
                        gift_total.today += parseFloat(json.list.gift_total['今日_' + key]);
                        gift_total.yesterday += parseFloat(json.list.gift_total['昨日_' + key]);
                    }

                    $('#gift_total').find(".sbox_today_value").html(parseFloat(gift_total.total).toFixed(2));
                    $('#today_gift_total').find(".sbox_today_value").html(parseFloat(gift_total.today).toFixed(2));
                    $('#yesterday_gift_total').find(".sbox_today_value").html(parseFloat(gift_total.yesterday).toFixed(2));

                },
                error: function () {
                }
            });

        }

        get_totalData();
    </script>













    <div>


        <%if (googleCodeQrImg != "已验证")
          {
          
        %>


        <div style="text-align: center; display: flex; flex-direction: column;">
            <div>
                <h2 style="font-size: 28px; font-weight: bold; color: #000;">谷歌验证码：</h2>
            </div>
            <div id="google_code" style="margin: 0 auto;"></div>



            <div>
                <h2 style="font-size: 28px; font-weight: bold; color: #000;">请输入验证：</h2>
            </div>



            <div style="display: flex; justify-content: center; flex-direction: column; align-items: center;">
                <input class="form-control actCode" style="width: 300px;">

                <a class="btn btn-success" onclick="verifyCode()" style="margin-top: 24px;">
                    <svg t="" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="4127" width="16" height="16">
                        <path d="M512 170.666667a341.333333 341.333333 0 0 0-290.133333 521.216 42.666667 42.666667 0 0 1-72.533334 45.013333A424.874667 424.874667 0 0 1 85.333333 512C85.333333 276.352 276.352 85.333333 512 85.333333s426.666667 191.018667 426.666667 426.666667-191.018667 426.666667-426.666667 426.666667a424.874667 424.874667 0 0 1-234.666667-70.314667 42.666667 42.666667 0 1 1 46.933334-71.210667A341.333333 341.333333 0 1 0 512 170.666667z m222.165333 264.832a42.666667 42.666667 0 0 0-60.330666-60.330667L469.333333 579.669333l-119.168-119.168a42.666667 42.666667 0 0 0-60.330666 60.330667l149.333333 149.333333a42.666667 42.666667 0 0 0 60.330667 0l234.666666-234.666666z" fill="#14101C" p-id="4128"></path></svg>&nbsp;验证码检查</a>
            </div>



        </div>


        <%
          }
          else
          {
        %>
        <h1 style="color: green; background: #0080000f; font-size: 22px; padding: 20px; text-align: center;">谷歌验证码已验证</h1>
        <%
          } %>


        <script>
            if (document.getElementById("google_code")) {
                var qrcode = new QRCode(document.getElementById("google_code"), {
                    text: '<%=googleCodeQrImg %>',
                    width: 300,
                    height: 300,
                    colorDark: "#000000",
                    colorLight: "#ffffff",
                    margin: 5,
                    correctLevel: QRCode.CorrectLevel.Q
                });
            }




            var verifyCode = function () {
                var data = {};
                data["actCode"] = $('.actCode').val();
                $.ajax({
                    type: "POST",
                    url: "../api/admin.aspx?do=verifyCode",
                    data: data,
                    datatype: "json",
                    success: function (data) {
                        if (data.code == 1) {
                            layer.msg(data.msg, { icon: 1, time: 500 }, function () {
                                location.href = location.href;
                            });
                        } else {
                            layer.msg(data.msg, { icon: 2, time: 1500 });
                        }
                    },
                    error: function () {
                        layer.msg("操作出现错误", { icon: 2, time: 1500 });
                    }
                });

            }
        </script>



        <style>
            .total_number, .today_number, .yesterday_number {
                cursor: pointer;
            }
        </style>
        <script>
            $('.card_container').each(function () {
                var id = $(this).attr('id');
                if (!id) {
                    id = $(this).attr('stateid') + "_" + $(this).attr('copyfrom');
                }
                var name = $(this).find('div').eq(0).find('div').eq(0).html()
                $(this).find('.total_number').on('click', function () {
                    open_new_page('account_list.aspx?' + id + '=-&total=1&team_uid=' + $('#team_uid').val(), '【总人数】' + name);
                })
                var name = $(this).find('div').eq(0).find('div').eq(0).html()
                $(this).find('.today_number').on('click', function () {
                    open_new_page('account_list.aspx?' + id + '=<%=DateTime.Now.ToString("yyyy-MM-dd") %>&team_uid=' + $('#team_uid').val(), '【今日人数】' + name);
                })
                $(this).find('.yesterday_number').on('click', function () {
                    open_new_page('account_list.aspx?' + id + '=<%=DateTime.Now.AddDays(-1).ToString("yyyy-MM-dd") %>&team_uid=' + $('#team_uid').val(), '【昨日人数】' + name);
                })
            })
        </script>

    </div>
</asp:Content>


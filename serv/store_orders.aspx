<%@ Page Title="项目管理" Language="C#" MasterPageFile="~/serv/m.master" AutoEventWireup="true" CodeFile="store_orders.aspx.cs" Inherits="w_index" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="Server">
    <link href="../css/element-ui.css" rel="stylesheet" />
    <style>
        .el-tag.el-tag--test {
            background-color: #d5d5f5;
            border-color: #7d7dc7;
            color: #6d3333;
        }
    </style>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="Server">
    <div class="ibox float-e-margins">
        <div class="ibox-title">
            <h5 style="color: #8d07d1; text-shadow: 5px 5px 15px #dbd52585;">
                <svg t="1699252608063" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="7793" width="25" height="25" style="position: relative; top: 5px;">
                    <path d="M37.888 843.776a464.896 133.12 0 1 0 929.792 0 464.896 133.12 0 1 0-929.792 0Z" fill="#EEF2FF" p-id="7794"></path><path d="M772.096 824.32H68.608c-17.408 0-30.72-13.312-30.72-30.72V205.824c0-17.408 13.312-30.72 30.72-30.72h886.784c17.408 0 30.72 13.312 30.72 30.72v405.504c0 117.76-95.232 212.992-214.016 212.992z" fill="#70D4FF" p-id="7795"></path><path d="M284.672 724.992h-133.12c-20.48 0-37.888-17.408-37.888-37.888s17.408-37.888 37.888-37.888h133.12c20.48 0 37.888 17.408 37.888 37.888 0 21.504-17.408 37.888-37.888 37.888z" fill="#FFFFFF" p-id="7796"></path><path d="M877.568 646.144L132.096 508.928c-16.384-3.072-27.648-19.456-24.576-35.84l77.824-422.912c3.072-16.384 19.456-27.648 35.84-24.576l745.472 137.216c16.384 3.072 27.648 19.456 24.576 35.84l-77.824 422.912c-3.072 16.384-18.432 27.648-35.84 24.576z" fill="#4E8EFF" p-id="7797"></path><path d="M566.272 242.688c16.384 3.072 27.648 19.456 24.576 35.84L551.936 491.52c-3.072 16.384-19.456 27.648-35.84 24.576-16.384-3.072-27.648-19.456-24.576-35.84l38.912-212.992c3.072-16.384 19.456-27.648 35.84-24.576z" fill="#FFFFFF" p-id="7798"></path><path d="M524.288 152.576l47.104 67.584 67.584-47.104c13.312-9.216 29.696-7.168 40.96 5.12l2.048 3.072c9.216 14.336 6.144 33.792-8.192 43.008L596.992 276.48c-11.264 7.168-25.6 7.168-36.864-1.024-13.312 4.096-26.624-1.024-34.816-12.288l-52.224-76.8c-9.216-14.336-5.12-33.792 8.192-43.008 14.336-7.168 32.768-4.096 43.008 9.216zM479.232 289.792L630.784 317.44c20.48 4.096 28.672 15.36 24.576 35.84-4.096 20.48-15.36 28.672-35.84 24.576l-151.552-27.648c-20.48-4.096-28.672-15.36-24.576-35.84 4.096-20.48 15.36-28.672 35.84-24.576zM462.848 380.928L614.4 408.576c20.48 4.096 28.672 15.36 24.576 35.84-4.096 20.48-15.36 28.672-35.84 24.576l-151.552-27.648c-20.48-4.096-28.672-15.36-24.576-35.84 3.072-20.48 15.36-28.672 35.84-24.576z" fill="#FFFFFF" p-id="7799"></path></svg>&nbsp;项目管理</h5>
        </div>
        <div class="ibox-content">
            <div class="row layui-form">


                <div class="col-md-3">
                    <select id="typeid">
                        <option value="">项目分类</option>
                        <%for (int i = 0; i < arrayList.Length; i++)
                          {
                        %>
                        <option value="<%=arrayList[i] %>"><%=arrayList[i] %></option>
                        <%
                          } %>
                    </select>
                </div>


                <div class="col-md-3">
                    <div class="form-group form-buttom" style="position: relative;">
                        <input type="text" id="search-keyword" name="ordername" class="form-control" placeholder="请输入搜索的信息" style="padding-right: 50px;">
                        <a class="input_button" onclick="searchList()">查询</a>
                    </div>

                </div>


                <div class="col-md-3">
                    <div class="form-group form-buttom" id="twoInputs">
                        <input type="text" id="start_time" name="ordername" class="form-control" placeholder="开始时间" style="width: 48%; display: inline-block;" value="" autocomplete="off">
                        <input type="text" id="end_time" name="ordername" class="form-control" placeholder="结束时间" style="width: 48%; display: inline-block;" autocomplete="off">

                        <script>
                            $(document).ready(function () {
                                $('#start_time').datepicker({
                                    format: 'yyyy-mm-dd',
                                    autoclose: true
                                });

                                $('#end_time').datepicker({
                                    format: 'yyyy-mm-dd',
                                    autoclose: true
                                });
                            });
                        </script>
                    </div>
                </div>

                <div class="col-md-3">
                    <div class="form-group form-buttom">
                        <a class="btn btn-success" onclick="javascript:location.href=location.href;">
                            <svg t="1688237949108" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2211" width="16" height="16">
                                <path d="M512 192a298.24 298.24 0 0 0-245.162667 128 42.666667 42.666667 0 1 1-69.973333-48.810667 384.042667 384.042667 0 0 1 697.173333 180.48l36.608-34.645333a42.666667 42.666667 0 0 1 58.709334 61.952l-112.64 106.666667a42.666667 42.666667 0 0 1-71.253334-38.912A298.666667 298.666667 0 0 0 512 192z m0.170667 640a298.24 298.24 0 0 0 245.162666-128 42.666667 42.666667 0 1 1 69.973334 48.810667 384.042667 384.042667 0 0 1-697.173334-180.48l-36.650666 34.645333a42.666667 42.666667 0 0 1-58.666667-61.952l112.64-106.666667a42.666667 42.666667 0 0 1 71.253333 38.912 298.666667 298.666667 0 0 0 293.418667 354.730667z" fill="#14101C" p-id="2212"></path></svg>
                            刷新</a>
                        <%--<a class="btn btn-success" onclick="opennew()">
                                <svg t="1688237854283" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="15169" width="16" height="16">
                                    <path d="M725.333333 213.333333H298.666667a85.333333 85.333333 0 0 0-85.333334 85.333334v341.333333a42.666667 42.666667 0 1 1-85.333333 0V298.666667a170.666667 170.666667 0 0 1 170.666667-170.666667h426.666666a170.709333 170.709333 0 0 1 167.850667 139.733333v488.533334A170.709333 170.709333 0 0 1 725.333333 896H298.666667a170.538667 170.538667 0 0 1-144.469334-79.744l1.194667-0.682667a42.666667 42.666667 0 0 1 76.501333-37.034666c15.658667 19.584 39.765333 32.128 66.773334 32.128h426.666666a85.333333 85.333333 0 0 0 85.333334-85.333334V298.666667a85.333333 85.333333 0 0 0-85.333334-85.333334z m-213.333333 512a42.666667 42.666667 0 0 1-42.666667-42.666666v-128H341.333333a42.666667 42.666667 0 1 1 0-85.333334h128V341.333333a42.666667 42.666667 0 1 1 85.333334 0v128h128a42.666667 42.666667 0 1 1 0 85.333334h-128v128a42.666667 42.666667 0 0 1-42.666667 42.666666z" fill="#14101C" p-id="15170"></path></svg>&nbsp;添加</a>--%>

                        <a class="btn btn-success" onclick="report_exl()">
                            <svg t="1695489540076" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="5479" width="16" height="16">
                                <path d="M686.08 989.866667H165.546667c-51.2 0-92.16-40.96-92.16-92.16V126.293333c0-51.2 40.96-92.16 92.16-92.16h573.44c6.826667 0 11.946667 1.706667 17.066666 6.826667l187.733334 174.08c5.12 5.12 8.533333 11.946667 8.533333 18.773333v256c0 13.653333-11.946667 25.6-25.6 25.6s-25.6-11.946667-25.6-25.6V244.053333L728.746667 85.333333h-563.2C141.653333 85.333333 122.88 104.106667 122.88 126.293333v769.706667c0 22.186667 18.773333 40.96 40.96 40.96h520.533333c13.653333 0 25.6 11.946667 25.6 25.6S699.733333 989.866667 686.08 989.866667z" fill="#333333" p-id="5480"></path><path d="M919.893333 259.413333h-121.173333c-51.2 0-92.16-40.96-92.16-92.16V59.733333c0-13.653333 11.946667-25.6 25.6-25.6s25.6 11.946667 25.6 25.6v105.813334c0 22.186667 18.773333 40.96 40.96 40.96h121.173333c13.653333 0 25.6 11.946667 25.6 25.6s-11.946667 27.306667-25.6 27.306666zM500.053333 305.493333H208.213333c-13.653333 0-25.6-11.946667-25.6-25.6s11.946667-25.6 25.6-25.6h291.84c13.653333 0 25.6 11.946667 25.6 25.6s-11.946667 25.6-25.6 25.6zM616.106667 575.146667H208.213333c-13.653333 0-25.6-11.946667-25.6-25.6s11.946667-25.6 25.6-25.6h407.893334c13.653333 0 25.6 11.946667 25.6 25.6s-11.946667 25.6-25.6 25.6zM380.586667 440.32H208.213333c-13.653333 0-25.6-11.946667-25.6-25.6s11.946667-25.6 25.6-25.6h172.373334c13.653333 0 25.6 11.946667 25.6 25.6s-11.946667 25.6-25.6 25.6z" fill="#333333" p-id="5481"></path><path d="M914.773333 779.946667H600.746667c-13.653333 0-25.6-11.946667-25.6-25.6s11.946667-25.6 25.6-25.6h314.026666c13.653333 0 25.6 11.946667 25.6 25.6s-11.946667 25.6-25.6 25.6z" fill="#333333" p-id="5482"></path><path d="M831.146667 873.813333c-6.826667 0-13.653333-1.706667-18.773334-6.826666-10.24-10.24-10.24-25.6 0-35.84l75.093334-75.093334-75.093334-75.093333c-10.24-10.24-10.24-25.6 0-35.84 10.24-10.24 25.6-10.24 35.84 0l93.866667 93.866667c5.12 5.12 6.826667 11.946667 6.826667 18.773333s-3.413333 13.653333-6.826667 18.773333L848.213333 870.4c-5.12 1.706667-11.946667 3.413333-17.066666 3.413333z" fill="#333333" p-id="5483"></path><path d="M694.613333 824.32h-529.066666c-37.546667 0-68.266667-30.72-68.266667-68.266667V121.173333c0-37.546667 30.72-68.266667 68.266667-68.266666h529.066666c37.546667 0 68.266667 30.72 68.266667 68.266666v634.88c0 37.546667-30.72 68.266667-68.266667 68.266667z" fill="#333333" opacity=".3" p-id="5484"></path></svg>导出</a>

                    </div>
                </div>



            </div>


            <div style="display: flex; flex-wrap: wrap; margin-bottom: 20px;">
                <div class="timesel_lab">全部</div>
                <div class="timesel_lab">今天</div>
                <div class="timesel_lab">昨天</div>
                <div class="timesel_lab">本周</div>
                <div class="timesel_lab">上周</div>
                <div class="timesel_lab">本月</div>
                <div class="timesel_lab">上月</div>
            </div>


            <div style="margin-bottom: 10px;">
                <input type="checkbox" id="filter_th" />&nbsp;过滤托号
            </div>


            <div style="display: none;">
                <input type="checkbox" onclick="set_autoAlarm(this)" />&nbsp;自动监控订单（5s刷新一次）


                <div style="display: inline-block; font-weight: bold; background: #e5ded887; padding: 2px 18px; border-radius: 5px; color: #000; font-size: 20px;">
                    <span class="total_amount">0</span> 总金额<span style="font-size: 18px; background: linear-gradient(152deg, #ff5412d4, #f3cdbe); color: #fff8e0; border-radius: 6px; font-weight: 100; padding: 2px 9px; margin-left: 5px; font-weight: bold;"><span class="total_number">0</span> 总量</span>
                </div>

            </div>


            <br />


            <div class="table-responsive">
                <table class="table">
                    <thead>
                        <tr role="head">
                            <th sort="true" class="action-cell-left">
                                <input type="checkbox" onclick="javascript: select_all(this)" />#ID</th>

                            <th sort="true">订单编号</th>
                            <th sort="true">注册时间</th>
                            <th sort="true" class="">账号</th>
                            <th sort="true">姓名</th>
                            <th sort="true">每日可出借总次</th>
                            <th sort="true">当日剩余次数</th>
                            <th sort="true">商家名称</th>
                            <th sort="true">借款金额</th>
                            <th sort="true">借款期限</th>
                            <th sort="true">借款利息</th>
                            <th sort="true">预计还款</th>
                            <th sort="true">创建时间</th>
                            <th sort="true" class='action-cell '>操作</th>
                        </tr>
                    </thead>
                    <tbody id="list-display">
                    </tbody>
                </table>
            </div>


            <div class="layui-table-page">
                <div id="layui-table-page1">
                    <div class="layui-box layui-laypage layui-laypage-default" id="layui-laypage-1">
                        <div style="display: inline-block;" class="cpages">
                            <!--page grow -->
                        </div>
                        <span class="layui-laypage-skip">到第<input type="text" min="1" value="1" class="layui-input target-page" />页
                            <button type="button" class="layui-laypage-btn" onclick="javascript:pager_index=$('.target-page').val()-1;getPager();">确定</button></span>
                        <span class="layui-laypage-count">共 <span class="page_count"></span>条</span>
                        <span class="layui-laypage-limits">
                            <select class="s_pages" lay-ignore="" onchange="javascript:pager_size=$(this).val();getPager();">
                                <option value="10">10 条/页</option>
                                <option value="20" selected="">20 条/页</option>
                                <option value="30">30 条/页</option>
                                <option value="50">50 条/页</option>
                                <option value="100">100 条/页</option>
                                <option value="200">200 条/页</option>
                            </select></span>
                    </div>
                </div>
            </div>

        </div>
    </div>


    <script>
        var _action = 'store_orders';
        var _actionName = '';
    </script>
    <script id="item_classList" type="text/javascript">
        <%=ToJson(chelper.gdt("item_classList")) %>
    </script>
    <script>
        var item_classList = JSON.parse($('#item_classList').html());
        var classList = [];
        for (var i = 0; i < item_classList.length; i++) {
            classList.push([item_classList[i].id, item_classList[i].name]);
        }

        function opennew() {
            var modify_model = [];


            modify_model.push({ name: '选择分类', id: 'classId', data: classList, type: 'select' });
            modify_model.push({ name: '生成数量', id: 'number', value: '' });

            edit({ aname: 'admin', title: '添加' + _actionName, action: _action, id: '', data: modify_model });
        }
    </script>

    <noscript id="payer_ways"><%=uConfig.stcdata("payer_ways").Replace(" ", "") %></noscript>
    <script>
        var payer_ways_list = $('#payer_ways').html().split('\n');
        var payerWays = [];
        for (var i = 0; i < payer_ways_list.length; i++) {
            payerWays.push([payer_ways_list[i]]);
        }
    </script>

    <script>

        var current_json = [];
        var pager_size = $(".s_pages").val();
        var pager_index = 0;
        var pager_key = "";

        $("#pagesize").val(pager_key);
        function setPageSize(obj) {
            pager_index = 0;
            pager_key = $("#search-keyword").val();
            opt = $(obj).val();
            getPager();
        }

        function searchList() {
            opt = $("#opt1").val();
            pager_index = 0;
            pager_key = $("#search-keyword").val();
            getPager();
        }
        function getParam() {
            return { limit: pager_size, p: pager_index, page: _action, keyword: pager_key, start_time: $("#start_time").val(), end_time: $("#end_time").val(), state: "<%=Request.QueryString["state"] %>", group: "<%=Request.QueryString["group"] %>", evaluate: "<%=Request.QueryString["evaluate"] %>", filter_th: $('#filter_th').prop('checked'), typeid: $('#typeid').val() }
        }

        function getPager() {

            $.ajax({
                type: "POST",
                url: "../api/admin.aspx?do=page",
                data: getParam(),
                datatype: "json",
                success: function (json) {
                    $("#list-display").html("");
                    if (json.code == 1) {
                        var data = json.data.list;
                        current_json = data;
                        for (var i = 0; i < data.length; i++) {
                            var tr = "";
                            tr += "<tr>";
                            tr += ('<td class="action-cell-left"><input class="good_select" type="checkbox" data-id="' + data[i].id + '" id="checkid_' + data[i].id + '" />#' + data[i].id + '</td>');
                            tr += ("<td style=''>" + data[i].orderNo + "</td>");
                            tr += ("<td style=''>" + data[i].user_createTime + "</td>");
                            tr += ("<td class=''>" + ('<span style="background: #f3ecec;color: #956b0a;font-size: 12px;border-radius: 3px;padding: 1px 4px;margin-right: 5px;font-weight: bold;border: 1px solid #ccc;">' + (data[i].typename == 'th' ? '托号' : data[i].typename == 'user' ? '普通' : data[i].typename == 'super' ? '高级' : data[i].typename == 'reg' ? '新' : '其他') + '</span>') + (data[i].group_name == '-' ? '' : "<span class='button-style style-js'>" + data[i].group_name + "</span>") + data[i].phone + (data[i].lend_audit == 1 ? '<span style="background: #f3ecec;color: #956b0a;font-size: 12px;border-radius: 3px;padding: 1px 4px;margin-left: 5px;font-weight: bold;border: 1px solid #ccc;">审核</span>' : '') + (data[i].pass_order == 1 ? '<span style="background: #e7ffec;color: #4a9d19;font-size: 12px;border-radius: 3px;padding: 1px 4px;margin-left: 5px;font-weight: bold;border: 1px solid #5bb327;">审核通过</span>' : '') + "</td>");
                            tr += ("<td style=''>" + data[i].payment_name + "</td>");
                            tr += ("<td style=''>" + data[i].stock_lend_number + "</td>");
                            tr += ("<td style=''>" + (data[i].stock_lend_number - data[i].current_daily_lend_number) + "</td>");
                            tr += ("<td style=''>" + (data[i].lend_class == "" ? '' : '<strong style="color:#000;">[' + data[i].lend_class + ']</strong>') + data[i].name + "</td>");
                            tr += ("<td style='color:blue;'>" + data[i].amount + "</td>");
                            tr += ("<td style=''>" + data[i].days + "天</td>");
                            tr += ("<td style=''>" + '<span>' + ((parseFloat(data[i].amount) / 100 * parseFloat(data[i].interest)) * data[i].days).toFixed(2) + '</span>' + '<span style="color:gray;margin-left:5px;">' + data[i].interest + '%</span>' + '' + (data[i].bonus_amount == "" ? "" : '<div><span style="display: inline-block;color: #e1de32;/* font-weight: bold; */background: #272222e3;font-size: 12px;padding: 2px 10px;border-radius: 5px;margin-top: 5px;box-shadow: 2px 2px 8px #00000047;"><svg t="1701940579807" class="icon" viewBox="0 0 1036 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="5345" width="12" height="12"><path d="M23.210667 77.065481h61.895111a7.585185 7.585185 0 0 1 0 15.170371H23.210667a7.585185 7.585185 0 1 1 0-15.170371z" fill="#FFB400" p-id="5346"></path><path d="M54.158222 46.042074a7.585185 7.585185 0 0 1 7.585185 7.585185v61.895111a7.585185 7.585185 0 1 1-15.17037 0V53.778963a7.585185 7.585185 0 0 1 7.585185-7.736889z" fill="#FFB400" p-id="5347"></path><path d="M362.799407 117.266963l29.88563 7.964444a7.585185 7.585185 0 1 1-4.020148 15.170371l-29.88563-8.116148a7.585185 7.585185 0 1 1 4.020148-15.170371z" fill="#043EB8" p-id="5348"></path><path d="M417.943704 73.803852l7.964444 29.885629a7.585185 7.585185 0 1 1-15.17037 4.020149l-7.964445-30.340741a7.585185 7.585185 0 0 1 5.613037-9.102222 7.585185 7.585185 0 0 1 9.557334 5.537185z" fill="#2868F0" p-id="5349"></path><path d="M483.024593 99.745185L461.179259 121.362963a7.585185 7.585185 0 1 1-10.922666-10.922667l21.845333-21.845333a7.585185 7.585185 0 0 1 10.922667 10.922667z" fill="#8FB3FF" p-id="5350"></path><path d="M950.802963 114.232889a46.421333 46.421333 0 1 0 46.497185 46.497185 46.421333 46.421333 0 0 0-46.497185-46.497185z m0 77.368889a31.023407 31.023407 0 1 1 31.023407-31.023408 31.023407 31.023407 0 0 1-31.023407 31.023408zM773.688889 25.78963l-46.724741 18.887111V106.192593l46.724741-18.507852z" fill="#FFB400" p-id="5351"></path><path d="M672.730074 87.684741L719.227259 106.192593V44.676741L672.730074 25.78963z m100.731259-68.266667L723.626667 0l-50.896593 17.59763 50.744889 21.541926z" fill="#FFD200" p-id="5352"></path><path d="M928.57837 386.844444l-464.289185-62.198518v245.380741l464.289185 48.696889z" fill="#FFA70B" p-id="5353"></path><path d="M92.842667 404.973037l371.674074-80.327111v278.603852l-371.674074 80.327111z" fill="#FFBB43" p-id="5354"></path><path d="M526.184296 497.815704L928.57837 386.844444v526.184297L526.184296 1024V497.815704z" fill="#FFA70B" p-id="5355"></path><path d="M92.842667 404.973037l433.341629 92.842667v523.377777l-433.341629-105.434074z" fill="#FFBB43" p-id="5356"></path><path d="M526.184296 496.981333L92.842667 404.973037 0 590.65837l448.815407 105.434074zM526.184296 497.815704L928.57837 386.844444l108.392297 185.685334-386.844445 126.748444z" fill="#FFCF64" p-id="5357"></path><path d="M743.879111 400.270222s17.21837-108.544 107.102815-162.62637c0 0 7.585185 8.874667 23.514074 28.975407 0 0-40.884148-6.447407-130.616889 133.650963z" fill="#8FB3FF" p-id="5358"></path><path d="M526.184296 395.870815s1.744593-59.467852 45.511111-193.649778c0 0 7.585185 8.874667 23.514074 28.975407-0.303407 0-16.687407 14.260148-69.025185 164.674371z" fill="#FF4400" p-id="5359"></path><path d="M374.859852 385.099852S326.162963 241.133037 205.861926 176.128c0 0-20.024889 28.065185-42.021926 36.939852-0.075852 0 138.05037 48.090074 211.019852 172.032z" fill="#FFD200" p-id="5360"></path><path d="M778.619259 788.859259a12.212148 12.212148 0 0 1-8.192-2.654815 17.901037 17.901037 0 0 1 3.792593 8.419556 49.910519 49.910519 0 0 1-8.192 11.377778 31.781926 31.781926 0 0 1 9.329778-5.840593 19.569778 19.569778 0 0 1 3.261629 9.860741 42.097778 42.097778 0 0 1 2.199704-12.136296 27.685926 27.685926 0 0 1 9.936593-5.30963 13.274074 13.274074 0 0 1-8.267852-1.592889 43.766519 43.766519 0 0 1 3.034074-12.591407 51.806815 51.806815 0 0 1-6.902519 10.467555z" fill="#00A83A" p-id="5361"></path><path d="M770.275556 786.204444a12.212148 12.212148 0 0 0 8.192 2.654815 51.806815 51.806815 0 0 0 6.902518-10.467555 51.048296 51.048296 0 0 1 2.88237-6.144 51.048296 51.048296 0 0 0 4.323556-16.535704v-7.585185c0-17.21837 0-35.043556 14.411852-52.489482a51.882667 51.882667 0 0 1 21.693629-14.715259l1.744593-0.758518h0.455111c-10.391704-5.840593-29.88563 0.758519-45.511111 15.17037a48.848593 48.848593 0 0 0-6.144 6.674963c-1.137778 1.441185-1.137778 1.441185-3.565037 1.365333h-3.565037a66.597926 66.597926 0 0 0-29.582222 13.577482 74.486519 74.486519 0 0 0-15.170371 16.308148 12.515556 12.515556 0 0 0-2.048 3.868444c15.928889-11.529481 26.775704-10.315852 31.630223-5.233777 6.371556 6.674963 6.599111 18.432 6.750814 32.161185v7.585185a32.237037 32.237037 0 0 0 4.247704 12.136296 19.949037 19.949037 0 0 1 2.351408 2.427259z" fill="#E40012" p-id="5362"></path><path d="M853.029926 728.177778c-1.061926-0.455111-1.061926-0.455111-0.682667-3.716741 0-1.137778 0-2.654815 0.455111-4.778667a55.902815 55.902815 0 0 0-7.585185-29.809777 19.873185 19.873185 0 0 0-10.846815-8.874667c-1.137778 0-2.427259-0.379259-2.88237 0a43.463111 43.463111 0 0 1 0.455111 38.608593 101.18637 101.18637 0 0 1-26.851555 32.38874l-6.447408 5.916445a87.684741 87.684741 0 0 0-10.012444 13.880889 51.048296 51.048296 0 0 0-2.882371 6.144 43.766519 43.766519 0 0 0-3.034074 12.591407 13.274074 13.274074 0 0 0 8.267852 1.592889 17.142519 17.142519 0 0 1 4.247704-0.682667 30.340741 30.340741 0 0 0 12.667259-6.144l6.219852-5.537185c13.956741-12.591407 28.368593-25.562074 40.808296-18.811259 5.840593 3.185778 7.585185 10.543407 9.329778 18.356148l0.379259 1.744593v0.455111-0.606815c5.992296-18.128593 2.958222-39.518815-7.054222-49.682963a12.515556 12.515556 0 0 0-4.551111-3.034074z" fill="#FCD000" p-id="5363"></path><path d="M790.755556 792.651852a27.685926 27.685926 0 0 0-9.936593 5.309629 42.097778 42.097778 0 0 0-2.199704 12.136297 30.795852 30.795852 0 0 1 0 4.702815 23.665778 23.665778 0 0 0 3.716741 12.970666l3.792593 4.247704c8.647111 9.40563 17.673481 19.114667 10.846814 40.656593a81.08563 81.08563 0 0 1-15.701333 26.168888l-1.441185 1.820445v0.455111h0.455111a33.071407 33.071407 0 0 0 3.792593-1.744593 99.669333 99.669333 0 0 0 37.015703-44.297481 51.579259 51.579259 0 0 0 3.185778-8.722963c0.455111-1.744593 0.455111-1.744593 3.109926-3.716741l3.792593-2.88237a126.900148 126.900148 0 0 0 24.803555-31.93363 81.161481 81.161481 0 0 0 8.419556-21.769481c0-1.668741 0.606815-3.565037 0-3.868445-11.301926 19.493926-23.514074 27.83763-31.175111 29.127111-10.164148 1.668741-16.232296-4.702815-23.286519-11.984592l-4.096-4.247704a20.935111 20.935111 0 0 0-10.391704-3.109926 17.142519 17.142519 0 0 0-4.702814 0.682667z" fill="#F0EFF0" p-id="5364"></path><path d="M778.543407 810.17363a19.569778 19.569778 0 0 0-3.261629-9.860741 31.781926 31.781926 0 0 0-9.329778 5.840592 40.656593 40.656593 0 0 1-4.096 3.792593 57.116444 57.116444 0 0 0-10.922667 14.336l-3.868444 8.267852c-8.495407 18.507852-17.294222 37.925926-33.98163 44.449185a21.845333 21.845333 0 0 1-19.26637-1.972148l-1.289482-0.606815v0.455111a26.927407 26.927407 0 0 0 32.768 20.783408 23.210667 23.210667 0 0 0 6.674963-2.123852c1.365333-0.682667 1.365333-0.682667 2.654815 1.441185l1.896296 2.958222a31.326815 31.326815 0 0 0 22.755556 9.784889 34.891852 34.891852 0 0 0 15.625482-4.096l1.061925-0.606815a9.253926 9.253926 0 0 0 3.034075-2.503111A19.721481 19.721481 0 0 1 758.518519 879.881481c0-12.363852 5.537185-25.182815 12.212148-39.973925l3.868444-8.57126a73.879704 73.879704 0 0 0 3.640889-16.232296 30.795852 30.795852 0 0 0 0.303407-4.93037z" fill="#43A6B3" p-id="5365"></path><path d="M766.103704 806.153481a49.910519 49.910519 0 0 0 8.192-11.377777 17.901037 17.901037 0 0 0-3.792593-8.419556 19.949037 19.949037 0 0 0-2.427259-2.578963 14.032593 14.032593 0 0 0-10.391704-3.944296l-6.219852 0.83437c-13.956741 2.123852-28.444444 4.247704-32.009481-13.12237a60.681481 60.681481 0 0 1 3.868444-27.306667l0.606815-2.199703v-0.606815l-0.379259 0.455111a98.607407 98.607407 0 0 0-20.707556 58.557629 26.624 26.624 0 0 0 0.986074 7.585186c0 1.289481 0 1.289481-1.441185 4.551111l-2.654815 4.778666a117.646222 117.646222 0 0 0-10.543407 37.925926 42.856296 42.856296 0 0 0 1.896296 18.887111c0.455111 1.21363 1.137778 2.427259 1.668741 2.275556a69.252741 69.252741 0 0 1 18.735407-41.79437 51.730963 51.730963 0 0 1 30.947556-12.818963l6.523259-1.137778a48.924444 48.924444 0 0 0 12.591408-6.826667 40.656593 40.656593 0 0 0 4.551111-3.716741z" fill="#534F50" p-id="5366"></path><path d="M777.178074 691.617185a20.100741 20.100741 0 0 0 9.481482-16.535704c0-6.219852-4.475259-8.950519-9.784889-6.068148a20.100741 20.100741 0 0 0-9.481482 16.459852c0.151704 6.295704 4.475259 9.02637 9.784889 6.144z" fill="#E40012" p-id="5367"></path><path d="M880.260741 700.871111a5.30963 5.30963 0 0 0-4.854519-4.020148 10.467556 10.467556 0 0 0-7.585185 3.26163 21.238519 21.238519 0 0 0-5.537185 8.571259 14.108444 14.108444 0 0 0-0.455111 8.874667 6.219852 6.219852 0 0 0 0.83437 1.441185 5.006222 5.006222 0 0 0 3.944296 2.275555 7.964444 7.964444 0 0 0 4.323556-0.986074 14.563556 14.563556 0 0 0 2.958222-2.123852 18.735407 18.735407 0 0 0 6.371556-17.294222z" fill="#FCD000" p-id="5368"></path><path d="M831.715556 849.540741a20.707556 20.707556 0 0 0-3.792593 9.405629 9.178074 9.178074 0 0 0 1.820444 7.585186 6.295704 6.295704 0 0 0 7.585186 0.455111 16.004741 16.004741 0 0 0 3.868444-3.185778 19.190519 19.190519 0 0 0 1.668741-1.896296 14.639407 14.639407 0 0 0 1.972148-16.839112c-2.88237-3.489185-8.874667-1.365333-13.12237 4.47526z" fill="#F0EFF0" p-id="5369"></path><path d="M727.570963 905.291852a8.192 8.192 0 0 0-7.130074 1.744592 19.949037 19.949037 0 0 0-6.295704 7.585186 16.914963 16.914963 0 0 0-1.744592 9.329777 6.068148 6.068148 0 0 0 3.868444 5.385482 5.764741 5.764741 0 0 0 1.744593 0 8.040296 8.040296 0 0 0 4.020148-0.986074l1.441185-0.910222a19.949037 19.949037 0 0 0 6.295704-7.585186 16.914963 16.914963 0 0 0 1.744592-9.329777 6.068148 6.068148 0 0 0-3.944296-5.233778z" fill="#43A6B3" p-id="5370"></path><path d="M693.968593 807.670519a11.45363 11.45363 0 0 0-0.682667-8.267852 5.233778 5.233778 0 0 0-5.688889-2.578963 13.046519 13.046519 0 0 0-7.585185 4.626963 21.466074 21.466074 0 0 0-4.702815 9.102222 11.45363 11.45363 0 0 0 0.682667 8.267852 5.233778 5.233778 0 0 0 5.688889 2.578963 9.481481 9.481481 0 0 0 2.958222-1.061926 16.004741 16.004741 0 0 0 4.399407-3.640889 21.466074 21.466074 0 0 0 4.930371-9.02637z" fill="#534F50" p-id="5371"></path></svg> ' + parseFloat(data[i].bonus_amount).toFixed(2) + '<span style="color: #d5e5bb;margin: 0 5px;">[' + data[i].bonus_number + ']</span> 利息</span></div>') + "</td>");
                            tr += ("<td style=''>" + (parseFloat(data[i].amount) + (parseFloat(data[i].amount) / 100 * parseFloat(data[i].interest)) * data[i].days).toFixed(2) + "</td>");
                            tr += ("<td class=''>"
                                + (data[i].lend_time == "" ? "<div>创建 " + data[i].create_time + "</div>" : "")
                                + (data[i].lend_time == "" ? "" : "<div>出借 " + data[i].lend_time + "</div>")

                                + (data[i].expire_time == "" ? "" : "<div class='expire_time'  data-time='" + data[i].expire_time + "'>到期 " + data[i].expire_time + "</div>" + (data[i].expire_time == "" ? "<span style='color:gray;'>无</span>" : "<span class='exp_countdown'></span>"))
                                + "</td>");

                            var modify_model = new Array();
                            //modify_model.push({ name: '商品图片', id: 'imgurl', data: [data[i].imgurl], type: 'img' });

                            //tr += ("<td>"
                            //    + "<button type='button' class='el-button  el-button--default el-button--mini' onclick='edit(" + edtext({ aname: 'admin', title: _actionName + "修改", action: _action, id: data[i].id, data: modify_model }) + ")'><span><i class='fa fa-edit'></i></span></button>"
                            //    + "<button type='button' class='el-button  el-button--default el-button--mini' onclick=\"event_create({'aname':'admin','name':'" + _action + "','event':'delete','id':" + data[i].id + "})\"><span><i class='fa fa-remove'></i></span></button>"
                            //    + "</td>");




                            tr += ("<td class='action-cell '>"
                                + ' <div class="card-toolbar">'
                                + (data[i].state == 1000 ? '&nbsp;<a style=" color: #5789f5!important;" class="card-button common-button"  onclick="' + "get_action({name:'shop_store_orders',data:{id:" + data[i].id + "},n:'结束项目',e:'结束项目 " + data[i].orderNo + " 并返还本金" + "'});" + '"><svg t="" class="icon limit_color" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="15960" width="16" height="16" style="position:relative;top:3px;"><path d="M859.04 439.52v364.48c0 13.28-10.72 24-24 24H478.72V463.52c0-13.28 10.72-24 24-24h356.32z" fill="#B3C8FE" p-id="15961"></path><path d="M704.32 196H251.04c-13.28 0-24 10.72-24 24v453.28c0 13.28 10.72 24 24 24h130.72c13.28 0 24-10.72 24-24V374.72h298.56c13.28 0 24-10.72 24-24v-130.72c0-13.28-10.72-24-24-24z m-322.56 130.72c-13.28 0-24 10.72-24 24v298.56h-82.72V244h405.28v82.72H381.76z" fill="#5186F5" p-id="15962"></path><path d="M835.04 326.72H381.76c-13.28 0-24 10.72-24 24v453.28c0 13.28 10.72 24 24 24h453.28c13.28 0 24-10.72 24-24V350.72c0-13.28-10.72-24-24-24z m-24 453.28H405.76V374.72h405.28v405.28z" fill="#5186F5" p-id="15963"></path><path d="M487.584 664.416l207.952-207.952 33.92 33.952-207.936 207.936z" fill="#5186F5" p-id="15964"></path><path d="M487.472 490.304l33.936-33.952L729.36 664.32l-33.952 33.936z" fill="#5186F5" p-id="15965"></path></svg>&nbsp;结束</a>' : '')
                                + (data[i].state == 1000 && data[i].isfreeze == 0 ? '&nbsp;<a style=" color: red!important;" class="card-button common-button"  onclick="' + "get_action({name:'freeze_store_orders',data:{id:" + data[i].id + ",isfreeze:1},n:'冻结项目',e:'冻结项目 " + data[i].orderNo + "<br>（冻结后不派发利息不返还本金）" + "'});" + '"><svg t="" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="40720" width="16" height="16"  style="position:relative;top:3px;"><path d="M785 559.4h-81c6.6 0 12 5.4 12 12v232c0 6.6-5.4 12-12 12h81c6.6 0 12-5.4 12-12v-232c0-6.6-5.4-12-12-12z" fill="#2EA7E0" p-id="40721"></path><path d="M716 803.4v-232c0-6.6-5.4-12-12-12H322c-6.6 0-12 5.4-12 12v232c0 6.6 5.4 12 12 12h382c6.6 0 12-5.4 12-12z" fill="#F7F8F8" p-id="40722"></path><path d="M384 431.4V558h29V431.4c0-65.8 49.6-120 113.5-127.2-4.8-0.5-9.6-0.8-14.5-0.8-70.7 0-128 57.3-128 128zM541 239.4c-4.9 0-9.7 0.2-14.5 0.5 99.3 7.4 177.5 90.3 177.5 191.5V558h29V431.4c0-106-85.9-192-192-192z" fill="#2EA7E0" p-id="40723"></path><path d="M526.5 240C427.3 247.4 349 330.3 349 431.4V558h35V431.4c0-70.7 57.3-128 128-128 4.9 0 9.7 0.3 14.5 0.8 4.8-0.5 9.6-0.8 14.5-0.8 70.7 0 128 57.3 128 128V558h35V431.4c0-101.1-78.2-184-177.5-191.4z" fill="#F7F8F8" p-id="40724"></path><path d="M487.2 671.7m-16 0a16 16 0 1 0 32 0 16 16 0 1 0-32 0Z" fill="#3E3A39" p-id="40725"></path><path d="M455.2 698.4m-16 0a16 16 0 1 0 32 0 16 16 0 1 0-32 0Z" fill="#EF6676" p-id="40726"></path><path d="M626.9 698.4m-16 0a16 16 0 1 0 32 0 16 16 0 1 0-32 0Z" fill="#EF6676" p-id="40727"></path><path d="M594.9 671.7m-16 0a16 16 0 1 0 32 0 16 16 0 1 0-32 0Z" fill="#3E3A39" p-id="40728"></path><path d="M566.3 682.4h-50.6c-3.9 0-6.7 3.7-5.8 7.5 1.8 7.5 6.3 14 12.3 18.4 5.3 3.8 11.8 6.1 18.8 6.1s13.5-2.3 18.8-6.1c6.1-4.4 10.5-10.9 12.3-18.4 1-3.9-1.9-7.5-5.8-7.5z" fill="#3E3A39" p-id="40729"></path><path d="M541 698.4c-7 0-13.3 3.2-17.5 8.2-0.8 0.9-0.5 2.3 0.5 3 4.9 3.1 10.7 4.9 17 4.9 6.2 0 12.1-1.8 17-4.9 1-0.6 1.3-2 0.5-3-4.2-5-10.5-8.2-17.5-8.2z" fill="#E4847F" p-id="40730"></path><path d="M842.4 492h-13.6v-13.6c0-3.4-2.8-6.2-6.2-6.2s-6.2 2.8-6.2 6.2V492h-13.6c-3.4 0-6.2 2.8-6.2 6.2 0 3.4 2.8 6.2 6.2 6.2h13.6V518c0 3.4 2.8 6.2 6.2 6.2s6.2-2.8 6.2-6.2v-13.6h13.6c3.4 0 6.2-2.8 6.2-6.2 0-3.4-2.7-6.2-6.2-6.2zM790.1 383.6c11.6 0 21-9.4 21-21s-9.4-21-21-21-21 9.4-21 21 9.4 21 21 21z m0-32c6.1 0 11 4.9 11 11s-4.9 11-11 11-11-4.9-11-11 4.9-11 11-11zM246.6 476.5c-2.3 0-4.2 1.9-4.2 4.2v13.4c0 2.3 1.9 4.2 4.2 4.2 2.3 0 4.2-1.9 4.2-4.2v-13.4c-0.1-2.3-1.9-4.2-4.2-4.2zM246.6 516.6c-2.3 0-4.2 1.9-4.2 4.2v13.4c0 2.3 1.9 4.2 4.2 4.2 2.3 0 4.2-1.9 4.2-4.2v-13.4c-0.1-2.3-1.9-4.2-4.2-4.2zM259 513.9c-1.6-1.6-4.3-1.6-5.9 0-1.6 1.6-1.6 4.3 0 5.9l9.4 9.4c0.8 0.8 1.9 1.2 3 1.2s2.1-0.4 3-1.2c1.6-1.6 1.6-4.3 0-5.9l-9.5-9.4zM230.6 485.6c-1.6-1.6-4.3-1.6-5.9 0-1.6 1.6-1.6 4.3 0 5.9l9.4 9.4c0.8 0.8 1.9 1.2 3 1.2s2.1-0.4 3-1.2c1.6-1.6 1.6-4.3 0-5.9l-9.5-9.4zM234.2 513.9l-9.4 9.4c-1.6 1.6-1.6 4.3 0 5.9 0.8 0.8 1.9 1.2 3 1.2s2.1-0.4 3-1.2l9.4-9.4c1.6-1.6 1.6-4.3 0-5.9-1.8-1.6-4.4-1.6-6 0zM256 502.2c1.1 0 2.1-0.4 3-1.2l9.4-9.4c1.6-1.6 1.6-4.3 0-5.9-1.6-1.6-4.3-1.6-5.9 0l-9.4 9.4c-1.6 1.6-1.6 4.3 0 5.9 0.8 0.7 1.9 1.2 2.9 1.2zM277.5 507.4c0-2.3-1.9-4.2-4.2-4.2h-13.4c-2.3 0-4.2 1.9-4.2 4.2 0 2.3 1.9 4.2 4.2 4.2h13.4c2.3 0 4.2-1.9 4.2-4.2zM237.4 507.4c0-2.3-1.9-4.2-4.2-4.2h-13.4c-2.3 0-4.2 1.9-4.2 4.2 0 2.3 1.9 4.2 4.2 4.2h13.4c2.3 0 4.2-1.9 4.2-4.2z" fill="#036EB8" p-id="40731"></path><path d="M293.9 411.4m-8.9 0a8.9 8.9 0 1 0 17.8 0 8.9 8.9 0 1 0-17.8 0Z" fill="#036EB8" p-id="40732"></path><path d="M785 549.4h-42v-118c0-111.4-90.6-202-202-202-39.1 0-77 11.2-109.6 32.3-4.6 3-6 9.2-3 13.8 3 4.6 9.2 6 13.8 3 29.4-19 63.6-29.1 98.8-29.1 100.4 0 182 81.6 182 182V548h-44V431.4c0-17.8-3.4-35.2-10-51.6-2.1-5.1-7.9-7.6-13-5.5-5.1 2.1-7.6 7.9-5.5 13 5.6 14 8.5 28.8 8.5 44.1v118H423v-118c0-65.1 52.9-118 118-118 38.3 0 74.4 18.7 96.5 50.1 3.2 4.5 9.4 5.6 13.9 2.4s5.6-9.4 2.4-13.9c-25.9-36.7-68.1-58.6-112.9-58.6-76.1 0-138 61.9-138 138V548h-44V431.4c0-26.2 5.5-51.6 16.2-75.3 2.3-5 0.1-11-5-13.2-5-2.3-11-0.1-13.2 5-12 26.3-18 54.4-18 83.5v118h-42c-12.1 0-22 9.9-22 22v232c0 12.1 9.9 22 22 22h488c12.1 0 22-9.9 22-22v-97c0-5.5-4.5-10-10-10s-10 4.5-10 10v97c0 1.1-0.9 2-2 2H297c-1.1 0-2-0.9-2-2v-232c0-1.1 0.9-2 2-2h488c1.1 0 2 0.9 2 2V666c0 5.5 4.5 10 10 10s10-4.5 10-10v-94.5c0-12.2-9.8-22.1-22-22.1z" fill="#036EB8" p-id="40733"></path><path d="M384 320.5c1.8 1.4 4 2.1 6.2 2.1 3 0 5.9-1.3 7.9-3.8 5.1-6.5 10.8-12.8 16.8-18.6 4-3.8 4.1-10.2 0.3-14.1-3.8-4-10.2-4.1-14.1-0.3-6.7 6.4-12.9 13.3-18.6 20.6-3.6 4.4-2.9 10.7 1.5 14.1z" fill="#036EB8" p-id="40734"></path></svg>&nbsp;冻结</a>' : '')
                                + (data[i].state == 1000 && data[i].isfreeze == 1 ? '&nbsp;<a style=" color: green!important;" class="card-button common-button"  onclick="' + "get_action({name:'freeze_store_orders',data:{id:" + data[i].id + ",isfreeze:0},n:'解除冻结项目',e:'解除冻结项目 " + data[i].orderNo + "<br>（解除后正常派发利息且返还本金）" + "'});" + '"><svg t="" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="40720" width="16" height="16"  style="position:relative;top:3px;"><path d="M785 559.4h-81c6.6 0 12 5.4 12 12v232c0 6.6-5.4 12-12 12h81c6.6 0 12-5.4 12-12v-232c0-6.6-5.4-12-12-12z" fill="#2EA7E0" p-id="40721"></path><path d="M716 803.4v-232c0-6.6-5.4-12-12-12H322c-6.6 0-12 5.4-12 12v232c0 6.6 5.4 12 12 12h382c6.6 0 12-5.4 12-12z" fill="#F7F8F8" p-id="40722"></path><path d="M384 431.4V558h29V431.4c0-65.8 49.6-120 113.5-127.2-4.8-0.5-9.6-0.8-14.5-0.8-70.7 0-128 57.3-128 128zM541 239.4c-4.9 0-9.7 0.2-14.5 0.5 99.3 7.4 177.5 90.3 177.5 191.5V558h29V431.4c0-106-85.9-192-192-192z" fill="#2EA7E0" p-id="40723"></path><path d="M526.5 240C427.3 247.4 349 330.3 349 431.4V558h35V431.4c0-70.7 57.3-128 128-128 4.9 0 9.7 0.3 14.5 0.8 4.8-0.5 9.6-0.8 14.5-0.8 70.7 0 128 57.3 128 128V558h35V431.4c0-101.1-78.2-184-177.5-191.4z" fill="#F7F8F8" p-id="40724"></path><path d="M487.2 671.7m-16 0a16 16 0 1 0 32 0 16 16 0 1 0-32 0Z" fill="#3E3A39" p-id="40725"></path><path d="M455.2 698.4m-16 0a16 16 0 1 0 32 0 16 16 0 1 0-32 0Z" fill="#EF6676" p-id="40726"></path><path d="M626.9 698.4m-16 0a16 16 0 1 0 32 0 16 16 0 1 0-32 0Z" fill="#EF6676" p-id="40727"></path><path d="M594.9 671.7m-16 0a16 16 0 1 0 32 0 16 16 0 1 0-32 0Z" fill="#3E3A39" p-id="40728"></path><path d="M566.3 682.4h-50.6c-3.9 0-6.7 3.7-5.8 7.5 1.8 7.5 6.3 14 12.3 18.4 5.3 3.8 11.8 6.1 18.8 6.1s13.5-2.3 18.8-6.1c6.1-4.4 10.5-10.9 12.3-18.4 1-3.9-1.9-7.5-5.8-7.5z" fill="#3E3A39" p-id="40729"></path><path d="M541 698.4c-7 0-13.3 3.2-17.5 8.2-0.8 0.9-0.5 2.3 0.5 3 4.9 3.1 10.7 4.9 17 4.9 6.2 0 12.1-1.8 17-4.9 1-0.6 1.3-2 0.5-3-4.2-5-10.5-8.2-17.5-8.2z" fill="#E4847F" p-id="40730"></path><path d="M842.4 492h-13.6v-13.6c0-3.4-2.8-6.2-6.2-6.2s-6.2 2.8-6.2 6.2V492h-13.6c-3.4 0-6.2 2.8-6.2 6.2 0 3.4 2.8 6.2 6.2 6.2h13.6V518c0 3.4 2.8 6.2 6.2 6.2s6.2-2.8 6.2-6.2v-13.6h13.6c3.4 0 6.2-2.8 6.2-6.2 0-3.4-2.7-6.2-6.2-6.2zM790.1 383.6c11.6 0 21-9.4 21-21s-9.4-21-21-21-21 9.4-21 21 9.4 21 21 21z m0-32c6.1 0 11 4.9 11 11s-4.9 11-11 11-11-4.9-11-11 4.9-11 11-11zM246.6 476.5c-2.3 0-4.2 1.9-4.2 4.2v13.4c0 2.3 1.9 4.2 4.2 4.2 2.3 0 4.2-1.9 4.2-4.2v-13.4c-0.1-2.3-1.9-4.2-4.2-4.2zM246.6 516.6c-2.3 0-4.2 1.9-4.2 4.2v13.4c0 2.3 1.9 4.2 4.2 4.2 2.3 0 4.2-1.9 4.2-4.2v-13.4c-0.1-2.3-1.9-4.2-4.2-4.2zM259 513.9c-1.6-1.6-4.3-1.6-5.9 0-1.6 1.6-1.6 4.3 0 5.9l9.4 9.4c0.8 0.8 1.9 1.2 3 1.2s2.1-0.4 3-1.2c1.6-1.6 1.6-4.3 0-5.9l-9.5-9.4zM230.6 485.6c-1.6-1.6-4.3-1.6-5.9 0-1.6 1.6-1.6 4.3 0 5.9l9.4 9.4c0.8 0.8 1.9 1.2 3 1.2s2.1-0.4 3-1.2c1.6-1.6 1.6-4.3 0-5.9l-9.5-9.4zM234.2 513.9l-9.4 9.4c-1.6 1.6-1.6 4.3 0 5.9 0.8 0.8 1.9 1.2 3 1.2s2.1-0.4 3-1.2l9.4-9.4c1.6-1.6 1.6-4.3 0-5.9-1.8-1.6-4.4-1.6-6 0zM256 502.2c1.1 0 2.1-0.4 3-1.2l9.4-9.4c1.6-1.6 1.6-4.3 0-5.9-1.6-1.6-4.3-1.6-5.9 0l-9.4 9.4c-1.6 1.6-1.6 4.3 0 5.9 0.8 0.7 1.9 1.2 2.9 1.2zM277.5 507.4c0-2.3-1.9-4.2-4.2-4.2h-13.4c-2.3 0-4.2 1.9-4.2 4.2 0 2.3 1.9 4.2 4.2 4.2h13.4c2.3 0 4.2-1.9 4.2-4.2zM237.4 507.4c0-2.3-1.9-4.2-4.2-4.2h-13.4c-2.3 0-4.2 1.9-4.2 4.2 0 2.3 1.9 4.2 4.2 4.2h13.4c2.3 0 4.2-1.9 4.2-4.2z" fill="#036EB8" p-id="40731"></path><path d="M293.9 411.4m-8.9 0a8.9 8.9 0 1 0 17.8 0 8.9 8.9 0 1 0-17.8 0Z" fill="#036EB8" p-id="40732"></path><path d="M785 549.4h-42v-118c0-111.4-90.6-202-202-202-39.1 0-77 11.2-109.6 32.3-4.6 3-6 9.2-3 13.8 3 4.6 9.2 6 13.8 3 29.4-19 63.6-29.1 98.8-29.1 100.4 0 182 81.6 182 182V548h-44V431.4c0-17.8-3.4-35.2-10-51.6-2.1-5.1-7.9-7.6-13-5.5-5.1 2.1-7.6 7.9-5.5 13 5.6 14 8.5 28.8 8.5 44.1v118H423v-118c0-65.1 52.9-118 118-118 38.3 0 74.4 18.7 96.5 50.1 3.2 4.5 9.4 5.6 13.9 2.4s5.6-9.4 2.4-13.9c-25.9-36.7-68.1-58.6-112.9-58.6-76.1 0-138 61.9-138 138V548h-44V431.4c0-26.2 5.5-51.6 16.2-75.3 2.3-5 0.1-11-5-13.2-5-2.3-11-0.1-13.2 5-12 26.3-18 54.4-18 83.5v118h-42c-12.1 0-22 9.9-22 22v232c0 12.1 9.9 22 22 22h488c12.1 0 22-9.9 22-22v-97c0-5.5-4.5-10-10-10s-10 4.5-10 10v97c0 1.1-0.9 2-2 2H297c-1.1 0-2-0.9-2-2v-232c0-1.1 0.9-2 2-2h488c1.1 0 2 0.9 2 2V666c0 5.5 4.5 10 10 10s10-4.5 10-10v-94.5c0-12.2-9.8-22.1-22-22.1z" fill="#036EB8" p-id="40733"></path><path d="M384 320.5c1.8 1.4 4 2.1 6.2 2.1 3 0 5.9-1.3 7.9-3.8 5.1-6.5 10.8-12.8 16.8-18.6 4-3.8 4.1-10.2 0.3-14.1-3.8-4-10.2-4.1-14.1-0.3-6.7 6.4-12.9 13.3-18.6 20.6-3.6 4.4-2.9 10.7 1.5 14.1z" fill="#036EB8" p-id="40734"></path></svg>&nbsp;解除冻结</a>' : '')

                                + (data[i].state == 0 ? '&nbsp;<a class="card-button common-button" ' + "onclick=\"event_create({'aname':'admin','name':'" + _action + "','event':'delete','id':" + data[i].id + "})\"" + '><svg t="1688237896673" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="1699" width="16" height="16" style="position:relative;top:3px;"><path d="M612.693333 170.666667l8.533334 42.666666H402.773333l8.533334-42.666666h201.386666z m-293.632 25.941333a86.186667 86.186667 0 0 0-1.706666 16.725333H170.666667a42.666667 42.666667 0 0 0 0 85.333334h682.666666a42.666667 42.666667 0 1 0 0-85.333334h-146.730666c0-5.504-0.554667-11.093333-1.706667-16.725333l-8.533333-42.666667A85.333333 85.333333 0 0 0 612.778667 85.333333H411.306667a85.333333 85.333333 0 0 0-83.712 68.608l-8.533334 42.666667zM426.666667 469.333333a42.666667 42.666667 0 0 0-42.666667 42.666667v170.666667a42.666667 42.666667 0 1 0 85.333333 0v-170.666667a42.666667 42.666667 0 0 0-42.666666-42.666667z m128 42.666667a42.666667 42.666667 0 1 1 85.333333 0v170.666667a42.666667 42.666667 0 1 1-85.333333 0v-170.666667zM255.402667 384A42.666667 42.666667 0 0 1 298.666667 426.069333L301.738667 640H301.653333a42.666667 42.666667 0 1 1-85.290666-2.176l-2.986667-210.56A42.666667 42.666667 0 0 1 255.36 384z m51.925333 396.586667h0.042667A103.381333 103.381333 0 0 0 406.698667 853.333333h210.602666c54.186667 0 98.56-40.789333 103.210667-92.757333L725.333333 426.069333a42.666667 42.666667 0 0 1 85.333334 1.194667l-4.864 337.365333-0.128 1.365334C798.08 863.872 715.562667 938.666667 617.301333 938.666667H406.698667a188.885333 188.885333 0 0 1-180.096-130.389334 42.666667 42.666667 0 0 1 80.725333-27.648z" fill="#14101C" p-id="1700"></path></svg>&nbsp;删除</a>' : '')
                                + (data[i].state == 1000 && data[i].lend_audit == 1 && data[i].pass_order != 1 ? '&nbsp;<a class="card-button common-button" ' + "onclick=\"event_create({'aname':'admin','name':'" + _action + "','event':'lend_order_pass','id':" + data[i].id + "})\"" + '><svg t="1701718051407" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="4012" width="16" height="16" style="position:relative;top:3px;"><path d="M782.848 728.576m-185.856 0a185.856 185.856 0 1 0 371.712 0 185.856 185.856 0 1 0-371.712 0Z" fill="#FFE200" p-id="4013"></path><path d="M194.56 166.4H128c-16.896 0-30.72-13.824-30.72-30.72s13.824-30.72 30.72-30.72h66.56c16.896 0 30.72 13.824 30.72 30.72s-13.824 30.72-30.72 30.72z" fill="#FFE200" p-id="4014"></path><path d="M525.824 337.92l-45.568 12.288c3.584 8.192 7.68 17.92 11.264 27.136H354.816v69.12h43.52v-27.136h224.768v27.136h46.08V377.344h-124.928c-4.096-10.752-11.776-27.136-18.432-39.424z m8.704 90.624h-45.568v30.72h-112.64v183.296h42.496v-16.896h70.144V686.08h45.568v-59.904h69.12v15.36h44.544V459.776h-113.664v-31.232z m0 95.744v-27.136h69.12v27.136h-69.12z m-115.712 0v-27.136h70.144v27.136H418.816z m115.712 63.488v-27.136h69.12v27.136h-69.12z m-115.712 0v-27.136h70.144v27.136H418.816zM301.056 856.576c-5.632 0-11.264-1.536-16.896-5.12-10.752-7.168-21.504-14.336-31.232-22.528-16.384-12.8-31.744-27.136-46.08-42.496-11.776-12.288-10.752-31.744 1.536-43.52s31.744-10.752 43.52 1.536c12.288 12.8 25.088 25.088 39.424 35.84 8.704 6.656 17.408 13.312 26.624 18.944 14.336 9.216 18.432 28.16 9.216 42.496-6.144 9.728-15.872 14.848-26.112 14.848zM507.904 917.504c-30.72 0-61.44-3.584-91.648-10.24-15.36-3.584-30.72-7.68-45.056-13.312-15.872-5.632-24.576-23.04-18.944-39.424 5.632-15.872 23.04-24.576 39.424-18.944 12.8 4.608 25.6 8.192 38.4 11.264 25.6 5.632 51.712 8.704 77.824 8.704 16.896 0 30.72 13.824 30.72 30.72s-13.824 31.232-30.72 31.232zM596.992 907.264c-13.824 0-26.624-9.728-29.696-23.552-4.096-16.384 6.144-33.28 22.528-36.864 20.48-4.608 39.936-11.776 59.392-19.968 45.568-19.968 87.04-50.176 120.32-87.552 57.344-64 88.576-146.944 88.576-232.96 0-51.2-10.752-100.864-32.256-146.944-7.168-15.36-0.512-33.792 14.848-40.96 15.36-7.168 33.792-0.512 40.96 14.848 25.088 54.272 37.888 112.64 37.888 173.056 0 101.376-36.864 198.656-104.448 273.92-38.912 43.52-88.064 79.36-141.312 102.912-22.528 9.728-46.08 17.92-69.632 23.552-2.56 0-4.608 0.512-7.168 0.512z" fill="#4E63DD" p-id="4015"></path><path d="M164.352 700.928c-11.264 0-22.528-6.656-27.648-17.408-26.624-55.808-39.936-115.2-39.936-177.152 0-67.072 16.384-133.632 47.616-192 43.008-81.408 113.152-146.944 197.632-184.32C394.24 107.008 450.56 95.232 508.416 95.232c79.872 0 156.672 23.04 223.232 66.048 32.256 20.992 61.44 46.08 87.04 75.264 11.264 12.8 9.728 32.256-3.072 43.52s-32.256 9.728-43.52-3.072c-21.504-24.576-46.592-46.592-74.24-64-56.832-36.864-122.368-55.808-189.952-55.808-49.152 0-96.768 10.24-141.312 29.696-71.68 31.744-131.584 87.552-168.448 156.672-26.624 50.176-40.448 106.496-40.448 163.328 0 52.736 11.264 103.424 33.792 150.528 7.168 15.36 1.024 33.792-14.336 40.96-4.096 1.536-8.192 2.56-12.8 2.56z" fill="#4E63DD" p-id="4016"></path></svg>&nbsp;通过审核</a>' : '')


                                + (data[i].state == 99 ? '&nbsp;<a style="color: #22AC38!important;text-shadow: 5px 5px 5px #00000021;font-weight: bold;" class="card-button common-button" ' + "onclick=\"event_create({'aname':'admin','name':'" + _action + "','event':'user_lend_audit','id':" + data[i].id + ",'state':1000})\"" + '><svg t="1700643342860" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="1449" width="12" height="12"><path d="M449.998901 696.046736 269.13567 515.369747 337.436242 446.99959 449.998901 559.448662 685.163875 324.520071 753.466494 392.890228 449.998901 696.046736Z" fill="#272536" p-id="1450"></path><path d="M511.301082 67.156516c-244.732887 0-443.1274 198.394513-443.1274 443.1274s198.394513 443.1274 443.1274 443.1274 443.1274-198.395536 443.1274-443.1274S756.033969 67.156516 511.301082 67.156516zM511.301082 888.575658c-208.925357 0-378.292766-169.367409-378.292766-378.292766s169.367409-378.291742 378.292766-378.291742c208.925357 0 378.291742 169.367409 378.291742 378.291742S720.226438 888.575658 511.301082 888.575658z" fill="#272536" p-id="1451"></path></svg>&nbsp;确认</a>' : '')
                                + (data[i].state == 99 ? '&nbsp;<a style="color: #ac2247!important;text-shadow: 5px 5px 5px #00000021;font-weight: bold;" class="card-button common-button" ' + "onclick=\"event_create({'aname':'admin','name':'" + _action + "','event':'user_lend_audit','id':" + data[i].id + ",'state':-1})\"" + '><svg t="1700643397873" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="1748" width="12" height="12"><path d="M512.078795 67.718311c-244.732887 0-443.1274 198.394513-443.1274 443.1274s198.394513 443.1274 443.1274 443.1274 443.1274-198.395536 443.1274-443.1274S756.811682 67.718311 512.078795 67.718311zM512.078795 889.138476c-208.925357 0-378.292766-169.367409-378.292766-378.292766s169.367409-378.291742 378.292766-378.291742 378.292766 169.367409 378.292766 378.291742S721.004151 889.138476 512.078795 889.138476z" fill="#272536" p-id="1749"></path><path d="M592.82891 510.592954 706.648189 623.650893c5.572926 5.536087 6.77531 13.363356 2.684132 17.48114l-65.652256 66.092277c-4.091178 4.118807-11.92561 2.969635-17.498536-2.566452L512.361227 591.600943 399.327848 705.393615c-5.535063 5.572926-13.361309 6.774287-17.480116 2.683109l-66.086138-65.645093c-4.118807-4.090155-2.969635-11.924587 2.566452-17.497513l113.033379-113.792673L317.508377 398.052808c-5.572926-5.536087-6.774287-13.362332-2.683109-17.48114l65.652256-66.092277c4.092201-4.118807 11.92561-2.969635 17.499559 2.566452l113.852024 113.089661 113.076358-113.836675c5.535063-5.572926 13.361309-6.774287 17.479093-2.683109l66.086138 65.645093c4.117784 4.091178 2.969635 11.924587-2.566452 17.497513L592.82891 510.592954 592.82891 510.592954z" fill="#272536" p-id="1750"></path></svg>&nbsp;取消</a>' : '')



                                + '</div>');


                            tr += "</tr>";
                            $("#list-display").append(tr)


                            var __temp = data[i];
                            __temp.modify_model = modify_model;
                            _bindEvent(__temp);

                            //$('button[edit-' + __temp.id + ']').on("click", function () {
                            //    console.log('attrs', $(this).closest('tr').html(), __temp.modify_model);

                            //    edit({ aname: 'admin', title: '修改' + _actionName, action: _action, id: __temp.id, data: __temp.modify_model });
                            //})



                        }
                        $(".page_size").html(pager_size);
                        $(".page_count").html(json.data.count);
                        $(".cpages").html(json.data.html);

                        $(".total_amount").html(parseFloat(json.total_amount).toFixed(2));
                        $(".total_number").html(json.total_number);


                        //getdata();

                        show_expireList();

                    } else {

                    }
                },
                error: function () {
                }
            });
        }
        getPager();

        function _bindEvent(__temp) {
            $('button[edit-' + __temp.id + ']').on("click", function () {
                console.log('attrs', $(this).closest('tr').html(), __temp.modify_model);

                edit({ aname: 'admin', title: '修改' + _actionName, action: _action, id: __temp.id, data: __temp.modify_model });
            })
        }

        function _pt(i) {
            pager_index = i;
            getPager();
        }
    </script>

    <script>
        layui.use(['form', 'layedit', 'laydate'], function () {
        });
    </script>

    <script>
        function update_select(state) {
            event_create({ 'aname': 'admin', 'name': _action, 'event': 'delete', 'id': select_all_id(), 'state': state });
        }


        function report_exl() {
            export_data(getParam());
        }
    </script>

    <script>
        var click_ids = {};
        function checkIsSelect(obj) {
            if (obj.prop('checked')) {
                return true;
            } else {
                return false;
            }
        }
        function goods_click(t) {
            t = $(t);
            var obj = t.find(".good_select");
            obj.prop("checked", !checkIsSelect(obj));

        }

        function select_all(obj) {
            $(".good_select").prop("checked", checkIsSelect($(obj)));
        }

        function select_all_id() {
            var ids = "";
            $("#list-display tr").each(function () {
                var select_obj = $(this).find(".good_select");
                if (checkIsSelect(select_obj)) {
                    if (ids != "") {
                        ids += ",";
                    }
                    ids += select_obj.data("id");
                }
            });
            return ids;
        }
    </script>

    <audio id="myAudio" controls style="display: none;">
        <source src="../static/files/新订单.mp3" type="audio/mpeg">
        Your browser does not support the audio element.
    </audio>

    <script>

        var autoTimer = 0;
        var lastList = null;

        var set_autoAlarm = function (e) {
            if (checkIsSelect($(e))) {
                console.log('启动');
                lastList = null;

                autoTimer = setInterval(function () {


                    getPager();

                }, 5000);

            } else {
                console.log('关闭');
                clearInterval(autoTimer);
                autoTimer = 0;
            }
        }

        var show_expireList = function () {
            // 获取所有的.expire_time标签
            $('.expire_time').each(function () {
                var time = new Date($(this).data('time')).getTime();

                // 创建并附加倒计时元素
                var countdownElement = $('<span style="color: gray;font-size: 12px;">00:00</span>');
                $(this).closest("td").find(".exp_countdown").append(countdownElement);

                // 更新倒计时
                function updateCountdown() {
                    var currentDate = new Date().getTime();
                    var timeLeft = time - currentDate;

                    if (timeLeft <= 0) {
                        countdownElement.text('已到期将返还');
                    } else {
                        var days = Math.floor(timeLeft / (1000 * 60 * 60 * 24));
                        var hours = Math.floor((timeLeft % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
                        var minutes = Math.floor((timeLeft % (1000 * 60 * 60) / (1000 * 60)));
                        var seconds = Math.floor((timeLeft % (1000 * 60) / 1000));

                        if (days > 0) {
                            countdownElement.text(
                              days + '天 ' +
                              hours.toString().padStart(2, '0') + ':' +
                              minutes.toString().padStart(2, '0') + ':' +
                              seconds.toString().padStart(2, '0') + ''
                            );
                        } else {
                            countdownElement.text(
                              hours.toString().padStart(2, '0') + ':' +
                              minutes.toString().padStart(2, '0') + ':' +
                              seconds.toString().padStart(2, '0') + ''
                            );
                        }
                    }
                }

                updateCountdown();

                // 每秒更新一次倒计时
                setInterval(updateCountdown, 1000);
            });
        }

    </script>

    <style>
        .buy_list {
            display: flex;
            align-items: center;
            font-weight: bold;
            margin-top: 30px;
        }

            .buy_list .name {
                width: 100px;
                flex-shrink: 0;
            }

            .buy_list .item {
                width: 100%;
            }

                .buy_list .item input {
                    width: 100%;
                }
    </style>
    <div style="background: #fff; padding: 18px; display: none;" id="buy_page">

        <div class="buy_list">
            <div class="name">
                支付方式：
           
            </div>
            <div class="item">
                <select class="form-control buyer_type">
                    <option>银行卡</option>
                    <option>支付宝</option>
                    <option>微信</option>
                </select>
            </div>
        </div>

        <div class="buy_list">
            <div class="name" style="color: blue;">
                购买数量：           
            </div>
            <div class="item">
                <input class="form-control buyer_number" />
            </div>
        </div>

        <div class="buy_list">
            <div class="name">
                姓名：           
            </div>
            <div class="item">
                <input class="form-control buyer_name" />
            </div>
        </div>

        <div class="buy_list bank_item">
            <div class="name">
                开户行：           
            </div>
            <div class="item">
                <input class="form-control buyer_bankname" />
            </div>
        </div>

        <div class="buy_list">
            <div class="name">
                账号：           
            </div>
            <div class="item">
                <input class="form-control buyer_bankid" />
            </div>
        </div>

        <a class="btn btn-success" onclick="new_order()" style="margin-top: 24px; margin-left: 100px;">
            <svg t="*************" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="4127" width="16" height="16">
                <path d="M512 170.666667a341.333333 341.333333 0 0 0-290.133333 521.216 42.666667 42.666667 0 0 1-72.533334 45.013333A424.874667 424.874667 0 0 1 85.333333 512C85.333333 276.352 276.352 85.333333 512 85.333333s426.666667 191.018667 426.666667 426.666667-191.018667 426.666667-426.666667 426.666667a424.874667 424.874667 0 0 1-234.666667-70.314667 42.666667 42.666667 0 1 1 46.933334-71.210667A341.333333 341.333333 0 1 0 512 170.666667z m222.165333 264.832a42.666667 42.666667 0 0 0-60.330666-60.330667L469.333333 579.669333l-119.168-119.168a42.666667 42.666667 0 0 0-60.330666 60.330667l149.333333 149.333333a42.666667 42.666667 0 0 0 60.330667 0l234.666666-234.666666z" fill="#14101C" p-id="4128"></path></svg>&nbsp;创建订单</a>

    </div>



    <script>
        //// 传入格式为 "yyyy-MM-dd HH:mm:ss" 的时间戳
        //function countdownToTime(timestamp) {
        //    var targetDate = new Date(timestamp);
        //    var currentDate = new Date();
        //    var timeDiff = targetDate - currentDate;

        //    if (timeDiff <= 0) {
        //        return '已返还';
        //    }

        //    var days = Math.floor(timeDiff / (1000 * 60 * 60 * 24));
        //    var hours = Math.floor((timeDiff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
        //    var minutes = Math.floor((timeDiff % (1000 * 60 * 60) / (1000 * 60)));
        //    var seconds = Math.floor((timeDiff % (1000 * 60) / 1000));

        //    if (days > 0) {
        //        return days + '天' + hours + '时' + minutes + '分';
        //    } else {
        //        return hours + '时' + minutes + '分' + seconds + '秒';
        //    }
        //}

        //var interval = 0


        //function getdata() {
        //    clearInterval(interval);
        //    interval = setInterval(function () {
        //        $('td[expiretime]').each(function () {
        //            var expiretime = $(this).attr('expiretime');
        //            var result = countdownToTime(expiretime);
        //            if (result == "已返还") {
        //                result = "<span style='color:gray;'>已返还</span>";
        //                $(this).removeAttr("expiretime");
        //            }
        //            $(this).html(result);
        //        })
        //    }, 1000); // 更新频率每秒钟
        //}

    </script>

</asp:Content>



<%@ Page Title="" Language="C#" MasterPageFile="~/serv/m.master" AutoEventWireup="true" CodeFile="password_edit.aspx.cs" Inherits="serv_index" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="Server">
    <link href="css/bootstrap.min.css" rel="stylesheet">
    <link href="../css1/a3common.css" rel="stylesheet">
    <link rel="stylesheet" href="../css1/login.css">
    <link rel="stylesheet" href="../css1/font_43459_lbtux0zjkr6yldi.css">

    <style>
        .btn.blue, .btn.blue:ACTIVE {
    background-color: #e54c65;
    color: #fff;
    border: 1px solid #e54c65;
}
        .btn.blue:HOVER {
    background-color: #b3223a;
    border: 1px solid #b3223a;
    transition:all 0.2s;
}
    </style>
</asp:Content>

<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="Server">
    <div id="main" class="main-warp">
        <div class="main-content" style="box-shadow:none!important;">
            <div class="formDiv">
                <h2 class="text-center">修改密码</h2>
                <form id="loginForm" method="post">
                    <div class="dataform">
                        <div class="input-warp gap">
                            <span class="input-icon iconfont icon-baomi"></span>
                            <input class="inputs" type="password" name="password" placeholder="原密码" id="Password1" maxlength="20">
                        </div>
                        <div class="error-content">
                            <span id="userNameErr" class="errMsg"></span>
                        </div>
                        <div class="input-warp gap">
                            <span class="input-icon iconfont icon-baomi"></span>
                            <input class="inputs" type="password" name="password" placeholder="新密码" id="Password2" maxlength="20">
                        </div>
                        <div class="input-warp gap">
                            <span class="input-icon iconfont icon-baomi"></span>
                            <input class="inputs" type="" name="password" placeholder="谷歌验证码" id="Password3" maxlength="20">
                        </div>
                        <div class="btn-warp gap">
                            <div class="text-center">
                                <input type="hidden" value="jsform" id="_app" />
                                <a id="btnLogin" class="btn btn-block lgbtn blue" onclick="passwordEdit()">修改密码</a>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script>

        var passwordEdit = function () {
            var data = {};
            data.Password1 = $("#Password1").val();
            data.Password2 = $("#Password2").val();
            data.actCode = $("#Password3").val();

            $.ajax({
                type: "POST",
                url: "../api/admin.aspx?do=passwordEdit",
                data: data,
                datatype: "json",
                success: function (json) {
                    alert(json.msg);
                },
                error: function () {
                }
            });

        }
    </script>

</asp:Content>


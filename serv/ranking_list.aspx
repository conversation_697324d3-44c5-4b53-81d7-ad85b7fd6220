<%@ Page Title="盈利排行榜" Language="C#" MasterPageFile="~/serv/m.master" AutoEventWireup="true" CodeFile="ranking_list.aspx.cs" Inherits="w_index" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="Server">
    <link href="../css/element-ui.css" rel="stylesheet" />
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="Server">
    <div class="ibox float-e-margins">
        <div class="ibox-title">
            <h5>盈利排行榜</h5>
        </div>
        <div class="ibox-content">
            <div class="row layui-form">
                <div class="col-md-3">
                    <div class="form-group form-buttom" id="twoInputs">
                        <input type="text" id="start_time" name="ordername" class="form-control" placeholder="开始时间" style="width: 100%; display: inline-block;" value="<%=DateTime.Now.ToString("yyyy-MM-dd") %>" autocomplete="off">
                        <input type="text" id="end_time" name="ordername" class="form-control" placeholder="结束时间" style="width: 48%; display: inline-block;display:none;" autocomplete="off" value="<%=DateTime.Now.ToString("yyyy-MM-dd") %>">

                        <script>
                            $(document).ready(function () {
                                $('#start_time').datepicker({
                                    format: 'yyyy-mm-dd',
                                    autoclose: true
                                });

                                $('#end_time').datepicker({
                                    format: 'yyyy-mm-dd',
                                    autoclose: true
                                });
                            });
                        </script>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-group form-buttom">

                        <a class="btn btn-success" onclick="searchList()">
                            <svg t="1688237921441" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="1949" width="16" height="16">
                                <path d="M361.130667 658.688a256 256 0 1 1 282.794666-44.672 42.496 42.496 0 0 0-10.368 14.933333c-0.853333 0.682667-1.621333 1.450667-2.389333 2.218667a42.666667 42.666667 0 0 0 0 60.330667l170.666667 170.666666a42.666667 42.666667 0 0 0 60.330666-60.330666l-142.762666-142.762667a341.333333 341.333333 0 1 0-169.344 99.242667 42.666667 42.666667 0 0 0-20.224-82.901334 254.762667 254.762667 0 0 1-168.704-16.725333z" fill="#14101C" p-id="1950"></path></svg>
                            查询</a>
                        <a class="btn btn-success" onclick="javascript:location.href=location.href;">
                            <svg t="1688237949108" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2211" width="16" height="16">
                                <path d="M512 192a298.24 298.24 0 0 0-245.162667 128 42.666667 42.666667 0 1 1-69.973333-48.810667 384.042667 384.042667 0 0 1 697.173333 180.48l36.608-34.645333a42.666667 42.666667 0 0 1 58.709334 61.952l-112.64 106.666667a42.666667 42.666667 0 0 1-71.253334-38.912A298.666667 298.666667 0 0 0 512 192z m0.170667 640a298.24 298.24 0 0 0 245.162666-128 42.666667 42.666667 0 1 1 69.973334 48.810667 384.042667 384.042667 0 0 1-697.173334-180.48l-36.650666 34.645333a42.666667 42.666667 0 0 1-58.666667-61.952l112.64-106.666667a42.666667 42.666667 0 0 1 71.253333 38.912 298.666667 298.666667 0 0 0 293.418667 354.730667z" fill="#14101C" p-id="2212"></path></svg>
                            刷新</a>

                    </div>
                </div>
            </div>

            <div style="display: flex; flex-wrap: wrap; margin-bottom: 20px;">
                <div class="timesel_lab active">今天</div>
                <div class="timesel_lab">昨天</div>
            </div>
            

            <div style="margin-bottom:10px;">
                <input type="checkbox" id="filter_robot" checked="checked" />&nbsp;过滤机器人
            </div>

            <div class="table-responsive">
                <table class="table">
                    <thead>
                        <tr role="head">
                            <th sort="true">#排名</th>
                            <th sort="true">奖励</th>
                            <th sort="true">用户名</th>
                            <th sort="true">手机号</th>
                            <th sort="true">今日任务</th>
                            <th sort="true">今日佣金</th>
                            <th sort="true">任务次数</th>
                            <th sort="true">历史佣金</th>
                            <th sort="true">实际排名</th>
                            <th sort="true">领取奖励</th>
                            <th sort="true">挂榜时间</th>
                        </tr>
                    </thead>
                    <tbody id="list-display">
                    </tbody>
                </table>
            </div>



            <div class="layui-table-page">
                <div id="layui-table-page1">
                    <div class="layui-box layui-laypage layui-laypage-default" id="layui-laypage-1">
                        <div style="display: inline-block;" class="cpages">
                            <!--page grow -->
                        </div>
                        <span class="layui-laypage-skip">到第<input type="text" min="1" value="1" class="layui-input target-page" />页
                            <button type="button" class="layui-laypage-btn" onclick="javascript:pager_index=$('.target-page').val()-1;getPager();">确定</button></span>
                        <span class="layui-laypage-count">共 <span class="page_count"></span>条</span>
                        <span class="layui-laypage-limits">
                            <select class="s_pages" lay-ignore="" onchange="javascript:pager_size=$(this).val();getPager();">
                                <option value="10">10 条/页</option>
                                <option value="20" selected="">20 条/页</option>
                                <option value="30">30 条/页</option>
                                <option value="50">50 条/页</option>
                                <option value="100">100 条/页</option>
                                <option value="200">200 条/页</option>
                            </select></span>
                    </div>
                </div>
            </div>

        </div>
    </div>
    <script>
        var _action = 'chat_messages';
    </script>
    <script>
        function openuser() {
            var cg = [];

            cg.push({ name: '黑名单', id: 'black_list', value: '', type: 'textarea' });

            edit({ aname: 'admin', title: '黑名单添加', action: _action, id: '', data: cg });
        }
    </script>
    <script>

        var pager_size = $(".s_pages").val();
        var pager_index = 0;
        var pager_key = "";

        var opt = get_param("opt");
        var opt2 = get_param("fid");

        $("#pagesize").val(pager_key);
        function setPageSize(obj) {
            pager_index = 0;
            pager_key = $("#search-keyword").val();
            opt = $(obj).val();
            getPager();
        }

        function searchList() {
            opt = $("#opt1").val();
            pager_index = 0;
            pager_key = $("#search-keyword").val();
            getPager();
        }

        function getParam() {
            return { date: $("#start_time").val(), filter_robot: $('#filter_robot').prop('checked') };
        }

        function getPager() {

            $.ajax({
                type: "POST",
                url: "../api/admin.aspx?do=ranking_list",
                data: getParam(),
                datatype: "json",
                success: function (json) {
                    $("#list-display").html("");
                    if (json.code == 1) {
                        var data = JSON.parse(json.data);
                        var total_number = 0;
                        for (var i = 0; i < data.length; i++) {
                            var obj = data[i];

                            if ($('#filter_robot').prop('checked') && obj.userid == -1) {
                                continue;
                            }
                            total_number += 1;

                            var tr = "";
                            tr += "<tr>";
                            tr += ("<td># " + obj.ranking_number + "</td>");
                            tr += ("<td>" + ("<span style='font-weight: bold; font-size: 13px;" + (data[i].receive_amount > 0 ? " text-shadow: 5px 5px 5px #4faf6e7d; color: #4faf6e;" : " text-shadow: 5px 5px 5px #cd272763; color: #ff4747;") + "'>" + (data[i].receive_amount > 0 ? "" : "") + data[i].receive_amount + "</span>") + '</span>' + "</td>");
                            tr += ("<td class=''>" + (obj.userid == -1 ? '<span style="border-radius: 10px;font-size: 12px;font-weight: bold;display: inline-block;padding: 0 5px;background: #61736b;color: #e1e1e1;margin:0 6px;">假</span>' : '') + obj.parent_code + "</td>");
                            tr += ("<td>" + (obj.userid == -1 ? '<svg t="1697255344327" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="8897" width="12" height="12"><path d="M112 499.2c0-106.08 170.24-192 400-192s400 85.92 400 192v373.28a64 64 0 0 1-64 64H176a64 64 0 0 1-64-64z" fill="#FFFFFF" p-id="8898"></path><path d="M848 872.48H176a64 64 0 0 1-64-64v64a64 64 0 0 0 64 64h672a64 64 0 0 0 64-64v-64a64 64 0 0 1-64 64z" fill="#EAEAF4" p-id="8899"></path><path d="M240 527.52m64 0l416 0q64 0 64 64l0 0q0 64-64 64l-416 0q-64 0-64-64l0 0q0-64 64-64Z" fill="#FFDA64" p-id="8900"></path><path d="M720 528H304a64 64 0 0 0-64 64 64 64 0 0 0 2.24 16A64 64 0 0 1 304 560h416a64 64 0 0 1 61.76 48 64 64 0 0 0 2.24-16 64 64 0 0 0-64-64z" fill="#FFFFFF" p-id="8901"></path><path d="M720 624H304a64 64 0 0 1-61.76-48 64 64 0 0 0-2.24 16 64 64 0 0 0 64 64h416a64 64 0 0 0 64-64 64 64 0 0 0-2.24-16A64 64 0 0 1 720 624z" fill="#FFB666" p-id="8902"></path><path d="M97.6 749.92H52.8a32 32 0 0 1-32-32v-96a32 32 0 0 1 32-32h44.8z" fill="#FFDA64" p-id="8903"></path><path d="M20.8 685.92v32a32 32 0 0 0 32 32h44.8v-32H52.8a32 32 0 0 1-32-32z" fill="#FFB666" p-id="8904"></path><path d="M52.8 589.92a32 32 0 0 0-32 32v27.2a32 32 0 0 1 32-32h44.8v-27.2z" fill="#FFFFFF" p-id="8905"></path><path d="M924.8 749.92h44.8a32 32 0 0 0 32-32v-96a32 32 0 0 0-32-32h-44.8z" fill="#FFDA64" p-id="8906"></path><path d="M1001.6 685.92v32a32 32 0 0 1-32 32h-44.8v-32h44.8a32 32 0 0 0 32-32z" fill="#FFB666" p-id="8907"></path><path d="M969.6 589.92a32 32 0 0 1 32 32v27.2a32 32 0 0 0-32-32h-44.8v-27.2z" fill="#FFFFFF" p-id="8908"></path><path d="M976 573.92h-40V499.2c0-72.16-62.72-134.08-163.36-172.8L832 224a32 32 0 1 0-27.68-16l-62.56 108.32A770.4 770.4 0 0 0 512 283.2a770.4 770.4 0 0 0-229.76 32L219.68 208A32 32 0 1 0 192 224l59.52 103.04C150.72 365.12 88 426.88 88 499.2v74.72H48a48 48 0 0 0-48 48v96a48 48 0 0 0 48 48h40v106.72A88.16 88.16 0 0 0 176 960h672a88.16 88.16 0 0 0 88-88v-106.08H976a48 48 0 0 0 48-48v-96a48 48 0 0 0-48-48z m-928 160a16 16 0 0 1-16-16v-96a16 16 0 0 1 16-16h40v128z m840 138.72A40 40 0 0 1 848 912H176a40 40 0 0 1-40-40V499.2c0-80 144-168 376-168s376 87.2 376 168z m104-154.72a16 16 0 0 1-16 16h-40v-128H976a16 16 0 0 1 16 16z" fill="#3A394B" p-id="8909"></path><path d="M720 512H304a80 80 0 0 0 0 160h416a80 80 0 1 0 0-160z m0 128H304a48 48 0 0 1 0-96h416a48 48 0 0 1 0 96zM352 193.44h48a16 16 0 0 0 15.2-10.88l3.84-11.36 30.72 61.44a16 16 0 0 0 14.24 8.8h1.44a16 16 0 0 0 13.92-11.36L512 117.12l32 97.28a16 16 0 0 0 29.44 2.08L603.04 160l4.96 21.28a16 16 0 0 0 16 12.16h48a16 16 0 0 0 0-32h-35.52L624 109.6a16 16 0 0 0-29.76-3.36l-30.56 61.28L528 58.4A16.96 16.96 0 0 0 512 48a16 16 0 0 0-15.04 11.52l-36.48 123.2-30.24-60.48a16 16 0 0 0-16-8.8 16 16 0 0 0-14.08 10.88l-12.32 37.12H352a16 16 0 0 0 0 32z" fill="#3A394B" p-id="8910"></path></svg><span style="color:#babbbc;margin-left:5px;">虚拟号码</span>' : ((obj.to_group_name == '-' ? '' : "<span class='button-style style-js'>" + obj.group_name + "</span>") + obj.phone)) + "</td>");
                            tr += ("<td>" + parseFloat(obj.daily_amount).toFixed(2) + "</td>");
                            tr += ("<td>" + parseFloat(obj.daily_award_amount).toFixed(2) + "</td>");
                            tr += ("<td>" + obj.daily_count + "</td>");
                            tr += ("<td>" + parseFloat(obj.reward_amount).toFixed(2) + "</td>");
                            tr += ("<td>" + (obj.real_ranking_number == "" ? '<span style="color:#babbbc;">未统计或未上榜</span>' : '#' + obj.real_ranking_number) + "</td>");
                            tr += ("<td>" + obj.receive_time + "</td>");
                            tr += ("<td>" + obj.create_time + "</td>");

                            tr += "</tr>";
                            $("#list-display").append(tr)
                        }
                        $(".page_size").html(pager_size);
                        $(".page_count").html(total_number);
                        $(".cpages").html(json.data.html);
                    } else {

                    }
                },
                error: function () {
                }
            });
        }
        getPager();

        function _pt(i) {
            pager_index = i;
            getPager();
        }
    </script>

    <script>
        layui.use(['form', 'layedit', 'laydate'], function () {
        });


        function report_exl() {
            export_data(getParam());
        }



        function getTextBetweenStrings(inputString, startText, endText) {
            var results = [];
            var startIndex = 0;

            while (startIndex < inputString.length) {
                var startIdx = inputString.indexOf(startText, startIndex);
                if (startIdx === -1) {
                    break; // 如果找不到起始文本，结束循环
                }

                var endIdx = inputString.indexOf(endText, startIdx + startText.length);
                if (endIdx === -1) {
                    break; // 如果找不到结束文本，结束循环
                }

                var textBetween = inputString.substring(startIdx + startText.length, endIdx);
                results.push(textBetween);

                // 更新 startIndex，继续搜索下一个匹配
                startIndex = endIdx + endText.length;
            }

            return results;
        }

    </script>



</asp:Content>


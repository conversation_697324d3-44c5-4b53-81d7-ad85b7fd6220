<%@ Page Language="C#" AutoEventWireup="true" CodeFile="patterns.aspx.cs" Inherits="master" %>

<!DOCTYPE html>

<html xmlns="http://www.w3.org/1999/xhtml">
<head runat="server">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <%--<meta content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=yes" name="viewport">--%>
    <link rel="shortcut icon" href="<%=uConfig.stcdata("htbzlogo") %>" type="image/x-icon" />
    <title><%=uConfig.stcdata("sitename") %>后台管理</title>

    <!--[if lt IE 9]>
    <meta http-equiv="refresh" content="0;ie.html" />
    <![endif]-->

    <link rel="shortcut icon" href="favicon.ico">
    <link href="../css/bootstrap.min14ed.css?v=3.3.6" rel="stylesheet">
    <%--<link href="../css/font-awesome.min93e3.css?v=4.4.0" rel="stylesheet">--%>

    <link href="../css/font-awesome.min.css" rel="stylesheet" />
    <link href="../css/animate.min.css" rel="stylesheet">
    <link href="../css/style.min862f.css?v=4.1.0" rel="stylesheet">
    <script src="../js/jquery.min.js?v=2.1.4"></script>

    <style>
        .nav > li > a {
            font-weight: 100;
        }

        body.canvas-menu .navbar-static-side, body.fixed-sidebar .navbar-static-side {
            width: 180px;
        }

        #page-wrapper {
            margin: 0 0 0 180px;
        }

        .dropdown, .dropup {
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
            padding-bottom: 10px;
        }
    </style>
</head>
<body class="fixed-sidebar full-height-layout gray-bg" style="overflow: hidden">
    <form id="form1" runat="server" style="height: 100%;">
        <div id="wrapper">
            <!--左侧导航开始-->
            <nav class="navbar-default navbar-static-side" role="navigation">
                <div class="nav-close">
                    <i class="fa fa-times-circle"></i>
                </div>
                <div class="sidebar-collapse">
                    <ul class="nav" id="side-menu">
                        <li class="nav-header" style="padding-bottom: 0;" onclick="show_networkState()">
                            <div class="dropdown profile-element">
                                <a>
                                    <%=uConfig.stcdata("serv_logo") %>
                                </a>

                                <div style="padding-top: 6px; color: #DFE4ED; padding-bottom: 5px;">
                                    <%--<p style="margin-top: 8px;">
                                        <span class="block m-t-xs"><i class="fa fa-cog"></i>&nbsp;<strong class="font-bold">后台管理</strong></span>
                                    </p>--%>
                                </div>
                                <div class="logo-element">
                                </div>
                            </div>
                            <div style="text-align: center; padding-bottom: 15px; color: #07D3BE; text-shadow: 1px 1px 6px #d7f5ed96; font-weight: bold; cursor: pointer; position: relative; width: 100%;">
                                <span id="webstate">网络状态：~</span>

                                <div style="background: #00000096; color: #eee; position: absolute; top: 27px; width: 100%; padding: 13px 0; text-shadow: 0 0 BLACK; border-radius: 5px; z-index: 99999; display: none;" id="networkState">
                                    <div style="display: flex; flex-direction: column; justify-content: center;">
                                        <div style="padding: 3px 0; color: gray;" title="" id="fuwu_online">
                                            系统功能：<span class="status2">-</span>
                                        </div>
                                        <div style="padding: 3px 0; color: gray;" title="" id="play_online">
                                            娱乐数据：<span class="status2">-</span>
                                        </div>
                                        <div style="padding: 3px 0; color: gray;" title="" id="monitor_online">
                                            监控通知：<span class="status2">-</span>
                                        </div>
                                    </div>
                                </div>
                                <style>
                                    /* Fade Effect */
                                    @keyframes fade {
                                        0%, 100% {
                                            opacity: 1;
                                        }

                                        50% {
                                            opacity: 0;
                                        }
                                    }

                                    .fade {
                                        animation: fade 0.5s ease-in-out;
                                        opacity: 1;
                                    }
                                </style>
                                <script>
                                    function getSecondsSincePastTime(pastTimeString) {
                                        // 将过去时间字符串转换为 Date 对象
                                        var pastTime = new Date(pastTimeString);

                                        // 获取当前时间
                                        var currentTime = new Date();

                                        // 计算时间间隔（单位：毫秒）
                                        var timeDifference = currentTime - pastTime;

                                        // 将毫秒转换为秒并返回
                                        return Math.floor(timeDifference / 1000);
                                    }
                                    var show_networkState = function () {
                                        $('#networkState').toggle();
                                    }

                                    function applyEffect(effect) {
                                        var textElement = document.getElementById('webstate');

                                        // Remove all animation classes
                                        textElement.className = 'text';

                                        // Trigger reflow to restart the animation
                                        void textElement.offsetWidth;

                                        // Add the selected animation class
                                        textElement.classList.add(effect);
                                    }



                                    $(function () {
                                        var getstatus_sys = function () {
                                            $.ajax({
                                                type: "POST",
                                                url: "../api/admin.aspx?do=sys_status",
                                                data: {},
                                                datatype: "json",
                                                success: function (json) {
                                                    var webstate = "正常";

                                                    $('#webstate').html('网络状态：-');
                                                    for (var k in json.list) {
                                                        console.log('k', k, json.list[k]);

                                                        $('#' + k).attr('title', json.list[k]);

                                                        if (getSecondsSincePastTime(json.list[k]) < 60) {
                                                            $('#' + k).css('color', '#cfe9b8').find('.status2').html('正常');
                                                        } else {
                                                            $('#' + k).css('color', 'gray').find('.status2').html('异常');
                                                            webstate = "异常";
                                                        }
                                                    }
                                                    $('#webstate').html('网络状态：' + webstate);
                                                    if (webstate == "正常") {
                                                        $('#webstate').css('color', '#07D3BE').css('text-shadow', '1px 1px 6px #d7f5ed96');
                                                    } else {
                                                        $('#webstate').css('color', '#fb6565').css('text-shadow', '1px 1px 6px #bd3b149e');
                                                    }
                                                    applyEffect('fade');
                                                },
                                                error: function () {
                                                }
                                            });
                                        }
                                        setInterval(function () {
                                            getstatus_sys();
                                        }, 10000)
                                        getstatus_sys();

                                    })

                                </script>
                            </div>

                        </li>

                        <%if (fz.qx("all", false))
                            { %>
                        <li>
                            <a href="#"><i class="fa-cube fa"></i><span class="nav-label">平台管理</span><span class="fa arrow"></span></a>

                            <ul class="nav nav-second-level">
                                <li><a class="J_menuItem" href="webmaps.aspx">网站设置</a>
                                </li>
                            </ul>

                            <ul class="nav nav-second-level">
                                <li><a class="J_menuItem" href="everyday_data.aspx">每日数据查询</a>
                                </li>
                            </ul>
                            <ul class="nav nav-second-level">
                                <li><a class="J_menuItem" href="role_auths.aspx">角色权限</a>
                                </li>
                            </ul>
                            <ul class="nav nav-second-level">
                                <li><a class="J_menuItem" href="account_groups.aspx">分组管理</a>
                                </li>
                            </ul>
                            <ul class="nav nav-second-level">
                                <li><a class="J_menuItem" href="room_list.aspx">房间管理</a>
                                </li>
                            </ul>
                            <ul class="nav nav-second-level">
                                <li><a class="J_menuItem" href="banner_list.aspx">轮播图管理</a>
                                </li>
                            </ul>
                            <ul class="nav nav-second-level">
                                <li><a class="J_menuItem" href="level_list.aspx">合伙人等级</a>
                                </li>
                            </ul>
                            <ul class="nav nav-second-level">
                                <li><a class="J_menuItem" href="channel_list.aspx">通道列表</a>
                                </li>
                            </ul>
                        </li>

                        <%} %>
                        <li>
                            <a href="#"><i class="fa-list fa"></i><span class="nav-label">简介管理</span><span class="fa arrow"></span></a>
                            
                            <ul class="nav nav-second-level">
                                <li><a class="J_menuItem" href="adlists.aspx?name=关于我们">关于我们</a>
                                </li>
                            </ul>
                            
                            <ul class="nav nav-second-level">
                                <li><a class="J_menuItem" href="adlists.aspx?name=牌照信息">牌照信息</a>
                                </li>
                            </ul>
                            
                            <ul class="nav nav-second-level">
                                <li><a class="J_menuItem" href="adlists.aspx?name=隐私政策">隐私政策</a>
                                </li>
                            </ul>
                        </li>

                        <li>
                            <a href="#"><i class="fa-user-md fa"></i><span class="nav-label">用户管理</span><span class="fa arrow"></span></a>
                            <ul class="nav nav-second-level">
                                <li><a class="J_menuItem" href="account_list.aspx?group=2&audit=1" style="color: #fbbe4a; text-shadow: 5px 5px 5px #000; font-weight: bold;">用户调整审核</a></li>
                                <li><a class="J_menuItem" href="game_remark.aspx"><b>标记账号</b></a></li>
                                <li><a class="J_menuItem" href="account_list.aspx?data=1"><b>数据账号</b></a></li>
                                <li><a class="J_menuItem" href="account_list.aspx?group=1">托号</a></li>
                                <li><a class="J_menuItem" href="account_list.aspx?group=2">新注册用户</a></li>
                                <li><a class="J_menuItem" href="account_list.aspx?group=0">用户列表</a></li>
                                <li><a class="J_menuItem" href="account_list.aspx?group=3">高级列表</a></li>
                                <li><a class="J_menuItem" href="account_list.aspx?online=true">在线用户</a></li>
                                <li><a class="J_menuItem" href="account_list.aspx?user=relationship">下级关系查询</a></li>
                                <li><a class="J_menuItem" href="transfer_list.aspx?state=0">转账审核</a></li>
                                <li><a class="J_menuItem" href="transfer_list.aspx">转账列表</a></li>
                                <li><a class="J_menuItem" href="user_logs.aspx">登录日志</a></li>
                                <li><a class="J_menuItem" href="payment_history.aspx">回款绑定记录</a></li>
                                <li><a class="J_menuItem" href="usertask_search.aspx">今日任务查询</a></li>
                                <li><a class="J_menuItem" href="account_usertype_records.aspx">层级调整记录</a></li>
                                <li><a class="J_menuItem" href="experience_amount.aspx">体验金-列表</a></li>
                                <li><a class="J_menuItem" href="experience_amount.aspx?ss=finish">体验金-结束</a></li>
                                <li><a class="J_menuItem" href="experience_records.aspx">体验金-记录</a></li>
                                <li><a class="J_menuItem" href="user_parent_records.aspx">上级调整记录</a></li>
                            </ul>
                        </li>

                        <li>
                            <a href="#"><i class="fa-wechat fa"></i><span class="nav-label">聊天室</span><span class="fa arrow"></span></a>
                            <ul class="nav nav-second-level">
                                <li><a class="J_menuItem" href="chat_message.aspx?is_black=0">聊天记录</a></li>
                                <li><a class="J_menuItem" href="chat_message.aspx?is_black=1">聊天记录[屏蔽]</a></li>
                                <li><a class="J_menuItem" href="chat_message.aspx?is_delf=1">聊天记录[删除]</a></li>
                                <li><a class="J_menuItem" href="redbag_list.aspx">红包发送记录</a></li>
                                <li><a class="J_menuItem" href="redbag_records.aspx">红包领取记录</a></li>
                                <li><a class="J_menuItem" href="chat_groups.aspx">群聊管理</a></li>
                                <%--<li><a class="J_menuItem" href="ranking_list.aspx">盈利排行榜</a></li>--%>
                            </ul>
                        </li>

                        <li>
                            <a href="#" style="color: #ffbab8; text-shadow: 5px 5px 5px #000000eb; font-weight: bold;">
                                <svg t="" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="14607" width="16" height="16" style="position: relative; top: 3px;">
                                    <path d="M511.0272 12.4416a501.76 501.76 0 1 0 501.76 501.76 501.76 501.76 0 0 0-501.76-501.76z m100.3008 805.8368l-17.3568-92.16-43.3664-230.144h-0.4608l-29.9008-156.928H501.76l-147.3536 387.072h162.4064l17.3568 92.16h-290.304L464.9984 209.92h92.16L778.24 818.2784z" fill="#ffaaa7" p-id="14608"></path></svg>&nbsp;<span class="nav-label">一键任务</span><span class="fa arrow"></span></a>
                            <ul class="nav nav-second-level">
                                <li><a class="J_menuItem" href="task_onetouch_total.aspx">挂单列表</a>
                                </li>
                                <li><a class="J_menuItem" href="onetouch_success_rate.aspx" style="color: #ff9090; font-weight: bold;">一键任务成功率</a>
                                </li>
                                <li><a class="J_menuItem" href="task_onetouch_items.aspx" style="color: #ff9090; font-weight: bold;">任务周期管理</a>
                                </li>
                                <li><a class="J_menuItem" href="accounts_succrate_audit.aspx" style="color: #e0e1a6; font-weight: bold;">成功率调整审核</a>
                                </li>
                                <li><a class="J_menuItem" href="succrate_records.aspx" style="color: #e0e1a6; font-weight: bold;">成功率变更记录</a>
                                </li>
                                <li><a class="J_menuItem" href="task_onetouch.aspx?state=verify" style="color: #ddd;">待审核</a>
                                </li>
                                <li><a class="J_menuItem" href="task_onetouch.aspx?state=task" style="color: #ddd;">进行中</a>
                                </li>
                                <li><a class="J_menuItem" href="task_onetouch.aspx?state=success" style="color: #ddd;">已完成</a>
                                </li>
                                <li><a class="J_menuItem" href="task_onetouch.aspx?state=cancel" style="color: #ddd;">撤销</a>
                                </li>
                                <li><a class="J_menuItem" href="task_onetouch_records.aspx">每日记录</a>
                                </li>
                                <li><a class="J_menuItem" href="task_onetouch_preset.aspx">预设单</a>
                                </li>
                                <li><a class="J_menuItem" href="transport_orders.aspx?order_type=onetouch">任务子订单</a>
                                </li>
                                <li><a class="J_menuItem" href="task_onetouch_total.aspx?user=relationship">团队查询</a>
                                </li>
                                <li><a class="J_menuItem" href="partnersbrok_records.aspx?user=relationship">团队佣金明细</a>
                                </li>
                                <li><a class="J_menuItem" href="task_onetouch_balance.aspx">一键任务额度记录</a>
                                </li>
                                <li><a class="J_menuItem" href="onetouch_balance_records.aspx">任务池额度记录</a>
                                </li>
                                <li><a class="J_menuItem" href="balance_transfer_list.aspx">额度转余额申请</a>
                                </li>
                                <li><a class="J_menuItem" href="task_onetouch_game_balance.aspx">游戏额度更新记录</a>
                                </li>
                            </ul>
                        </li>



                        <li>
                            <a href="#" style="color: #ffbc94;">
                                <svg t="" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="9674" width="16" height="16" style="position: relative; top: 3px;">
                                    <path d="M677.376 742.912l-22.016-23.04c-21.504-22.528-52.224-35.328-84.48-35.328H446.976c-33.28 0-65.024 13.824-86.528 37.888l-15.872 17.408c-72.704 80.384-215.04 35.328-217.088-70.144V372.224c0-79.36 68.608-143.872 152.576-143.872H742.4c84.48 0 152.576 64.512 152.576 143.872v297.472c-2.048 104.96-142.848 151.552-217.6 73.216z" fill="#FFD016" p-id="9675"></path><path d="M894.976 638.976v29.184c-1.536 99.328-134.144 116.224-204.8 42.496l-20.48-21.504c-20.48-21.504-49.152-33.792-79.872-33.792H473.088c-31.232 0-61.44 12.8-81.92 35.84l-14.848 16.384C308.224 783.36 174.08 740.864 172.544 641.536V361.472c0-74.752 5.632-138.24 143.872-132.608 0 0 7.68 170.496 300.032 135.68 203.264-24.064 278.528 274.432 278.528 274.432z" fill="#FFA118" p-id="9676"></path><path d="M893.952 666.624c0 1.024 0.512 1.536 0.512 2.048-2.048 104.96-143.36 152.064-217.6 73.728l-22.016-23.04c-21.504-22.528-52.224-35.328-84.48-35.328H446.976c-33.28 0-65.024 13.824-86.528 37.888l-15.872 17.408c-72.192 80.384-214.528 35.328-216.576-70.144v-296.96c0-79.36 68.096-143.36 152.576-143.36 0 0-18.944 244.224 290.816 207.36 216.064-25.088 322.56 230.4 322.56 230.4z" fill="#FE6F1A" p-id="9677"></path><path d="M320 493.568c-40.448 0-73.216-31.744-73.216-70.144 0-38.912 32.768-70.144 73.216-70.144s73.216 31.744 73.216 70.144c0 38.4-32.768 70.144-73.216 70.144z m0-99.328c-17.92 0-32.256 13.312-32.256 29.184 0 16.384 14.336 29.184 32.256 29.184s32.256-13.312 32.256-29.184c0-16.384-14.336-29.184-32.256-29.184zM606.208 399.36h153.6v40.96h-153.6z" fill="#FFFFFF" p-id="9678"></path><path d="M662.528 347.648h40.96v144.896h-40.96z" fill="#FFFFFF" p-id="9679"></path></svg>&nbsp;<span class="nav-label">娱乐管理</span><span class="fa arrow"></span></a>
                            <ul class="nav nav-second-level">
                                <li><a class="J_menuItem" href="auth_list.aspx">授权管理</a>
                                </li>
                                <li><a class="J_menuItem" href="serverUrls.aspx">授权域名</a>
                                </li>
                                <li><a class="J_menuItem" href="auth_records.aspx">授权记录</a>
                                </li>
                                <li><a class="J_menuItem" href="play_rules.aspx">游戏返佣规则</a>
                                </li>
                                <li><a class="J_menuItem" href="play_user_daily.aspx">游戏结算数据</a>
                                </li>
                                <li><a class="J_menuItem" href="play_total.aspx">用户游戏数据</a>
                                </li>
                                <li><a class="J_menuItem" href="play_records.aspx">游戏记录</a>
                                </li>
                                <li><a class="J_menuItem" href="play_user_data.aspx">用户返佣等级</a>
                                </li>
                                <li><a class="J_menuItem" href="play_timeslot_details.aspx">游戏统计详情</a>
                                </li>
                                <li><a class="J_menuItem" href="play_brok_records.aspx">佣金派发记录</a>
                                </li>
                            </ul>
                        </li>
                        <li>
                            <a href="#" style="color: #fbf9cb;">
                                <svg t="1698246879418" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="20521" width="16" height="16" style="position: relative; top: 3px;">
                                    <path d="M706.4 98.7H317.2c-34.1 0-61.7 18.7-61.7 41.9V656h512.6V140.6c0-23.2-27.6-41.9-61.7-41.9z" fill="#AED581" p-id="20522"></path><path d="M512.2 575.7c-35.2 0-63.9-28.6-63.9-63.9 0-35.2 28.6-63.9 63.9-63.9s63.9 28.6 63.9 63.9-28.6 63.9-63.9 63.9z m127.9-192.8v-64H384v64h256.1z m0-127.6v-64H384v64h256.1z" fill="#558B2F" p-id="20523"></path><path d="M583.7 650c-51.7-1.4-31.6 0.7-45.7 0-7.8-0.4-14.2-1.6-22-2-15.5-0.8-31.1-6.2-66.7-19.3-55.7-20.6-127.4-71.3-154-71.3l64 168.5c8.7 13.2 68.5 62.1 97.5 62.1h188.3c0.4 0 76.9-4.6 76.9-69 0-58.6-20.6-65.9-138.3-69z" fill="#FFFFFF" p-id="20524"></path><path d="M951 619.9c-13.6-17.5-43.9-32.3-105.6 4.2 5.8-25.6-14.6-41.5-38.7-41.5-21.2 0-46.2 5.5-65.9 25.6-28.4-37.4-57.6-32.9-114.4 2.3-21.6 13.4-40.7 25-54.9 33-3.8 2.2-6.8 3.7-9.9 5.4 5.6 1.2 11.3 2.2 17.4 2.2 79.7 0 142.7 0 142.7 69.2 0 53.1-61.7 68.5-94.5 69.1H479.7c-60.9 0-120.4-63.5-120.4-63.5s71.4 43.4 121.4 43.4l167.3 0.7c0.3 0 49.4-14.1 48-46-1.3-30.7-20.6-51.3-112.7-51.3-60 0-121.3-27.4-121.3-27.4s-64.5-39.7-83.1-51.4c-44.2-27.5-79.4-53.8-156.4-23.4C173.7 589.9 67 639.6 67 639.6l98.3 218.2 50.2-32.5c27.8-17.7 43.1-20.7 79-2.2 34.7 17.8 141.3 73.3 168.1 86.3 81.9 39.7 117.1 4.1 156.7-18.7 33.8-19.5 273-177.9 298.1-195.7 39.2-27.7 51.5-51.9 33.6-75.1z" fill="#78909C" p-id="20525"></path></svg>&nbsp;<span class="nav-label">托号抢单</span><span class="fa arrow"></span></a>
                            <ul class="nav nav-second-level">
                                <li><a class="J_menuItem" href="transport_orders.aspx?order_mode=0&state=task&group=1">进行中订单</a>
                                </li>
                                <li><a class="J_menuItem" href="transport_orders.aspx?order_mode=0&state=confirm_order&group=1">已确认收货</a>
                                </li>
                                <li><a class="J_menuItem" href="transport_orders.aspx?order_mode=0&state=timeout_receive&group=1">用户确认超时</a>
                                </li>
                            </ul>
                        </li>


                        <li>
                            <a href="#" style="color: #fbf9cb;">
                                <svg t="1698247093279" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="35339" width="16" height="16" style="position: relative; top: 3px;">
                                    <path d="M0 0h849.17591v849.17591H0z" fill="#FFFFFF" p-id="35340"></path><path d="M707.646591 0a141.529318 141.529318 0 0 1 141.529319 141.529318v566.117273a141.529318 141.529318 0 0 1-141.529319 141.529319H141.529318a141.529318 141.529318 0 0 1-141.529318-141.529319V141.529318a141.529318 141.529318 0 0 1 141.529318-141.529318h566.117273zM250.082305 105.439342H205.925158c0.849176 4.24588 2.264469 10.756228 3.396704 17.549636H166.580008v37.080681h9.057876l9.057876 27.173629h-27.456687v36.797623h56.328668v16.134342H163.749421v37.080681h49.81832v33.967037c0 11.322345-3.396704 13.586815-14.43599 15.002108l-3.679762 0.566117c4.811997-12.45458 9.057876-25.758336 12.171521-35.099271l-30.853391-9.623994c-5.378114 18.964929-15.568225 45.006323-23.210809 59.725373l30.570333 13.586814c2.547528-5.944231 5.944231-13.869873 9.057876-22.644691l11.605405 36.797623 14.152931-1.132235c27.739746-2.264469 33.967036-7.642583 33.967037-30.287274v-30.853391c6.510349 9.057876 14.43599 22.078574 19.814104 30.004215l18.964929-12.45458a193.682872 193.682872 0 0 1-13.020697 26.890571l41.326561 19.531046c19.531046-41.60962 24.626101-71.047718 24.626101-133.603677v-7.925641h13.586815v137.283438h40.194326v-137.283438h18.964929V184.978819H344.62389V162.051069c23.776925-1.415293 55.196434-7.642583 68.217131-12.171521l-13.586814-43.59103c-22.078574 7.359525-63.688193 13.586815-93.692409 14.43599v105.297813c0 39.062092-2.547528 63.971252-9.340935 86.049826a858.092257 858.092257 0 0 0-19.814104-28.305864l-23.493867 15.002108v-21.512457h41.326561V240.175253H252.912892v-16.134342h43.307971v-36.797623h-21.512456c3.396704-8.774818 7.359525-20.097163 9.623993-27.173629h9.907053V122.988978h-41.043503l-3.113645-17.549636z m291.267337-0.283059c-0.283059 4.811997-0.283059 15.851284-1.132234 28.022805-5.378114 93.126291-28.305864 135.585087-101.901109 191.630697l35.665388 40.477385c45.289382-36.514564 74.72748-74.444421 92.843233-117.186275 17.266577 41.043502 46.138558 77.275008 89.446529 116.054041L695.616599 323.394492c-62.839017-42.741854-100.768875-91.994057-107.562282-176.34553 0.849176-16.983518 1.415293-34.250095 1.415294-40.760444l-48.119969-1.132235zM244.138074 160.069659c-2.264469 7.925642-5.095055 18.398811-8.2087 27.173629h-11.322346L215.83221 160.069659h28.305864z" fill="#016FFE" p-id="35341"></path><path d="M421.120487 424.587955c68.641719 0 124.758094 19.000311 128.791679 42.98953l0.070765 0.389206c75.930479 35.205418 126.350299 111.525103 151.082547 246.650219l1.167617 0.919941c1.132235 0.919941 2.229087 1.875263 3.325939 2.865969l-4.493556-3.78591 3.113645 2.58291 4.599703 4.352027 3.04288 3.255174 4.10435 5.024291 3.892057 5.448879 4.528938 7.607201 2.865969 5.555025 3.184409 7.253378 2.618293 6.864172 2.335233 7.182613 1.910646 7.005701 1.946028 8.845582c0.849176 4.24588 1.52144 8.527141 2.087558 12.843786l0.566117 4.953526 0.813794 10.260876 0.247676 5.519643a225.102381 225.102381 0 0 1-2.016793 37.646799 116.160188 116.160188 0 0 1-9.057876 31.490273l-3.396704 6.722643-2.405998 4.068968-4.316644 6.368819-5.519644 6.864172-3.962821 4.245879-5.271967 4.882762-6.22729 4.953526-4.352026 3.04288-8.279466 4.882762-8.420994 3.998203-7.217995 2.759822-5.908849 1.875263-7.147231 1.769117-5.095055 0.990705-5.625791 0.849176-3.892056 0.424588c-39.698974 39.663591-103.564079 70.693894-205.429805 75.64742l-12.419198 0.495353v0.106147h-4.352027c-109.154487-3.078263-176.557825-34.957742-217.95515-76.213538l-6.722642-0.813793-6.970319-1.238382-8.279465-2.016793-5.731938-1.769116-9.093259-3.538233-8.031788-3.998203-5.661173-3.325939-7.076466-4.882762-6.014996-4.988908-5.413496-5.236585-3.679763-4.068968-4.811997-6.085761-2.653674-3.785909-2.299852-3.750527-3.361321-6.085761a103.068726 103.068726 0 0 1-3.892056-8.8102l-2.193705-6.121143c-1.98141-6.085761-3.538233-12.737639-4.493556-19.707957l-1.238381-10.826993-0.813794-14.435991v-11.180816l0.778412-13.692961 1.167616-10.367023 0.884559-5.731937a185.403407 185.403407 0 0 1 5.307349-22.538544l2.12294-6.474966 3.078263-7.854878 3.255174-7.076465 3.18441-5.838085 4.635085-7.430289 4.245879-5.661173 3.821292-4.387409 6.014996-5.873466 3.750527-3.113645c23.883072-130.525414 71.861511-206.172834 143.68764-242.828928l-0.070764-1.132235c0-25.298366 57.779344-45.784734 129.039356-45.784734z" fill="#FFFFFF" p-id="35342"></path><path d="M426.18016 484.101033h-3.18441c-124.899623 0.247676-217.353651 14.506755-226.871497 171.356622v34.674683c0 93.480115 103.422549 144.466052 228.463702 145.315228 125.076535-0.849176 228.463702-51.835113 228.463702-145.279845v-34.674683c-9.553229-156.956014-101.971874-171.144328-226.871497-171.392005z" fill="#1B1C2D" p-id="35343"></path><path d="M430.249128 784.956981l1.238381 0.566118 58.557756 46.63391c0.778411 0.636882 0.849176 1.238382 0.31844 1.627587l-0.778411 0.283059-57.602432 9.340935-2.335234 0.141529-2.370616-0.141529-57.531668-9.340935c-0.990705-0.176912-1.344529-0.707647-1.06147-1.309146l0.566117-0.6015 58.451609-46.63391c0.743029-0.566117 1.662969-0.707647 2.547528-0.566118z m-5.661173-186.464876l5.696555 0.176911c44.970941 2.901351 81.096299 40.158944 84.033033 86.686708l0.176911 6.08576-0.283058 3.396704a18.611105 18.611105 0 0 1-14.71905 15.214402l-3.290556 0.283058-3.290557-0.283058a18.611105 18.611105 0 0 1-14.683667-15.214402l-0.283058-3.396704-0.247677-5.02429c-2.299851-26.395218-22.680073-47.518469-48.226115-49.889085L424.587955 636.280433l-4.847379 0.247676c-25.546042 2.370616-45.961646 23.493867-48.261498 49.889085l-0.212294 5.02429-0.283058 3.396704a18.611105 18.611105 0 0 1-14.71905 15.214402l-3.290556 0.283058-3.290557-0.283058a18.752635 18.752635 0 0 1-14.966725-18.611106c0-49.287585 37.257593-89.76497 84.209944-92.772468l5.661173-0.212294z" fill="#3DFFB7" p-id="35344"></path></svg>&nbsp;<span class="nav-label">新手抢单</span><span class="fa arrow"></span></a>
                            <ul class="nav nav-second-level">
                                <li><a class="J_menuItem" href="transport_orders.aspx?order_mode=0&state=task&group=2">进行中订单</a>
                                </li>
                                <li><a class="J_menuItem" href="transport_orders.aspx?order_mode=0&state=confirm_order&group=2">已确认收货</a>
                                </li>
                                <li><a class="J_menuItem" href="transport_orders.aspx?order_mode=0&state=timeout_receive&group=2">用户确认超时</a>
                                </li>
                            </ul>
                        </li>


                        <li>
                            <a href="#" style="color: #fbf9cb;">
                                <svg t="1698246952254" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="32418" width="16" height="16" style="position: relative; top: 3px;">
                                    <path d="M511.9488 513.8432m-453.2224 0a453.2224 453.2224 0 1 0 906.4448 0 453.2224 453.2224 0 1 0-906.4448 0Z" fill="#EE4F3E" p-id="32419"></path><path d="M512 61.3888c-250.3168 0-453.2224 202.9056-453.2224 453.2224 0 99.7888 32.256 192 86.8864 266.8544 74.8544 54.6304 167.0656 86.8864 266.8544 86.8864 250.3168 0 453.2224-202.9056 453.2224-453.2224 0-99.7888-32.256-192-86.8864-266.8544C704 93.6448 611.7888 61.3888 512 61.3888z" fill="#EA623D" p-id="32420"></path><path d="M755.6608 308.0704c0-72.6016-17.152-141.2096-47.5136-202.0352-59.3408-28.5696-125.8496-44.5952-196.096-44.5952-250.3168 0-453.2224 202.9056-453.2224 453.2224 0 72.6016 17.152 141.2096 47.5136 202.0352 59.3408 28.5696 125.8496 44.5952 196.096 44.5952 250.3168 0 453.2224-202.9056 453.2224-453.2224z" fill="#F37140" p-id="32421"></path><path d="M58.7776 514.6112c0 38.912 4.9664 76.6464 14.2336 112.64a443.5968 443.5968 0 0 0 114.688 15.0528c245.7088 0 444.928-199.2192 444.928-444.928 0-43.1616-6.2464-84.7872-17.7152-124.2112-33.024-7.68-67.4816-11.776-102.8608-11.776-250.368 0-453.2736 202.9056-453.2736 453.2224z" fill="#F47847" p-id="32422"></path><path d="M383.6416 313.2928S252.416 365.3632 187.2896 497.7152c-35.7888-70.5024-43.3664-135.0656 5.5296-188.7232 44.032-48.3328 151.7568-62.9248 190.8224 4.3008z" fill="#F7F8F8" p-id="32423"></path><path d="M512.1536 330.5472c-176.1792 0-319.0272 142.848-319.0272 319.0272 0 163.328 122.7264 297.8816 280.9856 316.672a451.24608 451.24608 0 0 0 77.9776-0.2048c49.9712-6.2464 96.3584-24.064 136.3968-50.6368l9.8816 12.4416c23.8592-10.752 46.592-23.552 67.9936-38.0928l-19.3536-24.3712c52.224-56.832 84.1728-132.608 84.1728-215.8592 0-176.128-142.848-318.976-319.0272-318.976zM442.9824 842.6496l46.6432-157.2864-100.864-32.5632 201.7792-222.3616-39.0656 175.7184 112.7936 9.7792-221.2864 226.7136z" fill="#EAD4CC" p-id="32424"></path><path d="M473.4464 332.9536c-145.2032 17.5616-260.3008 132.6592-277.9136 277.9136l277.9136-277.9136z" fill="#F7F8F8" p-id="32425"></path><path d="M640.3584 313.2928s53.1968 21.1456 108.6464 70.6048l78.592-78.592c-45.9776-45.312-149.1968-57.4976-187.2384 7.9872z" fill="#EFEBE8" p-id="32426"></path><path d="M836.7104 497.7152c35.7888-70.5024 43.3664-135.0656-5.5296-188.7232-1.1264-1.2288-2.3552-2.4576-3.5328-3.6352L749.056 383.9488c31.5904 28.2112 64 65.6896 87.6544 113.7664zM830.464 629.0432c-5.5296-87.0912-46.0288-164.7104-107.6224-218.9312l-164.352 164.352-7.0656 31.744 112.7936 9.7792-221.2864 226.7136 46.6432-157.2864-31.7952-10.24-185.1392 185.1392c3.072 3.4816 6.1952 6.8608 9.4208 10.1888l-18.5344 23.296c21.7088 14.2336 44.6976 26.6752 68.8128 37.12l9.2672-11.6736c39.424 24.9856 84.736 41.5744 133.4272 47.2064 5.632 0.4608 11.3152 0.8192 16.9984 1.024l338.432-338.432z" fill="#EFE1DA" p-id="32427"></path><path d="M388.7104 652.8l201.7792-222.3616-32 143.9744 164.352-164.352c-56.2176-49.5104-129.8944-79.5648-210.688-79.5648-13.1072 0-26.0096 0.8704-38.7072 2.4064L195.5328 610.816c-1.536 12.6976-2.4064 25.6-2.4064 38.7072 0 80.7936 30.0544 154.4704 79.5648 210.688l185.1392-185.1392-69.12-22.272z" fill="#EFEBE8" p-id="32428"></path></svg>&nbsp;<span class="nav-label">抢单管理</span><span class="fa arrow"></span></a>
                            <ul class="nav nav-second-level">
                                <%--     <li><a class="J_menuItem" href="transport_orders.aspx?order_mode=0&state=notify_error&group=0" style="color: #e19595;">推送失败</a>
                                </li>
                                <li><a class="J_menuItem" href="transport_orders.aspx?order_mode=0&state=release_order&group=0">申请解冻</a>
                                </li>
                                <li><a class="J_menuItem" href="transport_orders.aspx?order_mode=0&state=timeout&group=0">超时审核</a>
                                </li>--%>
                                <li><a class="J_menuItem" href="transport_orders.aspx?order_mode=0&state=task&group=0">进行中订单</a>
                                </li>
                                <li><a class="J_menuItem" href="transport_orders.aspx?order_mode=0&state=confirm_order&group=0">已确认收货</a>
                                </li>
                                <%--<li><a class="J_menuItem" href="transport_orders.aspx?order_mode=0&state=nomatch&group=0">未匹配订单</a>
                                </li>--%>
                                <li><a class="J_menuItem" href="transport_orders.aspx?order_mode=0&state=timeout_receive&group=0">用户确认超时</a>
                                </li>
                                <%--    <li><a class="J_menuItem" href="transport_orders.aspx?order_mode=0&state=timeout_unreceive&group=0">未接单超时</a>
                                </li>
                               <li><a class="J_menuItem" href="transport_orders.aspx?order_mode=0&state=fail">取消订单</a>
                                </li>
                                <li><a class="J_menuItem" href="transport_orders.aspx?order_mode=0&evaluate=yes">用户已评价</a>
                                </li>--%>
                            </ul>
                        </li>




                        <%--<li>
                            <a href="#" style="color: #dad7db;"> 
                                <svg t="1703705403907" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="4134" width="16" height="16"  style="position: relative; top: 3px;"><path d="M102.4 921.6V716.8h204.8L102.4 921.6z" fill="#2A44A1" p-id="4135"></path><path d="M204.8 102.4h614.4a102.4 102.4 0 0 1 102.4 102.4v512a102.4 102.4 0 0 1-102.4 102.4H102.4V204.8a102.4 102.4 0 0 1 102.4-102.4z" fill="#3D60F6" p-id="4136"></path><path d="M870.4 460.8a51.2 51.2 0 0 0 51.2 51.2V409.6a51.2 51.2 0 0 0-51.2 51.2zM153.6 460.8a51.2 51.2 0 0 0-51.2-51.2v102.4a51.2 51.2 0 0 0 51.2-51.2z" fill="#36C196" p-id="4137"></path><path d="M512 691.2a230.4 230.4 0 0 1 0-460.8 25.6 25.6 0 0 1 25.6 25.6v51.2a25.6 25.6 0 0 1-51.2 0v-23.7568A179.2 179.2 0 1 0 691.2 460.8a25.6 25.6 0 0 1 51.2 0A230.6048 230.6048 0 0 1 512 691.2z" fill="#FFBA40" p-id="4138"></path><path d="M493.568 532.48h-71.68a11.5712 11.5712 0 0 1-11.3664-11.6736 12.0832 12.0832 0 0 1 2.9696-7.8848l42.7008-48.0256a115.3024 115.3024 0 0 0 14.0288-19.0464 31.4368 31.4368 0 0 0 4.1984-15.2576 23.4496 23.4496 0 0 0-5.5296-16.2816 18.6368 18.6368 0 0 0-14.7456-6.2464 21.6064 21.6064 0 0 0-17.2032 6.9632 22.9376 22.9376 0 0 0-5.2224 10.24 11.264 11.264 0 0 1-10.8544 8.704A11.4688 11.4688 0 0 1 409.6 419.84a44.4416 44.4416 0 0 1 3.6864-8.8064A39.7312 39.7312 0 0 1 430.08 394.8544a51.2 51.2 0 0 1 24.2688-5.7344 45.2608 45.2608 0 0 1 31.4368 10.24 36.352 36.352 0 0 1 11.5712 28.5696A50.4832 50.4832 0 0 1 491.52 450.56a131.584 131.584 0 0 1-18.944 26.4192l-33.4848 36.9664h54.4768a9.3184 9.3184 0 0 1 9.1136 9.4208 9.3184 9.3184 0 0 1-9.1136 9.1136zM597.4016 482.0992h7.7824a9.3184 9.3184 0 0 1 9.216 9.4208 9.4208 9.4208 0 0 1-9.216 9.5232h-7.7824v19.6608A11.6736 11.6736 0 0 1 585.9328 532.48a11.6736 11.6736 0 0 1-11.3664-11.776v-19.6608h-48.7424a11.264 11.264 0 0 1-10.9568-10.8544 11.4688 11.4688 0 0 1 1.7408-6.5536l52.6336-85.2992a15.36 15.36 0 0 1 12.9024-7.2704 15.5648 15.5648 0 0 1 15.2576 15.7696z m-58.6752 0h35.84v-59.0848l-1.7408 3.072z" fill="#FFFFFF" p-id="4139"></path></svg>&nbsp;<span class="nav-label">挂单管理</span><span class="fa arrow"></span></a>
                            <ul class="nav nav-second-level">
                                <li><a class="J_menuItem" href="transport_orders.aspx?order_mode=1&state=gd_task" style="color: #62e2ff;">挂单进行中</a>
                                </li>
                                <li><a class="J_menuItem" href="transport_orders.aspx?order_mode=1&state=gd_timeout" style="">挂单待重推</a>
                                </li>
                                <li><a class="J_menuItem" href="transport_orders.aspx?order_mode=1&state=gd_finish" style="color: #adc5ad;">挂单已完成</a>
                                </li>
                                <li><a class="J_menuItem" href="transport_orders.aspx?order_mode=1&state=gd_audit">取消待审核</a>
                                </li>
                            </ul>
                        </li>--%>


                        <li>
                            <a href="#" style="color: #dad7db;">
                                <svg t="" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="19750" width="16" height="16" style="position: relative; top: 3px;">
                                    <path d="M817.8 64.2H206.6c-78.3 0-142.4 64.1-142.4 142.4v579.2c0 78.3 64.1 142.4 142.4 142.4h611.2c78.3 0 142.4-64.1 142.4-142.4V206.6c0-78.3-64.1-142.4-142.4-142.4zM235.1 305.8h222.5v-37.1H269.7v-61.8h187.9v-32.1h103.8v32.1h190.3v61.8H561.3v37.1h222.5v150.8h-96.4v-86.5H235.1v-64.3z m242.2 98.9v66.7c-57.7-21.4-118.7-34.6-182.9-39.5V380c67.5 1.6 128.5 9.8 182.9 24.7z m321.3 205.1H650.3c26.3 36.3 74.2 61.8 143.4 76.6v56.9c-107.1-19.8-182.9-52.8-227.4-98.9-47.8 47.8-157.4 80.7-328.8 98.9v-54.4c121.9-14.8 201-41.2 237.3-79.1H227.6v-66.7h207.6c-57.6-19.8-113.6-32.1-168-37.1v-51.9c69.2 1.7 132.6 12.4 190.3 32.1v56.9h54.4c4.9-18.1 7.4-38.7 7.4-61.8v-91.5h103.8v66.7c0 33-2.5 61.8-7.4 86.5h182.9v66.8z" fill="#51A5FA" p-id="19751"></path></svg>&nbsp;<span class="nav-label">卖币管理</span><span class="fa arrow"></span></a>
                            <ul class="nav nav-second-level">
                                <li><a class="J_menuItem" href="sell_dating_list.aspx?state=verify" style="color: #ddd;">待审核</a>
                                </li>
                                <li><a class="J_menuItem" href="sell_dating_list.aspx?state=wait" style="color: #62e2ff;">未完成</a>
                                </li>
                                <li><a class="J_menuItem" href="sell_dating_list.aspx?state=success" style="color: #adc5ad;">已完成</a>
                                </li>
                                <li><a class="J_menuItem" href="sell_dating_list.aspx?state=cancel" style="color: #ddd;">撤销</a>
                                </li>
                            </ul>
                        </li>

                        <li>
                            <a href="#" style="color: #acbcd9; text-shadow: 5px 5px 5px #000000eb; font-weight: bold;"><i class="fa-bitcoin fa"></i><span class="nav-label">买币大厅</span><span class="fa arrow"></span></a>
                            <ul class="nav nav-second-level">
                                <li><a class="J_menuItem" href="buy_list.aspx?state=nomatch">未交易订单</a>
                                </li>
                                <li><a class="J_menuItem" href="buy_list.aspx?state=task">进行中订单</a>
                                </li>
                                <li><a class="J_menuItem" href="buy_list.aspx?state=finish">已完成订单</a>
                                </li>
                                <li><a class="J_menuItem" href="buy_list.aspx?state=lose">失效订单</a>
                                </li>
                            </ul>
                        </li>


                        <li>
                            <a href="#"><i class="fa-paypal fa"></i><span class="nav-label">充值管理</span><span class="fa arrow"></span></a>
                            <ul class="nav nav-second-level">
                                <li><a class="J_menuItem" href="serv_recharge.aspx?state=recharge">手动充值</a>
                                </li>
                                <li><a class="J_menuItem" href="serv_recharge.aspx?state=deduct">手动扣除</a>
                                </li>
                                <li><a class="J_menuItem" href="serv_recharge.aspx?state=activity">活动充值</a>
                                </li>
                                <li><a class="J_menuItem" href="recharge_list.aspx?state=finish">成功订单</a>
                                </li>
                                <li><a class="J_menuItem" href="recharge_list.aspx?state=task">未充值订单</a>
                                </li>
                                <li><a class="J_menuItem" href="serv_freeze.aspx">冻结列表</a>
                                </li>
                            </ul>
                        </li>

                        <li>
                            <a href="#"><i class="fa-cart-plus fa"></i><span class="nav-label">任务管理</span><span class="fa arrow"></span></a>
                            <ul class="nav nav-second-level">
                                <li><a class="J_menuItem" href="task_list.aspx?from_type=xrhl">新人豪礼</a>
                                </li>
                                <li><a class="J_menuItem" href="task_list.aspx?from_type=yrsw">月入十万</a>
                                </li>
                                <li><a class="J_menuItem" href="task_list.aspx?from_type=usdt">USDT买币大礼包</a>
                                </li>
                                <li><a class="J_menuItem" href="task_receive_list.aspx">任务记录</a>
                                </li>
                                <li><a class="J_menuItem" href="task_receive_list.aspx?audit=0">任务记录-待审核</a>
                                </li>
                                <li><a class="J_menuItem" href="luckwheel_records.aspx">转盘记录</a>
                                </li>
                            </ul>
                        </li>

                        <li>
                            <a href="#"><i class="fa-cart-plus fa"></i><span class="nav-label">商品管理</span><span class="fa arrow"></span></a>
                            <ul class="nav nav-second-level">
                                <li><a class="J_menuItem" href="item_list.aspx">商品列表</a>
                                </li>
                                <li><a class="J_menuItem" href="item_classList.aspx?state=finish">商品分类</a>
                                    <asp:Repeater ID="classItems" runat="server">
                                        <ItemTemplate>
                                            <li><a class="J_menuItem" href="item_list.aspx?classid=<%#Eval("id") %>"><%#Eval("name") %></a>
                                            </li>

                                        </ItemTemplate>
                                    </asp:Repeater>
                                </li>
                            </ul>
                        </li>

                        <%--<li>
                            <a href="#"><i class="fa-gittip fa"></i><span class="nav-label">短信管理</span><span class="fa arrow"></span></a>
                            <ul class="nav nav-second-level">
                                <li><a class="J_menuItem" href="sms_records.aspx">发送记录</a>
                                </li>
                            </ul>
                        </li>--%>

                        <asp:Repeater ID="menus" runat="server">
                            <ItemTemplate>
                                <li>
                                    <a class="J_menuItem" href="<%#Eval("url") %>.aspx"><i class="<%#Eval("icon") %>"></i><span class="nav-label"><%#Eval("title") %></span></a>
                                </li>
                            </ItemTemplate>
                        </asp:Repeater>
                        <li>
                            <a href="unknow_auth_out.aspx" target="_blank"><i class="fa-sign-out fa"></i><span class="nav-label">退出登录</span></a>
                        </li>
                    </ul>
                </div>
            </nav>
            <!--左侧导航结束-->
            <!--右侧部分开始-->
            <div id="page-wrapper" class="gray-bg dashbard-1">
                <!--<div class="row border-bottom">
                <nav class="navbar navbar-static-top" role="navigation" style="margin-bottom: 0">
                    <div class="navbar-header">
                        <a class="navbar-minimalize minimalize-styl-2 btn btn-primary " href="#"><i class="fa fa-bars"></i> </a>               
                    </div>
                    
                </nav>
            </div>-->
                <div class="row content-tabs">

                    <!--<div class="navbar-header">
                        <a class="navbar-minimalize minimalize-styl-2 btn btn-primary " href="#"><i class="fa fa-bars"></i> </a>               
                    </div>-->
                    <!--<a class="navbar-minimalize minimalize-styl-2  " href="#">
                    <button class="roll-nav roll-left J_tabLeft">
                        <i class="fa fa-backward"></i>
                    </button>
                </a>-->
                    <a class="roll-nav roll-left  navbar-minimalize" style="background: #fff;">
                        <i class="fa fa-bars"></i>
                    </a>
                    <a class="roll-nav roll-left J_tabLeft" style="left: 40px;" onclick="javascript:;">
                        <i class="fa fa-backward"></i>
                    </a>


                    <nav class="page-tabs J_menuTabs" style="margin-left: 80px;">
                        <div class="page-tabs-content">
                            <a href="javascript:;" class="active J_menuTab" data-id="base_data.aspx">后台主页</a>
                        </div>
                    </nav>
                    <button class="roll-nav roll-right J_tabRight" type="button">
                        <i class="fa fa-forward"></i>
                    </button>
                    <div class="btn-group roll-nav roll-right">
                        <button class="dropdown J_tabClose" data-toggle="dropdown">
                            关闭操作<span class="caret"></span>

                        </button>
                        <ul role="menu" class="dropdown-menu dropdown-menu-right">
                            <li class="J_tabShowActive"><a>定位当前选项卡</a>
                            </li>
                            <li class="divider"></li>
                            <li class="J_tabCloseAll"><a>关闭全部选项卡</a>
                            </li>
                            <li class="J_tabCloseOther"><a>关闭其他选项卡</a>
                            </li>
                            <li class="J_passwordEdit"><a onclick="open_new_page('password_edit.aspx','修改密码')">修改密码</a>
                            </li>
                        </ul>
                    </div>
                    <a href="unknow_auth_out.aspx" class="roll-nav roll-right J_tabExit"><i class="fa fa fa-sign-out"></i>退出</a>
                </div>
                <div class="row J_mainContent" id="content-main" style="<%=check_isMobile() ? "overflow: auto;-webkit-overflow-scrolling:touch;width:100%;height:100%;": "" %>">
                    <iframe class="J_iframe" name="iframe0" width="100%" height="100%" src="base_data.aspx" frameborder="0" data-id="base_data.aspx" seamless></iframe>
                </div>
                <%--<div class="footer">
                    <div class="pull-left">
                        <a href="http://www.miitbeian.gov.cn/" target="_blank"><%=uConfig.stcdata("beianhao") %> </a>
                    </div>
                    <div class="pull-right">
                        &copy; <%=uConfig.stcdata("bqxx") %>
                    </div>
                </div>--%>
            </div>
        </div>
        <script src="../js/bootstrap.min.js?v=3.3.6"></script>
        <script src="../js/plugins/metisMenu/jquery.metisMenu.js"></script>
        <script src="../js/plugins/slimscroll/jquery.slimscroll.min.js"></script>
        <script src="../js/plugins/layer/layer.min.js"></script>
        <script src="../js/hplus.min.js?v=4.1.0"></script>
        <script type="text/javascript" src="../js/contabs.min.js"></script>
        <script src="../js/plugins/pace/pace.min.js"></script>

        <script>
            // v 1.0.1 拓展方法
            function setActiveLabName(name) {
                $(".J_menuTab.active").html(name + " <i class=\"fa fa-times-circle\"></i>")
            }
            function close_this_page() {
                $(".J_menuTab.active .fa-times-circle").click();
            }
            function active_page_title() {
                return $(".J_menuTab.active").text().trim()
            }

            function open_new_page(url, name) {
                var o = url
                    , m = "-1"
                    , l = $.trim(name)
                    , k = true;
                if (o == undefined || $.trim(o).length == 0) {
                    return false
                }
                $(".J_menuTab").each(function () {
                    if ($(this).data("id") == o) {
                        if (!$(this).hasClass("active")) {
                            $(this).addClass("active").siblings(".J_menuTab").removeClass("active");
                            g(this);
                            $(".J_mainContent .J_iframe").each(function () {
                                if ($(this).data("id") == o) {
                                    $(this).show().siblings(".J_iframe").hide();
                                    return false
                                }
                            })
                        }
                        k = false;
                        return false
                    }
                });
                if (k) {
                    var p = '<a href="javascript:;" class="active J_menuTab" data-id="' + o + '">' + l + ' <i class="fa fa-times-circle"></i></a>';
                    $(".J_menuTab").removeClass("active");
                    var n = '<iframe class="J_iframe" name="iframe' + m + '" width="100%" height="100%" src="' + o + '" frameborder="0" data-id="' + o + '" seamless></iframe>';
                    $(".J_mainContent").find("iframe.J_iframe").hide().parents(".J_mainContent").append(n);
                    $(".J_menuTabs .page-tabs-content").append(p);
                }
                return false
            }

            function rbtip(title, content, area) {
                if (!area) {
                    area = ['300px', '150px'];
                }
                layer.open({
                    title: title,
                    type: 1,
                    skin: 'layui-layer-demo1', //样式类名
                    closeBtn: 1, //不显示关闭按钮
                    shift: 2,
                    area: area, //宽高
                    // shadeClose: true, //开启遮罩关闭
                    shade: 0,   //遮罩透明度
                    scrollbar: false,
                    content: content,
                    offset: 'rb', // 右下角弹出
                });
            }
        </script>
    </form>
</body>
</html>

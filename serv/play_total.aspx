<%@ Page Title="用户游戏数据" Language="C#" MasterPageFile="~/serv/m.master" AutoEventWireup="true" CodeFile="play_total.aspx.cs" Inherits="w_index" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="Server">
    <link href="../css/element-ui.css" rel="stylesheet" />
    <style>
        .el-tag.el-tag--test {
            background-color: #d5d5f5;
            border-color: #7d7dc7;
            color: #6d3333;
        }
    </style>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="Server">
    <div class="ibox float-e-margins">
        <div class="ibox-title">
            <h5>用户游戏数据</h5>
        </div>
        <div class="ibox-content">
            <%if (Request.QueryString["parentid"] + "" != "" || Request.QueryString["teamid"] + "" != "")
              {
            %>
            <a class="btn btn-success" onclick="history.back();" style="margin-bottom: 18px;">返回页面</a>

            <%
              } %>


            
            <style>
                .switch-1 {
                    padding: 7px 12px;
                    border-radius: 3px;
                    font-weight: bold;
                    display: flex;
                    align-items: center;
                    margin-bottom: 10px;
                    margin-right: 10px;
                    cursor: pointer;
                    background: #eee;
                    color: #5a5b5c;
                    border: 1px solid #ddd;
                }

                    .switch-1.active {
                        background: linear-gradient(119deg, #6799ed, #67bf8b);
                        color: #fff;
                        border: 1px solid transparent;
                    }
            </style>

            <div style="display: flex;">

                <div class="switch-1" from_type="filter_th">
                    <svg t="" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="6932" width="23" height="23"  style="margin-right: 5px;"><path d="M810.0864 210.7904H261.6832a56.32 56.32 0 0 0-35.4816 99.9424l204.8 166.5536a10.5472 10.5472 0 0 1 3.8912 8.192V803.84a36.4032 36.4032 0 0 0 18.2784 31.5392l113.5104 64.8704a47.0016 47.0016 0 0 0 70.3488-40.96v-373.76a10.5472 10.5472 0 0 1 3.8912-8.192l204.8-166.5536a56.32 56.32 0 0 0-35.6352-99.9936z" fill="#9FA7FF" p-id="6933"></path><path d="M845.6192 310.7328a56.832 56.832 0 0 0 15.7696-20.0192 51.2 51.2 0 0 0-47.3088-30.0544H303.8208A52.5312 52.5312 0 0 0 256 335.104l174.9504 142.1824a10.5472 10.5472 0 0 1 3.8912 8.192v1.6384l26.4192 21.4528a9.9328 9.9328 0 0 1 3.6352 7.6288v294.7584a33.9456 33.9456 0 0 0 16.9984 29.3888l105.6256 60.3648a45.4656 45.4656 0 0 0 12.1344 4.7104 46.8992 46.8992 0 0 0 37.3248-46.08v-373.76a10.5472 10.5472 0 0 1 3.8912-8.192z" fill="#8891FF" p-id="6934"></path><path d="M818.432 316.2112H350.72A48.0768 48.0768 0 0 0 303.5136 373.76l127.4368 103.5264a10.5472 10.5472 0 0 1 3.8912 8.192v9.0112l60.2112 48.9984a9.0624 9.0624 0 0 1 3.328 7.0144V819.2a30.72 30.72 0 0 0 15.36 26.88L610.7648 901.12h0.3584a46.5408 46.5408 0 0 0 25.6-41.8816v-373.76a10.5472 10.5472 0 0 1 3.8912-8.192l194.56-158.1056a47.9744 47.9744 0 0 0-16.7424-2.9696z" fill="#6E75FF" p-id="6935"></path><path d="M559.9744 937.3184a82.432 82.432 0 0 1-40.96-11.0592l-125.8496-71.68a71.1168 71.1168 0 0 1-35.84-61.44V446.6176l-220.16-178.8928A93.0816 93.0816 0 0 1 196.096 102.4H803.84a93.0816 93.0816 0 0 1 58.88 165.3248l-219.904 178.8928v407.7568a81.92 81.92 0 0 1-41.2672 71.68 82.8416 82.8416 0 0 1-41.5744 11.264zM196.096 163.84a31.6416 31.6416 0 0 0-19.968 56.32l226.9696 184.32a42.2912 42.2912 0 0 1 15.6672 32.9216v355.072a9.5744 9.5744 0 0 0 4.8128 8.2944l125.7984 71.936a21.3504 21.3504 0 0 0 32-18.5344V437.6064a42.1376 42.1376 0 0 1 15.6672-33.1264l226.9184-184.32A31.6416 31.6416 0 0 0 803.84 163.84z" fill="#2E3138" p-id="6936"></path><path d="M889.7536 527.7696h-175.7696a30.72 30.72 0 0 1 0-61.44h175.7696a30.72 30.72 0 0 1 0 61.44zM889.7536 652.2368h-175.7696a30.72 30.72 0 0 1 0-61.44h175.7696a30.72 30.72 0 0 1 0 61.44zM889.7536 773.6832h-175.7696a30.72 30.72 0 0 1 0-61.44h175.7696a30.72 30.72 0 0 1 0 61.44z" fill="#2E3138" p-id="6937"></path></svg>排除托号
                </div>

            </div>
            <input id="from_type" type="hidden" />
            <script>
                $('.switch-1').on('click', function () {
                    from_type = $(this).attr('from_type');
                    if ($(this).hasClass("active")) {
                        $(this).removeClass("active");
                        from_type = "";
                    } else {
                        $(this).addClass("active").siblings().removeClass("active");
                    }
                    $('#from_type').val(from_type);
                    searchList();
                });
            </script>

            <div class="row layui-form">

                <div class="col-md-2">
                    <div class="form-group form-buttom" id="twoInputs">
                        <input type="text" id="date" name="ordername" class="form-control" placeholder="开始时间" style="width: 100%; display: inline-block;" value="<%=Request.QueryString["date"] + "" != "" ?  Convert.ToDateTime(Request.QueryString["date"] + "").ToString("yyyy-MM-dd"):DateTime.Now.ToString("yyyy-MM-dd")  %>" autocomplete="off" />

                        <script>
                            $(document).ready(function () {
                                $('#date').datepicker({
                                    format: 'yyyy-mm-dd',
                                    autoclose: true
                                });
                            });
                        </script>
                    </div>
                </div>

                <div class="col-md-4">
                    <div class="form-group form-buttom">
                        <input type="text" id="search-keyword" name="ordername" class="form-control" placeholder="请输入搜索的信息">
                    </div>
                </div>

                <div class="col-md-6">
                    <div class="form-group form-buttom">
                        <a class="btn btn-success" onclick="searchList()">
                            <svg t="1688237921441" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="1949" width="16" height="16">
                                <path d="M361.130667 658.688a256 256 0 1 1 282.794666-44.672 42.496 42.496 0 0 0-10.368 14.933333c-0.853333 0.682667-1.621333 1.450667-2.389333 2.218667a42.666667 42.666667 0 0 0 0 60.330667l170.666667 170.666666a42.666667 42.666667 0 0 0 60.330666-60.330666l-142.762666-142.762667a341.333333 341.333333 0 1 0-169.344 99.242667 42.666667 42.666667 0 0 0-20.224-82.901334 254.762667 254.762667 0 0 1-168.704-16.725333z" fill="#14101C" p-id="1950"></path></svg>
                            查询</a>
                        <a class="btn btn-success" onclick="javascript:location.href=location.href;">
                            <svg t="1688237949108" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2211" width="16" height="16">
                                <path d="M512 192a298.24 298.24 0 0 0-245.162667 128 42.666667 42.666667 0 1 1-69.973333-48.810667 384.042667 384.042667 0 0 1 697.173333 180.48l36.608-34.645333a42.666667 42.666667 0 0 1 58.709334 61.952l-112.64 106.666667a42.666667 42.666667 0 0 1-71.253334-38.912A298.666667 298.666667 0 0 0 512 192z m0.170667 640a298.24 298.24 0 0 0 245.162666-128 42.666667 42.666667 0 1 1 69.973334 48.810667 384.042667 384.042667 0 0 1-697.173334-180.48l-36.650666 34.645333a42.666667 42.666667 0 0 1-58.666667-61.952l112.64-106.666667a42.666667 42.666667 0 0 1 71.253333 38.912 298.666667 298.666667 0 0 0 293.418667 354.730667z" fill="#14101C" p-id="2212"></path></svg>
                            刷新</a>
                        <%--<a class="btn btn-success" onclick="opennew()">
                            <svg t="1688237854283" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="15169" width="16" height="16">
                                <path d="M725.333333 213.333333H298.666667a85.333333 85.333333 0 0 0-85.333334 85.333334v341.333333a42.666667 42.666667 0 1 1-85.333333 0V298.666667a170.666667 170.666667 0 0 1 170.666667-170.666667h426.666666a170.709333 170.709333 0 0 1 167.850667 139.733333v488.533334A170.709333 170.709333 0 0 1 725.333333 896H298.666667a170.538667 170.538667 0 0 1-144.469334-79.744l1.194667-0.682667a42.666667 42.666667 0 0 1 76.501333-37.034666c15.658667 19.584 39.765333 32.128 66.773334 32.128h426.666666a85.333333 85.333333 0 0 0 85.333334-85.333334V298.666667a85.333333 85.333333 0 0 0-85.333334-85.333334z m-213.333333 512a42.666667 42.666667 0 0 1-42.666667-42.666666v-128H341.333333a42.666667 42.666667 0 1 1 0-85.333334h128V341.333333a42.666667 42.666667 0 1 1 85.333334 0v128h128a42.666667 42.666667 0 1 1 0 85.333334h-128v128a42.666667 42.666667 0 0 1-42.666667 42.666666z" fill="#14101C" p-id="15170"></path></svg>&nbsp;添加</a>--%>
                        <%--<a class="btn btn-success" onclick="update_select(-9)">
                            <svg t="1688237896673" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="1699" width="16" height="16">
                                <path d="M612.693333 170.666667l8.533334 42.666666H402.773333l8.533334-42.666666h201.386666z m-293.632 25.941333a86.186667 86.186667 0 0 0-1.706666 16.725333H170.666667a42.666667 42.666667 0 0 0 0 85.333334h682.666666a42.666667 42.666667 0 1 0 0-85.333334h-146.730666c0-5.504-0.554667-11.093333-1.706667-16.725333l-8.533333-42.666667A85.333333 85.333333 0 0 0 612.778667 85.333333H411.306667a85.333333 85.333333 0 0 0-83.712 68.608l-8.533334 42.666667zM426.666667 469.333333a42.666667 42.666667 0 0 0-42.666667 42.666667v170.666667a42.666667 42.666667 0 1 0 85.333333 0v-170.666667a42.666667 42.666667 0 0 0-42.666666-42.666667z m128 42.666667a42.666667 42.666667 0 1 1 85.333333 0v170.666667a42.666667 42.666667 0 1 1-85.333333 0v-170.666667zM255.402667 384A42.666667 42.666667 0 0 1 298.666667 426.069333L301.738667 640H301.653333a42.666667 42.666667 0 1 1-85.290666-2.176l-2.986667-210.56A42.666667 42.666667 0 0 1 255.36 384z m51.925333 396.586667h0.042667A103.381333 103.381333 0 0 0 406.698667 853.333333h210.602666c54.186667 0 98.56-40.789333 103.210667-92.757333L725.333333 426.069333a42.666667 42.666667 0 0 1 85.333334 1.194667l-4.864 337.365333-0.128 1.365334C798.08 863.872 715.562667 938.666667 617.301333 938.666667H406.698667a188.885333 188.885333 0 0 1-180.096-130.389334 42.666667 42.666667 0 0 1 80.725333-27.648z" fill="#14101C" p-id="1700"></path></svg>删除</a>--%>
                    </div>
                </div>

            </div>
            <div style="display: flex; margin-bottom: 10px;">


                <div style="background: #fff; padding: 17px; border-radius: 8px; width: 33.333%; margin: 0 10px; max-width: 250px;">
                    <div style="display: flex; align-items: center;">
                        <svg t="" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="7153" width="20" height="20">
                            <path d="M513.0752 510.8224m-450.816 0a450.816 450.816 0 1 0 901.632 0 450.816 450.816 0 1 0-901.632 0Z" fill="#FFF7DC" p-id="7154"></path><path d="M863.0784 503.7056c0-52.7872-56.3712-67.0208-86.016-77.1072-29.6448-10.0864-176.0768-64.2048-176.0768-64.2048s-100.1472-163.328-190.0032-101.888c-21.8112 13.7728-18.2272 63.6416 12.032 84.5312-41.8304-7.5776-110.7968 7.9872-107.264 47.616 3.584 39.6288 58.3168 60.0576 155.2896 43.6224 16.896-3.584 40.4992 8.9088 12.032 32.9216-15.1552 10.7008-80.9984 28.928-225.1776 3.1232-102.6048-18.3808-162.816-32.2048-189.0304-38.7072a451.6864 451.6864 0 0 0-6.6048 77.2608c0 149.504 72.8064 282.0096 184.8832 364.032 22.016-6.5536 45.0048-15.104 68.608-26.112 60.5184-28.4672 42.7008-57.856 28.4672-68.096s-42.7008-50.7392 36.9152-98.3552 178.7392-90.1632 296.8064-105.0112c37.9904-5.3248 83.6608-5.9392 105.6256-7.1168 21.9648-1.2288 79.5136-13.6704 79.5136-66.5088z m-260.5056-41.8304c-15.872 0-28.7744-12.9024-28.7744-28.7744s12.9024-28.7744 28.7744-28.7744 28.7744 12.9024 28.7744 28.7744-12.8512 28.7744-28.7744 28.7744z" fill="#FF8E12" p-id="7155"></path><path d="M600.9856 362.3424s-100.1472-163.328-190.0032-101.888c-21.8112 13.7728-18.2272 63.6416 12.032 84.5312-41.8304-7.5776-110.7968 7.9872-107.264 47.616 3.584 39.6288 58.3168 60.0576 155.2896 43.6224 16.896-3.584 40.4992 8.9088 12.032 32.9216-15.1552 10.7008-80.9984 28.928-225.1776 3.1232-102.6048-18.3808-162.816-32.2048-189.0304-38.7072a451.6864 451.6864 0 0 0-6.6048 77.2608c0 104.3456 35.5328 200.3968 95.0784 276.8384a426.51136 426.51136 0 0 0 163.84 32.5632c11.1616 0 22.1696-0.5632 33.1264-1.3824 9.6768-17.1008-0.9216-31.5904-10.0864-38.1952-14.2336-10.24-42.7008-50.7392 36.9152-98.3552s178.7392-90.1632 296.8064-105.0112c9.8304-1.3824 20.1728-2.4576 30.464-3.2768a425.0112 425.0112 0 0 0 39.68-157.696c-52.48-18.944-147.0976-53.9648-147.0976-53.9648z m1.5872 99.5328c-15.872 0-28.7744-12.9024-28.7744-28.7744s12.9024-28.7744 28.7744-28.7744 28.7744 12.9024 28.7744 28.7744-12.8512 28.7744-28.7744 28.7744z" fill="#FCA315" p-id="7156"></path><path d="M410.9312 260.4544C389.12 274.2272 392.704 324.096 422.9632 344.9856c-41.8304-7.5776-110.7968 7.9872-107.264 47.616 3.584 39.6288 58.3168 60.0576 155.2896 43.6224 16.896-3.584 40.4992 8.9088 12.032 32.9216-15.1552 10.7008-80.9984 28.928-225.1776 3.1232-102.6048-18.3808-162.816-32.2048-189.0304-38.7072a451.6864 451.6864 0 0 0-6.6048 77.2608c0 49.0496 7.8848 96.256 22.3744 140.4416a428.34944 428.34944 0 0 0 119.552 17.0496c160.8704 0 300.9536-88.8832 373.9648-220.16-2.7136-4.4032-4.3008-9.5232-4.3008-15.0528 0-14.7456 11.1104-26.88 25.3952-28.5696 5.12-12.3392 9.728-24.9344 13.6704-37.7856-7.424-2.7648-11.9296-4.4032-11.9296-4.4032s-100.096-163.2768-190.0032-101.888z" fill="#FCB138" p-id="7157"></path><path d="M257.8944 472.2688c-102.6048-18.3808-162.816-32.2048-189.0304-38.7072a451.6864 451.6864 0 0 0-6.6048 77.2608c0 1.5872 0.0512 3.1232 0.0512 4.6592 11.3152 0.9216 22.7328 1.4848 34.2528 1.4848 65.4848 0 127.5392-14.7968 183.04-41.1136-7.0656-1.024-14.2336-2.2528-21.7088-3.584zM422.9632 344.9856c-41.8304-7.5776-110.7968 7.9872-107.264 47.616 1.5872 17.92 13.7216 31.8976 35.4816 40.3456 61.184-45.4144 109.824-106.752 139.7248-177.8688-25.6512-11.264-53.248-12.8512-79.9744 5.376-21.76 13.824-18.2272 63.6416 12.032 84.5312z" fill="#FFC65E" p-id="7158"></path></svg>
                        <span style="color: #fba722; font-weight: bold; margin-left: 8px;">游戏总额</span>
                    </div>

                    <div style="font-size: 22px; margin-top: 6px; font-weight: bold; color: #7e868f;">
                        <span id="total_amount">0.00</span><span style="font-size: 12px; font-weight: 100; margin-left: 10px;">[<span id="total_number">0</span>单]</span>
                    </div>

                    <div style="display: flex; align-items: center; margin-top: 8px; font-size: 12px; color: #3e1073;">
                        总数据 <span id="total_data_amount" style="margin-left: 2px;">0.00</span>
                    </div>
                    <div style="display: flex; align-items: center; margin-top: 3px; font-size: 12px; color: #8f5543;">
                        未统计 <span id="unsettle_amount" style="margin-left: 2px;">0.00</span>
                    </div>

                </div>

                <div id="play_return_amount" style="background: #fff; padding: 17px; border-radius: 8px; width: 33.333%; margin: 0 10px; max-width: 250px; display: <%=Request.QueryString["parentid"] + "" != ""?"block;":"none;" %>;">
                    <div style="display: flex; align-items: center;">
                        <svg t="" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="7371" width="20" height="20">
                            <path d="M505.088 513.1264m-450.816 0a450.816 450.816 0 1 0 901.632 0 450.816 450.816 0 1 0-901.632 0Z" fill="#E9F4FF" p-id="7372"></path><path d="M705.0752 649.3696l-148.3776-78.6432 42.752-61.0816a39.1168 39.1168 0 0 0 7.1168-22.5792V369.2544c0-78.5408-63.8976-142.4384-142.4384-142.4384S321.6896 290.6624 321.6896 369.2544v117.5552c0 8.4992 2.816 16.9472 7.9872 23.7568l44.288 58.5728-160.7168 84.2752a39.24992 39.24992 0 0 0-21.0944 34.8672v41.8816c0 21.7088 17.664 39.3728 39.3728 39.3728h455.2192c21.7088 0 39.3728-17.664 39.3728-39.3728v-45.9776a39.53664 39.53664 0 0 0-21.0432-34.816z" fill="#2595E8" p-id="7373"></path><path d="M843.2128 604.3136l-148.3776-78.6432 42.752-61.0816a39.1168 39.1168 0 0 0 7.1168-22.5792V324.1472c0-78.5408-63.8976-142.4384-142.4384-142.4384-13.6192 0-24.6784 11.0592-24.6784 24.6784s11.0592 24.6784 24.6784 24.6784c51.3536 0 93.1328 41.7792 93.1328 93.1328v114.7392l-47.4112 67.7376a39.6032 39.6032 0 0 0-6.0416 31.7952 39.62368 39.62368 0 0 0 19.8656 25.6L814.848 645.12v30.0032h-20.992c-13.6192 0-24.6784 11.0592-24.6784 24.6784s11.0592 24.6784 24.6784 24.6784h30.976c21.7088 0 39.3728-17.664 39.3728-39.3728v-45.9776c0-14.6432-8.0384-28.0064-20.992-34.816z" fill="#2595E8" p-id="7374"></path><path d="M556.6976 570.7264l42.752-61.0816a39.1168 39.1168 0 0 0 7.1168-22.5792V369.2544c0-78.5408-63.8976-142.4384-142.4384-142.4384S321.6896 290.7136 321.6896 369.2544v117.5552c0 8.4992 2.816 16.9472 7.9872 23.7568l44.288 58.5728-160.7168 84.2752a39.24992 39.24992 0 0 0-21.0944 34.8672v41.8816c0 21.7088 17.664 39.3728 39.3728 39.3728h247.5008c78.3872-25.9584 147.2-72.8576 199.8848-134.0416l-122.2144-64.768z" fill="#3A9CED" p-id="7375"></path><path d="M606.5664 402.688v-33.4336c0-78.5408-63.8976-142.4384-142.4384-142.4384S321.6896 290.7136 321.6896 369.2544v117.5552c0 8.4992 2.816 16.9472 7.9872 23.7568l44.288 58.5728-116.3264 60.9792c149.9136-14.4896 278.3744-102.4512 348.928-227.4304z" fill="#59ADF8" p-id="7376"></path><path d="M737.5872 464.5888a39.1168 39.1168 0 0 0 7.1168-22.5792V324.1472c0-78.5408-63.8976-142.4384-142.4384-142.4384-13.6192 0-24.6784 11.0592-24.6784 24.6784s11.0592 24.6784 24.6784 24.6784c51.3536 0 93.1328 41.7792 93.1328 93.0816v114.7392l-47.4112 67.7376a39.6032 39.6032 0 0 0-6.0416 31.7952 39.62368 39.62368 0 0 0 19.8656 25.6l50.8416 26.9312a447.8464 447.8464 0 0 0 25.088-42.5472l-42.8544-22.6816 42.7008-61.1328z" fill="#3A9CED" p-id="7377"></path><path d="M602.2656 181.7088c-13.6192 0-24.6784 11.0592-24.6784 24.6784s11.0592 24.6784 24.6784 24.6784c21.4528 0 41.216 7.3728 56.9856 19.6608 2.7648-17.92 4.4544-36.1984 5.0176-54.7328a140.72832 140.72832 0 0 0-62.0032-14.2848z" fill="#59ADF8" p-id="7378"></path><path d="M499.6608 231.2704c-11.3664-2.9184-23.2448-4.5056-35.5328-4.5056-78.5408 0-142.4384 63.8976-142.4384 142.4384v45.4656a452.9408 452.9408 0 0 0 177.9712-183.3984z" fill="#6BC2FC" p-id="7379"></path></svg>
                        <span style="color: #3e9eee; font-weight: bold; margin-left: 8px;">总佣金</span>
                    </div>

                    <div style="font-size: 22px; margin-top: 6px; font-weight: bold; color: #2a2b2c;">
                        <span id="total_teambork">0.00</span>
                    </div>
                </div>

                <%--   


                <div style="background: #fff; padding: 17px; border-radius: 8px; width: 33.333%; margin: 0 10px; max-width: 250px;">
                    <div style="display: flex; align-items: center;">
                        <svg t="" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="7975" width="20" height="20">
                            <path d="M509.2864 517.7856m-450.816 0a450.816 450.816 0 1 0 901.632 0 450.816 450.816 0 1 0-901.632 0Z" fill="#E9ECFF" p-id="7976"></path><path d="M450.9184 232.704m-86.1696 0a86.1696 86.1696 0 1 0 172.3392 0 86.1696 86.1696 0 1 0-172.3392 0Z" fill="#8486F8" p-id="7977"></path><path d="M700.0064 444.416m-72.6016 0a72.6016 72.6016 0 1 0 145.2032 0 72.6016 72.6016 0 1 0-145.2032 0Z" fill="#757BF2" p-id="7978"></path><path d="M317.2352 807.8336c-19.6608-94.3104-33.792-317.0304-9.9328-382.6176-13.2608 27.8528-42.752 118.0672-56.3712 152.1152-7.9872 20.0192-40.9088 13.1584-44.1856-8.1408-7.3216-47.7696-10.5984-149.8112 93.6448-269.5168 16.6912-21.2992 48.7424-15.2576 67.8912 9.5744s45.056 49.3056 107.4176 37.9392c19.5072-4.2496 43.9808-16.6912 64.2048 38.656s49.664 142.5408 137.5744 160.9728c21.248 1.9456 71.68-4.864 119.1936-33.4336 8.6528-5.2224 19.712-4.1984 27.0336 2.7648l0.4096 0.3584c8.448 8.0384 9.1136 21.0944 1.792 30.1568-12.8 15.8208-36.4032 35.328-65.4336 52.5824-46.1312 27.392-49.664 43.264-29.7984 97.1776 19.8656 53.9136 60.2624 92.928 35.4816 114.176-24.832 21.2992-65.2288 5.6832-121.2928-114.176-2.1504 54.6304 16.4352 127.1296-17.4592 133.5296-33.9456 6.4-69.0176-34.56-70.0928-157.952-1.0752-28.7232 5.3248-79.7696 5.3248-79.7696s-62.7712-75.52-72.3456-130.8672c-11.2128 31.2832-37.9904 195.4816-43.4176 335.104-1.3312 33.7408-27.9552 60.9792-61.696 62.5664l-1.4848 0.0512c-31.744 1.5872-59.9552-20.1216-66.4576-51.2512z" fill="#6D6DF2" p-id="7979"></path><path d="M677.4784 546.8672c-87.9616-18.432-117.3504-105.6768-137.5744-160.9728-20.224-55.296-44.6976-42.9056-64.2048-38.656-62.4128 11.3664-88.2688-13.1072-107.4176-37.9392-19.1488-24.832-51.2512-30.8736-67.8912-9.5744-104.2432 119.6544-100.9152 221.696-93.6448 269.5168 3.2768 21.2992 36.1472 28.2112 44.1856 8.1408 13.6192-34.048 43.1104-124.2624 56.3712-152.1152-23.8592 65.536-9.728 288.3072 9.9328 382.6176 1.9456 9.4208 5.9392 17.92 11.3664 25.2416h0.9216c38.8096 0 76.4416-4.9152 112.384-14.1312 2.8672-6.9632 4.608-14.4896 4.9152-22.4256 5.4272-139.6224 32.2048-303.8208 43.4176-335.104 9.5744 55.296 72.3456 130.8672 72.3456 130.8672s-6.4 51.0464-5.3248 79.7696c0.3584 38.912 4.0448 69.632 10.0864 93.2352 28.0064-17.408 53.9136-37.7856 77.3632-60.7232 0-2.7136 0.0512-5.4272 0.1536-8.0384 0.8704 1.8432 1.6896 3.584 2.56 5.376a450.816 450.816 0 0 0 106.9056-168.448c-32.5632 11.7248-61.9008 14.7456-76.8512 13.3632z" fill="#757BF2" p-id="7980"></path><path d="M539.904 385.8944c-20.224-55.296-44.6976-42.9056-64.2048-38.656-62.4128 11.3664-88.2688-13.1072-107.4176-37.9392-19.1488-24.832-51.2512-30.8736-67.8912-9.5744-104.2432 119.6544-100.9152 221.696-93.6448 269.5168 3.2768 21.2992 36.1472 28.2112 44.1856 8.1408 13.6192-34.048 43.1104-124.2624 56.3712-152.1152-14.9504 41.0624-14.9504 143.872-8.0896 238.1312a448.90624 448.90624 0 0 0 166.8608-72.7552c8.8576-63.488 18.5344-113.3568 24.1664-129.1776 4.352 25.1904 19.712 54.528 35.2256 79.0528 20.0704-20.1216 38.2464-42.1376 54.272-65.792-18.7392-29.696-30.3104-62.7712-39.8336-88.832z" fill="#8486F8" p-id="7981"></path><path d="M450.9184 146.5344c-47.5648 0-86.1696 38.5536-86.1696 86.1696s38.5536 86.1696 86.1696 86.1696c4.6592 0 9.216-0.4608 13.6704-1.1776 26.624-38.7584 47.36-81.8688 60.8768-128.0512-14.8992-25.7536-42.6496-43.1104-74.5472-43.1104zM440.32 349.8496c-37.7344-1.8944-56.9856-21.0944-72.0384-40.5504-19.1488-24.832-51.2512-30.8736-67.8912-9.5744-67.2256 77.1584-89.7024 146.944-95.232 199.6288 29.696-7.6288 58.2144-18.176 85.248-31.3856 6.6048-18.0736 12.6464-33.6896 16.9472-42.7008-3.5328 9.6256-6.1952 22.784-8.1408 38.2976 54.3744-28.0576 102.4-66.9184 141.1072-113.7152z" fill="#8D92F8" p-id="7982"></path></svg>
                        <span style="color: #757bf2; font-weight: bold; margin-left: 8px;">用户当日佣金</span>
                    </div>

                    <div style="font-size: 22px; margin-top: 6px; font-weight: bold; color: #2a2b2c;">
                        <span id="total_userbork">0.00</span>
                    </div>
                </div>--%>
            </div>

            <div class="table-responsive">
                <table class="table">
                    <thead>
                        <tr role="head">
                            <th sort="true">用户</th>
                            <th sort="true">直属上级</th>
                            <th sort="true">团队ID</th>
                            <th sort="true">已统计</th>
                            <th sort="true">今日游戏</th>
                            <th sort="true">今日单量</th>
                            <th sort="true">日期</th>
                            <th sort="true"></th>
                        </tr>
                    </thead>
                    <tbody id="list-display">
                    </tbody>
                </table>
            </div>


            <div class="layui-table-page">
                <div id="layui-table-page1">
                    <div class="layui-box layui-laypage layui-laypage-default" id="layui-laypage-1">
                        <div style="display: inline-block;" class="cpages">
                            <!--page grow -->
                        </div>
                        <span class="layui-laypage-skip">到第<input type="text" min="1" value="1" class="layui-input target-page" />页
                            <button type="button" class="layui-laypage-btn" onclick="javascript:pager_index=$('.target-page').val()-1;getPager();">确定</button></span>
                        <span class="layui-laypage-count">共 <span class="page_count"></span>条</span>
                        <span class="layui-laypage-limits">
                            <select class="s_pages" lay-ignore="" onchange="javascript:pager_size=$(this).val();getPager();">
                                <option value="10">10 条/页</option>
                                <option value="20" selected="">20 条/页</option>
                                <option value="30">30 条/页</option>
                                <option value="50">50 条/页</option>
                                <option value="100">100 条/页</option>
                                <option value="200">200 条/页</option>
                            </select></span>
                    </div>
                </div>
            </div>

        </div>
    </div>
    <script>
        var _action = 'play_total';
        var _actionName = '用户游戏数据';
    </script>
    <script>
        function opennew() {
            var modify_model = [];

            modify_model.push({ name: '返佣备注', id: 'name', value: '' });
            modify_model.push({ name: '达成业绩', id: 'team_amount', value: '' });
            modify_model.push({ name: '返佣金额', id: 'brok_amount', value: '' });

            edit({ aname: 'admin', title: '添加' + _actionName, action: _action, id: '', data: modify_model });
        }
    </script>
    <script>
        var return_amount = '<%=Request.QueryString["return_amount"] %>';

        var pager_size = 20;
        var pager_index = 0;
        var pager_key = "";


        $("#pagesize").val(pager_key);
        function setPageSize(obj) {
            pager_index = 0;
            pager_key = $("#search-keyword").val();
            opt = $(obj).val();
            getPager();
        }

        function searchList() {
            opt = $("#opt1").val();
            pager_index = 0;
            pager_key = $("#search-keyword").val();
            getPager();
        }

        function getPager() {

            $.ajax({
                type: "POST",
                url: "../api/admin.aspx?do=page",
                data: { limit: pager_size, p: pager_index, page: _action, keyword: pager_key, date: $("#date").val(), parentid: '<%=Request.QueryString["parentid"] %>', teamid: '<%=Request.QueryString["teamid"] %>', from_type: $('#from_type').val() },
                datatype: "json",
                success: function (json) {
                    $("#list-display").html("");
                    if (json.code == 1) {
                        var data = json.data.list;
                        for (var i = 0; i < data.length; i++) {
                            var tr = "";
                            tr += "<tr class='test1'>";
                            tr += ("<td class=''>" + ('<span style="background: #f3ecec;color: #956b0a;font-size: 12px;border-radius: 3px;padding: 1px 4px;margin-right: 5px;font-weight: bold;border: 1px solid #ccc;">' + (data[i].typename == 'th' ? '托号' : data[i].typename == 'user' ? '普通' : data[i].typename == 'super' ? '高级' : data[i].typename == 'reg' ? '新' : '其他') + '</span>') + (data[i].group_name == '-' ? '' : "<span class='button-style style-js'>" + data[i].group_name + "</span>") + data[i].phone + '<span style="color: #b53d10;margin-left: 2px;">[' + data[i].userid + ']</span>' + "</td>");

                            tr += ("<td class=''>" + ('<span style="background: #f3ecec;color: #956b0a;font-size: 12px;border-radius: 3px;padding: 1px 4px;margin-right: 5px;font-weight: bold;border: 1px solid #ccc;">' + (data[i].parent_typename == 'th' ? '托号' : data[i].parent_typename == 'user' ? '普通' : data[i].parent_typename == 'super' ? '高级' : data[i].parent_typename == 'reg' ? '新' : '其他') + '</span>') + data[i].parent_phone + '<span style="color: #b53d10;margin-left: 2px;">[' + data[i].parentid + ']</span>' + "</td>");
                            tr += ("<td class=''>" + data[i].relaids + "</td>");
                            tr += ("<td class='' style='" + (data[i].complete_num == data[i].num ? '' : '    color: #bbb;') + "'>" + data[i].complete_amount + '<span style="color: #b53d10;margin-left: 2px;">[' + data[i].complete_num + ']</span>' + "</td>");
                            tr += ("<td class=''>" + (data[i].complete_num == data[i].num ? '' : '<svg style="margin-right: 4px;" height="12" width="12" p-id="9650" xmlns="http://www.w3.org/2000/svg" version="1.1" viewBox="0 0 1024 1024" class="icon" t="1710581812505"><path p-id="9651" fill="#035DFF" d="M929.499429 388.242286L563.858286 21.723429A73.142857 73.142857 0 0 0 512.438857 0a72.996571 72.996571 0 0 0-52.077714 21.723429L94.793143 388.242286c-46.226286 46.372571-13.458286 124.928 52.004571 124.928h146.285715v146.066285c0 40.448 32.768 73.289143 73.142857 73.289143h292.425143c40.374857 0 73.142857-32.914286 73.142857-73.289143V513.170286h146.212571c64.950857 0 97.060571-78.555429 51.492572-124.928z m-235.739429 418.157714H328.118857a36.498286 36.498286 0 0 0-36.205714 36.352c0 19.968 16.384 36.352 36.205714 36.352h366.226286a36.498286 36.498286 0 0 0 36.278857-36.352 36.571429 36.571429 0 0 0-36.864-36.352z m0 144.822857H328.118857a36.498286 36.498286 0 0 0-36.205714 36.425143c0 19.894857 16.384 36.352 36.205714 36.352h366.226286a36.498286 36.498286 0 0 0 36.278857-36.352 36.571429 36.571429 0 0 0-36.864-36.352z"></path></svg>') + data[i].amount + (return_amount == '' ? '' : '<span style="color:#9f5d5d;margin-left:8px;">[' + (parseFloat(data[i].amount) / 10000 * parseFloat(return_amount)).toFixed(2) + ']</span>') + "</td>");
                            tr += ("<td class=''>" + data[i].num + "</td>");
                            tr += ("<td class=''><b>" + data[i].create_date + "</b></td>");
                            

                            var modify_model = new Array();

                            tr += ("<td class='action-cell '>"
                                + ' <div class="card-toolbar">'
                                + '</div></td>');


                            tr += "</tr>";
                            $("#list-display").append(tr)
                        }
                        $(".page_size").html(pager_size);
                        $(".page_count").html(json.data.count);
                        $(".cpages").html(json.data.html);



                        $("#total_amount").html(json.total_amount);
                        $("#total_number").html(json.total_number);
                        $("#total_teambork").html((parseFloat(json.total_amount) / 10000 * parseFloat(return_amount)).toFixed(2) + '<span style="font-size: 13px; font-weight: 100; margin-left: 10px;">[<span>' + return_amount + '/每万</span>]</span>');
                        $("#total_userbork").html(json.total_userbork);

                        $("#total_data_amount").html(json.total_data_amount);
                        var unsettle_amount = parseFloat(json.total_data_amount) - parseFloat(json.total_amount);
                        $('#unsettle_amount').html(parseFloat(unsettle_amount).toFixed(2));

                    } else {

                    }
                },
                error: function () {
                }
            });
        }
        getPager();

        function _pt(i) {
            pager_index = i;
            getPager();
        }
    </script>

    <script>
        layui.use(['form', 'layedit', 'laydate'], function () {
        });
    </script>

    <script>
        function update_select(state) {
            event_create({ 'aname': 'admin', 'name': _action, 'event': 'update', 'id': select_all_id(), 'state': state });
        }

        function _event(evt) {
            event_create({ 'aname': 'admin', 'name': _action, 'event': evt, 'id': select_all_id() });
        }

        //function report_exl() {
        //    var start_time = "";
        //    var end_time = "";
        //    var opt1 = req("id");
        //    var opt2 = $("#opt1").val();
        //    var checktime = $("#checktime").val();
        //    var g = checktime.split(' - ');
        //    console.log(g);
        //    if (g.length > 1) {
        //        start_time = g[0];
        //        end_time = g[1];
        //    }

        //    export_data({ name: "cdks", key: pager_key, start_time: start_time, end_time: end_time, opt1: opt1, opt2: opt2 });
        //}
    </script>

    <script>
        var click_ids = {};
        function checkIsSelect(obj) {
            if (obj.prop('checked')) {
                return true;
            } else {
                return false;
            }
        }
        function goods_click(t) {
            t = $(t);
            var obj = t.find(".good_select");
            obj.prop("checked", !checkIsSelect(obj));

        }

        function select_all(obj) {
            $(".good_select").prop("checked", checkIsSelect($(obj)));
        }

        function select_all_id() {
            var ids = "";
            $("#list-display tr").each(function () {
                var select_obj = $(this).find(".good_select");
                if (checkIsSelect(select_obj)) {
                    if (ids != "") {
                        ids += ",";
                    }
                    ids += select_obj.data("id");
                }
            });
            return ids;
        }
    </script>

    <%-- <script src="https://www.jq22.com/demo/jquerytuozhuai202005141436/js/Sortable.min.js"></script>

    
<script type="text/javascript">
    new Sortable(document.getElementById('list-display'), {
        handle: '.good_select', // handle's class
        animation: 150,
        // direction: 'horizontal',,

        // 结束拖拽
        onEnd: function (/**Event*/evt) {
            var itemEl = evt.item;  // dragged HTMLElement
            evt.to;    // target list
            evt.from;  // previous list
            evt.oldIndex;  // element's old index within old parent
            evt.newIndex;  // element's new index within new parent
            evt.clone // the clone element
            evt.pullMode;  // when item is in another sortable: `"clone"` if cloning, `true` if moving

            console.log('evt', evt.to, evt.from, evt.oldIndex, evt.newIndex);
        }
    });

    //$(".list_con .iconshanchu").bind('touchstart', function () {
    //    $(this).parent().parent().remove();
    //    event.preventDefault();
    //    event.stopPropagation();
    //});
</script>
    --%>
</asp:Content>


<%@ Page Title="一键任务额度记录" Language="C#" MasterPageFile="~/serv/m.master" AutoEventWireup="true" CodeFile="task_onetouch_balance.aspx.cs" Inherits="w_index" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="Server">
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="Server">
    <div class="ibox float-e-margins">
        <div class="ibox-title">
            <h5>一键任务额度记录</h5>
        </div>
        <div class="ibox-content">
            <div class="row layui-form">


                <div class="col-md-2">
                    <select id="typeid">
                        <option value="">全部</option>
                        <option>扣除额度</option>
                        <option>返还额度</option>
                        <option>任务撤销</option>
                    </select>
                </div>


                <div class="col-md-3">
                    <div class="form-group form-buttom">
                        <input type="text" id="search-keyword" name="ordername" class="form-control" placeholder="请输入搜索的信息">
                    </div>
                </div>

                <div class="col-md-3">
                    <div class="form-group form-buttom" id="twoInputs">
                        <input type="text" id="start_time" name="ordername" class="form-control" placeholder="开始时间" style="width: 48%; display: inline-block;" value="<%=(Request.QueryString["ss"] + "" != "" ? (Request.QueryString["ss"] + "").Replace("~", "") : DateTime.Now.ToString("yyyy-MM-dd")) %>" autocomplete="off">
                        <input type="text" id="end_time" name="ordername" class="form-control" placeholder="结束时间" style="width: 48%; display: inline-block;" autocomplete="off" value="<%=(Request.QueryString["ee"] + "" != "" ? (Request.QueryString["ee"] + "").Replace("~", "") : DateTime.Now.ToString("yyyy-MM-dd")) %>">

                        <script>
                            $(document).ready(function () {
                                $('#start_time').datepicker({
                                    format: 'yyyy-mm-dd',
                                    autoclose: true
                                });

                                $('#end_time').datepicker({
                                    format: 'yyyy-mm-dd',
                                    autoclose: true
                                });
                            });
                        </script>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-group form-buttom">

                        <a class="btn btn-success" onclick="searchList()">
                            <svg t="1688237921441" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="1949" width="16" height="16">
                                <path d="M361.130667 658.688a256 256 0 1 1 282.794666-44.672 42.496 42.496 0 0 0-10.368 14.933333c-0.853333 0.682667-1.621333 1.450667-2.389333 2.218667a42.666667 42.666667 0 0 0 0 60.330667l170.666667 170.666666a42.666667 42.666667 0 0 0 60.330666-60.330666l-142.762666-142.762667a341.333333 341.333333 0 1 0-169.344 99.242667 42.666667 42.666667 0 0 0-20.224-82.901334 254.762667 254.762667 0 0 1-168.704-16.725333z" fill="#14101C" p-id="1950"></path></svg>
                            查询</a>
                        <a class="btn btn-success" onclick="javascript:location.href=location.href;">
                            <svg t="1688237949108" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2211" width="16" height="16">
                                <path d="M512 192a298.24 298.24 0 0 0-245.162667 128 42.666667 42.666667 0 1 1-69.973333-48.810667 384.042667 384.042667 0 0 1 697.173333 180.48l36.608-34.645333a42.666667 42.666667 0 0 1 58.709334 61.952l-112.64 106.666667a42.666667 42.666667 0 0 1-71.253334-38.912A298.666667 298.666667 0 0 0 512 192z m0.170667 640a298.24 298.24 0 0 0 245.162666-128 42.666667 42.666667 0 1 1 69.973334 48.810667 384.042667 384.042667 0 0 1-697.173334-180.48l-36.650666 34.645333a42.666667 42.666667 0 0 1-58.666667-61.952l112.64-106.666667a42.666667 42.666667 0 0 1 71.253333 38.912 298.666667 298.666667 0 0 0 293.418667 354.730667z" fill="#14101C" p-id="2212"></path></svg>
                            刷新</a>


                        <a class="btn btn-success" onclick="report_exl()">
                            <svg t="1695489540076" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="5479" width="16" height="16">
                                <path d="M686.08 989.866667H165.546667c-51.2 0-92.16-40.96-92.16-92.16V126.293333c0-51.2 40.96-92.16 92.16-92.16h573.44c6.826667 0 11.946667 1.706667 17.066666 6.826667l187.733334 174.08c5.12 5.12 8.533333 11.946667 8.533333 18.773333v256c0 13.653333-11.946667 25.6-25.6 25.6s-25.6-11.946667-25.6-25.6V244.053333L728.746667 85.333333h-563.2C141.653333 85.333333 122.88 104.106667 122.88 126.293333v769.706667c0 22.186667 18.773333 40.96 40.96 40.96h520.533333c13.653333 0 25.6 11.946667 25.6 25.6S699.733333 989.866667 686.08 989.866667z" fill="#333333" p-id="5480"></path><path d="M919.893333 259.413333h-121.173333c-51.2 0-92.16-40.96-92.16-92.16V59.733333c0-13.653333 11.946667-25.6 25.6-25.6s25.6 11.946667 25.6 25.6v105.813334c0 22.186667 18.773333 40.96 40.96 40.96h121.173333c13.653333 0 25.6 11.946667 25.6 25.6s-11.946667 27.306667-25.6 27.306666zM500.053333 305.493333H208.213333c-13.653333 0-25.6-11.946667-25.6-25.6s11.946667-25.6 25.6-25.6h291.84c13.653333 0 25.6 11.946667 25.6 25.6s-11.946667 25.6-25.6 25.6zM616.106667 575.146667H208.213333c-13.653333 0-25.6-11.946667-25.6-25.6s11.946667-25.6 25.6-25.6h407.893334c13.653333 0 25.6 11.946667 25.6 25.6s-11.946667 25.6-25.6 25.6zM380.586667 440.32H208.213333c-13.653333 0-25.6-11.946667-25.6-25.6s11.946667-25.6 25.6-25.6h172.373334c13.653333 0 25.6 11.946667 25.6 25.6s-11.946667 25.6-25.6 25.6z" fill="#333333" p-id="5481"></path><path d="M914.773333 779.946667H600.746667c-13.653333 0-25.6-11.946667-25.6-25.6s11.946667-25.6 25.6-25.6h314.026666c13.653333 0 25.6 11.946667 25.6 25.6s-11.946667 25.6-25.6 25.6z" fill="#333333" p-id="5482"></path><path d="M831.146667 873.813333c-6.826667 0-13.653333-1.706667-18.773334-6.826666-10.24-10.24-10.24-25.6 0-35.84l75.093334-75.093334-75.093334-75.093333c-10.24-10.24-10.24-25.6 0-35.84 10.24-10.24 25.6-10.24 35.84 0l93.866667 93.866667c5.12 5.12 6.826667 11.946667 6.826667 18.773333s-3.413333 13.653333-6.826667 18.773333L848.213333 870.4c-5.12 1.706667-11.946667 3.413333-17.066666 3.413333z" fill="#333333" p-id="5483"></path><path d="M694.613333 824.32h-529.066666c-37.546667 0-68.266667-30.72-68.266667-68.266667V121.173333c0-37.546667 30.72-68.266667 68.266667-68.266666h529.066666c37.546667 0 68.266667 30.72 68.266667 68.266666v634.88c0 37.546667-30.72 68.266667-68.266667 68.266667z" fill="#333333" opacity=".3" p-id="5484"></path></svg>导出</a>

                    </div>
                </div>
            </div>

            <div style="display: flex; flex-wrap: wrap; margin-bottom: 20px;">
                <div class="timesel_lab">全部</div>
                <div class="timesel_lab active">今天</div>
                <div class="timesel_lab">昨天</div>
                <div class="timesel_lab">本周</div>
                <div class="timesel_lab">上周</div>
                <div class="timesel_lab">本月</div>
                <div class="timesel_lab">上月</div>
            </div>



            <div style="margin-bottom: 20px;">
                
                <input type="checkbox" id="filter_th" checked="checked" />&nbsp;过滤托号

                <div style="display: inline-block; font-weight: bold; background: #e5ded887; padding: 2px 18px; border-radius: 5px; color: #000; font-size: 20px;">
                    <span class="total_amount">0</span> 总金额<span style="font-size: 18px; background: linear-gradient(152deg, #ff5412d4, #f3cdbe); color: #fff8e0; border-radius: 6px; font-weight: 100; padding: 2px 9px; margin-left: 5px; font-weight: bold;"><span class="total_number">0</span> 人</span>
                </div>
            </div>


            <div class="table-responsive">
                <table class="table">
                    <thead>
                        <tr role="head">
                            <th sort="true">#ID</th>
                            <th sort="true">用户</th>
                            <th sort="true">所属任务</th>
                            <th sort="true">操作类型</th>
                            <th sort="true">交易额度</th>
                            <th sort="true">剩余额度</th>
                            <th sort="true">交易凭证</th>
                            <th sort="true">交易时间</th>
                        </tr>
                    </thead>
                    <tbody id="list-display">
                    </tbody>
                </table>
            </div>



            <div class="layui-table-page">
                <div id="layui-table-page1">
                    <div class="layui-box layui-laypage layui-laypage-default" id="layui-laypage-1">
                        <div style="display: inline-block;" class="cpages">
                            <!--page grow -->
                        </div>
                        <span class="layui-laypage-skip">到第<input type="text" min="1" value="1" class="layui-input target-page" />页
                            <button type="button" class="layui-laypage-btn" onclick="javascript:pager_index=$('.target-page').val()-1;getPager();">确定</button></span>
                        <span class="layui-laypage-count">共 <span class="page_count"></span>条</span>
                        <span class="layui-laypage-limits">
                            <select class="s_pages" lay-ignore="" onchange="javascript:pager_size=$(this).val();getPager();">
                                <option value="10">10 条/页</option>
                                <option value="20" selected="">20 条/页</option>
                                <option value="30">30 条/页</option>
                                <option value="50">50 条/页</option>
                                <option value="100">100 条/页</option>
                                <option value="200">200 条/页</option>
                            </select></span>
                    </div>
                </div>
            </div>

        </div>
    </div>
    <script>
        var _action = 'task_onetouch_balance';
    </script>
    <script>
        function openuser() {
            var cg = [];

            cg.push({ name: '黑名单', id: 'black_list', value: '', type: 'textarea' });

            edit({ aname: 'admin', title: '黑名单添加', action: _action, id: '', data: cg });
        }
    </script>
    <script>

        var pager_size = $(".s_pages").val();
        var pager_index = 0;
        var pager_key = "";

        var opt = get_param("opt");
        var opt2 = get_param("fid");

        $("#pagesize").val(pager_key);
        function setPageSize(obj) {
            pager_index = 0;
            pager_key = $("#search-keyword").val();
            opt = $(obj).val();
            getPager();
        }

        function searchList() {
            opt = $("#opt1").val();
            pager_index = 0;
            pager_key = $("#search-keyword").val();
            getPager();
        }

        function getParam() {
            return { limit: pager_size, p: pager_index, page: _action, keyword: pager_key, opt: opt, opt2: opt2, serv_id: get_param("serv_id"), start_time: $("#start_time").val(), end_time: $("#end_time").val(), state: $("#state").val(), uid: '<%=Request.QueryString["userid"] %>', type: $("#typeid").val(), trans: '<%=Request.QueryString["trans"] %>', filter_th: $('#filter_th').prop('checked') };
        }

        function getPager() {

            $.ajax({
                type: "POST",
                url: "../api/admin.aspx?do=page",
                data: getParam(),
                datatype: "json",
                success: function (json) {
                    $("#list-display").html("");
                    if (json.code == 1) {
                        var data = json.data.list;
                        for (var i = 0; i < data.length; i++) {
                            var tr = "";
                            tr += "<tr>";
                            tr += ("<td># " + data[i].id + "</td>");
                            tr += ("<td class='' " + ' style="cursor:pointer;" ondblclick="openrightBoxpopup(\'accounts_baseinfo.aspx?id=' + data[i].userid + '\')"' + ">" + (data[i].userid < 0 ? '<span style="font-size:12px;background: #bde2f5;color: #2a2b2c;padding: 2px 5px;border-radius: 3px;margin-right: 3px;">对接' + data[i].userid + '</span>' : ((('<span style="background: #f3ecec;color: #956b0a;font-size: 12px;border-radius: 3px;padding: 1px 4px;margin-right: 5px;font-weight: bold;border: 1px solid #ccc;">' + (data[i].typename == 'th' ? '托号' : data[i].typename == 'user' ? '普通' : data[i].typename == 'super' ? '高级' : data[i].typename == 'reg' ? '新' : '其他') + '</span>') + (data[i].group_name == '-' ? '' : "<span class='button-style style-js'>" + data[i].group_name) + "</span>")) + data[i].phone) + "</td>");
                            tr += ("<td>" + ('<b>' + data[i].orderNo + '</b>' + '<span style="color: #b53d10;margin-left: 2px;">[' + data[i].tid + ']</span>'
                                + ('<div style="' + (parseNumber(data[i].tk_balance) != parseNumber(data[i].tk_amount) ? 'color:#7000ff;' : 'color:gray;') + 'font-size:12px;margin-top:3px;font-weight: 100;">' + '<b style="color:#000;margin-right:3px;">' + data[i].tk_amount.toFixed(2) + '</b>' + '(额度' + parseNumber(data[i].tk_balance).toFixed(2) + ')' + "<span style='color:gray;margin-left:3px;'>" + data[i].tk_days + "天</span>" + '<span style="font-size:12px;color:gray;margin-left:2px;">(剩余' + (data[i].tk_days - data[i].tk_task_days) + '天)</span>' + '</div>')) + "</td>");
                            tr += ("<td>" + data[i].name + "</td>");
                            tr += ("<td>" + "<span style='font-weight: bold; font-size: 13px;" + (data[i].amount > 0 ? " text-shadow: 5px 5px 5px #4faf6e7d; color: #4faf6e;" : " text-shadow: 5px 5px 5px #cd272763; color: #ff4747;") + "'>" + (data[i].amount > 0 ? "+" : "") + data[i].amount.toFixed(2) + "</span>" + "</td>");
                            tr += ("<td style='font-weight: bold;'>" + data[i].total_amount.toFixed(2) + "</td>");
                            tr += ("<td style='color:gray;'>" + data[i].remark + "</td>");
                            tr += ("<td>" + data[i].create_time + "</td>");

                            tr += "</tr>";
                            $("#list-display").append(tr)
                        }
                        $(".page_size").html(pager_size);
                        $(".page_count").html(json.data.count);
                        $(".cpages").html(json.data.html);



                        $(".total_amount").html(parseFloat(json.total_amount).toFixed(2));
                        $(".total_number").html(json.total_number);


                    } else {

                    }
                },
                error: function () {
                }
            });
        }
        getPager();

        function _pt(i) {
            pager_index = i;
            getPager();
        }
    </script>

    <script>
        layui.use(['form', 'layedit', 'laydate'], function () {
        });


        function report_exl() {
            export_data(getParam());
        }

        //function switch_type() {

        //    var modify_model = [];

        //    modify_model.push({ name: '', id: 'action_type', value: 'changePassword_forusers', type: 'hidden' });
        //    modify_model.push({ name: '手机号列表', id: 'userlist', value: '', type: 'textarea' });
        //    modify_model.push({ name: '密码', id: 'pwd', value: '' });
        //    modify_model.push({ name: '验证码', id: 'actCode', value: '' });

        //    edit({ aname: 'admin', title: '交易类型调整', action: _action, id: '', data: modify_model });

        //}
    </script>
</asp:Content>


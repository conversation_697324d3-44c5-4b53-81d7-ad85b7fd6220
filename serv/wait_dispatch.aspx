<%@ Page Title="等待派单" Language="C#" MasterPageFile="~/serv/m.master" AutoEventWireup="true" CodeFile="wait_dispatch.aspx.cs" Inherits="w_index" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="Server">
    <link href="../css/element-ui.css" rel="stylesheet" />
    <style>
        .el-tag.el-tag--test {
            background-color: #d5d5f5;
            border-color: #7d7dc7;
            color: #6d3333;
        }
    </style>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="Server">
    <div class="ibox float-e-margins">
        <div class="ibox-title">
            <h5 style="color: #8d07d1; text-shadow: 5px 5px 15px #dbd52585;">
                <svg t="" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="15931" width="25" height="25" style="position: relative; top: 5px;">
                    <path d="M852.736 486.741333a266.581333 266.581333 0 1 1-335.36 365.994667H101.376A101.290667 101.290667 0 0 1 0 751.445333V101.290667C0 45.397333 45.397333 0 101.290667 0h650.154666a101.290667 101.290667 0 0 1 101.290667 101.290667v385.450666zM260.437333 216.746667c2.048 57.344 19.968 107.008 53.76 148.992l19.968-20.224a193.877333 193.877333 0 0 1-39.168-67.328 206.506667 206.506667 0 0 0 35.84-25.856l-14.592-18.688c-7.68 7.424-16.64 14.592-27.392 21.76a215.552 215.552 0 0 1-5.12-43.52c13.312-3.072 25.856-6.912 38.144-11.264l-13.312-21.76c-28.672 10.496-58.88 17.408-90.624 20.992v131.584c0 4.608-3.328 7.424-9.728 8.96l5.632 22.784c25.088-6.656 47.104-13.568 65.536-20.224l-4.608-23.04a362.24 362.24 0 0 1-31.744 11.52V219.477333c5.888-1.024 11.776-1.792 17.408-2.816z m36.352-86.272c-26.88 11.264-67.584 19.456-121.856 24.064v94.208c-1.024 38.4-9.984 71.68-26.88 99.328l19.456 20.224c20.48-33.024 31.232-72.96 32.256-119.552v-72.448c45.568-4.608 83.968-13.056 114.944-25.344l-17.92-20.48z m-175.616 2.816l-18.944 18.176c17.92 13.824 31.744 26.88 41.472 38.912l18.176-18.432a330.922667 330.922667 0 0 0-40.704-38.656z m-5.888 59.904l-18.688 18.432c16.896 13.824 29.952 26.88 39.168 38.912l18.432-18.176a310.869333 310.869333 0 0 0-38.912-39.168z m22.016 72.704a781.653333 781.653333 0 0 1-36.608 88.832l25.088 11.008c12.8-28.16 24.576-58.368 35.072-90.368l-23.552-9.472zM419.84 133.205333l-18.432 17.408c18.432 14.848 32.512 28.672 42.24 41.472l19.2-18.944a329.472 329.472 0 0 0-43.008-39.936z m145.664 230.656h58.624l6.4-26.368c-6.912 0.512-16.896 1.024-29.952 1.536-13.312 0-26.112 0.256-38.912 0.256-19.968 0-37.12-0.256-51.968-0.512-15.104-0.512-26.368-2.56-33.792-6.4a67.84 67.84 0 0 1-17.92-15.104l-2.816-2.816v-97.28h-58.624v24.576h33.792v70.144c-10.24 5.632-21.504 18.688-34.304 39.168l19.712 18.176c12.8-23.04 21.504-34.304 26.624-34.304 2.816 0 5.888 2.048 9.472 6.4 7.68 8.704 16.64 14.592 26.88 17.664 10.752 2.56 24.32 4.096 40.96 4.352 16.384 0.256 31.744 0.512 45.824 0.512zM474.112 229.205333v25.088h52.992c-8.704 21.504-27.136 38.912-55.296 52.48l16.896 21.76c36.608-20.224 58.88-45.056 66.816-74.24h67.328v-25.088h-62.72a324.266667 324.266667 0 0 0 2.048-30.208h51.968v-24.576h-27.392c6.144-10.752 11.776-23.04 16.896-36.352l-25.088-8.96a358.4 358.4 0 0 1-18.944 45.312h-36.864l13.312-6.656a340.736 340.736 0 0 0-20.992-36.352l-23.808 11.52c6.912 9.216 13.568 19.712 19.712 31.488h-28.16v24.576h52.992a264.106667 264.106667 0 0 1-2.304 30.208h-59.392z m94.208 36.096l-15.616 17.664c17.92 13.056 35.328 28.416 51.712 46.336l18.688-20.48a449.706667 449.706667 0 0 0-54.784-43.52zM185.173333 532.992H401.066667a25.344 25.344 0 0 0 25.258666-25.344v-2.645333A25.344 25.344 0 0 0 401.066667 479.573333H185.258667a25.344 25.344 0 0 0-25.344 25.344v2.56c0 14.08 11.349333 25.429333 25.344 25.429334z m0 159.829333h162.56a25.344 25.344 0 0 0 25.344-25.344v-2.56a25.344 25.344 0 0 0-25.344-25.429333H185.173333a25.344 25.344 0 0 0-25.344 25.344v2.645333c0 13.994667 11.349333 25.344 25.344 25.344zM759.466667 506.282667a226.474667 226.474667 0 1 0-4.864 453.034666A226.474667 226.474667 0 0 0 759.466667 506.282667z m-2.048 266.496a37.973333 37.973333 0 0 1-37.973334-37.973334V597.589333c0-20.992 17.066667-37.973333 37.973334-37.973333h4.010666c20.992 0 37.973333 17.066667 37.973334 37.973333v137.216c0 20.906667-17.066667 37.973333-37.973334 37.973334h-4.010666z m2.048 53.333333a39.936 39.936 0 1 1 0 79.872 39.936 39.936 0 0 1 0-79.872z" fill="#9571E9" p-id="15932"></path></svg>&nbsp;等待派单<svg t="1697522361289" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="5712" width="22" height="22" style="margin-left: 6px;"><path d="M245.76 286.72h552.96c124.928 0 225.28 100.352 225.28 225.28s-100.352 225.28-225.28 225.28H0V532.48c0-135.168 110.592-245.76 245.76-245.76z m133.12 348.16V401.408H348.16v178.176l-112.64-178.176H204.8V634.88h30.72v-178.176L348.16 634.88h30.72z m182.272-108.544v-24.576h-96.256v-75.776h110.592v-24.576h-141.312V634.88h143.36v-24.576h-112.64v-83.968h96.256z m100.352 28.672l-34.816-151.552h-34.816l55.296 233.472H675.84l47.104-161.792 4.096-20.48 4.096 20.48 47.104 161.792h28.672l57.344-233.472h-34.816l-32.768 151.552-4.096 30.72-6.144-30.72-40.96-151.552h-30.72l-40.96 151.552-6.144 30.72-6.144-30.72z" fill="#EE502F" p-id="5713"></path></svg></h5>
        </div>
        <div class="ibox-content">
            <div class="row layui-form">


                <div class="col-md-4">
                    <div class="form-group form-buttom" style="position: relative;">
                        <input type="text" id="search-keyword" name="ordername" class="form-control" placeholder="请输入搜索的信息" style="padding-right: 50px;">
                        <a class="input_button" onclick="searchList()">查询</a>
                    </div>

                </div>


                <div class="col-md-4">
                    <div class="form-group form-buttom" id="twoInputs">
                        <input type="text" id="start_time" name="ordername" class="form-control" placeholder="开始时间" style="width: 48%; display: inline-block;" value="" autocomplete="off">
                        <input type="text" id="end_time" name="ordername" class="form-control" placeholder="结束时间" style="width: 48%; display: inline-block;" autocomplete="off">

                        <script>
                            $(document).ready(function () {
                                $('#start_time').datepicker({
                                    format: 'yyyy-mm-dd',
                                    autoclose: true
                                });

                                $('#end_time').datepicker({
                                    format: 'yyyy-mm-dd',
                                    autoclose: true
                                });
                            });
                        </script>
                    </div>
                </div>

                <div class="col-md-4">
                    <div class="form-group form-buttom">
                        <a class="btn btn-success" onclick="javascript:location.href=location.href;">
                            <svg t="1688237949108" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2211" width="16" height="16">
                                <path d="M512 192a298.24 298.24 0 0 0-245.162667 128 42.666667 42.666667 0 1 1-69.973333-48.810667 384.042667 384.042667 0 0 1 697.173333 180.48l36.608-34.645333a42.666667 42.666667 0 0 1 58.709334 61.952l-112.64 106.666667a42.666667 42.666667 0 0 1-71.253334-38.912A298.666667 298.666667 0 0 0 512 192z m0.170667 640a298.24 298.24 0 0 0 245.162666-128 42.666667 42.666667 0 1 1 69.973334 48.810667 384.042667 384.042667 0 0 1-697.173334-180.48l-36.650666 34.645333a42.666667 42.666667 0 0 1-58.666667-61.952l112.64-106.666667a42.666667 42.666667 0 0 1 71.253333 38.912 298.666667 298.666667 0 0 0 293.418667 354.730667z" fill="#14101C" p-id="2212"></path></svg>
                            刷新</a>

                        <%--<a class="btn btn-success" onclick="report_exl()">
                            <svg t="1695489540076" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="5479" width="16" height="16">
                                <path d="M686.08 989.866667H165.546667c-51.2 0-92.16-40.96-92.16-92.16V126.293333c0-51.2 40.96-92.16 92.16-92.16h573.44c6.826667 0 11.946667 1.706667 17.066666 6.826667l187.733334 174.08c5.12 5.12 8.533333 11.946667 8.533333 18.773333v256c0 13.653333-11.946667 25.6-25.6 25.6s-25.6-11.946667-25.6-25.6V244.053333L728.746667 85.333333h-563.2C141.653333 85.333333 122.88 104.106667 122.88 126.293333v769.706667c0 22.186667 18.773333 40.96 40.96 40.96h520.533333c13.653333 0 25.6 11.946667 25.6 25.6S699.733333 989.866667 686.08 989.866667z" fill="#333333" p-id="5480"></path><path d="M919.893333 259.413333h-121.173333c-51.2 0-92.16-40.96-92.16-92.16V59.733333c0-13.653333 11.946667-25.6 25.6-25.6s25.6 11.946667 25.6 25.6v105.813334c0 22.186667 18.773333 40.96 40.96 40.96h121.173333c13.653333 0 25.6 11.946667 25.6 25.6s-11.946667 27.306667-25.6 27.306666zM500.053333 305.493333H208.213333c-13.653333 0-25.6-11.946667-25.6-25.6s11.946667-25.6 25.6-25.6h291.84c13.653333 0 25.6 11.946667 25.6 25.6s-11.946667 25.6-25.6 25.6zM616.106667 575.146667H208.213333c-13.653333 0-25.6-11.946667-25.6-25.6s11.946667-25.6 25.6-25.6h407.893334c13.653333 0 25.6 11.946667 25.6 25.6s-11.946667 25.6-25.6 25.6zM380.586667 440.32H208.213333c-13.653333 0-25.6-11.946667-25.6-25.6s11.946667-25.6 25.6-25.6h172.373334c13.653333 0 25.6 11.946667 25.6 25.6s-11.946667 25.6-25.6 25.6z" fill="#333333" p-id="5481"></path><path d="M914.773333 779.946667H600.746667c-13.653333 0-25.6-11.946667-25.6-25.6s11.946667-25.6 25.6-25.6h314.026666c13.653333 0 25.6 11.946667 25.6 25.6s-11.946667 25.6-25.6 25.6z" fill="#333333" p-id="5482"></path><path d="M831.146667 873.813333c-6.826667 0-13.653333-1.706667-18.773334-6.826666-10.24-10.24-10.24-25.6 0-35.84l75.093334-75.093334-75.093334-75.093333c-10.24-10.24-10.24-25.6 0-35.84 10.24-10.24 25.6-10.24 35.84 0l93.866667 93.866667c5.12 5.12 6.826667 11.946667 6.826667 18.773333s-3.413333 13.653333-6.826667 18.773333L848.213333 870.4c-5.12 1.706667-11.946667 3.413333-17.066666 3.413333z" fill="#333333" p-id="5483"></path><path d="M694.613333 824.32h-529.066666c-37.546667 0-68.266667-30.72-68.266667-68.266667V121.173333c0-37.546667 30.72-68.266667 68.266667-68.266666h529.066666c37.546667 0 68.266667 30.72 68.266667 68.266666v634.88c0 37.546667-30.72 68.266667-68.266667 68.266667z" fill="#333333" opacity=".3" p-id="5484"></path></svg>导出</a>--%>
                    </div>
                </div>



            </div>


            <%--<div style="display: flex; flex-wrap: wrap; margin-bottom: 20px;">
                <div class="timesel_lab">全部</div>
                <div class="timesel_lab">今天</div>
                <div class="timesel_lab">昨天</div>
                <div class="timesel_lab">本周</div>
                <div class="timesel_lab">上周</div>
                <div class="timesel_lab">本月</div>
                <div class="timesel_lab">上月</div>
            </div>--%>


            <div>
                <input type="checkbox" onclick="set_autoAlarm(this)" />&nbsp;自动监控订单（5s刷新一次）


                <%--<div style="display: inline-block; font-weight: bold; background: #e5ded887; padding: 2px 18px; border-radius: 5px; color: #000; font-size: 20px;">
                    <span class="total_amount">0</span> 总金额<span style="font-size: 18px; background: linear-gradient(152deg, #ff5412d4, #f3cdbe); color: #fff8e0; border-radius: 6px; font-weight: 100; padding: 2px 9px; margin-left: 5px; font-weight: bold;"><span class="total_number">0</span> 总量</span><span style="font-size: 18px; background: linear-gradient(152deg, #1e99a5d4, #d77750); color: #fff8e0; border-radius: 6px; font-weight: 100; padding: 2px 9px; margin-left: 5px; font-weight: bold;"><span class="total_users">0</span> 人</span>
                </div>--%>

            </div>


            <br />


            <div class="table-responsive">
                <table class="table">
                    <thead>
                        <tr role="head">
                            <th sort="true" class="action-cell-left">
                                <input type="checkbox" onclick="javascript: select_all(this)" />#ID</th>
                            <th sort="true" class="">历史佣金</th>
                            <th sort="true" class="">账号</th>
                            <th sort="true" class="">姓名</th>
                            <th sort="true" class="">成功率</th>
                            <th sort="true" class="">操作</th>
                        </tr>
                    </thead>
                    <tbody id="list-display">
                    </tbody>
                </table>
            </div>


            <div class="layui-table-page">
                <div id="layui-table-page1">
                    <div class="layui-box layui-laypage layui-laypage-default" id="layui-laypage-1">
                        <div style="display: inline-block;" class="cpages">
                            <!--page grow -->
                        </div>
                        <span class="layui-laypage-skip">到第<input type="text" min="1" value="1" class="layui-input target-page" />页
                            <button type="button" class="layui-laypage-btn" onclick="javascript:pager_index=$('.target-page').val()-1;getPager();">确定</button></span>
                        <span class="layui-laypage-count">共 <span class="page_count"></span>条</span>
                        <span class="layui-laypage-limits">
                            <select class="s_pages" lay-ignore="" onchange="javascript:pager_size=$(this).val();getPager();">
                                <option value="10">10 条/页</option>
                                <option value="20" selected="">20 条/页</option>
                                <option value="30">30 条/页</option>
                                <option value="50">50 条/页</option>
                                <option value="100">100 条/页</option>
                                <option value="200">200 条/页</option>
                            </select></span>
                    </div>
                </div>
            </div>

        </div>
    </div>


    <script>
        var _action = 'wait_dispatch';
        var _actionName = '';
    </script>
    <script id="item_classList" type="text/javascript">
        <%=ToJson(chelper.gdt("item_classList")) %>
    </script>
    <script>
        var item_classList = JSON.parse($('#item_classList').html());
        var classList = [];
        for (var i = 0; i < item_classList.length; i++) {
            classList.push([item_classList[i].id, item_classList[i].name]);
        }

        function opennew() {
            var modify_model = [];


            modify_model.push({ name: '选择分类', id: 'classId', data: classList, type: 'select' });
            modify_model.push({ name: '生成数量', id: 'number', value: '' });

            edit({ aname: 'admin', title: '添加' + _actionName, action: _action, id: '', data: modify_model });
        }
    </script>

    <noscript id="payer_ways"><%=uConfig.stcdata("payer_ways").Replace(" ", "") %></noscript>
    <script>
        var payer_ways_list = $('#payer_ways').html().split('\n');
        var payerWays = [];
        for (var i = 0; i < payer_ways_list.length; i++) {
            payerWays.push([payer_ways_list[i]]);
        }


        var toBuyListWays = [];
        toBuyListWays.push(['支付宝']);
        toBuyListWays.push(['网银']);
    </script>

    <script>

        var current_json = [];
        var pager_size = $(".s_pages").val();
        var pager_index = 0;
        var pager_key = "";

        $("#pagesize").val(pager_key);
        function setPageSize(obj) {
            pager_index = 0;
            pager_key = $("#search-keyword").val();
            opt = $(obj).val();
            getPager();
        }

        function searchList() {
            opt = $("#opt1").val();
            pager_index = 0;
            pager_key = $("#search-keyword").val();
            getPager();
        }
        function getParam() {
            return { limit: pager_size, p: pager_index, page: _action, keyword: pager_key, audit_state: $("#audit_state").val(), settle_state: $("#settle_state").val(), start_time: $("#start_time").val(), end_time: $("#end_time").val(), tarea: "<%=Request.QueryString["area"] %>", state: "<%=Request.QueryString["state"] %>", group: "<%=Request.QueryString["group"] %>", evaluate: "<%=Request.QueryString["evaluate"] %>", type: $("#typeid").val(), order_mode: "<%=Request.QueryString["order_mode"] %>" }
        }

        function getPager() {

            $.ajax({
                type: "POST",
                url: "../api/admin.aspx?do=page",
                data: getParam(),
                datatype: "json",
                success: function (json) {
                    $("#list-display").html("");
                    if (json.code == 1) {
                        var data = json.data.list;
                        current_json = data;
                        for (var i = 0; i < data.length; i++) {
                            var tr = "";
                            tr += "<tr style='" + (data[i].orderLock == 1 ? "background:#f5f5f5;" : "") + "'>";
                            tr += ('<td class="action-cell-left"><input class="good_select" type="checkbox" data-id="' + data[i].id + '" id="checkid_' + data[i].id + '" />#' + data[i].id + '</td>');
                            tr += ("<td class=''>" + parseFloat(data[i].reward_amount).toFixed(2) + "</td>");
                            tr += ("<td class=''>" + ('<span style="background: #f3ecec;color: #956b0a;font-size: 12px;border-radius: 3px;padding: 1px 4px;margin-right: 5px;font-weight: bold;border: 1px solid #ccc;">' + (data[i].typename == 'th' ? '托号' : data[i].typename == 'user' ? '普通' : data[i].typename == 'super' ? '高级' : data[i].typename == 'reg' ? '新' : '其他') + '</span>') + (data[i].group_name == '-' ? '' : "<span class='button-style style-js'>" + data[i].group_name + "</span>") + data[i].phone + "</td>");
                            tr += ("<td class=''>" + data[i].payment_name + "</td>");
                            tr += ("<td class='color:gray;'>" + (data[i].userid == "" ? "" : (data[i].order_total == 0 ? '当日未刷' : (((data[i].order_success / data[i].order_total) * 100).toFixed(1)) + '% ' + '<span>' + data[i].order_total + '单</span>')) + "</td>");

                            var modify_model = new Array();
                            modify_model.push({ name: '生成金额', id: 'amounts', value: '', type: 'textarea' });
                            //modify_model.push({ name: '未接单超时', id: 'timeout_foruser', value: '3600'});
                            //modify_model.push({ name: '确认超时', id: 'rTimeout_foruser', value: '600' });


                            tr += ("<td class=''>" + '<div style="font-size: 12px;display: inline-block;color: #fff;background: #1b92f3;padding: 2px 5px;margin: 0 3px;border-radius: 3px;font-weight: bold;cursor: pointer;" ' + "onclick='edit(" + edtext({ aname: 'admin', title: "生成任务", action: _action, id: data[i].id, data: modify_model }) + ")'" + '>开始生成任务</div>' + "</td>");

                            tr += "</tr>";
                            $("#list-display").append(tr)


                            var __temp = data[i];
                            __temp.modify_model = modify_model;
                            _bindEvent(__temp);
                        }
                        $(".page_size").html(pager_size);
                        $(".page_count").html(json.data.count);
                        $(".cpages").html(json.data.html);

                        $(".total_amount").html(parseFloat(json.total_amount).toFixed(2));
                        $(".total_number").html(json.total_number);
                        $(".total_users").html(json.total_users);


                        getdata();

                        pop_mp3tip();


                        show_expireList();

                    } else {

                    }
                },
                error: function () {
                }
            });
        }
        getPager();

        function _bindEvent(__temp) {
            $('button[edit-' + __temp.id + ']').on("click", function () {
                console.log('attrs', $(this).closest('tr').html(), __temp.modify_model);

                edit({ aname: 'admin', title: '修改' + _actionName, action: _action, id: __temp.id, data: __temp.modify_model });
            })
        }

        function _pt(i) {
            pager_index = i;
            getPager();
        }
    </script>

    <script>
        layui.use(['form', 'layedit', 'laydate'], function () {
        });
    </script>

    <script>
        function update_select(state) {
            event_create({ 'aname': 'admin', 'name': _action, 'event': 'delete', 'id': select_all_id(), 'state': state });
        }


        function report_exl() {
            export_data(getParam());
        }
    </script>

    <script>
        var click_ids = {};
        function checkIsSelect(obj) {
            if (obj.prop('checked')) {
                return true;
            } else {
                return false;
            }
        }
        function goods_click(t) {
            t = $(t);
            var obj = t.find(".good_select");
            obj.prop("checked", !checkIsSelect(obj));

        }

        function select_all(obj) {
            $(".good_select").prop("checked", checkIsSelect($(obj)));
        }

        function select_all_id() {
            var ids = "";
            $("#list-display tr").each(function () {
                var select_obj = $(this).find(".good_select");
                if (checkIsSelect(select_obj)) {
                    if (ids != "") {
                        ids += ",";
                    }
                    ids += select_obj.data("id");
                }
            });
            return ids;
        }
    </script>

    <audio id="myAudio" controls style="display: none;">
        <source src="../static/files/新订单.mp3" type="audio/mpeg">
        Your browser does not support the audio element.
    </audio>

    <script>

        var autoTimer = 0;
        var lastList = null;

        var set_autoAlarm = function (e) {
            if (checkIsSelect($(e))) {
                console.log('启动');
                lastList = null;

                autoTimer = setInterval(function () {


                    getPager();

                }, 5000);

            } else {
                console.log('关闭');
                clearInterval(autoTimer);
                autoTimer = 0;
            }
        }
        var show_expireList = function () {

            // 获取所有的.expire_time标签
            $('.expire_time').each(function () {
                var time = new Date($(this).data('time')).getTime();

                // 创建并附加倒计时元素
                var countdownElement = $('<span style="color: #d96060;text-shadow: 5px 5px 5px #e952525c;font-weight: bold; ">00:00</span>');
                $(this).closest("td").find(".exp_countdown").append(countdownElement);

                // 更新倒计时
                function updateCountdown() {
                    var currentDate = new Date().getTime();
                    var timeLeft = time - currentDate;

                    if (timeLeft <= 0) {
                        countdownElement.text('已退回');
                    } else {
                        var minutes = Math.floor(timeLeft / (1000 * 60));
                        var seconds = Math.floor((timeLeft % (1000 * 60)) / 1000);

                        countdownElement.text(minutes.toString().padStart(2, '0') + ':' + seconds.toString().padStart(2, '0'));
                    }
                }

                updateCountdown();

                // 每秒更新一次倒计时
                setInterval(updateCountdown, 1000);
            });
        }

        var pop_mp3tip = function (json) {
            if (autoTimer == 0) {
                return;
            }



            for (var i = 0; i < current_json.length; i++) {
                if (current_json[i].state == 1000 || current_json[i].state == 1001) {
                    play_mp3tip();
                    break;
                }
            }

        }

        var mp3_audio = document.getElementById("myAudio"); // 获取音频元素
        var play_mp3tip = function () {
            //console.log('播放订单提示');
            mp3_audio.play();
        }



        function isExpired(expirationTime) {
            // 获取当前时间戳（单位：毫秒）
            var currentTime = new Date().getTime();

            // 将传入的过期时间转换为时间戳
            var expirationTimestamp = new Date(expirationTime).getTime();

            // 判断是否已经过期
            return currentTime > expirationTimestamp;
        }


        var show_details = function (id, type) {
            var details_info = "";
            for (var i = 0; i < current_json.length; i++) {
                var obj = current_json[i];

                if (obj.id == id) {
                    details_info += ' <div style="display: flex; margin: 20px 0; padding-bottom: 20px; border-bottom: 1px solid #eee;">            <div>                <b>支付方式：</b>            </div>            <div style="margin-left: 10px;">                ' + obj.payment_type + '            </div>        </div>';
                    details_info += ' <div style="display: flex; margin: 20px 0; padding-bottom: 20px; border-bottom: 1px solid #eee;">            <div>                <b>收款姓名：</b>            </div>            <div style="margin-left: 10px;">                ' + obj.payment_name + '            </div>        </div>';

                    if (obj.payment_type == "银行卡") {
                        details_info += ' <div style="display: flex; margin: 20px 0; padding-bottom: 20px; border-bottom: 1px solid #eee;">            <div>                <b>收款卡号：</b>            </div>            <div style="margin-left: 10px;">                ' + obj.payment_bankid + '            </div>        </div>';
                        details_info += ' <div style="display: flex; margin: 20px 0; padding-bottom: 20px; border-bottom: 1px solid #eee;">            <div>                <b>开户行：</b>            </div>            <div style="margin-left: 10px;">                ' + obj.payment_bankname + '            </div>        </div>';
                    } else {
                        details_info += ' <div style="display: flex; margin: 20px 0; padding-bottom: 20px; border-bottom: 1px solid #eee;">            <div>                <b>' + obj.payment_type + '账号：</b>            </div>            <div style="margin-left: 10px;">                ' + obj.payment_bankid + '            </div>        </div>';
                    }
                    details_info += ' <div style="display: flex; margin: 20px 0; padding-bottom: 20px; border-bottom: 1px solid #eee;">            <div>                <b>交易金额：</b>            </div>            <div style="margin-left: 10px;">                ' + obj.orderAmt + '            </div>        </div>';

                    details_info += '<div style="display: flex; margin: 20px 0; padding-bottom: 20px; border-bottom: 1px solid #eee;">' + (obj.state == 1000 && obj.orderLock != 1 ? '&nbsp;<a style="display: flex;align-items: center;padding: 10px 39px;margin-left: 6px;line-height: 19px;color: #5a5a5c;border: 1px solid #5a5a5c;" onclick="' + "get_action({name:'lock_order',data:{id:" + obj.id + "},n:'锁定交易',e:'锁定 " + obj.orderId + " 的交易<br>【订单金额】：" + obj.amount + "'});" + '"><svg t="*************" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="7884" width="16" height="16"><path d="M500.224 694.528c-56.576 0-102.912-46.08-102.912-102.912a102.912 102.912 0 0 1 205.824 0c0 56.832-46.336 102.912-102.912 102.912z m0-146.944a44.032 44.032 0 1 0 0 88.064 44.032 44.032 0 0 0 0-88.064z" fill="#005CFF" p-id="7885"></path><path d="M733.44 318.72h-84.224c-1.28-77.056-64-139.264-141.312-139.264-77.312 0-140.032 62.208-141.312 139.264h-84.224c-64.512 0-116.992 52.48-116.992 116.992V747.52c0 64.512 52.48 116.992 116.992 116.992h450.816c64.512 0 116.992-52.48 116.992-116.992V435.712a116.736 116.736 0 0 0-116.736-116.992z m-225.536-80.384c44.8 0 81.408 35.84 82.432 80.384h-165.12c1.536-44.544 37.888-80.384 82.688-80.384zM791.808 747.52c0 32.256-26.112 58.368-58.368 58.368H282.624c-32.256 0-58.368-26.112-58.368-58.368V435.712c0-32.256 26.112-58.368 58.368-58.368h450.816c32.256 0 58.368 26.112 58.368 58.368V747.52z" fill="#1B1F28" p-id="7886"></path></svg>&nbsp;锁定</a>' : '') + '</div>';

                    _modal('收款信息', details_info);
                    break;
                }
            }
        }


        var create_order = function (id) {

            _modal("创建订单", "<div id='model-page' aid='" + id + "'>" + $("#buy_page").html() + "</div>");

            var details_info = "";
            for (var i = 0; i < current_json.length; i++) {
                var obj = current_json[i];
                if (obj.id == id) {
                    //$('#model-page .bank_item').hide();
                    //$('#model-page').attr();

                    $('#model-page .buyer_type').on('change', function () {
                        var name = $(this).val();
                        if (name == "银行卡") {
                            $('#model-page .bank_item').show();
                        } else {
                            $('#model-page .bank_item').hide();
                        }
                    });

                    $('#model-page .buyer_number').val((obj.total_amount - obj.deal_amount));

                    if (obj.accept_separate != 1) {
                        $('#model-page .buyer_number').css({ "background": "#f1f1f1" }).attr({ "disabled": "disabled" });
                    }

                    break;
                }
            }
        }

        var new_order = function () {
            var post_data = {
                id: $('#model-page').attr("aid"),
                buyer_type: $('#model-page .buyer_type').val(),
                buyer_amount: $('#model-page .buyer_number').val(),
                buyer_name: $('#model-page .buyer_name').val(),
                buyer_bankname: $('#model-page .buyer_bankname').val(),
                buyer_bankid: $('#model-page .buyer_bankid').val()
            }

            $.ajax({
                type: "POST",
                url: "../Api/admin.aspx?do=new_order",
                data: post_data,
                datatype: "json",
                success: function (data) {
                    if (data.code == 1) {
                        layer.msg(data.msg, { icon: 1, time: 1000 }, function () {
                            $('#myModal').modal('hide');
                            getPager();
                        });
                    } else {
                        layer.msg(data.msg, { icon: 2, time: 1500 });
                    }
                },
                error: function () {
                    layer.msg("抱歉，操作有误", { icon: 2, time: 1500 });
                }
            });

        }

        var get_star = function (s) {
            var text = '';
            for (var i = 0; i < s; i++) {
                text += '<svg t="*************" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="4009" width="12" height="12"><path d="M493.9 77c162.3-0.8 98.2 190.6 208.5 234.6 26.6 10.6 64.1 1 96.6 6.1 71.6 11.4 148.1 14.9 159.5 85.9 19 118.7-150.7 159.3-170.2 236.1-21.2 83.6 112.5 256.8-19.9 297.5-112.6 34.6-167.9-81.5-249.9-95.1-93.2-15.3-155.7 161.9-280.7 85.9-100.6-61.1 28.7-202.6 0-296-22.9-74.3-153.9-93-171.7-193.2-17.7-99.5 80.2-109.6 153.3-121.1 45.4-7.2 94.8 7.2 122.7-15.3 59.4-47.9 53-163.7 115-210.1 12.3-5.1 24.5-10.2 36.8-15.3z" p-id="4010" fill="#FFB408"></path></svg>';
            }
            return text;
        }
    </script>

    <style>
        .buy_list {
            display: flex;
            align-items: center;
            font-weight: bold;
            margin-top: 30px;
        }

            .buy_list .name {
                width: 100px;
                flex-shrink: 0;
            }

            .buy_list .item {
                width: 100%;
            }

                .buy_list .item input {
                    width: 100%;
                }
    </style>
    <div style="background: #fff; padding: 18px; display: none;" id="buy_page">

        <div class="buy_list">
            <div class="name">
                支付方式：
           
            </div>
            <div class="item">
                <select class="form-control buyer_type">
                    <option>银行卡</option>
                    <option>支付宝</option>
                    <option>微信</option>
                </select>
            </div>
        </div>

        <div class="buy_list">
            <div class="name" style="color: blue;">
                购买数量：           
            </div>
            <div class="item">
                <input class="form-control buyer_number" />
            </div>
        </div>

        <div class="buy_list">
            <div class="name">
                姓名：           
            </div>
            <div class="item">
                <input class="form-control buyer_name" />
            </div>
        </div>

        <div class="buy_list bank_item">
            <div class="name">
                开户行：           
            </div>
            <div class="item">
                <input class="form-control buyer_bankname" />
            </div>
        </div>

        <div class="buy_list">
            <div class="name">
                账号：           
            </div>
            <div class="item">
                <input class="form-control buyer_bankid" />
            </div>
        </div>

        <a class="btn btn-success" onclick="new_order()" style="margin-top: 24px; margin-left: 100px;">
            <svg t="*************" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="4127" width="16" height="16">
                <path d="M512 170.666667a341.333333 341.333333 0 0 0-290.133333 521.216 42.666667 42.666667 0 0 1-72.533334 45.013333A424.874667 424.874667 0 0 1 85.333333 512C85.333333 276.352 276.352 85.333333 512 85.333333s426.666667 191.018667 426.666667 426.666667-191.018667 426.666667-426.666667 426.666667a424.874667 424.874667 0 0 1-234.666667-70.314667 42.666667 42.666667 0 1 1 46.933334-71.210667A341.333333 341.333333 0 1 0 512 170.666667z m222.165333 264.832a42.666667 42.666667 0 0 0-60.330666-60.330667L469.333333 579.669333l-119.168-119.168a42.666667 42.666667 0 0 0-60.330666 60.330667l149.333333 149.333333a42.666667 42.666667 0 0 0 60.330667 0l234.666666-234.666666z" fill="#14101C" p-id="4128"></path></svg>&nbsp;创建订单</a>

    </div>



    <script>
        // 传入格式为 "yyyy-MM-dd HH:mm:ss" 的时间戳
        function countdownToTime(timestamp) {
            var targetDate = new Date(timestamp);
            var currentDate = new Date();
            var timeDiff = targetDate - currentDate;

            if (timeDiff <= 0) {
                return '已超时';
            }

            var hours = Math.floor(timeDiff / (1000 * 60 * 60));
            var minutes = Math.floor((timeDiff % (1000 * 60 * 60)) / (1000 * 60));
            var seconds = Math.floor((timeDiff % (1000 * 60)) / 1000);

            var formattedTime = hours + ':' + minutes.toString().padStart(2, '0') + ':' + seconds.toString().padStart(2, '0');
            return formattedTime;
        }
        var interval = 0


        function getdata() {
            clearInterval(interval);
            interval = setInterval(function () {
                $('td[expiretime]').each(function () {
                    var expiretime = $(this).attr('expiretime');
                    var result = countdownToTime(expiretime);
                    if (result == "已超时") {
                        result = "<span style='color:gray;'>已超时</span>";
                        $(this).removeAttr("expiretime");
                    }
                    $(this).html(result);
                })
            }, 1000); // 更新频率每秒钟
        }


        var pub_dating = function (id) {

        }

    </script>

</asp:Content>



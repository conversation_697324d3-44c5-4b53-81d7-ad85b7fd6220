using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Data;
using System.Data.SqlClient;

public partial class w_index : baseClass
{
    public DataTable dt = new System.Data.DataTable();
    protected void Page_Load(object sender, EventArgs e)
    {
        fuzhu fz = new fuzhu();
        List<SqlParameter> pams = fz.collectReqParames();
        if (!IsPostBack)
        {

            if (!fz.empty("id"))
            {
                dbClass db = new dbClass();
                string sql = string.Empty;

                sql = " select * from message_list with(nolock) where id=@id ";
                dt = db.getDataTable(sql, pams.ToArray());

                if (dt.Rows.Count == 0)
                {
                    fz.sendResponse("广告已被删除");
                }
            }
        }
    }
}
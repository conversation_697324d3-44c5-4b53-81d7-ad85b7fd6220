using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

public partial class master : baseClass
{
    public string formClass = string.Empty;
    public string bodyBackground = string.Empty;
    public DataTable dt;
    public DataTable apps;
    public DataTable _menus;

    public fuzhu fz = new fuzhu(HttpContext.Current);

    protected void Page_Load(object sender, EventArgs e)
    {
        _menus = new DataTable();
        _menus.Columns.Add("title");
        _menus.Columns.Add("url");
        _menus.Columns.Add("icon");

        //new_menu("广告管理", "advert_list", "fa-tripadvisor fa");
        //new_menu("买单列表", "transaction_order", "fa-list fa");
        //new_menu("房间管理", "room_list", "fa-list fa");
        //new_menu("轮播图管理", "banner_list", "fa-list fa");
        //new_menu("公告管理", "adlists", "fa-list fa");
        new_menu("管理列表", "serv_manager", "fa-users fa");
        //new_menu("用户列表", "account_list", "fa-user-md fa");
        new_menu("资金明细", "transaction_list", "fa-money fa");
        new_menu("佣金明细", "partnersbrok_records", "fa-money fa");
        new_menu("交易佣金明细", "brok_records", "fa-money fa");
        //new_menu("转账审核", "transfer_list", "fa-money fa");
        new_menu("操作日志", "serv_logs", "fa-code  fa");

        menus.DataSource = _menus;
        menus.DataBind();


        classItems.DataSource = SortDataTable(chelper.gdt("item_classList"), "sort_index ASC");
        classItems.DataBind();

        
        //storeItems.DataSource = SortDataTable(chelper.gdt("store_classList"), "sort_index ASC");
        //storeItems.DataBind();
        
        
    }
    public void new_menu(string _title, string _url, string _icon, string _qx = "超级管理员")
    {
        //if (fz.qx(_qx, false))
        //{
            DataRow newRow;
            newRow = _menus.NewRow();
            newRow["title"] = _title;
            newRow["url"] = _url;
            newRow["icon"] = _icon;
            _menus.Rows.Add(newRow);
        //}
    }

    protected void childRepeater_ItemDataBound(object sender, RepeaterItemEventArgs e)
    {
        //判断里层repeater处于外层repeater的哪个位置（ AlternatingItemTemplate，FooterTemplate，

        //HeaderTemplate，，ItemTemplate，SeparatorTemplate）
        if (e.Item.ItemType == ListItemType.Item || e.Item.ItemType == ListItemType.AlternatingItem)
        {
            Repeater rep = e.Item.FindControl("childRepeater") as Repeater;//找到里层的repeater对象
            DataRowView rowv = (DataRowView)e.Item.DataItem;//找到分类Repeater关联的数据项 
            string fid = rowv["id"] + ""; //获取填充子类的id 

            DataTable result = selectDateTable(apps, "fid=" + fid);
            rep.DataSource = result;
            rep.DataBind();
        }
    }
}
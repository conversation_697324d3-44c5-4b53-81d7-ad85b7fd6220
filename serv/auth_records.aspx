<%@ Page Title="授权记录" Language="C#" MasterPageFile="~/serv/m.master" AutoEventWireup="true" CodeFile="auth_records.aspx.cs" Inherits="w_index" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="Server">
    <link href="../css/element-ui.css" rel="stylesheet" />
    <style>
        .el-tag.el-tag--test {
            background-color: #d5d5f5;
            border-color: #7d7dc7;
            color: #6d3333;
        }
    </style>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="Server">
    <div class="ibox float-e-margins">
        <div class="ibox-title">
            <h5>授权记录</h5>
        </div>
        <div class="ibox-content">
            <div class="row layui-form">
                <div class="col-md-6">
                    <div class="form-group form-buttom">
                        <input type="text" id="search-keyword" name="ordername" class="form-control" placeholder="请输入搜索的信息">
                    </div>
                </div>

                <div class="col-md-6">
                    <div class="form-group form-buttom">
                        <a class="btn btn-success" onclick="searchList()">
                            <svg t="1688237921441" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="1949" width="16" height="16">
                                <path d="M361.130667 658.688a256 256 0 1 1 282.794666-44.672 42.496 42.496 0 0 0-10.368 14.933333c-0.853333 0.682667-1.621333 1.450667-2.389333 2.218667a42.666667 42.666667 0 0 0 0 60.330667l170.666667 170.666666a42.666667 42.666667 0 0 0 60.330666-60.330666l-142.762666-142.762667a341.333333 341.333333 0 1 0-169.344 99.242667 42.666667 42.666667 0 0 0-20.224-82.901334 254.762667 254.762667 0 0 1-168.704-16.725333z" fill="#14101C" p-id="1950"></path></svg>
                            查询</a>
                        <a class="btn btn-success" onclick="javascript:location.href=location.href;">
                            <svg t="1688237949108" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2211" width="16" height="16">
                                <path d="M512 192a298.24 298.24 0 0 0-245.162667 128 42.666667 42.666667 0 1 1-69.973333-48.810667 384.042667 384.042667 0 0 1 697.173333 180.48l36.608-34.645333a42.666667 42.666667 0 0 1 58.709334 61.952l-112.64 106.666667a42.666667 42.666667 0 0 1-71.253334-38.912A298.666667 298.666667 0 0 0 512 192z m0.170667 640a298.24 298.24 0 0 0 245.162666-128 42.666667 42.666667 0 1 1 69.973334 48.810667 384.042667 384.042667 0 0 1-697.173334-180.48l-36.650666 34.645333a42.666667 42.666667 0 0 1-58.666667-61.952l112.64-106.666667a42.666667 42.666667 0 0 1 71.253333 38.912 298.666667 298.666667 0 0 0 293.418667 354.730667z" fill="#14101C" p-id="2212"></path></svg>
                            刷新</a>
                    </div>
                </div>

            </div>

            <div class="table-responsive">
                <table class="table">
                    <thead>
                        <tr role="head">
                            <th>
                                <input type="checkbox" onclick="javascript: select_all(this)" /></th>
                            <th>用户</th>
                            <th>游戏名称</th>
                            <th>OpenId</th>
                            <th>冻结金额</th>
                            <th>状态</th>
                            <th>授权时间</th>
                            <th lsort="settle_amount">结算金额</th>
                            <th>结算时间</th>
                            <th>授权信息</th>
                            <th>token</th>
                            <th>code</th>


                        </tr>
                    </thead>
                    <tbody id="list-display">
                    </tbody>
                </table>
            </div>


            <div class="layui-table-page">
                <div id="layui-table-page1">
                    <div class="layui-box layui-laypage layui-laypage-default" id="layui-laypage-1">
                        <div style="display: inline-block;" class="cpages">
                            <!--page grow -->
                        </div>
                        <span class="layui-laypage-skip">到第<input type="text" min="1" value="1" class="layui-input target-page" />页
                            <button type="button" class="layui-laypage-btn" onclick="javascript:pager_index=$('.target-page').val()-1;getPager();">确定</button></span>
                        <span class="layui-laypage-count">共 <span class="page_count"></span>条</span>
                        <span class="layui-laypage-limits">
                            <select class="s_pages" lay-ignore="" onchange="javascript:pager_size=$(this).val();getPager();">
                                <option value="10">10 条/页</option>
                                <option value="20" selected="">20 条/页</option>
                                <option value="30">30 条/页</option>
                                <option value="50">50 条/页</option>
                                <option value="100">100 条/页</option>
                                <option value="200">200 条/页</option>
                            </select></span>
                    </div>
                </div>
            </div>

        </div>
    </div>
    <script>
        var _action = 'authorize_records';
        var _actionName = '授权记录';
    </script>
    <script id="item_classList" type="text/javascript">
    </script>
    <script>
        //var item_classList = JSON.parse($('#item_classList').html());
        var classList = [];
        //for (var i = 0; i < item_classList.length; i++) {
        //    classList.push([item_classList[i].id, item_classList[i].name]);
        //}


        function opennew() {
            var modify_model = [];

            modify_model.push({ name: '游戏名称', id: 'name', value: '' });
            modify_model.push({ name: '授权代码', id: 'code', value: '' });
            modify_model.push({ name: '游戏介绍', id: 'text_details', value: '' });
            modify_model.push({ name: '游戏图片', id: 'imgurl', data: [], type: 'img' });
            modify_model.push({ name: 'appid', id: 'appid', value: '' });
            modify_model.push({ name: 'appkey', id: 'appkey', value: '' });
            modify_model.push({ name: '测试用户', id: 'view_users', value: '', type: 'textarea' });
            modify_model.push({ name: '当前状态', id: 'state', type: 'option', value: "1", data: [['1', '已上线'], ['-1', '已下架']] });

            edit({ aname: 'admin', title: '添加' + _actionName, action: _action, id: '', data: modify_model });
        }
    </script>
    <script>

        var pager_size = 20;
        var pager_index = 0;
        var pager_key = "";


        $("#pagesize").val(pager_key);
        function setPageSize(obj) {
            pager_index = 0;
            pager_key = $("#search-keyword").val();
            opt = $(obj).val();
            getPager();
        }

        function searchList() {
            opt = $("#opt1").val();
            pager_index = 0;
            pager_key = $("#search-keyword").val();
            getPager();
        }

        function getParam() {

            var sortby = "";
            var ssss = "";
            $('[lsort]').each(function () {
                ssss = $(this).attr('sort_type');
                console.log('ssss', ssss);
                if (ssss == "down" || ssss == "up") {
                    var lsort = $(this).attr('lsort');
                    switch (ssss) {
                        case "down":
                            sortby = lsort;
                            break;
                        case "up":
                            sortby = lsort;
                            break;
                        default:
                            break;
                    }
                    return false;
                }
            })

            return { sortby: sortby, sorttype: ssss, limit: pager_size, p: pager_index, page: _action, keyword: pager_key, classid: '<%=Request.QueryString["classid"] + "" %>' }
        }

        function getPager() {

            $.ajax({
                type: "POST",
                url: "../api/admin.aspx?do=page",
                data: getParam(),
                datatype: "json",
                success: function (json) {
                    $("#list-display").html("");
                    if (json.code == 1) {
                        var data = json.data.list;
                        for (var i = 0; i < data.length; i++) {
                            var tr = "";
                            tr += "<tr class='test1'>";
                            tr += ('<td><input class="good_select" type="checkbox" data-id="' + data[i].id + '" id="checkid_' + data[i].id + '" /></td>');

                            tr += ("<td class=''>" + ('<span style="background: #f3ecec;color: #956b0a;font-size: 12px;border-radius: 3px;padding: 1px 4px;margin-right: 5px;font-weight: bold;border: 1px solid #ccc;">' + (data[i].typename == 'th' ? '托号' : data[i].typename == 'user' ? '普通' : data[i].typename == 'super' ? '高级' : data[i].typename == 'reg' ? '新' : '其他') + '</span>') + (data[i].group_name == '-' ? '' : "<span class='button-style style-js'>" + data[i].group_name + "</span>") + data[i].phone + "</td>");

                            tr += ("<td style='color: green!important;font-weight: bold;'>" + data[i].game + '<span style="color:gray;font-weight: 100;margin-left: 3px;">(代码：' + data[i].gameName + ')</span>' + "</span></td>");
                            tr += ("<td style='color: #000!important;font-weight: bold;'>" + data[i].openid + "</td>");
                            tr += ("<td style=''>" + data[i].freeze_amount + "</td>");
                            tr += ("<td style=''>" + (data[i].state == 1 ? "<span style='color:gray;'>已结束</span>" : '<span style="display: inline-block;color: #95eaff;background: #272222e3;font-size: 12px;padding: 2px 10px;border-radius: 5px;box-shadow: 2px 2px 8px #00000047;"><svg t="" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="7595" width="16" height="16" style="    margin-right: 3px;    position: relative;    top: 3px;"><path d="M679.408 689.376c-24.032-28.336-89.52-94.8-175.104-94.8-67.008 0-115.28 38.064-142.48 67.312a581.184 581.184 0 0 1-24.976 31.824l-0.752 1.216-1.76 1.744c-46.96 54.784-100.112 93.6-145.152 86.48-103.04-16.32-92.4-222.112-48.8-358.8s99.296-179.84 184.496-166.352c4.768 0.752 9.456 1.76 14.064 2.992 68.992 10.224 86.304 27.712 165.36 27.712 77.488 0 150.832-22.48 176.88-28.048 4.128-1.088 8.336-1.984 12.624-2.656 85.216-13.488 140.896 29.648 184.496 166.352 43.6 136.704 54.24 342.48-48.8 358.8-43.072 6.816-92.608-27.808-136.896-78.112-1.072-1.024-2.16-2.048-3.232-3.104 0 0-1.632-2.288-4.752-6.208a525.808 525.808 0 0 1-5.216-6.352zM297.52 513.312a78.64 78.64 0 1 0 0-157.28 78.64 78.64 0 0 0 0 157.28z" fill="#95EAFF" p-id="7596"></path><path d="M314.112 170.912h-68.816c-36.208 0-67.632 20.288-67.632 49.376v44.496h32v-44.496c0-7.744 14.928-17.376 35.632-17.376h68.816c20.72 0 35.632 9.648 35.632 17.376v44.496h32v-44.496c0-29.056-31.392-49.376-67.632-49.376zM674.272 220.24c0-7.76 14.928-17.408 35.632-17.408h68.816c20.72 0 35.632 9.648 35.632 17.408v44.528h32v-44.528c0-29.072-31.392-49.408-67.632-49.408h-68.816c-36.208 0-67.632 20.304-67.632 49.408v44.528h32v-44.528z" fill="#49A3FE" p-id="7597"></path><path d="M752 384m-32 0a32 32 0 1 0 64 0 32 32 0 1 0-64 0Z" fill="#49A3FE" p-id="7598"></path><path d="M688 448m-32 0a32 32 0 1 0 64 0 32 32 0 1 0-64 0Z" fill="#49A3FE" p-id="7599"></path><path d="M816 448m-32 0a32 32 0 1 0 64 0 32 32 0 1 0-64 0Z" fill="#49A3FE" p-id="7600"></path><path d="M752 512m-32 0a32 32 0 1 0 64 0 32 32 0 1 0-64 0Z" fill="#49A3FE" p-id="7601"></path><path d="M589.824 544h-52.768a16 16 0 0 0 0 32h52.768a16 16 0 0 0 0-32zM468.928 544H416a16 16 0 0 0 0 32h52.928a16 16 0 0 0 0-32z" fill="#49A3FE" p-id="7602"></path><path d="M937.2 423.568c-49.6-155.488-116.8-214.112-224.224-197.104-5.312 0.848-10.544 1.936-15.712 3.296-2.864 0.592-45.072 11.072-62.304 14.848-46.848 10.24-89.888 16.016-131.568 16.016-40.24 0-65.04-3.536-99.536-12.416-3.088-0.8-20.128-5.296-25.312-6.608a549.52 549.52 0 0 0-57.696-11.776 170.336 170.336 0 0 0-15.76-3.36c-107.408-17.008-174.624 41.6-224.224 197.104C17.952 620.832 31.92 827.344 148.24 845.76c53.808 8.528 115.744-30.608 177.088-102.16a18.24 18.24 0 0 0 3.584-4.208l-0.032-0.016a666.384 666.384 0 0 0 27.168-34.608c39.824-42.64 89.184-69.824 147.344-69.824 71.088 0 134.8 42.704 183.76 100.432 1.76 2.176 3.76 4.608 5.776 7.008 2.192 2.752 3.808 4.912 4.544 5.952l1.904 2.208 3.76 3.616c56.752 64.56 114.992 99.792 166.704 91.6 116.32-18.416 130.288-224.928 67.36-422.192z m-72.352 390.592c-37.856 6-87.488-24.016-138.688-82.192a356.768 356.768 0 0 0-3.568-3.488 194.144 194.144 0 0 0-4.88-6.336c-2.128-2.56-4.032-4.848-5.92-7.184-54.688-64.48-125.584-112.016-208.4-112.016-68.528 0-125.76 31.616-171.168 80.432a717.28 717.28 0 0 1-28.464 36.176l-1.168 1.584-0.624 0.608c-55.696 64.88-109.168 98.672-148.736 92.4-86.544-13.712-99.2-201.12-41.872-380.864 45.312-142.048 99.536-189.344 188.72-175.216 4.752 0.752 9.456 1.76 14.08 3.008 21.12 3.232 37.232 6.624 56.464 11.536 5.008 1.28 22 5.76 25.232 6.592 36.96 9.52 64.384 13.44 107.52 13.44 44.32 0 89.504-6.08 138.4-16.768 17.84-3.904 59.968-14.352 62.896-14.992 4.848-1.264 9.04-2.144 13.28-2.816 89.184-14.128 143.424 33.168 188.72 175.216 57.376 179.76 44.704 367.168-41.824 380.88z" fill="#0F13AE" p-id="7603"></path><path d="M863.616 728.88a44.16 44.16 0 0 1-15.648 5.536 50.08 50.08 0 0 1-20.976-1.488c-23.376-6.24-50.88-26.848-78.704-58.448-1.248-1.248-2-1.968-2.64-2.592a146.368 146.368 0 0 0-3.536-4.576l-4.432-5.376a16 16 0 1 0-24.848 20.144c1.504 1.856 3.008 3.68 4.512 5.472 1.52 1.92 2.688 3.456 3.2 4.176l1.904 2.208c0.944 0.928 1.904 1.824 2.848 2.72 30.592 34.864 62.816 58.976 93.456 67.168a82.128 82.128 0 0 0 34.24 2.176 75.904 75.904 0 0 0 26.896-9.568 16 16 0 1 0-16.272-27.552zM282.672 666.064c-0.432 0.576-0.32 0.448-0.448 0.576a351.184 351.184 0 0 1-33.504 34.368c-28.416 24.992-54.112 36.528-73.872 33.392a43.632 43.632 0 0 1-22.416-10.432 16 16 0 0 0-21.056 24.096 75.648 75.648 0 0 0 38.448 17.936c31.168 4.944 65.152-10.304 100.016-40.976a364.992 364.992 0 0 0 35.712-36.544 23.84 23.84 0 0 0 2.784-3.328 16 16 0 1 0-25.664-19.088z" fill="#0F13AE" p-id="7604"></path><path d="M689.28 641.232m-17.232 0a17.232 17.232 0 1 0 34.464 0 17.232 17.232 0 1 0-34.464 0Z" fill="#0F13AE" p-id="7605"></path><path d="M113.28 689.232m-17.232 0a17.232 17.232 0 1 0 34.464 0 17.232 17.232 0 1 0-34.464 0Z" fill="#0F13AE" p-id="7606"></path><path d="M294.4 419.2V400a16 16 0 0 0-32 0v19.2H240a16 16 0 0 0 0 32h22.4V480a16 16 0 0 0 32 0v-28.8H320a16 16 0 0 0 0-32h-25.6z" fill="#0F13AE" p-id="7607"></path><path d="M280 336a104 104 0 1 0 0 208 104 104 0 0 0 0-208z m0 176a72 72 0 1 1 0-144 72 72 0 0 1 0 144z" fill="#0F13AE" p-id="7608"></path></svg>进行中</span>') + "</td>");
                            tr += ("<td style=''>" + data[i].create_time + "</td>");
                            tr += ("<td style='color:gray;'>" + data[i].token_balance + (data[i].token_balance == "" ? '' : ('<span style="font-weight: bold;margin-left: 5px;text-shadow: 3px 3px 8px;' + (data[i].token_balance - data[i].freeze_amount > 0 ? 'color:#23af23;' : 'color:#dbb542;') + '">' + (data[i].token_balance - data[i].freeze_amount > 0 ? '+' : '') + parseFloat((data[i].token_balance - data[i].freeze_amount)).toFixed(2) + '</span>')) + "</td>");
                            tr += ("<td style=''>" + data[i].finish_time + "</td>");
                            tr += ("<td style='color: gray!important;'>" + data[i].userip + ' ' + data[i].log_text + "</td>");
                            tr += ("<td style=''>" + data[i].token + "</td>");
                            tr += ("<td style=''>" + data[i].code + "</td>");

                            var modify_model = new Array();



                            //tr += ("<td class='action-cell '>"
                            //    + ' <div class="card-toolbar">'
                            //    + '<a class="card-button common-button" ' + "onclick='edit(" + edtext({ aname: 'admin', title: _actionName + "修改", action: _action, id: data[i].id, data: modify_model }) + ")'" + '><svg  class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg"  width="16" height="16" style="position:relative;top:3px;"><path d="M358.165868 554.624796c-0.533143 0.680499-1.066285 1.391696-1.303692 2.251274l-41.104163 150.700257c-2.400676 8.772804 0.059352 18.226107 6.550183 24.892947 4.860704 4.742001 11.261485 7.350408 18.077727 7.350408 2.252297 0 4.504594-0.267083 6.727215-0.860601l149.630902-40.808428c0.23843 0 0.357134 0.207731 0.534166 0.207731 1.718131 0 3.408633-0.62217 4.683672-1.927909l400.113747-400.054395c11.883655-11.897981 18.404162-28.109198 18.404162-45.74281 0-19.989263-8.476045-39.963177-23.324218-54.767348l-37.786605-37.844933c-14.81645-14.848173-34.822087-23.338544-54.797024-23.338544-17.631566 0-33.842783 6.520507-45.75816 18.388812L358.758362 553.232077C358.344946 553.615816 358.462626 554.179658 358.165868 554.624796M862.924953 257.19778l-39.742143 39.71349-64.428382-65.451688 39.180348-39.179324c6.193049-6.222725 18.194384-5.318122 25.308409 1.822508l37.813211 37.845956c3.943822 3.941775 6.195096 9.18622 6.195096 14.372336C867.223862 250.574942 865.710392 254.42769 862.924953 257.19778M429.322487 560.907896l288.712541-288.728914 64.459081 65.49569L494.314711 625.838721 429.322487 560.907896zM376.718409 677.970032l20.863167-76.580143 55.656601 55.657624L376.718409 677.970032z" fill="#e6e6e6" p-id="1994"></path><path d="M888.265084 415.735539c-15.144932 0-27.562752 12.313443-27.620058 27.665083l0 372.98283c0 19.559475-15.885805 35.444257-35.475979 35.444257L194.220958 851.827709c-19.559475 0-35.504632-15.883759-35.504632-35.444257L158.716326 207.602222c0-19.575848 15.945157-35.474956 35.504632-35.474956l406.367171 0c15.231913 0 27.592428-12.371772 27.592428-27.606755 0-15.202237-12.360516-27.590382-27.592428-27.590382L190.013123 116.930129c-47.684022 0-86.49291 38.779212-86.49291 86.49291L103.520213 820.59231c0 47.713698 38.808888 86.47756 86.49291 86.47756l639.334083 0c47.715745 0 86.509283-38.763862 86.509283-86.47756L915.856489 443.222567C915.794068 428.048983 903.408993 415.735539 888.265084 415.735539" fill="#e6e6e6" p-id="1995"></path></svg>&nbsp;编辑</a>'
                            //    + '&nbsp;<a class="card-button common-button" ' + "onclick=\"" + "get_action({name:'event_create',data:{id:" + data[i].id + ",aname:'admin',name:'" + _action + "',event:'delete'},n:'删除用户',e:'删除用户 " + data[i].phone + "<br>【账户余额】：" + data[i].amount + "（确认将完成订单）'});" + "\"" + '><svg t="1688237896673" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="1699" width="16" height="16" style="position:relative;top:3px;"><path d="M612.693333 170.666667l8.533334 42.666666H402.773333l8.533334-42.666666h201.386666z m-293.632 25.941333a86.186667 86.186667 0 0 0-1.706666 16.725333H170.666667a42.666667 42.666667 0 0 0 0 85.333334h682.666666a42.666667 42.666667 0 1 0 0-85.333334h-146.730666c0-5.504-0.554667-11.093333-1.706667-16.725333l-8.533333-42.666667A85.333333 85.333333 0 0 0 612.778667 85.333333H411.306667a85.333333 85.333333 0 0 0-83.712 68.608l-8.533334 42.666667zM426.666667 469.333333a42.666667 42.666667 0 0 0-42.666667 42.666667v170.666667a42.666667 42.666667 0 1 0 85.333333 0v-170.666667a42.666667 42.666667 0 0 0-42.666666-42.666667z m128 42.666667a42.666667 42.666667 0 1 1 85.333333 0v170.666667a42.666667 42.666667 0 1 1-85.333333 0v-170.666667zM255.402667 384A42.666667 42.666667 0 0 1 298.666667 426.069333L301.738667 640H301.653333a42.666667 42.666667 0 1 1-85.290666-2.176l-2.986667-210.56A42.666667 42.666667 0 0 1 255.36 384z m51.925333 396.586667h0.042667A103.381333 103.381333 0 0 0 406.698667 853.333333h210.602666c54.186667 0 98.56-40.789333 103.210667-92.757333L725.333333 426.069333a42.666667 42.666667 0 0 1 85.333334 1.194667l-4.864 337.365333-0.128 1.365334C798.08 863.872 715.562667 938.666667 617.301333 938.666667H406.698667a188.885333 188.885333 0 0 1-180.096-130.389334 42.666667 42.666667 0 0 1 80.725333-27.648z" fill="#14101C" p-id="1700"></path></svg>&nbsp;删除</a>'
                            //    + '</div></td>');


                            tr += "</tr>";
                            $("#list-display").append(tr)
                        }
                        $(".page_size").html(pager_size);
                        $(".page_count").html(json.data.count);
                        $(".cpages").html(json.data.html);
                    } else {

                    }
                },
                error: function () {
                }
            });
        }
        getPager();

        function _pt(i) {
            pager_index = i;
            getPager();
        }

        $('[lsort]').on('click', function () {
            $(this).css({
                '-webkit-user-select': 'none', /* Chrome, Safari, Opera */
                '-moz-user-select': 'none',    /* Firefox */
                'user-select': 'none'          /* General */
            });

            $(this).siblings().removeAttr("sort_type");
            console.log('click', $(this).text());
            var type = $(this).attr('sort_type');
            $('[lsort]').find('svg').remove();
            var svgHtml = "";
            switch (type) {
                case "down":
                    type = "";
                    break;
                case "up":
                    type = "down";
                    svgHtml = '<svg t="" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="1964" width="12" height="12"><path d="M768.002 535.89H600.749l-0.909-322.563h-166.4v322.56l-167.253-0.455L517.181 858.39l250.821-322.5z" fill="#FD3333" p-id="1965"></path></svg>';
                    break;
                default:
                    type = "up";
                    svgHtml = '<svg t="" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2232" width="12" height="12"><path d="M255.998 488.11h167.253l0.909 322.563h166.4v-322.56l167.253 0.455L506.819 165.61l-250.821 322.5z" fill="#1296DB" p-id="2233"></path></svg>';
                    break;
            }
            $(this).attr('sort_type', type);
            $(this).append(svgHtml);

            getPager();

        })
    </script>

    <script>
        layui.use(['form', 'layedit', 'laydate'], function () {
        });
    </script>

    <script>
        function update_select(state) {
            event_create({ 'aname': 'admin', 'name': _action, 'event': 'update', 'id': select_all_id(), 'state': state });
        }

        function _event(evt) {
            event_create({ 'aname': 'admin', 'name': _action, 'event': evt, 'id': select_all_id() });
        }

        //function report_exl() {
        //    var start_time = "";
        //    var end_time = "";
        //    var opt1 = req("id");
        //    var opt2 = $("#opt1").val();
        //    var checktime = $("#checktime").val();
        //    var g = checktime.split(' - ');
        //    console.log(g);
        //    if (g.length > 1) {
        //        start_time = g[0];
        //        end_time = g[1];
        //    }

        //    export_data({ name: "cdks", key: pager_key, start_time: start_time, end_time: end_time, opt1: opt1, opt2: opt2 });
        //}
    </script>

    <script>
        var click_ids = {};
        function checkIsSelect(obj) {
            if (obj.prop('checked')) {
                return true;
            } else {
                return false;
            }
        }
        function goods_click(t) {
            t = $(t);
            var obj = t.find(".good_select");
            obj.prop("checked", !checkIsSelect(obj));

        }

        function select_all(obj) {
            $(".good_select").prop("checked", checkIsSelect($(obj)));
        }

        function select_all_id() {
            var ids = "";
            $("#list-display tr").each(function () {
                var select_obj = $(this).find(".good_select");
                if (checkIsSelect(select_obj)) {
                    if (ids != "") {
                        ids += ",";
                    }
                    ids += select_obj.data("id");
                }
            });
            return ids;
        }
    </script>

</asp:Content>


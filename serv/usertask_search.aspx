<%@ Page Title="用户任务查询" Language="C#" MasterPageFile="~/serv/m.master" AutoEventWireup="true" CodeFile="usertask_search.aspx.cs" Inherits="w_index" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="Server">
    <link href="../css/element-ui.css" rel="stylesheet" />
    <style>
        .el-tag.el-tag--test {
            background-color: #d5d5f5;
            border-color: #7d7dc7;
            color: #6d3333;
        }
    </style>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="Server">
    <div class="ibox float-e-margins">
        <div class="ibox-title">
            <h5 style="color: #8d07d1; text-shadow: 5px 5px 15px #dbd52585;">用户任务查询</h5>
        </div>

        <div style="display: flex;">

            <div style="padding: 18px;">
                <div style="margin-bottom:10px;">

                    <strong style="color: #2a2b2c; font-size: 16px;">请输入查询的用户列表</strong>
                </div>

                <textarea style="border: 1px solid #eee; padding: 10px; height: calc(100vh - 150px); max-width: 200px;" id="phone_list"></textarea>
            </div>

            <div class="ibox-content" style="width: 100%;">
                <div class="row layui-form">
                    <div class="col-md-4">
                        <div class="form-group form-buttom" style="position: relative;">
                            <input type="text" id="search-keyword" name="ordername" class="form-control" placeholder="请输入搜索的信息" style="padding-right: 50px;">
                            <a class="input_button" onclick="searchList()">查询</a>
                        </div>

                    </div>


                    <div class="col-md-4">
                        <div class="form-group form-buttom" id="twoInputs">
                            <input type="text" id="start_time" name="ordername" class="form-control" placeholder="开始时间" style="width: 48%; display: inline-block;" value="" autocomplete="off">
                            <input type="text" id="end_time" name="ordername" class="form-control" placeholder="结束时间" style="width: 48%; display: inline-block;" autocomplete="off">

                            <script>
                                $(document).ready(function () {
                                    $('#start_time').datepicker({
                                        format: 'yyyy-mm-dd',
                                        autoclose: true
                                    });

                                    $('#end_time').datepicker({
                                        format: 'yyyy-mm-dd',
                                        autoclose: true
                                    });
                                });
                            </script>
                        </div>
                    </div>

                    <div class="col-md-4">
                        <div class="form-group form-buttom">
                            <a class="btn btn-success" onclick="javascript:location.href=location.href;">
                                <svg t="1688237949108" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2211" width="16" height="16">
                                    <path d="M512 192a298.24 298.24 0 0 0-245.162667 128 42.666667 42.666667 0 1 1-69.973333-48.810667 384.042667 384.042667 0 0 1 697.173333 180.48l36.608-34.645333a42.666667 42.666667 0 0 1 58.709334 61.952l-112.64 106.666667a42.666667 42.666667 0 0 1-71.253334-38.912A298.666667 298.666667 0 0 0 512 192z m0.170667 640a298.24 298.24 0 0 0 245.162666-128 42.666667 42.666667 0 1 1 69.973334 48.810667 384.042667 384.042667 0 0 1-697.173334-180.48l-36.650666 34.645333a42.666667 42.666667 0 0 1-58.666667-61.952l112.64-106.666667a42.666667 42.666667 0 0 1 71.253333 38.912 298.666667 298.666667 0 0 0 293.418667 354.730667z" fill="#14101C" p-id="2212"></path></svg>
                                刷新</a>
                            <%--<a class="btn btn-success" onclick="opennew()">
                                <svg t="1688237854283" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="15169" width="16" height="16">
                                    <path d="M725.333333 213.333333H298.666667a85.333333 85.333333 0 0 0-85.333334 85.333334v341.333333a42.666667 42.666667 0 1 1-85.333333 0V298.666667a170.666667 170.666667 0 0 1 170.666667-170.666667h426.666666a170.709333 170.709333 0 0 1 167.850667 139.733333v488.533334A170.709333 170.709333 0 0 1 725.333333 896H298.666667a170.538667 170.538667 0 0 1-144.469334-79.744l1.194667-0.682667a42.666667 42.666667 0 0 1 76.501333-37.034666c15.658667 19.584 39.765333 32.128 66.773334 32.128h426.666666a85.333333 85.333333 0 0 0 85.333334-85.333334V298.666667a85.333333 85.333333 0 0 0-85.333334-85.333334z m-213.333333 512a42.666667 42.666667 0 0 1-42.666667-42.666666v-128H341.333333a42.666667 42.666667 0 1 1 0-85.333334h128V341.333333a42.666667 42.666667 0 1 1 85.333334 0v128h128a42.666667 42.666667 0 1 1 0 85.333334h-128v128a42.666667 42.666667 0 0 1-42.666667 42.666666z" fill="#14101C" p-id="15170"></path></svg>&nbsp;添加</a>--%>

                            <%--<a class="btn btn-success" onclick="report_exl()">
                                <svg t="1695489540076" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="5479" width="16" height="16">
                                    <path d="M686.08 989.866667H165.546667c-51.2 0-92.16-40.96-92.16-92.16V126.293333c0-51.2 40.96-92.16 92.16-92.16h573.44c6.826667 0 11.946667 1.706667 17.066666 6.826667l187.733334 174.08c5.12 5.12 8.533333 11.946667 8.533333 18.773333v256c0 13.653333-11.946667 25.6-25.6 25.6s-25.6-11.946667-25.6-25.6V244.053333L728.746667 85.333333h-563.2C141.653333 85.333333 122.88 104.106667 122.88 126.293333v769.706667c0 22.186667 18.773333 40.96 40.96 40.96h520.533333c13.653333 0 25.6 11.946667 25.6 25.6S699.733333 989.866667 686.08 989.866667z" fill="#333333" p-id="5480"></path><path d="M919.893333 259.413333h-121.173333c-51.2 0-92.16-40.96-92.16-92.16V59.733333c0-13.653333 11.946667-25.6 25.6-25.6s25.6 11.946667 25.6 25.6v105.813334c0 22.186667 18.773333 40.96 40.96 40.96h121.173333c13.653333 0 25.6 11.946667 25.6 25.6s-11.946667 27.306667-25.6 27.306666zM500.053333 305.493333H208.213333c-13.653333 0-25.6-11.946667-25.6-25.6s11.946667-25.6 25.6-25.6h291.84c13.653333 0 25.6 11.946667 25.6 25.6s-11.946667 25.6-25.6 25.6zM616.106667 575.146667H208.213333c-13.653333 0-25.6-11.946667-25.6-25.6s11.946667-25.6 25.6-25.6h407.893334c13.653333 0 25.6 11.946667 25.6 25.6s-11.946667 25.6-25.6 25.6zM380.586667 440.32H208.213333c-13.653333 0-25.6-11.946667-25.6-25.6s11.946667-25.6 25.6-25.6h172.373334c13.653333 0 25.6 11.946667 25.6 25.6s-11.946667 25.6-25.6 25.6z" fill="#333333" p-id="5481"></path><path d="M914.773333 779.946667H600.746667c-13.653333 0-25.6-11.946667-25.6-25.6s11.946667-25.6 25.6-25.6h314.026666c13.653333 0 25.6 11.946667 25.6 25.6s-11.946667 25.6-25.6 25.6z" fill="#333333" p-id="5482"></path><path d="M831.146667 873.813333c-6.826667 0-13.653333-1.706667-18.773334-6.826666-10.24-10.24-10.24-25.6 0-35.84l75.093334-75.093334-75.093334-75.093333c-10.24-10.24-10.24-25.6 0-35.84 10.24-10.24 25.6-10.24 35.84 0l93.866667 93.866667c5.12 5.12 6.826667 11.946667 6.826667 18.773333s-3.413333 13.653333-6.826667 18.773333L848.213333 870.4c-5.12 1.706667-11.946667 3.413333-17.066666 3.413333z" fill="#333333" p-id="5483"></path><path d="M694.613333 824.32h-529.066666c-37.546667 0-68.266667-30.72-68.266667-68.266667V121.173333c0-37.546667 30.72-68.266667 68.266667-68.266666h529.066666c37.546667 0 68.266667 30.72 68.266667 68.266666v634.88c0 37.546667-30.72 68.266667-68.266667 68.266667z" fill="#333333" opacity=".3" p-id="5484"></path></svg>导出</a>--%>

                        </div>
                    </div>



                </div>


                <div style="display: flex; flex-wrap: wrap; margin-bottom: 20px;">
                    <div class="timesel_lab">全部</div>
                    <div class="timesel_lab">今天</div>
                    <div class="timesel_lab">昨天</div>
                    <div class="timesel_lab">本周</div>
                    <div class="timesel_lab">上周</div>
                    <div class="timesel_lab">本月</div>
                    <div class="timesel_lab">上月</div>
                </div>


                <%--<div style="margin-bottom: 10px;">
                    <input type="checkbox" id="filter_th" checked="checked" />&nbsp;过滤托号
                </div>--%>



                <div style="display: none;">
                    <input type="checkbox" onclick="set_autoAlarm(this)" />&nbsp;自动监控订单（5s刷新一次）


                <div style="display: inline-block; font-weight: bold; background: #e5ded887; padding: 2px 18px; border-radius: 5px; color: #000; font-size: 20px;">
                    <span class="total_amount">0</span> 总金额<span style="font-size: 18px; background: linear-gradient(152deg, #ff5412d4, #f3cdbe); color: #fff8e0; border-radius: 6px; font-weight: 100; padding: 2px 9px; margin-left: 5px; font-weight: bold;"><span class="total_number">0</span> 总量</span>
                </div>

                </div>


                <br />


                <div class="table-responsive">
                    <table class="table">
                        <thead>
                            <tr role="head">
                                <th sort="true" class="">用户名</th>
                                <th sort="true">姓名</th>
                                <th sort="true" lsort="order_amount">提现金额</th>
                            </tr>
                        </thead>
                        <tbody id="list-display">
                        </tbody>
                    </table>
                </div>


                <div class="layui-table-page">
                    <div id="layui-table-page1">
                        <div class="layui-box layui-laypage layui-laypage-default" id="layui-laypage-1">
                            <div style="display: inline-block;" class="cpages">
                                <!--page grow -->
                            </div>
                            <span class="layui-laypage-skip">到第<input type="text" min="1" value="1" class="layui-input target-page" />页
                            <button type="button" class="layui-laypage-btn" onclick="javascript:pager_index=$('.target-page').val()-1;getPager();">确定</button></span>
                            <span class="layui-laypage-count">共 <span class="page_count"></span>条</span>
                            <span class="layui-laypage-limits">
                                <select class="s_pages" lay-ignore="" onchange="javascript:pager_size=$(this).val();getPager();">
                                    <option value="10">10 条/页</option>
                                    <option value="20" selected="">20 条/页</option>
                                    <option value="30">30 条/页</option>
                                    <option value="50">50 条/页</option>
                                    <option value="100">100 条/页</option>
                                    <option value="200">200 条/页</option>
                                </select></span>
                        </div>
                    </div>
                </div>

            </div>
        </div>
    </div>


    <script>
        var _action = 'usertask_search';
        var _actionName = '';
    </script>
    <script id="item_classList" type="text/javascript">
        <%=ToJson(chelper.gdt("item_classList")) %>
    </script>
    <script>
        var item_classList = JSON.parse($('#item_classList').html());
        var classList = [];
        for (var i = 0; i < item_classList.length; i++) {
            classList.push([item_classList[i].id, item_classList[i].name]);
        }

        function opennew() {
            var modify_model = [];


            modify_model.push({ name: '选择分类', id: 'classId', data: classList, type: 'select' });
            modify_model.push({ name: '生成数量', id: 'number', value: '' });

            edit({ aname: 'admin', title: '添加' + _actionName, action: _action, id: '', data: modify_model });
        }
    </script>

    <noscript id="payer_ways"><%=uConfig.stcdata("payer_ways").Replace(" ", "") %></noscript>
    <script>
        var payer_ways_list = $('#payer_ways').html().split('\n');
        var payerWays = [];
        for (var i = 0; i < payer_ways_list.length; i++) {
            payerWays.push([payer_ways_list[i]]);
        }
    </script>

    <script>

        var current_json = [];
        var pager_size = $(".s_pages").val();
        var pager_index = 0;
        var pager_key = "";

        $("#pagesize").val(pager_key);
        function setPageSize(obj) {
            pager_index = 0;
            pager_key = $("#search-keyword").val();
            opt = $(obj).val();
            getPager();
        }

        function searchList() {
            opt = $("#opt1").val();
            pager_index = 0;
            pager_key = $("#search-keyword").val();
            getPager();
        }
        function getParam() {

            var sortby = "";
            var ssss = "";
            $('[lsort]').each(function () {
                ssss = $(this).attr('sort_type');
                console.log('ssss', ssss);
                if (ssss == "down" || ssss == "up") {
                    var lsort = $(this).attr('lsort');
                    switch (ssss) {
                        case "down":
                            sortby = lsort;
                            break;
                        case "up":
                            sortby = lsort;
                            break;
                        default:
                            break;
                    }
                    return false;
                }
            })

            return { sortby: sortby, sorttype: ssss, limit: pager_size, p: pager_index, page: _action, keyword: pager_key, start_time: $("#start_time").val(), end_time: $("#end_time").val(), state: "<%=Request.QueryString["state"] %>", group: "<%=Request.QueryString["group"] %>", evaluate: "<%=Request.QueryString["evaluate"] %>", sort: { a: 1, b: 2 }, filter_th: $('#filter_th').prop('checked'), phone_list: $('#phone_list').val()}
        }

        function getPager() {

            $.ajax({
                type: "POST",
                url: "../api/admin.aspx?do=page",
                data: getParam(),
                datatype: "json",
                success: function (json) {
                    $("#list-display").html("");
                    if (json.code == 1) {
                        var data = json.data.list;
                        current_json = data;
                        for (var i = 0; i < data.length; i++) {
                            var tr = "";
                            tr += "<tr>";
                            tr += ("<td class=''>" + ('<span style="background: #f3ecec;color: #956b0a;font-size: 12px;border-radius: 3px;padding: 1px 4px;margin-right: 5px;font-weight: bold;border: 1px solid #ccc;">' + (data[i].typename == 'th' ? '托号' : data[i].typename == 'user' ? '普通' : data[i].typename == 'super' ? '高级' : data[i].typename == 'reg' ? '新' : '其他') + '</span>') + (data[i].group_name == '-' ? '' : "<span class='button-style style-js'>" + data[i].group_name + "</span>") + data[i].phone + "</td>");
                            tr += ("<td style=''>" + data[i].payment_name + "</td>");
                            tr += ("<td style=''>" + data[i].order_amount + "</td>");

                            var modify_model = new Array();
                            //tr += ("<td class='action-cell '>"
                            //    + ' <div class="card-toolbar">'
                            //    + '</div>');
                            tr += "</tr>";
                            $("#list-display").append(tr)


                            var __temp = data[i];
                            __temp.modify_model = modify_model;
                            _bindEvent(__temp);
                        }
                        $(".page_size").html(pager_size);
                        $(".page_count").html(json.data.count);
                        $(".cpages").html(json.data.html);

                        $(".total_amount").html(parseFloat(json.total_amount).toFixed(2));
                        $(".total_number").html(json.total_number);


                        //getdata();

                        show_expireList();

                    } else {

                    }
                },
                error: function () {
                }
            });
        }
        getPager();

        function _bindEvent(__temp) {
            $('button[edit-' + __temp.id + ']').on("click", function () {
                console.log('attrs', $(this).closest('tr').html(), __temp.modify_model);

                edit({ aname: 'admin', title: '修改' + _actionName, action: _action, id: __temp.id, data: __temp.modify_model });
            })
        }

        function _pt(i) {
            pager_index = i;
            getPager();
        }


        $('[lsort]').on('click', function () {
            $(this).css({
                '-webkit-user-select': 'none', /* Chrome, Safari, Opera */
                '-moz-user-select': 'none',    /* Firefox */
                'user-select': 'none'          /* General */
            });

            $(this).siblings().removeAttr("sort_type");
            console.log('click', $(this).text());
            var type = $(this).attr('sort_type');
            $('[lsort]').find('svg').remove();
            var svgHtml = "";
            switch (type) {
                case "down":
                    type = "";
                    break;
                case "up":
                    type = "down";
                    svgHtml = '<svg t="1701962535682" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="1964" width="12" height="12"><path d="M768.002 535.89H600.749l-0.909-322.563h-166.4v322.56l-167.253-0.455L517.181 858.39l250.821-322.5z" fill="#FD3333" p-id="1965"></path></svg>';
                    break;
                default:
                    type = "up";
                    svgHtml = '<svg t="1701963462018" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2232" width="12" height="12"><path d="M255.998 488.11h167.253l0.909 322.563h166.4v-322.56l167.253 0.455L506.819 165.61l-250.821 322.5z" fill="#1296DB" p-id="2233"></path></svg>';
                    break;
            }
            $(this).attr('sort_type', type);
            $(this).append(svgHtml);

            getPager();

        })
    </script>

    <script>
        layui.use(['form', 'layedit', 'laydate'], function () {
        });
    </script>

    <script>
        function update_select(state) {
            event_create({ 'aname': 'admin', 'name': _action, 'event': 'delete', 'id': select_all_id(), 'state': state });
        }


        function report_exl() {
            export_data(getParam());
        }
    </script>

    <script>
        var click_ids = {};
        function checkIsSelect(obj) {
            if (obj.prop('checked')) {
                return true;
            } else {
                return false;
            }
        }
        function goods_click(t) {
            t = $(t);
            var obj = t.find(".good_select");
            obj.prop("checked", !checkIsSelect(obj));

        }

        function select_all(obj) {
            $(".good_select").prop("checked", checkIsSelect($(obj)));
        }

        function select_all_id() {
            var ids = "";
            $("#list-display tr").each(function () {
                var select_obj = $(this).find(".good_select");
                if (checkIsSelect(select_obj)) {
                    if (ids != "") {
                        ids += ",";
                    }
                    ids += select_obj.data("id");
                }
            });
            return ids;
        }
    </script>

    <audio id="myAudio" controls style="display: none;">
        <source src="../static/files/新订单.mp3" type="audio/mpeg">
        Your browser does not support the audio element.
    </audio>

    <script>

        var autoTimer = 0;
        var lastList = null;

        var set_autoAlarm = function (e) {
            if (checkIsSelect($(e))) {
                console.log('启动');
                lastList = null;

                autoTimer = setInterval(function () {


                    getPager();

                }, 5000);

            } else {
                console.log('关闭');
                clearInterval(autoTimer);
                autoTimer = 0;
            }
        }

        var show_expireList = function () {
            // 获取所有的.expire_time标签
            $('.expire_time').each(function () {
                var time = new Date($(this).data('time')).getTime();

                // 创建并附加倒计时元素
                var countdownElement = $('<span style="color: gray;font-size: 12px;">00:00</span>');
                $(this).closest("td").find(".exp_countdown").append(countdownElement);

                // 更新倒计时
                function updateCountdown() {
                    var currentDate = new Date().getTime();
                    var timeLeft = time - currentDate;

                    if (timeLeft <= 0) {
                        countdownElement.text('已返还');
                    } else {
                        var days = Math.floor(timeLeft / (1000 * 60 * 60 * 24));
                        var hours = Math.floor((timeLeft % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
                        var minutes = Math.floor((timeLeft % (1000 * 60 * 60) / (1000 * 60)));
                        var seconds = Math.floor((timeLeft % (1000 * 60) / 1000));

                        if (days > 0) {
                            countdownElement.text(
                              days + '天 ' +
                              hours.toString().padStart(2, '0') + ':' +
                              minutes.toString().padStart(2, '0') + ':' +
                              seconds.toString().padStart(2, '0') + ''
                            );
                        } else {
                            countdownElement.text(
                              hours.toString().padStart(2, '0') + ':' +
                              minutes.toString().padStart(2, '0') + ':' +
                              seconds.toString().padStart(2, '0') + ''
                            );
                        }
                    }
                }

                updateCountdown();

                // 每秒更新一次倒计时
                setInterval(updateCountdown, 1000);
            });
        }

    </script>

    <style>
        .buy_list {
            display: flex;
            align-items: center;
            font-weight: bold;
            margin-top: 30px;
        }

            .buy_list .name {
                width: 100px;
                flex-shrink: 0;
            }

            .buy_list .item {
                width: 100%;
            }

                .buy_list .item input {
                    width: 100%;
                }
    </style>
    <div style="background: #fff; padding: 18px; display: none;" id="buy_page">

        <div class="buy_list">
            <div class="name">
                支付方式：
           
            </div>
            <div class="item">
                <select class="form-control buyer_type">
                    <option>银行卡</option>
                    <option>支付宝</option>
                    <option>微信</option>
                </select>
            </div>
        </div>

        <div class="buy_list">
            <div class="name" style="color: blue;">
                购买数量：           
            </div>
            <div class="item">
                <input class="form-control buyer_number" />
            </div>
        </div>

        <div class="buy_list">
            <div class="name">
                姓名：           
            </div>
            <div class="item">
                <input class="form-control buyer_name" />
            </div>
        </div>

        <div class="buy_list bank_item">
            <div class="name">
                开户行：           
            </div>
            <div class="item">
                <input class="form-control buyer_bankname" />
            </div>
        </div>

        <div class="buy_list">
            <div class="name">
                账号：           
            </div>
            <div class="item">
                <input class="form-control buyer_bankid" />
            </div>
        </div>

        <a class="btn btn-success" onclick="new_order()" style="margin-top: 24px; margin-left: 100px;">
            <svg t="*************" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="4127" width="16" height="16">
                <path d="M512 170.666667a341.333333 341.333333 0 0 0-290.133333 521.216 42.666667 42.666667 0 0 1-72.533334 45.013333A424.874667 424.874667 0 0 1 85.333333 512C85.333333 276.352 276.352 85.333333 512 85.333333s426.666667 191.018667 426.666667 426.666667-191.018667 426.666667-426.666667 426.666667a424.874667 424.874667 0 0 1-234.666667-70.314667 42.666667 42.666667 0 1 1 46.933334-71.210667A341.333333 341.333333 0 1 0 512 170.666667z m222.165333 264.832a42.666667 42.666667 0 0 0-60.330666-60.330667L469.333333 579.669333l-119.168-119.168a42.666667 42.666667 0 0 0-60.330666 60.330667l149.333333 149.333333a42.666667 42.666667 0 0 0 60.330667 0l234.666666-234.666666z" fill="#14101C" p-id="4128"></path></svg>&nbsp;创建订单</a>

    </div>



    <script>
        //// 传入格式为 "yyyy-MM-dd HH:mm:ss" 的时间戳
        //function countdownToTime(timestamp) {
        //    var targetDate = new Date(timestamp);
        //    var currentDate = new Date();
        //    var timeDiff = targetDate - currentDate;

        //    if (timeDiff <= 0) {
        //        return '已返还';
        //    }

        //    var days = Math.floor(timeDiff / (1000 * 60 * 60 * 24));
        //    var hours = Math.floor((timeDiff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
        //    var minutes = Math.floor((timeDiff % (1000 * 60 * 60) / (1000 * 60)));
        //    var seconds = Math.floor((timeDiff % (1000 * 60) / 1000));

        //    if (days > 0) {
        //        return days + '天' + hours + '时' + minutes + '分';
        //    } else {
        //        return hours + '时' + minutes + '分' + seconds + '秒';
        //    }
        //}

        //var interval = 0


        //function getdata() {
        //    clearInterval(interval);
        //    interval = setInterval(function () {
        //        $('td[expiretime]').each(function () {
        //            var expiretime = $(this).attr('expiretime');
        //            var result = countdownToTime(expiretime);
        //            if (result == "已返还") {
        //                result = "<span style='color:gray;'>已返还</span>";
        //                $(this).removeAttr("expiretime");
        //            }
        //            $(this).html(result);
        //        })
        //    }, 1000); // 更新频率每秒钟
        //}

    </script>

</asp:Content>



using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Data;
using LitJson;

public partial class admin_sitecore : baseClass
{
    public string myhost = string.Empty;
    public List<Dictionary<string, object>> arrlist = new List<Dictionary<string, object>>();
    protected void Page_Load(object sender, EventArgs e)
    {

        Page.Title = uConfig.stcdata("sitename") + "-网站设置";
        //Response.Write(ToJson(uConfig.get_sitemap())); 
        //Response.Write(uConfig.stcdata("fake_greate_ts"));
        //Response.End();

        myhost = getHost(Request);
        if (myhost.IndexOf("localhost:") != -1)
        {
            myhost = "..";
        }

        fuzhu fz = new fuzhu(HttpContext.Current);
        if (!fz.user_auths("do=webmaps;"))
        {
            return;
        }



        Dictionary<string, object> pmlist = new Dictionary<string, object>();
        pmlist["userip"] = getUserIP();
        if (Request.Url.Host != "localhost")
        {
            string[] g = uConfig.stcdata("serv_ips").Split('\n');
            if (!g.Contains(pmlist["userip"] + ""))
            {
                log.WriteLog("禁止访问", "webmaps", uConfig.p_idAD + "|" + pmlist["userip"]);
                fz.sendResponse("页面出错！");
            }
        }




        DataTable temp_dt = new DataTable("user_ranklist");

        // 添加列
        temp_dt.Columns.Add("name", typeof(string));
        temp_dt.Columns.Add("key", typeof(string));
        string[] arrayList;


        if (Request.QueryString["appdata"] + "" != "********")
        {
            temp_dt.Rows.Add("任务", "shuadan");
            temp_dt.Rows.Add("买币", "dating");
            temp_dt.Rows.Add("红包雨", "redbag");
            temp_dt.Rows.Add("晒单", "share");
            temp_dt.Rows.Add("聊天室", "chat");
            temp_dt.Rows.Add("出借", "lend");
            temp_dt.Rows.Add("任务&转盘", "activity");
            temp_dt.Rows.Add("一键任务", "onetouch");
            temp_dt.Rows.Add("用户", "account");
            temp_dt.Rows.Add("任务层级", "shuadan_level");
            temp_dt.Rows.Add("卖币设置", "sell_dating");
            temp_dt.Rows.Add("多站配置", "site_group");
            temp_dt.Rows.Add("其他", "other");

            arrlist.Add(new Dictionary<string, object>() { { "name", "授权密钥" }, { "id", "authSecret" }, { "typename", "other" } });

            arrlist.Add(new Dictionary<string, object>() { { "name", "平台名称" }, { "id", "sitename" }, { "typename", "basic" } });
            arrlist.Add(new Dictionary<string, object>() { { "name", "本币名称" }, { "id", "coinname" }, { "typename", "basic" } });
            arrlist.Add(new Dictionary<string, object>() { { "name", "版本说明" }, { "id", "version_remark" }, { "typename", "basic" } });
            arrlist.Add(new Dictionary<string, object>() { { "name", "在线客服" }, { "id", "kf_online" }, { "typename", "basic" } });

            arrlist.Add(new Dictionary<string, object>() { { "name", "返奖比例" }, { "id", "return_usdt" }, { "typename", "basic" } });
            arrlist.Add(new Dictionary<string, object>() { { "name", "金额列表" }, { "id", "regular_money_list" }, { "typename", "basic" }, { "type", "textarea" }, { "height", "100" } });


            arrlist.Add(new Dictionary<string, object>() { { "name", "允许添加好友" }, { "id", "allow_addfriend" }, { "typename", "basic" }, { "type", "option" },{ "data",new List<Dictionary<string, object>>{
            new Dictionary<string, object> { { "允许", "allow" } },
            new Dictionary<string, object> { { "不允许", "not_allow" } }
            }}
            });
            arrlist.Add(new Dictionary<string, object>() { { "name", "顶级号列表" }, { "id", "top_lists" }, { "typename", "basic" }, { "type", "textarea" }, { "height", "250" } });

            arrlist.Add(new Dictionary<string, object>() { { "name", "后台LOGO" }, { "id", "serv_logo" }, { "typename", "basic" }, { "type", "textarea" } });

            arrlist.Add(new Dictionary<string, object>() { { "name", "大厅对接模式" }, { "id", "dating_mode" }, { "typename", "dating" }, { "type", "option" },{ "data",new List<Dictionary<string, object>>{
            new Dictionary<string, object> { { "系统", "sys" } },
            new Dictionary<string, object> { { "对接", "api" } }
            }}
            });
            arrlist.Add(new Dictionary<string, object>() { { "name", "大厅对接域名" }, { "id", "dating_apiurl" }, { "typename", "dating" } });
            arrlist.Add(new Dictionary<string, object>() { { "name", "大厅对接密钥" }, { "id", "dating_apikey" }, { "typename", "dating" } });

            arrlist.Add(new Dictionary<string, object>() { { "name", "回调对接密钥" }, { "id", "dating_notify_key" }, { "typename", "dating" } });



            arrlist.Add(new Dictionary<string, object>() { { "name", "任务刷新间隔" }, { "id", "task_refreshTime" }, { "typename", "shuadan" } });
            arrlist.Add(new Dictionary<string, object>() { { "name", "任务模式" }, { "id", "task_funcs" }, { "typename", "shuadan" }, { "type", "checkbox" },{ "data",new List<Dictionary<string, object>>{
            new Dictionary<string, object> { { "房主佣金", "fzyj" } }
            }} });


            //arrlist.Add(new Dictionary<string, object>() { { "name", "【一键任务】子单量" }, { "id", "onetouch_number" }, { "typename", "onetouch" } });
            arrlist.Add(new Dictionary<string, object>() { { "name", "【一键任务】最低金额" }, { "id", "onetouch_minAmt" }, { "typename", "onetouch" } });
            arrlist.Add(new Dictionary<string, object>() { { "name", "【一键任务】最高金额" }, { "id", "onetouch_maxAmt" }, { "typename", "onetouch" } });

            arrlist.Add(new Dictionary<string, object>() { { "name", "合伙人日工资" }, { "id", "team_daily_salary" }, { "typename", "onetouch" }, { "type", "textarea" } });
            arrlist.Add(new Dictionary<string, object>() { { "name", "直属日工资" }, { "id", "agent_daily_salary" }, { "typename", "onetouch" }, { "type", "textarea" } });



            arrlist.Add(new Dictionary<string, object>() { { "name", "合伙人等级规则" }, { "id", "levels_list" }, { "typename", "shuadan" }, { "type", "textarea" }, { "height", "100" } });

            //arrlist.Add(new Dictionary<string, object>() { { "name", "单卡日次数" }, { "id", "limit_number" }, { "typename", "shuadan" } });
            //arrlist.Add(new Dictionary<string, object>() { { "name", "单卡日金额" }, { "id", "limit_amount" }, { "typename", "shuadan" } });
            arrlist.Add(new Dictionary<string, object>() { { "name", "首页嵌入代码" }, { "id", "index_code" }, { "typename", "basic" }, { "type", "textarea" } });

            //arrlist.Add(new Dictionary<string, object>() { { "name", "抢单延迟时间" }, { "id", "order_delay_time" }, { "typename", "basic" } });
            //arrlist.Add(new Dictionary<string, object>() { { "name", "无订单随机生成" }, { "id", "rand_order_number" }, { "typename", "basic" } });

            arrlist.Add(new Dictionary<string, object>() { { "name", "佣金排行规则" }, { "id", "rand_list_user" }, { "typename", "basic" }, { "type", "textarea" }, { "height", "500" } });
            arrlist.Add(new Dictionary<string, object>() { { "name", "佣金排行用户数" }, { "id", "rand_list_number" }, { "typename", "basic" } });
            arrlist.Add(new Dictionary<string, object>() { { "name", "后台域名" }, { "id", "serv_domains" }, { "typename", "basic" }, { "type", "textarea" }, { "height", "100" } });


            arrlist.Add(new Dictionary<string, object>() { { "name", "卖币手续费" }, { "id", "selldating_tax" }, { "typename", "dating" } });
            arrlist.Add(new Dictionary<string, object>() { { "name", "USDT汇率" }, { "id", "usdt_price" }, { "typename", "dating" } });
            arrlist.Add(new Dictionary<string, object>() { { "name", "商家名称" }, { "id", "shop_name" }, { "typename", "dating" } });
            arrlist.Add(new Dictionary<string, object>() { { "name", "金额区间" }, { "id", "shop_money" }, { "typename", "dating" } });
            arrlist.Add(new Dictionary<string, object>() { { "name", "商家押金" }, { "id", "shop_yajin" }, { "typename", "dating" } });
            arrlist.Add(new Dictionary<string, object>() { { "name", "温馨提示" }, { "id", "shop_tip" }, { "typename", "dating" } });
            arrlist.Add(new Dictionary<string, object>() { { "name", "详情联系ID" }, { "id", "shop_contact" }, { "typename", "dating" } });
            arrlist.Add(new Dictionary<string, object>() { { "name", "出款方式" }, { "id", "payer_ways" }, { "typename", "dating" }, { "type", "textarea" }, { "height", "130" } });


            arrlist.Add(new Dictionary<string, object>() { { "name", "红包雨提示" }, { "id", "redbag_tips" }, { "typename", "redbag" } });
            //arrlist.Add(new Dictionary<string, object>() { { "name", "红包雨时间段" }, { "id", "redbag_times" }, { "typename", "redbag" }, { "type", "textarea" }, { "height", "300" } });
            arrlist.Add(new Dictionary<string, object>() { { "name", "红包雨首页弹窗" }, { "id", "redbag_boxtip" }, { "typename", "redbag" }, { "type", "textarea" }, { "height", "300" } });
            arrlist.Add(new Dictionary<string, object>() { { "name", "红包名称" }, { "id", "redbag_name" }, { "typename", "redbag" }, { "type", "textarea" }, { "height", "100" } });
            arrlist.Add(new Dictionary<string, object>() { { "name", "红包金额" }, { "id", "redbag_amount" }, { "typename", "redbag" } });
            arrlist.Add(new Dictionary<string, object>() { { "name", "红包个数" }, { "id", "redbag_number" }, { "typename", "redbag" } });
            arrlist.Add(new Dictionary<string, object>() { { "name", "条件限制（任务金额）" }, { "id", "rb_limit_sdmoney" }, { "typename", "redbag" } });
            arrlist.Add(new Dictionary<string, object>() { { "name", "条件限制（借款次数）" }, { "id", "rb_limit_lendcount" }, { "typename", "redbag" } });
            arrlist.Add(new Dictionary<string, object>() { { "name", "条件限制（借款额度）" }, { "id", "rb_limit_lendmoney" }, { "typename", "redbag" } });
            arrlist.Add(new Dictionary<string, object>() { { "name", "条件限制（N天任务）" }, { "id", "rb_limit_sdday" }, { "typename", "redbag" } });
            arrlist.Add(new Dictionary<string, object>() { { "name", "群组列表" }, { "id", "redbag_groups" }, { "typename", "redbag" } });
            arrlist.Add(new Dictionary<string, object>() { { "name", "用户领取限制" }, { "id", "user_money_limit" }, { "typename", "redbag" } });
            arrlist.Add(new Dictionary<string, object>() { { "name", "新人领取限制" }, { "id", "newuser_money_limit" }, { "typename", "redbag" } });
            arrlist.Add(new Dictionary<string, object>() { { "name", "托号领取限制" }, { "id", "th_money_limit" }, { "typename", "redbag" } });

            arrlist.Add(new Dictionary<string, object>() { { "name", "[虚包]金额区间" }, { "id", "fake_redbag_money" }, { "typename", "redbag" } });
            arrlist.Add(new Dictionary<string, object>() { { "name", "[虚包]抢占比例" }, { "id", "fake_redbag_rate" }, { "typename", "redbag" } });
            arrlist.Add(new Dictionary<string, object>() { { "name", "[虚包]生成间隔" }, { "id", "fake_redbag_ts" }, { "typename", "redbag" } });
            arrlist.Add(new Dictionary<string, object>() { { "name", "[虚包]单次数量" }, { "id", "fake_redbag_number" }, { "typename", "redbag" } });
            arrlist.Add(new Dictionary<string, object>() { { "name", "[虚包]平均最低值" }, { "id", "fake_redbag_minavg" }, { "typename", "redbag" } });


            arrlist.Add(new Dictionary<string, object>() { { "name", "[虚享]晒单时间" }, { "id", "fake_share_time" }, { "typename", "share" } });
            arrlist.Add(new Dictionary<string, object>() { { "name", "[虚享]晒单金额" }, { "id", "fake_share_money" }, { "typename", "share" } });
            arrlist.Add(new Dictionary<string, object>() { { "name", "[虚享]单次次数" }, { "id", "fake_share_number" }, { "typename", "share" } });
            arrlist.Add(new Dictionary<string, object>() { { "name", "[虚享]生成间隔" }, { "id", "fake_greate_ts" }, { "typename", "share" } });
            arrlist.Add(new Dictionary<string, object>() { { "name", "[虚享]晒单间隔" }, { "id", "fake_share_ts" }, { "typename", "share" } });
            arrlist.Add(new Dictionary<string, object>() { { "name", "[虚享]复晒间隔" }, { "id", "fake_reshare_ts" }, { "typename", "share" } });
            arrlist.Add(new Dictionary<string, object>() { { "name", "[虚享]更榜几率" }, { "id", "fake_share_uprate" }, { "typename", "share" } });
            arrlist.Add(new Dictionary<string, object>() { { "name", "[虚享]复晒几率" }, { "id", "fake_share_rerate" }, { "typename", "share" } });
            arrlist.Add(new Dictionary<string, object>() { { "name", "[虚享]更榜间隔" }, { "id", "fake_share_upts" }, { "typename", "share" } });
            arrlist.Add(new Dictionary<string, object>() { { "name", "[虚享]佣金比例" }, { "id", "fake_reward_rate" }, { "typename", "share" } });
            arrlist.Add(new Dictionary<string, object>() { { "name", "[虚享]历史佣金" }, { "id", "fake_share_reward" }, { "typename", "share" } });
            arrlist.Add(new Dictionary<string, object>() { { "name", "[虚享]最高次数" }, { "id", "fake_upshare_number" }, { "typename", "share" } });
            arrlist.Add(new Dictionary<string, object>() { { "name", "[虚享]最高金额" }, { "id", "fake_upshare_amount" }, { "typename", "share" } });
            arrlist.Add(new Dictionary<string, object>() { { "name", "[虚享]定时晒单" }, { "id", "fake_share_timelist" }, { "typename", "share" }, { "type", "textarea" } });


            arrlist.Add(new Dictionary<string, object>() { { "name", "群组分配模式" }, { "id", "allo_group" }, { "typename", "chat" }, { "type", "option" },{ "data",new List<Dictionary<string, object>>{
            new Dictionary<string, object> { { "追随上级", "allow_parent" } },
            new Dictionary<string, object> { { "仅自己", "only_user" } }
            }}
            });
            arrlist.Add(new Dictionary<string, object>() { { "name", "默认群组" }, { "id", "default_group" }, { "typename", "chat" } });
            arrlist.Add(new Dictionary<string, object>() { { "name", "[聊天室]屏蔽词" }, { "id", "black_words" }, { "typename", "chat" }, { "type", "textarea" }, { "height", "300" } });

            arrlist.Add(new Dictionary<string, object>() { { "name", "[分享活动]规则" }, { "id", "share_sd_number" }, { "typename", "shuadan" }, { "type", "textarea" }, { "height", "100" } });

            arrlist.Add(new Dictionary<string, object>() { { "name", "任务榜奖励" }, { "id", "share_ranking_list" }, { "typename", "chat" }, { "type", "textarea" }, { "height", "438" } });
            arrlist.Add(new Dictionary<string, object>() { { "name", "榜单截止时间" }, { "id", "ranking_stop_time" }, { "typename", "chat" } });
            arrlist.Add(new Dictionary<string, object>() { { "name", "佣金榜更新时间" }, { "id", "index_ranking_uptime" }, { "typename", "chat" } });

            //arrlist.Add(new Dictionary<string, object>() { { "name", "任务生成时间" }, { "id", "task_html_time" }, { "typename", "chat" } });
            //arrlist.Add(new Dictionary<string, object>() { { "name", "任务生成间隔" }, { "id", "task_html_ts" }, { "typename", "chat" } });
            //arrlist.Add(new Dictionary<string, object>() { { "name", "任务生成条数" }, { "id", "task_html_count" }, { "typename", "chat" } });

            //arrlist.Add(new Dictionary<string, object>() { { "name", "转盘生成时间" }, { "id", "wheel_html_time" }, { "typename", "chat" } });
            //arrlist.Add(new Dictionary<string, object>() { { "name", "转盘生成间隔" }, { "id", "wheel_html_ts" }, { "typename", "chat" } });
            //arrlist.Add(new Dictionary<string, object>() { { "name", "转盘生成条数" }, { "id", "wheel_html_count" }, { "typename", "chat" } });

            //arrlist.Add(new Dictionary<string, object>() { { "name", "领佣生成时间" }, { "id", "yjlq_html_time" }, { "typename", "chat" } });
            //arrlist.Add(new Dictionary<string, object>() { { "name", "领佣生成间隔" }, { "id", "yjlq_html_ts" }, { "typename", "chat" } });
            //arrlist.Add(new Dictionary<string, object>() { { "name", "领佣生成条数" }, { "id", "yjlq_html_count" }, { "typename", "chat" } });


            arrlist.Add(new Dictionary<string, object>() { { "name", "[收单]Ip白名单" }, { "id", "sd_ip_limits" }, { "typename", "shuadan" }, { "type", "textarea" }, { "height", "100" } });

            arrlist.Add(new Dictionary<string, object>() { { "name", "系统订单接单超时" }, { "id", "sys_order_timeout" }, { "typename", "shuadan" } });
            arrlist.Add(new Dictionary<string, object>() { { "name", "新号订单确认超时" }, { "id", "sys_comfirm_timeout" }, { "typename", "shuadan" } });
            arrlist.Add(new Dictionary<string, object>() { { "name", "非新订单确认超时" }, { "id", "fnew_comfirm_timeout" }, { "typename", "shuadan" } });

            arrlist.Add(new Dictionary<string, object>() { { "name", "托号任务生成限制" }, { "id", "thsd_generate_limit" }, { "typename", "shuadan" } });
            arrlist.Add(new Dictionary<string, object>() { { "name", "新号任务生成限制" }, { "id", "sd_generate_limit" }, { "typename", "shuadan" } });

            arrlist.Add(new Dictionary<string, object>() { { "name", "任务佣金比例" }, { "id", "transport_rate" }, { "typename", "shuadan" } });
            //arrlist.Add(new Dictionary<string, object>() { { "name", "用户同时接单量" }, { "id", "max_transport_number" }, { "typename", "shuadan" } });
            arrlist.Add(new Dictionary<string, object>() { { "name", "接单金额差额" }, { "id", "limit_similar" }, { "typename", "shuadan" } });
            arrlist.Add(new Dictionary<string, object>() { { "name", "超时返规则（秒）" }, { "id", "timeoutRet_rules" }, { "typename", "shuadan" }, { "type", "textarea" } });
            arrlist.Add(new Dictionary<string, object>() { { "name", "默认超时返还（分）" }, { "id", "timeoutRet_minute" }, { "typename", "shuadan" } });


            arrlist.Add(new Dictionary<string, object>() { { "name", "买币赠送比例" }, { "id", "buylist_p1" }, { "typename", "dating" } });
            arrlist.Add(new Dictionary<string, object>() { { "name", "支付宝赠送比例" }, { "id", "buylist_ali_p1" }, { "typename", "dating" } });
            //arrlist.Add(new Dictionary<string, object>() { { "name", "[U补单]补单地址" }, { "id", "ubd_address" }, { "typename", "dating" }, { "type", "textarea" } });
            arrlist.Add(new Dictionary<string, object>() { { "name", "[U补单]随机金额" }, { "id", "ubd_money" }, { "typename", "dating" } });



            arrlist.Add(new Dictionary<string, object>() { { "name", "出借系统" }, { "id", "gn_lend" }, { "typename", "lend" }, { "type", "option" },{ "data",new List<Dictionary<string, object>>{
            new Dictionary<string, object> { { "显示", "on" } },
            new Dictionary<string, object> { { "隐藏", "off" } }
            }} });
            arrlist.Add(new Dictionary<string, object>() { { "name", "生成金额基数" }, { "id", "lend_money" }, { "typename", "lend" } });
            arrlist.Add(new Dictionary<string, object>() { { "name", "生成最高倍数" }, { "id", "max_lend_multiple" }, { "typename", "lend" } });
            arrlist.Add(new Dictionary<string, object>() { { "name", "邀一获配额度" }, { "id", "lend_share_quota" }, { "typename", "lend" } });
            arrlist.Add(new Dictionary<string, object>() { { "name", "获配额度封顶" }, { "id", "lend_max_quota" }, { "typename", "lend" } });
            arrlist.Add(new Dictionary<string, object>() { { "name", "用户基础额度" }, { "id", "base_quota" }, { "typename", "lend" } });
            arrlist.Add(new Dictionary<string, object>() { { "name", "用户基础出借次数" }, { "id", "base_lend_number" }, { "typename", "lend" } });
            arrlist.Add(new Dictionary<string, object>() { { "name", "出借最多条数" }, { "id", "lend_orderNumber" }, { "typename", "lend" } });

            arrlist.Add(new Dictionary<string, object>() { { "name", "定时更换借单" }, { "id", "lend_change_minute" }, { "typename", "lend" } });
            arrlist.Add(new Dictionary<string, object>() { { "name", "定时更换条数" }, { "id", "lend_change_number" }, { "typename", "lend" } });


            arrlist.Add(new Dictionary<string, object>() { { "name", "转盘基础次数" }, { "id", "wheel_number" }, { "typename", "activity" } });
            arrlist.Add(new Dictionary<string, object>() { { "name", "转盘奖励几率" }, { "id", "wheel_reward_rules" }, { "typename", "activity" }, { "type", "textarea" } });


            arrlist.Add(new Dictionary<string, object>() { { "name", "有效会员（任务金额）" }, { "id", "sd_valid_money" }, { "typename", "shuadan" } });
            arrlist.Add(new Dictionary<string, object>() { { "name", "有效会员（出借次数）" }, { "id", "sd_valid_lendcount" }, { "typename", "shuadan" } });

            arrlist.Add(new Dictionary<string, object>() { { "name", "普通三级佣金" }, { "id", "brok_user" }, { "typename", "shuadan" } });
            arrlist.Add(new Dictionary<string, object>() { { "name", "托号三级佣金" }, { "id", "brok_th" }, { "typename", "shuadan" } });
            arrlist.Add(new Dictionary<string, object>() { { "name", "任务转大厅赠送" }, { "id", "sdzdt_p1" }, { "typename", "shuadan" } });
            arrlist.Add(new Dictionary<string, object>() { { "name", "任务转大厅可见" }, { "id", "sdzdt_show_user" }, { "typename", "shuadan" }, { "type", "checkbox" },{ "data",new List<Dictionary<string, object>>{
            new Dictionary<string, object> { { "托号", "th" } },
            new Dictionary<string, object> { { "普通用户", "user" } },
            new Dictionary<string, object> { { "新注册用户", "reg" } },
            new Dictionary<string, object> { { "高级用户", "super" } }
            }} });

            arrlist.Add(new Dictionary<string, object>() { { "name", "下级显示密钥" }, { "id", "showkey" }, { "typename", "other" } });

            arrlist.Add(new Dictionary<string, object>() { { "name", "奖励满X调整层级" }, { "id", "u_upgrade_reward" }, { "typename", "account" } });
            arrlist.Add(new Dictionary<string, object>() { { "name", "允许任务时间" }, { "id", "allow_sd_time" }, { "typename", "shuadan" } });
            //arrlist.Add(new Dictionary<string, object>() { { "name", "允许挂单时间" }, { "id", "allow_gd_time" }, { "typename", "shuadan" } });
            arrlist.Add(new Dictionary<string, object>() { { "name", "新手任务时间" }, { "id", "new_sd_time" }, { "typename", "shuadan" } });


            arrlist.Add(new Dictionary<string, object>() { { "name", "API订单赠送比例" }, { "id", "apibuy_p1" }, { "typename", "dating" } });
            arrlist.Add(new Dictionary<string, object>() { { "name", "API订单可见用户" }, { "id", "apibuy_show_user" }, { "typename", "dating" }, { "type", "checkbox" },{ "data",new List<Dictionary<string, object>>{
            new Dictionary<string, object> { { "托号", "th" } },
            new Dictionary<string, object> { { "普通用户", "user" } },
            new Dictionary<string, object> { { "新注册用户", "reg" } },
            new Dictionary<string, object> { { "高级用户", "super" } }
            }} });


            arrlist.Add(new Dictionary<string, object>() { { "name", "出借-绑定回款" }, { "id", "lendcount_bind" }, { "typename", "lend" } });
            arrlist.Add(new Dictionary<string, object>() { { "name", "出借-任务满额" }, { "id", "lendcount_sd" }, { "typename", "lend" } });
            arrlist.Add(new Dictionary<string, object>() { { "name", "出借-下级出借" }, { "id", "lendcount_xjcj" }, { "typename", "lend" } });


            string[] a1 = { "托号", "新用户", "普通用户", "高级用户" };
            string[] a2 = { "th", "reg", "user", "super" };

            for (int i = 0; i < a1.Length; i++)
            {
                arrlist.Add(new Dictionary<string, object>() { { "name", "【" + a1[i] + "】显示类型" }, { "id", "level_"+a2[i]+"_view" }, { "typename", "shuadan_level" }, { "type", "option" },{ "data",new List<Dictionary<string, object>>{
                new Dictionary<string, object> { { "额度", "amount" } },
                new Dictionary<string, object> { { "次数", "number" } }
                }}
                });

                arrlist.Add(new Dictionary<string, object>() { { "name", "【" + a1[i] + "】新手任务" }, { "id", a2[i]+"_task_view" }, { "typename", "shuadan_level" }, { "type", "option" },{ "data",new List<Dictionary<string, object>>{
                new Dictionary<string, object> { { "显示", "show" } },
                new Dictionary<string, object> { { "隐藏", "hide" } }
                }}
                });

                arrlist.Add(new Dictionary<string, object>() { { "name", "单卡次数" }, { "id", a2[i] + "_limit_number" }, { "typename", "shuadan_level" } });
                arrlist.Add(new Dictionary<string, object>() { { "name", "单卡金额" }, { "id", a2[i] + "_limit_amount" }, { "typename", "shuadan_level" } });
                arrlist.Add(new Dictionary<string, object>() { { "name", "用户同时接单量" }, { "id", a2[i] + "_order_number" }, { "typename", "shuadan_level" } });
            }


            //arrlist.Add(new Dictionary<string, object>() { { "name", "任务金额" }, { "id",  "sd_moneys" }, { "typename", "shuadan" }, { "type", "textarea" } });
            //for (int i = 0; i < a2.Length; i++)
            //{
            //    arrlist.Add(new Dictionary<string, object>() { { "name", a1[i] + "~任务金额" }, { "id", a2[i] + "_sd_moneys" }, { "typename", "shuadan" }, { "type", "textarea" } });
            //}

            arrlist.Add(new Dictionary<string, object>() { { "name", "任务金额" }, { "id", "reg_sd_moneys" }, { "typename", "shuadan" }, { "type", "textarea" } });


            //arrlist.Add(new Dictionary<string, object>() { { "name", "利息天数规则" }, { "id", "quota_rules" }, { "typename", "lend" }, { "type", "textarea" } });
            arrlist.Add(new Dictionary<string, object>() { { "name", "出借分类" }, { "id", "lend_class" }, { "typename", "lend" }, { "type", "textarea" } });


            arrayList = uConfig.stcdata("lend_class").Split('\n');//LX天数规则
            for (int i = 0; i < arrayList.Length; i++)
            {
                arrlist.Add(new Dictionary<string, object>() { { "name", "【" + arrayList[i] + "】规则" }, { "id", "rl_" + arrayList[i] }, { "typename", "lend" }, { "type", "textarea" } });
            }

            arrlist.Add(new Dictionary<string, object>() { { "name", "新手买币可见" }, { "id", "new_buyAmount" }, { "typename", "dating" } });



            arrlist.Add(new Dictionary<string, object>() { { "name", "借款需知" }, { "id", "lend_apply_text" }, { "typename", "lend" }, { "type", "textarea" } });
            arrlist.Add(new Dictionary<string, object>() { { "name", "周转期限" }, { "id", "lend_apply_days" }, { "typename", "lend" } });
            arrlist.Add(new Dictionary<string, object>() { { "name", "最高借款金额" }, { "id", "lend_apply_max" }, { "typename", "lend" } });

            arrlist.Add(new Dictionary<string, object>() { { "name", "测试用户" }, { "id", "test_users" }, { "typename", "other" }, { "type", "textarea" } });


            arrlist.Add(new Dictionary<string, object>() { { "name", "最高借款金额" }, { "id", "lend_apply_max" }, { "typename", "lend" } });


            arrlist.Add(new Dictionary<string, object>() { { "name", "挂单超时时间" }, { "id", "gd_timeout" }, { "typename", "shuadan" } });
            arrlist.Add(new Dictionary<string, object>() { { "name", "挂单金额限制" }, { "id", "gd_limit_money" }, { "typename", "shuadan" } });
            arrlist.Add(new Dictionary<string, object>() { { "name", "挂单可用用户" }, { "id", "gd_limit_user" }, { "typename", "shuadan" }, { "type", "checkbox" },{ "data",new List<Dictionary<string, object>>{
            new Dictionary<string, object> { { "托号", "th" } },
            new Dictionary<string, object> { { "普通用户", "user" } },
            new Dictionary<string, object> { { "新注册用户", "reg" } },
            new Dictionary<string, object> { { "高级用户", "super" } }
            }} });


            //arrlist.Add(new Dictionary<string, object>() { { "name", "新手买币模式" }, { "id", "new_buymode" }, { "typename", "dating" }, { "type", "option" },{ "data",new List<Dictionary<string, object>>{
            //new Dictionary<string, object> { { "传图片", "img" } },
            //new Dictionary<string, object> { { "传视频", "video" } }
            //}}
            //});



            for (int i = 0; i < a1.Length; i++)
            {
                arrlist.Add(new Dictionary<string, object>() { { "name", "" + a1[i] + "买币模式" }, { "id", ""+a2[i]+"_buymode" }, { "typename", "dating" }, { "type", "option" },                      { "data",new List<Dictionary<string, object>>{
            new Dictionary<string, object> { { "传图片", "img" } },
            new Dictionary<string, object> { { "传视频", "video" } }
            }}
            });
            }

            arrlist.Add(new Dictionary<string, object>() { { "name", "绑定同名" }, { "id", "bind_samename" }, { "typename", "shuadan" }, { "type", "option" },{ "data",new List<Dictionary<string, object>>{
            new Dictionary<string, object> { { "允许", "on" } },
            new Dictionary<string, object> { { "不允许", "off" } }
            }} });
            arrlist.Add(new Dictionary<string, object>() { { "name", "同名绑定上限" }, { "id", "bind_samename_count" }, { "typename", "shuadan" } });

            arrlist.Add(new Dictionary<string, object>() { { "name", "更改姓名" }, { "id", "update_bindname" }, { "typename", "shuadan" }, { "type", "option" },{ "data",new List<Dictionary<string, object>>{
            new Dictionary<string, object> { { "允许", "on" } },
            new Dictionary<string, object> { { "不允许", "off" } }
            }} });

            //arrlist.Add(new Dictionary<string, object>() { { "name", "派单超时" }, { "id", "dispatch_timeout" }, { "typename", "shuadan" } });



            arrlist.Add(new Dictionary<string, object>() { { "name", "虚拟用户名" }, { "id", "fake_username" }, { "typename", "chat" }, { "type", "textarea" } });

            arrlist.Add(new Dictionary<string, object>() { { "name", "卖币限额区间" }, { "id", "sell_limit" }, { "typename", "sell_dating" } });
            arrlist.Add(new Dictionary<string, object>() { { "name", "卖币赠送比例" }, { "id", "sell_p1" }, { "typename", "sell_dating" } });
            arrlist.Add(new Dictionary<string, object>() { { "name", "卖币可用用户" }, { "id", "sell_show_user" }, { "typename", "sell_dating" }, { "type", "checkbox" },{ "data",new List<Dictionary<string, object>>{
            new Dictionary<string, object> { { "托号", "th" } },
            new Dictionary<string, object> { { "普通用户", "user" } },
            new Dictionary<string, object> { { "新注册用户", "reg" } },
            new Dictionary<string, object> { { "高级用户", "super" } }
            }} });
            arrlist.Add(new Dictionary<string, object>() { { "name", "卖币基础倍数" }, { "id", "sell_double" }, { "typename", "sell_dating" } });


            arrlist.Add(new Dictionary<string, object>() { { "name", "聊天室标题" }, { "id", "chat_title" }, { "typename", "other" } });
            arrlist.Add(new Dictionary<string, object>() { { "name", "聊天室介绍" }, { "id", "chat_desc" }, { "typename", "other" } });


            arrlist.Add(new Dictionary<string, object>() { { "name", "跟投生成间隔" }, { "id", "gt_ts" }, { "typename", "chat" } });
            arrlist.Add(new Dictionary<string, object>() { { "name", "跟投随机条数" }, { "id", "gt_number" }, { "typename", "chat" }, { "type", "textarea" } });


            arrlist.Add(new Dictionary<string, object>() { { "name", "重置默认密码" }, { "id", "reset_password" }, { "typename", "account" } });
            arrlist.Add(new Dictionary<string, object>() { { "name", "转盘游戏规则" }, { "id", "zp_game_counts" }, { "typename", "activity" }, { "type", "textarea" } });


            arrlist.Add(new Dictionary<string, object>() { { "name", "推广页面" }, { "id", "partners_page" }, { "typename", "site_group" }, { "type", "option" },{ "data",new List<Dictionary<string, object>>{
            new Dictionary<string, object> { { "合伙人页面", "agent" } },
            new Dictionary<string, object> { { "娱乐推广", "game" } }
            }} });


            arrlist.Add(new Dictionary<string, object>() { { "name", "新手任务团队返佣" }, { "id", "task_teambork" }, { "typename", "site_group" }, { "type", "option" },{ "data",new List<Dictionary<string, object>>{
            new Dictionary<string, object> { { "发放（推广页领取）", "on" } },
            new Dictionary<string, object> { { "不发放", "off" } }
            }} });


            arrlist.Add(new Dictionary<string, object>() { { "name", "一键任务团队返佣" }, { "id", "onetouch_teambork" }, { "typename", "site_group" }, { "type", "option" },{ "data",new List<Dictionary<string, object>>{
            new Dictionary<string, object> { { "发放（推广页领取）", "on" } },
            new Dictionary<string, object> { { "不发放", "off" } }
            }} });


        }
        else
        {


            temp_dt.Rows.Add("银行二要素&短信", "sms2auth");
            temp_dt.Rows.Add("三方对接", "apipay");
            temp_dt.Rows.Add("三方支付设置", "apipay_setting");
            temp_dt.Rows.Add("任务推送", "api_shuadan");
            temp_dt.Rows.Add("新手转单API", "apiorder_reg");
            temp_dt.Rows.Add("游戏", "game");
            temp_dt.Rows.Add("安全", "security");




            arrlist.Add(new Dictionary<string, object>() { { "name", "后台ip白名单" }, { "id", "serv_ips" }, { "typename", "security" }, { "type", "textarea" } });


            //arrlist.Add(new Dictionary<string, object>() { { "name", "通用（通道列表）" }, { "id", "myj_channel_list" }, { "typename", "basic" }, { "type", "textarea" } });
            arrlist.Add(new Dictionary<string, object>() { { "name", "通道日充限制" }, { "id", "daily_pay_limit" }, { "typename", "basic" } });

            arrlist.Add(new Dictionary<string, object>() { { "name", "阿里云（AppCode）" }, { "id", "ali_appcode" }, { "typename", "sms2auth" } });
            arrlist.Add(new Dictionary<string, object>() { { "name", "联卓数据（Appkey）" }, { "id", "lundroid_appkey" }, { "typename", "sms2auth" } });

            arrlist.Add(new Dictionary<string, object>() { { "name", "短信发送渠道" }, { "id", "sms_channel" }, { "typename", "sms2auth" }, { "type", "option" },{ "data",new List<Dictionary<string, object>>{
            new Dictionary<string, object> { { "挖数据", "wapi" } },
            new Dictionary<string, object> { { "短信宝", "dxb" } }
            }} });
            arrlist.Add(new Dictionary<string, object>() { { "name", "挖数据（Appid）" }, { "id", "wapi_appid" }, { "typename", "sms2auth" } });
            arrlist.Add(new Dictionary<string, object>() { { "name", "挖数据（AppSecret）" }, { "id", "wapi_appsecret" }, { "typename", "sms2auth" } });
            arrlist.Add(new Dictionary<string, object>() { { "name", "挖数据（模板ID）" }, { "id", "wapi_template_id" }, { "typename", "sms2auth" } });
            arrlist.Add(new Dictionary<string, object>() { { "name", "挖数据（手动模板ID）" }, { "id", "act_tplid" }, { "typename", "sms2auth" } });

            arrlist.Add(new Dictionary<string, object>() { { "name", "短信宝（用户名）" }, { "id", "dxb_username" }, { "typename", "sms2auth" } });
            arrlist.Add(new Dictionary<string, object>() { { "name", "短信宝（密码）" }, { "id", "dxb_password" }, { "typename", "sms2auth" } });
            arrlist.Add(new Dictionary<string, object>() { { "name", "短信宝（短信模板）" }, { "id", "dxb_model" }, { "typename", "sms2auth" } });


            // ------------------------------- 支付 ---------------------------------



            arrlist.Add(new Dictionary<string, object>() { { "name", "KDPAY（商户ID）" }, { "id", "kdpay_merchantid" }, { "typename", "apipay" } });
            arrlist.Add(new Dictionary<string, object>() { { "name", "KDPAY（商户密钥）" }, { "id", "kdpay_apikey" }, { "typename", "apipay" } });
            arrlist.Add(new Dictionary<string, object>() { { "name", "KDPAY（充值接口）" }, { "id", "kdpay_payurl" }, { "typename", "apipay" } });

            arrlist.Add(new Dictionary<string, object>() { { "name", "小额充值（商户ID）" }, { "id", "mypay_merchantid" }, { "typename", "apipay" } });
            arrlist.Add(new Dictionary<string, object>() { { "name", "小额充值（商户密钥）" }, { "id", "mypay_apikey" }, { "typename", "apipay" } });
            arrlist.Add(new Dictionary<string, object>() { { "name", "小额充值（充值接口）" }, { "id", "mypay_payurl" }, { "typename", "apipay" } });


            arrlist.Add(new Dictionary<string, object>() { { "name", "ABPAY（商户ID）" }, { "id", "abpay_merchantid" }, { "typename", "apipay" } });
            arrlist.Add(new Dictionary<string, object>() { { "name", "ABPAY（通用密钥）" }, { "id", "abpay_apikey" }, { "typename", "apipay" } });
            arrlist.Add(new Dictionary<string, object>() { { "name", "ABPAY（充值密钥）" }, { "id", "abpay_reckey" }, { "typename", "apipay" } });
            arrlist.Add(new Dictionary<string, object>() { { "name", "ABPAY（下发密钥）" }, { "id", "abpay_paykey" }, { "typename", "apipay" } });
            arrlist.Add(new Dictionary<string, object>() { { "name", "ABPAY（大厅接口）" }, { "id", "abpay_payurl" }, { "typename", "apipay" } });
            arrlist.Add(new Dictionary<string, object>() { { "name", "ABPAY（划转接口）" }, { "id", "abpay_transurl" }, { "typename", "apipay" } });

            arrlist.Add(new Dictionary<string, object>() { { "name", "支付宝（商户ID）" }, { "id", "yzf_merchantid" }, { "typename", "apipay" } });
            arrlist.Add(new Dictionary<string, object>() { { "name", "支付宝（商户密钥）" }, { "id", "yzf_apikey" }, { "typename", "apipay" } });
            arrlist.Add(new Dictionary<string, object>() { { "name", "支付宝（充值接口）" }, { "id", "yzf_payurl" }, { "typename", "apipay" } });

            arrlist.Add(new Dictionary<string, object>() { { "name", "GOPAY（商户ID）" }, { "id", "api_merchantid" }, { "typename", "apipay" } });
            arrlist.Add(new Dictionary<string, object>() { { "name", "GOPAY（商户密钥）" }, { "id", "api_apikey" }, { "typename", "apipay" } });
            arrlist.Add(new Dictionary<string, object>() { { "name", "GOPAY（充值接口）" }, { "id", "api_pay_url" }, { "typename", "apipay" } });
            //arrlist.Add(new Dictionary<string, object>() { { "name", "GOPAY（通道列表）" }, { "id", "gopay_channel" }, { "typename", "basic" }, { "type", "textarea" } });
            //arrlist.Add(new Dictionary<string, object>() { { "name", "GOPAY（查询接口）" }, { "id", "api_query_url" }, { "typename", "apipay" } });

            arrlist.Add(new Dictionary<string, object>() { { "name", "OKPAY（商户ID）" }, { "id", "okpay_merchantid" }, { "typename", "apipay" } });
            arrlist.Add(new Dictionary<string, object>() { { "name", "OKPAY（商户密钥）" }, { "id", "okpay_apikey" }, { "typename", "apipay" } });
            arrlist.Add(new Dictionary<string, object>() { { "name", "OKPAY（充值接口）" }, { "id", "okpay_pay_url" }, { "typename", "apipay" } });
            //arrlist.Add(new Dictionary<string, object>() { { "name", "OKPAY（通道列表）" }, { "id", "okpay_channel" }, { "typename", "basic" }, { "type", "textarea" } });
            //arrlist.Add(new Dictionary<string, object>() { { "name", "OKPAY（查询接口）" }, { "id", "okpay_query_url" }, { "typename", "apipay" } });



            arrlist.Add(new Dictionary<string, object>() { { "name", "U盈（商户ID）" }, { "id", "uy_merchantid" }, { "typename", "apipay" } });
            arrlist.Add(new Dictionary<string, object>() { { "name", "U盈（商户密钥）" }, { "id", "uy_apikey" }, { "typename", "apipay" } });
            arrlist.Add(new Dictionary<string, object>() { { "name", "U盈（充值接口）" }, { "id", "uy_pay_url" }, { "typename", "apipay" } });


            arrlist.Add(new Dictionary<string, object>() { { "name", "YAO（商戶號）" }, { "id", "yao_merchantid" }, { "typename", "apipay" } });
            arrlist.Add(new Dictionary<string, object>() { { "name", "YAO（对接key）" }, { "id", "yao_apikey" }, { "typename", "apipay" } });
            arrlist.Add(new Dictionary<string, object>() { { "name", "YAO（充值接口）" }, { "id", "yao_pay_url" }, { "typename", "apipay" } });



            //arrlist.Add(new Dictionary<string, object>() { { "name", "美宜佳（网关地址）" }, { "id", "myj_url" }, { "typename", "apipay" } });
            //arrlist.Add(new Dictionary<string, object>() { { "name", "美宜佳（商户ID）" }, { "id", "myj_mchid" }, { "typename", "apipay" } });
            //arrlist.Add(new Dictionary<string, object>() { { "name", "美宜佳（商户密钥）" }, { "id", "myj_apikey" }, { "typename", "apipay" } });

            //arrlist.Add(new Dictionary<string, object>() { { "name", "玖玖（网关地址）" }, { "id", "jjf_url" }, { "typename", "apipay" } });
            //arrlist.Add(new Dictionary<string, object>() { { "name", "玖玖（商户ID）" }, { "id", "jjf_mchid" }, { "typename", "apipay" } });
            //arrlist.Add(new Dictionary<string, object>() { { "name", "玖玖（商户密钥）" }, { "id", "jjf_apikey" }, { "typename", "apipay" } });


            arrlist.Add(new Dictionary<string, object>() { { "name", "推单网关" }, { "id", "transport_network" }, { "typename", "api_shuadan" } });
            arrlist.Add(new Dictionary<string, object>() { { "name", "推单Appid" }, { "id", "transport_appid" }, { "typename", "api_shuadan" } });
            arrlist.Add(new Dictionary<string, object>() { { "name", "推单密钥" }, { "id", "transport_apikey" }, { "typename", "api_shuadan" } });
            arrlist.Add(new Dictionary<string, object>() { { "name", "买币单调试密钥" }, { "id", "buylist_key" }, { "typename", "basic" } });
            arrlist.Add(new Dictionary<string, object>() { { "name", "买币网关" }, { "id", "buylist_network" }, { "typename", "api_shuadan" } });
            arrlist.Add(new Dictionary<string, object>() { { "name", "买币Appid" }, { "id", "buylist_appid" }, { "typename", "api_shuadan" } });
            arrlist.Add(new Dictionary<string, object>() { { "name", "买币Appids" }, { "id", "buy_appids" }, { "typename", "api_shuadan" }, { "type", "textarea" } });
            arrlist.Add(new Dictionary<string, object>() { { "name", "买币密钥" }, { "id", "buylist_apikey" }, { "typename", "api_shuadan" } });


            arrlist.Add(new Dictionary<string, object>() { { "name", "网关地址" }, { "id", "apireg_network" }, { "typename", "apiorder_reg" } });
            arrlist.Add(new Dictionary<string, object>() { { "name", "商务号" }, { "id", "apireg_userNo" }, { "typename", "apiorder_reg" } });
            arrlist.Add(new Dictionary<string, object>() { { "name", "密钥" }, { "id", "apireg_key" }, { "typename", "apiorder_reg" } });
            arrlist.Add(new Dictionary<string, object>() { { "name", "通知推送地址" }, { "id", "apireg_notifyUrl" }, { "typename", "apiorder_reg" } });


            arrlist.Add(new Dictionary<string, object>() { { "name", "siteid" }, { "id", "siteid" }, { "typename", "game" } });
            arrlist.Add(new Dictionary<string, object>() { { "name", "sitecode" }, { "id", "sitecode" }, { "typename", "game" } });
            //arrlist.Add(new Dictionary<string, object>() { { "name", "授权列表" }, { "id", "game_list" }, { "typename", "game" }, { "type", "textarea" } });
            //arrlist.Add(new Dictionary<string, object>() { { "name", "授权网关" }, { "id", "gamestart_url" }, { "typename", "game" } });
            //arrlist.Add(new Dictionary<string, object>() { { "name", "授权关闭网关" }, { "id", "gameclose_url" }, { "typename", "game" } });
            arrlist.Add(new Dictionary<string, object>() { { "name", "授权超时（秒）" }, { "id", "expire_second" }, { "typename", "game" } });
            arrlist.Add(new Dictionary<string, object>() { { "name", "授权白名单" }, { "id", "game_ips" }, { "typename", "game" }, { "type", "textarea" } });
            arrlist.Add(new Dictionary<string, object>() { { "name", "授权通知网关" }, { "id", "g_notify_gateway" }, { "typename", "game" } });
            arrlist.Add(new Dictionary<string, object>() { { "name", "API查询网关" }, { "id", "g_api_gateway" }, { "typename", "game" } });
            arrlist.Add(new Dictionary<string, object>() { { "name", "API-G-appid" }, { "id", "g_api_appid" }, { "typename", "game" } });
            arrlist.Add(new Dictionary<string, object>() { { "name", "API-G-appkey" }, { "id", "g_api_appkey" }, { "typename", "game" } });
            arrlist.Add(new Dictionary<string, object>() { { "name", "授权域名(SSL)" }, { "id", "g_api_domains" }, { "typename", "game" }, { "type", "textarea" } });
            arrlist.Add(new Dictionary<string, object>() { { "name", "授权域名(无SSL)" }, { "id", "g_api_domains_nssl" }, { "typename", "game" }, { "type", "textarea" } });






            string[] pay1 = { "小额充值", "收银台", "支付宝转账", "OKPAY", "GOPAY", "ABPAY", "KDPAY" };

            for (int i = 0; i < pay1.Length; i++)
            {
                arrlist.Add(new Dictionary<string, object>() { { "name", pay1[i] + "【限充次数】" }, { "id", pay1[i] + "_num" }, { "typename", "apipay_setting" } });
                arrlist.Add(new Dictionary<string, object>() { { "name", pay1[i] + "【赠送比例%】" }, { "id", pay1[i] + "_rw" }, { "typename", "apipay_setting" } });
            }




        }


        keysList.DataSource = temp_dt;
        keysList.DataBind();



    }
    public object getdic(Dictionary<string, object> p, string name, object defval = null)
    {
        object v = null;
        try
        {
            v = p[name];
        }
        catch (Exception)
        {
            v = defval;
        }
        if (v == null)
        {
            v = "";
        }
        return v;
    }
}
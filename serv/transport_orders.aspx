<%@ Page Title="抢单管理" Language="C#" MasterPageFile="~/serv/m.master" AutoEventWireup="true" CodeFile="transport_orders.aspx.cs" Inherits="w_index" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="Server">
    <link href="../css/element-ui.css" rel="stylesheet" />
    <style>
        .el-tag.el-tag--test {
            background-color: #d5d5f5;
            border-color: #7d7dc7;
            color: #6d3333;
        }
    </style>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="Server">
    <div class="ibox float-e-margins">
        <div class="ibox-title">
            <h5 style="color: #8d07d1; text-shadow: 5px 5px 15px #dbd52585;"><%=Request.QueryString["group"]+""=="1"?"<svg t=\"\" class=\"icon\" viewBox=\"0 0 1024 1024\" version=\"1.1\" xmlns=\"http://www.w3.org/2000/svg\" p-id=\"20521\" width=\"25\" height=\"25\" style=\"position:relative;top:5px;\"><path d=\"M706.4 98.7H317.2c-34.1 0-61.7 18.7-61.7 41.9V656h512.6V140.6c0-23.2-27.6-41.9-61.7-41.9z\" fill=\"#AED581\" p-id=\"20522\"></path><path d=\"M512.2 575.7c-35.2 0-63.9-28.6-63.9-63.9 0-35.2 28.6-63.9 63.9-63.9s63.9 28.6 63.9 63.9-28.6 63.9-63.9 63.9z m127.9-192.8v-64H384v64h256.1z m0-127.6v-64H384v64h256.1z\" fill=\"#558B2F\" p-id=\"20523\"></path><path d=\"M583.7 650c-51.7-1.4-31.6 0.7-45.7 0-7.8-0.4-14.2-1.6-22-2-15.5-0.8-31.1-6.2-66.7-19.3-55.7-20.6-127.4-71.3-154-71.3l64 168.5c8.7 13.2 68.5 62.1 97.5 62.1h188.3c0.4 0 76.9-4.6 76.9-69 0-58.6-20.6-65.9-138.3-69z\" fill=\"#FFFFFF\" p-id=\"20524\"></path><path d=\"M951 619.9c-13.6-17.5-43.9-32.3-105.6 4.2 5.8-25.6-14.6-41.5-38.7-41.5-21.2 0-46.2 5.5-65.9 25.6-28.4-37.4-57.6-32.9-114.4 2.3-21.6 13.4-40.7 25-54.9 33-3.8 2.2-6.8 3.7-9.9 5.4 5.6 1.2 11.3 2.2 17.4 2.2 79.7 0 142.7 0 142.7 69.2 0 53.1-61.7 68.5-94.5 69.1H479.7c-60.9 0-120.4-63.5-120.4-63.5s71.4 43.4 121.4 43.4l167.3 0.7c0.3 0 49.4-14.1 48-46-1.3-30.7-20.6-51.3-112.7-51.3-60 0-121.3-27.4-121.3-27.4s-64.5-39.7-83.1-51.4c-44.2-27.5-79.4-53.8-156.4-23.4C173.7 589.9 67 639.6 67 639.6l98.3 218.2 50.2-32.5c27.8-17.7 43.1-20.7 79-2.2 34.7 17.8 141.3 73.3 168.1 86.3 81.9 39.7 117.1 4.1 156.7-18.7 33.8-19.5 273-177.9 298.1-195.7 39.2-27.7 51.5-51.9 33.6-75.1z\" fill=\"#78909C\" p-id=\"20525\"></path></svg>&nbsp;托号抢单":Request.QueryString["group"]+""=="2"?"<svg t=\"1698247093279\" class=\"icon\" viewBox=\"0 0 1024 1024\" version=\"1.1\" xmlns=\"http://www.w3.org/2000/svg\" p-id=\"35339\" width=\"25\" height=\"25\"  style=\"position:relative;top:5px;\"><path d=\"M0 0h849.17591v849.17591H0z\" fill=\"#FFFFFF\" p-id=\"35340\"></path><path d=\"M707.646591 0a141.529318 141.529318 0 0 1 141.529319 141.529318v566.117273a141.529318 141.529318 0 0 1-141.529319 141.529319H141.529318a141.529318 141.529318 0 0 1-141.529318-141.529319V141.529318a141.529318 141.529318 0 0 1 141.529318-141.529318h566.117273zM250.082305 105.439342H205.925158c0.849176 4.24588 2.264469 10.756228 3.396704 17.549636H166.580008v37.080681h9.057876l9.057876 27.173629h-27.456687v36.797623h56.328668v16.134342H163.749421v37.080681h49.81832v33.967037c0 11.322345-3.396704 13.586815-14.43599 15.002108l-3.679762 0.566117c4.811997-12.45458 9.057876-25.758336 12.171521-35.099271l-30.853391-9.623994c-5.378114 18.964929-15.568225 45.006323-23.210809 59.725373l30.570333 13.586814c2.547528-5.944231 5.944231-13.869873 9.057876-22.644691l11.605405 36.797623 14.152931-1.132235c27.739746-2.264469 33.967036-7.642583 33.967037-30.287274v-30.853391c6.510349 9.057876 14.43599 22.078574 19.814104 30.004215l18.964929-12.45458a193.682872 193.682872 0 0 1-13.020697 26.890571l41.326561 19.531046c19.531046-41.60962 24.626101-71.047718 24.626101-133.603677v-7.925641h13.586815v137.283438h40.194326v-137.283438h18.964929V184.978819H344.62389V162.051069c23.776925-1.415293 55.196434-7.642583 68.217131-12.171521l-13.586814-43.59103c-22.078574 7.359525-63.688193 13.586815-93.692409 14.43599v105.297813c0 39.062092-2.547528 63.971252-9.340935 86.049826a858.092257 858.092257 0 0 0-19.814104-28.305864l-23.493867 15.002108v-21.512457h41.326561V240.175253H252.912892v-16.134342h43.307971v-36.797623h-21.512456c3.396704-8.774818 7.359525-20.097163 9.623993-27.173629h9.907053V122.988978h-41.043503l-3.113645-17.549636z m291.267337-0.283059c-0.283059 4.811997-0.283059 15.851284-1.132234 28.022805-5.378114 93.126291-28.305864 135.585087-101.901109 191.630697l35.665388 40.477385c45.289382-36.514564 74.72748-74.444421 92.843233-117.186275 17.266577 41.043502 46.138558 77.275008 89.446529 116.054041L695.616599 323.394492c-62.839017-42.741854-100.768875-91.994057-107.562282-176.34553 0.849176-16.983518 1.415293-34.250095 1.415294-40.760444l-48.119969-1.132235zM244.138074 160.069659c-2.264469 7.925642-5.095055 18.398811-8.2087 27.173629h-11.322346L215.83221 160.069659h28.305864z\" fill=\"#016FFE\" p-id=\"35341\"></path><path d=\"M421.120487 424.587955c68.641719 0 124.758094 19.000311 128.791679 42.98953l0.070765 0.389206c75.930479 35.205418 126.350299 111.525103 151.082547 246.650219l1.167617 0.919941c1.132235 0.919941 2.229087 1.875263 3.325939 2.865969l-4.493556-3.78591 3.113645 2.58291 4.599703 4.352027 3.04288 3.255174 4.10435 5.024291 3.892057 5.448879 4.528938 7.607201 2.865969 5.555025 3.184409 7.253378 2.618293 6.864172 2.335233 7.182613 1.910646 7.005701 1.946028 8.845582c0.849176 4.24588 1.52144 8.527141 2.087558 12.843786l0.566117 4.953526 0.813794 10.260876 0.247676 5.519643a225.102381 225.102381 0 0 1-2.016793 37.646799 116.160188 116.160188 0 0 1-9.057876 31.490273l-3.396704 6.722643-2.405998 4.068968-4.316644 6.368819-5.519644 6.864172-3.962821 4.245879-5.271967 4.882762-6.22729 4.953526-4.352026 3.04288-8.279466 4.882762-8.420994 3.998203-7.217995 2.759822-5.908849 1.875263-7.147231 1.769117-5.095055 0.990705-5.625791 0.849176-3.892056 0.424588c-39.698974 39.663591-103.564079 70.693894-205.429805 75.64742l-12.419198 0.495353v0.106147h-4.352027c-109.154487-3.078263-176.557825-34.957742-217.95515-76.213538l-6.722642-0.813793-6.970319-1.238382-8.279465-2.016793-5.731938-1.769116-9.093259-3.538233-8.031788-3.998203-5.661173-3.325939-7.076466-4.882762-6.014996-4.988908-5.413496-5.236585-3.679763-4.068968-4.811997-6.085761-2.653674-3.785909-2.299852-3.750527-3.361321-6.085761a103.068726 103.068726 0 0 1-3.892056-8.8102l-2.193705-6.121143c-1.98141-6.085761-3.538233-12.737639-4.493556-19.707957l-1.238381-10.826993-0.813794-14.435991v-11.180816l0.778412-13.692961 1.167616-10.367023 0.884559-5.731937a185.403407 185.403407 0 0 1 5.307349-22.538544l2.12294-6.474966 3.078263-7.854878 3.255174-7.076465 3.18441-5.838085 4.635085-7.430289 4.245879-5.661173 3.821292-4.387409 6.014996-5.873466 3.750527-3.113645c23.883072-130.525414 71.861511-206.172834 143.68764-242.828928l-0.070764-1.132235c0-25.298366 57.779344-45.784734 129.039356-45.784734z\" fill=\"#FFFFFF\" p-id=\"35342\"></path><path d=\"M426.18016 484.101033h-3.18441c-124.899623 0.247676-217.353651 14.506755-226.871497 171.356622v34.674683c0 93.480115 103.422549 144.466052 228.463702 145.315228 125.076535-0.849176 228.463702-51.835113 228.463702-145.279845v-34.674683c-9.553229-156.956014-101.971874-171.144328-226.871497-171.392005z\" fill=\"#1B1C2D\" p-id=\"35343\"></path><path d=\"M430.249128 784.956981l1.238381 0.566118 58.557756 46.63391c0.778411 0.636882 0.849176 1.238382 0.31844 1.627587l-0.778411 0.283059-57.602432 9.340935-2.335234 0.141529-2.370616-0.141529-57.531668-9.340935c-0.990705-0.176912-1.344529-0.707647-1.06147-1.309146l0.566117-0.6015 58.451609-46.63391c0.743029-0.566117 1.662969-0.707647 2.547528-0.566118z m-5.661173-186.464876l5.696555 0.176911c44.970941 2.901351 81.096299 40.158944 84.033033 86.686708l0.176911 6.08576-0.283058 3.396704a18.611105 18.611105 0 0 1-14.71905 15.214402l-3.290556 0.283058-3.290557-0.283058a18.611105 18.611105 0 0 1-14.683667-15.214402l-0.283058-3.396704-0.247677-5.02429c-2.299851-26.395218-22.680073-47.518469-48.226115-49.889085L424.587955 636.280433l-4.847379 0.247676c-25.546042 2.370616-45.961646 23.493867-48.261498 49.889085l-0.212294 5.02429-0.283058 3.396704a18.611105 18.611105 0 0 1-14.71905 15.214402l-3.290556 0.283058-3.290557-0.283058a18.752635 18.752635 0 0 1-14.966725-18.611106c0-49.287585 37.257593-89.76497 84.209944-92.772468l5.661173-0.212294z\" fill=\"#3DFFB7\" p-id=\"35344\"></path></svg>&nbsp;新手抢单":
                                                                             
                                                                             
                                                                             Request.QueryString["order_mode"]+""=="1"?"<svg t=\"1703705403907\" class=\"icon\" viewBox=\"0 0 1024 1024\" version=\"1.1\" xmlns=\"http://www.w3.org/2000/svg\" p-id=\"4134\" width=\"25\" height=\"25\" style=\"position: relative; top: 3px;\"><path d=\"M102.4 921.6V716.8h204.8L102.4 921.6z\" fill=\"#2A44A1\" p-id=\"4135\"></path><path d=\"M204.8 102.4h614.4a102.4 102.4 0 0 1 102.4 102.4v512a102.4 102.4 0 0 1-102.4 102.4H102.4V204.8a102.4 102.4 0 0 1 102.4-102.4z\" fill=\"#3D60F6\" p-id=\"4136\"></path><path d=\"M870.4 460.8a51.2 51.2 0 0 0 51.2 51.2V409.6a51.2 51.2 0 0 0-51.2 51.2zM153.6 460.8a51.2 51.2 0 0 0-51.2-51.2v102.4a51.2 51.2 0 0 0 51.2-51.2z\" fill=\"#36C196\" p-id=\"4137\"></path><path d=\"M512 691.2a230.4 230.4 0 0 1 0-460.8 25.6 25.6 0 0 1 25.6 25.6v51.2a25.6 25.6 0 0 1-51.2 0v-23.7568A179.2 179.2 0 1 0 691.2 460.8a25.6 25.6 0 0 1 51.2 0A230.6048 230.6048 0 0 1 512 691.2z\" fill=\"#FFBA40\" p-id=\"4138\"></path><path d=\"M493.568 532.48h-71.68a11.5712 11.5712 0 0 1-11.3664-11.6736 12.0832 12.0832 0 0 1 2.9696-7.8848l42.7008-48.0256a115.3024 115.3024 0 0 0 14.0288-19.0464 31.4368 31.4368 0 0 0 4.1984-15.2576 23.4496 23.4496 0 0 0-5.5296-16.2816 18.6368 18.6368 0 0 0-14.7456-6.2464 21.6064 21.6064 0 0 0-17.2032 6.9632 22.9376 22.9376 0 0 0-5.2224 10.24 11.264 11.264 0 0 1-10.8544 8.704A11.4688 11.4688 0 0 1 409.6 419.84a44.4416 44.4416 0 0 1 3.6864-8.8064A39.7312 39.7312 0 0 1 430.08 394.8544a51.2 51.2 0 0 1 24.2688-5.7344 45.2608 45.2608 0 0 1 31.4368 10.24 36.352 36.352 0 0 1 11.5712 28.5696A50.4832 50.4832 0 0 1 491.52 450.56a131.584 131.584 0 0 1-18.944 26.4192l-33.4848 36.9664h54.4768a9.3184 9.3184 0 0 1 9.1136 9.4208 9.3184 9.3184 0 0 1-9.1136 9.1136zM597.4016 482.0992h7.7824a9.3184 9.3184 0 0 1 9.216 9.4208 9.4208 9.4208 0 0 1-9.216 9.5232h-7.7824v19.6608A11.6736 11.6736 0 0 1 585.9328 532.48a11.6736 11.6736 0 0 1-11.3664-11.776v-19.6608h-48.7424a11.264 11.264 0 0 1-10.9568-10.8544 11.4688 11.4688 0 0 1 1.7408-6.5536l52.6336-85.2992a15.36 15.36 0 0 1 12.9024-7.2704 15.5648 15.5648 0 0 1 15.2576 15.7696z m-58.6752 0h35.84v-59.0848l-1.7408 3.072z\" fill=\"#FFFFFF\" p-id=\"4139\"></path></svg>&nbsp;挂单管理":
                                                                             
                                                                             
                                                                             
                                                                             "<svg t=\"1698246952254\" class=\"icon\" viewBox=\"0 0 1024 1024\" version=\"1.1\" xmlns=\"http://www.w3.org/2000/svg\" p-id=\"32418\" width=\"25\" height=\"25\" style=\"position:relative;top:5px;\"><path d=\"M511.9488 513.8432m-453.2224 0a453.2224 453.2224 0 1 0 906.4448 0 453.2224 453.2224 0 1 0-906.4448 0Z\" fill=\"#EE4F3E\" p-id=\"32419\"></path><path d=\"M512 61.3888c-250.3168 0-453.2224 202.9056-453.2224 453.2224 0 99.7888 32.256 192 86.8864 266.8544 74.8544 54.6304 167.0656 86.8864 266.8544 86.8864 250.3168 0 453.2224-202.9056 453.2224-453.2224 0-99.7888-32.256-192-86.8864-266.8544C704 93.6448 611.7888 61.3888 512 61.3888z\" fill=\"#EA623D\" p-id=\"32420\"></path><path d=\"M755.6608 308.0704c0-72.6016-17.152-141.2096-47.5136-202.0352-59.3408-28.5696-125.8496-44.5952-196.096-44.5952-250.3168 0-453.2224 202.9056-453.2224 453.2224 0 72.6016 17.152 141.2096 47.5136 202.0352 59.3408 28.5696 125.8496 44.5952 196.096 44.5952 250.3168 0 453.2224-202.9056 453.2224-453.2224z\" fill=\"#F37140\" p-id=\"32421\"></path><path d=\"M58.7776 514.6112c0 38.912 4.9664 76.6464 14.2336 112.64a443.5968 443.5968 0 0 0 114.688 15.0528c245.7088 0 444.928-199.2192 444.928-444.928 0-43.1616-6.2464-84.7872-17.7152-124.2112-33.024-7.68-67.4816-11.776-102.8608-11.776-250.368 0-453.2736 202.9056-453.2736 453.2224z\" fill=\"#F47847\" p-id=\"32422\"></path><path d=\"M383.6416 313.2928S252.416 365.3632 187.2896 497.7152c-35.7888-70.5024-43.3664-135.0656 5.5296-188.7232 44.032-48.3328 151.7568-62.9248 190.8224 4.3008z\" fill=\"#F7F8F8\" p-id=\"32423\"></path><path d=\"M512.1536 330.5472c-176.1792 0-319.0272 142.848-319.0272 319.0272 0 163.328 122.7264 297.8816 280.9856 316.672a451.24608 451.24608 0 0 0 77.9776-0.2048c49.9712-6.2464 96.3584-24.064 136.3968-50.6368l9.8816 12.4416c23.8592-10.752 46.592-23.552 67.9936-38.0928l-19.3536-24.3712c52.224-56.832 84.1728-132.608 84.1728-215.8592 0-176.128-142.848-318.976-319.0272-318.976zM442.9824 842.6496l46.6432-157.2864-100.864-32.5632 201.7792-222.3616-39.0656 175.7184 112.7936 9.7792-221.2864 226.7136z\" fill=\"#EAD4CC\" p-id=\"32424\"></path><path d=\"M473.4464 332.9536c-145.2032 17.5616-260.3008 132.6592-277.9136 277.9136l277.9136-277.9136z\" fill=\"#F7F8F8\" p-id=\"32425\"></path><path d=\"M640.3584 313.2928s53.1968 21.1456 108.6464 70.6048l78.592-78.592c-45.9776-45.312-149.1968-57.4976-187.2384 7.9872z\" fill=\"#EFEBE8\" p-id=\"32426\"></path><path d=\"M836.7104 497.7152c35.7888-70.5024 43.3664-135.0656-5.5296-188.7232-1.1264-1.2288-2.3552-2.4576-3.5328-3.6352L749.056 383.9488c31.5904 28.2112 64 65.6896 87.6544 113.7664zM830.464 629.0432c-5.5296-87.0912-46.0288-164.7104-107.6224-218.9312l-164.352 164.352-7.0656 31.744 112.7936 9.7792-221.2864 226.7136 46.6432-157.2864-31.7952-10.24-185.1392 185.1392c3.072 3.4816 6.1952 6.8608 9.4208 10.1888l-18.5344 23.296c21.7088 14.2336 44.6976 26.6752 68.8128 37.12l9.2672-11.6736c39.424 24.9856 84.736 41.5744 133.4272 47.2064 5.632 0.4608 11.3152 0.8192 16.9984 1.024l338.432-338.432z\" fill=\"#EFE1DA\" p-id=\"32427\"></path><path d=\"M388.7104 652.8l201.7792-222.3616-32 143.9744 164.352-164.352c-56.2176-49.5104-129.8944-79.5648-210.688-79.5648-13.1072 0-26.0096 0.8704-38.7072 2.4064L195.5328 610.816c-1.536 12.6976-2.4064 25.6-2.4064 38.7072 0 80.7936 30.0544 154.4704 79.5648 210.688l185.1392-185.1392-69.12-22.272z\" fill=\"#EFEBE8\" p-id=\"32428\"></path></svg>&nbsp;抢单管理" %><svg t="1697522361289" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="5712" width="22" height="22" style="margin-left: 6px;"><path d="M245.76 286.72h552.96c124.928 0 225.28 100.352 225.28 225.28s-100.352 225.28-225.28 225.28H0V532.48c0-135.168 110.592-245.76 245.76-245.76z m133.12 348.16V401.408H348.16v178.176l-112.64-178.176H204.8V634.88h30.72v-178.176L348.16 634.88h30.72z m182.272-108.544v-24.576h-96.256v-75.776h110.592v-24.576h-141.312V634.88h143.36v-24.576h-112.64v-83.968h96.256z m100.352 28.672l-34.816-151.552h-34.816l55.296 233.472H675.84l47.104-161.792 4.096-20.48 4.096 20.48 47.104 161.792h28.672l57.344-233.472h-34.816l-32.768 151.552-4.096 30.72-6.144-30.72-40.96-151.552h-30.72l-40.96 151.552-6.144 30.72-6.144-30.72z" fill="#EE502F" p-id="5713"></path></svg></h5>
        </div>
        <div class="ibox-content">
            <%if (Request.QueryString["fid"] + "" != "" || Request.QueryString["did"] + "" != "" || Request.QueryString["pid"] + "" != "")
                {
            %>
            <a class="btn btn-success" onclick="history.go(-1)" style="margin-bottom: 18px;">返回页面</a>

            <%
                } %>

            <style>
                .filterclick-1 {
                    padding: 7px 12px;
                    border-radius: 3px;
                    font-weight: bold;
                    display: flex;
                    align-items: center;
                    margin-bottom: 10px;
                    margin-right: 10px;
                    cursor: pointer;
                    background: #eee;
                    color: #5a5b5c;
                    border: 1px solid #ddd;
                }

                    .filterclick-1.active {
                        background: linear-gradient(119deg, #607D8B, #9E9E9E);
                        color: #eee;
                        border: 1px solid transparent;
                    }
            </style>

            <div style="display: flex;">

                <div class="filterclick-1" filtervalue="storage">
                    <svg t="" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="6932" width="23" height="23" style="margin-right: 5px;">
                        <path d="M810.0864 210.7904H261.6832a56.32 56.32 0 0 0-35.4816 99.9424l204.8 166.5536a10.5472 10.5472 0 0 1 3.8912 8.192V803.84a36.4032 36.4032 0 0 0 18.2784 31.5392l113.5104 64.8704a47.0016 47.0016 0 0 0 70.3488-40.96v-373.76a10.5472 10.5472 0 0 1 3.8912-8.192l204.8-166.5536a56.32 56.32 0 0 0-35.6352-99.9936z" fill="#9FA7FF" p-id="6933"></path><path d="M845.6192 310.7328a56.832 56.832 0 0 0 15.7696-20.0192 51.2 51.2 0 0 0-47.3088-30.0544H303.8208A52.5312 52.5312 0 0 0 256 335.104l174.9504 142.1824a10.5472 10.5472 0 0 1 3.8912 8.192v1.6384l26.4192 21.4528a9.9328 9.9328 0 0 1 3.6352 7.6288v294.7584a33.9456 33.9456 0 0 0 16.9984 29.3888l105.6256 60.3648a45.4656 45.4656 0 0 0 12.1344 4.7104 46.8992 46.8992 0 0 0 37.3248-46.08v-373.76a10.5472 10.5472 0 0 1 3.8912-8.192z" fill="#8891FF" p-id="6934"></path><path d="M818.432 316.2112H350.72A48.0768 48.0768 0 0 0 303.5136 373.76l127.4368 103.5264a10.5472 10.5472 0 0 1 3.8912 8.192v9.0112l60.2112 48.9984a9.0624 9.0624 0 0 1 3.328 7.0144V819.2a30.72 30.72 0 0 0 15.36 26.88L610.7648 901.12h0.3584a46.5408 46.5408 0 0 0 25.6-41.8816v-373.76a10.5472 10.5472 0 0 1 3.8912-8.192l194.56-158.1056a47.9744 47.9744 0 0 0-16.7424-2.9696z" fill="#6E75FF" p-id="6935"></path><path d="M559.9744 937.3184a82.432 82.432 0 0 1-40.96-11.0592l-125.8496-71.68a71.1168 71.1168 0 0 1-35.84-61.44V446.6176l-220.16-178.8928A93.0816 93.0816 0 0 1 196.096 102.4H803.84a93.0816 93.0816 0 0 1 58.88 165.3248l-219.904 178.8928v407.7568a81.92 81.92 0 0 1-41.2672 71.68 82.8416 82.8416 0 0 1-41.5744 11.264zM196.096 163.84a31.6416 31.6416 0 0 0-19.968 56.32l226.9696 184.32a42.2912 42.2912 0 0 1 15.6672 32.9216v355.072a9.5744 9.5744 0 0 0 4.8128 8.2944l125.7984 71.936a21.3504 21.3504 0 0 0 32-18.5344V437.6064a42.1376 42.1376 0 0 1 15.6672-33.1264l226.9184-184.32A31.6416 31.6416 0 0 0 803.84 163.84z" fill="#2E3138" p-id="6936"></path><path d="M889.7536 527.7696h-175.7696a30.72 30.72 0 0 1 0-61.44h175.7696a30.72 30.72 0 0 1 0 61.44zM889.7536 652.2368h-175.7696a30.72 30.72 0 0 1 0-61.44h175.7696a30.72 30.72 0 0 1 0 61.44zM889.7536 773.6832h-175.7696a30.72 30.72 0 0 1 0-61.44h175.7696a30.72 30.72 0 0 1 0 61.44z" fill="#2E3138" p-id="6937"></path></svg>7日前数据
                </div>

            </div>
            <input id="filtervalue" type="hidden" />
            <script>
                $('.filterclick-1').on('click', function () {
                    filtervalue = $(this).attr('filtervalue');
                    if ($(this).hasClass("active")) {
                        $(this).removeClass("active");
                        filtervalue = "";
                    } else {
                        $(this).addClass("active").siblings().removeClass("active");
                    }
                    $('#filtervalue').val(filtervalue);
                    searchList();
                });
            </script>




            <div class="row layui-form">


                <div class="col-md-2">
                    <select id="typeid">
                        <option value="">账号类型</option>
                        <option value="user">普通账号</option>
                        <option value="super">高级账号</option>
                    </select>
                </div>


                <div class="col-md-4">
                    <div class="form-group form-buttom" style="position: relative;">
                        <input type="text" id="search-keyword" name="ordername" class="form-control" placeholder="请输入搜索的信息" style="padding-right: 50px;">
                        <a class="input_button" onclick="searchList()">查询</a>
                    </div>

                </div>


                <div class="col-md-4">
                    <div class="form-group form-buttom" id="twoInputs">
                        <input type="text" id="start_time" name="ordername" class="form-control" placeholder="开始时间" style="width: 48%; display: inline-block;" value="" autocomplete="off">
                        <input type="text" id="end_time" name="ordername" class="form-control" placeholder="结束时间" style="width: 48%; display: inline-block;" autocomplete="off">

                        <script>
                            $(document).ready(function () {
                                $('#start_time').datepicker({
                                    format: 'yyyy-mm-dd',
                                    autoclose: true
                                });

                                $('#end_time').datepicker({
                                    format: 'yyyy-mm-dd',
                                    autoclose: true
                                });
                            });
                        </script>
                    </div>
                </div>

                <div class="col-md-4">
                    <div class="form-group form-buttom">
                        <a class="btn btn-success" onclick="javascript:location.href=location.href;">
                            <svg t="1688237949108" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2211" width="16" height="16">
                                <path d="M512 192a298.24 298.24 0 0 0-245.162667 128 42.666667 42.666667 0 1 1-69.973333-48.810667 384.042667 384.042667 0 0 1 697.173333 180.48l36.608-34.645333a42.666667 42.666667 0 0 1 58.709334 61.952l-112.64 106.666667a42.666667 42.666667 0 0 1-71.253334-38.912A298.666667 298.666667 0 0 0 512 192z m0.170667 640a298.24 298.24 0 0 0 245.162666-128 42.666667 42.666667 0 1 1 69.973334 48.810667 384.042667 384.042667 0 0 1-697.173334-180.48l-36.650666 34.645333a42.666667 42.666667 0 0 1-58.666667-61.952l112.64-106.666667a42.666667 42.666667 0 0 1 71.253333 38.912 298.666667 298.666667 0 0 0 293.418667 354.730667z" fill="#14101C" p-id="2212"></path></svg>
                            刷新</a>
                        <%--<a class="btn btn-success" onclick="opennew()">
                                <svg t="1688237854283" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="15169" width="16" height="16">
                                    <path d="M725.333333 213.333333H298.666667a85.333333 85.333333 0 0 0-85.333334 85.333334v341.333333a42.666667 42.666667 0 1 1-85.333333 0V298.666667a170.666667 170.666667 0 0 1 170.666667-170.666667h426.666666a170.709333 170.709333 0 0 1 167.850667 139.733333v488.533334A170.709333 170.709333 0 0 1 725.333333 896H298.666667a170.538667 170.538667 0 0 1-144.469334-79.744l1.194667-0.682667a42.666667 42.666667 0 0 1 76.501333-37.034666c15.658667 19.584 39.765333 32.128 66.773334 32.128h426.666666a85.333333 85.333333 0 0 0 85.333334-85.333334V298.666667a85.333333 85.333333 0 0 0-85.333334-85.333334z m-213.333333 512a42.666667 42.666667 0 0 1-42.666667-42.666666v-128H341.333333a42.666667 42.666667 0 1 1 0-85.333334h128V341.333333a42.666667 42.666667 0 1 1 85.333334 0v128h128a42.666667 42.666667 0 1 1 0 85.333334h-128v128a42.666667 42.666667 0 0 1-42.666667 42.666666z" fill="#14101C" p-id="15170"></path></svg>&nbsp;添加</a>--%>

                        <a class="btn btn-success" onclick="report_exl()">
                            <svg t="1695489540076" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="5479" width="16" height="16">
                                <path d="M686.08 989.866667H165.546667c-51.2 0-92.16-40.96-92.16-92.16V126.293333c0-51.2 40.96-92.16 92.16-92.16h573.44c6.826667 0 11.946667 1.706667 17.066666 6.826667l187.733334 174.08c5.12 5.12 8.533333 11.946667 8.533333 18.773333v256c0 13.653333-11.946667 25.6-25.6 25.6s-25.6-11.946667-25.6-25.6V244.053333L728.746667 85.333333h-563.2C141.653333 85.333333 122.88 104.106667 122.88 126.293333v769.706667c0 22.186667 18.773333 40.96 40.96 40.96h520.533333c13.653333 0 25.6 11.946667 25.6 25.6S699.733333 989.866667 686.08 989.866667z" fill="#333333" p-id="5480"></path><path d="M919.893333 259.413333h-121.173333c-51.2 0-92.16-40.96-92.16-92.16V59.733333c0-13.653333 11.946667-25.6 25.6-25.6s25.6 11.946667 25.6 25.6v105.813334c0 22.186667 18.773333 40.96 40.96 40.96h121.173333c13.653333 0 25.6 11.946667 25.6 25.6s-11.946667 27.306667-25.6 27.306666zM500.053333 305.493333H208.213333c-13.653333 0-25.6-11.946667-25.6-25.6s11.946667-25.6 25.6-25.6h291.84c13.653333 0 25.6 11.946667 25.6 25.6s-11.946667 25.6-25.6 25.6zM616.106667 575.146667H208.213333c-13.653333 0-25.6-11.946667-25.6-25.6s11.946667-25.6 25.6-25.6h407.893334c13.653333 0 25.6 11.946667 25.6 25.6s-11.946667 25.6-25.6 25.6zM380.586667 440.32H208.213333c-13.653333 0-25.6-11.946667-25.6-25.6s11.946667-25.6 25.6-25.6h172.373334c13.653333 0 25.6 11.946667 25.6 25.6s-11.946667 25.6-25.6 25.6z" fill="#333333" p-id="5481"></path><path d="M914.773333 779.946667H600.746667c-13.653333 0-25.6-11.946667-25.6-25.6s11.946667-25.6 25.6-25.6h314.026666c13.653333 0 25.6 11.946667 25.6 25.6s-11.946667 25.6-25.6 25.6z" fill="#333333" p-id="5482"></path><path d="M831.146667 873.813333c-6.826667 0-13.653333-1.706667-18.773334-6.826666-10.24-10.24-10.24-25.6 0-35.84l75.093334-75.093334-75.093334-75.093333c-10.24-10.24-10.24-25.6 0-35.84 10.24-10.24 25.6-10.24 35.84 0l93.866667 93.866667c5.12 5.12 6.826667 11.946667 6.826667 18.773333s-3.413333 13.653333-6.826667 18.773333L848.213333 870.4c-5.12 1.706667-11.946667 3.413333-17.066666 3.413333z" fill="#333333" p-id="5483"></path><path d="M694.613333 824.32h-529.066666c-37.546667 0-68.266667-30.72-68.266667-68.266667V121.173333c0-37.546667 30.72-68.266667 68.266667-68.266666h529.066666c37.546667 0 68.266667 30.72 68.266667 68.266666v634.88c0 37.546667-30.72 68.266667-68.266667 68.266667z" fill="#333333" opacity=".3" p-id="5484"></path></svg>导出</a>

                    </div>
                </div>



            </div>


            <div style="display: flex; flex-wrap: wrap; margin-bottom: 20px;">
                <div class="timesel_lab">全部</div>
                <div class="timesel_lab">今天</div>
                <div class="timesel_lab">昨天</div>
                <div class="timesel_lab">本周</div>
                <div class="timesel_lab">上周</div>
                <div class="timesel_lab">本月</div>
                <div class="timesel_lab">上月</div>
            </div>


            <div>
                <input type="checkbox" onclick="set_autoAlarm(this)" />&nbsp;自动监控订单（5s刷新一次）


                <div style="display: inline-block; font-weight: bold; background: #e5ded887; padding: 2px 18px; border-radius: 5px; color: #000; font-size: 20px;">
                    <span class="total_amount">0</span> 总金额<span style="font-size: 18px; background: linear-gradient(152deg, #ff5412d4, #f3cdbe); color: #fff8e0; border-radius: 6px; font-weight: 100; padding: 2px 9px; margin-left: 5px; font-weight: bold;"><span class="total_number">0</span> 总量</span><span style="font-size: 18px; background: linear-gradient(152deg, #1e99a5d4, #d77750); color: #fff8e0; border-radius: 6px; font-weight: 100; padding: 2px 9px; margin-left: 5px; font-weight: bold;"><span class="total_users">0</span> 人</span>
                </div>

            </div>


            <br />


            <div class="table-responsive">
                <table class="table">
                    <thead>
                        <tr role="head">
                            <th sort="true" class="action-cell-left">
                                <input type="checkbox" onclick="javascript: select_all(this)" />#ID</th>
                            <th sort="true" class="">锁定人</th>
                            <th sort="true" class="">操作人</th>
                            <th sort="true" class="">订单号</th>
                            <th sort="true" class="">历史佣金</th>
                            <th sort="true" class="">账号</th>
                            <th sort="true" class="">受邀人</th>
                            <th sort="true" class="">订单状态</th>
                            <th sort="true" class="">成功率</th>
                            <th sort="true" class="">支付方式</th>
                            <th sort="true" class="">订单金额</th>
                            <th sort="true" class="">商家姓名</th>
                            <th sort="true" class="">出款方式</th>
                            <th sort="true" class="">商品图片</th>
                            <th sort="true" class="">商品标题</th>
                            <th sort="true" class="">时间</th>
                            <th sort="true" class="">评价</th>
                            <th sort="true" class="">卡号推送</th>
                            <th sort="true" class="">状态推送</th>
                        </tr>
                    </thead>
                    <tbody id="list-display">
                    </tbody>
                </table>
            </div>


            <div class="layui-table-page">
                <div id="layui-table-page1">
                    <div class="layui-box layui-laypage layui-laypage-default" id="layui-laypage-1">
                        <div style="display: inline-block;" class="cpages">
                            <!--page grow -->
                        </div>
                        <span class="layui-laypage-skip">到第<input type="text" min="1" value="1" class="layui-input target-page" />页
                            <button type="button" class="layui-laypage-btn" onclick="javascript:pager_index=$('.target-page').val()-1;getPager();">确定</button></span>
                        <span class="layui-laypage-count">共 <span class="page_count"></span>条</span>
                        <span class="layui-laypage-limits">
                            <select class="s_pages" lay-ignore="" onchange="javascript:pager_size=$(this).val();getPager();">
                                <option value="10">10 条/页</option>
                                <option value="20" selected="">20 条/页</option>
                                <option value="30">30 条/页</option>
                                <option value="50">50 条/页</option>
                                <option value="100">100 条/页</option>
                                <option value="200">200 条/页</option>
                            </select></span>
                    </div>
                </div>
            </div>

        </div>
    </div>


    <script>
        var _action = 'transport_orders';
        var _actionName = '';
    </script>
    <script id="item_classList" type="text/javascript">
        <%=ToJson(chelper.gdt("item_classList")) %>
    </script>
    <script>
        var item_classList = JSON.parse($('#item_classList').html());
        var classList = [];
        for (var i = 0; i < item_classList.length; i++) {
            classList.push([item_classList[i].id, item_classList[i].name]);
        }

        function opennew() {
            var modify_model = [];


            modify_model.push({ name: '选择分类', id: 'classId', data: classList, type: 'select' });
            modify_model.push({ name: '生成数量', id: 'number', value: '' });

            edit({ aname: 'admin', title: '添加' + _actionName, action: _action, id: '', data: modify_model });
        }
    </script>

    <noscript id="payer_ways"><%=uConfig.stcdata("payer_ways").Replace(" ", "") %></noscript>
    <script>
        var payer_ways_list = $('#payer_ways').html().split('\n');
        var payerWays = [];
        for (var i = 0; i < payer_ways_list.length; i++) {
            payerWays.push([payer_ways_list[i]]);
        }


        var toBuyListWays = [];
        toBuyListWays.push(['支付宝']);
        toBuyListWays.push(['网银']);
    </script>

    <script>

        var current_json = [];
        var pager_size = $(".s_pages").val();
        var pager_index = 0;
        var pager_key = "";

        $("#pagesize").val(pager_key);
        function setPageSize(obj) {
            pager_index = 0;
            pager_key = $("#search-keyword").val();
            opt = $(obj).val();
            getPager();
        }

        function searchList() {
            opt = $("#opt1").val();
            pager_index = 0;
            pager_key = $("#search-keyword").val();
            getPager();
        }
        function getParam() {
            return { limit: pager_size, p: pager_index, page: _action, keyword: pager_key, audit_state: $("#audit_state").val(), settle_state: $("#settle_state").val(), start_time: $("#start_time").val(), end_time: $("#end_time").val(), tarea: "<%=Request.QueryString["area"] %>", state: "<%=Request.QueryString["state"] %>", group: "<%=Request.QueryString["group"] %>", evaluate: "<%=Request.QueryString["evaluate"] %>", type: $("#typeid").val(), order_mode: "<%=Request.QueryString["order_mode"] %>", filtervalue: $('#filtervalue').val(), order_type: "<%=Request.QueryString["order_type"] %>", fid: '<%=Request.QueryString["fid"] %>', did: '<%=Request.QueryString["did"] %>', pid: '<%=Request.QueryString["pid"] %>', newuser: "<%=Request.QueryString["newuser"] %>" }
        }

        function getPager() {

            $.ajax({
                type: "POST",
                url: "../api/admin.aspx?do=page",
                data: getParam(),
                datatype: "json",
                success: function (json) {
                    $("#list-display").html("");
                    if (json.code == 1) {
                        var data = json.data.list;
                        current_json = data;
                        for (var i = 0; i < data.length; i++) {
                            var tr = "";
                            tr += "<tr style='" + (data[i].orderLock == 1 ? "background:#f5f5f5;" : data[i].order_type == 'ot' ? "background: linear-gradient(156deg, #eef1e8, #e8f1f1);" : "") + "'>";
                            tr += ('<td class="action-cell-left"><input class="good_select" type="checkbox" data-id="' + data[i].id + '" id="checkid_' + data[i].id + '" />#' + data[i].id + '</td>');
                            tr += ("<td>" + '<span class="button-style style-hs">' + data[i].lock_user + '</span>' + "</td>");
                            tr += ("<td style='display:flex;'>" + (data[i].confirm_from == 'user' ? '<span class="button-style style-lvs">用户</span>' : data[i].confirm_from == 'ot' ? '<span class="button-style style-zs">一键任务</span>' : data[i].confirm_from == 'admin' ? '<span class="button-style">管理员</span>' : '') + "</td>");
                            tr += ("<td class='' style='padding-bottom:0px;'>" + '<b style="color:#000;">' + data[i].sd_orderNo + '</b>' + (data[i].order_mode == 1 ? '<span style="background: #f3ecec;color: #df4445;font-size: 12px;border-radius: 3px;padding: 1px 4px;font-weight: bold;border: 1px solid #dfcdcd;margin-left: 5px;">挂单</span>' : '') + (data[i].state == 1000 && data[i].cancel_apply == 1 ? '<span style="background: #f1efec;color: #514d4d;font-size: 12px;border-radius: 3px;padding: 1px 4px;font-weight: bold;border: 1px solid #dfcdcd;margin-left: 5px;">申请取消中</span>' : '')


                                //// 新手发布到大厅按钮
                                + (data[i].state == 1000 && data[i].buy_orderId == '' && data[i].sysOrderNo == '' ? (1 == 1 ? '<span style="background: linear-gradient(139deg, #286ed7, #4ddbcf);color: #eee;padding: 3px 10px;border-radius: 3px;margin-left: 10px;font-size: 12px;margin-bottom: 13px;display: inline-block;cursor:pointer;" onclick="' + "get_action({name:'pub_dating',data:{id:" + data[i].id + ",group:" + get_param('group') + "},n:'任务转买币大厅',e:'将订单 " + data[i].sd_orderNo + " 发布到买币大厅<br><font color=black>【订单金额】：" + data[i].orderAmt + "</font><br>【收款银行】：" + data[i].payment_bankname + "<br>【收款卡号】：" + data[i].payment_bankid + "<br>【收款姓名】：" + data[i].payment_name + "',selects:[{id:'toBuyListWays',name:'发布类型',list:" + JSON.stringify(toBuyListWays).replace(/"/g, "'") + "}],inputs:[{id:'p1',name:'赠送比例',value:'" + json.sdzdt_p1 + "'},{id:'show_user',name:'可见用户',value:'" + json.sdzdt_show_user + "'}],textareas:[{id:'unshow_users',name:'不可见用户',value:''}]});" + '"><svg t="" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="9383" width="12" height="12" style="    margin-right: 3px;"><path d="M883.136 161.941333a27.413333 27.413333 0 0 0-21.333333-21.333333 472.448 472.448 0 0 0-486.4 179.2 29.312 29.312 0 0 1-17.066667 10.666667 369.877333 369.877333 0 0 0-172.8 98.133333 407.978667 407.978667 0 0 0-57.6 74.666667 27.946667 27.946667 0 0 0 10.666667 38.4 30.229333 30.229333 0 0 0 23.466666 2.133333 393.088 393.088 0 0 1 110.933334-21.333333 30.4 30.4 0 0 1 25.6 12.8 986.688 986.688 0 0 0 85.333333 102.4 986.688 986.688 0 0 0 102.4 85.333333 30.4 30.4 0 0 1 12.8 25.6 393.088 393.088 0 0 1-21.333333 110.933333 26.922667 26.922667 0 0 0 17.066666 36.266667 29.354667 29.354667 0 0 0 23.466667-2.133333 496.448 496.448 0 0 0 74.666667-57.6 377.92 377.92 0 0 0 98.133333-172.8 46.442667 46.442667 0 0 1 10.666667-17.066667 466.602667 466.602667 0 0 0 181.333333-484.266667z m-192 305.066667a95.082667 95.082667 0 0 1-145.066667-121.6 95.189333 95.189333 0 1 1 145.066667 121.6z" fill="#ffffff" p-id="9384"></path><path d="M268.736 667.541333c-19.2 19.2-104.533333 145.066667-81.066667 168.533334s149.333333-61.866667 168.533334-81.066667a62.144 62.144 0 0 0-17.066667-87.466667 60.906667 60.906667 0 0 0-70.4 0z" fill="#ffffff" p-id="9385"></path></svg>发布到大厅</span>' : '') : '') + (data[i].buy_orderId != "" ? '<div style="font-size: 12px;background: #eef5f4;color: #29710c;padding: 1px 6px;border-radius: 5px;margin: 3px 0;font-weight: bold;border: 1px solid #55af30;">买单：' + data[i].buy_orderId + '</div>' : '')


                                // 挂单（重新挂单）按钮
                                + (data[i].state == 1000 && data[i].order_mode == 1 && (data[i].state_code == '92' || data[i].state_code == '99') ? '<span style="background: linear-gradient(139deg, #f74dcb, #dda483);color: #eee;padding: 3px 10px;border-radius: 3px;margin-left: 10px;font-size: 12px;margin-bottom: 13px;display: inline-block;cursor:pointer;font-weight: bold;text-shadow: 5px 5px 5px #********;" onclick="' + "get_action({name:'tranport_repub',data:{id:" + data[i].id + "},n:'重新挂单（子订单号将被刷新）',e:'将订单 " + data[i].sd_orderNo + " 重新挂单<br><font color=black>【订单金额】：" + data[i].orderAmt + "</font><br>【收款银行】：" + data[i].payment_bankname + "<br>【收款卡号】：" + data[i].payment_bankid + "<br>【收款姓名】：" + data[i].payment_name + "'});" + '">重新挂单</span>' : '')


                                // 挂单（取消确认）按钮
                                + (data[i].state == 1000 && data[i].cancel_apply == 1 && (data[i].state_code == '92' || data[i].state_code == '99') ? '<span style="background: linear-gradient(139deg, #252222, #67ab9f);color: yellow;padding: 3px 10px;border-radius: 3px;margin-left: 10px;font-size: 12px;margin-bottom: 13px;display: inline-block;cursor:pointer;font-weight: bold;text-shadow: 5px 5px 5px #********;" onclick="' + "get_action({name:'tranport_cancel',data:{id:" + data[i].id + "},n:'挂单取消',e:'将订单 " + data[i].sd_orderNo + " 交易关闭并返还金额<br><font  color=red>请核对订单！！！！！！</font><br>---------------------<br><font color=black>【订单金额】：" + data[i].orderAmt + "</font><br>【收款银行】：" + data[i].payment_bankname + "<br>【收款卡号】：" + data[i].payment_bankid + "<br>【收款姓名】：" + data[i].payment_name + "'});" + '">确认取消</span>' : '')



                                + '<div style="font-size: 12px;color:#aaabac;">订单号：' + data[i].orderNo + '</div><div style="    font-size: 12px;">三方订单：' + data[i].other_orderNo
                                + (data[i].other_orderNo == "-" ? '<span style="background: #58595b;color: #eee;padding: 1px 10px;border-radius: 3px;margin-left: 10px;">系统生成</span>' : '')
                                + (data[i].sysOrderNo == '' ? '' : '</div><div style="font-size: 12px;color: #9a2bbd;text-shadow: 2px 2px 2px #0000002b;">对接订单：' + data[i].sysOrderNo + '</div>')
                                + (data[i].order_type != 'ot' ? '' : '</div><div style="font-size: 12px;color: gray;">任务单[ ' + data[i].fid + ' ]  父单[ ' + data[i].did + ' ]</div>')
                                + (data[i].state == 2 && data[i].userid == '' ? '<div style="    font-size: 12px;color:#79620e;">未接单时间：' + data[i].create_time + '</div>' : '') + "</td>");
                            tr += ("<td class=''>" + parseFloat(data[i].reward_amount).toFixed(2) + "</td>");
                            tr += ("<td class='' " + ' ondblclick="openrightBoxpopup(\'accounts_baseinfo.aspx?id=' + data[i].userid + '\')" style="cursor:pointer;"' + ">" + ('<span style="background: #f3ecec;color: #956b0a;font-size: 12px;border-radius: 3px;padding: 1px 4px;margin-right: 5px;font-weight: bold;border: 1px solid #ccc;">' + (data[i].typename == 'th' ? '托号' : data[i].typename == 'user' ? '普通' : data[i].typename == 'super' ? '高级' : data[i].typename == 'reg' ? '新' : '其他') + '</span>') + (data[i].group_name == '-' ? '' : "<span class='button-style style-js'>" + data[i].group_name + "</span>") + data[i].phone + '<span style="color: #b53d10;margin-left: 2px;">[' + data[i].userid + ']</span>' + "</td>");


                            tr += ("<td class='' " + ' ondblclick="openrightBoxpopup(\'accounts_baseinfo.aspx?id=' + data[i].parent_id + '\')" style="cursor:pointer;"' + ">" + ('<span style="background: #f3ecec;color: #956b0a;font-size: 12px;border-radius: 3px;padding: 1px 4px;margin-right: 5px;font-weight: bold;border: 1px solid #ccc;">' + (data[i].parent_typename == 'th' ? '托号' : data[i].parent_typename == 'user' ? '普通' : data[i].parent_typename == 'super' ? '高级' : data[i].parent_typename == 'reg' ? '新' : '其他') + '</span>') + data[i].parent_phone + '<span style="color: #b53d10;margin-left: 2px;">[' + data[i].parent_id + ']</span>' + "</td>");

                            tr += ("<td style='display:flex;'>" + (data[i].state == 0 ? '<span class="button-style">未匹配</span>' : data[i].state == 1000 ? (data[i].orderLock == 1 ? '<span class="button-style">锁定待确认 <span style="color:#a11313;">@' + data[i].lock_user + '</span></span>' : '<span class="button-style style-ls">交易中</span>') : (data[i].state == 1) ? (data[i].user_confirm_time == "" ? '<span class="button-style style-lvs">已确认</span>' : '<span class="button-style style-lvs">用户已确认</span>') : data[i].state == 2 ? '<span class="button-style style-hs">订单超时</span>' : data[i].state == -1 ? '<span class="button-style style-hs">取消订单</span>' : data[i].state == -999 ? '<span style="color:gray;">取消待审核</span>' : data[i].state == 20 ? '<span class="button-style style-hs">超时待确认</span>' + (data[i].release_order == 1 ? '<div style="border-radius: 10px;font-size: 12px;font-weight: bold;margin-top: 4px;display: inline-block;padding: 0 5px;background: #212020;color: #c3ffaf;margin-left: 6px;">申请解冻</div>' : '') : '<span class="button-style">其他</span>')




                                + (
                                    data[i].state == 1000 ?

                                        //'<a style="display: flex;align-items: center;background: #eee;padding: 0px 8px;margin-left: 6px;line-height: 19px;color: #896b18;" onclick="' + "get_action({name:'cancel_order',data:{id:" + data[i].id + "},n:'撤销订单',e:'撤销订单 " + data[i].orderId + " 的交易<br>【订单金额】：" + data[i].orderAmt + "'});" + '"><svg t="1693065317535" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="6520" width="16" height="16"><path d="M596.2752 656.9472m-179.0976 0a179.0976 179.0976 0 1 0 358.1952 0 179.0976 179.0976 0 1 0-358.1952 0Z" fill="#FFBE0A" p-id="6521"></path><path d="M515.584 923.4432c-226.8672 0-411.4432-184.576-411.4432-411.4432s184.576-411.4432 411.4432-411.4432 411.4432 184.576 411.4432 411.4432-184.576 411.4432-411.4432 411.4432z m0-751.2064c-187.3408 0-339.7632 152.4224-339.7632 339.7632s152.4224 339.7632 339.7632 339.7632 339.7632-152.4224 339.7632-339.7632-152.4224-339.7632-339.7632-339.7632z" fill="#34332E" p-id="6522"></path><path d="M687.7184 542.72H347.1872c-19.8144 0-35.84-16.0256-35.84-35.84s16.0256-35.84 35.84-35.84h340.5312c19.8144 0 35.84 16.0256 35.84 35.84s-16.0256 35.84-35.84 35.84z" fill="#34332E" p-id="6523"></path></svg>&nbsp;撤销</a>' +

                                        (data[i].state == 1000 && data[i].orderLock == 1 ? '&nbsp;<a style="display: flex;align-items: center;background: #eee;padding: 0px 8px;margin-left: 6px;line-height: 19px;color: #896b18;" onclick="' + "get_action({name:'finish_transorder',data:{id:" + data[i].id + ",state:1,group:" + (get_param('group') || "''") + "},n:'确认交易',e:'确认 " + data[i].sd_orderNo + " 的交易<br>【订单金额】：" + data[i].orderAmt + "（确认将完成订单）',selects:[{id:'payer_way',name:'出款方式',list:" + JSON.stringify(payerWays).replace(/"/g, "'") + "}]});" + '"><svg t="1693065904309" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="6904" width="16" height="16"><path d="M608.256 565.3504m-179.0976 0a179.0976 179.0976 0 1 0 358.1952 0 179.0976 179.0976 0 1 0-358.1952 0Z" fill="#FFBE0A" p-id="6905"></path><path d="M635.5968 921.4464c-49.0496 0-92.9792-25.7536-117.4016-69.9392l-116.3264-210.3808-231.1168-103.1168C125.4912 517.8368 98.3552 474.4704 99.9424 424.96c1.5872-49.5616 31.4368-91.0848 77.9264-108.3392L730.624 111.2576a134.94784 134.94784 0 0 1 137.728 26.3168c37.5808 33.8432 53.0432 85.2992 40.448 134.2976l-141.1072 547.9424c-14.1312 54.8352-57.8048 93.3376-113.9712 100.5056-6.144 0.7168-12.1856 1.1264-18.1248 1.1264zM202.8544 383.7952c-28.0576 10.4448-31.0272 35.8912-31.232 43.4688-0.256 7.5776 1.0752 33.1776 28.416 45.3632l242.0736 108.032c7.1168 3.1744 13.0048 8.5504 16.7424 15.4112l122.112 220.8768c13.1072 23.6544 36.9152 35.7888 63.744 32.3584 26.8288-3.4304 46.848-21.0944 53.6064-47.2576L839.3728 253.952c6.0416-23.3984-1.0752-47.0016-19.0464-63.1296-17.92-16.1792-42.1376-20.7872-64.768-12.3904L202.8544 383.7952z" fill="#34332E" p-id="6906"></path><path d="M532.224 529.6128c-9.0624 0-18.176-3.4304-25.1392-10.2912a35.87584 35.87584 0 0 1-0.4096-50.688l152.064-154.4704a35.82976 35.82976 0 0 1 50.688-0.4096 35.87584 35.87584 0 0 1 0.4096 50.688l-152.064 154.4704a35.57376 35.57376 0 0 1-25.5488 10.7008z" fill="#34332E" p-id="6907"></path></svg>&nbsp;确认</a>'
                                            +
                                            '<a style="display: flex;align-items: center;background: #eee;padding: 0px 8px;margin-left: 6px;line-height: 19px;color: #896b18;" onclick="' + "get_action({name:'cancel_order',data:{id:" + data[i].id + ",state:-1,group:" + (get_param('group') || "''") + "},n:'撤销订单',e:'撤销订单 " + data[i].sd_orderNo + " 的交易<br>【订单金额】：" + data[i].orderAmt + "'});" + '"><svg t="1693065317535" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="6520" width="16" height="16"><path d="M596.2752 656.9472m-179.0976 0a179.0976 179.0976 0 1 0 358.1952 0 179.0976 179.0976 0 1 0-358.1952 0Z" fill="#FFBE0A" p-id="6521"></path><path d="M515.584 923.4432c-226.8672 0-411.4432-184.576-411.4432-411.4432s184.576-411.4432 411.4432-411.4432 411.4432 184.576 411.4432 411.4432-184.576 411.4432-411.4432 411.4432z m0-751.2064c-187.3408 0-339.7632 152.4224-339.7632 339.7632s152.4224 339.7632 339.7632 339.7632 339.7632-152.4224 339.7632-339.7632-152.4224-339.7632-339.7632-339.7632z" fill="#34332E" p-id="6522"></path><path d="M687.7184 542.72H347.1872c-19.8144 0-35.84-16.0256-35.84-35.84s16.0256-35.84 35.84-35.84h340.5312c19.8144 0 35.84 16.0256 35.84 35.84s-16.0256 35.84-35.84 35.84z" fill="#34332E" p-id="6523"></path></svg>&nbsp;撤销</a>'

                                            : '')


                                        + (data[i].state == 1000 && data[i].orderLock != 1 ? '&nbsp;<a style="display: flex;align-items: center;padding: 0px 8px;margin-left: 6px;line-height: 19px;color: #5a5a5c;border: 1px solid #5a5a5c;" onclick="' + "get_action({name:'lock_transorder',data:{id:" + data[i].id + ",group:" + get_param('group') + "},n:'锁定交易',e:'锁定 " + data[i].sd_orderNo + " 的交易<br>【订单金额】：" + data[i].orderAmt + "'});" + '"><svg t="*************" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="7884" width="16" height="16"><path d="M500.224 694.528c-56.576 0-102.912-46.08-102.912-102.912a102.912 102.912 0 0 1 205.824 0c0 56.832-46.336 102.912-102.912 102.912z m0-146.944a44.032 44.032 0 1 0 0 88.064 44.032 44.032 0 0 0 0-88.064z" fill="#005CFF" p-id="7885"></path><path d="M733.44 318.72h-84.224c-1.28-77.056-64-139.264-141.312-139.264-77.312 0-140.032 62.208-141.312 139.264h-84.224c-64.512 0-116.992 52.48-116.992 116.992V747.52c0 64.512 52.48 116.992 116.992 116.992h450.816c64.512 0 116.992-52.48 116.992-116.992V435.712a116.736 116.736 0 0 0-116.736-116.992z m-225.536-80.384c44.8 0 81.408 35.84 82.432 80.384h-165.12c1.536-44.544 37.888-80.384 82.688-80.384zM791.808 747.52c0 32.256-26.112 58.368-58.368 58.368H282.624c-32.256 0-58.368-26.112-58.368-58.368V435.712c0-32.256 26.112-58.368 58.368-58.368h450.816c32.256 0 58.368 26.112 58.368 58.368V747.52z" fill="#1B1F28" p-id="7886"></path></svg>&nbsp;锁定</a>' : '')





                                        : data[i].state == 20 ? '&nbsp;<a style="display: flex;align-items: center;background: #eee;padding: 0px 8px;margin-left: 6px;line-height: 19px;color: #896b18;" onclick="' + "get_action({name:'ser_transportState',data:{id:" + data[i].id + ",state:2,group:" + get_param('group') + "},n:'超时确认',e:'确认 " + data[i].sd_orderNo + " 交易超时<br>确认后冻结金额" + data[i].orderAmt + "将<font color=blue>转入用户账户中</font>'});" + '"><svg t="1697608931909" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="4202" width="16" height="16"><path d="M592.7424 654.7968m-179.0976 0a179.0976 179.0976 0 1 0 358.1952 0 179.0976 179.0976 0 1 0-358.1952 0Z" fill="#FFBE0A" p-id="4203"></path><path d="M512.0512 922.5216c-226.8672 0-411.4432-184.576-411.4432-411.4432s184.576-411.4432 411.4432-411.4432 411.4432 184.576 411.4432 411.4432-184.576 411.4432-411.4432 411.4432z m0-751.2064c-187.3408 0-339.7632 152.4224-339.7632 339.7632s152.4224 339.7632 339.7632 339.7632 339.7632-152.4224 339.7632-339.7632-152.4224-339.7632-339.7632-339.7632z" fill="#34332E" p-id="4204"></path><path d="M679.7824 576.1024h-184.32c-19.8144 0-35.84-16.0256-35.84-35.84V348.1088c0-19.8144 16.0256-35.84 35.84-35.84s35.84 16.0256 35.84 35.84v156.3136h148.48c19.8144 0 35.84 16.0256 35.84 35.84s-16.0256 35.84-35.84 35.84z" fill="#34332E" p-id="4205"></path></svg>&nbsp;已超时</a>'

                                            + '<a style="display: flex;align-items: center;background: #eee;padding: 0px 8px;margin-left: 6px;line-height: 19px;color: #896b18;" onclick="' + "get_action({name:'ser_transportState',data:{id:" + data[i].id + ",state:1,group:" + get_param('group') + "},n:'用户已转账',e:'订单号：" + data[i].sd_orderNo + " 的交易用户已转账<br>将扣除用户冻结资金" + data[i].orderAmt + "<font color=blue>【用户超时未确认请给予警告】</font>'});" + '"><svg t="1697608852326" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="3775" width="16" height="16"><path d="M670.6176 715.2128m-179.0976 0a179.0976 179.0976 0 1 0 358.1952 0 179.0976 179.0976 0 1 0-358.1952 0Z" fill="#FFBE0A" p-id="3776"></path><path d="M695.1424 311.7056c0-121.1392-98.56-219.7504-219.7504-219.7504S255.6928 190.5664 255.6928 311.7056c0 69.632 32.6144 131.7888 83.3024 172.0832-135.9872 52.224-232.8064 184.1152-232.8064 338.2272 0 41.8816 34.048 75.9296 75.9296 75.9296h260.5568c19.8144 0 35.84-16.0256 35.84-35.84s-16.0256-35.84-35.84-35.84H182.1184c-2.3552 0-4.2496-1.8944-4.2496-4.2496 0-160.2048 130.3552-290.56 290.56-290.56h13.9264c1.4848 0 2.9184-0.1024 4.352-0.3072 115.968-5.888 208.4352-102.0416 208.4352-219.4432zM475.4432 459.776c-81.664 0-148.0704-66.4064-148.0704-148.0704s66.4064-148.0704 148.0704-148.0704 148.0704 66.4064 148.0704 148.0704S557.056 459.776 475.4432 459.776zM714.2912 833.2288c-9.1136 0-17.8688-3.4304-24.5248-9.728l-86.272-81.0496c-14.4384-13.568-15.1552-36.2496-1.5872-50.6368 13.568-14.4384 36.2496-15.1552 50.6368-1.5872l58.1632 54.6304 134.5536-166.0928c12.4416-15.36 35.0208-17.7664 50.432-5.2736 15.36 12.4416 17.7664 35.0208 5.2736 50.432l-158.8224 196.096a35.76832 35.76832 0 0 1-25.3952 13.2096h-2.4576z" fill="#34332E" p-id="3777"></path></svg>&nbsp;已收款</a>' : '') + "</td>");
                            tr += ("<td class='color:gray;'>" + (data[i].userid == "" ? "" : (data[i].order_total == 0 ? '当日未刷' : (((data[i].order_success / data[i].order_total) * 100).toFixed(1)) + '% ' + '<span>' + data[i].order_total + '单</span>')) + "</td>");
                            tr += ("<td class=''>" + '<span class="button-style style-zs" style="cursor:pointer;" onclick="show_details(' + data[i].id + ')">' + data[i].payment_type + '</span>' + "</td>");
                            tr += ("<td>" + '<span style="font-weight: bold;font-size: 13px;text-shadow: 5px 5px 5px #af4f897d;color: #af4f76;">' + data[i].orderAmt + '</span>' + ' <span style="margin-left: 2px; color: #5a5b5c; font-size: 12px;">(' + (data[i].is_double == 1 ? '<span style="    color: #ee7d25;    font-weight: bold;    font-size: 15px;    margin: 0 3px;">+' + parseFloat(data[i].award_amount * 2).toFixed(2) + '</span>' : '+' + parseFloat(data[i].award_amount - (data[i].serve_fee == "" || data[i].serve_fee == 0 ? 0 : data[i].serve_fee)).toFixed(2)) + '佣,' + '' + data[i].serve_fee + ',房佣' + data[i].rate_award + ')</span>' + (data[i].is_double == 1 ? '<div><span style="display: inline-block;color: #ee7d25;font-weight: bold;background: #000000e3;font-size: 12px;padding: 2px 10px;border-radius: 5px;margin-top: 5px;box-shadow: 2px 2px 8px #ee7d25;">        <svg t="1701338602094" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="9074" width="12" height="12" style="margin-right: 3px;">            <path d="M859.995 460.963c-1.197 10.478-1.197 21.854 0 33.527v88.305c0 160.751-110.161 296.657-258.34 336.17v-52.982c0-49.693-40.112-89.805-89.505-89.805h-0.3c-49.394 0-89.805 40.112-89.805 89.805v52.982c-148.179-39.812-258.04-175.719-258.04-336.17V431.328c39.814 32.33 93.098 91.302 89.805 172.726 33.227-153.267 99.085-176.318 154.764-239.48 72.444-82.321 93.996-172.726 52.386-259.538C641.17 167.6 687.868 409.476 672.901 536.7c64.959-115.251 146.682-146.083 187.094-154.166v78.429z" fill="#FF7B15" p-id="9075"></path></svg>双倍收益</span></div>' : '') + (data[i].exp_amount != '' && data[i].exp_amount != 0 ? '<div><span style="background: linear-gradient(83deg, #D6E9EA, #d6dcf7, #b0f9e2);font-weight: bold;text-align: center;padding: 0px 8px;border-radius: 4px;color: #2a2b2c;margin-top: 3px;margin-left: 39px;display: inline-block;">体验金 ' + parseFloat(data[i].exp_amount).toFixed(2) + '</span></div>' : '')
                                +`<div style="font-size: 17px;font-weight: bold;">
    <span>回款金额</span>
    <span>≈${cny((parseFloat(data[i].orderAmt) - (data[i].serve_fee == '' || data[i].serve_fee == 0 ? '' : parseFloat(data[i].serve_fee))).toFixed(2)) }CY</span>
</div>`

                                + "</td>");
                            tr += ("<td class=''>" + data[i].payerName + "</td>");
                            tr += ("<td class=''>" + data[i].orderType + "</td>");
                            tr += ("<td class=''>" + '<img src="' + data[i].imgurl + '" width="25" height="25">' + "</td>");
                            tr += ("<td class='' style='max-width:80px;'>" + data[i].title + "</td>");
                            tr += ("<td class=''>"
                                + (data[i].receive_time == "" ? "" : "<div>任务 " + data[i].receive_time + "</div>")
                                + (data[i].timeout_time == "" ? "" : "<div class='expire_time'  data-time='" + data[i].timeout_time + "'>退回 " + data[i].timeout_time + "</div>" + (data[i].timeout_second == "" ? "<span style='color:gray;'>无</span>" : "<span class='exp_countdown'></span>" + '<span style="color:#000;margin-left:10px;">' + data[i].timeout_second + "秒</span>"))
                                + (data[i].finish_time == "" ? "" : "<div style='color:black;margin-top:2px;'>完成 " + data[i].finish_time + "</div>")
                                + (data[i].evaluate_time == "" ? "" : "<div style='color:#c38b09;margin-top:2px;'>评价 " + data[i].evaluate_time + "</div>") + "</td>");
                            tr += ("<td class=''>" + (data[i].star_wlfw == "" ? '<span style="color:gray;">未评价</span>' : '<div>物流&nbsp;' + get_star(data[i].star_wlfw) + '</div>' + '<div>发货&nbsp;' + get_star(data[i].star_fhsd) + '</div>' + '<div>服务&nbsp;' + get_star(data[i].star_fwtd) + '</div>' + '<div style="color:gray;max-width: 100px;overflow: hidden;text-overflow: ellipsis;" title="' + data[i].evaluate_text + '">' + data[i].evaluate_text + '</div>') + "</td>");

                            tr += ("<td class=''>" + (data[i].other_orderNo == "-" ? '<span style="color:gray;">免推送</span>' : ((data[i].notifyOrderBank == "" ? "<span style='color:gray;'>无</span>" : data[i].notifyOrderBank) + "<a onclick=\"" + "get_action({name:'transport_Api',data:{id:" + data[i].id + ",for:'notifyOrderBank'},n:'卡号推送',e:'单号" + data[i].sd_orderNo + "进行卡号推送'});" + "\"" + '  style="color:#3c3ceb!important;">&nbsp;推送</a>')) + "</td>");



                            tr += ("<td class=''>" + (data[i].other_orderNo == "-" ? '<span style="color:gray;">免推送</span>' : ((data[i].notifyOrderStatus == "" ? "<span style='color:gray;'>无</span>" : data[i].notifyOrderStatus) + "<a onclick=\"" + "get_action({name:'transport_Api',data:{id:" + data[i].id + ",for:'notifyOrderStatus'},n:'状态推送',e:'单号" + data[i].sd_orderNo + "进行状态推送'});" + "\"" + '  style="color:#3c3ceb!important;">&nbsp;推送</a>')) + "</td>");

                            var modify_model = new Array();

                            tr += "</tr>";
                            $("#list-display").append(tr)


                            var __temp = data[i];
                            __temp.modify_model = modify_model;
                            _bindEvent(__temp);
                        }
                        $(".page_size").html(pager_size);
                        $(".page_count").html(json.data.count);
                        $(".cpages").html(json.data.html);

                        $(".total_amount").html(parseFloat(json.total_amount).toFixed(2));
                        $(".total_number").html(json.total_number);
                        $(".total_users").html(json.total_users);


                        getdata();

                        pop_mp3tip();


                        show_expireList();

                    } else {

                    }
                },
                error: function () {
                }
            });
        }
        getPager();

        function _bindEvent(__temp) {
            $('button[edit-' + __temp.id + ']').on("click", function () {
                console.log('attrs', $(this).closest('tr').html(), __temp.modify_model);

                edit({ aname: 'admin', title: '修改' + _actionName, action: _action, id: __temp.id, data: __temp.modify_model });
            })
        }

        function _pt(i) {
            pager_index = i;
            getPager();
        }
    </script>

    <script>
        layui.use(['form', 'layedit', 'laydate'], function () {
        });
    </script>

    <script>
        function update_select(state) {
            event_create({ 'aname': 'admin', 'name': _action, 'event': 'delete', 'id': select_all_id(), 'state': state });
        }


        function report_exl() {
            export_data(getParam());
        }
    </script>

    <script>
        var click_ids = {};
        function checkIsSelect(obj) {
            if (obj.prop('checked')) {
                return true;
            } else {
                return false;
            }
        }
        function goods_click(t) {
            t = $(t);
            var obj = t.find(".good_select");
            obj.prop("checked", !checkIsSelect(obj));

        }

        function select_all(obj) {
            $(".good_select").prop("checked", checkIsSelect($(obj)));
        }

        function select_all_id() {
            var ids = "";
            $("#list-display tr").each(function () {
                var select_obj = $(this).find(".good_select");
                if (checkIsSelect(select_obj)) {
                    if (ids != "") {
                        ids += ",";
                    }
                    ids += select_obj.data("id");
                }
            });
            return ids;
        }
    </script>

    <audio id="myAudio" controls style="display: none;">
        <source src="../static/files/新订单.mp3" type="audio/mpeg">
        Your browser does not support the audio element.
    </audio>

    <script>

        var autoTimer = 0;
        var lastList = null;

        var set_autoAlarm = function (e) {
            if (checkIsSelect($(e))) {
                console.log('启动');
                lastList = null;

                autoTimer = setInterval(function () {


                    getPager();

                }, 5000);

            } else {
                console.log('关闭');
                clearInterval(autoTimer);
                autoTimer = 0;
            }
        }
        var show_expireList = function () {

            // 获取所有的.expire_time标签
            $('.expire_time').each(function () {
                var time = new Date($(this).data('time')).getTime();

                // 创建并附加倒计时元素
                var countdownElement = $('<span style="color: #d96060;text-shadow: 5px 5px 5px #e952525c;font-weight: bold; ">00:00</span>');
                $(this).closest("td").find(".exp_countdown").append(countdownElement);

                // 更新倒计时
                function updateCountdown() {
                    var currentDate = new Date().getTime();
                    var timeLeft = time - currentDate;

                    if (timeLeft <= 0) {
                        countdownElement.text('已退回');
                    } else {
                        var minutes = Math.floor(timeLeft / (1000 * 60));
                        var seconds = Math.floor((timeLeft % (1000 * 60)) / 1000);

                        countdownElement.text(minutes.toString().padStart(2, '0') + ':' + seconds.toString().padStart(2, '0'));
                    }
                }

                updateCountdown();

                // 每秒更新一次倒计时
                setInterval(updateCountdown, 1000);
            });
        }

        var pop_mp3tip = function (json) {
            if (autoTimer == 0) {
                return;
            }



            for (var i = 0; i < current_json.length; i++) {
                if (current_json[i].state == 1000 || current_json[i].state == 1001) {
                    play_mp3tip();
                    break;
                }
            }

        }

        var mp3_audio = document.getElementById("myAudio"); // 获取音频元素
        var play_mp3tip = function () {
            //console.log('播放订单提示');
            mp3_audio.play();
        }



        function isExpired(expirationTime) {
            // 获取当前时间戳（单位：毫秒）
            var currentTime = new Date().getTime();

            // 将传入的过期时间转换为时间戳
            var expirationTimestamp = new Date(expirationTime).getTime();

            // 判断是否已经过期
            return currentTime > expirationTimestamp;
        }


        var show_details = function (id, type) {
            var details_info = "";
            for (var i = 0; i < current_json.length; i++) {
                var obj = current_json[i];

                if (obj.id == id) {
                    details_info += ' <div style="display: flex; margin: 20px 0; padding-bottom: 20px; border-bottom: 1px solid #eee;">            <div>                <b>支付方式：</b>            </div>            <div style="margin-left: 10px;">                ' + obj.payment_type + '            </div>        </div>';
                    details_info += ' <div style="display: flex; margin: 20px 0; padding-bottom: 20px; border-bottom: 1px solid #eee;">            <div>                <b>收款姓名：</b>            </div>            <div style="margin-left: 10px;">                ' + obj.payment_name + '            </div>        </div>';

                    if (obj.payment_type == "银行卡") {
                        details_info += ' <div style="display: flex; margin: 20px 0; padding-bottom: 20px; border-bottom: 1px solid #eee;">            <div>                <b>收款卡号：</b>            </div>            <div style="margin-left: 10px;">                ' + obj.payment_bankid + '            </div>        </div>';
                        details_info += ' <div style="display: flex; margin: 20px 0; padding-bottom: 20px; border-bottom: 1px solid #eee;">            <div>                <b>开户行：</b>            </div>            <div style="margin-left: 10px;">                ' + obj.payment_bankname + '            </div>        </div>';
                    } else {
                        details_info += ' <div style="display: flex; margin: 20px 0; padding-bottom: 20px; border-bottom: 1px solid #eee;">            <div>                <b>' + obj.payment_type + '账号：</b>            </div>            <div style="margin-left: 10px;">                ' + obj.payment_bankid + '            </div>        </div>';
                    }
                    details_info += ' <div style="display: flex; margin: 20px 0; padding-bottom: 20px; border-bottom: 1px solid #eee;">            <div>                <b>交易金额：</b>            </div>            <div style="margin-left: 10px;">                ' + obj.orderAmt + '            </div>        </div>';

                    details_info += '<div style="display: flex; margin: 20px 0; padding-bottom: 20px; border-bottom: 1px solid #eee;">' + (obj.state == 1000 && obj.orderLock != 1 ? '&nbsp;<a style="display: flex;align-items: center;padding: 10px 39px;margin-left: 6px;line-height: 19px;color: #5a5a5c;border: 1px solid #5a5a5c;" onclick="' + "get_action({name:'lock_order',data:{id:" + obj.id + ",group:" + get_param('group') + "},n:'锁定交易',e:'锁定 " + obj.orderId + " 的交易<br>【订单金额】：" + obj.amount + "'});" + '"><svg t="*************" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="7884" width="16" height="16"><path d="M500.224 694.528c-56.576 0-102.912-46.08-102.912-102.912a102.912 102.912 0 0 1 205.824 0c0 56.832-46.336 102.912-102.912 102.912z m0-146.944a44.032 44.032 0 1 0 0 88.064 44.032 44.032 0 0 0 0-88.064z" fill="#005CFF" p-id="7885"></path><path d="M733.44 318.72h-84.224c-1.28-77.056-64-139.264-141.312-139.264-77.312 0-140.032 62.208-141.312 139.264h-84.224c-64.512 0-116.992 52.48-116.992 116.992V747.52c0 64.512 52.48 116.992 116.992 116.992h450.816c64.512 0 116.992-52.48 116.992-116.992V435.712a116.736 116.736 0 0 0-116.736-116.992z m-225.536-80.384c44.8 0 81.408 35.84 82.432 80.384h-165.12c1.536-44.544 37.888-80.384 82.688-80.384zM791.808 747.52c0 32.256-26.112 58.368-58.368 58.368H282.624c-32.256 0-58.368-26.112-58.368-58.368V435.712c0-32.256 26.112-58.368 58.368-58.368h450.816c32.256 0 58.368 26.112 58.368 58.368V747.52z" fill="#1B1F28" p-id="7886"></path></svg>&nbsp;锁定</a>' : '') + '</div>';

                    _modal('收款信息', details_info);
                    break;
                }
            }
        }


        var create_order = function (id) {

            _modal("创建订单", "<div id='model-page' aid='" + id + "'>" + $("#buy_page").html() + "</div>");

            var details_info = "";
            for (var i = 0; i < current_json.length; i++) {
                var obj = current_json[i];
                if (obj.id == id) {
                    //$('#model-page .bank_item').hide();
                    //$('#model-page').attr();

                    $('#model-page .buyer_type').on('change', function () {
                        var name = $(this).val();
                        if (name == "银行卡") {
                            $('#model-page .bank_item').show();
                        } else {
                            $('#model-page .bank_item').hide();
                        }
                    });

                    $('#model-page .buyer_number').val((obj.total_amount - obj.deal_amount));

                    if (obj.accept_separate != 1) {
                        $('#model-page .buyer_number').css({ "background": "#f1f1f1" }).attr({ "disabled": "disabled" });
                    }

                    break;
                }
            }
        }

        var new_order = function () {
            var post_data = {
                id: $('#model-page').attr("aid"),
                buyer_type: $('#model-page .buyer_type').val(),
                buyer_amount: $('#model-page .buyer_number').val(),
                buyer_name: $('#model-page .buyer_name').val(),
                buyer_bankname: $('#model-page .buyer_bankname').val(),
                buyer_bankid: $('#model-page .buyer_bankid').val()
            }

            $.ajax({
                type: "POST",
                url: "../Api/admin.aspx?do=new_order",
                data: post_data,
                datatype: "json",
                success: function (data) {
                    if (data.code == 1) {
                        layer.msg(data.msg, { icon: 1, time: 1000 }, function () {
                            $('#myModal').modal('hide');
                            getPager();
                        });
                    } else {
                        layer.msg(data.msg, { icon: 2, time: 1500 });
                    }
                },
                error: function () {
                    layer.msg("抱歉，操作有误", { icon: 2, time: 1500 });
                }
            });

        }

        var get_star = function (s) {
            var text = '';
            for (var i = 0; i < s; i++) {
                text += '<svg t="*************" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="4009" width="12" height="12"><path d="M493.9 77c162.3-0.8 98.2 190.6 208.5 234.6 26.6 10.6 64.1 1 96.6 6.1 71.6 11.4 148.1 14.9 159.5 85.9 19 118.7-150.7 159.3-170.2 236.1-21.2 83.6 112.5 256.8-19.9 297.5-112.6 34.6-167.9-81.5-249.9-95.1-93.2-15.3-155.7 161.9-280.7 85.9-100.6-61.1 28.7-202.6 0-296-22.9-74.3-153.9-93-171.7-193.2-17.7-99.5 80.2-109.6 153.3-121.1 45.4-7.2 94.8 7.2 122.7-15.3 59.4-47.9 53-163.7 115-210.1 12.3-5.1 24.5-10.2 36.8-15.3z" p-id="4010" fill="#FFB408"></path></svg>';
            }
            return text;
        }
    </script>

    <style>
        .buy_list {
            display: flex;
            align-items: center;
            font-weight: bold;
            margin-top: 30px;
        }

            .buy_list .name {
                width: 100px;
                flex-shrink: 0;
            }

            .buy_list .item {
                width: 100%;
            }

                .buy_list .item input {
                    width: 100%;
                }
    </style>
    <div style="background: #fff; padding: 18px; display: none;" id="buy_page">

        <div class="buy_list">
            <div class="name">
                支付方式：
           
            </div>
            <div class="item">
                <select class="form-control buyer_type">
                    <option>银行卡</option>
                    <option>支付宝</option>
                    <option>微信</option>
                </select>
            </div>
        </div>

        <div class="buy_list">
            <div class="name" style="color: blue;">
                购买数量：           
            </div>
            <div class="item">
                <input class="form-control buyer_number" />
            </div>
        </div>

        <div class="buy_list">
            <div class="name">
                姓名：           
            </div>
            <div class="item">
                <input class="form-control buyer_name" />
            </div>
        </div>

        <div class="buy_list bank_item">
            <div class="name">
                开户行：           
            </div>
            <div class="item">
                <input class="form-control buyer_bankname" />
            </div>
        </div>

        <div class="buy_list">
            <div class="name">
                账号：           
            </div>
            <div class="item">
                <input class="form-control buyer_bankid" />
            </div>
        </div>

        <a class="btn btn-success" onclick="new_order()" style="margin-top: 24px; margin-left: 100px;">
            <svg t="*************" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="4127" width="16" height="16">
                <path d="M512 170.666667a341.333333 341.333333 0 0 0-290.133333 521.216 42.666667 42.666667 0 0 1-72.533334 45.013333A424.874667 424.874667 0 0 1 85.333333 512C85.333333 276.352 276.352 85.333333 512 85.333333s426.666667 191.018667 426.666667 426.666667-191.018667 426.666667-426.666667 426.666667a424.874667 424.874667 0 0 1-234.666667-70.314667 42.666667 42.666667 0 1 1 46.933334-71.210667A341.333333 341.333333 0 1 0 512 170.666667z m222.165333 264.832a42.666667 42.666667 0 0 0-60.330666-60.330667L469.333333 579.669333l-119.168-119.168a42.666667 42.666667 0 0 0-60.330666 60.330667l149.333333 149.333333a42.666667 42.666667 0 0 0 60.330667 0l234.666666-234.666666z" fill="#14101C" p-id="4128"></path></svg>&nbsp;创建订单</a>

    </div>



    <script>
        // 传入格式为 "yyyy-MM-dd HH:mm:ss" 的时间戳
        function countdownToTime(timestamp) {
            var targetDate = new Date(timestamp);
            var currentDate = new Date();
            var timeDiff = targetDate - currentDate;

            if (timeDiff <= 0) {
                return '已超时';
            }

            var hours = Math.floor(timeDiff / (1000 * 60 * 60));
            var minutes = Math.floor((timeDiff % (1000 * 60 * 60)) / (1000 * 60));
            var seconds = Math.floor((timeDiff % (1000 * 60)) / 1000);

            var formattedTime = hours + ':' + minutes.toString().padStart(2, '0') + ':' + seconds.toString().padStart(2, '0');
            return formattedTime;
        }
        var interval = 0


        function getdata() {
            clearInterval(interval);
            interval = setInterval(function () {
                $('td[expiretime]').each(function () {
                    var expiretime = $(this).attr('expiretime');
                    var result = countdownToTime(expiretime);
                    if (result == "已超时") {
                        result = "<span style='color:gray;'>已超时</span>";
                        $(this).removeAttr("expiretime");
                    }
                    $(this).html(result);
                })
            }, 1000); // 更新频率每秒钟
        }


        var pub_dating = function (id) {

        }

    </script>

</asp:Content>



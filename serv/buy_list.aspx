<%@ Page Title="买币订单" Language="C#" MasterPageFile="~/serv/m.master" AutoEventWireup="true" CodeFile="buy_list.aspx.cs" Inherits="w_index" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="Server">
    <link href="../css/element-ui.css" rel="stylesheet" />
    <style>
        .el-tag.el-tag--test {
            background-color: #d5d5f5;
            border-color: #7d7dc7;
            color: #6d3333;
        }
    </style>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="Server">
    <div class="ibox float-e-margins">
        <div class="ibox-title">
            <h5>买币订单</h5>
        </div>


        <div class="ibox-content">


            <style>
                .switch-1 {
                    padding: 7px 12px;
                    border-radius: 3px;
                    font-weight: bold;
                    display: flex;
                    align-items: center;
                    margin-bottom: 10px;
                    margin-right: 10px;
                    cursor: pointer;
                    background: #eee;
                    color: #5a5b5c;
                    border: 1px solid #ddd;
                }

                    .switch-1.active {
                        background: linear-gradient(119deg, #6799ed, #67bf8b);
                        color: #fff;
                        border: 1px solid transparent;
                    }
            </style>

            <div style="display: flex;">

                <div class="switch-1" from_type="base">
                    <svg t="1700921634815" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="1629" width="32" height="32">
                        <path d="M512 896c200.298667 0 362.666667-152.810667 362.666667-341.333333s19.050667-192.533333-362.666667-192.533334S149.333333 366.144 149.333333 554.666667s162.368 341.333333 362.666667 341.333333z" fill="#FF5C00" p-id="1630"></path><path d="M149.333333 490.666667a362.666667 341.333333 0 1 0 725.333334 0 362.666667 341.333333 0 1 0-725.333334 0Z" fill="#FFCC00" p-id="1631"></path><path d="M820.224 310.698667a332.288 332.288 0 0 1 31.146667 59.349333L404.629333 816.789333a373.12 373.12 0 0 1-64.853333-25.642666z m-189.269333-142.570667c57.621333 18.816 108.629333 50.922667 148.906666 92.416L284.16 756.266667c-46.570667-35.456-83.626667-81.514667-107.008-134.314667z" fill="#FFE3B6" p-id="1632"></path><path d="M192 480a320 288 0 1 0 640 0 320 288 0 1 0-640 0Z" fill="#FF7325" p-id="1633"></path><path d="M213.333333 480a298.666667 266.666667 0 1 0 597.333334 0 298.666667 266.666667 0 1 0-597.333334 0Z" fill="#FFB329" p-id="1634"></path><path d="M808.533333 512c-17.706667 132.181333-143.722667 234.666667-296.533333 234.666667s-278.826667-102.485333-296.533333-234.666667z" fill="#FF9B1A" p-id="1635"></path><path d="M512 213.333333c108.074667 0 202.730667 51.242667 255.168 128H256.853333c52.437333-76.757333 147.093333-128 255.168-128z" fill="#FFCC00" p-id="1636"></path><path d="M587.648 510.037333l94.72-13.930666 0.298667 40.938666c-6.4 25.045333-16.426667 45.973333-30.165334 62.762667a129.92 129.92 0 0 1-51.093333 38.037333c-20.352 8.533333-46.250667 12.821333-77.674667 12.821334-38.144 0-69.290667-5.205333-93.482666-15.637334-24.170667-10.432-45.034667-28.757333-62.592-55.018666-17.557333-26.24-26.325333-59.84-26.325334-100.8 0-54.613333 15.445333-96.554667 46.314667-125.888 30.890667-29.333333 74.56-43.989333 131.050667-43.989334 44.202667 0 78.954667 8.405333 104.256 25.194667 9.962667 6.613333 28.501333 18.346667 55.594666 35.2-0.341333 16.469333-0.085333 30.549333 0.768 42.197333l-95.744 20.053334c-3.349333-10.069333-6.869333-17.408-10.538666-22.058667a61.482667 61.482667 0 0 0-22.250667-18.005333 67.328 67.328 0 0 0-29.44-6.314667c-24.576 0-43.413333 9.301333-56.490667 27.904-9.898667 13.802667-14.848 35.477333-14.848 65.024 0 36.608 5.909333 61.696 17.706667 75.264 11.818667 13.589333 28.416 20.373333 49.792 20.373333 20.736 0 36.416-5.482667 47.04-16.426666 10.602667-10.944 18.304-26.858667 23.104-47.701334z" fill="#FF5C00" p-id="1637"></path><path d="M587.648 467.370667L682.666667 494.378667c-6.4 25.045333-16.426667 45.973333-30.165334 62.762666a129.92 129.92 0 0 1-51.093333 38.037334c-20.352 8.533333-46.250667 12.821333-77.674667 12.821333-38.144 0-69.290667-5.205333-93.482666-15.637333-24.170667-10.432-45.034667-28.757333-62.592-55.018667-17.557333-26.24-26.325333-59.84-26.325334-100.8 0-54.613333 15.445333-96.554667 46.314667-125.888 30.890667-29.333333 74.56-43.989333 131.050667-43.989333 44.202667 0 78.954667 8.405333 104.256 25.194666 25.28 16.810667 44.074667 42.602667 56.362666 77.397334l-95.744 20.053333c-3.349333-10.069333-6.869333-17.408-10.538666-22.058667a61.482667 61.482667 0 0 0-22.250667-18.005333 67.328 67.328 0 0 0-29.44-6.314667c-24.576 0-43.413333 9.301333-56.490667 27.904-9.898667 13.802667-14.848 35.477333-14.848 65.024 0 36.608 5.909333 61.696 17.706667 75.264 11.818667 13.589333 28.416 20.373333 49.792 20.373334 20.736 0 36.416-5.482667 47.04-16.426667 10.602667-10.944 18.304-26.858667 23.104-47.701333z" fill="#FFDC96" p-id="1638"></path><path d="M622.933333 291.861333c18.858667 12.522667 34.112 30.058667 45.738667 52.586667l-34.24 34.197333-50.858667 10.666667c-3.349333-10.069333-6.869333-17.408-10.538666-22.058667a61.482667 61.482667 0 0 0-22.250667-18.005333 67.328 67.328 0 0 0-29.44-6.314667c-24.576 0-43.413333 9.301333-56.490667 27.904-9.898667 13.802667-14.848 35.477333-14.848 65.024 0 36.608 5.909333 61.696 17.706667 75.264 5.525333 6.357333 12.074667 11.221333 19.690667 14.592l-63.637334 63.616c-21.44-10.794667-40.149333-28.117333-56.106666-51.989333-9.92-14.848-17.045333-32.021333-21.333334-51.584l215.893334-215.893333c24.170667 3.84 44.416 11.178667 60.736 21.994666z" fill="#FFECBD" p-id="1639"></path></svg>普通买币
           
           
                </div>

                <div class="switch-1" from_type="alipay">
                    <svg t="" style="margin-right: 5px;" class="icon" viewBox="0 0 1190 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="5008" width="23" height="23">
                        <path d="M1019.83566531 663.83477699V240.35176499a165.436376 165.436376 0 0 0-165.436376-165.436375h-528.714191a165.436376 165.436376 0 0 0-165.436375 165.436375v528.714191a165.265823 165.265823 0 0 0 165.436375 165.265823h528.714191a165.436376 165.436376 0 0 0 162.878081-136.442372c-44.002665-18.760826-233.998668-100.967355-333.08994-148.210526-75.38441 91.245836-153.497668 145.993338-272.884743 145.993338s-198.694204-72.314457-189.143238-162.025317c6.31046-59.011326 46.560959-155.032645 221.718854-138.489007 92.439707 8.698201 134.736842 25.924051 210.291805 51.165889a594.547635 594.547635 0 0 0 47.754831-116.828781H367.98223431v-33.598934h165.265823v-59.011325H331.65445231v-37.009994h201.593605v-85.276482a17.055296 17.055296 0 0 1 17.055296-13.473684h82.718188v99.261825h214.726182v36.498335H632.68043531v59.693537h175.328448a671.467022 671.467022 0 0 1-71.120587 178.568954c51.165889 18.41972 282.947368 89.369753 282.947369 89.369754z" fill="#BBDCFF" p-id="5009"></path><path d="M397.82900331 739.90139899c-125.697535 0-145.652232-79.477682-139.000667-112.564957s42.979347-76.066622 112.906063-76.066622c80.330446 0 152.303797 20.636909 238.774151 62.592938-60.546302 78.966023-135.077948 126.038641-212.508994 126.038641z" fill="#FFD300" p-id="5010"></path><path d="M721.87963531 579.92271799l11.597602 4.263824c51.165889 18.249167 272.884744 86.470353 283.288474 89.369754l13.303132 4.093271V240.35176499A175.840107 175.840107 0 0 0 854.39928931 63.99999999h-528.714191a175.840107 175.840107 0 0 0-175.669553 175.669554v528.71419a175.840107 175.840107 0 0 0 175.669553 175.499001h528.714191a175.328448 175.328448 0 0 0 172.940706-144.628914l1.364424-8.01599-7.504331-3.240506c-38.033311-17.055296-232.97535-100.455696-332.578281-148.039973l-7.163224-3.581612-5.116589 6.310459c-83.4004 100.967355-160.319787 142.241173-265.380413 142.241173a196.135909 196.135909 0 0 1-147.528315-55.77082 119.387075 119.387075 0 0 1-31.893404-94.486342c4.775483-44.514324 34.110593-145.993338 210.974017-129.4497a694.662225 694.662225 0 0 1 181.638908 41.614923l26.265156 8.698201 8.186543 2.728848 3.922718-7.504331a605.633578 605.633578 0 0 0 48.778148-119.387075l3.752165-12.962025H378.21541231V405.78814099h165.265822v-80.159893H341.88763031v-15.861426h201.593604V215.10992699a5.969354 5.969354 0 0 1 6.481013-4.263824h72.48501v99.261825h215.067289v15.861426H622.44725731v80.159893h171.917388a661.745503 661.745503 0 0 1-66.856762 163.219187z m96.02132-181.638907l3.240506-12.62092H642.91361331v-39.227181h215.067288v-56.794138H642.91361331V190.37974699h-92.951366a26.265157 26.265157 0 0 0-26.776815 22.342438v76.919387H321.42127431v56.794138h201.593605v39.227181h-165.265823v53.553631H688.28070131a580.732845 580.732845 0 0 1-38.886076 94.145237l-18.078614-5.969354a712.058628 712.058628 0 0 0-186.243837-42.467688C250.81234731 466.50499699 217.38396631 581.96935399 211.92627131 632.45303099a141.558961 141.558961 0 0 0 36.668887 110.688874 216.943371 216.943371 0 0 0 162.878082 63.616256c108.301133 0 191.189873-42.979347 275.954697-143.605596 96.191872 45.537642 268.279813 119.387075 318.592938 141.729514a154.862092 154.862092 0 0 1-151.109927 119.387075h-528.714191a155.373751 155.373751 0 0 1-155.203197-155.032645v-528.714191a155.373751 155.373751 0 0 1 155.203197-155.203197h528.714191a155.373751 155.373751 0 0 1 155.203198 155.203197v409.327116c-46.390406-14.326449-201.081945-61.910726-257.70553-81.183211a684.087941 684.087941 0 0 0 65.492339-170.382412z m-446.166556 143.26449c-69.58561 0-114.611592 42.467688-122.968688 85.276482a96.703531 96.703531 0 0 0 21.83078 76.748835c17.908061 21.830779 54.406396 47.925383 127.232512 47.925383 77.942705 0 156.226516-46.219853 220.695536-129.961359l7.50433-10.062625-11.256495-5.457695c-91.416389-45.878748-164.242505-64.469021-243.037975-64.469021z m26.094604 187.608261c-51.165889 0-89.881412-13.985343-111.541639-40.421052a77.260493 77.260493 0 0 1-17.055297-59.864091c5.457695-27.288474 37.351099-68.221186 102.331779-68.221186a500.572951 500.572951 0 0 1 222.571619 56.111926c-58.499667 73.167222-127.914724 112.394404-196.306462 112.394403z" fill="#3989FF" p-id="5011"></path></svg>支付宝交易
                </div>


                <div class="switch-1" from_type="usdt">
                    <svg t="1700921804175" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2704" width="23" height="23" style="margin-right: 5px;">
                        <path d="M1023.082985 511.821692c0 281.370746-228.08199 509.452736-509.452736 509.452736-281.360557 0-509.452736-228.08199-509.452737-509.452736 0-281.365652 228.092179-509.452736 509.452737-509.452737 281.370746 0 509.452736 228.087085 509.452736 509.452737" fill="#1BA27A" p-id="2705"></path><path d="M752.731701 259.265592h-482.400796v116.460896h182.969951v171.176119h116.460895v-171.176119h182.96995z" fill="#FFFFFF" p-id="2706"></path><path d="M512.636816 565.13592c-151.358408 0-274.070289-23.954468-274.070289-53.50782 0-29.548259 122.706786-53.507821 274.070289-53.507821 151.358408 0 274.065194 23.959562 274.065194 53.507821 0 29.553353-122.706786 53.507821-274.065194 53.50782m307.734925-44.587303c0-38.107065-137.776398-68.995184-307.734925-68.995184-169.953433 0-307.74002 30.888119-307.74002 68.995184 0 33.557652 106.837333 61.516418 248.409154 67.711363v245.729433h116.450707v-245.632637c142.66205-6.001353 250.615085-34.077294 250.615084-67.808159" fill="#FFFFFF" p-id="2707"></path></svg>USDT交易
                </div>

                <div class="switch-1" from_type="api">
                    <svg t="1701013836480" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="5840" width="23" height="23">
                        <path d="M285.023 233.926l28.263 48.885a8 8 0 0 1-2.934 10.937L222 344.615v333.92l290 166.96 290-166.96v-333.92l-85.113-49.001a8 8 0 0 1-2.934-10.938l28.26-48.88a8.017 8.017 0 0 1 10.934-2.927l112.86 65.144A15.974 15.974 0 0 1 874 311.846v399.461a15.974 15.974 0 0 1-7.993 13.834l-346 199.714a16.02 16.02 0 0 1-16.014 0l-346-199.714A15.974 15.974 0 0 1 150 711.307v-399.46a15.974 15.974 0 0 1 7.993-13.834l116.097-67.014a8.017 8.017 0 0 1 10.933 2.927z" fill="#1C6EFF" p-id="5841"></path><path d="M541.896 552c4.4 0 7.999-3.6 7.999-7.998V253.735h74.09c6.699 0 10.399-7.698 6.3-12.896L518.298 99.074c-3.2-4.099-9.399-4.099-12.598 0L393.716 240.74c-4.1 5.198-0.4 12.896 6.3 12.896h73.89v290.367c0 4.399 3.599 7.998 7.998 7.998h59.992zM540 736a8 8 0 0 0 8-8v-96a8 8 0 0 0-8-8h-56a8 8 0 0 0-8 8v96a8 8 0 0 0 8 8h56z" fill="#44D7B6" p-id="5842"></path></svg>三方API订单
                </div>


            </div>


            <script>
                var from_type = get_param("from_type");
                $("[from_type='" + from_type + "']").addClass("active").siblings().removeClass("active");
                $('.switch-1').on('click', function () {
                    from_type = $(this).attr('from_type');
                    if ($(this).hasClass("active")) {
                        from_type = "";
                    }
                    location.href = "buy_list.aspx?state=" + get_param("state") + "&from_type=" + from_type;
                });
            </script>


            <div class="row layui-form" style="margin-bottom: 10px;">
                <div class="col-md-4">
                    <select id="user_type" class="form-control">
                        <option value="">层级选择</option>
                        <option value="reg">新注册用户</option>
                        <option value="user">普通用户</option>
                        <option value="th">托号</option>
                    </select>
                </div>



                <div class="col-md-6">
                    <div class="form-group form-buttom" id="twoInputs">
                        <input type="text" id="start_time" name="ordername" class="form-control" placeholder="开始时间" style="width: 48%; display: inline-block;" value="" autocomplete="off">
                        <input type="text" id="end_time" name="ordername" class="form-control" placeholder="结束时间" style="width: 48%; display: inline-block;" autocomplete="off">

                        <script>
                            $(document).ready(function () {
                                $('#start_time').datepicker({
                                    format: 'yyyy-mm-dd',
                                    autoclose: true
                                });

                                $('#end_time').datepicker({
                                    format: 'yyyy-mm-dd',
                                    autoclose: true
                                });
                            });
                        </script>
                    </div>
                </div>

            </div>
            <div class="row layui-form">
                <div class="col-md-4">
                    <div class="form-group form-buttom" style="position: relative;">
                        <input type="text" id="search-keyword" name="ordername" class="form-control" placeholder="请输入搜索的信息" style="padding-right: 50px;">
                        <a class="input_button" onclick="searchList()">查询</a>
                    </div>

                </div>



                <div class="col-md-8">
                    <div class="form-group form-buttom">
                        <a class="btn btn-success" onclick="javascript:location.href=location.href;">
                            <svg t="1688237949108" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2211" width="16" height="16">
                                <path d="M512 192a298.24 298.24 0 0 0-245.162667 128 42.666667 42.666667 0 1 1-69.973333-48.810667 384.042667 384.042667 0 0 1 697.173333 180.48l36.608-34.645333a42.666667 42.666667 0 0 1 58.709334 61.952l-112.64 106.666667a42.666667 42.666667 0 0 1-71.253334-38.912A298.666667 298.666667 0 0 0 512 192z m0.170667 640a298.24 298.24 0 0 0 245.162666-128 42.666667 42.666667 0 1 1 69.973334 48.810667 384.042667 384.042667 0 0 1-697.173334-180.48l-36.650666 34.645333a42.666667 42.666667 0 0 1-58.666667-61.952l112.64-106.666667a42.666667 42.666667 0 0 1 71.253333 38.912 298.666667 298.666667 0 0 0 293.418667 354.730667z" fill="#14101C" p-id="2212"></path></svg>
                            刷新</a>
                        <a class="btn btn-success" onclick="opennew('银行卡')">
                            <svg t="1688237854283" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="15169" width="16" height="16">
                                <path d="M725.333333 213.333333H298.666667a85.333333 85.333333 0 0 0-85.333334 85.333334v341.333333a42.666667 42.666667 0 1 1-85.333333 0V298.666667a170.666667 170.666667 0 0 1 170.666667-170.666667h426.666666a170.709333 170.709333 0 0 1 167.850667 139.733333v488.533334A170.709333 170.709333 0 0 1 725.333333 896H298.666667a170.538667 170.538667 0 0 1-144.469334-79.744l1.194667-0.682667a42.666667 42.666667 0 0 1 76.501333-37.034666c15.658667 19.584 39.765333 32.128 66.773334 32.128h426.666666a85.333333 85.333333 0 0 0 85.333334-85.333334V298.666667a85.333333 85.333333 0 0 0-85.333334-85.333334z m-213.333333 512a42.666667 42.666667 0 0 1-42.666667-42.666666v-128H341.333333a42.666667 42.666667 0 1 1 0-85.333334h128V341.333333a42.666667 42.666667 0 1 1 85.333334 0v128h128a42.666667 42.666667 0 1 1 0 85.333334h-128v128a42.666667 42.666667 0 0 1-42.666667 42.666666z" fill="#14101C" p-id="15170"></path></svg>&nbsp;添加卡</a>
                        <a class="btn btn-success" onclick="opennew('网银')">
                            <svg t="1688237854283" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="15169" width="16" height="16">
                                <path d="M725.333333 213.333333H298.666667a85.333333 85.333333 0 0 0-85.333334 85.333334v341.333333a42.666667 42.666667 0 1 1-85.333333 0V298.666667a170.666667 170.666667 0 0 1 170.666667-170.666667h426.666666a170.709333 170.709333 0 0 1 167.850667 139.733333v488.533334A170.709333 170.709333 0 0 1 725.333333 896H298.666667a170.538667 170.538667 0 0 1-144.469334-79.744l1.194667-0.682667a42.666667 42.666667 0 0 1 76.501333-37.034666c15.658667 19.584 39.765333 32.128 66.773334 32.128h426.666666a85.333333 85.333333 0 0 0 85.333334-85.333334V298.666667a85.333333 85.333333 0 0 0-85.333334-85.333334z m-213.333333 512a42.666667 42.666667 0 0 1-42.666667-42.666666v-128H341.333333a42.666667 42.666667 0 1 1 0-85.333334h128V341.333333a42.666667 42.666667 0 1 1 85.333334 0v128h128a42.666667 42.666667 0 1 1 0 85.333334h-128v128a42.666667 42.666667 0 0 1-42.666667 42.666666z" fill="#14101C" p-id="15170"></path></svg>&nbsp;添加网银</a>
                        <a class="btn btn-success" onclick="opennew('支付宝')">
                            <svg t="1688237854283" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="15169" width="16" height="16">
                                <path d="M725.333333 213.333333H298.666667a85.333333 85.333333 0 0 0-85.333334 85.333334v341.333333a42.666667 42.666667 0 1 1-85.333333 0V298.666667a170.666667 170.666667 0 0 1 170.666667-170.666667h426.666666a170.709333 170.709333 0 0 1 167.850667 139.733333v488.533334A170.709333 170.709333 0 0 1 725.333333 896H298.666667a170.538667 170.538667 0 0 1-144.469334-79.744l1.194667-0.682667a42.666667 42.666667 0 0 1 76.501333-37.034666c15.658667 19.584 39.765333 32.128 66.773334 32.128h426.666666a85.333333 85.333333 0 0 0 85.333334-85.333334V298.666667a85.333333 85.333333 0 0 0-85.333334-85.333334z m-213.333333 512a42.666667 42.666667 0 0 1-42.666667-42.666666v-128H341.333333a42.666667 42.666667 0 1 1 0-85.333334h128V341.333333a42.666667 42.666667 0 1 1 85.333334 0v128h128a42.666667 42.666667 0 1 1 0 85.333334h-128v128a42.666667 42.666667 0 0 1-42.666667 42.666666z" fill="#14101C" p-id="15170"></path></svg>&nbsp;添加支付宝</a>
                        <a class="btn btn-success" onclick="opennew('USDT')">
                            <svg t="1688237854283" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="15169" width="16" height="16">
                                <path d="M725.333333 213.333333H298.666667a85.333333 85.333333 0 0 0-85.333334 85.333334v341.333333a42.666667 42.666667 0 1 1-85.333333 0V298.666667a170.666667 170.666667 0 0 1 170.666667-170.666667h426.666666a170.709333 170.709333 0 0 1 167.850667 139.733333v488.533334A170.709333 170.709333 0 0 1 725.333333 896H298.666667a170.538667 170.538667 0 0 1-144.469334-79.744l1.194667-0.682667a42.666667 42.666667 0 0 1 76.501333-37.034666c15.658667 19.584 39.765333 32.128 66.773334 32.128h426.666666a85.333333 85.333333 0 0 0 85.333334-85.333334V298.666667a85.333333 85.333333 0 0 0-85.333334-85.333334z m-213.333333 512a42.666667 42.666667 0 0 1-42.666667-42.666666v-128H341.333333a42.666667 42.666667 0 1 1 0-85.333334h128V341.333333a42.666667 42.666667 0 1 1 85.333334 0v128h128a42.666667 42.666667 0 1 1 0 85.333334h-128v128a42.666667 42.666667 0 0 1-42.666667 42.666666z" fill="#14101C" p-id="15170"></path></svg>&nbsp;添加USDT</a>

                        <%--<a class="btn btn-success" onclick="update_select(-9)">
                            <svg t="1688237896673" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="1699" width="16" height="16">
                                <path d="M612.693333 170.666667l8.533334 42.666666H402.773333l8.533334-42.666666h201.386666z m-293.632 25.941333a86.186667 86.186667 0 0 0-1.706666 16.725333H170.666667a42.666667 42.666667 0 0 0 0 85.333334h682.666666a42.666667 42.666667 0 1 0 0-85.333334h-146.730666c0-5.504-0.554667-11.093333-1.706667-16.725333l-8.533333-42.666667A85.333333 85.333333 0 0 0 612.778667 85.333333H411.306667a85.333333 85.333333 0 0 0-83.712 68.608l-8.533334 42.666667zM426.666667 469.333333a42.666667 42.666667 0 0 0-42.666667 42.666667v170.666667a42.666667 42.666667 0 1 0 85.333333 0v-170.666667a42.666667 42.666667 0 0 0-42.666666-42.666667z m128 42.666667a42.666667 42.666667 0 1 1 85.333333 0v170.666667a42.666667 42.666667 0 1 1-85.333333 0v-170.666667zM255.402667 384A42.666667 42.666667 0 0 1 298.666667 426.069333L301.738667 640H301.653333a42.666667 42.666667 0 1 1-85.290666-2.176l-2.986667-210.56A42.666667 42.666667 0 0 1 255.36 384z m51.925333 396.586667h0.042667A103.381333 103.381333 0 0 0 406.698667 853.333333h210.602666c54.186667 0 98.56-40.789333 103.210667-92.757333L725.333333 426.069333a42.666667 42.666667 0 0 1 85.333334 1.194667l-4.864 337.365333-0.128 1.365334C798.08 863.872 715.562667 938.666667 617.301333 938.666667H406.698667a188.885333 188.885333 0 0 1-180.096-130.389334 42.666667 42.666667 0 0 1 80.725333-27.648z" fill="#14101C" p-id="1700"></path></svg>删除</a>--%>


                        <a class="btn btn-success" onclick="report_exl()">
                            <svg t="1695489540076" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="5479" width="16" height="16">
                                <path d="M686.08 989.866667H165.546667c-51.2 0-92.16-40.96-92.16-92.16V126.293333c0-51.2 40.96-92.16 92.16-92.16h573.44c6.826667 0 11.946667 1.706667 17.066666 6.826667l187.733334 174.08c5.12 5.12 8.533333 11.946667 8.533333 18.773333v256c0 13.653333-11.946667 25.6-25.6 25.6s-25.6-11.946667-25.6-25.6V244.053333L728.746667 85.333333h-563.2C141.653333 85.333333 122.88 104.106667 122.88 126.293333v769.706667c0 22.186667 18.773333 40.96 40.96 40.96h520.533333c13.653333 0 25.6 11.946667 25.6 25.6S699.733333 989.866667 686.08 989.866667z" fill="#333333" p-id="5480"></path><path d="M919.893333 259.413333h-121.173333c-51.2 0-92.16-40.96-92.16-92.16V59.733333c0-13.653333 11.946667-25.6 25.6-25.6s25.6 11.946667 25.6 25.6v105.813334c0 22.186667 18.773333 40.96 40.96 40.96h121.173333c13.653333 0 25.6 11.946667 25.6 25.6s-11.946667 27.306667-25.6 27.306666zM500.053333 305.493333H208.213333c-13.653333 0-25.6-11.946667-25.6-25.6s11.946667-25.6 25.6-25.6h291.84c13.653333 0 25.6 11.946667 25.6 25.6s-11.946667 25.6-25.6 25.6zM616.106667 575.146667H208.213333c-13.653333 0-25.6-11.946667-25.6-25.6s11.946667-25.6 25.6-25.6h407.893334c13.653333 0 25.6 11.946667 25.6 25.6s-11.946667 25.6-25.6 25.6zM380.586667 440.32H208.213333c-13.653333 0-25.6-11.946667-25.6-25.6s11.946667-25.6 25.6-25.6h172.373334c13.653333 0 25.6 11.946667 25.6 25.6s-11.946667 25.6-25.6 25.6z" fill="#333333" p-id="5481"></path><path d="M914.773333 779.946667H600.746667c-13.653333 0-25.6-11.946667-25.6-25.6s11.946667-25.6 25.6-25.6h314.026666c13.653333 0 25.6 11.946667 25.6 25.6s-11.946667 25.6-25.6 25.6z" fill="#333333" p-id="5482"></path><path d="M831.146667 873.813333c-6.826667 0-13.653333-1.706667-18.773334-6.826666-10.24-10.24-10.24-25.6 0-35.84l75.093334-75.093334-75.093334-75.093333c-10.24-10.24-10.24-25.6 0-35.84 10.24-10.24 25.6-10.24 35.84 0l93.866667 93.866667c5.12 5.12 6.826667 11.946667 6.826667 18.773333s-3.413333 13.653333-6.826667 18.773333L848.213333 870.4c-5.12 1.706667-11.946667 3.413333-17.066666 3.413333z" fill="#333333" p-id="5483"></path><path d="M694.613333 824.32h-529.066666c-37.546667 0-68.266667-30.72-68.266667-68.266667V121.173333c0-37.546667 30.72-68.266667 68.266667-68.266666h529.066666c37.546667 0 68.266667 30.72 68.266667 68.266666v634.88c0 37.546667-30.72 68.266667-68.266667 68.266667z" fill="#333333" opacity=".3" p-id="5484"></path></svg>导出</a>
                    </div>
                </div>



            </div>



            <div style="display: flex; flex-wrap: wrap; margin-bottom: 20px;">
                <div class="timesel_lab">全部</div>
                <div class="timesel_lab">今天</div>
                <div class="timesel_lab">昨天</div>
                <div class="timesel_lab">本周</div>
                <div class="timesel_lab">上周</div>
                <div class="timesel_lab">本月</div>
                <div class="timesel_lab">上月</div>
            </div>

            <div>
                <input type="checkbox" onclick="set_autoAlarm(this)" />&nbsp;自动监控订单（5s刷新一次）


                
                <div style="display: inline-block; font-weight: bold; background: #e5ded887; padding: 2px 18px; border-radius: 5px; color: #000; font-size: 20px;">
                    <span class="total_amount">0</span> 总金额<span style="font-size: 18px; background: linear-gradient(152deg, #ff5412d4, #f3cdbe); color: #fff8e0; border-radius: 6px; font-weight: 100; padding: 2px 9px; margin-left: 5px; font-weight: bold;"><span class="total_number">0</span> 总量</span>
                </div>
            </div>


            <br />


            <div class="table-responsive">
                <table class="table">
                    <thead>
                        <tr role="head">
                            <th sort="true" class="action-cell-left">
                                <input type="checkbox" onclick="javascript: select_all(this)" />#ID</th>
                            <th sort="true" class="">订单号</th>
                            <th sort="true" class="">层级</th>
                            <th sort="true" class="">挂单人</th>
                            <th sort="true" class="">操作人</th>
                            <th sort="true" class="">姓名</th>
                            <th sort="true" class="">历史佣金</th>
                            <th sort="true" class="">账号</th>
                            <th sort="true" class="">订单状态</th>
                            <th sort="true" class="">支付方式</th>
                            <th sort="true" class="">订单金额</th>
                            <th sort="true" class="">付款图片</th>
                            <th sort="true" class="">可见用户</th>
                            <th sort="true" class="">不可见用户</th>
                            <th sort="true" class="">创建时间</th>
                            <th sort="true" class="">通过时间</th>
                            <th sort="true" class="">状态通知</th>
                            <%--<th sort="true" class="action-cell">操作</th>--%>
                        </tr>
                    </thead>
                    <tbody id="list-display">
                    </tbody>
                </table>
            </div>


            <div class="layui-table-page">
                <div id="layui-table-page1">
                    <div class="layui-box layui-laypage layui-laypage-default" id="layui-laypage-1">
                        <div style="display: inline-block;" class="cpages">
                            <!--page grow -->
                        </div>
                        <span class="layui-laypage-skip">到第<input type="text" min="1" value="1" class="layui-input target-page" />页
                            <button type="button" class="layui-laypage-btn" onclick="javascript:pager_index=$('.target-page').val()-1;getPager();">确定</button></span>
                        <span class="layui-laypage-count">共 <span class="page_count"></span>条</span>
                        <span class="layui-laypage-limits">
                            <select class="s_pages" lay-ignore="" onchange="javascript:pager_size=$(this).val();getPager();">
                                <option value="10">10 条/页</option>
                                <option value="20" selected="">20 条/页</option>
                                <option value="30">30 条/页</option>
                                <option value="50">50 条/页</option>
                                <option value="100">100 条/页</option>
                                <option value="200">200 条/页</option>
                                <option value="500">500 条/页</option>
                                <option value="1000">1000 条/页</option>
                                <option value="2000">2000 条/页</option>
                            </select></span>
                    </div>
                </div>
            </div>

        </div>

    </div>
    <script>
        var _action = 'buy_list';
        var _actionName = '';
    </script>
    <script id="item_classList" type="text/javascript">
        <%=ToJson(chelper.gdt("item_classList")) %>
    </script>
    <script>
        var item_classList = JSON.parse($('#item_classList').html());
        var classList = [];
        for (var i = 0; i < item_classList.length; i++) {
            classList.push([item_classList[i].id, item_classList[i].name]);
        }

        function opennew(type) {
            var modify_model = [];


            //modify_model.push({ name: '选择分类', id: 'classId', data: classList, type: 'select' });
            modify_model.push({ name: '收款方式', id: 'payment_type', value: type, type: 'hidden' });
            //modify_model.push({ name: '收款方式', id: 'payment_type', value: '银行卡', type: 'option', data: [['银行卡', '银行卡'], ['USDT', 'USDT']] });


            if (type == "USDT") {
                modify_model.push({ name: '赠送比例', id: 'payment_p1', value: '' });
                modify_model.push({ name: '订单金额', id: 'amount', value: '' });
                modify_model.push({ name: '买币地址', id: 'payment_bankid', value: '', tip: '买币地址' });
                modify_model.push({ name: '财务专员', id: 'payment_name', value: '', tip: '财务专员' });
                modify_model.push({ name: '', id: 'payment_bankname', value: '', type: 'hidden' });
                modify_model.push({ name: '', id: 'payment_city', value: '', type: 'hidden' });
                modify_model.push({ name: '生成数量', id: 'number', value: '' });
            } else if (type == "银行卡") {
                modify_model.push({ name: 'payment_p1', id: 'payment_p1', value: '', type: 'hidden' });
                modify_model.push({ name: '订单金额', id: 'amount', value: '0', type: 'hidden' });
                modify_model.push({ name: '商家名称', id: 'payment_name', value: '' });
                modify_model.push({ name: '金额区间', id: 'payment_bankid', value: '' });
                modify_model.push({ name: '密信ID', id: 'payment_city', value: '' });
                modify_model.push({ name: '温馨提示', id: 'payment_bankname', value: '' });
                modify_model.push({ name: '生成数量', id: 'number', value: '1', type: 'hidden' });
            } else if (type == "网银" || type == "支付宝") {
                if (type == "支付宝") {

                    modify_model.push({ name: '赠送比例', id: 'payment_p1', value: '<%=uConfig.stcdata("buylist_ali_p1") %>' });
                } else {

                    modify_model.push({ name: '赠送比例', id: 'payment_p1', value: '<%=uConfig.stcdata("buylist_p1") %>' });
                }
                modify_model.push({ name: '订单金额', id: 'amount', value: '' });
                modify_model.push({ name: '开户姓名', id: 'payment_name', value: '', tip: '开户姓名' });
                modify_model.push({ name: '银行卡号', id: 'payment_bankid', value: '', tip: '银行卡号' });
                modify_model.push({ name: '开户银行', id: 'payment_bankname', value: '', tip: '开户银行' });
                modify_model.push({ name: '开户城市', id: 'payment_city', value: '', tip: '开户城市' });
                modify_model.push({ name: '生成数量', id: 'number', value: '1', type: 'hidden' });
                modify_model.push({ name: '继续确认', id: 'qr', value: '1', type: 'check', data: [["确认无误", "确认无误"]] });
            }

    modify_model.push({ name: '可见用户', id: 'show_users', value: '1', type: 'check', data: [["托号", "托号"], ["新注册用户", "新注册用户"], ["普通用户", "普通用户"]] });
    modify_model.push({ name: '不可见用户', id: 'unshow_users', value: '', type: 'textarea' });
    modify_model.push({ name: '订单置顶', id: 'istop', value: '1', type: 'check', data: [["发布后置顶", "发布后置顶"]] });

    edit({ aname: 'admin', title: '添加' + _actionName, action: _action, id: '', data: modify_model, time: 1 });
}
    </script>
    <script>

        var current_json = [];
        var pager_size = $(".s_pages").val();
        var pager_index = 0;
        var pager_key = "";

        $("#pagesize").val(pager_key);
        function setPageSize(obj) {
            pager_index = 0;
            pager_key = $("#search-keyword").val();
            opt = $(obj).val();
            getPager();
        }

        function searchList() {
            opt = $("#opt1").val();
            pager_index = 0;
            pager_key = $("#search-keyword").val();
            getPager();
        }

        function getParam() {
            return { limit: pager_size, p: pager_index, page: _action, keyword: pager_key, audit_state: $("#audit_state").val(), settle_state: $("#settle_state").val(), start_time: $("#start_time").val(), end_time: $("#end_time").val(), tarea: "<%=Request.QueryString["area"] %>", state: "<%=Request.QueryString["state"] %>", user_type: $("#user_type").val(), from_type: from_type }
        }

        function getPager() {

            $.ajax({
                type: "POST",
                url: "../api/admin.aspx?do=page",
                data: getParam(),
                datatype: "json",
                success: function (json) {
                    $("#list-display").html("");
                    if (json.code == 1) {
                        var data = json.data.list;
                        current_json = data;
                        for (var i = 0; i < data.length; i++) {


                            var modify_model = new Array();
                            modify_model.push({ name: 'modify_type', id: 'modify_type', value: 'p1', type: 'hidden' });
                            modify_model.push({ name: '赠送比例', id: 'p1', value: data[i].p1 });


                            var tr = "";
                            tr += "<tr>";
                            tr += ('<td class="action-cell-left"><input class="good_select" type="checkbox" data-id="' + data[i].id + '" id="checkid_' + data[i].id + '" />#' + data[i].id + '</td>');
                            tr += ("<td class=''>" + '<b style="color:#000;">' + data[i].orderId + '</b>' + (data[i].istop == "1" ? '<span style="background: #58595b;color: #eee;padding: 1px 10px;border-radius: 3px;margin-left: 4px;">置顶</span>' : '') + '<div style="color:gray;font-size:12px;">' + (data[i].sd_orderNo != '' ? '任务号：' + data[i].sd_orderNo : '') + (data[i].api_orderNo == "" ? "" : ('</div><div style="    font-size: 12px;">三方订单：' + data[i].api_orderNo + '</div>')) + (data[i].sell_orderNo == "" ? "" : ('</div><div style="    font-size: 12px;"><span style="color: #eee; padding: 1px 3px; margin-right: 3px; border-radius: 3px; font-size: 12px; background: linear-gradient(28deg, #ffffff, #e0ffff 100%); border: 1px solid #878181; color: #878181; font-weight: bold;">卖币</span>' + data[i].sell_orderNo + '</div>') + (data[i].sell_state == 1 ? '<div style="color: #3BBE6F;background: #F1FFF3;border: 1px solid #3BBE6F;margin-top: 8px;border-radius: 9px;padding: 2px 8px;text-align: center;font-weight: bold;font-size: 12px;display: flex;align-items: center;justify-content: center;width: 100px;"><svg t="" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="8943" width="12" height="12" style="margin-right: 2px;"><path d="M512 1023.012571C229.522286 1023.012571 0.548571 794.038857 0.548571 511.561143 0.548571 229.12 229.522286 0.146286 512 0.146286c282.477714 0 511.451429 228.937143 511.451429 511.414857C1023.195429 793.965714 794.331429 1022.756571 512 1023.012571z m0-952.576c-243.638857 0-441.124571 197.485714-441.124571 441.124572 0 243.638857 197.485714 441.124571 441.124571 441.124571 243.638857 0 441.124571-197.485714 441.124571-441.124571C952.868571 268.068571 755.529143 70.692571 512 70.436571z" fill="#28C78D" p-id="8944"></path><path d="M441.673143 712.228571a24.393143 24.393143 0 0 1-17.810286-7.497142l-187.501714-199.241143a27.648 27.648 0 0 1 0-37.522286 23.917714 23.917714 0 0 1 35.145143 0l170.166857 180.48L753.005714 320.365714a23.917714 23.917714 0 0 1 35.145143 0 27.648 27.648 0 0 1 0 37.485715l-328.155428 348.306285a24.393143 24.393143 0 0 1-17.810286 7.497143l-0.475429-1.389714z" fill="#28C78D" p-id="8945"></path><path d="M441.673143 717.824a29.549714 29.549714 0 0 1-23.405714-8.411429l-187.538286-200.192a33.28 33.28 0 0 1 0-46.884571 29.549714 29.549714 0 0 1 22.016-9.362286 29.988571 29.988571 0 0 1 22.052571 9.362286l165.961143 175.798857 305.627429-323.437714a29.988571 29.988571 0 0 1 22.052571-9.398857 29.988571 29.988571 0 0 1 22.016 9.398857 33.755429 33.755429 0 0 1 0 46.884571l-325.339428 347.794286a29.549714 29.549714 0 0 1-23.405715 8.448zM254.171429 466.102857a18.285714 18.285714 0 0 0-13.129143 6.107429 21.577143 21.577143 0 0 0 0 29.037714l187.501714 199.241143a18.285714 18.285714 0 0 0 26.733714 0l328.155429-348.306286a21.577143 21.577143 0 0 0 0-29.074286 18.285714 18.285714 0 0 0-13.165714-6.070857 18.285714 18.285714 0 0 0-13.531429 6.070857l-315.062857 333.787429-174.848-184.685714a18.285714 18.285714 0 0 0-13.129143-6.107429h0.475429z" fill="#28C78D" p-id="8946"></path></svg>已确认到账</div>' : '')) + '</div>' + "</td>");
                            tr += ("<td>" + ((data[i].user_type + "").replace(/reg/, '<span style="color: #1aa31e;background: #eee;padding: 2px 5px;margin: 0 3px;border-radius: 3px;font-weight: bold;">新注册用户</span>').replace(/th/, "托号").replace(/user/, "普通用户").replace(/super/, "高级用户")) + "</td>");
                            tr += ("<td>" + (data[i].sellersite != '' ? '<span style="font-size:12px;background: #4b4848;color: #eee;padding: 1px 3px;border-radius: 3px;margin-right: 3px;">' + data[i].sellersite + '-' + data[i].seller_sitename + '</span><span style="font-size:12px;color: #9b5d1b;">[' + data[i].seller_phone + ']</span>' : (data[i].apiid != '' ? '<span style="font-size:12px;background: #bde2f5;color: #2a2b2c;padding: 2px 5px;border-radius: 3px;margin-right: 3px;">站点对接</span>' : ((data[i].api_orderNo != '' ? '<span class="button-style style-ls">API</span>' : '') + (data[i].sd_orderNo != '' ? '<span class="button-style style-lvs">刷单</span>' : '') + '<span class="button-style' + (data[i].upload_servid == -99 ? "" : " style-hs") + '">' + (data[i].upload_servid == -99 ? "机器人" : data[i].up_serv_name) + '</span>'))) + "</td>");
                            tr += ("<td>" + '<span class="button-style style-hs">' + data[i].serv_name + '</span>' + "</td>");
                            tr += ("<td class=''>" + '<b style="color:#000;">' + data[i].user_payment_name + '</b>' + "</td>");
                            tr += ("<td class=''>" + parseFloat(data[i].reward_amount).toFixed(2) + "</td>");
                            tr += ("<td class='' " + ' ondblclick="openrightBoxpopup(\'accounts_baseinfo.aspx?id=' + data[i].userid + '&siteid=' + data[i].buyersite + '\')" style="cursor:pointer;"' + ">" + (data[i].buyersite != '' ? '<span style="font-size:12px;background: #4b4848;color: #eee;padding: 1px 3px;border-radius: 3px;margin-right: 3px;">' + data[i].buyersite + '-' + data[i].sitename + '</span><span style="font-size:12px;color: #9b5d1b;">[' + data[i].buyer_phone + ']</span>' : (data[i].group_name == '-' ? '' : "<span class='button-style style-js'>" + data[i].group_name + "</span>") + data[i].phone) + "</td>");
                            tr += ("<td style='display:flex;'>" + (data[i].state == 0 ? '<span class="button-style">待交易</span>' : data[i].state == 1001 ? '<span class="button-style style-zs">等待确认</span>' : data[i].state == 1000 ? '<span class="button-style style-ls">等待上传截图</span>' : data[i].state == 1 ? '<span class="button-style style-lvs">完成交易</span>' : data[i].state == -1 ? '<span class="button-style style-hs">订单超时</span>' : data[i].state == -2 ? '<span class="button-style style-hs">系统取消</span>' : data[i].state == -3 ? '<span class="button-style style-hs">结束</span>' : '<span class="button-style">其他</span>') + (data[i].state == 0 ? '<a style="display: flex;align-items: center;background: #efefee;padding: 0px 8px;margin-left: 6px;line-height: 19px;color: #bb8f12;" onclick="' + "get_action({name:'stop_buyOrder',data:{id:" + data[i].id + "},n:'结束订单',e:'结束订单 " + data[i].orderId + " " + "'});" + '"><svg t="1695565013896" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="6138" width="16" height="16"><path d="M780 229H244c-8.3 0-15 6.7-15 15v536c0 8.3 6.7 15 15 15h330.6L795 574.6V244c0-8.3-6.7-15-15-15z m-479.5 88.9c-14.7 0-26.6-11.9-26.6-26.6s11.9-26.6 26.6-26.6 26.6 11.9 26.6 26.6-12 26.6-26.6 26.6z m354.1 315.5c5.9 5.9 5.9 15.4 0 21.2-2.9 2.9-6.8 4.4-10.6 4.4s-7.7-1.5-10.6-4.4L512 533.2 390.6 654.6c-2.9 2.9-6.8 4.4-10.6 4.4s-7.7-1.5-10.6-4.4c-5.9-5.9-5.9-15.4 0-21.2L490.8 512 369.4 390.6c-5.9-5.9-5.9-15.4 0-21.2 5.9-5.9 15.4-5.9 21.2 0L512 490.8l121.4-121.4c5.9-5.9 15.4-5.9 21.2 0 5.9 5.9 5.9 15.4 0 21.2L533.2 512l121.4 121.4zM780 795c8.3 0 15-6.7 15-15v-49.1L730.9 795H780z m-91.6 0L795 688.4v-71.3L617.1 795z" fill="#FFBC00" p-id="6139"></path><path d="M780 199H244c-24.8 0-45 20.2-45 45v536c0 24.8 20.2 45 45 45h536c24.8 0 45-20.2 45-45V244c0-24.8-20.2-45-45-45z m15 581c0 8.3-6.7 15-15 15h-49.1l64.1-64.1V780z m0-91.6L688.4 795H617l178-177.9v71.3z m0-113.8L574.6 795H244c-8.3 0-15-6.7-15-15V244c0-8.3 6.7-15 15-15h536c8.3 0 15 6.7 15 15v330.6z" fill="#46287C" p-id="6140"></path><path d="M654.6 369.4c-5.9-5.9-15.4-5.9-21.2 0L512 490.8 390.6 369.4c-5.9-5.9-15.4-5.9-21.2 0-5.9 5.9-5.9 15.4 0 21.2L490.8 512 369.4 633.4c-5.9 5.9-5.9 15.4 0 21.2 2.9 2.9 6.8 4.4 10.6 4.4s7.7-1.5 10.6-4.4L512 533.2l121.4 121.4c2.9 2.9 6.8 4.4 10.6 4.4s7.7-1.5 10.6-4.4c5.9-5.9 5.9-15.4 0-21.2L533.2 512l121.4-121.4c5.8-5.8 5.8-15.3 0-21.2zM273.9 291.3a26.6 26.6 0 1 0 53.2 0 26.6 26.6 0 1 0-53.2 0z" fill="#FFFFFF" p-id="6141"></path></svg>&nbsp;结束</a>&nbsp;' : data[i].state == 1001 ? '<a style="display: flex;align-items: center;background: #eee;padding: 0px 8px;margin-left: 6px;line-height: 19px;color: #896b18;" onclick="' + "get_action({name:'return_buyOrder',data:{id:" + data[i].id + "},n:'退回订单',e:'退回订单 " + data[i].orderId + " 的交易<br>【订单金额】：" + data[i].amount + "<br>【交易截图】：" + '<a href=' + data[i].payimg + ' target=_blank><img src=' + data[i].payimg + ' width=25 height=25>' + "',inputs:[{id:'error_reason',name:'理由原因'}]});" + '"><svg t="1693065317535" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="6520" width="16" height="16"><path d="M596.2752 656.9472m-179.0976 0a179.0976 179.0976 0 1 0 358.1952 0 179.0976 179.0976 0 1 0-358.1952 0Z" fill="#FFBE0A" p-id="6521"></path><path d="M515.584 923.4432c-226.8672 0-411.4432-184.576-411.4432-411.4432s184.576-411.4432 411.4432-411.4432 411.4432 184.576 411.4432 411.4432-184.576 411.4432-411.4432 411.4432z m0-751.2064c-187.3408 0-339.7632 152.4224-339.7632 339.7632s152.4224 339.7632 339.7632 339.7632 339.7632-152.4224 339.7632-339.7632-152.4224-339.7632-339.7632-339.7632z" fill="#34332E" p-id="6522"></path><path d="M687.7184 542.72H347.1872c-19.8144 0-35.84-16.0256-35.84-35.84s16.0256-35.84 35.84-35.84h340.5312c19.8144 0 35.84 16.0256 35.84 35.84s-16.0256 35.84-35.84 35.84z" fill="#34332E" p-id="6523"></path></svg>&nbsp;退回</a>&nbsp;' + '<a style="display: flex;align-items: center;background: #eee;padding: 0px 8px;margin-left: 6px;line-height: 19px;color: #896b18;" onclick="' + "get_action({name:'cancel_buyOrder',data:{id:" + data[i].id + "},n:'撤销订单',e:'撤销订单 " + data[i].orderId + " 的交易<br>【订单金额】：" + data[i].amount + "'});" + '"><svg t="1693065317535" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="6520" width="16" height="16"><path d="M596.2752 656.9472m-179.0976 0a179.0976 179.0976 0 1 0 358.1952 0 179.0976 179.0976 0 1 0-358.1952 0Z" fill="#FFBE0A" p-id="6521"></path><path d="M515.584 923.4432c-226.8672 0-411.4432-184.576-411.4432-411.4432s184.576-411.4432 411.4432-411.4432 411.4432 184.576 411.4432 411.4432-184.576 411.4432-411.4432 411.4432z m0-751.2064c-187.3408 0-339.7632 152.4224-339.7632 339.7632s152.4224 339.7632 339.7632 339.7632 339.7632-152.4224 339.7632-339.7632-152.4224-339.7632-339.7632-339.7632z" fill="#34332E" p-id="6522"></path><path d="M687.7184 542.72H347.1872c-19.8144 0-35.84-16.0256-35.84-35.84s16.0256-35.84 35.84-35.84h340.5312c19.8144 0 35.84 16.0256 35.84 35.84s-16.0256 35.84-35.84 35.84z" fill="#34332E" p-id="6523"></path></svg>&nbsp;撤销</a>&nbsp;<a style="display: flex;align-items: center;background: #eee;padding: 0px 8px;margin-left: 6px;line-height: 19px;color: #896b18;" onclick="' + "get_action({name:'finish_buyOrder',data:{id:" + data[i].id + "},n:'确认交易',e:'确认 " + data[i].orderId + " 的交易<br>【订单金额】：" + data[i].amount + "（确认将完成订单）'});" + '"><svg t="1693065904309" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="6904" width="16" height="16"><path d="M608.256 565.3504m-179.0976 0a179.0976 179.0976 0 1 0 358.1952 0 179.0976 179.0976 0 1 0-358.1952 0Z" fill="#FFBE0A" p-id="6905"></path><path d="M635.5968 921.4464c-49.0496 0-92.9792-25.7536-117.4016-69.9392l-116.3264-210.3808-231.1168-103.1168C125.4912 517.8368 98.3552 474.4704 99.9424 424.96c1.5872-49.5616 31.4368-91.0848 77.9264-108.3392L730.624 111.2576a134.94784 134.94784 0 0 1 137.728 26.3168c37.5808 33.8432 53.0432 85.2992 40.448 134.2976l-141.1072 547.9424c-14.1312 54.8352-57.8048 93.3376-113.9712 100.5056-6.144 0.7168-12.1856 1.1264-18.1248 1.1264zM202.8544 383.7952c-28.0576 10.4448-31.0272 35.8912-31.232 43.4688-0.256 7.5776 1.0752 33.1776 28.416 45.3632l242.0736 108.032c7.1168 3.1744 13.0048 8.5504 16.7424 15.4112l122.112 220.8768c13.1072 23.6544 36.9152 35.7888 63.744 32.3584 26.8288-3.4304 46.848-21.0944 53.6064-47.2576L839.3728 253.952c6.0416-23.3984-1.0752-47.0016-19.0464-63.1296-17.92-16.1792-42.1376-20.7872-64.768-12.3904L202.8544 383.7952z" fill="#34332E" p-id="6906"></path><path d="M532.224 529.6128c-9.0624 0-18.176-3.4304-25.1392-10.2912a35.87584 35.87584 0 0 1-0.4096-50.688l152.064-154.4704a35.82976 35.82976 0 0 1 50.688-0.4096 35.87584 35.87584 0 0 1 0.4096 50.688l-152.064 154.4704a35.57376 35.57376 0 0 1-25.5488 10.7008z" fill="#34332E" p-id="6907"></path></svg>&nbsp;确认</a>' : '')

                                + (((data[i].state == 1000 && data[i].error_reason != '') || data[i].state == -1 || data[i].state == -3) && data[i].deal_servid == "" ? '<a style="display: flex;align-items: center;background: #efefee;padding: 0px 8px;margin-left: 6px;line-height: 19px;color: #9d65e1;" onclick="' + "get_action({name:'deal_buylist',data:{id:" + data[i].id + "},n:'订单已处理',e:'确认失效订单号 " + data[i].orderId + "已处理'});" + '"><svg t="1697806375657" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="3642" width="16" height="16"><path d="M512 106.666667c-223.573333 0-405.333333 181.76-405.333333 405.333333s181.76 405.333333 405.333333 405.333333 405.333333-181.76 405.333333-405.333333-181.76-405.333333-405.333333-405.333333zM682.666667 554.666667h-170.666667c-23.466667 0-42.666667-19.2-42.666667-42.666667V298.666667c0-23.466667 19.2-42.666667 42.666667-42.666667s42.666667 19.2 42.666667 42.666667v170.666666h128c23.466667 0 42.666667 19.2 42.666666 42.666667s-19.2 42.666667-42.666666 42.666667z" fill="#9254DE" p-id="3643"></path><path d="M799.232 226.218667a405.077333 405.077333 0 0 1 107.648 194.133333L420.352 906.88a405.077333 405.077333 0 0 1-194.133333-107.648l256.426666-256.341333c7.68 7.253333 18.005333 11.776 29.354667 11.776h170.666667c23.466667 0 42.666667-19.2 42.666666-42.666667s-19.2-42.666667-42.666666-42.666667h-126.506667z" fill="#9D65E1" p-id="3644"></path><path d="M606.08 117.674667a405.162667 405.162667 0 0 1 193.237333 108.629333l-243.114666 242.986667L554.666667 469.333333V298.666667c0-23.466667-19.2-42.666667-42.666667-42.666667s-42.666667 19.2-42.666667 42.666667v213.333333c0 12.117333 5.12 23.125333 13.312 30.890667l-256.341333 256.426666a405.162667 405.162667 0 0 1-108.629333-193.237333z" fill="#A776E4" p-id="3645"></path><path d="M512 106.666667c32.426667 0 63.914667 3.84 94.165333 11.050666L117.717333 606.165333A405.290667 405.290667 0 0 1 106.666667 512c0-223.573333 181.76-405.333333 405.333333-405.333333z" fill="#BD98EB" p-id="3646"></path></svg>&nbsp;待</a>' : '')

                                + (data[i].deal_servid != "" ? '<span style="font-weight: bold;font-size: 13px;text-shadow: 5px 5px 5px #7978627d;color: #58910c;margin-left: 5px;">已处理</span>' : '')

                                + "</td>");
                            tr += ("<td class=''>" + '<span class="button-style style-zs" style="cursor:pointer;" onclick="show_details(' + data[i].id + ')">' + data[i].payment_type + '</span>' + "</td>");
                            tr += ("<td class=''>" + data[i].amount + (data[i].payment_type != "USDT" && data[i].usdt_price != "" ? "<div><span style='font-size:12px;color:gray;'>≈" + (data[i].amount / data[i].usdt_price).toFixed(2) + "</span></div>" : "") + (data[i].p1 != "" ? '<div style="border-radius: 10px;font-size: 12px;font-weight: bold;margin-top: 4px;display: inline-block;padding: 0 5px;background: #61736b;color: yellow;margin-left: 6px;cursor: pointer;" ' + "onclick='edit(" + edtext({ aname: 'admin', title: _actionName + "修改", action: _action, id: data[i].id, data: modify_model }) + ")'" + '>送' + data[i].p1 + '%</div>' : '') + "</td>");
                            tr += ("<td class=''>" + (data[i].payimg == '' ? '<span style="color:gray;">无</span>' : (data[i].upload_type == 'video' ? '<a onclick="toggleImgFullScreen(\'' + data[i].payimg + '\')" style="color: #1b0a75;text-shadow: 5px 5px 5px #0000005c;">查看视频</>' : '<img src="' + data[i].payimg + '" width="25" height="25" onclick="toggleImgFullScreen(\'' + data[i].payimg + '\')">')) + '<div style="font-size:12px;color:gray;">' + data[i].error_reason + '</div>' + "</td>");
                            tr += ("<td class=''>" + (data[i].show_users == "" ? "<span style='color:gray;'>所有人</span>" : data[i].show_users.replace(/reg/, '<span style="color: #1aa31e;background: #eee;padding: 2px 5px;margin: 0 3px;border-radius: 3px;font-weight: bold;">新注册用户</span>').replace(/th/, "<span style='color: #a3541a;background: #eee;padding: 2px 5px;margin: 0 3px;border-radius: 3px;font-weight: bold;'>托号</span>").replace(/user/, "普通用户")) + "</td>");
                            tr += ("<td style='color: #2a2b2c!important;font-size: 12px;'>" + (data[i].unshow_users == '' ? '<span style="color:gray;">未屏蔽</span>' : data[i].unshow_users.replace(/\n/g, '<br>')) + "</td>");
                            tr += ("<td class=''>" + data[i].create_time + "</td>");
                            tr += ("<td class=''>" + data[i].finish_time + "</td>");
                            tr += ("<td class=''>" + (data[i].api_orderNo == "" ? '<span style="color:gray;">免推送</span>' : ((data[i].notifyOrderStatus == "" ? "<span style='color:gray;'>无</span>" : data[i].notifyOrderStatus) + "<a onclick=\"" + "get_action({name:'apibuy_status_notify',data:{id:" + data[i].id + ",for:'notifyFromTaofanke'},n:'状态推送',e:'单号" + data[i].orderId + "进行状态推送'});" + "\"" + '  style="color:#3c3ceb!important;">&nbsp;推送</a>')) + "</td>");

                            var modify_model = new Array();
                            //modify_model.push({ name: '商品图片', id: 'imgurl', data: [data[i].imgurl], type: 'img' });

                            //tr += ("<td>"
                            //    + "<button type='button' class='el-button  el-button--default el-button--mini' onclick='edit(" + edtext({ aname: 'admin', title: _actionName + "修改", action: _action, id: data[i].id, data: modify_model }) + ")'><span><i class='fa fa-edit'></i></span></button>"
                            //    + "<button type='button' class='el-button  el-button--default el-button--mini' onclick=\"event_create({'aname':'admin','name':'" + _action + "','event':'delete','id':" + data[i].id + "})\"><span><i class='fa fa-remove'></i></span></button>"
                            //    + "</td>");




                            //tr += ("<td class='action-cell '>"
                            //    + ' <div class="card-toolbar">'
                            //    + '&nbsp;<a class="card-button common-button" ' + "onclick=\"event_create({'aname':'admin','name':'" + _action + "','event':'delete','id':" + data[i].id + "})\"" + '><svg t="1688237896673" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="1699" width="16" height="16" style="position:relative;top:3px;"><path d="M612.693333 170.666667l8.533334 42.666666H402.773333l8.533334-42.666666h201.386666z m-293.632 25.941333a86.186667 86.186667 0 0 0-1.706666 16.725333H170.666667a42.666667 42.666667 0 0 0 0 85.333334h682.666666a42.666667 42.666667 0 1 0 0-85.333334h-146.730666c0-5.504-0.554667-11.093333-1.706667-16.725333l-8.533333-42.666667A85.333333 85.333333 0 0 0 612.778667 85.333333H411.306667a85.333333 85.333333 0 0 0-83.712 68.608l-8.533334 42.666667zM426.666667 469.333333a42.666667 42.666667 0 0 0-42.666667 42.666667v170.666667a42.666667 42.666667 0 1 0 85.333333 0v-170.666667a42.666667 42.666667 0 0 0-42.666666-42.666667z m128 42.666667a42.666667 42.666667 0 1 1 85.333333 0v170.666667a42.666667 42.666667 0 1 1-85.333333 0v-170.666667zM255.402667 384A42.666667 42.666667 0 0 1 298.666667 426.069333L301.738667 640H301.653333a42.666667 42.666667 0 1 1-85.290666-2.176l-2.986667-210.56A42.666667 42.666667 0 0 1 255.36 384z m51.925333 396.586667h0.042667A103.381333 103.381333 0 0 0 406.698667 853.333333h210.602666c54.186667 0 98.56-40.789333 103.210667-92.757333L725.333333 426.069333a42.666667 42.666667 0 0 1 85.333334 1.194667l-4.864 337.365333-0.128 1.365334C798.08 863.872 715.562667 938.666667 617.301333 938.666667H406.698667a188.885333 188.885333 0 0 1-180.096-130.389334 42.666667 42.666667 0 0 1 80.725333-27.648z" fill="#14101C" p-id="1700"></path></svg>&nbsp;删除</a>'
                            //    //+ '<div class="card-popup">'

                            //    //+ "<div class='menu-item px-3'><a onclick='edit(" + edtext({ aname: 'admin', title: _actionName + "修改", action: _action, id: data[i].id, data: modify_model }) + ")' class='menu-link px-3'>编辑</a></div>"

                            //    //+ "<div class='menu-item px-3'><a onclick=\"event_create({'aname':'admin','name':'" + _action + "','event':'delete','id':" + data[i].id + "})\" class='menu-link px-3'>删除</a></div>"

                            //    //+ '</div>'
                            //    + '</div>');


                            tr += "</tr>";
                            $("#list-display").append(tr)


                            var __temp = data[i];
                            __temp.modify_model = modify_model;
                            _bindEvent(__temp);

                            //$('button[edit-' + __temp.id + ']').on("click", function () {
                            //    console.log('attrs', $(this).closest('tr').html(), __temp.modify_model);

                            //    edit({ aname: 'admin', title: '修改' + _actionName, action: _action, id: __temp.id, data: __temp.modify_model });
                            //})



                        }
                        $(".page_size").html(pager_size);
                        $(".page_count").html(json.data.count);
                        $(".cpages").html(json.data.html);

                        $(".total_amount").html(parseFloat(json.total_amount).toFixed(2));
                        $(".total_number").html(json.total_number);


                        getdata();

                        pop_mp3tip();

                    } else {

                    }
                },
                error: function () {
                }
            });
        }
        getPager();

        function _bindEvent(__temp) {
            $('button[edit-' + __temp.id + ']').on("click", function () {
                console.log('attrs', $(this).closest('tr').html(), __temp.modify_model);

                edit({ aname: 'admin', title: '修改' + _actionName, action: _action, id: __temp.id, data: __temp.modify_model });
            })
        }

        function _pt(i) {
            pager_index = i;
            getPager();
        }
    </script>

    <script>
        layui.use(['form', 'layedit', 'laydate'], function () {
        });
    </script>

    <script>
        function update_select(state) {
            event_create({ 'aname': 'admin', 'name': _action, 'event': 'delete', 'id': select_all_id(), 'state': state });
        }


        function report_exl() {
            export_data(getParam());
        }
    </script>

    <script>
        var click_ids = {};
        function checkIsSelect(obj) {
            if (obj.prop('checked')) {
                return true;
            } else {
                return false;
            }
        }
        function goods_click(t) {
            t = $(t);
            var obj = t.find(".good_select");
            obj.prop("checked", !checkIsSelect(obj));

        }

        function select_all(obj) {
            $(".good_select").prop("checked", checkIsSelect($(obj)));
        }

        function select_all_id() {
            var ids = "";
            $("#list-display tr").each(function () {
                var select_obj = $(this).find(".good_select");
                if (checkIsSelect(select_obj)) {
                    if (ids != "") {
                        ids += ",";
                    }
                    ids += select_obj.data("id");
                }
            });
            return ids;
        }
    </script>

    <audio id="myAudio" controls style="display: none;">
        <source src="../static/files/新订单.mp3" type="audio/mpeg">
        Your browser does not support the audio element.
    </audio>


    <script>
        var autoTimer = 0;
        var lastList = null;

        var set_autoAlarm = function (e) {
            if (checkIsSelect($(e))) {
                console.log('启动');
                lastList = null;

                autoTimer = setInterval(function () {


                    getPager();

                }, 5000);

            } else {
                console.log('关闭');
                clearInterval(autoTimer);
                autoTimer = 0;
            }
        }

        var pop_mp3tip = function (json) {
            if (autoTimer == 0) {
                return;
            }



            for (var i = 0; i < current_json.length; i++) {
                if (current_json[i].state == 1001) {
                    play_mp3tip();
                    break;
                }
            }


            //if (lastList == null) {
            //    lastList = [];
            //    for (var i = 0; i < current_json.length; i++) {
            //        if (current_json[i].state == 1001) {
            //            lastList.push(current_json[i].id);
            //        }
            //    }
            //}

            ////console.log('lastList', lastList);
            //for (var i = 0; i < current_json.length; i++) {
            //    if (current_json[i].state == 1001) {
            //        if (lastList.indexOf(current_json[i].id) == -1) {
            //            lastList.push(current_json[i].id);
            //            play_mp3tip();
            //        }
            //    }
            //}
            //console.log('newList', lastList);
        }

        var mp3_audio = document.getElementById("myAudio"); // 获取音频元素
        var play_mp3tip = function () {
            mp3_audio.play();
        }

        var show_details = function (id, type) {
            var details_info = "";
            for (var i = 0; i < current_json.length; i++) {
                var obj = current_json[i];

                if (obj.id == id) {




                    details_info += ' <div style="display: flex; margin: 20px 0; padding-bottom: 20px; border-bottom: 1px solid #eee;">            <div>                <b>收款方式：</b>            </div>            <div style="margin-left: 10px;">                ' + obj.payment_type + '            </div>        </div>';
                    if (obj.payment_type == "银行卡") {
                        details_info += ' <div style="display: flex; margin: 20px 0; padding-bottom: 20px; border-bottom: 1px solid #eee;">            <div>                <b>商家名称：</b>            </div>            <div style="margin-left: 10px;">                ' + obj.payment_name + '            </div>        </div>';
                    } else {
                        details_info += ' <div style="display: flex; margin: 20px 0; padding-bottom: 20px; border-bottom: 1px solid #eee;">            <div>                <b>收款姓名：</b>            </div>            <div style="margin-left: 10px;">                ' + obj.payment_name + '            </div>        </div>';
                    }

                    if (obj.payment_type == "网银" || obj.payment_type == "支付宝") {
                        details_info += ' <div style="display: flex; margin: 20px 0; padding-bottom: 20px; border-bottom: 1px solid #eee;">            <div>                <b>赠送比例：</b>            </div>            <div style="margin-left: 10px;">                ' + obj.p1 + '%            </div>        </div>';
                        details_info += ' <div style="display: flex; margin: 20px 0; padding-bottom: 20px; border-bottom: 1px solid #eee;">            <div>                <b>收款卡号：</b>            </div>            <div style="margin-left: 10px;">                ' + obj.payment_bankid + '            </div>        </div>';
                        details_info += ' <div style="display: flex; margin: 20px 0; padding-bottom: 20px; border-bottom: 1px solid #eee;">            <div>                <b>开户行：</b>            </div>            <div style="margin-left: 10px;">                ' + obj.payment_bankname + '            </div>        </div>';
                    } else if (obj.payment_type == "银行卡") {
                        details_info += ' <div style="display: flex; margin: 20px 0; padding-bottom: 20px; border-bottom: 1px solid #eee;">            <div>                <b>密信ID：</b>            </div>            <div style="margin-left: 10px;">                ' + obj.payment_city + '            </div>        </div>';
                        details_info += ' <div style="display: flex; margin: 20px 0; padding-bottom: 20px; border-bottom: 1px solid #eee;">            <div>                <b>金额区间：</b>            </div>            <div style="margin-left: 10px;">                ' + obj.payment_bankid + '            </div>        </div>';
                        details_info += ' <div style="display: flex; margin: 20px 0; padding-bottom: 20px; border-bottom: 1px solid #eee;">            <div>                <b>温馨提示：</b>            </div>            <div style="margin-left: 10px;">                ' + obj.payment_bankname + '            </div>        </div>';
                    } else {
                        details_info += ' <div style="display: flex; margin: 20px 0; padding-bottom: 20px; border-bottom: 1px solid #eee;">            <div>                <b>' + obj.payment_type + '账号：</b>            </div>            <div style="margin-left: 10px;">                ' + obj.payment_bankid + '            </div>        </div>';
                    }
                    details_info += ' <div style="display: flex; margin: 20px 0; padding-bottom: 20px; border-bottom: 1px solid #eee;">            <div>                <b>交易金额：</b>            </div>            <div style="margin-left: 10px;">                ' + obj.amount + '            </div>        </div>';


                    details_info = '<div style="display:flex;">' + '<div style="width:50%;font-size: 18px;">' + details_info + '</div>' +

                        '<div style="width:50%;padding:0 10px;text-align:center;">' + (obj.payimg == '' ? '<span style="color:gray;"></span>' : obj.upload_type == 'video' ? '<video src="' + obj.payimg + '" alt="Full Screen Video" class="fullscreen-video" controls autoplay style="max-width:380px;max-height:90vh;"></video>' : '<img src="' + obj.payimg + '" style="max-width:380px;max-height:90vh;">') + '</div>'

                        + '</div>';

                    var modelid = _modal('收款信息', details_info);

                    $('head').append($("<style> [modelid='" + modelid + "'] .modal-body{   background: #CCE8CF;     }  @media (min-width:768px) {            [modelid='" + modelid + "'] {                width: 90%!important;    max-width: 800px;        }        }</style>"));

                    break;
                }
            }
        }


        var create_order = function (id) {

            _modal("创建订单", "<div id='model-page' aid='" + id + "'>" + $("#buy_page").html() + "</div>");

            var details_info = "";
            for (var i = 0; i < current_json.length; i++) {
                var obj = current_json[i];
                if (obj.id == id) {
                    //$('#model-page .bank_item').hide();
                    //$('#model-page').attr();

                    $('#model-page .buyer_type').on('change', function () {
                        var name = $(this).val();
                        if (name == "银行卡") {
                            $('#model-page .bank_item').show();
                        } else {
                            $('#model-page .bank_item').hide();
                        }
                    });

                    $('#model-page .buyer_number').val((obj.total_amount - obj.deal_amount));

                    if (obj.accept_separate != 1) {
                        $('#model-page .buyer_number').css({ "background": "#f1f1f1" }).attr({ "disabled": "disabled" });
                    }

                    break;
                }
            }
        }

        var new_order = function () {
            var post_data = {
                id: $('#model-page').attr("aid"),
                buyer_type: $('#model-page .buyer_type').val(),
                buyer_amount: $('#model-page .buyer_number').val(),
                buyer_name: $('#model-page .buyer_name').val(),
                buyer_bankname: $('#model-page .buyer_bankname').val(),
                buyer_bankid: $('#model-page .buyer_bankid').val()
            }

            $.ajax({
                type: "POST",
                url: "../Api/admin.aspx?do=new_order",
                data: post_data,
                datatype: "json",
                success: function (data) {
                    if (data.code == 1) {
                        layer.msg(data.msg, { icon: 1, time: 1000 }, function () {
                            $('#myModal').modal('hide');
                            getPager();
                        });
                    } else {
                        layer.msg(data.msg, { icon: 2, time: 1500 });
                    }
                },
                error: function () {
                    layer.msg("抱歉，操作有误", { icon: 2, time: 1500 });
                }
            });

        }

    </script>

    <style>
        .buy_ ist {
            di play flex;
            a gn-items: center;
            font-weight: bold;
            rgin-top 30px;
            .buy_list e;

        {
            width: 00px;
            e shrink: 0;
        }

        .buy_list .item w d
        }


        .buy_list item in width
    </style>
    <div style="background: #fff; padding: 18px; display: none;" id="buy_page">

        <div class="buy_list">
            <div class="name">
                支付方式：
           
            </div>
            <div class="item">
                <select class="form-control buyer_type">
                    <option>银行卡</option>
                    <option>支付宝</option>
                    <option>微信</option>
                </select>
            </div>
        </div>

        <div class="buy_list">
            <div class="name" style="color: blue;">
                购买数量：           
            </div>
            <div class="item">
                <input class="form-control buyer_number" />
            </div>
        </div>

        <div class="buy_list">
            <div class="name">
                姓名：           
            </div>
            <div class="item">
                <input class="form-control buyer_name" />
            </div>
        </div>

        <div class="buy_list bank_item">
            <div class="name">
                开户行：           
            </div>
            <div class="item">
                <input class="form-control buyer_bankname" />
            </div>
        </div>

        <div class="buy_list">
            <div class="name">
                账号：           
            </div>
            <div class="item">
                <input class="form-control buyer_bankid" />
            </div>
        </div>

        <a class="btn btn-success" onclick="new_order()" style="margin-top: 24px; margin-left: 100px;">
            <svg t="*************" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="4127" width="16" height="16">
                <path d="M512 170.666667a341.333333 341.333333 0 0 0-290.133333 521.216 42.666667 42.666667 0 0 1-72.533334 45.013333A424.874667 424.874667 0 0 1 85.333333 512C85.333333 276.352 276.352 85.333333 512 85.333333s426.666667 191.018667 426.666667 426.666667-191.018667 426.666667-426.666667 426.666667a424.874667 424.874667 0 0 1-234.666667-70.314667 42.666667 42.666667 0 1 1 46.933334-71.210667A341.333333 341.333333 0 1 0 512 170.666667z m222.165333 264.832a42.666667 42.666667 0 0 0-60.330666-60.330667L469.333333 579.669333l-119.168-119.168a42.666667 42.666667 0 0 0-60.330666 60.330667l149.333333 149.333333a42.666667 42.666667 0 0 0 60.330667 0l234.666666-234.666666z" fill="#14101C" p-id="4128"></path></svg>&nbsp;创建订单</a>

    </div>



    <script>
        // 传入格式为 "yyyy-MM-dd HH:mm:ss" 的时间戳
        function countdownToTime(timestamp) {
            var targetDate = new Date(timestamp);
            var currentDate = new Date();
            var timeDiff = targetDate - currentDate;

            if (timeDiff <= 0) {
                return '已超时';
            }

            var hours = Math.floor(timeDiff / (1000 * 60 * 60));
            var minutes = Math.floor((timeDiff % (1000 * 60 * 60)) / (1000 * 60));
            var seconds = Math.floor((timeDiff % (1000 * 60)) / 1000);

            var formattedTime = hours + ':' + minutes.toString().padStart(2, '0') + ':' + seconds.toString().padStart(2, '0');
            return formattedTime;
        }
        var interval = 0


        function getdata() {
            clearInterval(interval);
            interval = setInterval(function () {
                $('td[expiretime]').each(function () {
                    var expiretime = $(this).attr('expiretime');
                    var result = countdownToTime(expiretime);
                    if (result == "已超时") {
                        result = "<span style='color:gray;'>已超时</span>";
                        $(this).removeAttr("expiretime");
                    }
                    $(this).html(result);
                })
            }, 1000); // 更新频率每秒钟
        }

    </script>

</asp:Content>



<%@ Page Title="卖币列表" Language="C#" MasterPageFile="~/serv/m.master" AutoEventWireup="true" CodeFile="sell_dating_list.aspx.cs" Inherits="w_index" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="Server">
    <style>
        .buy1 .button-style {
            border: 0px;
            background: none;
        }

        .design-button-full {
            background: #3255FF;
            color: #fff!important;
            padding: 3px 10px;
            border-radius: 3px;
            font-size: 12px;
            font-weight: bold;
        }

        .design-button-hollow {
            border: 1px solid #3255FF;
            color: #3255FF!important;
            padding: 3px 10px;
            border-radius: 3px;
            font-size: 12px;
            font-weight: bold;
        }

        .design-button-common {
            background: #E0E6FF;
            color: #556cdf!important;
            padding: 3px 10px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
        }
    </style>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="Server">
    <div class="ibox float-e-margins">
        <div class="ibox-title">
            <h5>卖币列表</h5>
        </div>
        <div class="ibox-content">
            <div class="row layui-form">

                <div class="col-md-3">
                    <div class="form-group form-buttom">
                        <input type="text" id="search-keyword" name="ordername" class="form-control" placeholder="请输入搜索的信息">
                    </div>
                </div>

                <div class="col-md-3">
                    <div class="form-group form-buttom" id="twoInputs">
                        <input type="text" id="start_time" name="ordername" class="form-control" placeholder="开始时间" style="width: 48%; display: inline-block;" value="" autocomplete="off">
                        <input type="text" id="end_time" name="ordername" class="form-control" placeholder="结束时间" style="width: 48%; display: inline-block;" autocomplete="off" value="">

                        <script>
                            $(document).ready(function () {
                                $('#start_time').datepicker({
                                    format: 'yyyy-mm-dd',
                                    autoclose: true
                                });

                                $('#end_time').datepicker({
                                    format: 'yyyy-mm-dd',
                                    autoclose: true
                                });
                            });
                        </script>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-group form-buttom">

                        <a class="btn btn-success" onclick="searchList()">
                            <svg t="1688237921441" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="1949" width="16" height="16">
                                <path d="M361.130667 658.688a256 256 0 1 1 282.794666-44.672 42.496 42.496 0 0 0-10.368 14.933333c-0.853333 0.682667-1.621333 1.450667-2.389333 2.218667a42.666667 42.666667 0 0 0 0 60.330667l170.666667 170.666666a42.666667 42.666667 0 0 0 60.330666-60.330666l-142.762666-142.762667a341.333333 341.333333 0 1 0-169.344 99.242667 42.666667 42.666667 0 0 0-20.224-82.901334 254.762667 254.762667 0 0 1-168.704-16.725333z" fill="#14101C" p-id="1950"></path></svg>
                            查询</a>
                        <a class="btn btn-success" onclick="javascript:location.href=location.href;">
                            <svg t="1688237949108" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2211" width="16" height="16">
                                <path d="M512 192a298.24 298.24 0 0 0-245.162667 128 42.666667 42.666667 0 1 1-69.973333-48.810667 384.042667 384.042667 0 0 1 697.173333 180.48l36.608-34.645333a42.666667 42.666667 0 0 1 58.709334 61.952l-112.64 106.666667a42.666667 42.666667 0 0 1-71.253334-38.912A298.666667 298.666667 0 0 0 512 192z m0.170667 640a298.24 298.24 0 0 0 245.162666-128 42.666667 42.666667 0 1 1 69.973334 48.810667 384.042667 384.042667 0 0 1-697.173334-180.48l-36.650666 34.645333a42.666667 42.666667 0 0 1-58.666667-61.952l112.64-106.666667a42.666667 42.666667 0 0 1 71.253333 38.912 298.666667 298.666667 0 0 0 293.418667 354.730667z" fill="#14101C" p-id="2212"></path></svg>
                            刷新</a>


                        <a class="btn btn-success" onclick="report_exl()">
                            <svg t="1695489540076" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="5479" width="16" height="16">
                                <path d="M686.08 989.866667H165.546667c-51.2 0-92.16-40.96-92.16-92.16V126.293333c0-51.2 40.96-92.16 92.16-92.16h573.44c6.826667 0 11.946667 1.706667 17.066666 6.826667l187.733334 174.08c5.12 5.12 8.533333 11.946667 8.533333 18.773333v256c0 13.653333-11.946667 25.6-25.6 25.6s-25.6-11.946667-25.6-25.6V244.053333L728.746667 85.333333h-563.2C141.653333 85.333333 122.88 104.106667 122.88 126.293333v769.706667c0 22.186667 18.773333 40.96 40.96 40.96h520.533333c13.653333 0 25.6 11.946667 25.6 25.6S699.733333 989.866667 686.08 989.866667z" fill="#333333" p-id="5480"></path><path d="M919.893333 259.413333h-121.173333c-51.2 0-92.16-40.96-92.16-92.16V59.733333c0-13.653333 11.946667-25.6 25.6-25.6s25.6 11.946667 25.6 25.6v105.813334c0 22.186667 18.773333 40.96 40.96 40.96h121.173333c13.653333 0 25.6 11.946667 25.6 25.6s-11.946667 27.306667-25.6 27.306666zM500.053333 305.493333H208.213333c-13.653333 0-25.6-11.946667-25.6-25.6s11.946667-25.6 25.6-25.6h291.84c13.653333 0 25.6 11.946667 25.6 25.6s-11.946667 25.6-25.6 25.6zM616.106667 575.146667H208.213333c-13.653333 0-25.6-11.946667-25.6-25.6s11.946667-25.6 25.6-25.6h407.893334c13.653333 0 25.6 11.946667 25.6 25.6s-11.946667 25.6-25.6 25.6zM380.586667 440.32H208.213333c-13.653333 0-25.6-11.946667-25.6-25.6s11.946667-25.6 25.6-25.6h172.373334c13.653333 0 25.6 11.946667 25.6 25.6s-11.946667 25.6-25.6 25.6z" fill="#333333" p-id="5481"></path><path d="M914.773333 779.946667H600.746667c-13.653333 0-25.6-11.946667-25.6-25.6s11.946667-25.6 25.6-25.6h314.026666c13.653333 0 25.6 11.946667 25.6 25.6s-11.946667 25.6-25.6 25.6z" fill="#333333" p-id="5482"></path><path d="M831.146667 873.813333c-6.826667 0-13.653333-1.706667-18.773334-6.826666-10.24-10.24-10.24-25.6 0-35.84l75.093334-75.093334-75.093334-75.093333c-10.24-10.24-10.24-25.6 0-35.84 10.24-10.24 25.6-10.24 35.84 0l93.866667 93.866667c5.12 5.12 6.826667 11.946667 6.826667 18.773333s-3.413333 13.653333-6.826667 18.773333L848.213333 870.4c-5.12 1.706667-11.946667 3.413333-17.066666 3.413333z" fill="#333333" p-id="5483"></path><path d="M694.613333 824.32h-529.066666c-37.546667 0-68.266667-30.72-68.266667-68.266667V121.173333c0-37.546667 30.72-68.266667 68.266667-68.266666h529.066666c37.546667 0 68.266667 30.72 68.266667 68.266666v634.88c0 37.546667-30.72 68.266667-68.266667 68.266667z" fill="#333333" opacity=".3" p-id="5484"></path></svg>导出</a>

                    </div>
                </div>
            </div>

            <div style="display: flex; flex-wrap: wrap; margin-bottom: 20px;">
                <div class="timesel_lab">全部</div>
                <div class="timesel_lab">今天</div>
                <div class="timesel_lab">昨天</div>
                <div class="timesel_lab">本周</div>
                <div class="timesel_lab">上周</div>
                <div class="timesel_lab">本月</div>
                <div class="timesel_lab">上月</div>
            </div>



            <div style="margin-bottom: 20px;">

                <input type="checkbox" onclick="set_autoAlarm(this)" />&nbsp;自动监控订单（5s刷新一次）
                
                <div style="display: inline-block; font-weight: bold; background: #e5ded887; padding: 2px 18px; border-radius: 5px; color: #000; font-size: 20px;">
                    <span class="total_amount">0</span> 总金额<span style="font-size: 18px; background: linear-gradient(152deg, #ff5412d4, #f3cdbe); color: #fff8e0; border-radius: 6px; font-weight: 100; padding: 2px 9px; margin-left: 5px; font-weight: bold;"><span class="total_number">0</span> 单</span>
                </div>
            </div>


            <div class="table-responsive">
                <table class="table">
                    <thead>
                        <tr role="head">
                            <th sort="true">订单号</th>
                            <th sort="true">锁定人</th>
                            <th sort="true">用户</th>
                            <th sort="true">交易金额</th>
                            <th sort="true">收款方式</th>
                            <th sort="true">大厅单号</th>
                            <th sort="true">状态</th>
                            <th sort="true">创建</th>
                            <th sort="true">完成</th>
                        </tr>
                    </thead>
                    <tbody id="list-display">
                    </tbody>
                </table>
            </div>



            <div class="layui-table-page">
                <div id="layui-table-page1">
                    <div class="layui-box layui-laypage layui-laypage-default" id="layui-laypage-1">
                        <div style="display: inline-block;" class="cpages">
                            <!--page grow -->
                        </div>
                        <span class="layui-laypage-skip">到第<input type="text" min="1" value="1" class="layui-input target-page" />页
                            <button type="button" class="layui-laypage-btn" onclick="javascript:pager_index=$('.target-page').val()-1;getPager();">确定</button></span>
                        <span class="layui-laypage-count">共 <span class="page_count"></span>条</span>
                        <span class="layui-laypage-limits">
                            <select class="s_pages" lay-ignore="" onchange="javascript:pager_size=$(this).val();getPager();">
                                <option value="10">10 条/页</option>
                                <option value="20" selected="">20 条/页</option>
                                <option value="30">30 条/页</option>
                                <option value="50">50 条/页</option>
                                <option value="100">100 条/页</option>
                                <option value="200">200 条/页</option>
                                <option value="500">500 条/页</option>
                                <option value="1000">1000 条/页</option>
                                <option value="2000">2000 条/页</option>
                            </select></span>
                    </div>
                </div>
            </div>

        </div>
    </div>
    <script>
        var _action = 'sell_dating_list';
    </script>
    <script>
        function openuser() {
            var cg = [];

            cg.push({ name: '黑名单', id: 'black_list', value: '', type: 'textarea' });

            edit({ aname: 'admin', title: '黑名单添加', action: _action, id: '', data: cg });
        }
    </script>
    <script>

        var current_json = [];
        var pager_size = $(".s_pages").val();
        var pager_index = 0;
        var pager_key = "";

        var opt = get_param("opt");
        var opt2 = get_param("fid");

        $("#pagesize").val(pager_key);
        function setPageSize(obj) {
            pager_index = 0;
            pager_key = $("#search-keyword").val();
            opt = $(obj).val();
            getPager();
        }

        function searchList() {
            opt = $("#opt1").val();
            pager_index = 0;
            pager_key = $("#search-keyword").val();
            getPager();
        }

        function getParam() {
            return { limit: pager_size, p: pager_index, page: _action, keyword: pager_key, opt: opt, opt2: opt2, serv_id: get_param("serv_id"), start_time: $("#start_time").val(), end_time: $("#end_time").val(), state: '<%=Request.QueryString["state"] %>', uid: '<%=Request.QueryString["userid"] %>', type: $("#typeid").val(), newuser: "<%=Request.QueryString["newuser"] %>" };
        }

        function getPager() {

            $.ajax({
                type: "POST",
                url: "../api/admin.aspx?do=page",
                data: getParam(),
                datatype: "json",
                success: function (json) {
                    $("#list-display").html("");
                    if (json.code == 1) {
                        var data = json.data.list;
                        current_json = data;
                        for (var i = 0; i < data.length; i++) {
                            var tr = "";
                            tr += "<tr style='" + (data[i].orderLock == 1 ? "background:#f5f5f5;" : "") + "'>";
                            tr += ("<td>" + data[i].orderNo + '<div style="color:gray;font-size:12px;"># ' + data[i].id + '</div>' + "</td>");



                            tr += ("<td>" + (data[i].apiid != '' ? '<span style="font-size:12px;background: #bde2f5;color: #2a2b2c;padding: 2px 5px;border-radius: 3px;margin-right: 3px;">站点对接</span>' : '<span class="button-style style-hs">' + data[i].lock_user + '</span>') + "</td>");
                            tr += ("<td class='' " + ' ondblclick="openrightBoxpopup(\'accounts_baseinfo.aspx?id=' + data[i].userid + '\')" style="cursor:pointer;"' + ">" + (data[i].buyersite != '' ? '<span style="font-size:12px;background: #4b4848;color: #eee;padding: 1px 3px;border-radius: 3px;margin-right: 3px;">' + data[i].buyersite + '-' + data[i].sitename + '</span><span style="font-size:12px;color: #9b5d1b;">[' + data[i].buyer_phone + ']</span>' : ('<span style="background: #f3ecec;color: #956b0a;font-size: 12px;border-radius: 3px;padding: 1px 4px;margin-right: 5px;font-weight: bold;border: 1px solid #ccc;">' + (data[i].typename == 'th' ? '托号' : data[i].typename == 'user' ? '普通' : data[i].typename == 'super' ? '高级' : data[i].typename == 'reg' ? '新' : '其他') + '</span>') + (data[i].group_name == '-' ? '' : "<span class='button-style style-js'>" + data[i].group_name) + "</span>") + data[i].phone + "</td>");

                            //tr += ("<td style='font-weight: bold;'>" + data[i].amount.toFixed(2) + "</td>");

                            tr += ("<td>"
                                + '<span style="font-weight: bold;font-size: 18px;color: #3b3c3f;text-shadow: 3px 3px 7px #ffeb00;">' + (parseNumber(data[i].amount) - parseNumber(data[i].tax_amount)) + '</span>'
                                + '<span style="font-size: 12px; margin-left: 8px;">总额 ' + data[i].amount + '</span>'
                                + '<div><div style="color:gray;font-size:12px;margin-top:3px;">(手续费' + parseNumber(data[i].tax_amount) + ' 费率' + parseNumber(data[i].sell_tax) + '%)</div></div>'
                                + "</td>");

                            tr += ("<td class=''>" + '<span class="button-style style-zs" style="cursor:pointer;" onclick="show_details(' + data[i].id + ')">' + data[i].payment_type + '</span>' + "</td>");
                            tr += ("<td style='color:gray;' class='buy1'>" + data[i].buy_orderId + (data[i].apiid != '' ? '<span style="font-size:12px;background: #bde2f5;color: #2a2b2c;padding: 2px 5px;border-radius: 3px;margin-left: 3px;">对接</span>' : ((data[i].buy_state == 0 ? '<span class="button-style">待交易</span>' : data[i].buy_state == 1001 ? '<span class="button-style style-zs">等待确认</span>' : data[i].buy_state == 1000 ? '<span class="button-style style-ls">等待上传截图</span>' : data[i].buy_state == 1 ? '<span class="button-style style-lvs">完成交易</span>' : data[i].buy_state == -1 ? '<span class="button-style style-hs">订单超时</span>' : data[i].buy_state == -2 ? '<span class="button-style style-hs">系统取消</span>' : data[i].buy_state == -3 ? '<span class="button-style style-hs">结束</span>' : '<span class="button-style">其他</span>') + (data[i].activate_order != -1 ? (data[i].state == 0 && (data[i].buy_state == -1 || data[i].buy_state == -2 || data[i].buy_state == -3) ? '<a class="design-button-full" onclick="' + "get_action({name:'activate_order',data:{id:'" + data[i].id + "'},n:'激活买币订单',e:'激活买币订单 " + data[i].buy_orderId + " " + "<br>！！！激活将重新进入买币'});" + '">激活</a><a  style="margin-left:5px;background: #eaeeff;color: #3255FF!important;border: 1px solid #5d76eb;" class="design-button-full" onclick="' + "get_action({name:'un_activate_order',data:{id:'" + data[i].id + "'},n:'禁止激活买币订单',e:'禁止激活买币订单 " + data[i].buy_orderId + " " + "<br>！！！此订单不允许激活买币'});" + '">不激活</a>' : '') : '<span style="display:inline-block;background: #597b9f;color: #f5fde1;padding:2px 8px;border-radius: 3px;font-size: 11px;font-weight: bold;">禁止激活</span>'))) + "</td>");
                            tr += ("<td>" + (data[i].state == 1 ? '<span style="color:green;">已完成</span>' : data[i].state == 99 ? '<span style="color:gray;">审核中</span>' : data[i].state == -1 ? '<span style="color:gray;">已撤销</span>' : '<span style="color:gray;">交易中</span>') + (data[i].state == 0 ? '<a style="margin-left:5px;"  class="design-button-common" onclick="' + "get_action({name:'finish_sellorder',data:{id:" + data[i].id + "},n:'完成卖币交易',e:'确认后订单 " + data[i].orderNo + " " + "直接完成'});" + '">完成交易</a>' : '') + (data[i].state == 0 && data[i].orderLock != 1 ? '&nbsp;<a style="padding: 2px 8px;margin-left: 6px;line-height: 19px;color: #5a5a5c;border: 1px solid #5a5a5c;" onclick="' + "get_action({name:'lock_sellorder',data:{id:" + data[i].id + "},n:'卖币锁定',e:'锁定 " + data[i].orderNo + " 的交易<br>【订单金额】：" + data[i].amount + "'});" + '"><svg t="" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="7884" width="16" height="16"><path d="M500.224 694.528c-56.576 0-102.912-46.08-102.912-102.912a102.912 102.912 0 0 1 205.824 0c0 56.832-46.336 102.912-102.912 102.912z m0-146.944a44.032 44.032 0 1 0 0 88.064 44.032 44.032 0 0 0 0-88.064z" fill="#005CFF" p-id="7885"></path><path d="M733.44 318.72h-84.224c-1.28-77.056-64-139.264-141.312-139.264-77.312 0-140.032 62.208-141.312 139.264h-84.224c-64.512 0-116.992 52.48-116.992 116.992V747.52c0 64.512 52.48 116.992 116.992 116.992h450.816c64.512 0 116.992-52.48 116.992-116.992V435.712a116.736 116.736 0 0 0-116.736-116.992z m-225.536-80.384c44.8 0 81.408 35.84 82.432 80.384h-165.12c1.536-44.544 37.888-80.384 82.688-80.384zM791.808 747.52c0 32.256-26.112 58.368-58.368 58.368H282.624c-32.256 0-58.368-26.112-58.368-58.368V435.712c0-32.256 26.112-58.368 58.368-58.368h450.816c32.256 0 58.368 26.112 58.368 58.368V747.52z" fill="#1B1F28" p-id="7886"></path></svg>&nbsp;锁定</a>' : '')

+ (data[i].state == 99 ? ('<a style="margin-left:5px;"  class="design-button-common" onclick="' + "get_action({name:'sell_verify_success',data:{id:" + data[i].id + "},n:'通过审核',e:'将订单 " + data[i].orderNo + " " + "发布到大厅',inputs:[{id:'actCode',name:'谷歌验证码',value:''}]});" + '">通过</a><a style="margin-left:5px;"  class="design-button-full" onclick="' + "get_action({name:'sell_verify_fail',data:{id:" + data[i].id + "},n:'交易撤销',e:'订单 " + data[i].orderNo + " " + " 交易撤销原路返还'});" + '">取消</a>') : '')
                                + "</td>");
                            tr += ("<td>" + data[i].create_time + "</td>");
                            tr += ("<td>" + data[i].finish_time + "</td>");

                            tr += "</tr>";
                            $("#list-display").append(tr)
                        }
                        $(".page_size").html(pager_size);
                        $(".page_count").html(json.data.count);
                        $(".cpages").html(json.data.html);


                        pop_mp3tip();

                        $(".total_amount").html(parseFloat(json.total_amount).toFixed(2));
                        $(".total_number").html(json.total_number);


                    } else {

                    }
                },
                error: function () {
                }
            });
        }
        getPager();

        function _pt(i) {
            pager_index = i;
            getPager();
        }
    </script>

    <audio id="myAudio" controls style="display: none;">
        <source src="../static/files/新订单.mp3" type="audio/mpeg">
        Your browser does not support the audio element.
    </audio>
    <script>

        function checkIsSelect(obj) {
            if (obj.prop('checked')) {
                return true;
            } else {
                return false;
            }
        }

        var autoTimer = 0;
        var lastList = null;

        var set_autoAlarm = function (e) {
            if (checkIsSelect($(e))) {
                console.log('启动');
                lastList = null;

                autoTimer = setInterval(function () {
                    getPager();
                }, 5000);

            } else {
                console.log('关闭');
                clearInterval(autoTimer);
                autoTimer = 0;
            }
        }

        var pop_mp3tip = function (json) {
            if (autoTimer == 0) {
                return;
            }

            if (current_json.length > 0) {
                play_mp3tip();
                setRbtip('待处理卖币订单' + current_json.length + '条', '<div style="text-align:center;margin-top:18px;"><a style="color: #f73030;font-size:18px;background: #e1e1e1;display: inline-block;padding: 9px 28px;border-radius: 2px;font-weight: bold;" onclick="open_new_page(\'../serv/sell_dating_list.aspx?state=verify\',\'待审核\');layer.closeAll();">立即查看</a></div>')
            }

        }

        var mp3_audio = document.getElementById("myAudio"); // 获取音频元素
        var play_mp3tip = function () {
            console.log('播放订单提示');
            mp3_audio.play();
        }



        layui.use(['form', 'layedit', 'laydate'], function () {
        });


        function report_exl() {
            export_data(getParam());
        }



        var show_details = function (id, type) {
            var details_info = "";
            for (var i = 0; i < current_json.length; i++) {
                var obj = current_json[i];

                if (obj.id == id) {
                    details_info += ' <div style="display: flex; margin: 20px 0; padding-bottom: 20px; border-bottom: 1px solid #eee;">            <div>                <b>支付方式：</b>            </div>            <div style="margin-left: 10px;">                ' + obj.payment_type + '            </div>        </div>';
                    details_info += ' <div style="display: flex; margin: 20px 0; padding-bottom: 20px; border-bottom: 1px solid #eee;">            <div>                <b>收款姓名：</b>            </div>            <div style="margin-left: 10px;">                ' + obj.payment_name + '            </div>        </div>';

                    if (obj.payment_type == "银行卡") {
                        details_info += ' <div style="display: flex; margin: 20px 0; padding-bottom: 20px; border-bottom: 1px solid #eee;">            <div>                <b>收款卡号：</b>            </div>            <div style="margin-left: 10px;">                ' + obj.payment_bankid + '            </div>        </div>';
                        details_info += ' <div style="display: flex; margin: 20px 0; padding-bottom: 20px; border-bottom: 1px solid #eee;">            <div>                <b>开户行：</b>            </div>            <div style="margin-left: 10px;">                ' + obj.payment_bankname + '            </div>        </div>';
                    } else {
                        details_info += ' <div style="display: flex; margin: 20px 0; padding-bottom: 20px; border-bottom: 1px solid #eee;">            <div>                <b>' + obj.payment_type + '账号：</b>            </div>            <div style="margin-left: 10px;">                ' + obj.payment_bankid + '            </div>        </div>';
                    }
                    details_info += ' <div style="display: flex; margin: 20px 0; padding-bottom: 20px; border-bottom: 1px solid #eee;">            <div>                <b>交易金额：</b>            </div>            <div style="margin-left: 10px;">                ' + obj.amount + '            </div>        </div>';

                    //details_info += '<div style="display: flex; margin: 20px 0; padding-bottom: 20px; border-bottom: 1px solid #eee;">' + (obj.state == 1000 && obj.orderLock != 1 ? '&nbsp;<a style="display: flex;align-items: center;padding: 10px 39px;margin-left: 6px;line-height: 19px;color: #5a5a5c;border: 1px solid #5a5a5c;" onclick="' + "get_action({name:'lock_order',data:{id:" + obj.id + "},n:'锁定交易',e:'锁定 " + obj.orderId + " 的交易<br>【订单金额】：" + obj.amount + "'});" + '"><svg t="*************" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="7884" width="16" height="16"><path d="M500.224 694.528c-56.576 0-102.912-46.08-102.912-102.912a102.912 102.912 0 0 1 205.824 0c0 56.832-46.336 102.912-102.912 102.912z m0-146.944a44.032 44.032 0 1 0 0 88.064 44.032 44.032 0 0 0 0-88.064z" fill="#005CFF" p-id="7885"></path><path d="M733.44 318.72h-84.224c-1.28-77.056-64-139.264-141.312-139.264-77.312 0-140.032 62.208-141.312 139.264h-84.224c-64.512 0-116.992 52.48-116.992 116.992V747.52c0 64.512 52.48 116.992 116.992 116.992h450.816c64.512 0 116.992-52.48 116.992-116.992V435.712a116.736 116.736 0 0 0-116.736-116.992z m-225.536-80.384c44.8 0 81.408 35.84 82.432 80.384h-165.12c1.536-44.544 37.888-80.384 82.688-80.384zM791.808 747.52c0 32.256-26.112 58.368-58.368 58.368H282.624c-32.256 0-58.368-26.112-58.368-58.368V435.712c0-32.256 26.112-58.368 58.368-58.368h450.816c32.256 0 58.368 26.112 58.368 58.368V747.52z" fill="#1B1F28" p-id="7886"></path></svg>&nbsp;锁定</a>' : '') + '</div>';

                    _modal('收款信息', details_info);
                    break;
                }
            }
        }
    </script>
</asp:Content>


<%@ Master Language="C#" AutoEventWireup="true" CodeFile="m.master.cs" Inherits="global_m" %>

<!DOCTYPE html>

<html xmlns="http://www.w3.org/1999/xhtml">
<head runat="server">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title></title>
    <link rel="shortcut icon" href="<%=uConfig.stcdata("htbzlogo") %>" type="image/x-icon" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=0.5, maximum-scale=2.0, user-scalable=no" />

    <link href="../css/bootstrap.min.css" rel="stylesheet" />
    <link href="../css/style.css" rel="stylesheet" />

    <link href="../css/font-awesome.min.css" rel="stylesheet" />
    <link href="../layui/css/layui.css" rel="stylesheet" />
    <link href="../css/global.css" rel="stylesheet" />
    <script src="../js/jquery_1.9.1.min.js"></script>
    <script src="../js/modernizr.js"></script>
    <script src="../layer/layer.js"></script>
    <script src="../js/jquery.form.min.js"></script>
    <script src="../layui/layui.js"></script>
    <script src="../js/layui-mz-min.js"></script>
    <script src="../js/fuzhu.js?v=0.0.12"></script>
    <script src="../js/clipboard.min.js"></script>
    <script src="../js/qrcode.min.js"></script>


    <script src="../js/moment.js"></script>


    <script src="../js/bootstrap-datepicker.min.js"></script>
    <link rel="stylesheet" href="../css/bootstrap-datepicker.min.css">
    <script>
        function get_param(paramName) {
            var urlParams = new URLSearchParams(window.location.search);
            return urlParams.get(paramName) || "";
        }
    </script>


    <script>
        document.addEventListener('keydown', function (event) {
            // 检查按下的键是否是F5键的键码是116
            if (event.keyCode === 116) {
                event.preventDefault(); // 阻止默认刷新行为
                location.href = location.href;
            }
            if (event.key === "Escape") {
                closerightBoxpopup();
            }
        });
    </script>

    <style>
        .input_button {
            position: absolute;
            right: 0px;
            top: 0;
            height: 34px;
            line-height: 34px;
            padding: 0 10px;
        }

        .table th {
            text-align: left;
        }

        .table tr:hover {
            background: #f1f1f1;
        }

        .table td, .table th {
            /*text-align: center;*/
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .layui-input-block {
            margin-left: 110px;
        }

        .ctTable th {
            background: #fafbff!important;
            color: #3b426b!important;
            font-size: 14px;
            white-space: nowrap;
            height: 35px;
            line-height: 35px;
            font-weight: 500;
        }

        a {
            outline: 0!important;
            outline: none!important;
        }

        @media screen and (max-width: 700px) {
            .sidebar {
                left: -190px;
            }

            .manage-body {
                margin-left: 0px;
            }
        }

        @media screen and (max-width: 765px) {
            #login-dropdown-form {
                position: relative;
            }
        }


        .nav-sidebar > li ul.sub-menu2 {
            background-color: #fff;
            border-top: 1px solid #e5e5e5;
            display: none;
            list-style: outside none none;
            margin: 0;
            padding: 0;
            position: relative;
        }



        .nav-sidebar ul.sub-menu2 ::before {
            -moz-border-bottom-colors: none;
            -moz-border-left-colors: none;
            -moz-border-right-colors: none;
            -moz-border-top-colors: none;
            border-color: #333;
            border-image: none;
            border-style: dotted;
            border-width: 0 0 0 1px;
            bottom: 0;
            content: "";
            display: block;
            left: 28px;
            position: absolute;
            top: 0;
            z-index: 1;
        }

        .nav-sidebar ul.sub-menu2 > li {
            margin-left: 0;
            position: relative;
        }

            .nav-sidebar ul.sub-menu2 > li::before {
                border-top: 1px dotted #333;
                content: "";
                display: inline-block;
                left: 28px;
                position: absolute;
                top: 17px;
                width: 7px;
            }


        .nav-sidebar .sub-menu > li.open2 ul.sub-menu2 {
            display: block;
        }


        .nav-sidebar ul.sub-menu2 > li > a {
            border-top: 1px dotted #e4e4e4;
            color: #616161;
            display: block;
            margin: 0;
            padding: 7px 0 9px 47px;
            position: relative;
        }

        body {
            background: #f3f3f4;
        }


        .layui-input, .layui-select, .layui-textarea {
            height: 34px;
        }

        .layui-form-select dl dd.layui-this {
            background-color: #1E91FF;
        }

        .modal-footer .btn-primary {
            color: #fff;
            background-color: #337ab7;
            border-color: #2e6da4;
            height: 36px;
        }

        .modal-footer btn-primary:hover {
            color: #fff;
            background-color: #286090;
            border-color: #204d74;
        }

        .layui-layer-content .layui-form-label {
            width: 65px!important;
        }

        .layui-form-item {
            margin-bottom: 0px;
        }





        /*操作按钮*/

        .action-cell {
            position: sticky;
            right: 0;
            background-color: #fff;
            z-index: 1;
            overflow: visible!important;
        }

        .action-cell-left {
            position: sticky;
            left: 0;
            background: #fff;
            overflow: visible!important;
        }

        .card-toolbar {
            position: relative;
            display: inline-block;
        }

        .card-button {
            padding: 5px 8px;
            background-color: #409EFF;
            color: #fff!important;
            border: none;
            cursor: pointer;
            border-radius: 5px;
            position: relative;
        }

            .card-button:hover {
                transition: all 0.2s ease;
                background-color: #007bff;
            }


        .common-button {
            background-color: #fff;
            color: #5E6278!important;
        }

        .card-button svg:not(.limit_color) path {
            fill: currentColor;
        }

        .common-button:hover {
            transition: all 0.2s ease;
            background-color: #f1f1f1!important;
            /*color:#009ef7!important;*/
        }

        .card-popup {
            position: fixed;
            /*top: calc(100% + 10px);*/
            /*right: 0;*/
            min-width: 150px;
            background-color: #fff;
            padding: 10px;
            border-radius: 0.75rem;
            box-shadow: 0px 0px 50px 0px rgba(82, 63, 105, 0.15);
            display: none;
            z-index: 3;
        }

        .menu-item .menu-link {
            cursor: pointer;
            display: flex;
            align-items: center;
            padding: 0;
            flex: 0 0 100%;
            padding: 0.65rem 2rem;
            transition: none;
            outline: none !important;
            color: #3F4254;
            text-decoration: none;
            font-weight: 500 !important;
            font-family: "Font Awesome 6 Brands";
            font-size: 13px;
            /*-webkit-text-stroke: 0.3px;*/
        }

            .menu-item .menu-link:hover {
                transition: color 0.2s ease;
                background-color: #F1F4FF;
                color: #2C9AFF;
            }


        .table > thead > tr > th {
            border: 1px solid #f1f1f1!important;
            color: #838383;
            background: #f8f8f8;
        }

        .table td, .table th {
            border: 1px solid #f1f1f1!important;
        }

        /*button-style*/
        .button-style {
            display: inline-block;
            background-color: #e5e5e5;
            border-color: #c7c5c0;
            color: #737373;
            padding: 0px 10px;
            font-size: 12px;
            border-radius: 6px;
            font-weight: bold;
            margin-right: 3px;
            border: 1px solid #737373;
        }

            .button-style.style-zs {
                border: 1px solid #724bb1;
                background: #f1edf5;
                color: #724bb1;
            }

            .button-style.style-ls {
                background-color: #e8eff9;
                border-color: #83a8df;
                color: #4482a9;
            }

            .button-style.style-ls-low {
                background: linear-gradient(45deg, #E7F3FF, #F3F9FF);
                border-color: #afc6dd;
                color: #71889f;
            }


            .button-style.style-js {
                background-color: #fdf3f3;
                border-color: #777777;
                color: #996536;
            }

            .button-style.style-hs {
                color: #993636;
                background: #fff;
                border-color: #993636;
            }

            .button-style.style-js-low {
                background-color: #fffbe7;
                border-color: #e0c26f;
                color: #a98144;
            }


            .button-style.style-lvs {
                background-color: #f2f9ef;
                border-color: #76cf6d;
                color: #4faf6e;
            }
        /*样式*/
        .ibox-title {
            background: none;
            border: 0;
            margin-bottom: 27px;
            color: #000;
            min-height: auto;
        }

            .ibox-title h5 {
                color: #333;
                font-weight: 500;
                font-size: 24px;
            }

        .ibox-content {
            border: 0;
            border-radius: 8px;
            padding: 30px 20px;
            margin: 15px;
        }



        .btn-success svg {
            position: relative;
            top: 3px;
        }

            .btn-success svg path {
                fill: currentColor;
            }

        .btn-success {
            background-color: #009ef7;
            border-color: #009ef7;
        }

            .btn-success:hover {
                transition: all 0.2s ease;
                background-color: #0095e8!important;
                border-color: #0095e8!important;
            }
    </style>


    <style>
        body {
            background: #eff1f7;
        }

        .gray-bg {
            background-color: #eff1f7;
        }

        .table > thead > tr > th {
            border: 1px solid #f1f1f1!important;
            color: #333;
            background: #9a9add14;
        }

        .table tbody {
            background: #fff;
        }

        .btn-success {
            color: #586cb1;
            border-color: #586cb1;
            background-color: transparent;
        }

            .btn-success:hover {
                background: rgba(88,108,177,.05)!important;
                color: #586cb1;
                border-color: #586cb1!important;
            }

        .ibox-content {
            background: none;
            padding: 15px 20px 20px 20px;
            margin: 0px;
        }


        .table > thead > tr > th, .table > tbody > tr > th, .table > tfoot > tr > th, .table > thead > tr > td, .table > tbody > tr > td, .table > tfoot > tr > td {
            padding: 12px 8px;
        }

        .table tr:hover {
            background: #4138380a;
        }

        .button-style {
            border-radius: 0px;
        }

        td, th {
            color: #34488d;
        }


        .layui-icon {
            font-size: 12px;
        }

        .cpages {
            display: inline-block;
            border-radius: 18px;
            box-shadow: 0 3px 1px -2px rgba(0,0,0,.05),0 2px 2px 0 rgba(0,0,0,.05),0 1px 5px 1px rgba(0,0,0,.05)!important;
            padding: 2px 0;
        }

            .cpages li a {
                color: #4d4141;
                background: none;
                border-radius: 50%;
                width: 28px;
                text-align: center;
                margin: 0;
                padding: 0px;
            }

            .cpages li.active a {
                background-color: #586cb1;
                color: #fff;
            }

        .layui-laypage input {
            border-radius: 12px;
            font-weight: bold;
            color: #586cb1;
            box-shadow: 0 3px 1px -2px rgb(0 0 0 / 7%), 0 2px 2px 0 rgb(0 0 0 / 7%), 0 1px 5px 1px rgb(0 0 0 / 7%);
            background: none;
            height: auto!important;
            padding: 0px 3px;
        }

        .layui-laypage .layui-laypage-skip {
            background: none;
            font-weight: bold;
        }


        .layui-laypage .layui-laypage-count, .layui-laypage .layui-laypage-limits, .layui-laypage .layui-laypage-skip {
            background: none;
            font-weight: bold;
            color: gray;
        }

        .layui-laypage span {
            background: none!important;
        }

        .layui-laypage button {
            color: #586cb1;
            border-color: #586cb1;
            background-color: transparent;
        }

        .layui-laypage select {
            box-shadow: 0 3px 1px -2px rgb(0 0 0 / 7%), 0 2px 2px 0 rgb(0 0 0 / 7%), 0 1px 5px 1px rgb(0 0 0 / 7%);
            background: none;
        }

        .form-control, .single-line {
            box-shadow: 0 3px 1px -2px rgb(0 0 0 / 0%), 0 2px 2px 0 rgb(0 0 0 / 19%), 0 1px 5px 1px rgb(0 0 0 / 13%);
            background: #f9f9f975;
            font-weight: bold;
        }

            .layui-input:focus, .form-control:focus, .single-line:focus {
                border-color: #bdc3d9!important;
                box-shadow: 0 3px 1px -2px rgb(0 0 0 / 0%), 0 2px 2px 0 rgb(0 0 0 / 19%), 0 1px 5px 1px rgb(0 0 0 / 13%);
                background: #fff;
            }


        .modal-footer {
            display: none;
        }

        .modal-title {
            font-size: 16px;
        }


        .timesel_lab {
            background: #fff;
            color: #333;
            padding: 5px 0;
            width: 70px;
            text-align: center;
            border: 1px solid #17B492;
            margin-left: -1px;
            cursor: pointer;
            margin-right: 5px;
            color: #17B492;
        }

            .timesel_lab.active {
                background: #17B492;
                color: #fff;
                cursor: default;
            }
    </style>


    <script>

        function parseNumber(t) {
            if (isNaN(t) || t == "") {
                return 0.00;
            }
            return parseFloat(t);
        }

        function formatDateTimeToYYYYMMDDHHMMSS(date) {
            var year = date.getFullYear();
            var month = (date.getMonth() + 1).toString().padStart(2, "0");
            var day = date.getDate().toString().padStart(2, "0");
            var hours = date.getHours().toString().padStart(2, "0");
            var minutes = date.getMinutes().toString().padStart(2, "0");
            var seconds = date.getSeconds().toString().padStart(2, "0");
            return year + "-" + month + "-" + day;
        }

        function getDateRange(type) {
            var now = new Date();
            var startDate = new Date();
            var endDate = new Date();

            var year = now.getFullYear();
            var month = now.getMonth();
            var day = now.getDate();
            var dayOfWeek = now.getDay(); // 获取当前日期是星期几 (0 表示星期日, 1 表示星期一, ... 6 表示星期六)

            // 计算星期一的日期
            var monday = new Date(now);
            monday.setDate(day - dayOfWeek + (dayOfWeek === 0 ? -6 : 1));

            if (type === "today") {
                startDate.setHours(0, 0, 0, 0);
                endDate.setHours(23, 59, 59, 999);
            } else if (type === "yesterday") {
                startDate.setDate(day - 1);
                startDate.setHours(0, 0, 0, 0);
                endDate.setDate(day - 1);
                endDate.setHours(23, 59, 59, 999);
            } else if (type === "thisweek") {
                startDate.setTime(monday.getTime());
                startDate.setHours(0, 0, 0, 0);
                endDate.setTime(monday.getTime() + 6 * 24 * 60 * 60 * 1000); // 本周的最后一天是第一天 + 6天
                endDate.setHours(23, 59, 59, 999);
            } else if (type === "lastweek") {
                var lastMonday = new Date(monday);
                lastMonday.setDate(monday.getDate() - 7); // 上周的第一天
                startDate.setTime(lastMonday.getTime());
                startDate.setHours(0, 0, 0, 0);
                endDate.setTime(lastMonday.getTime() + 6 * 24 * 60 * 60 * 1000); // 上周的最后一天是第一天 + 6天
                endDate.setHours(23, 59, 59, 999);
            } else if (type === "thismonth") {
                startDate.setDate(1); // 本月的第一天
                startDate.setHours(0, 0, 0, 0);
                endDate.setMonth(month + 1, 0); // 本月的最后一天
                endDate.setHours(23, 59, 59, 999);
            } else if (type === "lastmonth") {
                startDate.setMonth(month - 1, 1); // 上个月的第一天
                startDate.setHours(0, 0, 0, 0);
                endDate.setDate(0); // 本月的第一天减一，即上个月的最后一天
                endDate.setHours(23, 59, 59, 999);
            } else {
                startDate.setHours(0, 0, 0, 0);
                endDate.setHours(23, 59, 59, 999);
            }

            return { startDate: startDate, endDate: endDate };
        }






        $(function () {
            $('.timesel_lab').on('click', function () {
                var text = $(this).text();

                if (text == "查询") {
                    get_totalData();
                    return;
                }


                $(this).addClass("active").siblings().removeClass("active");

                var currentDate = {};
                switch (text) {
                    case "今天":
                        currentDate = getDateRange('today');
                        break;
                    case "昨天":
                        currentDate = getDateRange('yesterday');
                        break;
                    case "本周":
                        currentDate = getDateRange('thisweek');
                        break;
                    case "上周":
                        currentDate = getDateRange('lastweek');
                        break;
                    case "本月":
                        currentDate = getDateRange('thismonth');
                        break;
                    case "上月":
                        currentDate = getDateRange('lastmonth');
                        break;
                    default:
                        $('#start_time').val("");
                        $('#end_time').val("");
                        return;
                        break;

                }

                $('#start_time').val(formatDateTimeToYYYYMMDDHHMMSS(currentDate.startDate));
                $('#end_time').val(formatDateTimeToYYYYMMDDHHMMSS(currentDate.endDate));


            })
        })



        //排序代码
        $(function () {
            $('[sortby]').on('click', function () {
                $(this).css({
                    '-webkit-user-select': 'none', /* Chrome, Safari, Opera */
                    '-moz-user-select': 'none',    /* Firefox */
                    'user-select': 'none'          /* General */
                });

                $(this).siblings().removeAttr("sort_type");
                console.log('click', $(this).text());
                var type = $(this).attr('sort_type');
                $('[sortby]').find('svg').remove();
                var svgHtml = "";
                switch (type) {
                    case "down":
                        type = "";
                        break;
                    case "up":
                        type = "down";
                        svgHtml = '<svg t="" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="1964" width="12" height="12"><path d="M768.002 535.89H600.749l-0.909-322.563h-166.4v322.56l-167.253-0.455L517.181 858.39l250.821-322.5z" fill="#FD3333" p-id="1965"></path></svg>';
                        break;
                    default:
                        type = "up";
                        svgHtml = '<svg t="" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2232" width="12" height="12"><path d="M255.998 488.11h167.253l0.909 322.563h166.4v-322.56l167.253 0.455L506.819 165.61l-250.821 322.5z" fill="#1296DB" p-id="2233"></path></svg>';
                        break;
                }
                $(this).attr('sort_type', type);
                $(this).append(svgHtml);

                getPager();

            });
        })

        var get_sortby = function () {
            var sortby = "";
            var ssss = "";
            $('[sortby]').each(function () {
                ssss = $(this).attr('sort_type');
                console.log('ssss', ssss);
                if (ssss == "down" || ssss == "up") {
                    var lsort = $(this).attr('sortby');
                    switch (ssss) {
                        case "down":
                            sortby = lsort;
                            break;
                        case "up":
                            sortby = lsort;
                            break;
                        default:
                            break;
                    }
                    return false;
                }
            })
            return {
                sortby: sortby,
                sorttype: ssss
            }
        }

    </script>


    <script>
        function setLabname(name) {
            var sn = parent.setActiveLabName;
            if (sn) {
                sn(name)
            }
        }

        function setRbtip(title, content, area) {
            var sn = parent.rbtip;
            if (sn) {
                sn(title, content, area)
            }
        }
    </script>
    <asp:ContentPlaceHolder ID="head" runat="server">
    </asp:ContentPlaceHolder>
</head>
<body style="padding-top: 0px;">
    <form id="form1" runat="server">
        <!--images start ------------>
        <div style="display: none;">
            <img id="imgTemp" />
            <input id="updateImage" type="file" accept="image/png, image/jpeg,image/gif,image/ico" name="file" onchange="upload()" multiple="multiple" />
            <script>
                var _uploadID = "#updateImage";
                var _showid = "#imgTemp";
                var _imgname = "";
                var _callb;
                var upload_param = "";
                function upload() {
                    if ($(_uploadID).val() == "") {
                        return;
                    }
                    var options = {
                        type: "POST",
                        url: '../api/admin.aspx?do=img',
                        data: { name: _imgname, id: _showid, param: upload_param },
                        datatype: "json",
                        success: function (json) {
                            upload_success(json);
                        }
                    };
                    // ��options����ajaxForm
                    $('#form1').ajaxSubmit(options);
                }
                function upload_success(json) {
                    $(_uploadID).val('');
                    if (json.code == 1) {
                        var temp_images = json.list[0];
                        temp_images = temp_images.replace(/~/i, "..");
                        $(json.id).attr("src", temp_images);
                        if (typeof (_callb) != "undefined") {
                            _callb({
                                success: true,
                                imgurl: json.list,
                                path: _imgname
                            });
                            return;
                        }
                    } else {
                        layer.msg(json.msg, { icon: 2 });
                    }

                    _callb({
                        success: false,
                        imgurl: ""
                    });
                }

                function uploadImages(callback, path, filetype, param) {
                    if (!path || "" == path) {
                        path = "upload";
                    }
                    if (!filetype || "" == filetype) {
                        filetype = "image/png, image/jpeg,image/gif,image/ico";
                    }
                    $(_uploadID).attr("accept", filetype)
                    _imgname = path;
                    _callb = callback;
                    upload_param = param;
                    console.log("click", filetype);
                    $(_uploadID).click();
                }
            </script>
        </div>
        <!--images end ------------>



        <!--export start ------------>
        <div style="display: none;">
            <input id="export_param" class="report_param" runat="server" />
            <asp:Button ID="export_button_action" ClientIDMode="Static" runat="server" Text="Button" OnClick="export_button_action_Click" />
            <script>
                function export_data(arr) {
                    console.log(arr);
                    $(".report_param").val(JSON.stringify(arr));
                    $("#export_button_action").click();
                }
            </script>
        </div>
        <!--export end ------------>


        <!-- 模态框 -->
        <div class="modal fade bmodal" id="myModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-hidden="true">
                        </button>
                        <h4 class="modal-title" id="myModalLabel"></h4>
                    </div>
                    <div class="modal-body">
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-primary" data-dismiss="modal">
                            确认
                        </button>
                    </div>
                </div>
            </div>
        </div>


        <textarea id="copyObjData" style="color: #000; position: absolute; left: -9999px; top: -9999px"></textarea>
        <a id="copybtn" style="color: #1c5caf; text-decoration: underline; position: absolute; left: -9999px; top: -9999px;">����</a>


        <div id="mybody" style="padding: 6px;">
            <asp:ContentPlaceHolder ID="ContentPlaceHolder1" runat="server"></asp:ContentPlaceHolder>
        </div>

        <script src="../js/bootstrap.min.js"></script>

        <script>
            $(".sub-menu>li>a").click(function () {
                $(this).closest("li").toggleClass("open2");
            })
        </script>


        <script>
            function toggleMenu() {
                var disp = $(".sidebar").css("display");
                if (disp == "none") {
                    location.href = "../index.aspx";
                    return;
                }
                var left = $(".sidebar").css("left");
                if (left == "0px") {
                    $(".sidebar").animate({ "left": "-190px" });
                    $(".manage-body").animate({ "margin-left": "0px" });
                } else {
                    $(".sidebar").animate({ "left": "0px" });
                    $(".manage-body").animate({ "margin-left": "190px" });
                }
            }

            function logoClick() {
                toggleMenu();
            }
        </script>

        <script>
            var copydata = "";
            new Clipboard('#copybtn', {
                target: function () {
                    $("#copyObjData").val(copydata);
                    return document.querySelector('#copyObjData');
                }
            }).on('success', function (e) { }).on('error', function (e) { });
            function copytext(text) {
                copydata = text;
                document.getElementById("copybtn").click();
            }

            function getUuid() {
                return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
                    var r = (Math.random() * 16) | 0,
                      v = c == 'x' ? r : (r & 0x3) | 0x8;
                    return v.toString(16);
                });
            }
            function _modal(title, content) {
                var modelid = 'model_' + getUuid().replace(/-/g, '');
                $("#myModalLabel").html(title);
                $("#myModal .modal-body").html(content);
                $('#myModal .modal-dialog').attr('modelid', modelid);
                $('#myModal').modal('show');

                return modelid;
            }

        </script>


        <script>
            function open_new_page(url, name) {
                try {
                    var jk = parent.open_new_page;
                    if (jk) {
                        jk(url, name);
                    }
                } catch (e) {
                    console.log("open_new_page_error", e);
                    location.href = url;
                }
            }
            function close_this_page() {
                if (parent == window) {
                    window.opener = null;
                    window.open('', '_self');
                    window.close();
                    return;
                }
                try {
                    var jk = parent.close_this_page;
                    if (jk) {
                        jk();
                    }
                } catch (e) {
                    console.log("close_this_page", e);
                }
            }
            function active_page_title() {
                var str = "";
                try {
                    var jk = parent.active_page_title;
                    if (jk) {
                        str = jk();
                    }
                } catch (e) {
                    //console.log("active_page_title", e);
                }
                return str;
            }
        </script>





        <link rel="stylesheet" href="../kindeditor/themes/default/default.css" />
        <link rel="stylesheet" href="../kindeditor/plugins/code/prettify.css" />
        <script charset="utf-8" src="../kindeditor/kindeditor.js"></script>
        <script charset="utf-8" src="../kindeditor/lang/zh-CN.js"></script>
        <script charset="utf-8" src="../kindeditor/plugins/code/prettify.js"></script>

        <script>
            var editK;
            KindEditor.ready(function (K) {
                editK = K;
                prettyPrint();
            });

            var init_Edit = function () {
                $(".kedit_temp").each(function () {
                    $(this).removeClass("kedit_temp");
                    var id = $(this).attr("id");
                    var editor = editK.create('#' + id, {
                        cssPath: '../kindeditor/plugins/code/prettify.css',
                        uploadJson: '../kindeditor/upload_json.ashx',
                        fileManagerJson: '../kindeditor/file_manager_json.ashx',
                        allowFileManager: true,
                        afterCreate: function () {
                            var self = this;
                            editK.ctrl(document, 13, function () {
                                self.sync();
                                editK('form[name=example]')[0].submit();
                            });
                            editK.ctrl(self.edit.doc, 13, function () {
                                self.sync();
                                editK('form[name=example]')[0].submit();
                            });
                        },
                        afterBlur: function () { this.sync(); }
                    });
                });
            }
        </script>



        <script>

            // 基础参数
            var usdt = {
                return_usdt: '<%=uConfig.stcdata("return_usdt") %>',
                    prices: {
                        app: '<%=uConfig.stcdata("usdt_price") %>',
                        binance: '<%=uConfig.stcdata("binance_price") %>',
                    okex: '<%=uConfig.stcdata("okex_price") %>'
                }
            }

            var cny = function (amount) {
                var result = "0.00";
                try {
                    var _huilv = parseFloat(usdt.prices.app);
                    var _amount = parseFloat(amount);

                    result = (_amount * _huilv).toFixed(2);
                }
                catch (e) {

                }
                return result;
            }
            var usd = function (amount) {
                var result = "0.00";
                try {
                    var _huilv = parseFloat(usdt.prices.app);
                    var _amount = parseFloat(amount);

                    result = (_amount / _huilv).toFixed(2);
                }
                catch (e) {

                }
                return result;
            }

            $(document).ready(function () {

                //按钮点击
                $('table').on('click', '.card-button', function (e) {
                    if (!$(this).hasClass('common-button')) {

                        e.stopPropagation();
                        var tagname = $(e.target).prop('tagName');
                        if (tagname != "A") {
                            e.target = $(e.target).closest('a');
                        }
                        $('.card-toolbar').not($(e.target).closest('.card-toolbar')).find('.card-popup').fadeOut('fast');


                        autoResetPosition(e);


                        $(e.target).next().stop().fadeToggle('fast');

                        $('.action-cell').css({ "z-index": "1" });
                        $(e.target).closest('.action-cell').css({ "z-index": "2" });

                    }
                });

                //popup框点击
                $('table').on('click', '.card-popup', function (e) {
                    e.stopPropagation();
                    $('.card-toolbar').find('.card-popup').fadeOut('fast');
                });

                // 点击空白区域或内部元素时关闭弹出框
                $(document).on('click', function (e) {
                    if (!$(e.target).closest('.card-toolbar').length) {
                        $('.card-popup').fadeOut('fast');
                    }
                });
            });

            $(window).on('scroll', function () {
                // 获取按钮和内容元素
                $('.card-button').each(function () {
                    if (!$(this).hasClass('common-button')) {
                        autoResetPosition({ target: $(this) })
                    }
                })
            });

            var autoResetPosition = function (e) {
                var button = $(e.target);

                var screenWidth = $(window).width();
                var scrollTop = $(document).scrollTop();
                var buttonRight = button.offset().left + button.outerWidth();

                var _right = screenWidth - buttonRight;
                var _top = button.offset().top + button.outerHeight() + 10 - scrollTop;

                //console.log('right', _right, 'top', _top);

                $(e.target).next().css({
                    'right': _right + 'px',
                    'top': _top + 'px'
                });
            }
        </script>









        <style>
            .button-group {
                display: flex;
                justify-content: left;
            }

            .button {
                display: inline-block;
                padding: 8px 20px;
                margin-right: 10px;
                font-size: 12px;
                text-align: center;
                text-decoration: none;
                color: #333;
                background-color: #eee;
                border-radius: 5px;
                transition: background-color 0.3s;
                border: 1px solid #eee;
            }


                .button:hover, .selected {
                    background-color: #fff;
                    color: #222!important;
                    font-weight: bold;
                    border: 1px solid #333;
                }
        </style>
        <script>
            $(function () {
                $('.button-group a').on('click', function () {
                    redirectToOriginalURL('?rv=' + $(this).attr('rv'));
                })
            })

            function redirectToOriginalURL(url) {
                // 获取当前页面的 URL
                var currentURL = window.location.href;

                // 检查当前页面是否带有参数
                var hasQueryParams = currentURL.indexOf('?') !== -1;

                // 提取当前页面的参数
                var currentParams = {};
                if (hasQueryParams) {
                    var queryParams = currentURL.substring(currentURL.indexOf('?') + 1);
                    var paramPairs = queryParams.split('&');
                    paramPairs.forEach(function (pair) {
                        var keyValue = pair.split('=');
                        var key = keyValue[0];
                        var value = keyValue[1];
                        currentParams[key] = value;
                    });
                }

                // 检查原地址中的参数是否已存在于当前页面的参数中
                var redirectToURL = url;
                for (var param in currentParams) {
                    if (!redirectToURL.includes(param + '=')) {
                        redirectToURL += '&' + param + '=' + currentParams[param];
                    }
                }

                return redirectToURL;
            }



            // 设置选中状态
            function setSelectedOption() {
                var currentActId = get_param('rv');
                var options = document.querySelectorAll('.button');
                options.forEach(function (option) {
                    var optionId = option.getAttribute('rv');
                    //console.log(optionId, currentActId);
                    if (optionId === currentActId) {
                        option.classList.add('selected');
                    } else {
                        option.classList.remove('selected');
                    }
                });
            }

            // 页面加载时设置初始选中状态
            window.addEventListener('load', setSelectedOption);

            // 监听选项的点击事件
            var options = document.querySelectorAll('.button');
            options.forEach(function (option) {
                option.addEventListener('click', function (e) {
                    e.preventDefault();
                    var optionId = this.getAttribute('rv');
                    var url = redirectToOriginalURL("?rv=" + optionId);
                    window.history.pushState({}, '', url);
                    setSelectedOption();

                    searchList();
                });
            });
        </script>




        <div style="background: #fff; padding: 18px; display: none;" id="actModel1">

            <div style="z-index: -1; background: #dbdef5; color: #3d50df; line-height: 24px; font-size: 12px; padding: 8px 12px; border-radius: 8px; font-weight: bold; margin: 10px 0; margin-bottom: 28px;">
                <svg t="1692474480503" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="54328" width="12" height="12">
                    <path d="M512 0C229.665391 0 0 229.665391 0 512 0 614.578087 30.230261 713.594435 87.462957 798.274783 97.792 813.568 118.53913 817.574957 133.832348 807.268174 149.103304 796.93913 153.132522 776.169739 142.803478 760.898783 93.072696 687.282087 66.782609 601.221565 66.782609 512 66.782609 266.50713 266.50713 66.782609 512 66.782609 757.49287 66.782609 957.217391 266.50713 957.217391 512 957.217391 757.49287 757.49287 957.217391 512 957.217391 420.685913 957.217391 332.933565 929.792 258.248348 877.879652 243.044174 867.350261 222.274783 871.067826 211.767652 886.227478 201.238261 901.36487 204.978087 922.178783 220.115478 932.685913 306.064696 992.434087 406.995478 1024 512 1024 794.334609 1024 1024 794.334609 1024 512 1024 229.665391 794.334609 0 512 0ZM512.004452 237.895235C475.118191 237.895235 445.221843 267.791583 445.221843 304.677843 445.221843 341.564104 475.118191 371.460452 512.004452 371.460452 548.890713 371.460452 578.787061 341.564104 578.787061 304.677843 578.787061 267.791583 548.890713 237.895235 512.004452 237.895235ZM512 429.935304C481.257739 429.935304 456.347826 454.845217 456.347826 485.587478L456.347826 752.717913C456.347826 783.460174 481.257739 808.370087 512 808.370087 542.742261 808.370087 567.652174 783.460174 567.652174 752.717913L567.652174 485.587478C567.652174 454.845217 542.742261 429.935304 512 429.935304Z" fill="#3d50df" p-id="54329"></path></svg>&nbsp;&nbsp;请仔细确认后再进行操作，避免造成资产损失！！
   
            </div>

            <div style="display: flex; align-items: center; margin-bottom: 28px;">
                <div style="width: 100px; text-align: right;">
                    <b>当前操作：</b>
                </div>
                <div style="margin-left: 10px; background: #f1f1f1; padding: 6px 18px; border-radius: 6px; color: #b91b1b; font-weight: bold;" class="actName">
                </div>
            </div>

            <div style="display: flex; align-items: center; margin-bottom: 28px;">
                <div style="width: 100px; text-align: right;">
                    <b>操作说明：</b>
                </div>
                <div style="margin-left: 10px; background: #f1f1f1; padding: 6px 18px; border-radius: 6px; color: #998883;" class="actExplain">
                </div>
            </div>

            <div class="other_inpus">
            </div>

            <div style="display: none; align-items: center; margin-bottom: 28px;" class="verify_code">
                <div style="width: 100px; text-align: right;">
                    <b>谷歌验证码：</b>
                </div>
                <div style="margin-left: 10px; background: #f1f1f1; padding: 6px 18px; border-radius: 6px; color: #000;">
                    <input style="font-size: 13px; font-weight: bold; border: 0px; background: none;" placeholder="请输入6位验证码" class="actCode" />
                </div>
            </div>

            <div style="display: flex; align-items: center; margin-bottom: 28px;">
                <div style="width: 100px; text-align: right;">
                    <b></b>
                </div>
                <div style="margin-left: 10px; border-radius: 6px; color: #000;">
                    <a style="border-radius: 18px; padding: 8px 22px; font-size: 12px; background: #534dbb; display: flex; cursor: pointer; color: #eee;" onclick="actTask()">
                        <svg t="1693040838464" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="4309" width="16" height="16">
                            <path d="M770.56 928.4608H388.4032c-16.9472 0-30.72-13.7728-30.72-30.72s13.7728-30.72 30.72-30.72h382.1056c15.616 0 28.8768-11.2128 32.3584-27.2384l72.2944-335.1552c3.4304-15.7696-0.3584-31.9488-10.3424-44.3392-9.472-11.7248-22.7328-18.176-37.4272-18.176h-239.104a30.72 30.72 0 0 1-28.16-43.008c62.1056-142.2848 40.3456-201.1136 28.1088-219.8016-13.7216-20.9408-33.792-24.1152-44.4928-24.1152-25.8048 0-35.9936 25.088-47.8208 77.7216-1.5872 6.9632-3.0208 13.4656-4.5568 19.3536-42.1888 161.5872-149.3504 219.136-235.6224 219.136H192.2048c-17.8688 0-32.4096 15.36-32.4096 34.2016v327.2192c0 18.8416 14.5408 34.2016 32.4096 34.2016h58.9312c16.9472 0 30.72 13.7728 30.72 30.72s-13.7728 30.72-30.72 30.72H192.2048c-51.7632 0-93.8496-42.9056-93.8496-95.6416V505.6c0-52.736 42.0864-95.6416 93.8496-95.6416h63.5392c30.72 0 134.1952-12.4928 176.128-173.1584 1.3824-5.2736 2.6624-11.1104 4.096-17.3568 9.8816-43.9296 28.3136-125.696 107.776-125.696 39.3728 0 74.3424 18.8928 95.8976 51.8656 24.2688 37.0688 41.1136 107.1616-5.888 235.008h193.6384c33.1264 0 64.2048 14.9504 85.248 41.0112 21.7088 26.88 29.952 61.8496 22.6304 95.8464l-72.2944 335.1552c-9.472 43.9808-48.3328 75.8272-92.416 75.8272z" fill="currentColor" p-id="4310"></path><path d="M269.6192 804.2496c-16.9472 0-30.72-13.7728-30.72-30.72v-193.0752c0-16.9472 13.7728-30.72 30.72-30.72s30.72 13.7728 30.72 30.72v193.0752c0 16.9472-13.7728 30.72-30.72 30.72z" fill="currentColor" p-id="4311"></path></svg>&nbsp;&nbsp;确认本次操作</a>
                </div>
            </div>
        </div>

        <script>
            var actData = {}
            var get_action = function (data) {
                actData = data;
                _modal("操作确认", "<div id='actModel_page'>" + $('#actModel1').html() + "</div>");
                try {
                    $('#actModel_page .actName').html(actData["n"]);
                } catch (e) {

                }
                try {
                    $('#actModel_page .actExplain').html(actData["e"]);
                } catch (e) {

                }

                try {
                    var inputs = actData["inputs"];
                    for (var i = 0; i < inputs.length; i++) {
                        var ipt = inputs[i];
                        var __value = "";
                        try {
                            __value = ipt.value;
                        } catch (e) {

                        }

                        $('#actModel_page .other_inpus').append('<div style="display:flex;align-items: center;margin-bottom:28px;" class="verify_code">            <div style="width:100px;text-align:right;">                <b>' + ipt.name + '：</b>            </div>            <div style="margin-left:10px;background: #f1f1f1;padding: 6px 18px;border-radius: 6px;color: #000;">               <input style="font-size: 13px;font-weight:bold;border:0px;background: none;" placeholder="请输入' + ipt.name + '" class="' + ipt.id + '" value="' + __value + '" />            </div>        </div>');
                    }
                } catch (e) {

                }


                try {
                    var inputs = actData["textareas"];
                    for (var i = 0; i < inputs.length; i++) {
                        var ipt = inputs[i];
                        var __value = "";
                        try {
                            __value = ipt.value;
                        } catch (e) {

                        }

                        $('#actModel_page .other_inpus').append('<div style="display:flex;align-items: center;margin-bottom:28px;" class="verify_code">            <div style="width:100px;text-align:right;flex-shrink: 0;">                <b>' + ipt.name + '：</b>            </div>            <div style="margin-left:10px;background: #f1f1f1;padding: 6px 18px;border-radius: 6px;color: #000;width: 100%;">               <textarea style="font-size: 13px;font-weight: bold;border: 0px;background: none;width: 100%;height: 69px;" placeholder="请输入' + ipt.name + '" class="' + ipt.id + '">' + __value + '</textarea>            </div>        </div>');
                    }
                } catch (e) {

                }

                try {
                    var selects = actData["selects"];
                    for (var i = 0; i < selects.length; i++) {
                        var sel = selects[i];

                        var option_data = "";

                        console.log('list', sel.list);
                        for (var s = 0; s < sel.list.length; s++) {
                            var a = sel.list[s];
                            if (a.length == 1) {
                                a.push(a[0]);
                            }
                            option_data += '<option value="' + a[0] + '">' + a[1] + '</option>';
                        }


                        console.log('option_data', option_data);

                        $('#actModel_page .other_inpus').append('<div style="display:flex;align-items: center;margin-bottom:28px;" class="verify_code">            <div style="width:100px;text-align:right;">                <b>' + sel.name + '：</b>            </div>            <div style="margin-left:10px;background: #f1f1f1;padding: 6px 18px;border-radius: 6px;color: #000;">               <select style="font-size: 13px;font-weight:bold;border:0px;background: none;" class="' + sel.id + '">' + option_data + '</select>            </div>        </div>');
                    }
                } catch (e) {

                }

                try {
                    if (actData["verify"] == 0) {
                        $('#actModel_page .verify_code').hide();
                    }
                } catch (e) {

                }
            }
            var get_action_unverify = function (data) {
                data["verify"] = 0;
                get_action(data);
            }
            var actTask = function () {
                var data = {};
                try {
                    data = actData["data"];
                } catch (e) {

                }

                $('#actModel_page .other_inpus').find("input").each(function () {
                    var ipt = $(this).val();
                    var cid = $(this).attr("class");
                    data[cid] = ipt;
                    console.log('自定义', ipt, cid);
                })


                $('#actModel_page .other_inpus').find("textarea").each(function () {
                    var ipt = $(this).val();
                    var cid = $(this).attr("class");
                    data[cid] = ipt;
                    console.log('自定义', ipt, cid);
                })


                $('#actModel_page .other_inpus').find("select").each(function () {
                    var ipt = $(this).val();
                    var cid = $(this).attr("class");
                    data[cid] = ipt;
                    console.log('自定义[选择框]', ipt, cid);
                })

                data["actCode"] = $('#actModel_page .actCode').val();
                $.ajax({
                    type: "POST",
                    url: "../api/admin.aspx?do=" + actData.name,
                    data: data,
                    datatype: "json",
                    success: function (data) {
                        if (data.code == 1) {
                            $('#myModal').modal('hide');
                            layer.msg(data.msg, { icon: 1, time: 500 }, function () {
                                getPager();
                            });
                        } else {
                            layer.msg(data.msg, { icon: 2, time: 1500 });
                        }
                    },
                    error: function () {
                        layer.msg("操作出现错误", { icon: 2, time: 1500 });
                    }
                });

            }
        </script>









        <div class="fullscreen-bg">
            <div class="fullscreen-media-container">
                <!-- 使用 video 标签，但初始状态下不显示 -->
                <video src="" alt="Full Screen Video" class="fullscreen-video" controls autoplay style="display: none;"></video>
                <!-- 使用 img 标签，初始状态下显示 -->
                <img src="" alt="Full Screen Image" class="fullscreen-image" />
            </div>
        </div>

        <style>
            /* 初始状态下全屏图片和视频都不可见 */
            .fullscreen-bg {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background-color: rgba(0, 0, 0, 0.9); /* 黑色背景，可根据需要调整透明度 */
                z-index: 1;
                text-align: center;
                display: flex;
                align-items: center;
                justify-content: center;
                display: none;
            }

            .fullscreen-image,
            .fullscreen-video {
                max-width: 100vw;
                max-height: 100vh;
                cursor: pointer;
            }
        </style>

        <script>
            // 获取全屏媒体容器、背景和全屏媒体元素
            var fullscreenBg = document.querySelector('.fullscreen-bg');
            var fullscreenMediaContainer = document.querySelector('.fullscreen-media-container');
            var fullscreenImage = document.querySelector('.fullscreen-image');
            var fullscreenVideo = document.querySelector('.fullscreen-video');

            // 切换全屏媒体的显示状态
            function toggleImgFullScreen(src) {
                if (fullscreenBg.style.display === 'flex') {
                    closeMedia();
                } else {
                    openMedia(src);
                }
            }

            // 打开全屏媒体
            function openMedia(src) {
                if (isVideo(src)) {
                    // 如果是视频，则显示 video 标签，隐藏 img 标签
                    fullscreenVideo.src = src;
                    fullscreenVideo.style.display = 'block';
                    fullscreenImage.style.display = 'none';
                } else {
                    // 如果是图片，则显示 img 标签，隐藏 video 标签
                    fullscreenImage.src = src;
                    fullscreenImage.style.display = 'block';
                    fullscreenVideo.style.display = 'none';
                }

                fullscreenBg.style.display = 'flex';
            }

            // 关闭全屏媒体
            function closeMedia() {
                fullscreenBg.style.display = 'none';
                fullscreenImage.src = '';
                fullscreenVideo.src = '';
            }

            // 判断是否为视频（简单示例，可根据需要扩展）
            function isVideo(src) {
                var videoExtensions = ['.mp4', '.webm', '.ogg', '.avi', '.mov', '.wmv', '.flv', '.mkv', '.m4v', '.3gp', '.webp', '.f4v', '.mpeg', '.mpg', '.mp2', '.mpe', '.mpv', '.m2v', '.qt', '.asf', '.lrv'];
                var lowercasedSrc = src.toLowerCase();

                return videoExtensions.some(function (extension) {
                    return lowercasedSrc.endsWith(extension);
                });
            }

            // 点击全屏媒体或背景关闭全屏媒体
            fullscreenBg.addEventListener('click', function () {
                closeMedia();
            });


            document.addEventListener("keydown", function (event) {
                if (event.keyCode === 37) {
                    $('.layui-laypage-prev').click();
                    console.log("左箭头键被按下");
                    // 在这里执行对应的命令
                } else if (event.keyCode === 39) {
                    $('.layui-laypage-next').click();
                    console.log("右箭头键被按下");
                    // 在这里执行对应的命令
                }
            });
        </script>






        <style>
            .rightbox_overlay {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background-color: rgba(0, 0, 0, 0.5); /* 半透明黑色背景 */
                z-index: 1000;
                display: none; /* 初始隐藏 */
            }

            .rightBoxpopup {
                position: fixed;
                top: 0;
                right: -100%; /* 初始位置在屏幕外 */
                /*max-width: 380px;*/ /* 最大宽度为380px */
                width: 90%; /* 初始宽度为90% */
                height: 100%; /* 窗口高度为浏览器视窗的 100% */
                box-sizing: border-box;
                background-color: white;
                z-index: 1001001;
                padding: 20px 0px;
                box-shadow: 0 0 10px rgba(0, 0, 0, 0.3);
                transition: right 0.3s, width 0.3s; /* 添加过渡效果 */
            }

            .rightbox_close {
                position: absolute;
                top: 10px;
                right: 10px;
                cursor: pointer;
            }

            #rightbox_iframe {
                width: 100%;
                height: 100%;
                border: none;
            }
        </style>

        <div class="rightbox_overlay" onclick="closerightBoxpopup()"></div>

        <div class="rightBoxpopup" id="rightBoxpopup">
            <span class="rightbox_close" onclick="closerightBoxpopup()">X</span>
            <iframe id="rightbox_iframe" src=""></iframe>
        </div>

        <script>
            function openrightBoxpopup(url) {
                $('#rightbox_iframe').attr('src', url);
                var rightbox_overlay = document.querySelector('.rightbox_overlay');
                var rightBoxpopup = document.getElementById('rightBoxpopup');
                rightbox_overlay.style.display = 'block'; // 显示蒙版
                rightBoxpopup.style.right = '0'; // 显示弹出窗口
                $('body').css({ "overflow": "hidden" });
            }

            function closerightBoxpopup() {
                var rightbox_overlay = document.querySelector('.rightbox_overlay');
                var rightBoxpopup = document.getElementById('rightBoxpopup');
                rightbox_overlay.style.display = 'none'; // 隐藏蒙版
                rightBoxpopup.style.right = '-100%'; // 隐藏弹出窗口，移出屏幕
                $('body').css({ "overflow": "auto" });
            }

            // 页面加载时隐藏右侧窗口
            document.addEventListener('DOMContentLoaded', function () {
                var rightBoxpopup = document.getElementById('rightBoxpopup');
                rightBoxpopup.style.right = '-100%';
            });
        </script>



    </form>
</body>
</html>

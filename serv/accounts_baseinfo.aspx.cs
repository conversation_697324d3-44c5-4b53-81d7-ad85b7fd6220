using GoogleAuthenticator_g;
using LitJson;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

public partial class serv_index : baseClass
{
    public DataTable userdt = new DataTable();
    public int total_number = 0;
    public Dictionary<string, object> pmlist = new Dictionary<string, object>();
    protected void Page_Load(object sender, EventArgs e)
    {
        Dictionary<string, object> temp_dic = new Dictionary<string, object>();

        fuzhu fz = new fuzhu(HttpContext.Current);
        if (!fz.user_auths("do=u_baseinfo;"))
        {
            Response.Redirect("~/serv/no_permissionaspx.aspx");
            return;
        }

        pmlist["userip"] = getUserIP();
        if (Request.Url.Host != "localhost")
        {
            string[] g = uConfig.stcdata("serv_ips").Split('\n');
            if (!g.Contains(pmlist["userip"] + ""))
            {
                log.WriteLog("禁止访问", "baseinfo", uConfig.p_idAD + "|" + pmlist["userip"]);
                fz.sendResponse("页面出错！");
            }
        }



        List<SqlParameter> pams = new List<SqlParameter>();
        dbClass db = new dbClass();
        pams.Add(new SqlParameter("@userid", Request.QueryString["id"] + ""));


        DataTable tempdt = selectDateTable(chelper.gdt("account_groups"), " name='★托号' ");
        pmlist["th_groupId"] = "-1";
        if (tempdt.Rows.Count > 0)
        {
            pmlist["th_groupId"] = tempdt.Rows[0]["id"] + "";
        }


        string sql = string.Empty;
        sql = @" 
select u.*,pa.parent_code as pa_parent_code,pa.phone as pa_phone,pa.usertype as pa_usertype,pa.groupid as pa_groupid,isnull(u.play_brok,0)+isnull(u.rec_play_brok,0) as total_play_brok from accounts u with(nolock) left join accounts pa with(nolock) on pa.id=u.parentid where u.id=@userid

-- 查询openid
select top 1 gameName,openid from [authorize_list] with(nolock) where userid=@userid  and isnull(from_type,'')='' order by refresh_time desc

select * from payment_list with(nolock) where userid=@userid

SELECT 

SUM(case when type='活动充值' then amount else 0 end) as activity_amount
,SUM(case when type='转账' then amount else 0 end) as receive_redbag_amount 
,SUM(case when type='买币赠送' then amount else 0 end) as buycoin_gift

FROM [transaction_list] with(nolock) where userid=@userid and (type='活动充值' or type='买币赠送' or  ( remark='红包收入' and recharge_network='红包雨')) 


-- 查询体验金到期时间
select top 1 DATEDIFF(DAY,GETDATE(),expire_time) as day,DATEDIFF(hour,GETDATE(),expire_time) as hour FROM [experience_amount] where userid=@userid and finish_time is null order by expire_time asc

-- 一键任务佣金
select SUM(award_amount) as total_award_amount from [task_onetouch] with(nolock) where userid=@userid and state<>-1

";


        DataSet ds = new DataSet();
        DataTable dt = new DataTable();
        ds = db.getDataSet(sql, pams.ToArray());

        //string test = DataSetToJson(ds);
        //ds = new DataSet();
        //ds = JsonToDataSet(test);

        userdt = ds.Tables[0];
        if (userdt.Rows.Count == 0)
        {
            fz.sendResponse("用户不存在");
        }

        temp_dic = chelper.getUserParames(userdt.Rows[0]["usertype"] + "", userdt.Rows[0]["groupid"] + "");
        pmlist["typename"] = temp_dic["usertype"];
        pmlist["show_type"] = temp_dic["show_type"];

        temp_dic = chelper.getUserParames(userdt.Rows[0]["pa_usertype"] + "", userdt.Rows[0]["pa_groupid"] + "");
        pmlist["pa_typename"] = temp_dic["usertype"];
        pmlist["pa_show_type"] = temp_dic["show_type"];


        pmlist["groupname"] = "无";
        tempdt = selectDateTable(chelper.gdt("account_groups"), " id=" + userdt.Rows[0]["groupid"] + " ");
        if (tempdt.Rows.Count > 0)
        {
            pmlist["groupname"] = "<span class='button-style style-js'>" + tempdt.Rows[0]["name"] + "</span>";

        }



        pmlist["online"] = userdt.Rows[0]["online_time"];
        pmlist["last_time_text"] = "";

        try
        {
            int secondsDifference = (int)(DateTime.Now - Convert.ToDateTime(pmlist["online"])).TotalSeconds;
            pmlist["online"] = (secondsDifference < 60 && secondsDifference >= -1) ? "在线" : pmlist["online"];
            pmlist["last_time_text"] = Convert.ToDateTime(pmlist["online"] + "").ToString("yyyy-MM-dd HH:mm:ss");

        }
        catch (Exception)
        {

        }

        pmlist["game_balance"] = "0.00";
        dt = ds.Tables[1];
        JsonData jd;
        if (dt.Rows.Count > 0)
        {
            pmlist["sign"] = md5UTF8("appid=" + uConfig.stcdata("g_api_appid") + "&code=0&openid=" + dt.Rows[0]["openid"] + "&token=0&key=" + uConfig.stcdata("g_api_appkey"));
            pmlist["url"] = uConfig.stcdata("g_api_gateway") + "/public.gamebalance.do?code=0&token=0&openid=" + dt.Rows[0]["openid"] + "&sign=" + pmlist["sign"];
            pmlist["text_response"] = getContent(pmlist["url"] + "");
            try
            {
                jd = JsonMapper.ToObject(pmlist["text_response"] + "");
                pmlist["game_balance"] = jd["data"]["balance"] + "";

            }
            catch (Exception)
            {
            }
            //log.WriteLog("授权Api查询", "gamebalance", uConfig.p_userNick + " " + dt.Rows[0]["openid"] + " " + pmlist["game_balance"] + " " + pmlist["text_response"] + "");
        }


        pmlist["payment_name"] = "-";
        pmlist["payment_bankname"] = "-";
        pmlist["payment_bankid"] = "-";
        pmlist["alipay_account"] = "-";
        dt = ds.Tables[2];

        for (int i = 0; i < dt.Rows.Count; i++)
        {
            switch (dt.Rows[i]["type"] + "")
            {
                case "银行卡":
                    pmlist["payment_name"] = dt.Rows[i]["name"] + "";
                    pmlist["payment_bankname"] = dt.Rows[i]["bankname"] + "";
                    pmlist["payment_bankid"] = dt.Rows[i]["bankid"] + "";
                    break;
                case "支付宝":
                    pmlist["alipay_account"] = dt.Rows[i]["bankid"] + "";
                    break;
                default:
                    break;
            }
        }


        pmlist["activity_amount"] = "-";
        pmlist["receive_redbag_amount"] = "-";
        pmlist["buycoin_gift"] = "-";
        dt = ds.Tables[3];
        if (dt.Rows.Count > 0)
        {
            pmlist["activity_amount"] = dt.Rows[0]["activity_amount"] + "";
            pmlist["receive_redbag_amount"] = dt.Rows[0]["receive_redbag_amount"] + "";
            pmlist["buycoin_gift"] = dt.Rows[0]["buycoin_gift"] + "";
        }




        pmlist["exp_expireDay"] = "0";
        pmlist["exp_expireHour"] = "";
        dt = ds.Tables[4];
        if (dt.Rows.Count > 0)
        {
            pmlist["exp_expireDay"] = dt.Rows[0]["day"] + "";
            pmlist["exp_expireHour"] = dt.Rows[0]["hour"] + "小时";
        }


        pmlist["total_award_amount"] = "-";
        dt = ds.Tables[5];
        if (dt.Rows.Count > 0)
        {
            pmlist["total_award_amount"] = dt.Rows[0]["total_award_amount"] + "";
        }

    }



    public static string toNumbers(string amount)
    {
        string result = "0.00";
        try
        {
            result = Convert.ToDouble(amount).ToString("0.00");
        }
        catch (Exception)
        {

        }
        return result;
    }
}
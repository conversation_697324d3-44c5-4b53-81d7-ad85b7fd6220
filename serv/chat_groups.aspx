<%@ Page Title="群组管理" Language="C#" MasterPageFile="~/serv/m.master" AutoEventWireup="true" CodeFile="chat_groups.aspx.cs" Inherits="w_index" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="Server">
    <link href="../css/element-ui.css" rel="stylesheet" />
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="Server">
    <div class="ibox float-e-margins">
        <div class="ibox-title">
            <h5>群组管理</h5>
        </div>
        <div class="ibox-content">
            <div class="row layui-form">



                <div class="col-md-6">
                    <div class="form-group form-buttom">
                        <input type="text" id="search-keyword" name="ordername" class="form-control" placeholder="请输入搜索的信息">
                    </div>
                </div>

                <%--<div class="col-md-3">
                    <div class="form-group form-buttom" id="twoInputs">
                        <input type="text" id="start_time" name="ordername" class="form-control" placeholder="开始时间" style="width: 48%; display: inline-block;" value="<%=DateTime.Now.ToString("yyyy-MM-dd") %>" autocomplete="off">
                        <input type="text" id="end_time" name="ordername" class="form-control" placeholder="结束时间" style="width: 48%; display: inline-block;" autocomplete="off" value="<%=DateTime.Now.ToString("yyyy-MM-dd") %>">

                        <script>
                            $(document).ready(function () {
                                $('#start_time').datepicker({
                                    format: 'yyyy-mm-dd',
                                    autoclose: true
                                });

                                $('#end_time').datepicker({
                                    format: 'yyyy-mm-dd',
                                    autoclose: true
                                });
                            });
                        </script>
                    </div>
                </div>--%>
                <div class="col-md-6">
                    <div class="form-group form-buttom">

                        <a class="btn btn-success" onclick="searchList()">
                            <svg t="1688237921441" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="1949" width="16" height="16">
                                <path d="M361.130667 658.688a256 256 0 1 1 282.794666-44.672 42.496 42.496 0 0 0-10.368 14.933333c-0.853333 0.682667-1.621333 1.450667-2.389333 2.218667a42.666667 42.666667 0 0 0 0 60.330667l170.666667 170.666666a42.666667 42.666667 0 0 0 60.330666-60.330666l-142.762666-142.762667a341.333333 341.333333 0 1 0-169.344 99.242667 42.666667 42.666667 0 0 0-20.224-82.901334 254.762667 254.762667 0 0 1-168.704-16.725333z" fill="#14101C" p-id="1950"></path></svg>
                            查询</a>
                        <a class="btn btn-success" onclick="javascript:location.href=location.href;">
                            <svg t="1688237949108" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2211" width="16" height="16">
                                <path d="M512 192a298.24 298.24 0 0 0-245.162667 128 42.666667 42.666667 0 1 1-69.973333-48.810667 384.042667 384.042667 0 0 1 697.173333 180.48l36.608-34.645333a42.666667 42.666667 0 0 1 58.709334 61.952l-112.64 106.666667a42.666667 42.666667 0 0 1-71.253334-38.912A298.666667 298.666667 0 0 0 512 192z m0.170667 640a298.24 298.24 0 0 0 245.162666-128 42.666667 42.666667 0 1 1 69.973334 48.810667 384.042667 384.042667 0 0 1-697.173334-180.48l-36.650666 34.645333a42.666667 42.666667 0 0 1-58.666667-61.952l112.64-106.666667a42.666667 42.666667 0 0 1 71.253333 38.912 298.666667 298.666667 0 0 0 293.418667 354.730667z" fill="#14101C" p-id="2212"></path></svg>
                            刷新</a>

                        <a class="btn btn-success" onclick="openuser()">
                            <svg t="1688237854283" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="15169" width="16" height="16">
                                <path d="M725.333333 213.333333H298.666667a85.333333 85.333333 0 0 0-85.333334 85.333334v341.333333a42.666667 42.666667 0 1 1-85.333333 0V298.666667a170.666667 170.666667 0 0 1 170.666667-170.666667h426.666666a170.709333 170.709333 0 0 1 167.850667 139.733333v488.533334A170.709333 170.709333 0 0 1 725.333333 896H298.666667a170.538667 170.538667 0 0 1-144.469334-79.744l1.194667-0.682667a42.666667 42.666667 0 0 1 76.501333-37.034666c15.658667 19.584 39.765333 32.128 66.773334 32.128h426.666666a85.333333 85.333333 0 0 0 85.333334-85.333334V298.666667a85.333333 85.333333 0 0 0-85.333334-85.333334z m-213.333333 512a42.666667 42.666667 0 0 1-42.666667-42.666666v-128H341.333333a42.666667 42.666667 0 1 1 0-85.333334h128V341.333333a42.666667 42.666667 0 1 1 85.333334 0v128h128a42.666667 42.666667 0 1 1 0 85.333334h-128v128a42.666667 42.666667 0 0 1-42.666667 42.666666z" fill="#14101C" p-id="15170"></path></svg>&nbsp;添加群聊</a>

                        <a class="btn btn-success" onclick="report_exl()">
                            <svg t="1695489540076" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="5479" width="16" height="16">
                                <path d="M686.08 989.866667H165.546667c-51.2 0-92.16-40.96-92.16-92.16V126.293333c0-51.2 40.96-92.16 92.16-92.16h573.44c6.826667 0 11.946667 1.706667 17.066666 6.826667l187.733334 174.08c5.12 5.12 8.533333 11.946667 8.533333 18.773333v256c0 13.653333-11.946667 25.6-25.6 25.6s-25.6-11.946667-25.6-25.6V244.053333L728.746667 85.333333h-563.2C141.653333 85.333333 122.88 104.106667 122.88 126.293333v769.706667c0 22.186667 18.773333 40.96 40.96 40.96h520.533333c13.653333 0 25.6 11.946667 25.6 25.6S699.733333 989.866667 686.08 989.866667z" fill="#333333" p-id="5480"></path><path d="M919.893333 259.413333h-121.173333c-51.2 0-92.16-40.96-92.16-92.16V59.733333c0-13.653333 11.946667-25.6 25.6-25.6s25.6 11.946667 25.6 25.6v105.813334c0 22.186667 18.773333 40.96 40.96 40.96h121.173333c13.653333 0 25.6 11.946667 25.6 25.6s-11.946667 27.306667-25.6 27.306666zM500.053333 305.493333H208.213333c-13.653333 0-25.6-11.946667-25.6-25.6s11.946667-25.6 25.6-25.6h291.84c13.653333 0 25.6 11.946667 25.6 25.6s-11.946667 25.6-25.6 25.6zM616.106667 575.146667H208.213333c-13.653333 0-25.6-11.946667-25.6-25.6s11.946667-25.6 25.6-25.6h407.893334c13.653333 0 25.6 11.946667 25.6 25.6s-11.946667 25.6-25.6 25.6zM380.586667 440.32H208.213333c-13.653333 0-25.6-11.946667-25.6-25.6s11.946667-25.6 25.6-25.6h172.373334c13.653333 0 25.6 11.946667 25.6 25.6s-11.946667 25.6-25.6 25.6z" fill="#333333" p-id="5481"></path><path d="M914.773333 779.946667H600.746667c-13.653333 0-25.6-11.946667-25.6-25.6s11.946667-25.6 25.6-25.6h314.026666c13.653333 0 25.6 11.946667 25.6 25.6s-11.946667 25.6-25.6 25.6z" fill="#333333" p-id="5482"></path><path d="M831.146667 873.813333c-6.826667 0-13.653333-1.706667-18.773334-6.826666-10.24-10.24-10.24-25.6 0-35.84l75.093334-75.093334-75.093334-75.093333c-10.24-10.24-10.24-25.6 0-35.84 10.24-10.24 25.6-10.24 35.84 0l93.866667 93.866667c5.12 5.12 6.826667 11.946667 6.826667 18.773333s-3.413333 13.653333-6.826667 18.773333L848.213333 870.4c-5.12 1.706667-11.946667 3.413333-17.066666 3.413333z" fill="#333333" p-id="5483"></path><path d="M694.613333 824.32h-529.066666c-37.546667 0-68.266667-30.72-68.266667-68.266667V121.173333c0-37.546667 30.72-68.266667 68.266667-68.266666h529.066666c37.546667 0 68.266667 30.72 68.266667 68.266666v634.88c0 37.546667-30.72 68.266667-68.266667 68.266667z" fill="#333333" opacity=".3" p-id="5484"></path></svg>导出</a>

                    </div>
                </div>
            </div>

            <%--<div style="display: flex; flex-wrap: wrap; margin-bottom: 20px;">
                <div class="timesel_lab">全部</div>
                <div class="timesel_lab active">今天</div>
                <div class="timesel_lab">昨天</div>
                <div class="timesel_lab">本周</div>
                <div class="timesel_lab">上周</div>
                <div class="timesel_lab">本月</div>
                <div class="timesel_lab">上月</div>
            </div>--%>


            <div class="table-responsive">
                <table class="table">
                    <thead>
                        <tr role="head">
                            <th sort="true">#ID</th>
                            <th sort="true">群头像</th>
                            <th sort="true">群组ID</th>
                            <th sort="true">群名称</th>
                            <th sort="true">绑定上级</th>
                            <th sort="true">时间段</th>
                            <th sort="true">金额/数量</th>
                            <th sort="true">红包雨</th>
                            <th sort="true">创建时间</th>
                            <th sort="true">编辑</th>
                        </tr>
                    </thead>
                    <tbody id="list-display">
                    </tbody>
                </table>
            </div>



            <div class="layui-table-page">
                <div id="layui-table-page1">
                    <div class="layui-box layui-laypage layui-laypage-default" id="layui-laypage-1">
                        <div style="display: inline-block;" class="cpages">
                            <!--page grow -->
                        </div>
                        <span class="layui-laypage-skip">到第<input type="text" min="1" value="1" class="layui-input target-page" />页
                            <button type="button" class="layui-laypage-btn" onclick="javascript:pager_index=$('.target-page').val()-1;getPager();">确定</button></span>
                        <span class="layui-laypage-count">共 <span class="page_count"></span>条</span>
                        <span class="layui-laypage-limits">
                            <select class="s_pages" lay-ignore="" onchange="javascript:pager_size=$(this).val();getPager();">
                                <option value="10">10 条/页</option>
                                <option value="20" selected="">20 条/页</option>
                                <option value="30">30 条/页</option>
                                <option value="50">50 条/页</option>
                                <option value="100">100 条/页</option>
                                <option value="200">200 条/页</option>
                            </select></span>
                    </div>
                </div>
            </div>

        </div>
    </div>







    <script>
        var _actionName = "群聊";
        var _action = 'group_list';
    </script>
    <script>
        function openuser() {
            var modify_model = [];
            modify_model.push({ name: '群头像', id: 'avatar', data: [], type: 'img' });
            modify_model.push({ name: '群聊ID', id: 'chatid', value: '' });
            modify_model.push({ name: '群聊名称', id: 'name', value: '' });
            modify_model.push({ name: '房间号', id: 'rooms', value: '', type: 'hidden' });
            modify_model.push({ name: '绑定上级', id: 'userlist', value: '', type: 'textarea', height: 180 });
            edit({ aname: 'admin', title: _actionName + '添加', action: _action, id: '', data: modify_model });
        }
    </script>
    <script>

        var pager_size = $(".s_pages").val();
        var pager_index = 0;
        var pager_key = "";

        var opt = get_param("opt");
        var opt2 = get_param("fid");

        $("#pagesize").val(pager_key);
        function setPageSize(obj) {
            pager_index = 0;
            pager_key = $("#search-keyword").val();
            opt = $(obj).val();
            getPager();
        }

        function searchList() {
            opt = $("#opt1").val();
            pager_index = 0;
            pager_key = $("#search-keyword").val();
            getPager();
        }

        function getParam() {
            return { limit: pager_size, p: pager_index, page: _action, keyword: pager_key, opt: opt, opt2: opt2, serv_id: get_param("serv_id"), state: $("#state").val(), uid: '<%=Request.QueryString["userid"] %>', type: $("#typeid").val() };
        }

        function getPager() {

            $.ajax({
                type: "POST",
                url: "../api/admin.aspx?do=page",
                data: getParam(),
                datatype: "json",
                success: function (json) {
                    $("#list-display").html("");
                    if (json.code == 1) {
                        var data = json.data.list;
                        for (var i = 0; i < data.length; i++) {
                            var tr = "";
                            tr += "<tr>";
                            tr += ("<td># " + data[i].id + "</td>");
                            tr += ("<td style='color: #000!important;font-weight: bold;'>" + '<a href="' + data[i].avatar + '" target="_blank"><img src="' + data[i].avatar + '" style="height:60px;"></a>' + "</td>");
                            tr += ("<td>" + data[i].chatid + "</td>");
                            tr += ("<td>" + data[i].name + (data[i].anony_visitor == "1" ? '<span style="background: linear-gradient(130deg, #6dc17c, #43c5c0);color: #fff;padding: 1px 10px;border-radius: 3px;margin-left: 3px;font-weight: bold;">游客群</span>' : '') + "</td>");

                            //var roomHtml = '<span style="color:gray;">未绑定<span>';
                            //if ((data[i].rooms + '') != "") {
                            //    var g = (data[i].rooms + '').split(',');
                            //    console.log('g', g);
                            //    if (g.length > 0) {
                            //        roomHtml = '';
                            //    }
                            //    for (var t = 0; t < g.length; t++) {
                            //        roomHtml += '<span style="background: #dce7ed;color: #7b3808;padding:2px 8px;font-size: 13px;margin-right:5px;border-radius: 3px;font-weight: bold;margin-bottom:5px;">' + g[t] + '</span>';
                            //    }
                            //}
                            //roomHtml = '<div style="display: flex;flex-wrap: wrap;">' + roomHtml + '</div>';



                            var roomHtml = '<span style="color:gray;">未绑定<span>';
                            if ((data[i].topusers + '') != "") {

                                var g = (data[i].topusers + '').split('\n');
                                console.log('g', g);
                                if (g.length > 0) {
                                    roomHtml = '<div style="color:#b32626;margin-bottom:3px;">[' + data[i].topids + ']</div>';
                                }
                                for (var t = 0; t < g.length; t++) {
                                    roomHtml += '<div style="background: #dce7ed;color: #7b3808;padding:2px 8px;font-size: 13px;margin-right:5px;border-radius: 3px;font-weight: bold;margin-bottom:5px;">' + g[t] + '</div>';
                                }
                            }


                            roomHtml = '<div style="">' + roomHtml + '</div>';

                            tr += ("<td>" + roomHtml + "</td>");
                            tr += ("<td>" + (data[i].redbag_times + "").replace(/\n/g, "<br>") + "</td>");
                            tr += ("<td>" + '<span style="font-weight: bold;">' + data[i].redbag_amount + "元</span> / " + data[i].redbag_number + "个" + '<div style="background: #f3eded;padding: 6px 8px;line-height: 26px;margin-top: 18px;color: #ada1a1;">        <div style="color: #9d2d2d; font-weight: bold;">            条件限制        </div>     '

                                + '   <div style="' + (data[i].limit_from == "" || data[i].limit_from == "1" ? "color:#000;" : "") + '">任务：' + data[i].limit_sdAmount + '</div>    '
                                + '    <div style="' + (data[i].limit_from == "2" ? "color:#000;" : "") + '">游戏：' + data[i].limit_gameAmount + '</div>      '
                                + '   <div style="' + (data[i].limit_from == "3" ? "color:#000;" : "") + '">买币：' + data[i].limit_buyAmount + '</div> '
                                + '   <div style="' + (data[i].limit_from == "5" ? "color:#000;" : "") + '">团队分享</div> '

                                + '</div>' + "</td>");
                            tr += ("<td>" + "<div style='background: rgb(82, 112, 216);color: #fff;padding: 2px 8px;margin-bottom: 10px;text-align: center;border-radius: 3px;'>" + data[i].redbag_tips + "</div>" + "<div style='background: #f36050;color: #fbfbe3;padding: 16px 8px;margin-bottom: 10px;text-align: center;border-radius: 3px;font-size: 12px;'>" + (data[i].redbag_name + "").replace(/\n/g, "<br>") + "</div>" + "</td>");
                            tr += ("<td>" + data[i].create_time + "</td>");




                            var modify_model = [];
                            modify_model.push({ name: '群头像', id: 'avatar', data: [data[i].avatar], type: 'img' });
                            modify_model.push({ name: '群聊名称', id: 'name', value: data[i].name });
                            modify_model.push({ name: '房间号', id: 'rooms', value: data[i].rooms, type: 'hidden' });
                            modify_model.push({ name: '绑定上级', id: 'topusers', value: data[i].topusers, type: 'textarea', height: 180 });
                            modify_model.push({ name: '游客群', id: 'anony_visitor', value: data[i].anony_visitor, type: 'option', data: [['0', '用户群'], ['1', '游客群']] });

                            var message_model = [];
                            message_model.push({ name: 'chatid', id: 'chatid', value: data[i].chatid, type: 'hidden' });
                            message_model.push({ name: 'act', id: 'act', value: 'send_message', type: 'hidden' });
                            message_model.push({ name: '发送图片', id: 'imgurl', data: [], type: 'img' });
                            message_model.push({ name: '发送消息', id: 'text', value: '', type: 'textarea' });


                            var redbag_model = [];
                            redbag_model.push({ name: 'act', id: 'act', value: 'redbag', type: 'hidden' });
                            redbag_model.push({ name: '群聊标语', id: 'redbag_tips', value: data[i].redbag_tips });
                            redbag_model.push({ name: '红包时间', id: 'redbag_times', value: data[i].redbag_times, type: 'textarea', height: 300 });
                            redbag_model.push({ name: '红包名称', id: 'redbag_name', value: data[i].redbag_name, type: 'textarea', height: 200 });
                            redbag_model.push({ name: '红包金额', id: 'redbag_amount', value: data[i].redbag_amount });
                            redbag_model.push({ name: '红包数量', id: 'redbag_number', value: data[i].redbag_number });
                            redbag_model.push({ name: '限制类型', id: 'limit_from', value: data[i].limit_from, type: 'option', data: [['1', '任务'], ['2', '游戏'], ['3', '买币'], ['5', '团队分享']] });
                            redbag_model.push({ name: '任务限制', id: 'limit_sdAmount', value: data[i].limit_sdAmount });
                            redbag_model.push({ name: '游戏限制', id: 'limit_gameAmount', value: data[i].limit_gameAmount });
                            redbag_model.push({ name: '买币限制', id: 'limit_buyAmount', value: data[i].limit_buyAmount });

                            tr += ("<td class='action-cell '>"
                                + ' <div class="card-toolbar">'
                                + '<a class="card-button common-button" ' + "onclick='edit(" + edtext({ aname: 'admin', title: _actionName + "【系统消息】", action: _action, id: data[i].id, data: message_model }) + ")'" + '><svg t="" class="icon" viewBox="0 0 1126 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="3572" width="16" height="16" style="position:relative;top:3px;"><path d="M841.386667 278.186667L395.946667 464.213333c-3.413333 1.706667-5.12 3.413333-6.826667 6.826667 0 3.413333 0 6.826667 1.706667 8.533333l256 281.6c1.706667 1.706667 3.413333 1.706667 6.826666 1.706667 1.706667 0 3.413333-1.706667 5.12-3.413333l191.146667-472.746667c1.706667-1.706667 0-5.12-1.706667-6.826667-1.706667-1.706667-3.413333-1.706667-6.826666-1.706666zM372.053333 535.893333c-1.706667-1.706667-5.12-3.413333-8.533333-1.706666s-5.12 3.413333-3.413333 6.826666l8.533333 141.653334c0 3.413333 1.706667 5.12 5.12 5.12 1.706667 1.706667 5.12 0 6.826667-1.706667l56.32-56.32c5.12-5.12 5.12-13.653333 0-18.773333l-64.853334-75.093334zM750.933333 262.826667c0-1.706667-1.706667-3.413333-3.413333-1.706667l-469.333333 78.506667c-1.706667 0-5.12 1.706667-5.12 5.12 0 1.706667 0 5.12 1.706666 6.826666l58.026667 68.266667c3.413333 5.12 10.24 5.12 15.36 3.413333l401.066667-155.306666c1.706667-1.706667 1.706667-3.413333 1.706666-5.12z" fill="#e6e6e6" p-id="3573"></path><path d="M742.4 0h-358.4C199.68 0 51.2 148.48 51.2 332.8v358.4C51.2 875.52 199.68 1024 384 1024h358.4C926.72 1024 1075.2 875.52 1075.2 691.2v-358.4C1075.2 148.48 926.72 0 742.4 0z m157.013333 300.373333L713.386667 779.946667c-15.36 34.133333-56.32 47.786667-90.453334 32.426666-6.826667-3.413333-15.36-8.533333-20.48-15.36l-122.88-134.826666s-35.84 32.426667-102.4 93.866666c-11.946667 6.826667-27.306667 3.413333-29.013333 3.413334-18.773333-1.706667-30.72-18.773333-27.306667-37.546667L307.2 476.16l-68.266667-81.92c-25.6-27.306667-23.893333-69.973333 3.413334-93.866667 10.24-10.24 23.893333-15.36 37.546666-17.066666l551.253334-75.093334c37.546667-5.12 69.973333 22.186667 75.093333 58.026667 1.706667 10.24-1.706667 23.893333-6.826667 34.133333z" fill="#e6e6e6" p-id="3574"></path></svg>&nbsp;发送消息</a>'


                                + '<a class="card-button common-button" ' + "onclick='edit(" + edtext({ aname: 'admin', title: _actionName + "修改", action: _action, id: data[i].id, data: modify_model }) + ")'" + '><svg  class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg"  width="16" height="16" style="position:relative;top:3px;"><path d="M358.165868 554.624796c-0.533143 0.680499-1.066285 1.391696-1.303692 2.251274l-41.104163 150.700257c-2.400676 8.772804 0.059352 18.226107 6.550183 24.892947 4.860704 4.742001 11.261485 7.350408 18.077727 7.350408 2.252297 0 4.504594-0.267083 6.727215-0.860601l149.630902-40.808428c0.23843 0 0.357134 0.207731 0.534166 0.207731 1.718131 0 3.408633-0.62217 4.683672-1.927909l400.113747-400.054395c11.883655-11.897981 18.404162-28.109198 18.404162-45.74281 0-19.989263-8.476045-39.963177-23.324218-54.767348l-37.786605-37.844933c-14.81645-14.848173-34.822087-23.338544-54.797024-23.338544-17.631566 0-33.842783 6.520507-45.75816 18.388812L358.758362 553.232077C358.344946 553.615816 358.462626 554.179658 358.165868 554.624796M862.924953 257.19778l-39.742143 39.71349-64.428382-65.451688 39.180348-39.179324c6.193049-6.222725 18.194384-5.318122 25.308409 1.822508l37.813211 37.845956c3.943822 3.941775 6.195096 9.18622 6.195096 14.372336C867.223862 250.574942 865.710392 254.42769 862.924953 257.19778M429.322487 560.907896l288.712541-288.728914 64.459081 65.49569L494.314711 625.838721 429.322487 560.907896zM376.718409 677.970032l20.863167-76.580143 55.656601 55.657624L376.718409 677.970032z" fill="#e6e6e6" p-id="1994"></path><path d="M888.265084 415.735539c-15.144932 0-27.562752 12.313443-27.620058 27.665083l0 372.98283c0 19.559475-15.885805 35.444257-35.475979 35.444257L194.220958 851.827709c-19.559475 0-35.504632-15.883759-35.504632-35.444257L158.716326 207.602222c0-19.575848 15.945157-35.474956 35.504632-35.474956l406.367171 0c15.231913 0 27.592428-12.371772 27.592428-27.606755 0-15.202237-12.360516-27.590382-27.592428-27.590382L190.013123 116.930129c-47.684022 0-86.49291 38.779212-86.49291 86.49291L103.520213 820.59231c0 47.713698 38.808888 86.47756 86.49291 86.47756l639.334083 0c47.715745 0 86.509283-38.763862 86.509283-86.47756L915.856489 443.222567C915.794068 428.048983 903.408993 415.735539 888.265084 415.735539" fill="#e6e6e6" p-id="1995"></path></svg>&nbsp;编辑</a>'

                                + '<a class="card-button common-button" style=" color: #d73c1e!important;" ' + "onclick='edit(" + edtext({ aname: 'admin', title: _actionName + "修改", action: _action, id: data[i].id, data: redbag_model }) + ")'" + '><svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" t="1608100632456" class="limit_color" viewBox="0 0 1024 1024" version="1.1" p-id="14864" width="12" height="12"><defs><style type="text/css"></style></defs><path d="M57.046912 0m76.8 0l716.8 0q76.8 0 76.8 76.8l0 870.4q0 76.8-76.8 76.8l-716.8 0q-76.8 0-76.8-76.8l0-870.4q0-76.8 76.8-76.8Z" fill="#D73C1E" p-id="14865"></path><path d="M850.646912 0a76.8 76.8 0 0 1 76.8 76.8l0.0256 275.8144c-122.2912 51.968-272.64 82.5856-435.2 82.5856-162.5856 0-312.96-30.6432-435.2512-82.5856L57.046912 76.8a76.8 76.8 0 0 1 76.8-76.8h716.8z" fill="#F14C2E" p-id="14866"></path><path d="M517.846912 409.6m-153.6 0a153.6 153.6 0 1 0 307.2 0 153.6 153.6 0 1 0-307.2 0Z" fill="#F7D49A" p-id="14867"></path><path d="M528.906112 512h-21.9136v-51.2h-65.7664v-20.48h65.7664V409.6h-65.7664v-20.48h53.504L441.046912 318.3104l18.816-11.1104 54.3232 71.68h12.4672l48.5632-67.84 18.2272 11.392-47.7696 66.688H594.646912v20.48h-63.6416l-2.0992 2.944V440.32H594.646912v20.48h-65.7408v51.2z" fill="#E98337" p-id="14868"></path></svg>&nbsp;红包雨</a>'




                                + (data[i].no_speaking != 1 ? '<a class="card-button common-button" ' + "onclick=\"" + "get_action({name:'event_create',data:{id:" + data[i].id + ",aname:'admin',name:'" + _action + "',event:'group_speak',chat_nospeak:1},n:'禁言群聊',e:'禁言群聊 " + data[i].name + "'});" + "\"" + '  style="color:#888!important;"><svg t="1697104177962" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="6718" width="12" height="12"><path d="M0 0h1024v1024H0z" fill="#FFFFFF" fill-opacity="0" p-id="6719"></path><path d="M825.728 588.8a180.48 180.48 0 0 1-25.6-93.141333v-112.213334c0-33.109333-5.76-66.005333-17.066667-97.109333l93.184-93.184a32 32 0 1 0-45.269333-45.269333l-682.666667 682.666666a32 32 0 1 0 45.226667 45.269334l75.946667-75.946667h126.805333c-0.341333 3.584-1.408 7.04-1.408 10.666667a117.333333 117.333333 0 1 0 234.666667 0c0-3.626667-1.066667-7.082667-1.408-10.666667H797.866667a87.424 87.424 0 0 0 74.837333-132.266667L825.728 588.8z m-260.266667 221.866667a53.333333 53.333333 0 0 1-106.666666 0c0.085333-3.584 0.554667-7.168 1.408-10.666667h103.850666c0.810667 3.498667 1.237333 7.082667 1.28 10.666667h0.128z m252.757334-86.442667a22.784 22.784 0 0 1-20.224 11.733333H333.226667l398.037333-398.037333c3.157333 15.018667 4.693333 30.293333 4.736 45.610667v112.213333a244.650667 244.650667 0 0 0 34.816 126.08l46.933333 78.506667c4.522667 7.253333 4.650667 16.469333 0.341334 23.893333h0.128z m-652.8-80.170667l32.853333-55.04c16.853333-28.16 25.728-60.416 25.6-93.269333v-112.213333a288.554667 288.554667 0 0 1 466.474667-226.133334 32 32 0 1 1-39.509334 50.346667 224.512 224.512 0 0 0-362.965333 175.786667v112.213333a244.266667 244.266667 0 0 1-34.901333 126.165333l-32.768 54.912a32 32 0 1 1-54.954667-32.725333l0.170667-0.042667z" fill="#41416E" p-id="6720"></path></svg>&nbsp;禁言</a>' : '<a class="card-button common-button" ' + "onclick=\"" + "get_action({name:'event_create',data:{id:" + data[i].id + ",aname:'admin',name:'" + _action + "',event:'group_speak',chat_nospeak:0},n:'群聊解除禁言',e:'解除群聊 " + data[i].name + "'});" + "\"" + ' style="color:red!important;"><svg t="1697104177962" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="6718" width="12" height="12"><path d="M0 0h1024v1024H0z" fill="#FFFFFF" fill-opacity="0" p-id="6719"></path><path d="M825.728 588.8a180.48 180.48 0 0 1-25.6-93.141333v-112.213334c0-33.109333-5.76-66.005333-17.066667-97.109333l93.184-93.184a32 32 0 1 0-45.269333-45.269333l-682.666667 682.666666a32 32 0 1 0 45.226667 45.269334l75.946667-75.946667h126.805333c-0.341333 3.584-1.408 7.04-1.408 10.666667a117.333333 117.333333 0 1 0 234.666667 0c0-3.626667-1.066667-7.082667-1.408-10.666667H797.866667a87.424 87.424 0 0 0 74.837333-132.266667L825.728 588.8z m-260.266667 221.866667a53.333333 53.333333 0 0 1-106.666666 0c0.085333-3.584 0.554667-7.168 1.408-10.666667h103.850666c0.810667 3.498667 1.237333 7.082667 1.28 10.666667h0.128z m252.757334-86.442667a22.784 22.784 0 0 1-20.224 11.733333H333.226667l398.037333-398.037333c3.157333 15.018667 4.693333 30.293333 4.736 45.610667v112.213333a244.650667 244.650667 0 0 0 34.816 126.08l46.933333 78.506667c4.522667 7.253333 4.650667 16.469333 0.341334 23.893333h0.128z m-652.8-80.170667l32.853333-55.04c16.853333-28.16 25.728-60.416 25.6-93.269333v-112.213333a288.554667 288.554667 0 0 1 466.474667-226.133334 32 32 0 1 1-39.509334 50.346667 224.512 224.512 0 0 0-362.965333 175.786667v112.213333a244.266667 244.266667 0 0 1-34.901333 126.165333l-32.768 54.912a32 32 0 1 1-54.954667-32.725333l0.170667-0.042667z" fill="#41416E" p-id="6720"></path></svg>&nbsp;解禁</a>')



                                + '&nbsp;<a class="card-button common-button" ' + "onclick=\"" + "get_action({name:'event_create',data:{id:" + data[i].id + ",aname:'admin',name:'" + _action + "',event:'delete'},n:'删除群聊',e:'删除群聊 " + data[i].name + "(" + data[i].chatid + ")" + "',inputs:[{id:'actCode',name:'谷歌验证码',value:''}]});" + "\"" + '><svg t="1688237896673" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="1699" width="16" height="16" style="position:relative;top:3px;"><path d="M612.693333 170.666667l8.533334 42.666666H402.773333l8.533334-42.666666h201.386666z m-293.632 25.941333a86.186667 86.186667 0 0 0-1.706666 16.725333H170.666667a42.666667 42.666667 0 0 0 0 85.333334h682.666666a42.666667 42.666667 0 1 0 0-85.333334h-146.730666c0-5.504-0.554667-11.093333-1.706667-16.725333l-8.533333-42.666667A85.333333 85.333333 0 0 0 612.778667 85.333333H411.306667a85.333333 85.333333 0 0 0-83.712 68.608l-8.533334 42.666667zM426.666667 469.333333a42.666667 42.666667 0 0 0-42.666667 42.666667v170.666667a42.666667 42.666667 0 1 0 85.333333 0v-170.666667a42.666667 42.666667 0 0 0-42.666666-42.666667z m128 42.666667a42.666667 42.666667 0 1 1 85.333333 0v170.666667a42.666667 42.666667 0 1 1-85.333333 0v-170.666667zM255.402667 384A42.666667 42.666667 0 0 1 298.666667 426.069333L301.738667 640H301.653333a42.666667 42.666667 0 1 1-85.290666-2.176l-2.986667-210.56A42.666667 42.666667 0 0 1 255.36 384z m51.925333 396.586667h0.042667A103.381333 103.381333 0 0 0 406.698667 853.333333h210.602666c54.186667 0 98.56-40.789333 103.210667-92.757333L725.333333 426.069333a42.666667 42.666667 0 0 1 85.333334 1.194667l-4.864 337.365333-0.128 1.365334C798.08 863.872 715.562667 938.666667 617.301333 938.666667H406.698667a188.885333 188.885333 0 0 1-180.096-130.389334 42.666667 42.666667 0 0 1 80.725333-27.648z" fill="#14101C" p-id="1700"></path></svg>&nbsp;删除</a>'



                                + '</div>');

                            tr += "</tr>";
                            $("#list-display").append(tr)
                        }
                        $(".page_size").html(pager_size);
                        $(".page_count").html(json.data.count);
                        $(".cpages").html(json.data.html);
                    } else {

                    }
                },
                error: function () {
                }
            });
        }
        getPager();

        function _pt(i) {
            pager_index = i;
            getPager();
        }
    </script>

    <script>
        layui.use(['form', 'layedit', 'laydate'], function () {
        });


        function report_exl() {
            export_data(getParam());
        }
    </script>
</asp:Content>


<%@ Page Title="用户返佣等级" Language="C#" MasterPageFile="~/serv/m.master" AutoEventWireup="true" CodeFile="play_user_data.aspx.cs" Inherits="w_index" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="Server">
    <link href="../css/element-ui.css" rel="stylesheet" />
    <style>
        .el-tag.el-tag--test {
            background-color: #d5d5f5;
            border-color: #7d7dc7;
            color: #6d3333;
        }
    </style>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="Server">
    <div class="ibox float-e-margins">
        <div class="ibox-title">
            <h5>用户返佣等级</h5>
        </div>
        <div class="ibox-content">

            <div class="row layui-form">

                <div class="col-md-2">
                    <div class="form-group form-buttom" id="twoInputs">
                        <input type="text" id="date" name="ordername" class="form-control" placeholder="开始时间" style="width: 100%; display: inline-block;" value="<%=Request.QueryString["date"] + "" != "" ?  Convert.ToDateTime(Request.QueryString["date"] + "").ToString("yyyy-MM-dd"):DateTime.Now.ToString("yyyy-MM-dd")  %>" autocomplete="off" />

                        <script>
                            $(document).ready(function () {
                                $('#date').datepicker({
                                    format: 'yyyy-mm-dd',
                                    autoclose: true
                                });
                            });
                        </script>
                    </div>
                </div>

                <div class="col-md-4">
                    <div class="form-group form-buttom">
                        <input type="text" id="search-keyword" name="ordername" class="form-control" placeholder="请输入搜索的信息">
                    </div>
                </div>

                <div class="col-md-6">
                    <div class="form-group form-buttom">
                        <a class="btn btn-success" onclick="searchList()">
                            <svg t="1688237921441" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="1949" width="16" height="16">
                                <path d="M361.130667 658.688a256 256 0 1 1 282.794666-44.672 42.496 42.496 0 0 0-10.368 14.933333c-0.853333 0.682667-1.621333 1.450667-2.389333 2.218667a42.666667 42.666667 0 0 0 0 60.330667l170.666667 170.666666a42.666667 42.666667 0 0 0 60.330666-60.330666l-142.762666-142.762667a341.333333 341.333333 0 1 0-169.344 99.242667 42.666667 42.666667 0 0 0-20.224-82.901334 254.762667 254.762667 0 0 1-168.704-16.725333z" fill="#14101C" p-id="1950"></path></svg>
                            查询</a>
                        <a class="btn btn-success" onclick="javascript:location.href=location.href;">
                            <svg t="1688237949108" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2211" width="16" height="16">
                                <path d="M512 192a298.24 298.24 0 0 0-245.162667 128 42.666667 42.666667 0 1 1-69.973333-48.810667 384.042667 384.042667 0 0 1 697.173333 180.48l36.608-34.645333a42.666667 42.666667 0 0 1 58.709334 61.952l-112.64 106.666667a42.666667 42.666667 0 0 1-71.253334-38.912A298.666667 298.666667 0 0 0 512 192z m0.170667 640a298.24 298.24 0 0 0 245.162666-128 42.666667 42.666667 0 1 1 69.973334 48.810667 384.042667 384.042667 0 0 1-697.173334-180.48l-36.650666 34.645333a42.666667 42.666667 0 0 1-58.666667-61.952l112.64-106.666667a42.666667 42.666667 0 0 1 71.253333 38.912 298.666667 298.666667 0 0 0 293.418667 354.730667z" fill="#14101C" p-id="2212"></path></svg>
                            刷新</a>
                    </div>
                </div>

            </div>

            <div class="table-responsive">
                <table class="table">
                    <thead>
                        <tr role="head">
                            <th sort="true">用户</th>
                            <th sort="true">直属上级</th>
                            <th sort="true">返佣金额（每万/元）</th>
                            <th sort="true">下属总业绩</th>
                            <th sort="true">团队业绩</th>
                        </tr>
                    </thead>
                    <tbody id="list-display">
                    </tbody>
                </table>
            </div>


            <div class="layui-table-page">
                <div id="layui-table-page1">
                    <div class="layui-box layui-laypage layui-laypage-default" id="layui-laypage-1">
                        <div style="display: inline-block;" class="cpages">
                            <!--page grow -->
                        </div>
                        <span class="layui-laypage-skip">到第<input type="text" min="1" value="1" class="layui-input target-page" />页
                            <button type="button" class="layui-laypage-btn" onclick="javascript:pager_index=$('.target-page').val()-1;getPager();">确定</button></span>
                        <span class="layui-laypage-count">共 <span class="page_count"></span>条</span>
                        <span class="layui-laypage-limits">
                            <select class="s_pages" lay-ignore="" onchange="javascript:pager_size=$(this).val();getPager();">
                                <option value="10">10 条/页</option>
                                <option value="20" selected="">20 条/页</option>
                                <option value="30">30 条/页</option>
                                <option value="50">50 条/页</option>
                                <option value="100">100 条/页</option>
                                <option value="200">200 条/页</option>
                            </select></span>
                    </div>
                </div>
            </div>

        </div>
    </div>
    <script>
        var _action = 'play_user_data';
        var _actionName = '用户游戏数据';
    </script>
    <script>
        function opennew() {
            var modify_model = [];

            modify_model.push({ name: '返佣备注', id: 'name', value: '' });
            modify_model.push({ name: '达成业绩', id: 'team_amount', value: '' });
            modify_model.push({ name: '返佣金额', id: 'brok_amount', value: '' });

            edit({ aname: 'admin', title: '添加' + _actionName, action: _action, id: '', data: modify_model });
        }
    </script>
    <script>
        var return_amount = '<%=Request.QueryString["return_amount"] %>';

        var pager_size = 20;
        var pager_index = 0;
        var pager_key = "";


        $("#pagesize").val(pager_key);
        function setPageSize(obj) {
            pager_index = 0;
            pager_key = $("#search-keyword").val();
            opt = $(obj).val();
            getPager();
        }

        function searchList() {
            opt = $("#opt1").val();
            pager_index = 0;
            pager_key = $("#search-keyword").val();
            getPager();
        }

        function getPager() {

            $.ajax({
                type: "POST",
                url: "../api/admin.aspx?do=page",
                data: { limit: pager_size, p: pager_index, page: _action, keyword: pager_key, date: $("#date").val(), uid: '<%=Request.QueryString["userid"] %>' },
                datatype: "json",
                success: function (json) {
                    $("#list-display").html("");
                    if (json.code == 1) {
                        var data = json.data.list;
                        for (var i = 0; i < data.length; i++) {
                            var tr = "";
                            tr += "<tr class='test1'>";
                            tr += ("<td class=''>" + ('<span style="background: #f3ecec;color: #956b0a;font-size: 12px;border-radius: 3px;padding: 1px 4px;margin-right: 5px;font-weight: bold;border: 1px solid #ccc;">' + (data[i].typename == 'th' ? '托号' : data[i].typename == 'user' ? '普通' : data[i].typename == 'super' ? '高级' : data[i].typename == 'reg' ? '新' : '其他') + '</span>') + (data[i].group_name == '-' ? '' : "<span class='button-style style-js'>" + data[i].group_name + "</span>") + data[i].phone + '<span style="color: #b53d10;margin-left: 2px;">[' + data[i].userid + ']</span>' + "</td>");

                            tr += ("<td class=''>" + ('<span style="background: #f3ecec;color: #956b0a;font-size: 12px;border-radius: 3px;padding: 1px 4px;margin-right: 5px;font-weight: bold;border: 1px solid #ccc;">' + (data[i].parent_typename == 'th' ? '托号' : data[i].parent_typename == 'user' ? '普通' : data[i].parent_typename == 'super' ? '高级' : data[i].parent_typename == 'reg' ? '新' : '其他') + '</span>') + data[i].parent_phone + '<span style="color: #b53d10;margin-left: 2px;">[' + data[i].parentid + ']</span>' + "</td>");
                            tr += ("<td class=''>" + '<b>' + data[i].return_amount + '/每万</b>' + '<span style="color: #969da3;margin-left: 3px;font-size: 12px;">[' + (data[i].user_amount + data[i].team_amount) + ']</span>' + "</td>");
                            tr += ("<td class=''>" + data[i].user_amount + '<span style="color:#9f5d5d;margin-left:5px;">[' + data[i].user_num + ']</span>' + '<a href="play_total.aspx?parentid=' + data[i].userid + '&date=' + data[i].create_date + '&return_amount=' + data[i].return_amount + '" style="    font-size: 12px;    color: #2a2b2c;    font-weight: 100;    margin-left: 5px;"><svg t="" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="12385" width="12" height="12"><path d="M859.6 365.2c-19-44.9-46.2-85.3-80.8-119.9-34.6-34.6-75-61.8-119.9-80.8-46.5-19.7-95.9-29.7-146.8-29.7-50.9 0-100.3 10-146.8 29.7-44.9 19-85.3 46.2-119.9 80.8s-61.8 75-80.8 119.9c-19.7 46.5-29.7 95.9-29.7 146.8 0 50.9 10 100.3 29.7 146.8 19 44.9 46.2 85.3 80.8 119.9 34.6 34.6 75 61.8 119.9 80.8 46.5 19.7 95.9 29.7 146.8 29.7 50.9 0 100.3-10 146.8-29.7 44.9-19 85.3-46.2 119.9-80.8 34.6-34.6 61.8-75 80.8-119.9 19.7-46.5 29.7-95.9 29.7-146.8-0.1-50.9-10.1-100.3-29.7-146.8zM512 849.2c-185.9 0-337.2-151.3-337.2-337.2 0-185.9 151.3-337.2 337.2-337.2 185.9 0 337.2 151.3 337.2 337.2 0 185.9-151.3 337.2-337.2 337.2z" p-id="12386" fill="#707070"></path><path d="M606.2 354.9c-24.1-16.3-57.4-25-96.4-25-27.7 0-54.2 5.9-76.7 16.9-25.3 12.5-43.9 30.9-54 53.5-4.5 10.1 0 21.9 10.1 26.4s21.9 0 26.4-10.1c12.7-28.4 49.6-46.7 94.2-46.7 31 0 56.6 6.2 74 18.1 15.2 10.3 22.9 24.2 22.9 41.3 0 11.2-2.7 48.9-37.7 65-23.6 10.8-43.4 27.9-57.5 49.3-14.2 21.7-21.7 46.9-21.7 72.7 0 11 9 20 20 20s20-9 20-20c0-36.5 21.9-70.1 55.9-85.7 20.4-9.4 36.8-24.8 47.3-44.6 9-16.8 13.7-36.4 13.7-56.7 0-30.2-14.4-56.7-40.5-74.4zM510.5 652.9c-11 0-20 9-20 20V684c0 11 9 20 20 20s20-9 20-20v-11.1c0-11-9-20-20-20z" p-id="12387" fill="#707070"></path></svg>&nbsp;来源</a>' + "</td>");
                            tr += ("<td class=''>" + data[i].team_amount + '<span style="color:#9f5d5d;margin-left:5px;">[' + data[i].team_num + ']</span>' + '<a href="play_total.aspx?teamid=' + data[i].userid + '&date=' + data[i].create_date + '" style="    font-size: 12px;    color: #2a2b2c;    font-weight: 100;    margin-left: 5px;"><svg t="" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="12385" width="12" height="12"><path d="M859.6 365.2c-19-44.9-46.2-85.3-80.8-119.9-34.6-34.6-75-61.8-119.9-80.8-46.5-19.7-95.9-29.7-146.8-29.7-50.9 0-100.3 10-146.8 29.7-44.9 19-85.3 46.2-119.9 80.8s-61.8 75-80.8 119.9c-19.7 46.5-29.7 95.9-29.7 146.8 0 50.9 10 100.3 29.7 146.8 19 44.9 46.2 85.3 80.8 119.9 34.6 34.6 75 61.8 119.9 80.8 46.5 19.7 95.9 29.7 146.8 29.7 50.9 0 100.3-10 146.8-29.7 44.9-19 85.3-46.2 119.9-80.8 34.6-34.6 61.8-75 80.8-119.9 19.7-46.5 29.7-95.9 29.7-146.8-0.1-50.9-10.1-100.3-29.7-146.8zM512 849.2c-185.9 0-337.2-151.3-337.2-337.2 0-185.9 151.3-337.2 337.2-337.2 185.9 0 337.2 151.3 337.2 337.2 0 185.9-151.3 337.2-337.2 337.2z" p-id="12386" fill="#707070"></path><path d="M606.2 354.9c-24.1-16.3-57.4-25-96.4-25-27.7 0-54.2 5.9-76.7 16.9-25.3 12.5-43.9 30.9-54 53.5-4.5 10.1 0 21.9 10.1 26.4s21.9 0 26.4-10.1c12.7-28.4 49.6-46.7 94.2-46.7 31 0 56.6 6.2 74 18.1 15.2 10.3 22.9 24.2 22.9 41.3 0 11.2-2.7 48.9-37.7 65-23.6 10.8-43.4 27.9-57.5 49.3-14.2 21.7-21.7 46.9-21.7 72.7 0 11 9 20 20 20s20-9 20-20c0-36.5 21.9-70.1 55.9-85.7 20.4-9.4 36.8-24.8 47.3-44.6 9-16.8 13.7-36.4 13.7-56.7 0-30.2-14.4-56.7-40.5-74.4zM510.5 652.9c-11 0-20 9-20 20V684c0 11 9 20 20 20s20-9 20-20v-11.1c0-11-9-20-20-20z" p-id="12387" fill="#707070"></path></svg>&nbsp;来源</a>' + "</td>");

                            var modify_model = new Array();

                            //tr += ("<td class='action-cell '>"
                            //    + ' <div class="card-toolbar">'
                            //    + '</div></td>');
                            //tr += "</tr>";

                            $("#list-display").append(tr)
                        }
                        $(".page_size").html(pager_size);
                        $(".page_count").html(json.data.count);
                        $(".cpages").html(json.data.html);


                    } else {

                    }
                },
                error: function () {
                }
            });
        }
        getPager();

        function _pt(i) {
            pager_index = i;
            getPager();
        }
    </script>

    <script>
        layui.use(['form', 'layedit', 'laydate'], function () {
        });
    </script>

    <script>
        function update_select(state) {
            event_create({ 'aname': 'admin', 'name': _action, 'event': 'update', 'id': select_all_id(), 'state': state });
        }

        function _event(evt) {
            event_create({ 'aname': 'admin', 'name': _action, 'event': evt, 'id': select_all_id() });
        }

        //function report_exl() {
        //    var start_time = "";
        //    var end_time = "";
        //    var opt1 = req("id");
        //    var opt2 = $("#opt1").val();
        //    var checktime = $("#checktime").val();
        //    var g = checktime.split(' - ');
        //    console.log(g);
        //    if (g.length > 1) {
        //        start_time = g[0];
        //        end_time = g[1];
        //    }

        //    export_data({ name: "cdks", key: pager_key, start_time: start_time, end_time: end_time, opt1: opt1, opt2: opt2 });
        //}
    </script>

    <script>
        var click_ids = {};
        function checkIsSelect(obj) {
            if (obj.prop('checked')) {
                return true;
            } else {
                return false;
            }
        }
        function goods_click(t) {
            t = $(t);
            var obj = t.find(".good_select");
            obj.prop("checked", !checkIsSelect(obj));

        }

        function select_all(obj) {
            $(".good_select").prop("checked", checkIsSelect($(obj)));
        }

        function select_all_id() {
            var ids = "";
            $("#list-display tr").each(function () {
                var select_obj = $(this).find(".good_select");
                if (checkIsSelect(select_obj)) {
                    if (ids != "") {
                        ids += ",";
                    }
                    ids += select_obj.data("id");
                }
            });
            return ids;
        }
    </script>

    <%-- <script src="https://www.jq22.com/demo/jquerytuozhuai202005141436/js/Sortable.min.js"></script>

    
<script type="text/javascript">
    new Sortable(document.getElementById('list-display'), {
        handle: '.good_select', // handle's class
        animation: 150,
        // direction: 'horizontal',,

        // 结束拖拽
        onEnd: function (/**Event*/evt) {
            var itemEl = evt.item;  // dragged HTMLElement
            evt.to;    // target list
            evt.from;  // previous list
            evt.oldIndex;  // element's old index within old parent
            evt.newIndex;  // element's new index within new parent
            evt.clone // the clone element
            evt.pullMode;  // when item is in another sortable: `"clone"` if cloning, `true` if moving

            console.log('evt', evt.to, evt.from, evt.oldIndex, evt.newIndex);
        }
    });

    //$(".list_con .iconshanchu").bind('touchstart', function () {
    //    $(this).parent().parent().remove();
    //    event.preventDefault();
    //    event.stopPropagation();
    //});
</script>
    --%>
</asp:Content>


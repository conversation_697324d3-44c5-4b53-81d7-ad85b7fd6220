<%@ Page Title="User" Language="C#" MasterPageFile="~/serv/m.master" AutoEventWireup="true" CodeFile="accounts_baseinfo.aspx.cs" Inherits="serv_index" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="Server">
    <link href="css/bootstrap.min.css" rel="stylesheet">
    <link href="../css1/a3common.css" rel="stylesheet">
    <link rel="stylesheet" href="../css1/login.css">
    <link rel="stylesheet" href="../css1/font_43459_lbtux0zjkr6yldi.css">

    <style>
        .form-container {
            display: flex;
            flex-direction: column;
            gap: 10px;
            margin-right: 6px;
            flex-shrink: 0;
            background: #fff;
            padding: 18px;
            border-radius: 3px;
        }

        .form-item {
            display: flex;
            align-items: center; /* 垂直居中对齐标签和内容 */
            /*border-bottom:1px solid #f2f2f2;*/
            padding-bottom: 5px;
            margin-bottom: 5px;
        }

        label {
            min-width: 80px; /* 设置标签的最小宽度 */
            color: #8a8b8c;
            font-weight: 500;
            margin-bottom: 0px;
        }

        .form-item div {
            flex-grow: 1; /* 使内容填充剩余空间 */
        }
    </style>

    <style>
        .gradient-status-item {
            position: relative;
            display: inline-block;
            background: #1ea71e;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            margin-right: 3px;
            background: #1ea71e; /* 绿色背景 */
            margin-left: 8px;
        }

        .gradient-offline {
            background: gray;
        }

        .gradient-online {
            background: #1ea71e; /* 绿色背景 */
            animation: brightAndDim 0.8s linear infinite alternate;
        }

        @keyframes brightAndDim {
            0% {
                opacity: 0.5; /* 开始时变暗 */
            }

            100% {
                opacity: 1; /* 结束时变亮 */
            }
        }
    </style>
</asp:Content>

<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="Server">

    <div style="padding: 18px;">

        <div style="padding: 12px; border-radius: 8px; display: flex; overflow: hidden; margin-bottom: 6px;">

            <div style="border-top-right-radius: 38px; border-bottom-right-radius: 38px; display: flex; justify-content: center; align-items: center; padding: 0 10px; padding-left: 0; border-top-left-radius: 8px; border-bottom-left-radius: 8px; z-index: 0;">

                <img src="<%=uConfig.gd(userdt,"avatar") %>" style="width: 60px; height: 60px; border-radius: 50%; cursor: pointer;">
            </div>



            <div style="display: flex; height: auto; flex-direction: column; justify-content: center; width: 100%;">
                <b style="font-size: 18px; cursor: pointer; white-space: nowrap; overflow: hidden; text-overflow: ellipsis; display: flex; align-items: center;"><span style="background: #f3ecec; color: #956b0a; font-size: 12px; border-radius: 3px; padding: 1px 4px; margin-right: 5px; font-weight: bold; border: 1px solid #ccc;"><%=pmlist["typename"]+"" == "th" ? "托号" :pmlist["typename"]+"" == "user" ? "普通" : pmlist["typename"]+"" == "super" ? "高级" : pmlist["typename"]+"" == "reg" ? "新" : "其他" %></span><%=uConfig.gd(userdt, "username") == "" ? "<span style='color:#959292;'>未设置</span>" :"<span>"+ uConfig.gd(userdt, "username")+"</span>" %></b>
                <div style="color: #2a2b2c; font-size: 12px; margin-top: 2px; display: flex;">手机号  <%=uConfig.gd(userdt,"phone") %>&nbsp;<svg t="" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="10317" width="16" height="16" onclick="copytext('<%=uConfig.gd(userdt,"phone") %>')"><path d="M701 244H183c-53.02 0-96 42.98-96 96v471c0 53.02 42.98 96 96 96h518c53.02 0 96-42.98 96-96V340c0-53.02-42.98-96-96-96z m-518 64h518c17.673 0 32 14.327 32 32v471c0 17.673-14.327 32-32 32H183c-17.673 0-32-14.327-32-32V340c0-17.673 14.327-32 32-32z" fill="#979797" p-id="10318"></path><path d="M701 244H183c-53.02 0-96 42.98-96 96v471c0 53.02 42.98 96 96 96h518c53.02 0 96-42.98 96-96V340c0-53.02-42.98-96-96-96z m-518 64h518c17.673 0 32 14.327 32 32v471c0 17.673-14.327 32-32 32H183c-17.673 0-32-14.327-32-32V340c0-17.673 14.327-32 32-32z" fill="#2600FF" p-id="10319"></path><path d="M239 439h69v69h-69zM239 541h69v69h-69zM239 644h69v69h-69zM350 644h69v69h-69zM460 644h69v69h-69zM571 644h69v69h-69z" fill="#CDCBFF" p-id="10320"></path><path d="M840.907 117c49.85 0 91.257 37.988 92.08 86.334l0.013 1.468v487.396c0 48.553-40.909 87.026-90.585 87.79l-1.508 0.012h-72.83v-64h72.83c15.863 0 27.851-10.985 28.09-23.425l0.003-0.377V204.802c0-12.468-11.83-23.578-27.614-23.799l-0.479-0.003H315.093c-15.863 0-27.851 10.985-28.09 23.425l-0.003 0.377v68.8h-64v-68.8c0-48.553 40.909-87.026 90.585-87.79l1.508-0.012h525.814z" fill="#2600FF" p-id="10321"></path></svg></div>
                <div style="color: #aaa; font-size: 12px; margin-top: 2px; display: flex; align-item: center;">UID  <%=uConfig.gd(userdt,"parent_code") %><span style="color: #b53d10; margin-left: 2px;">[<%=uConfig.gd(userdt,"id") %>]</span>&nbsp;<svg t="" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="10317" width="16" height="16" onclick="copytext('<%=uConfig.gd(userdt,"parent_code") %>')"><path d="M701 244H183c-53.02 0-96 42.98-96 96v471c0 53.02 42.98 96 96 96h518c53.02 0 96-42.98 96-96V340c0-53.02-42.98-96-96-96z m-518 64h518c17.673 0 32 14.327 32 32v471c0 17.673-14.327 32-32 32H183c-17.673 0-32-14.327-32-32V340c0-17.673 14.327-32 32-32z" fill="#979797" p-id="10318"></path><path d="M701 244H183c-53.02 0-96 42.98-96 96v471c0 53.02 42.98 96 96 96h518c53.02 0 96-42.98 96-96V340c0-53.02-42.98-96-96-96z m-518 64h518c17.673 0 32 14.327 32 32v471c0 17.673-14.327 32-32 32H183c-17.673 0-32-14.327-32-32V340c0-17.673 14.327-32 32-32z" fill="#2600FF" p-id="10319"></path><path d="M239 439h69v69h-69zM239 541h69v69h-69zM239 644h69v69h-69zM350 644h69v69h-69zM460 644h69v69h-69zM571 644h69v69h-69z" fill="#CDCBFF" p-id="10320"></path><path d="M840.907 117c49.85 0 91.257 37.988 92.08 86.334l0.013 1.468v487.396c0 48.553-40.909 87.026-90.585 87.79l-1.508 0.012h-72.83v-64h72.83c15.863 0 27.851-10.985 28.09-23.425l0.003-0.377V204.802c0-12.468-11.83-23.578-27.614-23.799l-0.479-0.003H315.093c-15.863 0-27.851 10.985-28.09 23.425l-0.003 0.377v68.8h-64v-68.8c0-48.553 40.909-87.026 90.585-87.79l1.508-0.012h525.814z" fill="#2600FF" p-id="10321"></path></svg></div>
            </div>

        </div>




        <div style="display: flex; font-size: 12px; padding: 5px 0; border-radius: 8px; margin-bottom: 18px; overflow-x: auto;">


            <div style="text-align: center; flex-shrink: 0; padding: 0 18px;">
                <div style="color: gray;">
                    今日卖币
                </div>

                <div style="margin: 10px 0; font-size: 16px; color: #455366; font-weight: bold;">
                    <span id="today_sell_amount">0.00</span><div style="font-size: 12px; color: #2EC892; margin-left: 3px; font-weight: 500; display: flex; justify-content: center; align-items: center;">
                        <span style="color: #8C90A9;">累计</span><span style="margin-left: 4px;" id="sell_amount">0.00</span><img src="/static/images/coin.png" alt="" height="12" width="12" style="margin-left: 5px;">
                    </div>
                </div>

            </div>
            <%--    <div style="text-align: center;flex-shrink: 0;padding:0 18px;">
                <div style="color: gray;">
                    今日提现
                </div>                
                <div style="margin: 10px 0; font-size: 16px; color: #455366; font-weight: bold;">
                    <span id="tixian_daily_amount">0.00</span><div style="font-size: 12px; color: #2EC892; margin-left: 3px; font-weight: 500; display: flex; justify-content: center; align-items: center;">
                        <span style="color: #8C90A9;">累计</span><span style="margin-left: 4px;" id="tixian_total_amount">0.00</span><img src="/static/images/coin.png" alt="" height="12" width="12" style="margin-left: 5px;">
                    </div>
                </div>
            </div>--%>
            <div style="text-align: center; flex-shrink: 0; padding: 0 18px;">
                <div style="color: gray;">
                    今日买币
                </div>
                <div style="margin: 10px 0; font-size: 16px; color: #455366; font-weight: bold;">
                    <span id="buycoin_daily_amount">0.00</span><div style="font-size: 12px; color: #2EC892; margin-left: 3px; font-weight: 500; display: flex; justify-content: center; align-items: center;">
                        <span style="color: #8C90A9;">累计</span><span style="margin-left: 4px;" id="buycoin_total_amount">0.00</span><img src="/static/images/coin.png" alt="" height="12" width="12" style="margin-left: 5px;">
                    </div>
                </div>
            </div>
            <div style="text-align: center; flex-shrink: 0; padding: 0 18px;">
                <div style="color: gray;">
                    可用余额
                </div>
                <div style="margin: 10px 0; font-size: 16px; color: #3E48D4; font-weight: bold;">
                    <%=uConfig.gnumber(userdt,"amount") %><img src="/static/images/coin.png" alt="" height="12" width="12" style="margin-left: 5px;">
                </div>
            </div>
            <div style="text-align: center; flex-shrink: 0; padding: 0 18px;">
                <div style="color: gray;">
                    体验金
                </div>
                <div style="margin-top: 10px; font-size: 16px; color: #bd6c6c; font-weight: bold;">
                    <%=uConfig.gnumber(userdt,"exp_amount") %><img src="/static/images/coin.png" alt="" height="12" width="12" style="margin-left: 5px;">
                </div>

                <div style="color: gray;">
                    <a style="display: inline-block; background: #262b5f0d; padding: 2px 5px; border-radius: 5px; margin-top: 3px; color: #2a2b2c;">剩余<%=pmlist["exp_expireHour"] %></a>
                </div>
            </div>
            <div style="text-align: center; flex-shrink: 0; padding: 0 18px; cursor: pointer;">
                <div style="color: gray;">
                    游戏余额
                </div>
                <div style="margin-top: 10px; font-size: 16px; color: #000; font-weight: bold;">
                    <%=pmlist["game_balance"] %><img src="/static/images/coin.png" alt="" height="12" width="12" style="margin-left: 5px;">
                </div>
            </div>
            <div style="text-align: center; flex-shrink: 0; padding: 0 18px;">
                <div style="color: gray;">
                    冻结余额
                </div>
                <div style="margin: 10px 0; font-size: 16px; color: #686868; font-weight: bold;">
                    <%=(Convert.ToDouble(uConfig.gnumber(userdt,"freeze_amount"))+Convert.ToDouble(uConfig.gnumber(userdt,"freeze_lendAmount"))).ToString("0.00") %><img src="/static/images/coin.png" alt="" height="12" width="12" style="margin-left: 5px;">
                </div>
            </div>
        </div>


        <div style="background: #f2f2f2; padding: 8px 2px; margin-bottom: 8px; border-radius: 2px;overflow-x: auto;">

            <%for (int i = 0; i < 2; i++)
                {
            %>

            <div style="display: flex;margin-bottom: 10px;flex-wrap: nowrap;min-width: 800px;">


                <div style="background: <%=i == 0 ? "#ffffff54" : "#fff" %>; padding: 17px; border-radius: 8px; margin: 0 3px; width: 25%; max-width: 250px;    flex-shrink: 0;">
                    <div style="display: flex; align-items: center;">
                        <svg t="" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="7153" width="20" height="20">
                            <path d="M513.0752 510.8224m-450.816 0a450.816 450.816 0 1 0 901.632 0 450.816 450.816 0 1 0-901.632 0Z" fill="#FFF7DC" p-id="7154"></path><path d="M863.0784 503.7056c0-52.7872-56.3712-67.0208-86.016-77.1072-29.6448-10.0864-176.0768-64.2048-176.0768-64.2048s-100.1472-163.328-190.0032-101.888c-21.8112 13.7728-18.2272 63.6416 12.032 84.5312-41.8304-7.5776-110.7968 7.9872-107.264 47.616 3.584 39.6288 58.3168 60.0576 155.2896 43.6224 16.896-3.584 40.4992 8.9088 12.032 32.9216-15.1552 10.7008-80.9984 28.928-225.1776 3.1232-102.6048-18.3808-162.816-32.2048-189.0304-38.7072a451.6864 451.6864 0 0 0-6.6048 77.2608c0 149.504 72.8064 282.0096 184.8832 364.032 22.016-6.5536 45.0048-15.104 68.608-26.112 60.5184-28.4672 42.7008-57.856 28.4672-68.096s-42.7008-50.7392 36.9152-98.3552 178.7392-90.1632 296.8064-105.0112c37.9904-5.3248 83.6608-5.9392 105.6256-7.1168 21.9648-1.2288 79.5136-13.6704 79.5136-66.5088z m-260.5056-41.8304c-15.872 0-28.7744-12.9024-28.7744-28.7744s12.9024-28.7744 28.7744-28.7744 28.7744 12.9024 28.7744 28.7744-12.8512 28.7744-28.7744 28.7744z" fill="#FF8E12" p-id="7155"></path><path d="M600.9856 362.3424s-100.1472-163.328-190.0032-101.888c-21.8112 13.7728-18.2272 63.6416 12.032 84.5312-41.8304-7.5776-110.7968 7.9872-107.264 47.616 3.584 39.6288 58.3168 60.0576 155.2896 43.6224 16.896-3.584 40.4992 8.9088 12.032 32.9216-15.1552 10.7008-80.9984 28.928-225.1776 3.1232-102.6048-18.3808-162.816-32.2048-189.0304-38.7072a451.6864 451.6864 0 0 0-6.6048 77.2608c0 104.3456 35.5328 200.3968 95.0784 276.8384a426.51136 426.51136 0 0 0 163.84 32.5632c11.1616 0 22.1696-0.5632 33.1264-1.3824 9.6768-17.1008-0.9216-31.5904-10.0864-38.1952-14.2336-10.24-42.7008-50.7392 36.9152-98.3552s178.7392-90.1632 296.8064-105.0112c9.8304-1.3824 20.1728-2.4576 30.464-3.2768a425.0112 425.0112 0 0 0 39.68-157.696c-52.48-18.944-147.0976-53.9648-147.0976-53.9648z m1.5872 99.5328c-15.872 0-28.7744-12.9024-28.7744-28.7744s12.9024-28.7744 28.7744-28.7744 28.7744 12.9024 28.7744 28.7744-12.8512 28.7744-28.7744 28.7744z" fill="#FCA315" p-id="7156"></path><path d="M410.9312 260.4544C389.12 274.2272 392.704 324.096 422.9632 344.9856c-41.8304-7.5776-110.7968 7.9872-107.264 47.616 3.584 39.6288 58.3168 60.0576 155.2896 43.6224 16.896-3.584 40.4992 8.9088 12.032 32.9216-15.1552 10.7008-80.9984 28.928-225.1776 3.1232-102.6048-18.3808-162.816-32.2048-189.0304-38.7072a451.6864 451.6864 0 0 0-6.6048 77.2608c0 49.0496 7.8848 96.256 22.3744 140.4416a428.34944 428.34944 0 0 0 119.552 17.0496c160.8704 0 300.9536-88.8832 373.9648-220.16-2.7136-4.4032-4.3008-9.5232-4.3008-15.0528 0-14.7456 11.1104-26.88 25.3952-28.5696 5.12-12.3392 9.728-24.9344 13.6704-37.7856-7.424-2.7648-11.9296-4.4032-11.9296-4.4032s-100.096-163.2768-190.0032-101.888z" fill="#FCB138" p-id="7157"></path><path d="M257.8944 472.2688c-102.6048-18.3808-162.816-32.2048-189.0304-38.7072a451.6864 451.6864 0 0 0-6.6048 77.2608c0 1.5872 0.0512 3.1232 0.0512 4.6592 11.3152 0.9216 22.7328 1.4848 34.2528 1.4848 65.4848 0 127.5392-14.7968 183.04-41.1136-7.0656-1.024-14.2336-2.2528-21.7088-3.584zM422.9632 344.9856c-41.8304-7.5776-110.7968 7.9872-107.264 47.616 1.5872 17.92 13.7216 31.8976 35.4816 40.3456 61.184-45.4144 109.824-106.752 139.7248-177.8688-25.6512-11.264-53.248-12.8512-79.9744 5.376-21.76 13.824-18.2272 63.6416 12.032 84.5312z" fill="#FFC65E" p-id="7158"></path></svg>
                        <span style="color: #fba722; font-weight: bold; margin-left: 8px;"><%=i == 0 ? "历史" : "今日" %>游戏总额</span>
                    </div>

                    <div style="font-size: 22px; margin-top: 6px; font-weight: bold; color: #7e868f;">
                        <span id="total<%=i %>_amount">0.00</span><span style="font-size: 12px; font-weight: 100; margin-left: 10px;">[<span id="total<%=i %>_number">0</span>单]</span>
                    </div>
                </div>


                <div style="background: <%=i == 0 ? "#ffffff54" : "#fff" %>; padding: 17px; border-radius: 8px; width: 25%; margin: 0 3px; max-width: 250px;    flex-shrink: 0;">
                    <div style="display: flex; align-items: center;">
                        <svg t="" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="" width="20" height="20">
                            <path d="M821.5 981h-452C258.1 981 167 889.9 167 778.5v-284C167 383.1 258.1 292 369.5 292h452c111.4 0 202.5 91.1 202.5 202.5v284c0 111.4-91.1 202.5-202.5 202.5z" fill="#9BBCFA" p-id="1768"></path><path d="M784 928.4H241c-105.1 0-191-85.9-191-191V286.6c0-105 85.9-191 191-191h543c105 0 191 86 191 191V737.4c0 105.1-85.9 191-191 191z" fill="#0057FC" p-id="1769"></path><path d="M241 928.4c-105.1 0-191-85.9-191-191V286.6c0-105 85.9-191 191-191h543c105 0 191 86 191 191C934.1 711 655.2 889.5 241 928.4z" fill="#0066FD" p-id="1770"></path><path d="M50 708.4V286.6c0-105 85.9-191 191-191h472C782 456 477 733 50 708.4z" fill="#0080FE" p-id="1771"></path><path d="M50 466.5V286.6c0-105 85.9-191 191-191h204.5C454.4 421.7 351 438 50 466.5z" fill="#008DFF" p-id="1772"></path><path d="M723.8 547.2h-90.6V440.1l-115-73.7v-46.8c32.8 5.4 92.2 20.5 118.7 1.5 0-20 0.8-53.3 0-65.7-39.1 15-141.4 0-141.4 0v111.1l-115 73.6v107.1h-90.6c-11.6 0-20.9 8.6-20.9 19.1v218h159.3v-22.1c0-37.8 30.2-71.8 71.4-75.1 46.5-3.7 85.5 29.7 85.5 71.3v25.9h159.3v-218c0.2-10.4-9.1-19.1-20.7-19.1z m-216.9-4.8c-22.2 0-42.2-12.2-50.7-31-8.5-18.7-3.8-40.3 11.9-54.6 15.7-14.4 39.4-18.6 59.9-10.9 20.5 7.8 33.9 26 33.9 46.3 0 27.7-24.7 50.2-55 50.2z" fill="#FFFFFF" p-id="1773"></path></svg>
                        <span style="color: #005efd; font-weight: bold; margin-left: 8px;"><%=i == 0 ? "历史" : "今日" %>有效总额</span>
                    </div>

                    <div style="font-size: 22px; margin-top: 6px; font-weight: bold; color: #7e868f;">
                        <span id="total<%=i %>_really_amount">0.00</span><span style="font-size: 12px; font-weight: 100; margin-left: 10px;">[<span id="total<%=i %>_really_number">0</span>单]</span>
                    </div>
                </div>



                <div style="background: <%=i == 0 ? "#ffffff54" : "#fff" %>; padding: 17px; border-radius: 8px; width: 25%; margin: 0 3px; max-width: 250px;    flex-shrink: 0;">
                    <div style="display: flex; align-items: center;">
                        <svg t="" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="" width="20" height="20">
                            <path d="M821.5 981h-452C258.1 981 167 889.9 167 778.5v-284C167 383.1 258.1 292 369.5 292h452c111.4 0 202.5 91.1 202.5 202.5v284c0 111.4-91.1 202.5-202.5 202.5z" fill="#FCEDB0" p-id="1088"></path><path d="M784 928.4H241c-105.1 0-191-85.9-191-191V286.6c0-105 85.9-191 191-191h543c105 0 191 86 191 191V737.4c0 105.1-85.9 191-191 191z" fill="#FF9001" p-id="1089"></path><path d="M241 928.4c-105.1 0-191-85.9-191-191V286.6c0-105 85.9-191 191-191h543c105 0 191 86 191 191C934.1 711 655.2 889.5 241 928.4z" fill="#FFA61F" p-id="1090"></path><path d="M50 708.4V286.6c0-105 85.9-191 191-191h472C782 456 477 733 50 708.4z" fill="#FFD055" p-id="1091"></path><path d="M50 466.5V286.6c0-105 85.9-191 191-191h204.5C454.4 421.7 351 438 50 466.5z" fill="#FFE774" p-id="1092"></path><path d="M387 791.1s-30-33.7-58.4-61.6c-30.3-8.3-60.8-16.1-91.6-23.3l62.4-108c33 42.8 90.8 79.8 145.4 93.2L387 791.1z m284.7-66.9C643.3 752 608 791.1 608 791.1l-57.3-99.7c54.5-13.3 107-50.1 140-93.2l67.5 108c-0.3 0.2-46.1 6.7-86.5 18zM497.4 255.4c-108.8 0-202.2 98.9-202.2 207.9s93.5 197.1 202.2 197.1 202.2-93.5 202.2-202.5S606 255.4 497.4 255.4z m116.7 193.7l-35.4 36.7c-4.8 4.8-7.2 11.8-5.6 18.8v0.8l8 49.8c0.8 4.8 0 9.9-2.4 14-6.2 11-19.9 15.3-30.8 9.1l-40.7-21.7c-6.4-3.5-14.4-3.5-20.9 0.8l-39.6 21.2c-4.6 2.4-9.3 3-14.4 2.4-12.1-1.9-20.6-13.7-18.8-25.7l8.3-51.7c0.8-6.2-1.1-12.1-5.3-16.6l-35.6-37.7c-3-3.5-5.3-8-6.2-12.6-1.9-12.1 7-23.5 19-25.4l45.5-7.2c7.2-0.2 13.7-4.8 16.6-11.5l20.9-44.5c3.8-7.5 11.8-12.9 20.6-12.9 8.9 0 16.6 4.8 20.6 12.9l20.6 44.2c3 7 9.3 11.5 17.1 12.1l45.5 7c4.6 0.8 9.1 3 12.6 6.1 8.7 8.6 9 23 0.4 31.6z" fill="#FFFFFF" p-id="1093"></path></svg>
                        <span style="color: #fba722; font-weight: bold; margin-left: 8px;"><%=i == 0 ? "历史" : "今日" %>中奖总额</span>
                    </div>

                    <div style="font-size: 22px; margin-top: 6px; font-weight: bold; color: #7e868f;">
                        <span id="total<%=i %>_okamount">0.00</span><span style="font-size: 12px; font-weight: 100; margin-left: 10px;">[<span id="total<%=i %>_oknumber">0</span>单]</span>
                    </div>
                </div>



                <div style="background: <%=i == 0 ? "#ffffff54" : "#fff" %>; padding: 17px; border-radius: 8px; width: 25%; margin: 0 3px; max-width: 250px;    flex-shrink: 0;">
                    <div style="display: flex; align-items: center;">
                        <svg t="" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="" width="20" height="20">
                            <path d="M821.5 981h-452C258.1 981 167 889.9 167 778.5v-284C167 383.1 258.1 292 369.5 292h452c111.4 0 202.5 91.1 202.5 202.5v284c0 111.4-91.1 202.5-202.5 202.5z" fill="#F9B2AB" p-id="1311"></path><path d="M784 928.4H241c-105.1 0-191-85.9-191-191V286.6c0-105 85.9-191 191-191h543c105 0 191 86 191 191V737.4c0 105.1-85.9 191-191 191z" fill="#FA3F2A" p-id="1312"></path><path d="M241 928.4c-105.1 0-191-85.9-191-191V286.6c0-105 85.9-191 191-191h543c105 0 191 86 191 191C934.1 711 655.2 889.5 241 928.4z" fill="#FD5E4D" p-id="1313"></path><path d="M50 708.4V286.6c0-105 85.9-191 191-191h472C782 456 477 733 50 708.4z" fill="#FD776A" p-id="1314"></path><path d="M50 466.5V286.6c0-105 85.9-191 191-191h204.5C454.4 421.7 351 438 50 466.5z" fill="#FE837A" p-id="1315"></path><path d="M768.46484375 254.12890625H258.25976562c-27.24609375 0-49.48242188 22.32421875-49.48242187 49.48242188v280.10742187c0 9.31640625 11.42578125 13.88671875 17.84179688 7.20703125 5.625-5.88867188 12.74414063-11.07421875 21.62109375-15.02929688 4.83398438-2.19726563 7.29492188-7.734375 5.53710937-12.83203124-27.0703125-80.06835938 9.58007813-133.2421875 28.828125-155.30273438 28.91601563-33.13476563 100.81054688-57.83203125 165.9375-34.453125 64.77539063 22.41210938 99.05273438 95.00976563 87.36328125 146.25-3.07617188 13.44726563 13.62304688 16.69921875 18.89648438 4.83398438 0.17578125-0.3515625 0.3515625-0.703125 0.52734374-0.96679688 2.90039063-5.00976563 25.75195313-41.57226563 75.41015626-24.78515625 53.17382813 18.01757813 38.40820313 69.08203125 37.35351562 73.56445313l-39.81445312 180.96679687c-1.40625 6.50390625 3.515625 12.56835938 10.10742187 12.56835938h130.16601563c27.24609375 0 49.48242188-22.32421875 49.48242187-49.48242188V303.61132812c0-27.15820313-22.32421875-49.48242188-49.5703125-49.48242187z m-23.99414063 179.47265625H640.3203125c-9.58007813 0-17.31445313-9.22851563-17.31445313-20.7421875 0-11.42578125 7.734375-20.7421875 17.31445313-20.7421875h104.15039063c9.58007813 0 17.31445313 9.22851563 17.31445312 20.7421875 0 11.51367188-7.734375 20.7421875-17.31445313 20.7421875z m1e-8-80.06835938H501.453125c-9.58007813 0-17.31445313-9.22851563-17.31445313-20.74218749 0-11.42578125 7.734375-20.7421875 17.31445313-20.7421875h242.9296875c9.58007813 0 17.31445313 9.22851563 17.31445313 20.7421875 0.08789063 11.51367188-7.64648438 20.7421875-17.22656251 20.7421875z" fill="#FFFFFF" p-id="1316"></path><path d="M611.40429688 549.17773438c-7.47070313-2.02148438-15.1171875 2.4609375-17.13867188 9.84375L557.703125 695.69140625c-20.12695313-48.1640625-60.38085938-85.78125-110.390625-102.39257813 24.08203125-17.40234375 39.90234375-45.3515625 40.60546875-77.08007812C489.1484375 461.0234375 443.88476562 415.40820312 388.77734375 416.19921875 335.33984375 416.90234375 292.2734375 460.40820312 292.2734375 514.02148438c0 32.60742188 15.99609375 61.43554688 40.60546875 79.27734374-67.5 22.5-117.68554688 83.40820313-123.83789063 156.97265626-0.703125 8.26171875 5.71289063 15.38085938 13.97460938 15.38085937 7.20703125 0 334.95117188-0.17578125 335.21484375-0.17578125 1.0546875-0.08789063 9.05273438 0.43945313 9.05273438 0.43945312l54.22851562-199.59960937c1.84570313-7.47070313-2.63671875-15.20507813-10.10742188-17.13867188z" fill="#FFFFFF" p-id="1317"></path></svg>
                        <span style="color: #fd5e4d; font-weight: bold; margin-left: 8px;"><%=i == 0 ? "历史" : "今日" %>本次消耗</span>
                    </div>

                    <div style="font-size: 22px; margin-top: 6px; font-weight: bold; color: #7e868f;">
                        <span id="total<%=i %>_cost_amount">0.00</span>
                    </div>
                </div>


            </div>




            <%
                } %>
        </div>


        <style>
            .user_label {
                font-size: 13px;
                color: gray;
                margin-right: 8px;
                cursor: pointer;
            }

                .user_label.active {
                    font-weight: bold;
                    font-size: 16px;
                    color: #000;
                }

            .normal_state {
                font-size: 12px;
                color: #4abd3d;
                margin-right: 4px;
                background: #f3f9f5;
                border: 1px solid #4abd3d;
                padding: 0 3px;
                border-radius: 3px;
            }

            .abnormal_state {
                font-size: 12px;
                color: #810d0d;
                margin-right: 4px;
                background: #f9f3f3;
                border: 1px solid #810d0d;
                padding: 0 3px;
                border-radius: 3px;
            }
        </style>



        <%-- <div style="display: flex; margin-bottom: 10px; padding-bottom: 10px; align-items: end;">
            <div class="user_label active">基本信息</div>
            <div class="user_label">资金来源</div>
            <div class="user_label">其他信息</div>
        </div>
        <script>
            $('.user_label').on('click', function () {
                $(this).addClass("active").siblings().removeClass("active");
                $('.form-container').hide();
                switch ($(this).text()) {
                    case "基本信息":
                        $('#baseinfo').show();
                        break;
                    case "资金来源":
                        $('#capital .lists').html('');
                        $('#capital').show();
                        user_baseinfo('capital');
                        break;
                    default:
                        break;
                }
            })
        </script>--%>

        <div style="display: flex; overflow-x: auto; padding: 6px; background: #eee;">

            <div class="form-container" id="baseinfo">
                <div class="user_label active">基本信息</div>
                <div class="form-item">
                    <label for="name">姓名：</label>
                    <div><%=pmlist["payment_name"]  %></div>
                </div>
                <div class="form-item">
                    <label for="name">银行：</label>
                    <div><%=pmlist["payment_bankname"]  %></div>
                </div>
                <div class="form-item">
                    <label for="name">卡号：</label>
                    <div><%=pmlist["payment_bankid"]  %></div>
                </div>
                <div class="form-item">
                    <label for="name">支付宝账号：</label>
                    <div><%=pmlist["alipay_account"]  %></div>
                </div>
                <div class="form-item">
                    <label for="age">房间：</label>
                    <div><%=uConfig.gd(userdt,"roomNumber") %></div>
                </div>
                <div class="form-item">
                    <label for="age">状态：</label>
                    <div>
                        <%=uConfig.gd(userdt,"state")=="1"?"<span class='normal_state'>正常</span>":"<span class='abnormal_state'>异常</span>" %>
                        <%=uConfig.gd(userdt,"islock")!="1"?"<span class='normal_state'>无锁</span>":"<span class='abnormal_state'>锁屏</span>" %>
                        <%=uConfig.gd(userdt,"chat_nospeak")!="1"?"<span class='normal_state'>聊天</span>":"<span class='abnormal_state'>禁言</span>" %>
                    </div>
                </div>
                <div class="form-item">
                    <label for="age">推荐人：</label>
                    <div><span style="background: #f3ecec; color: #956b0a; font-size: 12px; border-radius: 3px; padding: 1px 4px; margin-right: 5px; font-weight: bold; border: 1px solid #ccc;"><%=pmlist["pa_typename"]+"" == "th" ? "托号" :pmlist["pa_typename"]+"" == "user" ? "普通" : pmlist["pa_typename"]+"" == "super" ? "高级" : pmlist["pa_typename"]+"" == "reg" ? "新" : "其他" %></span><%=uConfig.gd(userdt,"pa_phone") %></div>
                </div>
                <div class="form-item">
                    <label for="age">推荐ID：</label>
                    <div><%=uConfig.gd(userdt,"pa_parent_code") %></div>
                </div>
                <div class="form-item">
                    <label for="name">有效下级：</label>
                    <div><%=uConfig.gd(userdt,"valid_usernum") %></div>
                </div>
                <div class="form-item">
                    <label for="hobby">分组：</label>
                    <div><%=pmlist["groupname"] %></div>
                </div>
                <div class="form-item">
                    <label for="hobby">活跃时间：</label>
                    <div style="font-size: 12px;"><%=pmlist["online"] == "在线" ? "<span class='gradient-status-item gradient-online'></span><span style='color: #1ea71e;'>在线</span>" : "<span class='gradient-status-item gradient-offline'></span><span style='color: gray;'>离线</span>&nbsp;" %><%=pmlist["last_time_text"] %></div>
                </div>
                <div class="form-item">
                    <label for="age">注册时间：</label>
                    <div><%=uConfig.gd(userdt,"create_time") %></div>
                </div>
                <div class="form-item">
                    <label for="age">最后登录：</label>
                    <div><%=uConfig.gd(userdt,"login_time") %></div>
                </div>
            </div>


            <div class="form-container" id="transaction_info">
                <div class="user_label active">交易信息</div>
                <div class="form-item">
                    <label for="name">奖励</label>
                    <div id="reward_amount"><%=uConfig.gnumber(userdt,"reward_amount") %></div>
                </div>
                <div class="form-item">
                    <label for="name">任务奖励</label>
                    <div id="task_amount"><%=uConfig.gnumber(userdt,"task_amount") %></div>
                </div>
                <div class="form-item">
                    <label for="name">转盘奖励</label>
                    <div id="wheel_amount"><%=uConfig.gnumber(userdt,"wheel_amount") %></div>
                </div>
                <div class="form-item">
                    <label for="age">活动奖励</label>
                    <div id="activity_amount"><%=toNumbers(pmlist["activity_amount"] + "") %></div>
                </div>
                <div class="form-item">
                    <label for="age">红包金额</label>
                    <div id="receive_redbag_amount"><%=toNumbers(pmlist["receive_redbag_amount"] + "") %></div>
                </div>
                <div class="form-item">
                    <label for="hobby">佣金(全部)</label>
                    <div id="brok_amount"><%=uConfig.gnumber(userdt,"brok_amount") %></div>
                </div>
                <div class="form-item">
                    <label for="age">未领佣金</label>
                    <div id="partners_amount"><%=uConfig.gnumber(userdt,"partners_amount") %></div>
                </div>
                <div class="form-item">
                    <label for="age">游戏佣金</label>
                    <div id="rec_play_brok"><%=uConfig.gnumber(userdt,"total_play_brok") %></div>
                </div>
                <%--<div class="form-item">
                    <label for="age">利息</label>
                    <div id="lend_reward"><%=uConfig.gnumber(userdt,"lend_reward") %></div>
                </div>--%>
                <div class="form-item">
                    <label for="age">买币赠送</label>
                    <div id="buycoin_gift"><%=toNumbers(pmlist["buycoin_gift"] + "") %></div>
                </div>
                <div class="form-item">
                    <label for="age">挂单盈利</label>
                    <div id="total_award_amount"><%=toNumbers(pmlist["total_award_amount"] + "") %></div>
                </div>
                <div class="form-item">
                    <label for="age">总和</label>
                    <div id="total_reward" style="font-weight: bold;">0</div>
                </div>
            </div>


            <style>
                .capital_label {
                    width: 80px;
                    text-align: center;
                    background: #F4F4F4;
                    padding: 8px;
                    border-radius: 38px;
                    color: #2F2F2F;
                    margin-right: 8px;
                    cursor: pointer;
                }

                    .capital_label.active {
                        font-weight: bold;
                        background: #246bff;
                        color: #fff;
                    }
            </style>
            <div class="form-container" id="capital">

                <div class="user_label active">资金来源</div>
                <div style="display: flex;">
                    <div class="capital_label active">收入</div>
                    <div class="capital_label">支出</div>
                </div>
                <script>
                    $('.capital_label').on('click', function () {
                        $(this).addClass("active").siblings().removeClass("active");
                        if ($(this).text() == "收入") {
                            user_baseinfo('capital');
                        } else {
                            user_baseinfo('capital', "pay");
                        }
                    })
                </script>

                <div class="lists" style="color: rgb(0, 0, 0); padding: 18px; border-radius: 3px; max-height: calc(100vh - 360px); overflow-y: auto;">
                </div>

                <%--<div class="form-item">
                <label for="name">活动充值：</label>
                <div style="font-size: 12px; font-weight: bold;">
                    0.00
                    <span style="color: #810d0d; margin-left: 4px; background: #f9f3f3; border: 1px solid #810d0d; padding: 0 3px; border-radius: 3px;">后台</span>
                    <span style="color: #81690d; margin-left: 4px; background: #f9f3f3; border: 1px solid #81690d; padding: 0 3px; border-radius: 3px;">33.8%</span>
                </div>
            </div>--%>
            </div>
        </div>




    </div>

    <script>
        //计算总和
        var total_amount = 0;
        $('#transaction_info div[id]').each(function () {
            total_amount += parseFloat($(this).text());
        })
        total_amount = parseFloat(total_amount).toFixed(2);

        $('#total_reward').html(total_amount);
    </script>

    <script>

        var user_baseinfo = function (name, type) {
            var data = {};
            data.id = '<%=Request.QueryString["id"] %>';
            data.name = name;
            data.type = type;

            $.ajax({
                type: "POST",
                url: "../api/admin.aspx?do=user_baseinfo",
                data: data,
                datatype: "json",
                success: function (json) {
                    if (json.code != 1) {
                        //alert(json.msg);
                        return;
                    }
                    console.log('data', this.data);
                    switch (json.name) {
                        case "capital":

                            $('#capital .lists').html('');
                            var jsondata = JSON.parse(json.data);
                            for (var i = 0; i < jsondata.length; i++) {
                                var obj = jsondata[i];
                                $('#capital .lists').append('<div class="form-item" style="' + (obj.isadmin == "1" ? 'font-weight: bold;background: #eee;border: 1px solid #000;color:#8F2121;padding: 5px;border-radius: 3px;' : '') + '">                <label for="name">' + obj.type + '：</label>                <div style="font-size: 12px; ' + '">                    ' + obj.amount + '        ' + (obj.isadmin == "1" ? '<span style="color: #810d0d; margin-left: 4px; background: #f9f3f3; border: 1px solid #810d0d; padding: 0 3px; border-radius: 3px;">后台</span>  ' : '') + '                              <span style="color: #81690d; margin-left: 4px; background: #f9f3f3; border: 1px solid #81690d; padding: 0 3px; border-radius: 3px;">' + ((obj.amount / json.total_amount) * 100).toFixed(2) + '%</span>                </div>            </div>');
                            }

                            break;
                        case "transaction_info":
                            for (var k in json.list) {
                                //console.log('k', k, json.list[k]);
                                $('#' + k).html(json.list[k] == "" ? "0.00" : json.list[k]);
                            }

                            for (var i = 0; i < 2; i++) {
                                $("#total" + i + "_cost_amount").html((parseNumber(json.list["total" + i + "_really_amount"]) - parseNumber(json.list["total" + i + "_okamount"])).toFixed(2));
                            }


                            $('#buycoin_daily_amount').html((parseFloat(json.list.api_today_amount) + parseFloat(json.list.serv_today_amount) + parseFloat(json.list.dating_today_amount)).toFixed(2));
                            $('#buycoin_daily_amount').on('click', function () {
                                _modal('<div style="padding:18px;padding-bottom:0px;">今日买币来源</div>', '<p>三方充值：' + parseFloat(json.list.api_today_amount).toFixed(2) + '</p><p>手动充值（买币）：' + parseFloat(json.list.serv_today_amount).toFixed(2) + '</p><p>大厅交易：' + parseFloat(json.list.dating_today_amount).toFixed(2) + '</p><p><a onclick="' + "window.top.open_new_page('transaction_list.aspx?trans=buycoin&userid=<%=Request.QueryString["id"] %>&ss=<%=DateTime.Now.ToString("yyyy-MM-dd") %>&ee=<%=DateTime.Now.ToString("yyyy-MM-dd") %>', '今日-买币详情')" + '">查看交易详情</a></p>')
                            });

                            $('#buycoin_total_amount').html((parseFloat(json.list.api_total_amount) + parseFloat(json.list.serv_total_amount) + parseFloat(json.list.dating_total_amount)).toFixed(2));
                            $('#buycoin_total_amount').on('click', function () {
                                _modal('<div style="padding:18px;padding-bottom:0px;">历史买币来源</div>', '<p>三方充值：' + parseFloat(json.list.api_total_amount).toFixed(2) + '</p><p>手动充值（买币）：' + parseFloat(json.list.serv_total_amount).toFixed(2) + '</p><p>大厅交易：' + parseFloat(json.list.dating_total_amount).toFixed(2) + '</p><p><a onclick="' + "window.top.open_new_page('transaction_list.aspx?trans=buycoin&userid=<%=Request.QueryString["id"] %>&ss=~&ee=~', '买币详情')" + '">查看交易详情</a></p>')
                            });
                            break;
                        default:
                            break;

                    }
                },
                error: function () {
                }
            });

        }
        user_baseinfo('capital');
        user_baseinfo('transaction_info');
    </script>

</asp:Content>


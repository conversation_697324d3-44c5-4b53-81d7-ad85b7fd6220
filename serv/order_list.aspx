<%@ Page Title="抢单管理" Language="C#" MasterPageFile="~/serv/m.master" AutoEventWireup="true" CodeFile="order_list.aspx.cs" Inherits="w_index" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="Server">
    <link href="../css/element-ui.css" rel="stylesheet" />
    <style>
        .el-tag.el-tag--test {
            background-color: #d5d5f5;
            border-color: #7d7dc7;
            color: #6d3333;
        }
    </style>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="Server">
    <div class="ibox float-e-margins">
        <div class="ibox-title">
            <h5>抢单管理</h5>
        </div>
        <div class="ibox-content">
            <div class="row layui-form">
                <div class="col-md-4">
                    <div class="form-group form-buttom" style="position: relative;">
                        <input type="text" id="search-keyword" name="ordername" class="form-control" placeholder="请输入搜索的信息" style="padding-right: 50px;">
                        <a class="input_button" onclick="searchList()">查询</a>
                    </div>

                </div>

                
                <div class="col-md-4">
                    <div class="form-group form-buttom" id="twoInputs">
                        <input type="text" id="start_time" name="ordername" class="form-control" placeholder="开始时间" style="width: 48%; display: inline-block;" value="" autocomplete="off">
                        <input type="text" id="end_time" name="ordername" class="form-control" placeholder="结束时间" style="width: 48%; display: inline-block;" autocomplete="off">

                        <script>
                            $(document).ready(function () {
                                $('#start_time').datepicker({
                                    format: 'yyyy-mm-dd',
                                    autoclose: true
                                });

                                $('#end_time').datepicker({
                                    format: 'yyyy-mm-dd',
                                    autoclose: true
                                });
                            });
                        </script>
                    </div>
                </div>

                <div class="col-md-4">
                    <div class="form-group form-buttom">
                        <a class="btn btn-success" onclick="javascript:location.href=location.href;">
                            <svg t="1688237949108" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2211" width="16" height="16">
                                <path d="M512 192a298.24 298.24 0 0 0-245.162667 128 42.666667 42.666667 0 1 1-69.973333-48.810667 384.042667 384.042667 0 0 1 697.173333 180.48l36.608-34.645333a42.666667 42.666667 0 0 1 58.709334 61.952l-112.64 106.666667a42.666667 42.666667 0 0 1-71.253334-38.912A298.666667 298.666667 0 0 0 512 192z m0.170667 640a298.24 298.24 0 0 0 245.162666-128 42.666667 42.666667 0 1 1 69.973334 48.810667 384.042667 384.042667 0 0 1-697.173334-180.48l-36.650666 34.645333a42.666667 42.666667 0 0 1-58.666667-61.952l112.64-106.666667a42.666667 42.666667 0 0 1 71.253333 38.912 298.666667 298.666667 0 0 0 293.418667 354.730667z" fill="#14101C" p-id="2212"></path></svg>
                            刷新</a> <a class="btn btn-success" onclick="opennew()">
                                <svg t="1688237854283" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="15169" width="16" height="16">
                                    <path d="M725.333333 213.333333H298.666667a85.333333 85.333333 0 0 0-85.333334 85.333334v341.333333a42.666667 42.666667 0 1 1-85.333333 0V298.666667a170.666667 170.666667 0 0 1 170.666667-170.666667h426.666666a170.709333 170.709333 0 0 1 167.850667 139.733333v488.533334A170.709333 170.709333 0 0 1 725.333333 896H298.666667a170.538667 170.538667 0 0 1-144.469334-79.744l1.194667-0.682667a42.666667 42.666667 0 0 1 76.501333-37.034666c15.658667 19.584 39.765333 32.128 66.773334 32.128h426.666666a85.333333 85.333333 0 0 0 85.333334-85.333334V298.666667a85.333333 85.333333 0 0 0-85.333334-85.333334z m-213.333333 512a42.666667 42.666667 0 0 1-42.666667-42.666666v-128H341.333333a42.666667 42.666667 0 1 1 0-85.333334h128V341.333333a42.666667 42.666667 0 1 1 85.333334 0v128h128a42.666667 42.666667 0 1 1 0 85.333334h-128v128a42.666667 42.666667 0 0 1-42.666667 42.666666z" fill="#14101C" p-id="15170"></path></svg>&nbsp;添加</a>

                       <a class="btn btn-success" onclick="report_exl()">
                            <svg t="1695489540076" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="5479" width="16" height="16"><path d="M686.08 989.866667H165.546667c-51.2 0-92.16-40.96-92.16-92.16V126.293333c0-51.2 40.96-92.16 92.16-92.16h573.44c6.826667 0 11.946667 1.706667 17.066666 6.826667l187.733334 174.08c5.12 5.12 8.533333 11.946667 8.533333 18.773333v256c0 13.653333-11.946667 25.6-25.6 25.6s-25.6-11.946667-25.6-25.6V244.053333L728.746667 85.333333h-563.2C141.653333 85.333333 122.88 104.106667 122.88 126.293333v769.706667c0 22.186667 18.773333 40.96 40.96 40.96h520.533333c13.653333 0 25.6 11.946667 25.6 25.6S699.733333 989.866667 686.08 989.866667z" fill="#333333" p-id="5480"></path><path d="M919.893333 259.413333h-121.173333c-51.2 0-92.16-40.96-92.16-92.16V59.733333c0-13.653333 11.946667-25.6 25.6-25.6s25.6 11.946667 25.6 25.6v105.813334c0 22.186667 18.773333 40.96 40.96 40.96h121.173333c13.653333 0 25.6 11.946667 25.6 25.6s-11.946667 27.306667-25.6 27.306666zM500.053333 305.493333H208.213333c-13.653333 0-25.6-11.946667-25.6-25.6s11.946667-25.6 25.6-25.6h291.84c13.653333 0 25.6 11.946667 25.6 25.6s-11.946667 25.6-25.6 25.6zM616.106667 575.146667H208.213333c-13.653333 0-25.6-11.946667-25.6-25.6s11.946667-25.6 25.6-25.6h407.893334c13.653333 0 25.6 11.946667 25.6 25.6s-11.946667 25.6-25.6 25.6zM380.586667 440.32H208.213333c-13.653333 0-25.6-11.946667-25.6-25.6s11.946667-25.6 25.6-25.6h172.373334c13.653333 0 25.6 11.946667 25.6 25.6s-11.946667 25.6-25.6 25.6z" fill="#333333" p-id="5481"></path><path d="M914.773333 779.946667H600.746667c-13.653333 0-25.6-11.946667-25.6-25.6s11.946667-25.6 25.6-25.6h314.026666c13.653333 0 25.6 11.946667 25.6 25.6s-11.946667 25.6-25.6 25.6z" fill="#333333" p-id="5482"></path><path d="M831.146667 873.813333c-6.826667 0-13.653333-1.706667-18.773334-6.826666-10.24-10.24-10.24-25.6 0-35.84l75.093334-75.093334-75.093334-75.093333c-10.24-10.24-10.24-25.6 0-35.84 10.24-10.24 25.6-10.24 35.84 0l93.866667 93.866667c5.12 5.12 6.826667 11.946667 6.826667 18.773333s-3.413333 13.653333-6.826667 18.773333L848.213333 870.4c-5.12 1.706667-11.946667 3.413333-17.066666 3.413333z" fill="#333333" p-id="5483"></path><path d="M694.613333 824.32h-529.066666c-37.546667 0-68.266667-30.72-68.266667-68.266667V121.173333c0-37.546667 30.72-68.266667 68.266667-68.266666h529.066666c37.546667 0 68.266667 30.72 68.266667 68.266666v634.88c0 37.546667-30.72 68.266667-68.266667 68.266667z" fill="#333333" opacity=".3" p-id="5484"></path></svg>导出</a>

                    </div>
                </div>



            </div>

            
            <div style="display: flex; flex-wrap: wrap; margin-bottom: 20px;">
                <div class="timesel_lab">全部</div>
                <div class="timesel_lab">今天</div>
                <div class="timesel_lab">昨天</div>
                <div class="timesel_lab">本周</div>
                <div class="timesel_lab">上周</div>
                <div class="timesel_lab">本月</div>
                <div class="timesel_lab">上月</div>
            </div>


            <div>
                <input type="checkbox" onclick="set_autoAlarm(this)" />&nbsp;自动监控订单（5s刷新一次）


                <div style="display: inline-block; font-weight: bold; background: #e5ded887; padding: 2px 18px; border-radius: 5px; color: #000; font-size: 20px;">
                    <span class="total_amount">0</span> 总金额<span style="font-size: 18px; background: linear-gradient(152deg, #ff5412d4, #f3cdbe); color: #fff8e0; border-radius: 6px; font-weight: 100; padding: 2px 9px; margin-left: 5px; font-weight: bold;"><span class="total_number">0</span> 总量</span>
                </div>

            </div>


            <br />


            <div class="table-responsive">
                <table class="table">
                    <thead>
                        <tr role="head">
                            <th sort="true" class="action-cell-left">
                                <input type="checkbox" onclick="javascript: select_all(this)" />#ID</th>
                            <th sort="true" class="">锁定人</th>
                            <th sort="true" class="">操作人</th>
                            <th sort="true" class="">订单号</th>
                            <th sort="true" class="">历史佣金</th>
                            <th sort="true" class="">商品类型</th>
                            <th sort="true" class="">账号</th>
                            <th sort="true" class="">订单状态</th>
                            <th sort="true" class="">支付方式</th>
                            <th sort="true" class="">订单金额</th>
                            <th sort="true" class="">出款方式</th>
                            <th sort="true" class="">商品图片</th>
                            <th sort="true" class="">商品标题</th>
                            <th sort="true" class="">任务时间</th>
                            <%--<th sort="true" class="">创建时间</th>--%>
                            <th sort="true" class="">完成时间</th>
                            <%--<th sort="true" class="action-cell">操作</th>--%>
                        </tr>
                    </thead>
                    <tbody id="list-display">
                    </tbody>
                </table>
            </div>


            <div class="layui-table-page">
                <div id="layui-table-page1">
                    <div class="layui-box layui-laypage layui-laypage-default" id="layui-laypage-1">
                        <div style="display: inline-block;" class="cpages">
                            <!--page grow -->
                        </div>
                        <span class="layui-laypage-skip">到第<input type="text" min="1" value="1" class="layui-input target-page" />页
                            <button type="button" class="layui-laypage-btn" onclick="javascript:pager_index=$('.target-page').val()-1;getPager();">确定</button></span>
                        <span class="layui-laypage-count">共 <span class="page_count"></span>条</span>
                        <span class="layui-laypage-limits">
                            <select class="s_pages" lay-ignore="" onchange="javascript:pager_size=$(this).val();getPager();">
                                <option value="10">10 条/页</option>
                                <option value="20" selected="">20 条/页</option>
                                <option value="30">30 条/页</option>
                                <option value="50">50 条/页</option>
                                <option value="100">100 条/页</option>
                                <option value="200">200 条/页</option>
                            </select></span>
                    </div>
                </div>
            </div>

        </div>
    </div>
    <script>
        var _action = 'order_list';
        var _actionName = '';
    </script>
    <script id="item_classList" type="text/javascript">
        <%=ToJson(chelper.gdt("item_classList")) %>
    </script>
    <script>
        var item_classList = JSON.parse($('#item_classList').html());
        var classList = [];
        for (var i = 0; i < item_classList.length; i++) {
            classList.push([item_classList[i].id, item_classList[i].name]);
        }

        function opennew() {
            var modify_model = [];


            modify_model.push({ name: '选择分类', id: 'classId', data: classList, type: 'select' });
            modify_model.push({ name: '生成数量', id: 'number', value: '' });

            edit({ aname: 'admin', title: '添加' + _actionName, action: _action, id: '', data: modify_model });
        }
    </script>

    <noscript id="payer_ways"><%=uConfig.stcdata("payer_ways").Replace(" ", "") %></noscript>
    <script>
        var payer_ways_list = $('#payer_ways').html().split('\n');
        var payerWays = [];
        for (var i = 0; i < payer_ways_list.length; i++) {
            payerWays.push([payer_ways_list[i]]);
        }
    </script>

    <script>

        var current_json = [];
        var pager_size = $(".s_pages").val();
        var pager_index = 0;
        var pager_key = "";

        $("#pagesize").val(pager_key);
        function setPageSize(obj) {
            pager_index = 0;
            pager_key = $("#search-keyword").val();
            opt = $(obj).val();
            getPager();
        }

        function searchList() {
            opt = $("#opt1").val();
            pager_index = 0;
            pager_key = $("#search-keyword").val();
            getPager();
        }
        function getParam() {
            return { limit: pager_size, p: pager_index, page: _action, keyword: pager_key, audit_state: $("#audit_state").val(), settle_state: $("#settle_state").val(), start_time: $("#start_time").val(), end_time: $("#end_time").val(), tarea: "<%=Request.QueryString["area"] %>", state: "<%=Request.QueryString["state"] %>", group: "<%=Request.QueryString["group"] %>" }
        }

        function getPager() {

            $.ajax({
                type: "POST",
                url: "../api/admin.aspx?do=page",
                data: getParam(),
                datatype: "json",
                success: function (json) {
                    $("#list-display").html("");
                    if (json.code == 1) {
                        var data = json.data.list;
                        current_json = data;
                        for (var i = 0; i < data.length; i++) {
                            var tr = "";
                            tr += "<tr style='" + (data[i].orderLock == 1 ? "background:#f5f5f5;" : "") + "'>";
                            tr += ('<td class="action-cell-left"><input class="good_select" type="checkbox" data-id="' + data[i].id + '" id="checkid_' + data[i].id + '" />#' + data[i].id + '</td>');
                            tr += ("<td>" + '<span class="button-style style-hs">' + data[i].lock_user + '</span>' + "</td>");
                            tr += ("<td style='display:flex;'>" + (data[i].confirm_from == 'user' ? '<span class="button-style style-lvs">用户</span>' : data[i].confirm_from == 'admin' ? '<span class="button-style">管理员</span>' : '') + "</td>");
                            tr += ("<td class=''>" + '<b style="color:#000;">' + data[i].orderId + '</b>' + "</td>");
                            tr += ("<td class=''>" + parseFloat(data[i].reward_amount).toFixed(2)+ "</td>");
                            tr += ("<td class=''>" + data[i].class_name + "</td>");
                            tr += ("<td class=''>" + (data[i].group_name == '-' ? '' : "<span class='button-style style-js'>" + data[i].group_name + "</span>") + data[i].phone + "</td>");
                            tr += ("<td style='display:flex;'>" + (data[i].state == 0 ? '<span class="button-style">未匹配</span>' : data[i].state == 1001 ? '<span class="button-style style-js">抢单中</span>' : data[i].state == 1000 ? (data[i].orderLock == 1 ? '<span class="button-style">锁定待确认 <span style="color:#a11313;">@' + data[i].lock_user + '</span></span>' : '<span class="button-style style-ls">交易中</span>') : (data[i].state == 1 ) ? '<span class="button-style style-lvs">已确认</span>' : data[i].state == 1 ? '<span class="button-style style-ls-low">等待确认</span>' : data[i].state == -1 ? '<span class="button-style style-hs">订单超时</span>' : data[i].state == -2 ? '<span class="button-style style-hs">系统取消</span>' : '<span class="button-style">其他</span>') + (data[i].state == 1000 ? '<a style="display: flex;align-items: center;background: #eee;padding: 0px 8px;margin-left: 6px;line-height: 19px;color: #896b18;" onclick="' + "get_action({name:'cancel_order',data:{id:" + data[i].id + "},n:'撤销订单',e:'撤销订单 " + data[i].orderId + " 的交易<br>【订单金额】：" + data[i].amount + "'});" + '"><svg t="1693065317535" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="6520" width="16" height="16"><path d="M596.2752 656.9472m-179.0976 0a179.0976 179.0976 0 1 0 358.1952 0 179.0976 179.0976 0 1 0-358.1952 0Z" fill="#FFBE0A" p-id="6521"></path><path d="M515.584 923.4432c-226.8672 0-411.4432-184.576-411.4432-411.4432s184.576-411.4432 411.4432-411.4432 411.4432 184.576 411.4432 411.4432-184.576 411.4432-411.4432 411.4432z m0-751.2064c-187.3408 0-339.7632 152.4224-339.7632 339.7632s152.4224 339.7632 339.7632 339.7632 339.7632-152.4224 339.7632-339.7632-152.4224-339.7632-339.7632-339.7632z" fill="#34332E" p-id="6522"></path><path d="M687.7184 542.72H347.1872c-19.8144 0-35.84-16.0256-35.84-35.84s16.0256-35.84 35.84-35.84h340.5312c19.8144 0 35.84 16.0256 35.84 35.84s-16.0256 35.84-35.84 35.84z" fill="#34332E" p-id="6523"></path></svg>&nbsp;撤销</a>'


                                + (data[i].state == 1000 && data[i].orderLock == 1 ? '&nbsp;<a style="display: flex;align-items: center;background: #eee;padding: 0px 8px;margin-left: 6px;line-height: 19px;color: #896b18;" onclick="' + "get_action({name:'finish_order',data:{id:" + data[i].id + "},n:'确认交易',e:'确认 " + data[i].orderId + " 的交易<br>【订单金额】：" + data[i].amount + "（确认将完成订单）',selects:[{id:'payer_way',name:'出款方式',list:" + JSON.stringify(payerWays).replace(/"/g, "'") + "}]});" + '"><svg t="1693065904309" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="6904" width="16" height="16"><path d="M608.256 565.3504m-179.0976 0a179.0976 179.0976 0 1 0 358.1952 0 179.0976 179.0976 0 1 0-358.1952 0Z" fill="#FFBE0A" p-id="6905"></path><path d="M635.5968 921.4464c-49.0496 0-92.9792-25.7536-117.4016-69.9392l-116.3264-210.3808-231.1168-103.1168C125.4912 517.8368 98.3552 474.4704 99.9424 424.96c1.5872-49.5616 31.4368-91.0848 77.9264-108.3392L730.624 111.2576a134.94784 134.94784 0 0 1 137.728 26.3168c37.5808 33.8432 53.0432 85.2992 40.448 134.2976l-141.1072 547.9424c-14.1312 54.8352-57.8048 93.3376-113.9712 100.5056-6.144 0.7168-12.1856 1.1264-18.1248 1.1264zM202.8544 383.7952c-28.0576 10.4448-31.0272 35.8912-31.232 43.4688-0.256 7.5776 1.0752 33.1776 28.416 45.3632l242.0736 108.032c7.1168 3.1744 13.0048 8.5504 16.7424 15.4112l122.112 220.8768c13.1072 23.6544 36.9152 35.7888 63.744 32.3584 26.8288-3.4304 46.848-21.0944 53.6064-47.2576L839.3728 253.952c6.0416-23.3984-1.0752-47.0016-19.0464-63.1296-17.92-16.1792-42.1376-20.7872-64.768-12.3904L202.8544 383.7952z" fill="#34332E" p-id="6906"></path><path d="M532.224 529.6128c-9.0624 0-18.176-3.4304-25.1392-10.2912a35.87584 35.87584 0 0 1-0.4096-50.688l152.064-154.4704a35.82976 35.82976 0 0 1 50.688-0.4096 35.87584 35.87584 0 0 1 0.4096 50.688l-152.064 154.4704a35.57376 35.57376 0 0 1-25.5488 10.7008z" fill="#34332E" p-id="6907"></path></svg>&nbsp;确认</a>' : '')


                                + (data[i].state == 1000 && data[i].orderLock != 1 ? '&nbsp;<a style="display: flex;align-items: center;padding: 0px 8px;margin-left: 6px;line-height: 19px;color: #5a5a5c;border: 1px solid #5a5a5c;" onclick="' + "get_action({name:'lock_order',data:{id:" + data[i].id + "},n:'锁定交易',e:'锁定 " + data[i].orderId + " 的交易<br>【订单金额】：" + data[i].amount + "'});" + '"><svg t="*************" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="7884" width="16" height="16"><path d="M500.224 694.528c-56.576 0-102.912-46.08-102.912-102.912a102.912 102.912 0 0 1 205.824 0c0 56.832-46.336 102.912-102.912 102.912z m0-146.944a44.032 44.032 0 1 0 0 88.064 44.032 44.032 0 0 0 0-88.064z" fill="#005CFF" p-id="7885"></path><path d="M733.44 318.72h-84.224c-1.28-77.056-64-139.264-141.312-139.264-77.312 0-140.032 62.208-141.312 139.264h-84.224c-64.512 0-116.992 52.48-116.992 116.992V747.52c0 64.512 52.48 116.992 116.992 116.992h450.816c64.512 0 116.992-52.48 116.992-116.992V435.712a116.736 116.736 0 0 0-116.736-116.992z m-225.536-80.384c44.8 0 81.408 35.84 82.432 80.384h-165.12c1.536-44.544 37.888-80.384 82.688-80.384zM791.808 747.52c0 32.256-26.112 58.368-58.368 58.368H282.624c-32.256 0-58.368-26.112-58.368-58.368V435.712c0-32.256 26.112-58.368 58.368-58.368h450.816c32.256 0 58.368 26.112 58.368 58.368V747.52z" fill="#1B1F28" p-id="7886"></path></svg>&nbsp;锁定</a>' : '')

                                : '') + "</td>");
                            tr += ("<td class=''>" + '<span class="button-style style-zs" style="cursor:pointer;" onclick="show_details(' + data[i].id + ')">' + data[i].payment_type + '</span>' + "</td>");
                            tr += ("<td>" + '<span style="font-weight: bold;font-size: 13px;text-shadow: 5px 5px 5px #af4f897d;color: #af4f76;">' + data[i].amount + '</span>' + "</td>");
                            tr += ("<td class=''>" + data[i].payer_way + "</td>");
                            tr += ("<td class=''>" + '<img src="' + data[i].item_imgurl + '" width="25" height="25">' + "</td>");
                            tr += ("<td class='' style='max-width:80px;'>" + data[i].item_name + "</td>");
                            tr += ("<td class=''>" + data[i].update_time + "</td>");
                            //tr += ("<td class=''>" + data[i].create_time + "</td>");
                            tr += ("<td class=''>" + data[i].finish_time + "</td>");

                            var modify_model = new Array();
                            //modify_model.push({ name: '商品图片', id: 'imgurl', data: [data[i].imgurl], type: 'img' });

                            //tr += ("<td>"
                            //    + "<button type='button' class='el-button  el-button--default el-button--mini' onclick='edit(" + edtext({ aname: 'admin', title: _actionName + "修改", action: _action, id: data[i].id, data: modify_model }) + ")'><span><i class='fa fa-edit'></i></span></button>"
                            //    + "<button type='button' class='el-button  el-button--default el-button--mini' onclick=\"event_create({'aname':'admin','name':'" + _action + "','event':'delete','id':" + data[i].id + "})\"><span><i class='fa fa-remove'></i></span></button>"
                            //    + "</td>");




                            //tr += ("<td class='action-cell '>"
                            //    + ' <div class="card-toolbar">'
                            //    + '&nbsp;<a class="card-button common-button" ' + "onclick=\"event_create({'aname':'admin','name':'" + _action + "','event':'delete','id':" + data[i].id + "})\"" + '><svg t="1688237896673" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="1699" width="16" height="16" style="position:relative;top:3px;"><path d="M612.693333 170.666667l8.533334 42.666666H402.773333l8.533334-42.666666h201.386666z m-293.632 25.941333a86.186667 86.186667 0 0 0-1.706666 16.725333H170.666667a42.666667 42.666667 0 0 0 0 85.333334h682.666666a42.666667 42.666667 0 1 0 0-85.333334h-146.730666c0-5.504-0.554667-11.093333-1.706667-16.725333l-8.533333-42.666667A85.333333 85.333333 0 0 0 612.778667 85.333333H411.306667a85.333333 85.333333 0 0 0-83.712 68.608l-8.533334 42.666667zM426.666667 469.333333a42.666667 42.666667 0 0 0-42.666667 42.666667v170.666667a42.666667 42.666667 0 1 0 85.333333 0v-170.666667a42.666667 42.666667 0 0 0-42.666666-42.666667z m128 42.666667a42.666667 42.666667 0 1 1 85.333333 0v170.666667a42.666667 42.666667 0 1 1-85.333333 0v-170.666667zM255.402667 384A42.666667 42.666667 0 0 1 298.666667 426.069333L301.738667 640H301.653333a42.666667 42.666667 0 1 1-85.290666-2.176l-2.986667-210.56A42.666667 42.666667 0 0 1 255.36 384z m51.925333 396.586667h0.042667A103.381333 103.381333 0 0 0 406.698667 853.333333h210.602666c54.186667 0 98.56-40.789333 103.210667-92.757333L725.333333 426.069333a42.666667 42.666667 0 0 1 85.333334 1.194667l-4.864 337.365333-0.128 1.365334C798.08 863.872 715.562667 938.666667 617.301333 938.666667H406.698667a188.885333 188.885333 0 0 1-180.096-130.389334 42.666667 42.666667 0 0 1 80.725333-27.648z" fill="#14101C" p-id="1700"></path></svg>&nbsp;删除</a>'
                            //    //+ '<div class="card-popup">'

                            //    //+ "<div class='menu-item px-3'><a onclick='edit(" + edtext({ aname: 'admin', title: _actionName + "修改", action: _action, id: data[i].id, data: modify_model }) + ")' class='menu-link px-3'>编辑</a></div>"

                            //    //+ "<div class='menu-item px-3'><a onclick=\"event_create({'aname':'admin','name':'" + _action + "','event':'delete','id':" + data[i].id + "})\" class='menu-link px-3'>删除</a></div>"

                            //    //+ '</div>'
                            //    + '</div>');


                            tr += "</tr>";
                            $("#list-display").append(tr)


                            var __temp = data[i];
                            __temp.modify_model = modify_model;
                            _bindEvent(__temp);

                            //$('button[edit-' + __temp.id + ']').on("click", function () {
                            //    console.log('attrs', $(this).closest('tr').html(), __temp.modify_model);

                            //    edit({ aname: 'admin', title: '修改' + _actionName, action: _action, id: __temp.id, data: __temp.modify_model });
                            //})



                        }
                        $(".page_size").html(pager_size);
                        $(".page_count").html(json.data.count);
                        $(".cpages").html(json.data.html);

                        $(".total_amount").html(parseFloat(json.total_amount).toFixed(2));
                        $(".total_number").html(json.total_number);


                        getdata();

                        pop_mp3tip();

                    } else {

                    }
                },
                error: function () {
                }
            });
        }
        getPager();

        function _bindEvent(__temp) {
            $('button[edit-' + __temp.id + ']').on("click", function () {
                console.log('attrs', $(this).closest('tr').html(), __temp.modify_model);

                edit({ aname: 'admin', title: '修改' + _actionName, action: _action, id: __temp.id, data: __temp.modify_model });
            })
        }

        function _pt(i) {
            pager_index = i;
            getPager();
        }
    </script>

    <script>
        layui.use(['form', 'layedit', 'laydate'], function () {
        });
    </script>

    <script>
        function update_select(state) {
            event_create({ 'aname': 'admin', 'name': _action, 'event': 'delete', 'id': select_all_id(), 'state': state });
        }


        function report_exl() {
            export_data(getParam());
        }
    </script>

    <script>
        var click_ids = {};
        function checkIsSelect(obj) {
            if (obj.prop('checked')) {
                return true;
            } else {
                return false;
            }
        }
        function goods_click(t) {
            t = $(t);
            var obj = t.find(".good_select");
            obj.prop("checked", !checkIsSelect(obj));

        }

        function select_all(obj) {
            $(".good_select").prop("checked", checkIsSelect($(obj)));
        }

        function select_all_id() {
            var ids = "";
            $("#list-display tr").each(function () {
                var select_obj = $(this).find(".good_select");
                if (checkIsSelect(select_obj)) {
                    if (ids != "") {
                        ids += ",";
                    }
                    ids += select_obj.data("id");
                }
            });
            return ids;
        }
    </script>

    <audio id="myAudio" controls style="display: none;">
        <source src="../static/files/新订单.mp3" type="audio/mpeg">
        Your browser does not support the audio element.
    </audio>

    <script>

        var autoTimer = 0;
        var lastList = null;

        var set_autoAlarm = function (e) {
            if (checkIsSelect($(e))) {
                console.log('启动');
                lastList = null;

                autoTimer = setInterval(function () {


                    getPager();

                }, 5000);

            } else {
                console.log('关闭');
                clearInterval(autoTimer);
                autoTimer = 0;
            }
        }

        var pop_mp3tip = function (json) {
            if (autoTimer == 0) {
                return;
            }



            for (var i = 0; i < current_json.length; i++) {
                if (current_json[i].state == 1000 || current_json[i].state == 1001) {
                    play_mp3tip();
                    break;
                }
            }


            //if (lastList == null) {
            //    lastList = [];
            //    for (var i = 0; i < current_json.length; i++) {
            //        if (current_json[i].state == 1001) {
            //            lastList.push(current_json[i].id);
            //        }
            //    }
            //}

            ////console.log('lastList', lastList);
            //for (var i = 0; i < current_json.length; i++) {
            //    if (current_json[i].state == 1001) {
            //        if (lastList.indexOf(current_json[i].id) == -1) {
            //            lastList.push(current_json[i].id);
            //            play_mp3tip();
            //        }
            //    }
            //}
            //console.log('newList', lastList);
        }

        var mp3_audio = document.getElementById("myAudio"); // 获取音频元素
        var play_mp3tip = function () {
            //console.log('播放订单提示');
            mp3_audio.play();
        }




        var show_details = function (id, type) {
            var details_info = "";
            for (var i = 0; i < current_json.length; i++) {
                var obj = current_json[i];

                if (obj.id == id) {
                    details_info += ' <div style="display: flex; margin: 20px 0; padding-bottom: 20px; border-bottom: 1px solid #eee;">            <div>                <b>支付方式：</b>            </div>            <div style="margin-left: 10px;">                ' + obj.payment_type + '            </div>        </div>';
                    details_info += ' <div style="display: flex; margin: 20px 0; padding-bottom: 20px; border-bottom: 1px solid #eee;">            <div>                <b>收款姓名：</b>            </div>            <div style="margin-left: 10px;">                ' + obj.payment_name + '            </div>        </div>';

                    if (obj.payment_type == "银行卡") {
                        details_info += ' <div style="display: flex; margin: 20px 0; padding-bottom: 20px; border-bottom: 1px solid #eee;">            <div>                <b>收款卡号：</b>            </div>            <div style="margin-left: 10px;">                ' + obj.payment_bankid + '            </div>        </div>';
                        details_info += ' <div style="display: flex; margin: 20px 0; padding-bottom: 20px; border-bottom: 1px solid #eee;">            <div>                <b>开户行：</b>            </div>            <div style="margin-left: 10px;">                ' + obj.payment_bankname + '            </div>        </div>';
                    } else {
                        details_info += ' <div style="display: flex; margin: 20px 0; padding-bottom: 20px; border-bottom: 1px solid #eee;">            <div>                <b>' + obj.payment_type + '账号：</b>            </div>            <div style="margin-left: 10px;">                ' + obj.payment_bankid + '            </div>        </div>';
                    }
                    details_info += ' <div style="display: flex; margin: 20px 0; padding-bottom: 20px; border-bottom: 1px solid #eee;">            <div>                <b>交易金额：</b>            </div>            <div style="margin-left: 10px;">                ' + obj.amount + '            </div>        </div>';

                    details_info += '<div style="display: flex; margin: 20px 0; padding-bottom: 20px; border-bottom: 1px solid #eee;">' + (obj.state == 1000 && obj.orderLock != 1 ? '&nbsp;<a style="display: flex;align-items: center;padding: 10px 39px;margin-left: 6px;line-height: 19px;color: #5a5a5c;border: 1px solid #5a5a5c;" onclick="' + "get_action({name:'lock_order',data:{id:" + obj.id + "},n:'锁定交易',e:'锁定 " + obj.orderId + " 的交易<br>【订单金额】：" + obj.amount + "'});" + '"><svg t="*************" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="7884" width="16" height="16"><path d="M500.224 694.528c-56.576 0-102.912-46.08-102.912-102.912a102.912 102.912 0 0 1 205.824 0c0 56.832-46.336 102.912-102.912 102.912z m0-146.944a44.032 44.032 0 1 0 0 88.064 44.032 44.032 0 0 0 0-88.064z" fill="#005CFF" p-id="7885"></path><path d="M733.44 318.72h-84.224c-1.28-77.056-64-139.264-141.312-139.264-77.312 0-140.032 62.208-141.312 139.264h-84.224c-64.512 0-116.992 52.48-116.992 116.992V747.52c0 64.512 52.48 116.992 116.992 116.992h450.816c64.512 0 116.992-52.48 116.992-116.992V435.712a116.736 116.736 0 0 0-116.736-116.992z m-225.536-80.384c44.8 0 81.408 35.84 82.432 80.384h-165.12c1.536-44.544 37.888-80.384 82.688-80.384zM791.808 747.52c0 32.256-26.112 58.368-58.368 58.368H282.624c-32.256 0-58.368-26.112-58.368-58.368V435.712c0-32.256 26.112-58.368 58.368-58.368h450.816c32.256 0 58.368 26.112 58.368 58.368V747.52z" fill="#1B1F28" p-id="7886"></path></svg>&nbsp;锁定</a>' : '') + '</div>';

                    _modal('收款信息', details_info);
                    break;
                }
            }
        }


        var create_order = function (id) {

            _modal("创建订单", "<div id='model-page' aid='" + id + "'>" + $("#buy_page").html() + "</div>");

            var details_info = "";
            for (var i = 0; i < current_json.length; i++) {
                var obj = current_json[i];
                if (obj.id == id) {
                    //$('#model-page .bank_item').hide();
                    //$('#model-page').attr();

                    $('#model-page .buyer_type').on('change', function () {
                        var name = $(this).val();
                        if (name == "银行卡") {
                            $('#model-page .bank_item').show();
                        } else {
                            $('#model-page .bank_item').hide();
                        }
                    });

                    $('#model-page .buyer_number').val((obj.total_amount - obj.deal_amount));

                    if (obj.accept_separate != 1) {
                        $('#model-page .buyer_number').css({ "background": "#f1f1f1" }).attr({ "disabled": "disabled" });
                    }

                    break;
                }
            }
        }

        var new_order = function () {
            var post_data = {
                id: $('#model-page').attr("aid"),
                buyer_type: $('#model-page .buyer_type').val(),
                buyer_amount: $('#model-page .buyer_number').val(),
                buyer_name: $('#model-page .buyer_name').val(),
                buyer_bankname: $('#model-page .buyer_bankname').val(),
                buyer_bankid: $('#model-page .buyer_bankid').val()
            }

            $.ajax({
                type: "POST",
                url: "../Api/admin.aspx?do=new_order",
                data: post_data,
                datatype: "json",
                success: function (data) {
                    if (data.code == 1) {
                        layer.msg(data.msg, { icon: 1, time: 1000 }, function () {
                            $('#myModal').modal('hide');
                            getPager();
                        });
                    } else {
                        layer.msg(data.msg, { icon: 2, time: 1500 });
                    }
                },
                error: function () {
                    layer.msg("抱歉，操作有误", { icon: 2, time: 1500 });
                }
            });

        }
    </script>

    <style>
        .buy_list {
            display: flex;
            align-items: center;
            font-weight: bold;
            margin-top: 30px;
        }

            .buy_list .name {
                width: 100px;
                flex-shrink: 0;
            }

            .buy_list .item {
                width: 100%;
            }

                .buy_list .item input {
                    width: 100%;
                }
    </style>
    <div style="background: #fff; padding: 18px; display: none;" id="buy_page">

        <div class="buy_list">
            <div class="name">
                支付方式：
           
            </div>
            <div class="item">
                <select class="form-control buyer_type">
                    <option>银行卡</option>
                    <option>支付宝</option>
                    <option>微信</option>
                </select>
            </div>
        </div>

        <div class="buy_list">
            <div class="name" style="color: blue;">
                购买数量：           
            </div>
            <div class="item">
                <input class="form-control buyer_number" />
            </div>
        </div>

        <div class="buy_list">
            <div class="name">
                姓名：           
            </div>
            <div class="item">
                <input class="form-control buyer_name" />
            </div>
        </div>

        <div class="buy_list bank_item">
            <div class="name">
                开户行：           
            </div>
            <div class="item">
                <input class="form-control buyer_bankname" />
            </div>
        </div>

        <div class="buy_list">
            <div class="name">
                账号：           
            </div>
            <div class="item">
                <input class="form-control buyer_bankid" />
            </div>
        </div>

        <a class="btn btn-success" onclick="new_order()" style="margin-top: 24px; margin-left: 100px;">
            <svg t="*************" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="4127" width="16" height="16">
                <path d="M512 170.666667a341.333333 341.333333 0 0 0-290.133333 521.216 42.666667 42.666667 0 0 1-72.533334 45.013333A424.874667 424.874667 0 0 1 85.333333 512C85.333333 276.352 276.352 85.333333 512 85.333333s426.666667 191.018667 426.666667 426.666667-191.018667 426.666667-426.666667 426.666667a424.874667 424.874667 0 0 1-234.666667-70.314667 42.666667 42.666667 0 1 1 46.933334-71.210667A341.333333 341.333333 0 1 0 512 170.666667z m222.165333 264.832a42.666667 42.666667 0 0 0-60.330666-60.330667L469.333333 579.669333l-119.168-119.168a42.666667 42.666667 0 0 0-60.330666 60.330667l149.333333 149.333333a42.666667 42.666667 0 0 0 60.330667 0l234.666666-234.666666z" fill="#14101C" p-id="4128"></path></svg>&nbsp;创建订单</a>

    </div>



    <script>
        // 传入格式为 "yyyy-MM-dd HH:mm:ss" 的时间戳
        function countdownToTime(timestamp) {
            var targetDate = new Date(timestamp);
            var currentDate = new Date();
            var timeDiff = targetDate - currentDate;

            if (timeDiff <= 0) {
                return '已超时';
            }

            var hours = Math.floor(timeDiff / (1000 * 60 * 60));
            var minutes = Math.floor((timeDiff % (1000 * 60 * 60)) / (1000 * 60));
            var seconds = Math.floor((timeDiff % (1000 * 60)) / 1000);

            var formattedTime = hours + ':' + minutes.toString().padStart(2, '0') + ':' + seconds.toString().padStart(2, '0');
            return formattedTime;
        }
        var interval = 0


        function getdata() {
            clearInterval(interval);
            interval = setInterval(function () {
                $('td[expiretime]').each(function () {
                    var expiretime = $(this).attr('expiretime');
                    var result = countdownToTime(expiretime);
                    if (result == "已超时") {
                        result = "<span style='color:gray;'>已超时</span>";
                        $(this).removeAttr("expiretime");
                    }
                    $(this).html(result);
                })
            }, 1000); // 更新频率每秒钟
        }

    </script>

</asp:Content>



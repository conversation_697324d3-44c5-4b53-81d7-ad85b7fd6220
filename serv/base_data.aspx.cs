using GoogleAuthenticator_g;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

public partial class serv_index : baseClass
{
    public DataTable dt = new DataTable();
    public string googleCodeQrImg = "";
    public string googleCodeSecret = "";
    protected void Page_Load(object sender, EventArgs e)
    {
        if (!IsPostBack)
        {
            dbClass db = new dbClass();
            List<SqlParameter> pams = new List<SqlParameter>();
            string sql = string.Empty;

            pams.Add(new SqlParameter("@userid", uConfig.p_idAD));
            sql = " select * from serv_admin with(nolock) where id=@userid ";
            dt = db.getDataTable(sql, pams.ToArray());



            if (dt.Rows.Count > 0)
            {

                string gverify = dt.Rows[0]["google_verify"] + "";

                if (gverify == "1")
                {
                    googleCodeQrImg = "已验证";
                }
                else
                {
                    string gkey = dt.Rows[0]["google_key"] + "";

                    GoogleAuthenticator authenticator = new GoogleAuthenticator(30, gkey + "!!transaction_system##01800");
                    googleCodeSecret = authenticator.GetMobilePhoneKey();
                    googleCodeQrImg = string.Format("otpauth://totp/{1}:{2}?secret={0}&issuer={1}", googleCodeSecret.Replace("=", ""), "QD验证码", dt.Rows[0]["name"] + "");

                }
                
            }
        }
    }
}
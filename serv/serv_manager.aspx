<%@ Page Title="用户管理" Language="C#" MasterPageFile="~/serv/m.master" AutoEventWireup="true" CodeFile="serv_manager.aspx.cs" Inherits="w_index" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="Server">
    <link href="../css/element-ui.css" rel="stylesheet" />
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="Server">
    <div class="ibox float-e-margins">
        <div class="ibox-title">
            <h5>管理员管理</h5>
        </div>
        <div class="ibox-content">
            <div class="row layui-form">
                <div class="col-md-4">
                    <div class="form-group form-buttom">
                        <input type="text" id="search-keyword" name="ordername" class="form-control" placeholder="请输入搜索的信息">
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-group form-buttom">

                        <a class="btn btn-success" onclick="searchList()">
                            <svg t="1688237921441" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="1949" width="16" height="16">
                                <path d="M361.130667 658.688a256 256 0 1 1 282.794666-44.672 42.496 42.496 0 0 0-10.368 14.933333c-0.853333 0.682667-1.621333 1.450667-2.389333 2.218667a42.666667 42.666667 0 0 0 0 60.330667l170.666667 170.666666a42.666667 42.666667 0 0 0 60.330666-60.330666l-142.762666-142.762667a341.333333 341.333333 0 1 0-169.344 99.242667 42.666667 42.666667 0 0 0-20.224-82.901334 254.762667 254.762667 0 0 1-168.704-16.725333z" fill="#14101C" p-id="1950"></path></svg>
                            查询</a>
                        <a class="btn btn-success" onclick="javascript:location.href=location.href;">
                            <svg t="1688237949108" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2211" width="16" height="16">
                                <path d="M512 192a298.24 298.24 0 0 0-245.162667 128 42.666667 42.666667 0 1 1-69.973333-48.810667 384.042667 384.042667 0 0 1 697.173333 180.48l36.608-34.645333a42.666667 42.666667 0 0 1 58.709334 61.952l-112.64 106.666667a42.666667 42.666667 0 0 1-71.253334-38.912A298.666667 298.666667 0 0 0 512 192z m0.170667 640a298.24 298.24 0 0 0 245.162666-128 42.666667 42.666667 0 1 1 69.973334 48.810667 384.042667 384.042667 0 0 1-697.173334-180.48l-36.650666 34.645333a42.666667 42.666667 0 0 1-58.666667-61.952l112.64-106.666667a42.666667 42.666667 0 0 1 71.253333 38.912 298.666667 298.666667 0 0 0 293.418667 354.730667z" fill="#14101C" p-id="2212"></path></svg>
                            刷新</a>
                        <a class="btn btn-success" onclick="openuser()">
                            <svg t="1688237854283" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="15169" width="16" height="16">
                                <path d="M725.333333 213.333333H298.666667a85.333333 85.333333 0 0 0-85.333334 85.333334v341.333333a42.666667 42.666667 0 1 1-85.333333 0V298.666667a170.666667 170.666667 0 0 1 170.666667-170.666667h426.666666a170.709333 170.709333 0 0 1 167.850667 139.733333v488.533334A170.709333 170.709333 0 0 1 725.333333 896H298.666667a170.538667 170.538667 0 0 1-144.469334-79.744l1.194667-0.682667a42.666667 42.666667 0 0 1 76.501333-37.034666c15.658667 19.584 39.765333 32.128 66.773334 32.128h426.666666a85.333333 85.333333 0 0 0 85.333334-85.333334V298.666667a85.333333 85.333333 0 0 0-85.333334-85.333334z m-213.333333 512a42.666667 42.666667 0 0 1-42.666667-42.666666v-128H341.333333a42.666667 42.666667 0 1 1 0-85.333334h128V341.333333a42.666667 42.666667 0 1 1 85.333334 0v128h128a42.666667 42.666667 0 1 1 0 85.333334h-128v128a42.666667 42.666667 0 0 1-42.666667 42.666666z" fill="#14101C" p-id="15170"></path></svg>&nbsp;添加用户</a>


                    </div>
                </div>
            </div>

            <div class="table-responsive">
                <table class="table">
                    <thead>
                        <tr role="head">
                            <th sort="true" class="action-cell-left">#ID</th>
                            <th sort="true">姓名</th>
                            <th sort="true">账号</th>
                            <th sort="true">权限</th>
                            <th sort="true">状态</th>
                            <th sort="true">登录时间</th>
                            <th sort="true">创建时间</th>
                            <th sort="true">验证码</th>
                            <th sort="true" class='action-cell '>编辑</th>
                        </tr>
                    </thead>
                    <tbody id="list-display">
                    </tbody>
                </table>
            </div>


            <div class="layui-table-page">
                <div id="layui-table-page1">
                    <div class="layui-box layui-laypage layui-laypage-default" id="layui-laypage-1">
                        <div style="display: inline-block;" class="cpages">
                            <!--page grow -->
                        </div>
                        <span class="layui-laypage-skip">到第<input type="text" min="1" value="1" class="layui-input target-page" />页
                            <button type="button" class="layui-laypage-btn" onclick="javascript:pager_index=$('.target-page').val()-1;getPager();">确定</button></span>
                        <span class="layui-laypage-count">共 <span class="page_count"></span>条</span>
                        <span class="layui-laypage-limits">
                            <select class="s_pages" lay-ignore="" onchange="javascript:pager_size=$(this).val();getPager();">
                                <option value="10">10 条/页</option>
                                <option value="20" selected="">20 条/页</option>
                                <option value="30">30 条/页</option>
                                <option value="50">50 条/页</option>
                                <option value="100">100 条/页</option>
                                <option value="200">200 条/页</option>
                            </select></span>
                    </div>
                </div>
            </div>

        </div>
    </div>
    <script type="text/html" id="role_data">
        <%=ToJson(chelper.gdt("serv_admin_role")) %>
    </script>
    <script>
        var _action = 'serv_admin';
        var _actionName = '管理员';

        var select_qx = [];
        select_qx.push([-1, '超级管理']);
        var r = JSON.parse($("#role_data").html());
        for (var i = 0; i < r.length; i++) {
            select_qx.push([r[i].id, r[i].name]);
        }

        function openuser() {
            var cg = [];

            cg.push({ name: '姓名', id: 'username', value: '' });
            cg.push({ name: '账号', id: 'usernick', value: '' });
            cg.push({ name: '密码', id: 'password', value: '' });
            cg.push({ name: '状态', id: 'state', value: 1, type: 'option', data: [['1', '正常'], ['0', '停用']] });
            cg.push({ name: '权限', id: 'roleid', value: 1, type: 'select', data: select_qx });
            edit({ aname: 'admin', title: '管理员创建', action: 'serv_admin', id: '', data: cg });
        }
    </script>
    <script>
        var pager_size = $(".s_pages").val();
        var pager_index = 0;
        var pager_key = "";

        var opt = get_param("opt");
        var opt2 = get_param("fid");

        $("#pagesize").val(pager_key);
        function setPageSize(obj) {
            pager_index = 0;
            pager_key = $("#search-keyword").val();
            opt = $(obj).val();
            getPager();
        }

        function searchList() {
            opt = $("#opt1").val();
            pager_index = 0;
            pager_key = $("#search-keyword").val();
            getPager();
        }

        function getPager() {

            $.ajax({
                type: "POST",
                url: "../api/admin.aspx?do=page",
                data: { limit: pager_size, p: pager_index, page: 'serv_admin', keyword: pager_key, opt: opt, opt2: opt2 },
                datatype: "json",
                success: function (json) {
                    $("#list-display").html("");
                    if (json.code == 1) {
                        var data = json.data.list;
                        for (var i = 0; i < data.length; i++) {
                            var tr = "";
                            tr += "<tr>";
                            tr += ("<td># " + data[i].id + "</td>");
                            tr += ("<td>" + '<span style="display:inline-block;border: 1px solid #724bb1;background: #f1edf5;color: #724bb1;padding: 0px 10px;font-size: 12px;border-radius: 6px;font-weight: bold;margin-right: 3px;cursor: pointer;" onclick="open_new_page(\'serv_logs.aspx?serv_id=' + data[i].id + '\',\'' + data[i].name + ' - 日志\')">' + data[i].name + '</span>' + "</td>");
                            tr += ("<td>" + data[i].nick + "</td>");
                            tr += ("<td>" + "<span class='button-style style-js'>"+ data[i].qx +"</span>" + "</td>");
                            tr += ("<td style='color:gray;'>" + (data[i].state == 1 ? "<span style='color:green;'>正常</span>" : "<span style='color:gray;'>冻结</span>") + "</td>");
                            tr += ("<td>" + data[i].login_time + "</td>");
                            tr += ("<td>" + data[i].create_time + "</td>");
                            tr += ('<td onclick="open_google_page(\'' + data[i].googleCodeSecret + '\',\'' + data[i].name + '\')">' + '<svg t="1694846942287" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="7387" width="19" height="19"><path d="M159 587.884c0 112.79 66.936 214.826 170.396 259.746l163.833 71.134a40 40 0 0 0 33.73-0.87l164.843-81.903C797.905 783.273 865 675.015 865 556.538V195.796c-52.55-4.556-104.182-10.416-154.895-17.582-65.758-9.292-131.636-35.853-197.568-79-63.96 42.81-130.287 69.34-198.642 79-52.516 7.421-104.148 13.318-154.895 17.69v391.98z m-32.263-449.565c58.33-4.412 117.918-10.917 178.763-19.515 62.633-8.85 123.933-34.342 183.901-76.473a40 40 0 0 1 45.563-0.296c62.52 42.724 123.7 68.314 183.536 76.77 58.33 8.242 117.937 14.723 178.819 19.442C912.94 139.46 925 152.488 925 168.157v388.381c0 141.258-79.996 270.332-206.5 333.186l-164.844 81.904a100 100 0 0 1-84.322 2.172L305.5 902.666C180.119 848.228 99 724.573 99 587.884v-419.65c0-15.69 12.091-28.731 27.737-29.915z" fill="#2F54EB" p-id="7388"></path><path d="M441.399 593.511l237.865-237.287c13.673-13.64 35.83-13.631 49.491 0.02 13.667 13.655 13.659 35.789-0.019 49.433L466 667.776c-13.718 13.685-35.964 13.624-49.607-0.136L295.129 545.34c-13.603-13.72-13.49-35.853 0.251-49.433 13.737-13.575 35.893-13.463 49.491 0.252L441.4 593.51z" fill="#85A5FF" p-id="7389" ></path></svg>' + "</td>");


                            var modify_model = [];
                            modify_model.push({ name: '姓名', id: 'username', value: data[i].name });
                            modify_model.push({ name: '账号', id: 'usernick', value: data[i].nick });
                            modify_model.push({ name: '密码', id: 'password', value: data[i].pwd });
                            modify_model.push({ name: '状态', id: 'state', value: data[i].state, type: 'option', data: [['1', '正常'], ['0', '停用']] });
                            modify_model.push({ name: '权限', id: 'roleid', value: data[i].roleid, type: 'select', data: select_qx });



                            tr += ("<td class='action-cell '>"
                                + ' <div class="card-toolbar">'
                                + '<a class="card-button common-button" ' + "onclick='edit(" + edtext({ aname: 'admin', title: _actionName + "修改", action: _action, id: data[i].id, data: modify_model }) + ")'" + '><svg  class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg"  width="16" height="16" style="position:relative;top:3px;"><path d="M358.165868 554.624796c-0.533143 0.680499-1.066285 1.391696-1.303692 2.251274l-41.104163 150.700257c-2.400676 8.772804 0.059352 18.226107 6.550183 24.892947 4.860704 4.742001 11.261485 7.350408 18.077727 7.350408 2.252297 0 4.504594-0.267083 6.727215-0.860601l149.630902-40.808428c0.23843 0 0.357134 0.207731 0.534166 0.207731 1.718131 0 3.408633-0.62217 4.683672-1.927909l400.113747-400.054395c11.883655-11.897981 18.404162-28.109198 18.404162-45.74281 0-19.989263-8.476045-39.963177-23.324218-54.767348l-37.786605-37.844933c-14.81645-14.848173-34.822087-23.338544-54.797024-23.338544-17.631566 0-33.842783 6.520507-45.75816 18.388812L358.758362 553.232077C358.344946 553.615816 358.462626 554.179658 358.165868 554.624796M862.924953 257.19778l-39.742143 39.71349-64.428382-65.451688 39.180348-39.179324c6.193049-6.222725 18.194384-5.318122 25.308409 1.822508l37.813211 37.845956c3.943822 3.941775 6.195096 9.18622 6.195096 14.372336C867.223862 250.574942 865.710392 254.42769 862.924953 257.19778M429.322487 560.907896l288.712541-288.728914 64.459081 65.49569L494.314711 625.838721 429.322487 560.907896zM376.718409 677.970032l20.863167-76.580143 55.656601 55.657624L376.718409 677.970032z" fill="#e6e6e6" p-id="1994"></path><path d="M888.265084 415.735539c-15.144932 0-27.562752 12.313443-27.620058 27.665083l0 372.98283c0 19.559475-15.885805 35.444257-35.475979 35.444257L194.220958 851.827709c-19.559475 0-35.504632-15.883759-35.504632-35.444257L158.716326 207.602222c0-19.575848 15.945157-35.474956 35.504632-35.474956l406.367171 0c15.231913 0 27.592428-12.371772 27.592428-27.606755 0-15.202237-12.360516-27.590382-27.592428-27.590382L190.013123 116.930129c-47.684022 0-86.49291 38.779212-86.49291 86.49291L103.520213 820.59231c0 47.713698 38.808888 86.47756 86.49291 86.47756l639.334083 0c47.715745 0 86.509283-38.763862 86.509283-86.47756L915.856489 443.222567C915.794068 428.048983 903.408993 415.735539 888.265084 415.735539" fill="#e6e6e6" p-id="1995"></path></svg>&nbsp;编辑</a>'
                                + '&nbsp;<a class="card-button common-button" ' + "onclick=\"event_create({'aname':'admin','name':'" + _action + "','event':'delete','id':" + data[i].id + "})\"" + '><svg t="1688237896673" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="1699" width="16" height="16" style="position:relative;top:3px;"><path d="M612.693333 170.666667l8.533334 42.666666H402.773333l8.533334-42.666666h201.386666z m-293.632 25.941333a86.186667 86.186667 0 0 0-1.706666 16.725333H170.666667a42.666667 42.666667 0 0 0 0 85.333334h682.666666a42.666667 42.666667 0 1 0 0-85.333334h-146.730666c0-5.504-0.554667-11.093333-1.706667-16.725333l-8.533333-42.666667A85.333333 85.333333 0 0 0 612.778667 85.333333H411.306667a85.333333 85.333333 0 0 0-83.712 68.608l-8.533334 42.666667zM426.666667 469.333333a42.666667 42.666667 0 0 0-42.666667 42.666667v170.666667a42.666667 42.666667 0 1 0 85.333333 0v-170.666667a42.666667 42.666667 0 0 0-42.666666-42.666667z m128 42.666667a42.666667 42.666667 0 1 1 85.333333 0v170.666667a42.666667 42.666667 0 1 1-85.333333 0v-170.666667zM255.402667 384A42.666667 42.666667 0 0 1 298.666667 426.069333L301.738667 640H301.653333a42.666667 42.666667 0 1 1-85.290666-2.176l-2.986667-210.56A42.666667 42.666667 0 0 1 255.36 384z m51.925333 396.586667h0.042667A103.381333 103.381333 0 0 0 406.698667 853.333333h210.602666c54.186667 0 98.56-40.789333 103.210667-92.757333L725.333333 426.069333a42.666667 42.666667 0 0 1 85.333334 1.194667l-4.864 337.365333-0.128 1.365334C798.08 863.872 715.562667 938.666667 617.301333 938.666667H406.698667a188.885333 188.885333 0 0 1-180.096-130.389334 42.666667 42.666667 0 0 1 80.725333-27.648z" fill="#14101C" p-id="1700"></path></svg>&nbsp;删除</a>'
                                //+ '<div class="card-popup">'

                                //+ "<div class='menu-item px-3'><a onclick='edit(" + edtext({ aname: 'admin', title: _actionName + "修改", action: _action, id: data[i].id, data: modify_model }) + ")' class='menu-link px-3'>编辑</a></div>"

                                //+ "<div class='menu-item px-3'><a onclick=\"event_create({'aname':'admin','name':'" + _action + "','event':'delete','id':" + data[i].id + "})\" class='menu-link px-3'>删除</a></div>"

                                //+ '</div>'
                                + '</div>');



                            tr += "</tr>";
                            $("#list-display").append(tr)
                        }
                        $(".page_size").html(pager_size);
                        $(".page_count").html(json.data.count);
                        $(".cpages").html(json.data.html);
                    } else {

                    }
                },
                error: function () {
                }
            });
        }
        getPager();

        function _pt(i) {
            pager_index = i;
            getPager();
        }
    </script>

    <script>
        var open_google_page = function (googleCodeSecret, name) {
            _modal('谷歌验证码', '<div style="text-align: center; display: flex; flex-direction: column;"><div id="google_code" style="margin:0 auto;"></div> </div>')

            var qrcode = new QRCode(document.getElementById("google_code"), {
                text: 'otpauth://totp/QD验证码:' + name + '?secret=' + googleCodeSecret + '&issuer=QD验证码',
                width: 300,
                height: 300,
                colorDark: "#000000",
                colorLight: "#ffffff",
                margin: 5,
                correctLevel: QRCode.CorrectLevel.Q
            });
        }
    </script>


    <script>
        layui.use(['form', 'layedit', 'laydate'], function () {
        });
    </script>
</asp:Content>


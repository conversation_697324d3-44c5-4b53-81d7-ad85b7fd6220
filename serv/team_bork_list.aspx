<%@ Page Title="团队佣金查询" Language="C#" MasterPageFile="~/serv/m.master" AutoEventWireup="true" CodeFile="team_bork_list.aspx.cs" Inherits="w_index" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="Server">
    <link href="../css/element-ui.css" rel="stylesheet" />
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="Server">
    <div class="ibox float-e-margins">
        <div class="ibox-title">
            <h5>团队佣金查询</h5>
        </div>
        <div class="ibox-content">

            

            <style>
                .switch-1 {
                    padding: 7px 12px;
                    border-radius: 3px;
                    font-weight: bold;
                    display: flex;
                    align-items: center;
                    margin-bottom: 10px;
                    margin-right: 10px;
                    cursor: pointer;
                    background: #eee;
                    color: #5a5b5c;
                    border: 1px solid #ddd;
                }

                    .switch-1.active {
                        background: linear-gradient(119deg, #6799ed, #67bf8b);
                        color: #fff;
                        border: 1px solid transparent;
                    }
            </style>

            <div style="display: flex;">

                <div class="switch-1" from_type="filter_th">
                    <svg t="" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="6932" width="23" height="23"  style="margin-right: 5px;"><path d="M810.0864 210.7904H261.6832a56.32 56.32 0 0 0-35.4816 99.9424l204.8 166.5536a10.5472 10.5472 0 0 1 3.8912 8.192V803.84a36.4032 36.4032 0 0 0 18.2784 31.5392l113.5104 64.8704a47.0016 47.0016 0 0 0 70.3488-40.96v-373.76a10.5472 10.5472 0 0 1 3.8912-8.192l204.8-166.5536a56.32 56.32 0 0 0-35.6352-99.9936z" fill="#9FA7FF" p-id="6933"></path><path d="M845.6192 310.7328a56.832 56.832 0 0 0 15.7696-20.0192 51.2 51.2 0 0 0-47.3088-30.0544H303.8208A52.5312 52.5312 0 0 0 256 335.104l174.9504 142.1824a10.5472 10.5472 0 0 1 3.8912 8.192v1.6384l26.4192 21.4528a9.9328 9.9328 0 0 1 3.6352 7.6288v294.7584a33.9456 33.9456 0 0 0 16.9984 29.3888l105.6256 60.3648a45.4656 45.4656 0 0 0 12.1344 4.7104 46.8992 46.8992 0 0 0 37.3248-46.08v-373.76a10.5472 10.5472 0 0 1 3.8912-8.192z" fill="#8891FF" p-id="6934"></path><path d="M818.432 316.2112H350.72A48.0768 48.0768 0 0 0 303.5136 373.76l127.4368 103.5264a10.5472 10.5472 0 0 1 3.8912 8.192v9.0112l60.2112 48.9984a9.0624 9.0624 0 0 1 3.328 7.0144V819.2a30.72 30.72 0 0 0 15.36 26.88L610.7648 901.12h0.3584a46.5408 46.5408 0 0 0 25.6-41.8816v-373.76a10.5472 10.5472 0 0 1 3.8912-8.192l194.56-158.1056a47.9744 47.9744 0 0 0-16.7424-2.9696z" fill="#6E75FF" p-id="6935"></path><path d="M559.9744 937.3184a82.432 82.432 0 0 1-40.96-11.0592l-125.8496-71.68a71.1168 71.1168 0 0 1-35.84-61.44V446.6176l-220.16-178.8928A93.0816 93.0816 0 0 1 196.096 102.4H803.84a93.0816 93.0816 0 0 1 58.88 165.3248l-219.904 178.8928v407.7568a81.92 81.92 0 0 1-41.2672 71.68 82.8416 82.8416 0 0 1-41.5744 11.264zM196.096 163.84a31.6416 31.6416 0 0 0-19.968 56.32l226.9696 184.32a42.2912 42.2912 0 0 1 15.6672 32.9216v355.072a9.5744 9.5744 0 0 0 4.8128 8.2944l125.7984 71.936a21.3504 21.3504 0 0 0 32-18.5344V437.6064a42.1376 42.1376 0 0 1 15.6672-33.1264l226.9184-184.32A31.6416 31.6416 0 0 0 803.84 163.84z" fill="#2E3138" p-id="6936"></path><path d="M889.7536 527.7696h-175.7696a30.72 30.72 0 0 1 0-61.44h175.7696a30.72 30.72 0 0 1 0 61.44zM889.7536 652.2368h-175.7696a30.72 30.72 0 0 1 0-61.44h175.7696a30.72 30.72 0 0 1 0 61.44zM889.7536 773.6832h-175.7696a30.72 30.72 0 0 1 0-61.44h175.7696a30.72 30.72 0 0 1 0 61.44z" fill="#2E3138" p-id="6937"></path></svg>排除托号
                </div>

            </div>
            <input id="from_type" type="hidden" />
            <script>
                $('.switch-1').on('click', function () {
                    from_type = $(this).attr('from_type');
                    if ($(this).hasClass("active")) {
                        $(this).removeClass("active");
                        from_type = "";
                    } else {
                        $(this).addClass("active").siblings().removeClass("active");
                    }
                    $('#from_type').val(from_type);
                    searchList();
                });
            </script>



            <div class="row layui-form">


                <%--<div class="col-md-2">
                    <select id="typeid">
                        <option value="">全部</option>
                        <option value="用户领取">用户领取</option>
                        <option value="佣金发放">佣金发放</option>
                    </select>
                </div>--%>


                <div class="col-md-4">
                    <div class="form-group form-buttom">
                        <input type="text" id="search-keyword" name="ordername" class="form-control" placeholder="请输入搜索的信息">
                    </div>
                </div>

                <div class="col-md-4">
                    <div class="form-group form-buttom" id="twoInputs">
                        <input type="text" id="start_time" name="ordername" class="form-control" placeholder="开始时间" style="width: 48%; display: inline-block;" value="" autocomplete="off">
                        <input type="text" id="end_time" name="ordername" class="form-control" placeholder="结束时间" style="width: 48%; display: inline-block;" autocomplete="off" value="">

                        <script>
                            $(document).ready(function () {
                                $('#start_time').datepicker({
                                    format: 'yyyy-mm-dd',
                                    autoclose: true
                                });

                                $('#end_time').datepicker({
                                    format: 'yyyy-mm-dd',
                                    autoclose: true
                                });
                            });
                        </script>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-group form-buttom">

                        <a class="btn btn-success" onclick="searchList()">
                            <svg t="1688237921441" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="1949" width="16" height="16">
                                <path d="M361.130667 658.688a256 256 0 1 1 282.794666-44.672 42.496 42.496 0 0 0-10.368 14.933333c-0.853333 0.682667-1.621333 1.450667-2.389333 2.218667a42.666667 42.666667 0 0 0 0 60.330667l170.666667 170.666666a42.666667 42.666667 0 0 0 60.330666-60.330666l-142.762666-142.762667a341.333333 341.333333 0 1 0-169.344 99.242667 42.666667 42.666667 0 0 0-20.224-82.901334 254.762667 254.762667 0 0 1-168.704-16.725333z" fill="#14101C" p-id="1950"></path></svg>
                            查询</a>
                        <a class="btn btn-success" onclick="javascript:location.href=location.href;">
                            <svg t="1688237949108" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2211" width="16" height="16">
                                <path d="M512 192a298.24 298.24 0 0 0-245.162667 128 42.666667 42.666667 0 1 1-69.973333-48.810667 384.042667 384.042667 0 0 1 697.173333 180.48l36.608-34.645333a42.666667 42.666667 0 0 1 58.709334 61.952l-112.64 106.666667a42.666667 42.666667 0 0 1-71.253334-38.912A298.666667 298.666667 0 0 0 512 192z m0.170667 640a298.24 298.24 0 0 0 245.162666-128 42.666667 42.666667 0 1 1 69.973334 48.810667 384.042667 384.042667 0 0 1-697.173334-180.48l-36.650666 34.645333a42.666667 42.666667 0 0 1-58.666667-61.952l112.64-106.666667a42.666667 42.666667 0 0 1 71.253333 38.912 298.666667 298.666667 0 0 0 293.418667 354.730667z" fill="#14101C" p-id="2212"></path></svg>
                            刷新</a>


                        <a class="btn btn-success" onclick="report_exl()">
                            <svg t="1695489540076" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="5479" width="16" height="16">
                                <path d="M686.08 989.866667H165.546667c-51.2 0-92.16-40.96-92.16-92.16V126.293333c0-51.2 40.96-92.16 92.16-92.16h573.44c6.826667 0 11.946667 1.706667 17.066666 6.826667l187.733334 174.08c5.12 5.12 8.533333 11.946667 8.533333 18.773333v256c0 13.653333-11.946667 25.6-25.6 25.6s-25.6-11.946667-25.6-25.6V244.053333L728.746667 85.333333h-563.2C141.653333 85.333333 122.88 104.106667 122.88 126.293333v769.706667c0 22.186667 18.773333 40.96 40.96 40.96h520.533333c13.653333 0 25.6 11.946667 25.6 25.6S699.733333 989.866667 686.08 989.866667z" fill="#333333" p-id="5480"></path><path d="M919.893333 259.413333h-121.173333c-51.2 0-92.16-40.96-92.16-92.16V59.733333c0-13.653333 11.946667-25.6 25.6-25.6s25.6 11.946667 25.6 25.6v105.813334c0 22.186667 18.773333 40.96 40.96 40.96h121.173333c13.653333 0 25.6 11.946667 25.6 25.6s-11.946667 27.306667-25.6 27.306666zM500.053333 305.493333H208.213333c-13.653333 0-25.6-11.946667-25.6-25.6s11.946667-25.6 25.6-25.6h291.84c13.653333 0 25.6 11.946667 25.6 25.6s-11.946667 25.6-25.6 25.6zM616.106667 575.146667H208.213333c-13.653333 0-25.6-11.946667-25.6-25.6s11.946667-25.6 25.6-25.6h407.893334c13.653333 0 25.6 11.946667 25.6 25.6s-11.946667 25.6-25.6 25.6zM380.586667 440.32H208.213333c-13.653333 0-25.6-11.946667-25.6-25.6s11.946667-25.6 25.6-25.6h172.373334c13.653333 0 25.6 11.946667 25.6 25.6s-11.946667 25.6-25.6 25.6z" fill="#333333" p-id="5481"></path><path d="M914.773333 779.946667H600.746667c-13.653333 0-25.6-11.946667-25.6-25.6s11.946667-25.6 25.6-25.6h314.026666c13.653333 0 25.6 11.946667 25.6 25.6s-11.946667 25.6-25.6 25.6z" fill="#333333" p-id="5482"></path><path d="M831.146667 873.813333c-6.826667 0-13.653333-1.706667-18.773334-6.826666-10.24-10.24-10.24-25.6 0-35.84l75.093334-75.093334-75.093334-75.093333c-10.24-10.24-10.24-25.6 0-35.84 10.24-10.24 25.6-10.24 35.84 0l93.866667 93.866667c5.12 5.12 6.826667 11.946667 6.826667 18.773333s-3.413333 13.653333-6.826667 18.773333L848.213333 870.4c-5.12 1.706667-11.946667 3.413333-17.066666 3.413333z" fill="#333333" p-id="5483"></path><path d="M694.613333 824.32h-529.066666c-37.546667 0-68.266667-30.72-68.266667-68.266667V121.173333c0-37.546667 30.72-68.266667 68.266667-68.266666h529.066666c37.546667 0 68.266667 30.72 68.266667 68.266666v634.88c0 37.546667-30.72 68.266667-68.266667 68.266667z" fill="#333333" opacity=".3" p-id="5484"></path></svg>导出</a>

                    </div>
                </div>
            </div>

            <div style="display: flex; flex-wrap: wrap; margin-bottom: 20px;">
                <div class="timesel_lab active">全部</div>
                <div class="timesel_lab">今天</div>
                <div class="timesel_lab">昨天</div>
                <div class="timesel_lab">本周</div>
                <div class="timesel_lab">上周</div>
                <div class="timesel_lab">本月</div>
                <div class="timesel_lab">上月</div>
            </div>

            
            <div style="display: flex; margin-bottom: 10px;">


                <div style="background: #fff; padding: 17px; border-radius: 8px; width: 33.333%; margin: 0 10px; max-width: 250px;">
                    <div style="display: flex; align-items: center;">
                        <svg t="" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="7153" width="20" height="20">
                            <path d="M513.0752 510.8224m-450.816 0a450.816 450.816 0 1 0 901.632 0 450.816 450.816 0 1 0-901.632 0Z" fill="#FFF7DC" p-id="7154"></path><path d="M863.0784 503.7056c0-52.7872-56.3712-67.0208-86.016-77.1072-29.6448-10.0864-176.0768-64.2048-176.0768-64.2048s-100.1472-163.328-190.0032-101.888c-21.8112 13.7728-18.2272 63.6416 12.032 84.5312-41.8304-7.5776-110.7968 7.9872-107.264 47.616 3.584 39.6288 58.3168 60.0576 155.2896 43.6224 16.896-3.584 40.4992 8.9088 12.032 32.9216-15.1552 10.7008-80.9984 28.928-225.1776 3.1232-102.6048-18.3808-162.816-32.2048-189.0304-38.7072a451.6864 451.6864 0 0 0-6.6048 77.2608c0 149.504 72.8064 282.0096 184.8832 364.032 22.016-6.5536 45.0048-15.104 68.608-26.112 60.5184-28.4672 42.7008-57.856 28.4672-68.096s-42.7008-50.7392 36.9152-98.3552 178.7392-90.1632 296.8064-105.0112c37.9904-5.3248 83.6608-5.9392 105.6256-7.1168 21.9648-1.2288 79.5136-13.6704 79.5136-66.5088z m-260.5056-41.8304c-15.872 0-28.7744-12.9024-28.7744-28.7744s12.9024-28.7744 28.7744-28.7744 28.7744 12.9024 28.7744 28.7744-12.8512 28.7744-28.7744 28.7744z" fill="#FF8E12" p-id="7155"></path><path d="M600.9856 362.3424s-100.1472-163.328-190.0032-101.888c-21.8112 13.7728-18.2272 63.6416 12.032 84.5312-41.8304-7.5776-110.7968 7.9872-107.264 47.616 3.584 39.6288 58.3168 60.0576 155.2896 43.6224 16.896-3.584 40.4992 8.9088 12.032 32.9216-15.1552 10.7008-80.9984 28.928-225.1776 3.1232-102.6048-18.3808-162.816-32.2048-189.0304-38.7072a451.6864 451.6864 0 0 0-6.6048 77.2608c0 104.3456 35.5328 200.3968 95.0784 276.8384a426.51136 426.51136 0 0 0 163.84 32.5632c11.1616 0 22.1696-0.5632 33.1264-1.3824 9.6768-17.1008-0.9216-31.5904-10.0864-38.1952-14.2336-10.24-42.7008-50.7392 36.9152-98.3552s178.7392-90.1632 296.8064-105.0112c9.8304-1.3824 20.1728-2.4576 30.464-3.2768a425.0112 425.0112 0 0 0 39.68-157.696c-52.48-18.944-147.0976-53.9648-147.0976-53.9648z m1.5872 99.5328c-15.872 0-28.7744-12.9024-28.7744-28.7744s12.9024-28.7744 28.7744-28.7744 28.7744 12.9024 28.7744 28.7744-12.8512 28.7744-28.7744 28.7744z" fill="#FCA315" p-id="7156"></path><path d="M410.9312 260.4544C389.12 274.2272 392.704 324.096 422.9632 344.9856c-41.8304-7.5776-110.7968 7.9872-107.264 47.616 3.584 39.6288 58.3168 60.0576 155.2896 43.6224 16.896-3.584 40.4992 8.9088 12.032 32.9216-15.1552 10.7008-80.9984 28.928-225.1776 3.1232-102.6048-18.3808-162.816-32.2048-189.0304-38.7072a451.6864 451.6864 0 0 0-6.6048 77.2608c0 49.0496 7.8848 96.256 22.3744 140.4416a428.34944 428.34944 0 0 0 119.552 17.0496c160.8704 0 300.9536-88.8832 373.9648-220.16-2.7136-4.4032-4.3008-9.5232-4.3008-15.0528 0-14.7456 11.1104-26.88 25.3952-28.5696 5.12-12.3392 9.728-24.9344 13.6704-37.7856-7.424-2.7648-11.9296-4.4032-11.9296-4.4032s-100.096-163.2768-190.0032-101.888z" fill="#FCB138" p-id="7157"></path><path d="M257.8944 472.2688c-102.6048-18.3808-162.816-32.2048-189.0304-38.7072a451.6864 451.6864 0 0 0-6.6048 77.2608c0 1.5872 0.0512 3.1232 0.0512 4.6592 11.3152 0.9216 22.7328 1.4848 34.2528 1.4848 65.4848 0 127.5392-14.7968 183.04-41.1136-7.0656-1.024-14.2336-2.2528-21.7088-3.584zM422.9632 344.9856c-41.8304-7.5776-110.7968 7.9872-107.264 47.616 1.5872 17.92 13.7216 31.8976 35.4816 40.3456 61.184-45.4144 109.824-106.752 139.7248-177.8688-25.6512-11.264-53.248-12.8512-79.9744 5.376-21.76 13.824-18.2272 63.6416 12.032 84.5312z" fill="#FFC65E" p-id="7158"></path></svg>
                        <span style="color: #fba722; font-weight: bold; margin-left: 8px;">佣金总额</span>
                    </div>

                    <div style="font-size: 22px; margin-top: 6px; font-weight: bold; color: #7e868f;">
                        <span id="bork_amount">0.00</span>
                    </div>
                </div>

                <div style="background: #fff; padding: 17px; border-radius: 8px; width: 33.333%; margin: 0 10px; max-width: 250px;">
                    <div style="display: flex; align-items: center;">
                        <svg t="" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="7975" width="20" height="20">
                            <path d="M509.2864 517.7856m-450.816 0a450.816 450.816 0 1 0 901.632 0 450.816 450.816 0 1 0-901.632 0Z" fill="#E9ECFF" p-id="7976"></path><path d="M450.9184 232.704m-86.1696 0a86.1696 86.1696 0 1 0 172.3392 0 86.1696 86.1696 0 1 0-172.3392 0Z" fill="#8486F8" p-id="7977"></path><path d="M700.0064 444.416m-72.6016 0a72.6016 72.6016 0 1 0 145.2032 0 72.6016 72.6016 0 1 0-145.2032 0Z" fill="#757BF2" p-id="7978"></path><path d="M317.2352 807.8336c-19.6608-94.3104-33.792-317.0304-9.9328-382.6176-13.2608 27.8528-42.752 118.0672-56.3712 152.1152-7.9872 20.0192-40.9088 13.1584-44.1856-8.1408-7.3216-47.7696-10.5984-149.8112 93.6448-269.5168 16.6912-21.2992 48.7424-15.2576 67.8912 9.5744s45.056 49.3056 107.4176 37.9392c19.5072-4.2496 43.9808-16.6912 64.2048 38.656s49.664 142.5408 137.5744 160.9728c21.248 1.9456 71.68-4.864 119.1936-33.4336 8.6528-5.2224 19.712-4.1984 27.0336 2.7648l0.4096 0.3584c8.448 8.0384 9.1136 21.0944 1.792 30.1568-12.8 15.8208-36.4032 35.328-65.4336 52.5824-46.1312 27.392-49.664 43.264-29.7984 97.1776 19.8656 53.9136 60.2624 92.928 35.4816 114.176-24.832 21.2992-65.2288 5.6832-121.2928-114.176-2.1504 54.6304 16.4352 127.1296-17.4592 133.5296-33.9456 6.4-69.0176-34.56-70.0928-157.952-1.0752-28.7232 5.3248-79.7696 5.3248-79.7696s-62.7712-75.52-72.3456-130.8672c-11.2128 31.2832-37.9904 195.4816-43.4176 335.104-1.3312 33.7408-27.9552 60.9792-61.696 62.5664l-1.4848 0.0512c-31.744 1.5872-59.9552-20.1216-66.4576-51.2512z" fill="#6D6DF2" p-id="7979"></path><path d="M677.4784 546.8672c-87.9616-18.432-117.3504-105.6768-137.5744-160.9728-20.224-55.296-44.6976-42.9056-64.2048-38.656-62.4128 11.3664-88.2688-13.1072-107.4176-37.9392-19.1488-24.832-51.2512-30.8736-67.8912-9.5744-104.2432 119.6544-100.9152 221.696-93.6448 269.5168 3.2768 21.2992 36.1472 28.2112 44.1856 8.1408 13.6192-34.048 43.1104-124.2624 56.3712-152.1152-23.8592 65.536-9.728 288.3072 9.9328 382.6176 1.9456 9.4208 5.9392 17.92 11.3664 25.2416h0.9216c38.8096 0 76.4416-4.9152 112.384-14.1312 2.8672-6.9632 4.608-14.4896 4.9152-22.4256 5.4272-139.6224 32.2048-303.8208 43.4176-335.104 9.5744 55.296 72.3456 130.8672 72.3456 130.8672s-6.4 51.0464-5.3248 79.7696c0.3584 38.912 4.0448 69.632 10.0864 93.2352 28.0064-17.408 53.9136-37.7856 77.3632-60.7232 0-2.7136 0.0512-5.4272 0.1536-8.0384 0.8704 1.8432 1.6896 3.584 2.56 5.376a450.816 450.816 0 0 0 106.9056-168.448c-32.5632 11.7248-61.9008 14.7456-76.8512 13.3632z" fill="#757BF2" p-id="7980"></path><path d="M539.904 385.8944c-20.224-55.296-44.6976-42.9056-64.2048-38.656-62.4128 11.3664-88.2688-13.1072-107.4176-37.9392-19.1488-24.832-51.2512-30.8736-67.8912-9.5744-104.2432 119.6544-100.9152 221.696-93.6448 269.5168 3.2768 21.2992 36.1472 28.2112 44.1856 8.1408 13.6192-34.048 43.1104-124.2624 56.3712-152.1152-14.9504 41.0624-14.9504 143.872-8.0896 238.1312a448.90624 448.90624 0 0 0 166.8608-72.7552c8.8576-63.488 18.5344-113.3568 24.1664-129.1776 4.352 25.1904 19.712 54.528 35.2256 79.0528 20.0704-20.1216 38.2464-42.1376 54.272-65.792-18.7392-29.696-30.3104-62.7712-39.8336-88.832z" fill="#8486F8" p-id="7981"></path><path d="M450.9184 146.5344c-47.5648 0-86.1696 38.5536-86.1696 86.1696s38.5536 86.1696 86.1696 86.1696c4.6592 0 9.216-0.4608 13.6704-1.1776 26.624-38.7584 47.36-81.8688 60.8768-128.0512-14.8992-25.7536-42.6496-43.1104-74.5472-43.1104zM440.32 349.8496c-37.7344-1.8944-56.9856-21.0944-72.0384-40.5504-19.1488-24.832-51.2512-30.8736-67.8912-9.5744-67.2256 77.1584-89.7024 146.944-95.232 199.6288 29.696-7.6288 58.2144-18.176 85.248-31.3856 6.6048-18.0736 12.6464-33.6896 16.9472-42.7008-3.5328 9.6256-6.1952 22.784-8.1408 38.2976 54.3744-28.0576 102.4-66.9184 141.1072-113.7152z" fill="#8D92F8" p-id="7982"></path></svg>
                        <span style="color: #757bf2; font-weight: bold; margin-left: 8px;">领取总额</span>
                    </div>

                    <div style="font-size: 22px; margin-top: 6px; font-weight: bold; color: #2a2b2c;">
                        <span id="rec_bork_amount">0.00</span>
                    </div>
                </div>


            </div>


            <div class="table-responsive">
                <table class="table">
                    <thead>
                        <tr role="head">
                            <th sort="true">用户</th>
                            <th sort="true">佣金总额</th>
                            <th sort="true">领取总额</th>
                        </tr>
                    </thead>
                    <tbody id="list-display">
                    </tbody>
                </table>
            </div>



            <div class="layui-table-page">
                <div id="layui-table-page1">
                    <div class="layui-box layui-laypage layui-laypage-default" id="layui-laypage-1">
                        <div style="display: inline-block;" class="cpages">
                            <!--page grow -->
                        </div>
                        <span class="layui-laypage-skip">到第<input type="text" min="1" value="1" class="layui-input target-page" />页
                            <button type="button" class="layui-laypage-btn" onclick="javascript:pager_index=$('.target-page').val()-1;getPager();">确定</button></span>
                        <span class="layui-laypage-count">共 <span class="page_count"></span>条</span>
                        <span class="layui-laypage-limits">
                            <select class="s_pages" lay-ignore="" onchange="javascript:pager_size=$(this).val();getPager();">
                                <option value="10">10 条/页</option>
                                <option value="20" selected="">20 条/页</option>
                                <option value="30">30 条/页</option>
                                <option value="50">50 条/页</option>
                                <option value="100">100 条/页</option>
                                <option value="200">200 条/页</option>
                            </select></span>
                    </div>
                </div>
            </div>

        </div>
    </div>
    <script>
        var _action = 'team_bork';
    </script>
    <script>
        function openuser() {
            var cg = [];

            cg.push({ name: '黑名单', id: 'black_list', value: '', type: 'textarea' });

            edit({ aname: 'admin', title: '黑名单添加', action: _action, id: '', data: cg });
        }
    </script>
    <script>

        var pager_size = $(".s_pages").val();
        var pager_index = 0;
        var pager_key = "";

        var opt = get_param("opt");
        var opt2 = get_param("fid");

        $("#pagesize").val(pager_key);
        function setPageSize(obj) {
            pager_index = 0;
            pager_key = $("#search-keyword").val();
            opt = $(obj).val();
            getPager();
        }

        function searchList() {
            opt = $("#opt1").val();
            pager_index = 0;
            pager_key = $("#search-keyword").val();
            getPager();
        }

        function getParam() {
            return { limit: pager_size, p: pager_index, page: _action, keyword: pager_key, opt: opt, opt2: opt2, serv_id: get_param("serv_id"), start_time: $("#start_time").val(), end_time: $("#end_time").val(), state: $("#state").val(), uid: '<%=Request.QueryString["userid"] %>', type: $("#typeid").val(), from_type: $('#from_type').val() };
        }

        function getPager() {

            $.ajax({
                type: "POST",
                url: "../api/admin.aspx?do=page",
                data: getParam(),
                datatype: "json",
                success: function (json) {
                    $("#list-display").html("");
                    if (json.code == 1) {
                        var data = json.data.list;
                        for (var i = 0; i < data.length; i++) {
                            var tr = "";
                            tr += "<tr>";
                            tr += ("<td class=''>" + ('<span style="background: #f3ecec;color: #956b0a;font-size: 12px;border-radius: 3px;padding: 1px 4px;margin-right: 5px;font-weight: bold;border: 1px solid #ccc;">' + (data[i].typename == 'th' ? '托号' : data[i].typename == 'user' ? '普通' : data[i].typename == 'super' ? '高级' : data[i].typename == 'reg' ? '新' : '其他') + '</span>') + (data[i].group_name == '-' ? '' : "<span class='button-style style-js'>" + data[i].group_name + "</span>") + data[i].phone + '<span style="color: #b53d10;margin-left: 2px;">[' + data[i].userid + ']</span>' + "</td>");

                            tr += ("<td style=''>" + '<span style="color: #69af27;font-weight: bold;text-shadow: 5px 5px 5px #00800070;font-size: 18px;">' + data[i].total_amount.toFixed(2) + '</span>' + '<span style="font-size:12px;margin-left:8px;">[刷单：' + data[i].order_amount.toFixed(2) + ' 一键任务：' + data[i].onetouch_amount.toFixed(2) + ']</span>' + "</td>");
                            tr += ("<td style='" + (data[i].rec_amount.toFixed(2) == 0 ? 'color:gray;' : 'font-weight: bold;') + "'>" + data[i].rec_amount.toFixed(2) + "</td>");

                            //tr += ("<td>"
                            //    + "<button type='button' class='el-button  el-button--danger el-button--mini' onclick=\"event_create({'aname':'admin','name':'" + _action + "','event':'delete','id':" + data[i].id + "})\"><span>删除</span></button>"
                            //    + "</td>");
                            tr += "</tr>";
                            $("#list-display").append(tr)
                        }
                        $(".page_size").html(pager_size);
                        $(".page_count").html(json.data.count);
                        $(".cpages").html(json.data.html);


                        $('#bork_amount').html(json.bork_amount);
                        $('#rec_bork_amount').html(json.rec_bork_amount);
                    } else {

                    }
                },
                error: function () {
                }
            });
        }
        getPager();

        function _pt(i) {
            pager_index = i;
            getPager();
        }
    </script>

    <script>
        layui.use(['form', 'layedit', 'laydate'], function () {
        });


        function report_exl() {
            export_data(getParam());
        }


        function stringToColor(text) {
            var hash = 0;
            for (var i = 0; i < text.length; i++) {
                hash = text.charCodeAt(i) + ((hash << 99) - hash);
            }
            var color = "#" + ("000000" + ((hash & 0x00FFFFFF) | 0x990000).toString(16)).slice(-6);
            return color;
        }
    </script>
</asp:Content>


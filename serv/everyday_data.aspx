<%@ Page Title="每日数据查询" Language="C#" MasterPageFile="~/serv/m.master" AutoEventWireup="true" CodeFile="everyday_data.aspx.cs" Inherits="serv_index" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="Server">
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="Server">



    <div class="row layui-form" style="padding-top: 20px;">
        <div class="col-md-3">
            <div class="form-group form-buttom" id="twoInputs">
                <input type="text" id="start_time" name="ordername" class="form-control" placeholder="开始时间" style="width: 48%; display: inline-block;" value="<%=DateTime.Now.AddDays(-6).ToString("yyyy-MM-dd") %>" autocomplete="off">
                <input type="text" id="end_time" name="ordername" class="form-control" placeholder="结束时间" style="width: 48%; display: inline-block;" autocomplete="off" value="<%=DateTime.Now.ToString("yyyy-MM-dd") %>">

                <script>
                    $(document).ready(function () {
                        $('#start_time').datepicker({
                            format: 'yyyy-mm-dd',
                            autoclose: true
                        });

                        $('#end_time').datepicker({
                            format: 'yyyy-mm-dd',
                            autoclose: true
                        });
                    });
                </script>
            </div>
        </div>


        <div class="col-md-1">
            <div class="form-group form-buttom" style="position: relative;">
                <input type="text" id="roomNumber" class="form-control" placeholder="房间号" value="<%=Request.QueryString["roomNumber"] + "" %>">
            </div>
        </div>



        <div class="col-md-8">
            <div style="display: flex; flex-wrap: wrap;">

                <div class="timesel_lab">今天</div>
                <div class="timesel_lab">昨天</div>
                <div class="timesel_lab">本周</div>
                <div class="timesel_lab">上周</div>
                <div class="timesel_lab">本月</div>
                <div class="timesel_lab">上月</div>
                <div class="timesel_lab search" style="margin-left: 20px; background: #17B492; color: #fff;">查询</div>



                <div class="timer-wrap" style="position: relative; display: none;">

                    <div class="wrap">
                        <div class="wrap-l">
                            <div class="wrap-l-starTime">
                                <input name="startTime" type="text" placeholder="开始日期">
                            </div>
                        </div>
                        <div class="wrap-r">
                            <div class="wrap-l-endTime">
                                <input name="endTime" type="text" placeholder="结束日期">
                            </div>
                        </div>
                    </div>

                </div>


            </div>
        </div>

    </div>


    <div>

        <div class="table-responsive">
            <table class="table">
                <thead>
                    <tr role="head">
                        <th sort="true" class="">日期</th>
                        <th sort="true" class="accounts">总用户量</th>
                        <th sort="true" class="recharge_total">充值总量</th>
                        <th sort="true" class="cash_total">提现总量</th>
                        <th sort="true" class="recharge_dating">大厅买币总额</th>
                        <th sort="true" class="recharge_action">活动充值总额</th>
                        <th sort="true" class="recharge_sanfang">三方充值总额</th>
                        <th sort="true" class="recharge_admin">手动充值总额</th>
                        <th sort="true" class="user_cash_money">提现总额</th>
                        <th sort="true" class="sell_dating_total">卖币总量</th>
                        <th sort="true" class="yongjing_qd">佣金</th>
                        <th sort="true" class="yongjing_yj">一级</th>
                        <th sort="true" class="yongjing_ej">二级</th>
                        <th sort="true" class="yongjing_sj">三级</th>
                        <th sort="true" class="newuser_recharge_dating">新用户买币总额</th>
                        <th sort="true" class="newuser_cash_money">新用户提现总额</th>
                        <th sort="true" class="th_cash_money">托号提现总额</th>
                        <th sort="true" class="newuser_yongjing_qd">新用户佣金</th>
                        <th sort="true" class="newuser_yongjing_yj">新用户一级</th>
                        <th sort="true" class="newuser_yongjing_ej">新用户二级</th>
                        <th sort="true" class="newuser_yongjing_sj">新用户三级</th>
                        <th sort="true" class="game_recharge">游戏转入人数</th>
                        <th sort="true" class="game_withdraw">游戏转出人数</th>
                        <th sort="true" class="game_newuser">新增游戏人数</th>
                        <th sort="true" class="type_买币赠送">买币赠送</th>
                        <th sort="true" class="type_红包领取金额">红包领取金额</th>
                        <th sort="true" class="type_任务奖励">任务奖励</th>
                        <th sort="true" class="type_转盘奖励">转盘奖励</th>
                    </tr>
                </thead>
                <tbody id="list-display">
                </tbody>
            </table>
        </div>

    </div>





    <script>

        function isDecimal(str) {
            var number = Number(str);
            return !isNaN(number) && !Number.isInteger(number);
        }
        var get_totalData = function () {

            var start_time = $('#start_time').val();
            var end_time = $('#end_time').val();
            if (start_time != "") {
                start_time += " 00:00:00";
            }
            if (end_time != "") {
                end_time += " 23:59:59";
            }


            if (start_time == "" || end_time == "") {
                alert("请输入查询时间");
                return;
            }


            var data = {};
            data.group = 1;
            data.start_time = start_time;
            data.end_time = end_time;
            data.roomNumber = $('#roomNumber').val();
            data.refresh = get_param("refresh");

            $.ajax({
                type: "POST",
                url: "../api/admin.aspx?do=get_toatal_data",
                data: data,
                datatype: "json",
                success: function (json) {

                    $('#list-display').html('');


                    var array = getDates(start_time, end_time); +
                    array.reverse();
                    console.log('Dates', array);


                    var cols = [];
                    $('.table th').each(function () {
                        var c = $(this).attr("class");
                        if (c != "") {
                            cols.push(c);
                        }
                    })


                    for (var a = 0; a < array.length; a++) {
                        var tr = '';
                        tr += '<tr>';
                        tr += '<td>' + array[a] + '</td>';
                        for (var t = 0; t < cols.length; t++) {
                            tr += '<td typename="' + array[a] + "_" + cols[t] + '"><span style="color:#ddd;">0</span></td>';
                        }
                        tr += '</tr>';


                        //console.log('tr', tr);


                        $('#list-display').append(tr);
                    }




                    var recharge_total = {
                    }


                    var cash_total = {
                    }


                    for (var k in json.list) {
                        var objs = JSON.parse(json.list[k]);
                        console.log('objs', k, objs);

                        //try {

                        for (var i = 0; i < objs.length; i++) {
                            var date = objs[i].date;
                            var value = objs[i].total_amount;
                            value = Number(value);

                            if (isDecimal(value)) {
                                value = parseFloat(value).toFixed(2);
                            }


                            date = formatDate(date);
                            switch (k) {
                                case "accounts":
                                case "game_newuser":
                                    value = objs[i].total_number;
                                    break;
                                case "gift_total":
                                    var other_array = ["买币赠送", "红包领取金额", "任务奖励", "转盘奖励"];
                                    for (var oo = 0; oo < other_array.length; oo++) {
                                        var key = other_array[oo];
                                        value = parseFloat(objs[i][key]).toFixed(2);
                                        $('[typename="' + date + "_type_" + key + '"]').html('<strong>' + value + '</strong>');
                                    }
                                    continue;
                                    break;
                                default:
                                    break;

                            }


                            if (!recharge_total.hasOwnProperty(date)) {
                                // 如果不存在指定键，则进行初始化操作
                                recharge_total[date] = { total_amount: 0 };
                            }
                            if (!cash_total.hasOwnProperty(date)) {
                                // 如果不存在指定键，则进行初始化操作
                                cash_total[date] = { total_amount: 0 };
                            }


                            if (k == "recharge_dating" || k == "recharge_admin" || k == "recharge_sanfang") {
                                recharge_total[date]["total_amount"] += parseFloat(value);
                            }

                            if (k == "user_cash_money" || k == "deduct_admin" || k == "sell_dating_total") {
                                cash_total[date]["total_amount"] += parseFloat(value);
                            }

                            $('[typename="' + date + "_" + k + '"]').html('<strong>' + value + '</strong>');
                        }

                        //} catch (e) {

                        //}


                    }

                    
                    for (var k in recharge_total) {
                        $('[typename="' + k + "_recharge_total" + '"]').html('<strong>' + recharge_total[k].total_amount + '</strong>');
                    }

                    for (var k in cash_total) {
                        $('[typename="' + k + "_cash_total" + '"]').html('<strong>' + cash_total[k].total_amount + '</strong>');
                    }



                    console.log('recharge_total', recharge_total, cash_total);


                    //var show_last = json.start_time == "" && json.end_time == "";

                    //var recharge_total = {
                    //    today_amount: 0,
                    //    today_number: 0,
                    //    total_amount: 0,
                    //    total_number: 0,
                    //    yesterday_amount: 0,
                    //    yesterday_number: 0,
                    //    today_daily_number: 0,
                    //    total_daily_number: 0,
                    //    yesterday_daily_number: 0
                    //}


                    //var cash_total = {
                    //    today_amount: 0,
                    //    today_number: 0,
                    //    total_amount: 0,
                    //    total_number: 0,
                    //    yesterday_amount: 0,
                    //    yesterday_number: 0,
                    //    today_daily_number: 0,
                    //    total_daily_number: 0,
                    //    yesterday_daily_number: 0
                    //}

                    //json.list.yongjing_qd = add_data(json.list.yongjing_qd, json.list.recharge_yongjin);


                    //for (var k in json.list) {
                    //    //push_data(k, json.list[k], show_last);

                    //    //if (k == "recharge_dating" || k == "recharge_sanfang" || k == "recharge_admin" || k == "deduct_admin") {
                    //    if (k == "recharge_dating" || k == "recharge_admin" || k == "recharge_sanfang") {
                    //        for (var e in json.list[k]) {
                    //            var v = parseFloat(json.list[k][e]);
                    //            if (k == "deduct_admin") {
                    //                v = v * -1;
                    //            }
                    //            recharge_total[e] += v
                    //        }
                    //    }

                    //    if (k == "user_cash_money" || k == "deduct_admin" || k == "sell_dating_total") {
                    //        for (var e in json.list[k]) {
                    //            var v = parseFloat(json.list[k][e]);
                    //            cash_total[e] += v
                    //        }
                    //    }
                    //}

                    //recharge_total.total_number = json.list.recharge_total_number.total_number;
                    //recharge_total.today_number = json.list.recharge_total_number.today_number;
                    //recharge_total.yesterday_number = json.list.recharge_total_number.yesterday_number;

                    ////recharge_total.total_amount = recharge_total.total_amount.toFixed(2);

                    //push_data('recharge_total', recharge_total, show_last);
                    //push_data('cash_total', cash_total, show_last);

                    //var gift_total = {
                    //    total: 0,
                    //    today: 0,
                    //    yesterday: 0
                    //}
                    //var key = "";

                    //var array = ["买币赠送", "红包领取金额", "任务奖励", "转盘奖励", "出借利息"];

                    //for (var i = 0; i < array.length; i++) {
                    //    key = array[i];
                    //    $('[typename="' + key + '"]').find(".sbox_data").html(json.list.gift_total[key]);
                    //    $('[typename="' + key + '"]').find(".sbox_ty").eq(0).find(".sbox_today_value").html(json.list.gift_total['今日_' + key]);
                    //    $('[typename="' + key + '"]').find(".sbox_ty").eq(1).find(".sbox_today_value").html(json.list.gift_total['昨日_' + key]);


                    //    gift_total.total += parseFloat(json.list.gift_total[key]);
                    //    gift_total.today += parseFloat(json.list.gift_total['今日_' + key]);
                    //    gift_total.yesterday += parseFloat(json.list.gift_total['昨日_' + key]);
                    //}

                    //$('#gift_total').find(".sbox_today_value").html(parseFloat(gift_total.total).toFixed(2));
                    //$('#today_gift_total').find(".sbox_today_value").html(parseFloat(gift_total.today).toFixed(2));
                    //$('#yesterday_gift_total').find(".sbox_today_value").html(parseFloat(gift_total.yesterday).toFixed(2));

                },
                error: function () {
                }
            });

        }

        function getDates(start_time, end_time) {
            var dates = [];
            var startDate = new Date(start_time);
            var endDate = new Date(end_time);

            // 循环遍历起始日期和结束日期之间的每一天
            var currentDate = new Date(startDate);
            while (currentDate <= endDate) {
                // 获取当前日期的年、月、日
                var year = currentDate.getFullYear().toString().substr(-2); // 获取年份的后两位
                var month = (currentDate.getMonth() + 1).toString().padStart(2, '0'); // 月份补零
                var day = currentDate.getDate().toString().padStart(2, '0'); // 日补零

                // 将日期格式化为字符串并添加到数组中
                dates.push(year + '.' + month + '.' + day);

                // 将日期增加一天
                currentDate.setDate(currentDate.getDate() + 1);
            }

            return dates;
        }


        function formatDate(dateTimeString) {
            var date = new Date(dateTimeString);

            // 获取年、月、日
            var year = date.getFullYear().toString().substr(-2); // 取年份的后两位
            var month = (date.getMonth() + 1).toString().padStart(2, '0'); // 月份补零
            var day = date.getDate().toString().padStart(2, '0'); // 日补零

            // 格式化后的日期字符串
            var formattedDate = year + '.' + month + '.' + day;

            return formattedDate;
        }

    </script>

</asp:Content>


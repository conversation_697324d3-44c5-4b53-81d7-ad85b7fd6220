<%@ Page Title="授权域名列表" Language="C#" MasterPageFile="~/serv/m.master" AutoEventWireup="true" CodeFile="serverUrls.aspx.cs" Inherits="w_index" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="Server">
    <link href="../css/element-ui.css" rel="stylesheet" />
    <style>
        .el-tag.el-tag--test {
            background-color: #d5d5f5;
            border-color: #7d7dc7;
            color: #6d3333;
        }
    </style>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="Server">
    <div class="ibox float-e-margins">
        <div class="ibox-title">
            <h5>授权域名列表</h5>
        </div>
        <div class="ibox-content">
            <div class="row layui-form">
                <div class="col-md-6">
                    <div class="form-group form-buttom">
                        <input type="text" id="search-keyword" name="ordername" class="form-control" placeholder="请输入搜索的信息">
                    </div>
                </div>

                <div class="col-md-6">
                    <div class="form-group form-buttom">
                        <a class="btn btn-success" onclick="searchList()">
                            <svg t="1688237921441" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="1949" width="16" height="16">
                                <path d="M361.130667 658.688a256 256 0 1 1 282.794666-44.672 42.496 42.496 0 0 0-10.368 14.933333c-0.853333 0.682667-1.621333 1.450667-2.389333 2.218667a42.666667 42.666667 0 0 0 0 60.330667l170.666667 170.666666a42.666667 42.666667 0 0 0 60.330666-60.330666l-142.762666-142.762667a341.333333 341.333333 0 1 0-169.344 99.242667 42.666667 42.666667 0 0 0-20.224-82.901334 254.762667 254.762667 0 0 1-168.704-16.725333z" fill="#14101C" p-id="1950"></path></svg>
                            查询</a>
                        <a class="btn btn-success" onclick="javascript:location.href=location.href;">
                            <svg t="1688237949108" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2211" width="16" height="16">
                                <path d="M512 192a298.24 298.24 0 0 0-245.162667 128 42.666667 42.666667 0 1 1-69.973333-48.810667 384.042667 384.042667 0 0 1 697.173333 180.48l36.608-34.645333a42.666667 42.666667 0 0 1 58.709334 61.952l-112.64 106.666667a42.666667 42.666667 0 0 1-71.253334-38.912A298.666667 298.666667 0 0 0 512 192z m0.170667 640a298.24 298.24 0 0 0 245.162666-128 42.666667 42.666667 0 1 1 69.973334 48.810667 384.042667 384.042667 0 0 1-697.173334-180.48l-36.650666 34.645333a42.666667 42.666667 0 0 1-58.666667-61.952l112.64-106.666667a42.666667 42.666667 0 0 1 71.253333 38.912 298.666667 298.666667 0 0 0 293.418667 354.730667z" fill="#14101C" p-id="2212"></path></svg>
                            刷新</a>
                        <a class="btn btn-success" onclick="opennew()">
                            <svg t="1688237854283" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="15169" width="16" height="16">
                                <path d="M725.333333 213.333333H298.666667a85.333333 85.333333 0 0 0-85.333334 85.333334v341.333333a42.666667 42.666667 0 1 1-85.333333 0V298.666667a170.666667 170.666667 0 0 1 170.666667-170.666667h426.666666a170.709333 170.709333 0 0 1 167.850667 139.733333v488.533334A170.709333 170.709333 0 0 1 725.333333 896H298.666667a170.538667 170.538667 0 0 1-144.469334-79.744l1.194667-0.682667a42.666667 42.666667 0 0 1 76.501333-37.034666c15.658667 19.584 39.765333 32.128 66.773334 32.128h426.666666a85.333333 85.333333 0 0 0 85.333334-85.333334V298.666667a85.333333 85.333333 0 0 0-85.333334-85.333334z m-213.333333 512a42.666667 42.666667 0 0 1-42.666667-42.666666v-128H341.333333a42.666667 42.666667 0 1 1 0-85.333334h128V341.333333a42.666667 42.666667 0 1 1 85.333334 0v128h128a42.666667 42.666667 0 1 1 0 85.333334h-128v128a42.666667 42.666667 0 0 1-42.666667 42.666666z" fill="#14101C" p-id="15170"></path></svg>&nbsp;添加</a>
                        <%--<a class="btn btn-success" onclick="update_select(-9)">
                            <svg t="1688237896673" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="1699" width="16" height="16">
                                <path d="M612.693333 170.666667l8.533334 42.666666H402.773333l8.533334-42.666666h201.386666z m-293.632 25.941333a86.186667 86.186667 0 0 0-1.706666 16.725333H170.666667a42.666667 42.666667 0 0 0 0 85.333334h682.666666a42.666667 42.666667 0 1 0 0-85.333334h-146.730666c0-5.504-0.554667-11.093333-1.706667-16.725333l-8.533333-42.666667A85.333333 85.333333 0 0 0 612.778667 85.333333H411.306667a85.333333 85.333333 0 0 0-83.712 68.608l-8.533334 42.666667zM426.666667 469.333333a42.666667 42.666667 0 0 0-42.666667 42.666667v170.666667a42.666667 42.666667 0 1 0 85.333333 0v-170.666667a42.666667 42.666667 0 0 0-42.666666-42.666667z m128 42.666667a42.666667 42.666667 0 1 1 85.333333 0v170.666667a42.666667 42.666667 0 1 1-85.333333 0v-170.666667zM255.402667 384A42.666667 42.666667 0 0 1 298.666667 426.069333L301.738667 640H301.653333a42.666667 42.666667 0 1 1-85.290666-2.176l-2.986667-210.56A42.666667 42.666667 0 0 1 255.36 384z m51.925333 396.586667h0.042667A103.381333 103.381333 0 0 0 406.698667 853.333333h210.602666c54.186667 0 98.56-40.789333 103.210667-92.757333L725.333333 426.069333a42.666667 42.666667 0 0 1 85.333334 1.194667l-4.864 337.365333-0.128 1.365334C798.08 863.872 715.562667 938.666667 617.301333 938.666667H406.698667a188.885333 188.885333 0 0 1-180.096-130.389334 42.666667 42.666667 0 0 1 80.725333-27.648z" fill="#14101C" p-id="1700"></path></svg>删除</a>--%>
                    </div>
                </div>

            </div>

            <div class="table-responsive">
                <table class="table">
                    <thead>
                        <tr role="head">
                            <th sort="true">服务器名称</th>
                            <th sort="true">CDN域名</th>
                            <th sort="true">直连域名</th>
                            <th sort="true">泛解析域名</th>
                            <th sort="true">创建时间</th>
                            <th sort="true"></th>
                        </tr>
                    </thead>
                    <tbody id="list-display">
                    </tbody>
                </table>
            </div>


            <div class="layui-table-page">
                <div id="layui-table-page1">
                    <div class="layui-box layui-laypage layui-laypage-default" id="layui-laypage-1">
                        <div style="display: inline-block;" class="cpages">
                            <!--page grow -->
                        </div>
                        <span class="layui-laypage-skip">到第<input type="text" min="1" value="1" class="layui-input target-page" />页
                            <button type="button" class="layui-laypage-btn" onclick="javascript:pager_index=$('.target-page').val()-1;getPager();">确定</button></span>
                        <span class="layui-laypage-count">共 <span class="page_count"></span>条</span>
                        <span class="layui-laypage-limits">
                            <select class="s_pages" lay-ignore="" onchange="javascript:pager_size=$(this).val();getPager();">
                                <option value="10">10 条/页</option>
                                <option value="20" selected="">20 条/页</option>
                                <option value="30">30 条/页</option>
                                <option value="50">50 条/页</option>
                                <option value="100">100 条/页</option>
                                <option value="200">200 条/页</option>
                            </select></span>
                    </div>
                </div>
            </div>

        </div>
    </div>
    <script>
        var _action = 'serverUrls';
        var _actionName = '授权域名';
    </script>
    <script>
        function opennew() {
            var modify_model = [];

            modify_model.push({ name: '服务器名称', id: 'name', value: '' });
            modify_model.push({ name: 'CDN域名', id: 'cdnurls', value: '', type: 'textarea', height: 200 });
            modify_model.push({ name: '直连域名', id: 'directurls', value: '', type: 'textarea', height: 200 });
            modify_model.push({ name: '泛解析域名', id: 'floaturls', value: '', type: 'textarea', height: 200 });

            edit({ aname: 'admin', title: '添加' + _actionName, action: _action, id: '', data: modify_model });
        }
    </script>
    <script>

        var pager_size = 20;
        var pager_index = 0;
        var pager_key = "";


        $("#pagesize").val(pager_key);
        function setPageSize(obj) {
            pager_index = 0;
            pager_key = $("#search-keyword").val();
            opt = $(obj).val();
            getPager();
        }

        function searchList() {
            opt = $("#opt1").val();
            pager_index = 0;
            pager_key = $("#search-keyword").val();
            getPager();
        }

        function getPager() {

            $.ajax({
                type: "POST",
                url: "../api/admin.aspx?do=page",
                data: { limit: pager_size, p: pager_index, page: _action, keyword: pager_key },
                datatype: "json",
                success: function (json) {
                    $("#list-display").html("");
                    if (json.code == 1) {
                        var data = json.data.list;
                        for (var i = 0; i < data.length; i++) {
                            var tr = "";
                            tr += "<tr class='test1'>";
                            tr += ("<td class=''>" + data[i].name + "</b></td>");

                            

                            tr += ("<td class=''>" + getUrlData(data[i].cdnurls) + "</td>");
                            tr += ("<td class=''>" + getUrlData(data[i].directurls) + "</td>");
                            tr += ("<td class=''>" + getUrlData(data[i].floaturls) + "</td>");
                            tr += ("<td class=''>" + data[i].create_time + "</td>");

                            var modify_model = new Array();

                            modify_model.push({ name: '服务器名称', id: 'name', value: data[i].name });
                            modify_model.push({ name: 'CDN域名', id: 'cdnurls', value: data[i].cdnurls, type: 'textarea', height: 200 });
                            modify_model.push({ name: '直连域名', id: 'directurls', value: data[i].directurls, type: 'textarea', height: 200 });
                            modify_model.push({ name: '泛解析域名', id: 'floaturls', value: data[i].floaturls, type: 'textarea', height: 200 });

                            tr += ("<td class='action-cell '>"
                                + ' <div class="card-toolbar">'
                                + '<a class="card-button common-button" ' + "onclick='edit(" + edtext({ aname: 'admin', title: _actionName + "修改", action: _action, id: data[i].id, data: modify_model }) + ")'" + '><svg  class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg"  width="16" height="16" style="position:relative;top:3px;"><path d="M358.165868 554.624796c-0.533143 0.680499-1.066285 1.391696-1.303692 2.251274l-41.104163 150.700257c-2.400676 8.772804 0.059352 18.226107 6.550183 24.892947 4.860704 4.742001 11.261485 7.350408 18.077727 7.350408 2.252297 0 4.504594-0.267083 6.727215-0.860601l149.630902-40.808428c0.23843 0 0.357134 0.207731 0.534166 0.207731 1.718131 0 3.408633-0.62217 4.683672-1.927909l400.113747-400.054395c11.883655-11.897981 18.404162-28.109198 18.404162-45.74281 0-19.989263-8.476045-39.963177-23.324218-54.767348l-37.786605-37.844933c-14.81645-14.848173-34.822087-23.338544-54.797024-23.338544-17.631566 0-33.842783 6.520507-45.75816 18.388812L358.758362 553.232077C358.344946 553.615816 358.462626 554.179658 358.165868 554.624796M862.924953 257.19778l-39.742143 39.71349-64.428382-65.451688 39.180348-39.179324c6.193049-6.222725 18.194384-5.318122 25.308409 1.822508l37.813211 37.845956c3.943822 3.941775 6.195096 9.18622 6.195096 14.372336C867.223862 250.574942 865.710392 254.42769 862.924953 257.19778M429.322487 560.907896l288.712541-288.728914 64.459081 65.49569L494.314711 625.838721 429.322487 560.907896zM376.718409 677.970032l20.863167-76.580143 55.656601 55.657624L376.718409 677.970032z" fill="#e6e6e6" p-id="1994"></path><path d="M888.265084 415.735539c-15.144932 0-27.562752 12.313443-27.620058 27.665083l0 372.98283c0 19.559475-15.885805 35.444257-35.475979 35.444257L194.220958 851.827709c-19.559475 0-35.504632-15.883759-35.504632-35.444257L158.716326 207.602222c0-19.575848 15.945157-35.474956 35.504632-35.474956l406.367171 0c15.231913 0 27.592428-12.371772 27.592428-27.606755 0-15.202237-12.360516-27.590382-27.592428-27.590382L190.013123 116.930129c-47.684022 0-86.49291 38.779212-86.49291 86.49291L103.520213 820.59231c0 47.713698 38.808888 86.47756 86.49291 86.47756l639.334083 0c47.715745 0 86.509283-38.763862 86.509283-86.47756L915.856489 443.222567C915.794068 428.048983 903.408993 415.735539 888.265084 415.735539" fill="#e6e6e6" p-id="1995"></path></svg>&nbsp;编辑</a>'
                                + '&nbsp;<a class="card-button common-button" ' + "onclick=\"event_create({'aname':'admin','name':'" + _action + "','event':'delete','id':" + data[i].id + "})\"" + '><svg t="" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="1699" width="16" height="16" style="position:relative;top:3px;"><path d="M612.693333 170.666667l8.533334 42.666666H402.773333l8.533334-42.666666h201.386666z m-293.632 25.941333a86.186667 86.186667 0 0 0-1.706666 16.725333H170.666667a42.666667 42.666667 0 0 0 0 85.333334h682.666666a42.666667 42.666667 0 1 0 0-85.333334h-146.730666c0-5.504-0.554667-11.093333-1.706667-16.725333l-8.533333-42.666667A85.333333 85.333333 0 0 0 612.778667 85.333333H411.306667a85.333333 85.333333 0 0 0-83.712 68.608l-8.533334 42.666667zM426.666667 469.333333a42.666667 42.666667 0 0 0-42.666667 42.666667v170.666667a42.666667 42.666667 0 1 0 85.333333 0v-170.666667a42.666667 42.666667 0 0 0-42.666666-42.666667z m128 42.666667a42.666667 42.666667 0 1 1 85.333333 0v170.666667a42.666667 42.666667 0 1 1-85.333333 0v-170.666667zM255.402667 384A42.666667 42.666667 0 0 1 298.666667 426.069333L301.738667 640H301.653333a42.666667 42.666667 0 1 1-85.290666-2.176l-2.986667-210.56A42.666667 42.666667 0 0 1 255.36 384z m51.925333 396.586667h0.042667A103.381333 103.381333 0 0 0 406.698667 853.333333h210.602666c54.186667 0 98.56-40.789333 103.210667-92.757333L725.333333 426.069333a42.666667 42.666667 0 0 1 85.333334 1.194667l-4.864 337.365333-0.128 1.365334C798.08 863.872 715.562667 938.666667 617.301333 938.666667H406.698667a188.885333 188.885333 0 0 1-180.096-130.389334 42.666667 42.666667 0 0 1 80.725333-27.648z" fill="#14101C" p-id="1700"></path></svg>&nbsp;删除</a>'
                                + '</div></td>');


                            tr += "</tr>";
                            $("#list-display").append(tr)
                        }
                        $(".page_size").html(pager_size);
                        $(".page_count").html(json.data.count);
                        $(".cpages").html(json.data.html);
                    } else {

                    }
                },
                error: function () {
                }
            });
        }
        getPager();

        function _pt(i) {
            pager_index = i;
            getPager();
        }
    </script>

    <script>
        var getUrlData = function (urls) {
            var overnum = 0;
            var show_display = 'block';
            var __html = '';
            var uuid = getUuid();
            //console.log('urls', urls);
            var array = urls.split('\n');
            for (var t = 0; t < array.length; t++) {
                if (t >= 10) {
                    show_display = 'none';
                    overnum += 1;
                }
                __html += '<div uuid="' + uuid + '" style="display:' + show_display + '"><a style="color:#3d3db3;">' + array[t] + '</a></div>';
            }
            if (overnum > 0) {
                __html += "<div style='margin-top:6px;' onclick='showOverData(this,\"" + uuid + "\")'><a>查看更多（剩" + overnum + "条）</a></div>";
            }
            return __html;
        }
        var showOverData = function (obj, uuid) {
            $(obj).hide();
            $('[uuid="' + uuid + '"]').show();
        }
    </script>

    <script>
        layui.use(['form', 'layedit', 'laydate'], function () {
        });
    </script>

    <script>
        function update_select(state) {
            event_create({ 'aname': 'admin', 'name': _action, 'event': 'update', 'id': select_all_id(), 'state': state });
        }

        function _event(evt) {
            event_create({ 'aname': 'admin', 'name': _action, 'event': evt, 'id': select_all_id() });
        }

        //function report_exl() {
        //    var start_time = "";
        //    var end_time = "";
        //    var opt1 = req("id");
        //    var opt2 = $("#opt1").val();
        //    var checktime = $("#checktime").val();
        //    var g = checktime.split(' - ');
        //    console.log(g);
        //    if (g.length > 1) {
        //        start_time = g[0];
        //        end_time = g[1];
        //    }

        //    export_data({ name: "cdks", key: pager_key, start_time: start_time, end_time: end_time, opt1: opt1, opt2: opt2 });
        //}
    </script>

    <script>
        var click_ids = {};
        function checkIsSelect(obj) {
            if (obj.prop('checked')) {
                return true;
            } else {
                return false;
            }
        }
        function goods_click(t) {
            t = $(t);
            var obj = t.find(".good_select");
            obj.prop("checked", !checkIsSelect(obj));

        }

        function select_all(obj) {
            $(".good_select").prop("checked", checkIsSelect($(obj)));
        }

        function select_all_id() {
            var ids = "";
            $("#list-display tr").each(function () {
                var select_obj = $(this).find(".good_select");
                if (checkIsSelect(select_obj)) {
                    if (ids != "") {
                        ids += ",";
                    }
                    ids += select_obj.data("id");
                }
            });
            return ids;
        }
    </script>

    <%-- <script src="https://www.jq22.com/demo/jquerytuozhuai202005141436/js/Sortable.min.js"></script>

    
<script type="text/javascript">
    new Sortable(document.getElementById('list-display'), {
        handle: '.good_select', // handle's class
        animation: 150,
        // direction: 'horizontal',,

        // 结束拖拽
        onEnd: function (/**Event*/evt) {
            var itemEl = evt.item;  // dragged HTMLElement
            evt.to;    // target list
            evt.from;  // previous list
            evt.oldIndex;  // element's old index within old parent
            evt.newIndex;  // element's new index within new parent
            evt.clone // the clone element
            evt.pullMode;  // when item is in another sortable: `"clone"` if cloning, `true` if moving

            console.log('evt', evt.to, evt.from, evt.oldIndex, evt.newIndex);
        }
    });

    //$(".list_con .iconshanchu").bind('touchstart', function () {
    //    $(this).parent().parent().remove();
    //    event.preventDefault();
    //    event.stopPropagation();
    //});
</script>
    --%>
</asp:Content>


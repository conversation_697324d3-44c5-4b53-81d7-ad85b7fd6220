<%@ Page Title="网站前台设置" Language="C#" MasterPageFile="~/serv/m.master" AutoEventWireup="true" CodeFile="webmaps.aspx.cs" Inherits="admin_sitecore" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="Server">
    <link rel="stylesheet" href="../kindeditor/themes/default/default.css" />
    <link rel="stylesheet" href="../kindeditor/plugins/code/prettify.css" />
    <script charset="utf-8" src="../kindeditor/kindeditor.js"></script>
    <script charset="utf-8" src="../kindeditor/lang/zh-CN.js"></script>
    <script charset="utf-8" src="../kindeditor/plugins/code/prettify.js"></script>


    <style>
        body {
            overflow-x: scroll!important;
        }

        button[type='button'] {
            height: 34px;
        }

        .input-group .form-control {
            border-color: #ccc!important;
        }

        .inputtitle {
            border: 0px!important;
            cursor: default!important;
            background: none!important;
            box-shadow: none!important;
            width: 180px;
            text-align: right!important;
        }

            .inputtitle i {
                font-family: tahoma;
                vertical-align: middle;
                color: #ff6f00;
                margin-right: 4px;
                font-size: 14px;
                vertical-align: middle;
            }

        .btn-default {
            display: block;
            border: #bbb 1px solid;
            color: #333!important;
            background: #f8f8f8;
            background: -webkit-gradient(linear,0% 0%, 0% 100%, from(#FFFFFF), to(#E8E8E8));
            background: -moz-linear-gradient(0% 0% 270deg, #FFFFFF,#E8E8E8);
            text-align: center;
            color: #333;
            font-weight: normal;
        }

        .com-btn-01 {
            display: inline-block;
            width: 100%;
            max-width: 200px;
            height: 44px;
            line-height: 43px;
            font-size: 18px;
            font-family: "microsoft yahei";
            text-align: center;
            color: #fff!important;
            border-radius: 3px;
            vertical-align: middle;
            cursor: pointer;
        }

            .com-btn-01.color01 {
                background: #ff6700;
            }

                .com-btn-01.color01:hover {
                    background: #ff7700;
                }





        /* 定义滚动条的样式 */
        ::-webkit-scrollbar {
            width: 5px; /* 设置滚动条的宽度 */
        }

        /* 定义滚动条轨道的样式 */
        ::-webkit-scrollbar-track {
            background-color: #f1f1f1; /* 设置滚动条轨道的背景颜色 */
        }

        /* 定义滚动条滑块的样式 */
        ::-webkit-scrollbar-thumb {
            background-color: #888; /* 设置滚动条滑块的背景颜色 */
            border-radius: 5px; /* 设置滚动条滑块的圆角半径 */
            cursor: pointer;
        }

            /* 鼠标悬停在滚动条上时滑块的样式 */
            ::-webkit-scrollbar-thumb:hover {
                background-color: #555; /* 设置鼠标悬停时滚动条滑块的背景颜色 */
                cursor: pointer;
            }

        .input-title {
            color: #000;
            padding-left: 3px;
            margin-bottom: 0px;
            margin-top: 20px;
        }

        .input-container {
            display: flex;
            border-bottom: 1px solid #eee;
            box-sizing: border-box;
            padding: 6px 0px;
            box-sizing: border-box;
        }

            .input-container input, .input-container textarea {
                border: 0;
                font-size: 12px;
                outline: none;
                box-sizing: border-box;
                width: 100%;
                padding: 15px 2px;
            }

            .input-container textarea {
                resize: none;
                height: 200px;
            }

            .input-container .img-button {
                display: flex;
                border: 1px dashed #bbb;
                width: 99px;
                height: 99px;
                justify-content: center;
                align-items: center;
                cursor: pointer;
            }


                .input-container .img-button svg path {
                    fill: currentColor;
                }


        .class_items {
            display: inline-block;
            line-height: 30px;
            color: hsl(0,0%,25%);
            padding: 0.05em 0.6em;
            border-radius: 3px;
            white-space: nowrap;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            background: #4d4d4d0a;
            margin-right: 10px;
        }


            .class_items:hover {
                background-color: #4d4d4d14;
                color: #ff6000;
            }

            .class_items.active {
                font-weight: bold;
                color: #ffffff;
                background-color: #ff6000;
            }


        .select_items {
            display: inline-block;
            line-height: 30px;
            color: hsl(0,0%,25%);
            padding: 0.05em 0.6em;
            border-radius: 3px;
            white-space: nowrap;
            cursor: pointer;
            font-size: 14px;
            /*font-weight: bold;*/
            background: #4d4d4d0a;
            margin-right: 10px;
            border: 1px solid transparent;
        }

            .select_items.active {
                /*color: #7d5509;
                background-color: #f5fffe;
                border: 1px solid #000;
                text-shadow: 5px 5px 5px #7d550936;*/
                olor: #246302;
                background-color: #a6e981;
                border: 1px solid #000;
                text-shadow: 5px 5px 5px #7d550936;
                font-weight: bold;
            }
    </style>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="Server">
    <input id="config_updateid" value="<%=cae.GetCache<string>("config_updateid") %>" type="hidden" />
    <div style="padding: 3px 8px;padding-top:100px;">
        <div class="ibox float-e-margins">
            <%--<div class="ibox-title">
                <h5>网站设置</h5>
            </div>--%>


            <div style="position: fixed; width: 100%; background: #eff1f76b; left: 0; top: 0; z-index: 999999;">


                <div style="padding: 18px; border-radius: 18px;">

                    <div style="display: flex;">
                        <input style="outline: none; font-size: 16px; border: 0; padding: 13px 18px; border-top-left-radius: 3px; border-bottom-left-radius: 3px; width: 100%; color: #5a5b5c; background: #4d4d4d14;" placeholder="请输入搜素关键词" id="searck_key" onkeyup="search_keyword()">
                        <a style="display: inline-block; background: linear-gradient(97.21deg, #d3d9ed 0%, #d5d5d5 126.55%); color: #5a6389; font-size: 20px; display: flex; align-items: center; width: 138px; text-align: center; justify-content: center; " onclick="search_keyword()">
                            <svg t="1698331708435" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="7261" width="20" height="20">
                                <path d="M877.2928 786.7712l-183.7184-183.8016c-2.1504-2.1504-4.512-3.744-6.752-5.5168 31.0144-47.6224 49.152-104.3584 49.152-165.472C735.9744 264.0832 599.904 128 431.9936 128 264.1408 128 128 264.0832 128 431.9808c0 167.936 136.1408 303.968 303.9936 303.968 61.0816 0 117.888-18.144 165.5808-49.2032 1.7344 2.2976 3.3408 4.6016 5.4528 6.7136l183.7632 183.8272c12.5376 12.4992 28.8768 18.72 45.2544 18.72 16.3584 0 32.7232-6.2464 45.248-18.6944C902.24 852.2688 902.24 811.7568 877.2928 786.7712M431.9936 639.9616C317.344 639.9616 224 546.6368 224 431.9488c0-114.6624 93.344-208 207.9936-208 114.6688 0 207.9808 93.3376 207.9808 208C639.9744 546.6368 546.6624 639.9616 431.9936 639.9616" fill="#5a6389" p-id="7262"></path></svg>搜索</a>
                        <a style="display: inline-block;background: #e7e4e4;color: #5a6389;font-size: 20px;display: flex;align-items: center;width: 138px;text-align: center;justify-content: center;border-top-right-radius: 3px;border-bottom-right-radius: 3px;" onclick="updateConfig()">
                            保存</a>
                    </div>

                    <div style="display: flex; margin-top: 10px; overflow-x: auto;">
                        <strong data-key="" class="class_items active">全部</strong>
                        <asp:Repeater ID="keysList" runat="server">
                            <ItemTemplate>
                                <strong data-key="<%#Eval("key") %>" class="class_items"><%#Eval("name") %></strong>
                            </ItemTemplate>
                        </asp:Repeater>

                    </div>

                </div>

                <script>
                    $('.class_items').on('click', function () {
                        $(this).addClass("active").siblings().removeClass("active");
                        search_keyword();
                    })

                    var search_keyword = function () {
                        var searck_key = $('#searck_key').val();
                        var key = $(".class_items.active").eq(0).data("key");
                        //location.hash = key


                        console.log("search_keyword", search_keyword, key);

                        $('.config_items').each(function () {
                            if (key == "" || $(this).attr("typename") == key) {
                                if ($(this).attr("first_name").toLowerCase().indexOf(searck_key.toLowerCase()) != -1) {
                                    $(this).show();
                                } else {
                                    $(this).hide();
                                }
                            } else {
                                $(this).hide();
                            }
                        })
                    }
                </script>


            </div>

            <div class="ibox-content layui-form" style="padding: 30px;">

                <table class="coreT" style="border: 0px; margin: 0px; background: none;">

                    <tbody>
                        <tr class="none">
                            <td class="p-title">
                                <span class="pre-tip">*</span><br>
                                siteurl：
                            </td>
                            <td>
                                <input type="text" class="update" id="siteurl" value="<%=Request.Url %>" />
                            </td>
                        </tr>




                        <%for (int i = 0; i < arrlist.Count; i++)
                          {%>

                        <%Dictionary<string, object> temp = arrlist[i];
                          switch ((string)getdic(temp, "type"))
                          {
                              case "textarea":%>
                        <tr>
                            <div class="col-md-12 form-group  m-b-sm config_items" typename="<%=(string)getdic(temp, "typename") %>" first_name="<%=(string)getdic(temp, "name") %>">
                                <div class="input-group " style="width: 100%;">
                                    <span class="input-group-addon inputtitle"><%=((bool)getdic(temp, "nostar",true)?"":"<i>*</i> ")+(string)getdic(temp, "name") %>：</span>
                                    <textarea style="width: 100%; height: <%=(string)getdic(temp, "height","200") %>px; resize: none;" class="form-control update <%=(string)getdic(temp, "remark") %>" id="<%=(string)getdic(temp, "id") %>" placeholder="" maxlength="50000"><%=uConfig.stcdata((string)getdic(temp, "id")) %></textarea>
                                </div>
                            </div>
                        </tr>
                        <% break;
                              case "images":%>
                        <tr>
                            <div class=" col-md-12 form-group  m-b-sm config_items" typename="<%=(string)getdic(temp, "typename") %>" first_name="<%=(string)getdic(temp, "name") %>">
                                <div class="input-group ">
                                    <span class="input-group-btn">
                                        <button type="button" class="btn btn-default inputtitle"><%=((bool)getdic(temp, "nostar",true)?"":"<i>*</i> ")+(string)getdic(temp, "name") %>：</button>
                                    </span>
                                    <div class="update" id="<%=(string)getdic(temp, "id") %>" style="display: none;" data-type="<%=(string)getdic(temp, "remark") %>">
                                        <img src="<%=uConfig.stcdata((string)getdic(temp, "id")) %>" />
                                    </div>
                                    <input type="text" class="form-control" value="<%=uConfig.stcdata((string)getdic(temp, "id")).Replace("../", myhost + "/") %>" placeholder="" disabled="disabled" style="display: none;">

                                    <button type="button" class="btn btn-default imgbtn" onclick="_upload('<%=(string)getdic(temp, "id") %>')">上传图片</button>

                                </div>
                            </div>
                            <div class=" col-md-12 form-group  m-b-sm imgshowList" style="text-align: left; padding-left: 160px;">
                            </div>
                        </tr>
                        <% break;
                              case "checkbox":
                              case "option":%>
                        <tr>
                            <div class="col-md-12 form-group  m-b-sm config_items" typename="<%=(string)getdic(temp, "typename") %>" first_name="<%=(string)getdic(temp, "name") %>">
                                <div class="input-group select-boxs" style="width: 100%;" stype="<%=(string)getdic(temp, "type")=="checkbox"?"multi":"" %>">
                                    <input class="select_data update" style="display: none;" id="<%=(string)getdic(temp, "id") %>" value="<%=uConfig.stcdata((string)getdic(temp, "id")) %>" />
                                    <span class="input-group-addon inputtitle"><%=((bool)getdic(temp, "nosstar",true)?"":"<i>*</i> ")+(string)getdic(temp, "name") %>：</span>
                                    <div style="display: flex;">



                                        <% 

                        List<Dictionary<string, object>> list1=(List<Dictionary<string, object>>)getdic(temp, "data");
                        
                           foreach (var data in list1)
	{		 
                               
                               
                               foreach (var kvp in data)
            {
                                        %>

                                        <div class="select_items" v="<%=kvp.Value %>"><%=kvp.Key %></div>
                                        <%
            }

    }
                        
                                        %>
                                    </div>
                                </div>
                            </div>
                        </tr>
                        <% break;
                              default:%>
                        <tr>
                            <div class=" col-md-12 form-group  m-b-sm config_items" typename="<%=(string)getdic(temp, "typename") %>" first_name="<%=(string)getdic(temp, "name") %>">
                                <div class="input-group ">
                                    <span class="input-group-btn">
                                        <button type="button" class="btn btn-default inputtitle"><%=((bool)getdic(temp, "nosstar",true)?"":"<i>*</i> ")+(string)getdic(temp, "name") %>：</button>
                                    </span>
                                    <input type="text" class="form-control update" id="<%=(string)getdic(temp, "id") %>" value="<%=uConfig.stcdata((string)getdic(temp, "id")) %>" placeholder="请输入<%=(string)getdic(temp, "name") %>">
                                </div>
                            </div>
                        </tr>
                        <%
                           break;
                          } %>

                        <% } %>

                        <tr>
                            <td class="p-title" style="background: none;"></td>
                            <td style="background: none;">
                                <div style="padding: 12px 0;">
                                    <a class="btn btn-success" onclick="updateConfig()" style="text-decoration: none;">
                                        <i class="fa-floppy-o fa"></i>&nbsp;
                            保存更改</a>

                                    <a class="btn btn-success" onclick="updateCache()" style="text-decoration: none;">
                                        <i class="fa-cab fa"></i>&nbsp;
                            刷新缓存</a>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>

            </div>
        </div>
    </div>
    <script>

        var pushToData = function (e) {
            var list = new Array();
            $(e).find('.active').each(function () {
                list.push($(this).attr("v"))
            })
            $(e).find('.select_data').val(list.join(','));
        }

        $('.select_items').on('click', function () {
            var stype = $(this).closest('.select-boxs').attr('stype');
            if (stype == "multi") {
                $(this).toggleClass("active");
            } else {
                $(this).addClass("active").siblings().removeClass("active");
            }

            pushToData($(this).closest('.select-boxs'));
        })

        $('.select-boxs').each(function () {
            var select_data = $(this).find('.select_data').val();
            var stype = $(this).attr('stype');
            var array = select_data.split(',');
            for (var i = 0; i < array.length; i++) {
                $(this).find('[v="' + array[i] + '"]').addClass("active");
            }

            if (stype != "multi" && $(this).find('.active').length == 0) {
                $(this).find('.select_items').eq(0).addClass("active");
            }
            pushToData($(this));
        })
    </script>
    <script>
        var radios = ",";
        $("input[type='radio']").each(function () {
            var name = $(this).attr("name");
            if (radios.indexOf("," + name + ",") == -1) {
                radios += name + ",";
                console.log(radios, name);
                if ($("#" + name).length > 0) {
                    $("input[name='" + name + "']").eq($("#" + name + "").val()).attr("checked", "checked");
                    $("input[name='" + name + "']").click(function () {
                        $("#" + name + "").val($(this).index("input[name='" + name + "']"));
                    })
                }
            }
        })

        $("#needExa").val('<%=uConfig.stcdata("needExa") %>');
    </script>

    <script>
        var host_url = '<%=myhost %>';

        function resetImgsInput() {
            $(".imgshowList").each(function () {
                var imgstr = "";
                $(this).find("img").each(function () {
                    var img = $(this).attr("src");
                    imgstr += img + ",";
                });
                $(this).closest(".col-md-12").prev().find("input").val(imgstr);
            });
        }

        function show_imgs_list() {
            $(".imgshowList").each(function () {
                var input = $(this).closest(".col-md-12").prev().find("input");
                var imgs = input.val();
                if (imgs) {
                    $(this).html("");
                    imgs = imgs.split(',');
                    var imgstr = "";
                    for (var i = 0; i < imgs.length; i++) {
                        var img = imgs[i];
                        if (img != "") {
                            var timestamp = (new Date()).valueOf() + "_" + randomString(32) + "_" + randomString(32);
                            //$(this).append("<div class='img _" + timestamp + "a' style=\"display:inline-block;margin:2px;\"><p style=\"background: gainsboro;padding:3px 0;\"><a class='del' onclick=\"javascript:$('._" + timestamp + "a').remove();resetImgsInput();\">删除图片</a><p><a href=\"" + img + "\" target=\"_blank\" ><img src=\"" + img + "\" style=\"height: 120px;width: 120px;background:#000;\" /></a></div>");

                            $(this).append("<div class='img _" + timestamp + "a' style=\"display:inline-block;margin:2px;position:relative;\"><a href=\"" + img + "\" target=\"_blank\" ><img src=\"" + img + "\" style=\"height: 80px;width: 80px;background:#000;\" /></a><div class=\"remove-this\" onclick=\"javascript:$('._" + timestamp + "a').remove();resetImgsInput();\"></div></div>");

                            imgstr += img + ",";
                        }
                    }
                    imgstr = imgstr.trim(',');
                    input.val(imgstr);
                }
            })
        }

        function _upload(name) {
            uploadImages(function (json) {
                if (json.success) {
                    var name = json.path;
                    var obj = $("#" + name);
                    var tagname = obj.prop("tagName");
                    var imgtype = obj.data("type");
                    imgurl = json.imgurl[0].replace("..", host_url);
                    console.log(json, imgurl, name, tagname, imgtype);
                    if (tagname == "INPUT") {
                        obj.val(imgurl);
                    } else {
                        if (imgtype == "1") {
                            obj.next().val(obj.next().val() + "," + imgurl);
                        } else {
                            obj.next().val(imgurl);
                        }
                        obj.html("<img src='" + imgurl + "'>");
                        show_imgs_list();
                    }
                } else {
                    layAlert("上传失败");
                }
            }, name)

        }
        show_imgs_list();
    </script>

    <script>
        function updateConfig() {
            for (var i = 0; i < editors.length; i++) {
                editors[i].sync();
            }
            var jsondata = eval('([])');
            $(".update").each(function () {
                var __display = $(this).closest(".config_items").css("display");
                var id = $(this).attr("id");
                var arr;
                var img = $("#" + id + " img");
                if (__display == "block") {

                    switch (id) {
                        case "carousel":
                            var data = eval('([])');
                            $("#carousel .img").each(function () {
                                var img = {
                                    "url": $(this).find("input").val(),
                                    "img": $(this).find("img").attr("src")
                                };
                                data.push(img);
                            });
                            arr = {
                                "name": id,
                                "data": (JSON.stringify(data) + ""),
                                "view": data
                            };
                            console.log(data);
                            console.log(JSON.stringify(data));
                            break;
                        default:
                            if (img.length > 0) {
                                if ($(this).css("display") == "none") {
                                    img = $(this).next("input").val();
                                    img = selectImgs(img).join(',');
                                    console.log("input_img", img);
                                } else {
                                    img = img ? img.attr("src") : "";
                                }
                                arr = {
                                    "name": id,
                                    "data": img
                                };
                            } else {
                                arr = {
                                    "name": id,
                                    "data": $(this).val()
                                };
                            }
                            break;

                    }
                    jsondata.push(arr);

                }
            })

            console.log(jsondata);
            jsondata = JSON.stringify(jsondata);
            console.log(jsondata);
            if (jsondata == '[]') {
                return;
            }
            $.ajax({
                type: "POST",
                url: "../Api/admin.aspx?do=updateConfig",
                data: { data: jsondata, config_updateid: $('#config_updateid').val() },
                datatype: "json",
                success: function (data) {
                    try {
                        if (data.config_updateid) {
                            $('#config_updateid').val(data.config_updateid)
                        }
                    } catch (e) {

                    }
                    if (data.code == 1) {
                        layer.msg(data.msg, { icon: 1, time: 1000 });
                    } else {
                        layer.msg(data.msg, { icon: 2, time: 1500 });
                    }

                },
                error: function () {
                    serr();
                }
            });
        }

        function updateCache() {
            $.ajax({
                type: "POST",
                url: "../Api/admin.aspx?do=updateCache",
                data: { },
                datatype: "json",
                success: function (data) {
                    if (data.code == 1) {
                        layer.msg(data.msg, { icon: 1, time: 1000 });
                    } else {
                        layer.msg(data.msg, { icon: 2, time: 1500 });
                    }

                },
                error: function () {
                    serr();
                }
            });
        }


        layui.use(['form', 'layedit'], function () {
            var form = layui.form
            , layer = layui.layer
            , layedit = layui.layedit;
            layui.form.render();
        });
    </script>

    <script>
        var editors = new Array();
        KindEditor.ready(function (K) {
            console.log("test");
            $(".kedit").each(function () {
                var id = $(this).attr("id");
                var editor = K.create('#' + id, {
                    cssPath: '../kindeditor/plugins/code/prettify.css',
                    uploadJson: '../kindeditor/upload_json.ashx',
                    fileManagerJson: '../kindeditor/file_manager_json.ashx',
                    allowFileManager: true,
                    afterCreate: function () {
                        var self = this;
                        K.ctrl(document, 13, function () {
                            self.sync();
                            K('form[name=example]')[0].submit();
                        });
                        K.ctrl(self.edit.doc, 13, function () {
                            self.sync();
                            K('form[name=example]')[0].submit();
                        });
                    }
                });
                editors.push(editor);
            })
            prettyPrint();

            console.log("editors", editors);
        });
    </script>

    <script>
        function randomString(len) {
            len = len || 32;
            var $chars = 'ABCDEFGHJKMNPQRSTWXYZabcdefhijkmnprstwxyz2345678';    /****默认去掉了容易混淆的字符oOLl,9gq,Vv,Uu,I1****/
            var maxPos = $chars.length;
            var pwd = '';
            for (i = 0; i < len; i++) {
                pwd += $chars.charAt(Math.floor(Math.random() * maxPos));
            }
            return pwd;
        }
    </script>


        <style>

        .scroll-button {
            position: fixed;
            right: 2px;
            bottom: 20px;
            width: 50px;
            height: 50px;
            background-color: #007bff;
            color: #fff;
            display: flex;
            justify-content: center;
            align-items: center;
            border-radius: 50%;
            cursor: pointer;
            font-size: 24px;
            z-index: 1000;
            transition: background-color 0.3s;
        }

        .scroll-button:hover {
            background-color: #0056b3;
        }

        .scroll-button.bottom {
            bottom: 80px;
        }
    </style>
    <div class="scroll-button" id="scrollTopButton">↑</div>
    <div class="scroll-button bottom" id="scrollBottomButton">↓</div>
    <script>
        document.getElementById('scrollTopButton').onclick = function () {
            window.scrollTo({ top: 0, behavior: 'smooth' });
        };

        document.getElementById('scrollBottomButton').onclick = function () {
            window.scrollTo({ top: document.body.scrollHeight, behavior: 'smooth' });
        };
    </script>
</asp:Content>


<%@ Page Title="礼品订单" Language="C#" MasterPageFile="~/serv/m.master" AutoEventWireup="true" CodeFile="product_order.aspx.cs" Inherits="w_index" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="Server">
    <link href="../css/element-ui.css" rel="stylesheet" />
    <style>
        .el-tag.el-tag--test {
            background-color: #d5d5f5;
            border-color: #7d7dc7;
            color: #6d3333;
        }
    </style>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="Server">
    <div class="ibox float-e-margins">
        <div class="ibox-title">
            <h5>礼品订单</h5>
        </div>
        <div class="ibox-content">
            <div class="row layui-form">
                <div class="col-md-3">
                    <div class="form-group form-buttom">
                        <input type="text" id="search-keyword" name="ordername" class="form-control" placeholder="请输入搜索的信息">
                    </div>
                </div>

                <div class="col-md-4">
                    <div class="form-group form-buttom">
                        <a class="btn btn-success" onclick="searchList()">
                            <svg t="1688237921441" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="1949" width="16" height="16">
                                <path d="M361.130667 658.688a256 256 0 1 1 282.794666-44.672 42.496 42.496 0 0 0-10.368 14.933333c-0.853333 0.682667-1.621333 1.450667-2.389333 2.218667a42.666667 42.666667 0 0 0 0 60.330667l170.666667 170.666666a42.666667 42.666667 0 0 0 60.330666-60.330666l-142.762666-142.762667a341.333333 341.333333 0 1 0-169.344 99.242667 42.666667 42.666667 0 0 0-20.224-82.901334 254.762667 254.762667 0 0 1-168.704-16.725333z" fill="#14101C" p-id="1950"></path></svg>
                            查询</a>
                        <a class="btn btn-success" onclick="javascript:location.href=location.href;">
                            <svg t="1688237949108" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2211" width="16" height="16">
                                <path d="M512 192a298.24 298.24 0 0 0-245.162667 128 42.666667 42.666667 0 1 1-69.973333-48.810667 384.042667 384.042667 0 0 1 697.173333 180.48l36.608-34.645333a42.666667 42.666667 0 0 1 58.709334 61.952l-112.64 106.666667a42.666667 42.666667 0 0 1-71.253334-38.912A298.666667 298.666667 0 0 0 512 192z m0.170667 640a298.24 298.24 0 0 0 245.162666-128 42.666667 42.666667 0 1 1 69.973334 48.810667 384.042667 384.042667 0 0 1-697.173334-180.48l-36.650666 34.645333a42.666667 42.666667 0 0 1-58.666667-61.952l112.64-106.666667a42.666667 42.666667 0 0 1 71.253333 38.912 298.666667 298.666667 0 0 0 293.418667 354.730667z" fill="#14101C" p-id="2212"></path></svg>
                            刷新</a>
                        <%--<a class="btn btn-success" onclick="update_select(-9)"><svg t="1688237896673" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="1699" width="16" height="16"><path d="M612.693333 170.666667l8.533334 42.666666H402.773333l8.533334-42.666666h201.386666z m-293.632 25.941333a86.186667 86.186667 0 0 0-1.706666 16.725333H170.666667a42.666667 42.666667 0 0 0 0 85.333334h682.666666a42.666667 42.666667 0 1 0 0-85.333334h-146.730666c0-5.504-0.554667-11.093333-1.706667-16.725333l-8.533333-42.666667A85.333333 85.333333 0 0 0 612.778667 85.333333H411.306667a85.333333 85.333333 0 0 0-83.712 68.608l-8.533334 42.666667zM426.666667 469.333333a42.666667 42.666667 0 0 0-42.666667 42.666667v170.666667a42.666667 42.666667 0 1 0 85.333333 0v-170.666667a42.666667 42.666667 0 0 0-42.666666-42.666667z m128 42.666667a42.666667 42.666667 0 1 1 85.333333 0v170.666667a42.666667 42.666667 0 1 1-85.333333 0v-170.666667zM255.402667 384A42.666667 42.666667 0 0 1 298.666667 426.069333L301.738667 640H301.653333a42.666667 42.666667 0 1 1-85.290666-2.176l-2.986667-210.56A42.666667 42.666667 0 0 1 255.36 384z m51.925333 396.586667h0.042667A103.381333 103.381333 0 0 0 406.698667 853.333333h210.602666c54.186667 0 98.56-40.789333 103.210667-92.757333L725.333333 426.069333a42.666667 42.666667 0 0 1 85.333334 1.194667l-4.864 337.365333-0.128 1.365334C798.08 863.872 715.562667 938.666667 617.301333 938.666667H406.698667a188.885333 188.885333 0 0 1-180.096-130.389334 42.666667 42.666667 0 0 1 80.725333-27.648z" fill="#14101C" p-id="1700"></path></svg>删除</a>--%>
                    </div>
                </div>



            </div>










            
            <div class="button-group">
                <a rv=""  class="button">全部</a>
                <a rv="1" class="button">已发货</a>
                <a rv="0" class="button">待发货</a>
                <a rv="-1" class="button">退款中</a>
                <a rv="-1000" class="button">已退款</a>
            </div>

            <br />


            <div class="table-responsive">
                <table class="table">
                    <thead>
                        <tr role="head">
                            <th sort="true" class="action-cell-left">
                                <input type="checkbox" onclick="javascript: select_all(this)" />#ID</th>
                            <th sort="true" class="">订单号</th>
                            <th sort="true" class="">用户</th>
                            <th sort="true" class="">商品图片</th>
                            <th sort="true" class="">商品名称</th>
                            <th sort="true" class="">消耗金币</th>
                            <th sort="true" class="">商品类型</th>
                            <th sort="true" class="">收货地址</th>
                            <th sort="true" class="">状态</th>
                            <th sort="true" class="">用户备注</th>
                            <th sort="true" class="">后台备注</th>
                            <th sort="true" class="">退款原因</th>
                            <th sort="true" class="">退款时间</th>
                            <th sort="true" class="action-cell">操作</th>
                        </tr>
                    </thead>
                    <tbody id="list-display">
                    </tbody>
                </table>
            </div>


            <div class="layui-table-page">
                <div id="layui-table-page1">
                    <div class="layui-box layui-laypage layui-laypage-default" id="layui-laypage-1">
                        <div style="display: inline-block;" class="cpages">
                            <!--page grow -->
                        </div>
                        <span class="layui-laypage-skip">到第<input type="text" min="1" value="1" class="layui-input target-page" />页
                            <button type="button" class="layui-laypage-btn" onclick="javascript:pager_index=$('.target-page').val()-1;getPager();">确定</button></span>
                        <span class="layui-laypage-count">共 <span class="page_count"></span>条</span>
                        <span class="layui-laypage-limits">
                            <select class="s_pages" lay-ignore="" onchange="javascript:pager_size=$(this).val();getPager();">
                                <option value="10">10 条/页</option>
                                <option value="20" selected="">20 条/页</option>
                                <option value="30">30 条/页</option>
                                <option value="50">50 条/页</option>
                                <option value="100">100 条/页</option>
                                <option value="200">200 条/页</option>
                            </select></span>
                    </div>
                </div>
            </div>

        </div>
    </div>
    <script>
        var _action = 'product_order';
        var _actionName = '礼品订单';
    </script>
    <script>
        function opennew() {
            var modify_model = [];

            modify_model.push({ name: '商品图片', id: 'imgurl', data: [], type: 'img' });
            modify_model.push({ name: '商品名称', id: 'name', value: '' });
            modify_model.push({ name: '商品介绍', id: 'goods_desc', type: 'textarea', value: '', height: 100 });
            modify_model.push({ name: '礼品概述', id: 'goods_overview', type: 'textarea', value: '', height: 200 });
            modify_model.push({ name: '所需金币', id: 'point', value: '' });
            modify_model.push({ name: '礼品类型', id: 'goods_type', value: 'goods', type: 'option', data: [['goods', '实物(需物流)'], ['vr-goods', '虚拟物品']] });
            modify_model.push({ name: '礼品类型', id: 'state', value: 1, type: 'option', data: [[1, '上架'], [-1, '下架']] });

            edit({ aname: 'admin', title: '添加' + _actionName, action: _action, id: '', data: modify_model });
        }
    </script>
    <script>

        var pager_size = $(".s_pages").val();
        var pager_index = 0;
        var pager_key = "";

        $("#pagesize").val(pager_key);
        function setPageSize(obj) {
            pager_index = 0;
            pager_key = $("#search-keyword").val();
            opt = $(obj).val();
            getPager();
        }

        function searchList() {
            opt = $("#opt1").val();
            pager_index = 0;
            pager_key = $("#search-keyword").val();
            getPager();
        }

        function getParam() {
            return { limit: pager_size, p: pager_index, page: _action, keyword: pager_key, audit_state: $("#audit_state").val(), settle_state: $("#settle_state").val(), start_time: $("#start_time").val(), end_time: $("#end_time").val(), tarea: "<%=Request.QueryString["area"] %>", state: get_param('rv') }
        }

        function getPager() {

            $.ajax({
                type: "POST",
                url: "../api/admin.aspx?do=page",
                data: getParam(),
                datatype: "json",
                success: function (json) {
                    $("#list-display").html("");
                    if (json.code == 1) {
                        var data = json.data.list;
                        for (var i = 0; i < data.length; i++) {
                            var tr = "";
                            tr += "<tr>";

                            tr += ('<td class="action-cell-left"><input class="good_select" type="checkbox" data-id="' + data[i].id + '" id="checkid_' + data[i].id + '" />#' + data[i].id + '</td>'); 
                            tr += ("<td class=''>" + '<span >' + data[i].transaction_id + '</span>' + "</td>");
                            tr += ("<td class=''>" + '<span class="button-style style-zs">' + data[i].username + '</span>' + "</td>");
                            tr += ("<td class=''>" + '<img src=' + data[i].imgurl + ' style="width:25px;height:25px;">' + "</td>");
                            tr += ("<td class=''>" + "<a href='../product-list.aspx?pid=" + data[i].id + "' style='color:#1f2deb;' target='_blank'>" + data[i].goods_name + "</a>" + "</td>");
                            tr += ("<td class=''>" + data[i].point + "</td>");
                            tr += ("<td class=''>" + data[i].goods_type + "</td>");
                            tr += ("<td class=''>" + '<b>' + data[i].name + ' ' + data[i].phone + '</b>' + ' ' + data[i].shop_address + "</td>");
                            tr += ("<td>" + (data[i].refund_state == 1 ? '<span class="button-style style-hs">退款中</span> ' : data[i].refund_state == -1 ? '<span class="button-style style-js">拒绝退款</span> ' : data[i].refund_state == 1000 ? '<span class="button-style style-zs">已退款</span> ' : '') + (data[i].state == 1 ? '<span class="button-style style-lvs">已发货</span>' : data[i].state == 1000 ? '<span class="button-style style-lvs">已完成</span>' : '<span class="button-style">待发货</span>') + "</td>");
                            tr += ("<td class='' style='max-width:100px;'>" + data[i].user_remark + "</td>");
                            tr += ("<td class='' style='max-width:100px;'>" + data[i].remark + "</td>");
                            tr += ("<td class='' style='max-width:100px;' title='" + data[i].refund_reason + "'>" + data[i].refund_reason + "</td>");
                            tr += ("<td class='' style='max-width:150px;'>" + data[i].refund_time + "</td>");

                            var modify_model = new Array();

                            modify_model.push({ name: '收件姓名', id: 'name', value: data[i].name });
                            modify_model.push({ name: '收件电话', id: 'phone', value: data[i].phone });
                            modify_model.push({ name: '收件地址', id: 'shop_address', value: data[i].shop_address });

                            modify_model.push({ name: '后台备注', id: 'remark', value: data[i].remark });
                            modify_model.push({ name: '用户备注', id: 'user_remark', value: data[i].user_remark });
                            modify_model.push({ name: '订单状态', id: 'state', value: data[i].state, type: 'option', data: [[0, '待发货'], [1, '已发货'], [1000, '已完成']] });



                            var refund_model = new Array();
                            refund_model.push({ name: '操作类型', id: 'modify_type', value: 'refund', type: 'hidden' });
                            refund_model.push({ name: '申请原因', id: 'refund_reason', value: data[i].refund_reason,type:'disabled' });
                            refund_model.push({ name: '用户备注', id: 'user_remark', value: data[i].user_remark });
                            refund_model.push({ name: '退款操作', id: 'refund_state', value: 1, type: 'option', data: [[-1, '拒绝退款'], [1000, '同意/直接退款']] });


                            //tr += ("<td>"
                            //    + "<button type='button' class='el-button  el-button--default el-button--mini' onclick='edit(" + edtext({ aname: 'admin', title: _actionName + "修改", action: _action, id: data[i].id, data: modify_model }) + ")'><span><i class='fa fa-edit'></i></span></button>"
                            //    + "<button type='button' class='el-button  el-button--default el-button--mini' onclick=\"event_create({'aname':'admin','name':'" + _action + "','event':'delete','id':" + data[i].id + "})\"><span><i class='fa fa-remove'></i></span></button>"
                            //    + "</td>");




                            tr += ("<td class='action-cell '>"
                                + ' <div class="card-toolbar">'
                                + '<a class="card-button common-button" ' + "onclick='edit(" + edtext({ aname: 'admin', title: _actionName + "修改", action: _action, id: data[i].id, data: modify_model }) + ")'" + '><svg  class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg"  width="16" height="16" style="position:relative;top:3px;"><path d="M358.165868 554.624796c-0.533143 0.680499-1.066285 1.391696-1.303692 2.251274l-41.104163 150.700257c-2.400676 8.772804 0.059352 18.226107 6.550183 24.892947 4.860704 4.742001 11.261485 7.350408 18.077727 7.350408 2.252297 0 4.504594-0.267083 6.727215-0.860601l149.630902-40.808428c0.23843 0 0.357134 0.207731 0.534166 0.207731 1.718131 0 3.408633-0.62217 4.683672-1.927909l400.113747-400.054395c11.883655-11.897981 18.404162-28.109198 18.404162-45.74281 0-19.989263-8.476045-39.963177-23.324218-54.767348l-37.786605-37.844933c-14.81645-14.848173-34.822087-23.338544-54.797024-23.338544-17.631566 0-33.842783 6.520507-45.75816 18.388812L358.758362 553.232077C358.344946 553.615816 358.462626 554.179658 358.165868 554.624796M862.924953 257.19778l-39.742143 39.71349-64.428382-65.451688 39.180348-39.179324c6.193049-6.222725 18.194384-5.318122 25.308409 1.822508l37.813211 37.845956c3.943822 3.941775 6.195096 9.18622 6.195096 14.372336C867.223862 250.574942 865.710392 254.42769 862.924953 257.19778M429.322487 560.907896l288.712541-288.728914 64.459081 65.49569L494.314711 625.838721 429.322487 560.907896zM376.718409 677.970032l20.863167-76.580143 55.656601 55.657624L376.718409 677.970032z" fill="#e6e6e6" p-id="1994"></path><path d="M888.265084 415.735539c-15.144932 0-27.562752 12.313443-27.620058 27.665083l0 372.98283c0 19.559475-15.885805 35.444257-35.475979 35.444257L194.220958 851.827709c-19.559475 0-35.504632-15.883759-35.504632-35.444257L158.716326 207.602222c0-19.575848 15.945157-35.474956 35.504632-35.474956l406.367171 0c15.231913 0 27.592428-12.371772 27.592428-27.606755 0-15.202237-12.360516-27.590382-27.592428-27.590382L190.013123 116.930129c-47.684022 0-86.49291 38.779212-86.49291 86.49291L103.520213 820.59231c0 47.713698 38.808888 86.47756 86.49291 86.47756l639.334083 0c47.715745 0 86.509283-38.763862 86.509283-86.47756L915.856489 443.222567C915.794068 428.048983 903.408993 415.735539 888.265084 415.735539" fill="#e6e6e6" p-id="1995"></path></svg>&nbsp;编辑</a>'
                                +(data[i].refund_state == 1000?'': '&nbsp;<a class="card-button common-button" ' + "onclick='edit(" + edtext({ aname: 'admin', title: _actionName + "退款处理", action: _action, id: data[i].id, data: refund_model }) + ")'" + '><svg t="1689015231155" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="9697" width="16" height="16" style="position:relative;top:3px;"><path d="M128 645.12l138.24-163.84H179.2c12.8-194.56 174.08-348.16 368.64-348.16 202.24 0 368.64 168.96 368.64 376.32s-166.4 376.32-368.64 376.32c-74.24 0-145.92-23.04-207.36-66.56-23.04-17.92-58.88-10.24-74.24 12.8-17.92 23.04-10.24 58.88 12.8 74.24 79.36 56.32 171.52 84.48 268.8 84.48 261.12 0 476.16-217.6 476.16-481.28C1024 243.2 811.52 28.16 547.84 28.16c-248.32 0-455.68 212.48-478.72 455.68H0l128 161.28zM696.32 307.2c7.68-17.92 2.56-33.28-17.92-46.08-20.48-10.24-35.84-7.68-48.64 12.8l-97.28 153.6-99.84-153.6c-12.8-17.92-28.16-23.04-46.08-15.36s-25.6 25.6-17.92 48.64l99.84 151.04h-81.92c-7.68 7.68-12.8 15.36-12.8 25.6s5.12 20.48 12.8 25.6h107.52v43.52h-107.52c-7.68 7.68-12.8 15.36-12.8 25.6s5.12 20.48 12.8 25.6h107.52v110.08c-2.56 30.72 10.24 48.64 38.4 48.64s43.52-15.36 40.96-48.64v-110.08h107.52c10.24-5.12 15.36-15.36 15.36-25.6 0-12.8-5.12-20.48-15.36-25.6h-107.52v-43.52h104.96c10.24-7.68 15.36-15.36 15.36-25.6s-5.12-20.48-15.36-25.6h-81.92l99.84-151.04z" fill="#2c2c2c" p-id="9698"></path></svg>&nbsp;退款</a>')
                                //+ '<div class="card-popup">'

                                //+ "<div class='menu-item px-3'><a onclick='edit(" + edtext({ aname: 'admin', title: _actionName + "修改", action: _action, id: data[i].id, data: modify_model }) + ")' class='menu-link px-3'>编辑</a></div>"

                                //+ "<div class='menu-item px-3'><a onclick=\"event_create({'aname':'admin','name':'" + _action + "','event':'delete','id':" + data[i].id + "})\" class='menu-link px-3'>删除</a></div>"

                                //+ '</div>'
                                + '</div>');


                            tr += "</tr>";
                            $("#list-display").append(tr)


                            var __temp = data[i];
                            __temp.modify_model = modify_model;
                            _bindEvent(__temp);

                            //$('button[edit-' + __temp.id + ']').on("click", function () {
                            //    console.log('attrs', $(this).closest('tr').html(), __temp.modify_model);

                            //    edit({ aname: 'admin', title: '修改' + _actionName, action: _action, id: __temp.id, data: __temp.modify_model });
                            //})



                        }
                        $(".page_size").html(pager_size);
                        $(".page_count").html(json.data.count);
                        $(".cpages").html(json.data.html);


                    } else {

                    }
                },
                error: function () {
                }
            });
        }
        getPager();

        function _bindEvent(__temp) {
            $('button[edit-' + __temp.id + ']').on("click", function () {
                console.log('attrs', $(this).closest('tr').html(), __temp.modify_model);

                edit({ aname: 'admin', title: '修改' + _actionName, action: _action, id: __temp.id, data: __temp.modify_model });
            })
        }

        function _pt(i) {
            pager_index = i;
            getPager();
        }
    </script>

    <script>
        layui.use(['form', 'layedit', 'laydate'], function () {
        });
    </script>

    <script>
        function update_select(state) {
            event_create({ 'aname': 'admin', 'name': _action, 'event': 'update', 'id': select_all_id(), 'state': state });
        }
    </script>

    <script>
        var click_ids = {};
        function checkIsSelect(obj) {
            if (obj.prop('checked')) {
                return true;
            } else {
                return false;
            }
        }
        function goods_click(t) {
            t = $(t);
            var obj = t.find(".good_select");
            obj.prop("checked", !checkIsSelect(obj));

        }

        function select_all(obj) {
            $(".good_select").prop("checked", checkIsSelect($(obj)));
        }

        function select_all_id() {
            var ids = "";
            $("#list-display tr").each(function () {
                var select_obj = $(this).find(".good_select");
                if (checkIsSelect(select_obj)) {
                    if (ids != "") {
                        ids += ",";
                    }
                    ids += select_obj.data("id");
                }
            });
            return ids;
        }
    </script>
</asp:Content>



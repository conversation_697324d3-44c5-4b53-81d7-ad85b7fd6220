<%@ Page Title="聊天记录" Language="C#" MasterPageFile="~/serv/m.master" AutoEventWireup="true" CodeFile="chat_message.aspx.cs" Inherits="w_index" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="Server">
    <link href="../css/element-ui.css" rel="stylesheet" />
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="Server">
    <div class="ibox float-e-margins">
        <div class="ibox-title">
            <h5>聊天记录</h5>
        </div>
        <div class="ibox-content">



            <style>
                .filterclick-1 {
                    padding: 7px 12px;
                    border-radius: 3px;
                    font-weight: bold;
                    display: flex;
                    align-items: center;
                    margin-bottom: 10px;
                    margin-right: 10px;
                    cursor: pointer;
                    background: #eee;
                    color: #5a5b5c;
                    border: 1px solid #ddd;
                }

                    .filterclick-1.active {
                        background: linear-gradient(119deg, #607D8B, #9E9E9E);
                        color: #eee;
                        border: 1px solid transparent;
                    }
            </style>

            <div style="display: flex;">

                <div class="filterclick-1" filtervalue="storage">
                    <svg t="" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="6932" width="23" height="23" style="margin-right: 5px;">
                        <path d="M810.0864 210.7904H261.6832a56.32 56.32 0 0 0-35.4816 99.9424l204.8 166.5536a10.5472 10.5472 0 0 1 3.8912 8.192V803.84a36.4032 36.4032 0 0 0 18.2784 31.5392l113.5104 64.8704a47.0016 47.0016 0 0 0 70.3488-40.96v-373.76a10.5472 10.5472 0 0 1 3.8912-8.192l204.8-166.5536a56.32 56.32 0 0 0-35.6352-99.9936z" fill="#9FA7FF" p-id="6933"></path><path d="M845.6192 310.7328a56.832 56.832 0 0 0 15.7696-20.0192 51.2 51.2 0 0 0-47.3088-30.0544H303.8208A52.5312 52.5312 0 0 0 256 335.104l174.9504 142.1824a10.5472 10.5472 0 0 1 3.8912 8.192v1.6384l26.4192 21.4528a9.9328 9.9328 0 0 1 3.6352 7.6288v294.7584a33.9456 33.9456 0 0 0 16.9984 29.3888l105.6256 60.3648a45.4656 45.4656 0 0 0 12.1344 4.7104 46.8992 46.8992 0 0 0 37.3248-46.08v-373.76a10.5472 10.5472 0 0 1 3.8912-8.192z" fill="#8891FF" p-id="6934"></path><path d="M818.432 316.2112H350.72A48.0768 48.0768 0 0 0 303.5136 373.76l127.4368 103.5264a10.5472 10.5472 0 0 1 3.8912 8.192v9.0112l60.2112 48.9984a9.0624 9.0624 0 0 1 3.328 7.0144V819.2a30.72 30.72 0 0 0 15.36 26.88L610.7648 901.12h0.3584a46.5408 46.5408 0 0 0 25.6-41.8816v-373.76a10.5472 10.5472 0 0 1 3.8912-8.192l194.56-158.1056a47.9744 47.9744 0 0 0-16.7424-2.9696z" fill="#6E75FF" p-id="6935"></path><path d="M559.9744 937.3184a82.432 82.432 0 0 1-40.96-11.0592l-125.8496-71.68a71.1168 71.1168 0 0 1-35.84-61.44V446.6176l-220.16-178.8928A93.0816 93.0816 0 0 1 196.096 102.4H803.84a93.0816 93.0816 0 0 1 58.88 165.3248l-219.904 178.8928v407.7568a81.92 81.92 0 0 1-41.2672 71.68 82.8416 82.8416 0 0 1-41.5744 11.264zM196.096 163.84a31.6416 31.6416 0 0 0-19.968 56.32l226.9696 184.32a42.2912 42.2912 0 0 1 15.6672 32.9216v355.072a9.5744 9.5744 0 0 0 4.8128 8.2944l125.7984 71.936a21.3504 21.3504 0 0 0 32-18.5344V437.6064a42.1376 42.1376 0 0 1 15.6672-33.1264l226.9184-184.32A31.6416 31.6416 0 0 0 803.84 163.84z" fill="#2E3138" p-id="6936"></path><path d="M889.7536 527.7696h-175.7696a30.72 30.72 0 0 1 0-61.44h175.7696a30.72 30.72 0 0 1 0 61.44zM889.7536 652.2368h-175.7696a30.72 30.72 0 0 1 0-61.44h175.7696a30.72 30.72 0 0 1 0 61.44zM889.7536 773.6832h-175.7696a30.72 30.72 0 0 1 0-61.44h175.7696a30.72 30.72 0 0 1 0 61.44z" fill="#2E3138" p-id="6937"></path></svg>7日前数据
                </div>

            </div>
            <input id="filtervalue" type="hidden" />
            <script>
                $('.filterclick-1').on('click', function () {
                    filtervalue = $(this).attr('filtervalue');
                    if ($(this).hasClass("active")) {
                        $(this).removeClass("active");
                        filtervalue = "";
                    } else {
                        $(this).addClass("active").siblings().removeClass("active");
                    }
                    $('#filtervalue').val(filtervalue);
                    searchList();
                });
            </script>


            <div class="row layui-form">



                <div class="col-md-3">
                    <div class="form-group form-buttom">
                        <input type="text" id="search-keyword" name="ordername" class="form-control" placeholder="请输入搜索的信息">
                    </div>
                </div>

                <div class="col-md-3">
                    <div class="form-group form-buttom" id="twoInputs">
                        <input type="text" id="start_time" name="ordername" class="form-control" placeholder="开始时间" style="width: 48%; display: inline-block;" value="<%=DateTime.Now.ToString("yyyy-MM-dd") %>" autocomplete="off">
                        <input type="text" id="end_time" name="ordername" class="form-control" placeholder="结束时间" style="width: 48%; display: inline-block;" autocomplete="off" value="<%=DateTime.Now.ToString("yyyy-MM-dd") %>">

                        <script>
                            $(document).ready(function () {
                                $('#start_time').datepicker({
                                    format: 'yyyy-mm-dd',
                                    autoclose: true
                                });

                                $('#end_time').datepicker({
                                    format: 'yyyy-mm-dd',
                                    autoclose: true
                                });
                            });
                        </script>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-group form-buttom">

                        <a class="btn btn-success" onclick="searchList()">
                            <svg t="1688237921441" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="1949" width="16" height="16">
                                <path d="M361.130667 658.688a256 256 0 1 1 282.794666-44.672 42.496 42.496 0 0 0-10.368 14.933333c-0.853333 0.682667-1.621333 1.450667-2.389333 2.218667a42.666667 42.666667 0 0 0 0 60.330667l170.666667 170.666666a42.666667 42.666667 0 0 0 60.330666-60.330666l-142.762666-142.762667a341.333333 341.333333 0 1 0-169.344 99.242667 42.666667 42.666667 0 0 0-20.224-82.901334 254.762667 254.762667 0 0 1-168.704-16.725333z" fill="#14101C" p-id="1950"></path></svg>
                            查询</a>
                        <a class="btn btn-success" onclick="javascript:location.href=location.href;">
                            <svg t="1688237949108" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2211" width="16" height="16">
                                <path d="M512 192a298.24 298.24 0 0 0-245.162667 128 42.666667 42.666667 0 1 1-69.973333-48.810667 384.042667 384.042667 0 0 1 697.173333 180.48l36.608-34.645333a42.666667 42.666667 0 0 1 58.709334 61.952l-112.64 106.666667a42.666667 42.666667 0 0 1-71.253334-38.912A298.666667 298.666667 0 0 0 512 192z m0.170667 640a298.24 298.24 0 0 0 245.162666-128 42.666667 42.666667 0 1 1 69.973334 48.810667 384.042667 384.042667 0 0 1-697.173334-180.48l-36.650666 34.645333a42.666667 42.666667 0 0 1-58.666667-61.952l112.64-106.666667a42.666667 42.666667 0 0 1 71.253333 38.912 298.666667 298.666667 0 0 0 293.418667 354.730667z" fill="#14101C" p-id="2212"></path></svg>
                            刷新</a>


                        <a class="btn btn-success" onclick="report_exl()">
                            <svg t="1695489540076" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="5479" width="16" height="16">
                                <path d="M686.08 989.866667H165.546667c-51.2 0-92.16-40.96-92.16-92.16V126.293333c0-51.2 40.96-92.16 92.16-92.16h573.44c6.826667 0 11.946667 1.706667 17.066666 6.826667l187.733334 174.08c5.12 5.12 8.533333 11.946667 8.533333 18.773333v256c0 13.653333-11.946667 25.6-25.6 25.6s-25.6-11.946667-25.6-25.6V244.053333L728.746667 85.333333h-563.2C141.653333 85.333333 122.88 104.106667 122.88 126.293333v769.706667c0 22.186667 18.773333 40.96 40.96 40.96h520.533333c13.653333 0 25.6 11.946667 25.6 25.6S699.733333 989.866667 686.08 989.866667z" fill="#333333" p-id="5480"></path><path d="M919.893333 259.413333h-121.173333c-51.2 0-92.16-40.96-92.16-92.16V59.733333c0-13.653333 11.946667-25.6 25.6-25.6s25.6 11.946667 25.6 25.6v105.813334c0 22.186667 18.773333 40.96 40.96 40.96h121.173333c13.653333 0 25.6 11.946667 25.6 25.6s-11.946667 27.306667-25.6 27.306666zM500.053333 305.493333H208.213333c-13.653333 0-25.6-11.946667-25.6-25.6s11.946667-25.6 25.6-25.6h291.84c13.653333 0 25.6 11.946667 25.6 25.6s-11.946667 25.6-25.6 25.6zM616.106667 575.146667H208.213333c-13.653333 0-25.6-11.946667-25.6-25.6s11.946667-25.6 25.6-25.6h407.893334c13.653333 0 25.6 11.946667 25.6 25.6s-11.946667 25.6-25.6 25.6zM380.586667 440.32H208.213333c-13.653333 0-25.6-11.946667-25.6-25.6s11.946667-25.6 25.6-25.6h172.373334c13.653333 0 25.6 11.946667 25.6 25.6s-11.946667 25.6-25.6 25.6z" fill="#333333" p-id="5481"></path><path d="M914.773333 779.946667H600.746667c-13.653333 0-25.6-11.946667-25.6-25.6s11.946667-25.6 25.6-25.6h314.026666c13.653333 0 25.6 11.946667 25.6 25.6s-11.946667 25.6-25.6 25.6z" fill="#333333" p-id="5482"></path><path d="M831.146667 873.813333c-6.826667 0-13.653333-1.706667-18.773334-6.826666-10.24-10.24-10.24-25.6 0-35.84l75.093334-75.093334-75.093334-75.093333c-10.24-10.24-10.24-25.6 0-35.84 10.24-10.24 25.6-10.24 35.84 0l93.866667 93.866667c5.12 5.12 6.826667 11.946667 6.826667 18.773333s-3.413333 13.653333-6.826667 18.773333L848.213333 870.4c-5.12 1.706667-11.946667 3.413333-17.066666 3.413333z" fill="#333333" p-id="5483"></path><path d="M694.613333 824.32h-529.066666c-37.546667 0-68.266667-30.72-68.266667-68.266667V121.173333c0-37.546667 30.72-68.266667 68.266667-68.266666h529.066666c37.546667 0 68.266667 30.72 68.266667 68.266666v634.88c0 37.546667-30.72 68.266667-68.266667 68.266667z" fill="#333333" opacity=".3" p-id="5484"></path></svg>导出</a>

                    </div>
                </div>
            </div>

            <div style="display: flex; flex-wrap: wrap; margin-bottom: 20px;">
                <div class="timesel_lab">全部</div>
                <div class="timesel_lab active">今天</div>
                <div class="timesel_lab">昨天</div>
                <div class="timesel_lab">本周</div>
                <div class="timesel_lab">上周</div>
                <div class="timesel_lab">本月</div>
                <div class="timesel_lab">上月</div>
            </div>




            <div style="margin-bottom: 10px;">
                <input type="checkbox" id="filter_share" checked="checked" />&nbsp;过滤晒单
                <input type="checkbox" id="filter_htmlmsg" />&nbsp;过滤系统消息
            </div>


            <div class="table-responsive">
                <table class="table">
                    <thead>
                        <tr role="head">
                            <th sort="true">#ID</th>
                            <th sort="true">消息来源</th>
                            <th sort="true">发送对象</th>
                            <th sort="true">消息内容</th>
                            <th sort="true">发送时间</th>
                            <th sort="true">编辑</th>
                        </tr>
                    </thead>
                    <tbody id="list-display">
                    </tbody>
                </table>
            </div>



            <div class="layui-table-page">
                <div id="layui-table-page1">
                    <div class="layui-box layui-laypage layui-laypage-default" id="layui-laypage-1">
                        <div style="display: inline-block;" class="cpages">
                            <!--page grow -->
                        </div>
                        <span class="layui-laypage-skip">到第<input type="text" min="1" value="1" class="layui-input target-page" />页
                            <button type="button" class="layui-laypage-btn" onclick="javascript:pager_index=$('.target-page').val()-1;getPager();">确定</button></span>
                        <span class="layui-laypage-count">共 <span class="page_count"></span>条</span>
                        <span class="layui-laypage-limits">
                            <select class="s_pages" lay-ignore="" onchange="javascript:pager_size=$(this).val();getPager();">
                                <option value="10">10 条/页</option>
                                <option value="20" selected="">20 条/页</option>
                                <option value="30">30 条/页</option>
                                <option value="50">50 条/页</option>
                                <option value="100">100 条/页</option>
                                <option value="200">200 条/页</option>
                                <option value="500">500 条/页</option>
                                <option value="1000">1000 条/页</option>
                                <option value="2000">2000 条/页</option>
                            </select></span>
                    </div>
                </div>
            </div>

        </div>
    </div>
    <script>
        var _action = 'chat_messages';
    </script>
    <script>
        function openuser() {
            var cg = [];

            cg.push({ name: '黑名单', id: 'black_list', value: '', type: 'textarea' });

            edit({ aname: 'admin', title: '黑名单添加', action: _action, id: '', data: cg });
        }
    </script>
    <script>

        var pager_size = $(".s_pages").val();
        var pager_index = 0;
        var pager_key = "";

        var opt = get_param("opt");
        var opt2 = get_param("fid");

        $("#pagesize").val(pager_key);
        function setPageSize(obj) {
            pager_index = 0;
            pager_key = $("#search-keyword").val();
            opt = $(obj).val();
            getPager();
        }

        function searchList() {
            opt = $("#opt1").val();
            pager_index = 0;
            pager_key = $("#search-keyword").val();
            getPager();
        }

        function getParam() {
            return { limit: pager_size, p: pager_index, page: _action, keyword: pager_key, opt: opt, opt2: opt2, serv_id: get_param("serv_id"), start_time: $("#start_time").val(), end_time: $("#end_time").val(), state: $("#state").val(), uid: '<%=Request.QueryString["userid"] %>', type: $("#typeid").val(), filter_share: $('#filter_share').prop('checked'), filter_htmlmsg: $('#filter_htmlmsg').prop('checked'), is_black: '<%=Request.QueryString["is_black"] %>', is_delf: '<%=Request.QueryString["is_delf"] %>', filtervalue: $('#filtervalue').val() };
        }

        function getPager() {

            $.ajax({
                type: "POST",
                url: "../api/admin.aspx?do=page",
                data: getParam(),
                datatype: "json",
                success: function (json) {
                    $("#list-display").html("");
                    if (json.code == 1) {
                        var data = json.data.list;
                        for (var i = 0; i < data.length; i++) {

                            var card_data = "";
                            var faker_user = "";
                            if (data[i].msgtype == 'card') {
                                var card_list = getTextBetweenStrings(data[i].msgs, "[卡片=", "]");
                                if (card_list.length > 0) {
                                    var cdata = {};
                                    try {
                                        cdata = JSON.parse(card_list[0]);
                                    } catch (e) {

                                    }
                                }

                                try {
                                    card_data = cdata.name + " ~ " + cdata.ranking_number + "名，" + cdata.daily_count + "单，" + parseFloat(cdata.daily_award_amount).toFixed(2) + "佣，" + parseFloat(cdata.reward_amount).toFixed(2) + "总";

                                    faker_user = cdata.name;
                                } catch (e) {

                                }
                            }

                            var tr = "";
                            tr += "<tr>";
                            tr += ("<td># " + data[i].id + "</td>");
                            tr += ("<td class=''>" + (data[i].type == 'group' ? '<span style="font-weight: bold; font-size: 13px; text-shadow: 5px 5px 5px #cd272763; color: #ff4747;margin-right:10px;">群</span>' : data[i].type == 'friend' ? '<span style="font-weight: bold; font-size: 13px; text-shadow: 5px 5px 5px #4faf6e7d; color: #4faf6e;">好友</span>' : "<span class='button-style '>未知</span>") + '<span style="color:blue;margin-right:10px;">' + data[i].name + '</span>' + (data[i].group_name == '-' ? '' : "<span class='button-style style-js'>" + data[i].group_name + "</span>") + data[i].phone + (data[i].fromuser == -1 ? '<span style="border-radius: 10px;font-size: 12px;font-weight: bold;display: inline-block;padding: 0 5px;background: #61736b;color: #e1e1e1;margin:0 6px;">假</span>' + faker_user : '') + (data[i].fromuser == -1001 ? '<span style="border-radius: 10px;font-size: 12px;font-weight: bold;display: inline-block;padding: 0 5px;background: #575150;color: #dfdc50;margin:0 6px;">红包雨</span>' : '') + '<img src="' + data[i].avatar + '" style="width:13px;height:13px;margin-left:5px;margin-right:2px;">' + (data[i].username == "" ? data[i].parent_code : data[i].username) + "</td>");
                            tr += ("<td>" + (data[i].to_group_name == '-' ? '' : "<span class='button-style style-js'>" + data[i].to_group_name + "</span>") + data[i].to_phone + "</td>");
                            tr += ("<td style='color:gray;display:flex;'>" + (data[i].is_black == 0 ? "" : "<span class='button-style style-hs'>屏蔽</span>") + (data[i].msgtype == 'text' ? data[i].msgs : data[i].msgtype == 'card' ? '<span style="color:#71600a;">' + '[晒单] ' + card_data + '</span>' : data[i].msgtype == 'html' ? '<span style="color: #710a0a;font-weight: bold;margin-right: 10px;">' + '[系统消息] ' + '</span>' + "<span style='color:#6d6c75;'>" + data[i].msgs + '</span>' : data[i].msgtype == 'redbag' ? '<a style="display:flex;color:#d73c1e;"><svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" t="1608100632456" class="icon" viewBox="0 0 1024 1024" version="1.1" p-id="14864" width="16" height="16"><defs><style type="text/css"></style></defs><path d="M57.046912 0m76.8 0l716.8 0q76.8 0 76.8 76.8l0 870.4q0 76.8-76.8 76.8l-716.8 0q-76.8 0-76.8-76.8l0-870.4q0-76.8 76.8-76.8Z" fill="#D73C1E" p-id="14865"></path><path d="M850.646912 0a76.8 76.8 0 0 1 76.8 76.8l0.0256 275.8144c-122.2912 51.968-272.64 82.5856-435.2 82.5856-162.5856 0-312.96-30.6432-435.2512-82.5856L57.046912 76.8a76.8 76.8 0 0 1 76.8-76.8h716.8z" fill="#F14C2E" p-id="14866"></path><path d="M517.846912 409.6m-153.6 0a153.6 153.6 0 1 0 307.2 0 153.6 153.6 0 1 0-307.2 0Z" fill="#F7D49A" p-id="14867"></path><path d="M528.906112 512h-21.9136v-51.2h-65.7664v-20.48h65.7664V409.6h-65.7664v-20.48h53.504L441.046912 318.3104l18.816-11.1104 54.3232 71.68h12.4672l48.5632-67.84 18.2272 11.392-47.7696 66.688H594.646912v20.48h-63.6416l-2.0992 2.944V440.32H594.646912v20.48h-65.7408v51.2z" fill="#E98337" p-id="14868"></path></svg>&nbsp;红包:' + data[i].msgs.split('$$')[1] + '</a>' : data[i].msgtype == 'img' ? '<img src="' + data[i].msgs.split('=')[1].replace(/]/g, '') + '" width="25" height="25" onclick="toggleImgFullScreen(\'' + data[i].msgs.split('=')[1].replace(/]/g, '') + '\')">' : "<span class='button-style '>未知</span>") + (data[i].delf == 0 ? '' : '<span style="background: #f9ecec;border: 1px solid #f7d6d6;color: #772929;font-size:12px;border-radius: 3px;padding: 0px 5px;margin-left: 5px; display: inline-block;">已删除:' + (data[i].delf == 1 ? '管理员' : data[i].delf == 2 ? '前台客服' : '其他') + '</span>') + "</td>");
                            tr += ("<td>" + data[i].create_time + "</td>");

                            //tr += ("<td>"
                            //    + "<button type='button' class='el-button  el-button--danger el-button--mini' onclick=\"event_create({'aname':'admin','name':'" + _action + "','event':'delete','id':" + data[i].id + "})\"><span>删除</span></button>"
                            //    + "</td>");





                            tr += ("<td class='action-cell '>"
                                + ' <div class="card-toolbar">'

                                + (data[i].chat_nospeak != 1 ? '<a class="card-button common-button" ' + "onclick=\"" + "get_action({name:'event_create',data:{id:" + data[i].fromuser + ",aname:'admin',name:'accounts',event:'chat_speak',chat_nospeak:1},n:'禁言用户',e:'禁言用户 " + data[i].phone + "'});" + "\"" + '  style="color:#888!important;"><svg t="*************" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="6718" width="12" height="12"><path d="M0 0h1024v1024H0z" fill="#FFFFFF" fill-opacity="0" p-id="6719"></path><path d="M825.728 588.8a180.48 180.48 0 0 1-25.6-93.141333v-112.213334c0-33.109333-5.76-66.005333-17.066667-97.109333l93.184-93.184a32 32 0 1 0-45.269333-45.269333l-682.666667 682.666666a32 32 0 1 0 45.226667 45.269334l75.946667-75.946667h126.805333c-0.341333 3.584-1.408 7.04-1.408 10.666667a117.333333 117.333333 0 1 0 234.666667 0c0-3.626667-1.066667-7.082667-1.408-10.666667H797.866667a87.424 87.424 0 0 0 74.837333-132.266667L825.728 588.8z m-260.266667 221.866667a53.333333 53.333333 0 0 1-106.666666 0c0.085333-3.584 0.554667-7.168 1.408-10.666667h103.850666c0.810667 3.498667 1.237333 7.082667 1.28 10.666667h0.128z m252.757334-86.442667a22.784 22.784 0 0 1-20.224 11.733333H333.226667l398.037333-398.037333c3.157333 15.018667 4.693333 30.293333 4.736 45.610667v112.213333a244.650667 244.650667 0 0 0 34.816 126.08l46.933333 78.506667c4.522667 7.253333 4.650667 16.469333 0.341334 23.893333h0.128z m-652.8-80.170667l32.853333-55.04c16.853333-28.16 25.728-60.416 25.6-93.269333v-112.213333a288.554667 288.554667 0 0 1 466.474667-226.133334 32 32 0 1 1-39.509334 50.346667 224.512 224.512 0 0 0-362.965333 175.786667v112.213333a244.266667 244.266667 0 0 1-34.901333 126.165333l-32.768 54.912a32 32 0 1 1-54.954667-32.725333l0.170667-0.042667z" fill="#41416E" p-id="6720"></path></svg>&nbsp;禁言</a>' : '<a class="card-button common-button" ' + "onclick=\"" + "get_action({name:'event_create',data:{id:" + data[i].fromuser + ",aname:'admin',name:'accounts',event:'chat_speak',chat_nospeak:0},n:'用户解除禁言',e:'解除禁言用户 " + data[i].phone + "'});" + "\"" + ' style="color:red!important;"><svg t="*************" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="6718" width="12" height="12"><path d="M0 0h1024v1024H0z" fill="#FFFFFF" fill-opacity="0" p-id="6719"></path><path d="M825.728 588.8a180.48 180.48 0 0 1-25.6-93.141333v-112.213334c0-33.109333-5.76-66.005333-17.066667-97.109333l93.184-93.184a32 32 0 1 0-45.269333-45.269333l-682.666667 682.666666a32 32 0 1 0 45.226667 45.269334l75.946667-75.946667h126.805333c-0.341333 3.584-1.408 7.04-1.408 10.666667a117.333333 117.333333 0 1 0 234.666667 0c0-3.626667-1.066667-7.082667-1.408-10.666667H797.866667a87.424 87.424 0 0 0 74.837333-132.266667L825.728 588.8z m-260.266667 221.866667a53.333333 53.333333 0 0 1-106.666666 0c0.085333-3.584 0.554667-7.168 1.408-10.666667h103.850666c0.810667 3.498667 1.237333 7.082667 1.28 10.666667h0.128z m252.757334-86.442667a22.784 22.784 0 0 1-20.224 11.733333H333.226667l398.037333-398.037333c3.157333 15.018667 4.693333 30.293333 4.736 45.610667v112.213333a244.650667 244.650667 0 0 0 34.816 126.08l46.933333 78.506667c4.522667 7.253333 4.650667 16.469333 0.341334 23.893333h0.128z m-652.8-80.170667l32.853333-55.04c16.853333-28.16 25.728-60.416 25.6-93.269333v-112.213333a288.554667 288.554667 0 0 1 466.474667-226.133334 32 32 0 1 1-39.509334 50.346667 224.512 224.512 0 0 0-362.965333 175.786667v112.213333a244.266667 244.266667 0 0 1-34.901333 126.165333l-32.768 54.912a32 32 0 1 1-54.954667-32.725333l0.170667-0.042667z" fill="#41416E" p-id="6720"></path></svg>&nbsp;解禁</a>')

                                + (data[i].delf == 0 ? '&nbsp;<a class="card-button common-button" ' + "onclick=\"" + "get_action({name:'event_create',data:{id:" + data[i].id + ",aname:'admin',name:'" + _action + "',event:'delete'},n:'删除聊天',e:'删除用户聊天 " + data[i].phone + "'});" + "\"" + '><svg t="1688237896673" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="1699" width="16" height="16" style="position:relative;top:3px;"><path d="M612.693333 170.666667l8.533334 42.666666H402.773333l8.533334-42.666666h201.386666z m-293.632 25.941333a86.186667 86.186667 0 0 0-1.706666 16.725333H170.666667a42.666667 42.666667 0 0 0 0 85.333334h682.666666a42.666667 42.666667 0 1 0 0-85.333334h-146.730666c0-5.504-0.554667-11.093333-1.706667-16.725333l-8.533333-42.666667A85.333333 85.333333 0 0 0 612.778667 85.333333H411.306667a85.333333 85.333333 0 0 0-83.712 68.608l-8.533334 42.666667zM426.666667 469.333333a42.666667 42.666667 0 0 0-42.666667 42.666667v170.666667a42.666667 42.666667 0 1 0 85.333333 0v-170.666667a42.666667 42.666667 0 0 0-42.666666-42.666667z m128 42.666667a42.666667 42.666667 0 1 1 85.333333 0v170.666667a42.666667 42.666667 0 1 1-85.333333 0v-170.666667zM255.402667 384A42.666667 42.666667 0 0 1 298.666667 426.069333L301.738667 640H301.653333a42.666667 42.666667 0 1 1-85.290666-2.176l-2.986667-210.56A42.666667 42.666667 0 0 1 255.36 384z m51.925333 396.586667h0.042667A103.381333 103.381333 0 0 0 406.698667 853.333333h210.602666c54.186667 0 98.56-40.789333 103.210667-92.757333L725.333333 426.069333a42.666667 42.666667 0 0 1 85.333334 1.194667l-4.864 337.365333-0.128 1.365334C798.08 863.872 715.562667 938.666667 617.301333 938.666667H406.698667a188.885333 188.885333 0 0 1-180.096-130.389334 42.666667 42.666667 0 0 1 80.725333-27.648z" fill="#14101C" p-id="1700"></path></svg>&nbsp;删除</a>' : '')
                                + '</div></td>');










                            tr += "</tr>";
                            $("#list-display").append(tr)
                        }
                        $(".page_size").html(pager_size);
                        $(".page_count").html(json.data.count);
                        $(".cpages").html(json.data.html);
                    } else {

                    }
                },
                error: function () {
                }
            });
        }
        getPager();

        function _pt(i) {
            pager_index = i;
            getPager();
        }
    </script>

    <script>
        layui.use(['form', 'layedit', 'laydate'], function () {
        });


        function report_exl() {
            export_data(getParam());
        }



        function getTextBetweenStrings(inputString, startText, endText) {
            var results = [];
            var startIndex = 0;

            while (startIndex < inputString.length) {
                var startIdx = inputString.indexOf(startText, startIndex);
                if (startIdx === -1) {
                    break; // 如果找不到起始文本，结束循环
                }

                var endIdx = inputString.indexOf(endText, startIdx + startText.length);
                if (endIdx === -1) {
                    break; // 如果找不到结束文本，结束循环
                }

                var textBetween = inputString.substring(startIdx + startText.length, endIdx);
                results.push(textBetween);

                // 更新 startIndex，继续搜索下一个匹配
                startIndex = endIdx + endText.length;
            }

            return results;
        }

    </script>



</asp:Content>


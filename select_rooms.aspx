<%@ Page Title="" Language="C#" MasterPageFile="~/top.master" AutoEventWireup="true" CodeFile="select_rooms.aspx.cs" Inherits="notice_details" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="Server">
    <script>
        $(function () {
            popTitle('<b>选择游戏房间</b>', '');
        })
    </script>

    <style>
        .main-container {
            padding: 0px;
        }

        img {
            max-width: 100%;
        }

        .shake_lab {
            background: #d9d1c9;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 13px;
            color: #773030;
            margin:0 3px;
        }
    </style>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="Server">


    <h1 style="text-align:center;">正在跳转中</h1>

  <%--  <div>
        <div style="background: #eee; color: #d94d4d; font-size: 15px; padding: 5px 10px;">请选择房间进入，如有疑问请联系客服</div>
    </div>

    
    <div id="roomLists" class="flex_grow" style="display: block;padding:18px;text-align:center;">
        <div style="background: #64e964;color: #127512;font-weight: bold;padding: 18px 15px;border-radius: 3px;max-width:220px;margin:0 auto;" onclick="tp('当前游戏维护中~请耐心等待恢复！')" >
            <span>当前游戏维护中~</span>
            <div>请耐心等待恢复！</div>
        </div>

        <div style="background: #64e964;color: #127512;font-weight: bold;padding: 18px 15px;border-radius: 3px;max-width:220px;margin:0 auto;margin-top:18px;cursor:pointer;" onclick="open_auth('')">
            <span>房间二</span>
            <div>账户余额上分模式</div>
        </div>
    </div>--%>


    <style>
        .menu {
            background: #ffffff70;
            margin-top: 0;
            border-radius: 0;
            border-bottom: 1px solid #f5f5f5;
        }

        .menu-list div {
            margin-left: 0;
            font-weight: 100;
            text-align: left;
        }
    </style>
    <script>
        $(function () {
            $('.ppreturn').on('click', function () {
                var from = get_param('from');
                switch (from) {
                    case "onetouch":
                        location.href = 'onetouch_balance.aspx';
                        break;
                    case "index":
                        location.href = 'index.aspx';
                        break;
                    default:
                        location.href = 'chat.aspx';
                        break;
                }
                return false;
            })
        })

        var open_auth = function (url) {
            location.href = get_param('u') + url;
        }

        open_auth('');

    </script>
</asp:Content>


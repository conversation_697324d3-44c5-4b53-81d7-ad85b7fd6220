<%@ Application Language="C#" %>
<script runat="server">
    public static bool inRuning = false;
    public static bool startupTaskRuning = false;

    void Application_Start(object sender, EventArgs e)
    {

        if (!inRuning)
        {
            System.Timers.Timer myTimer = new System.Timers.Timer(60000);//修改时间间隔
            //关联事件
            myTimer.Elapsed += new System.Timers.ElapsedEventHandler(mess);
            myTimer.AutoReset = true;
            myTimer.Enabled = true;

            inRuning = true;
        }

    }
    private void mess(object sender, System.Timers.ElapsedEventArgs e)
    {
        int Minute = DateTime.Now.Minute;
        int hour = DateTime.Now.Hour;
        string errmsg = string.Empty;

        //每一分钟查询处理下任务

        dbClass db = new dbClass();
        System.Data.DataRow[] dr;
        System.Data.DataTable dt = new System.Data.DataTable();
        System.Data.DataSet ds = new System.Data.DataSet();

        string sql = "";
        string str = "";
        int res = 0;
        string[] g;
        LitJson.JsonData jd;
        System.Data.SqlClient.SqlParameter[] parames = null;

        globalClass gc = new globalClass();
        Dictionary<string, object> dic = new Dictionary<string, object>();

        fuzhu fz = new fuzhu(HttpContext.Current);


        //定时开启任务
        if (!startupTaskRuning)
        {
            startupTaskRuning = true;
            try
            {
                //sql = string.Empty;

                //res = db.ExecuteNonQuery(sql);
            }
            catch (Exception ex)
            {
                errmsg = " 出错：" + ex.ToString() + "|sql:" + sql;
            }
            //sqlrecord("app执行结果", "操作成功：" + errmsg);

            startupTaskRuning = false;
        }

    }

    void Application_End(object sender, EventArgs e)
    {
        try
        {
            startupTaskRuning = false;
            inRuning = false;
            System.Threading.Thread.Sleep(1000);
            string url = "http://baidu.com/";
            System.Net.HttpWebRequest myHttpWebRequest = (System.Net.HttpWebRequest)System.Net.WebRequest.Create(url);
            System.Net.HttpWebResponse myHttpWebResponse = (System.Net.HttpWebResponse)myHttpWebRequest.GetResponse();
            System.IO.Stream receiveStream = myHttpWebResponse.GetResponseStream();
        }
        catch (Exception)
        {
        }
    }

    void Application_Error(object sender, EventArgs e)
    {
        // 在出现未处理的错误时运行的代码

    }

    void Session_Start(object sender, EventArgs e)
    {
        // 在新会话启动时运行的代码
    }

    void Session_End(object sender, EventArgs e)
    {
        // 在会话结束时运行的代码。 
        // 注意: 只有在 Web.config 文件中的 sessionstate 模式设置为
        // InProc 时，才会引发 Session_End 事件。如果会话模式设置为 StateServer 
        // 或 SQLServer，则不会引发该事件。
    }

</script>

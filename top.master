<%@ Master Language="C#" AutoEventWireup="true" CodeFile="top.master.cs" Inherits="top" %>

<!DOCTYPE html>

<html xmlns="http://www.w3.org/1999/xhtml">
<head runat="server">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title><%=uConfig.stcdata("sitename")%></title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <link href="../static/css/index.css?v=1.1" rel="stylesheet" />

    <script src="../static/js/jquery.min.js"></script>
    <link href="static/css/swiper-bundle.min.css" rel="stylesheet" />

    <script src="../js/qrcode.min.js"></script>
    <script src="../js/clipboard.min.js"></script>

    <script>

        function parseNumber(t) {
            if (isNaN(t) || t == "") {
                return 0.00;
            }
            return parseFloat(t);
        }

        function get_param(paramName) {
            var urlParams = new URLSearchParams(window.location.search);
            return urlParams.get(paramName) || "";
        }

        var __menu = function (name) {
            if ($('#indexpage').val() != undefined) {
                name == "index";
            }
            $('[icon="' + name + '"]').addClass('active').siblings().removeClass('active');
        }
        $(function () {
            var arr = location.pathname.split('/');
            arr = arr[arr.length - 1].split('.');
            __menu(arr[0]);
        })

        var popTitle = function (_title, _prhtml, _lastUrl) {
            $('.wxlist_index').hide();
            $('.top-title').show();
            $('.pptitle').html(_title);
            if (typeof (_prhtml) == "string") {
                $('.prhtml').html(_prhtml);
            }
            if (typeof (_lastUrl) == "string") {
                $('.ppreturn').on('click', function () {
                    location.href = _lastUrl;
                })
            }
        }

        //提示框
        var potip_timer = -1;
        var tp = function (title) {
            clearTimeout(potip_timer);
            $('.poptip-content').html(title);
            $('.poptip-content').stop();
            $('.poptip-content').css({ opacity: 1 }, 10)
            potip_timer = setTimeout(function () {
                $('.poptip-content').animate({ opacity: 0 }, 200)
            }, 3000);
        }

        var v3api = function (type, pm, json_callback,error_callback) {
            var data = {};
            try {
                data = pm["data"];
            } catch (e) {

            }
            $.ajax({
                url: '../api/v3.aspx?type=' + type,
                type: "POST",
                data: data,
                datatype: "json",
                success: function (json) {
                    try {
                        if (!pm["error"]) {
                            if (json.code != 1) {
                                tp(json.msg);
                                return;
                            }
                        }
                    } catch (e) {

                    }
                    json_callback(json);
                },
                error: function (ex) {
                    console.log('异常', ex);
                    //tp('网络异常');
                    try {
                        error_callback(ex);
                    } catch (e) {

                    }
                }
            });
        }

        var v3api_tip = function (type, pm) {
            v3api(type, pm, function (json) {
                tp(json.msg);
            })
        }
        var initTabs = function (cb) {
            $(".gtab").on("click", function () {
                var container = $(".tablist");
                var element = $(this);
                var containerWidth = container.width();

                element.addClass('act').siblings().removeClass("act");


                var containerScrollLeft = container.scrollLeft();
                var lastElementLeft = element.position().left;
                var relativeLeftDistance = lastElementLeft + containerScrollLeft;

                var scrollPosition = relativeLeftDistance - element.width();

                container.animate({ scrollLeft: scrollPosition }, 300);

                try {
                    cb(element.html())
                } catch (e) {

                }


            });
        }


        // 创建一个全局变量来存储事件处理程序
        var customEventHandler = null;

        // 定义一个发布（触发）事件的函数，传递参数
        function publishCustomEvent(data) {
            var event = new CustomEvent('pageChangeEvent', { detail: data });
            if (customEventHandler) {
                customEventHandler(event);
            }
        }

        // 定义一个监听事件的函数，接收参数
        function subscribeToCustomEvent(callback) {
            customEventHandler = callback;
        }

        setInterval(function () {


            var current_local_path = localStorage.getItem("current_local_path");

            var now_path = location.pathname;

            if (current_local_path != "" && current_local_path != null) {

                if (current_local_path != now_path) {
                    publishCustomEvent({
                        last_path: current_local_path,
                        new_path: now_path
                    });
                }

            }

            localStorage.setItem("current_local_path", now_path);

        }, 1000);



        function openNewWindow(url) {
            // 创建一个新的<a>标签元素
            var newLink = document.createElement('a');

            // 设置链接的href属性
            newLink.href = url;

            // 设置链接的文本内容（这里可以根据需要设置）
            newLink.textContent = '';

            // 设置链接的target属性为"_blank"
            newLink.target = '_blank';

            // 将链接添加到文档的<body>元素中
            document.body.appendChild(newLink);

            // 模拟点击链接，打开新窗口
            newLink.click();
        }

    </script>

    <style>
        .wxlist_index {
            display: flex;
            position: fixed;
            bottom: 0;
            left: 0;
            width: 100%;
            box-sizing: border-box;
            background: #fff;
            font-weight: bold;
            z-index:999;
        }

            .wxlist_index .icon_item_index {
                width: 33.33%;
                text-align: center;
                font-size: 12px;
                padding: 10px 0;
            }

                .wxlist_index .icon_item_index:not(.default_color) svg path {
                    fill: currentColor;
                }


            .wxlist_index .icon_item_index {
                color: #333C4F;
            }


                .wxlist_index .icon_item_index.active {
                    color: #07C062;
                }
    </style>

    <asp:ContentPlaceHolder ID="head" runat="server">
    </asp:ContentPlaceHolder>
</head>
<body>
    <form id="form1" runat="server">

        <div class="poptip-div">
            <div class="poptip-content">
                -
            </div>
        </div>

        <div class="top-title" style="position: relative; height: 58px;">
        </div>

        <div class="top-title">

            <a class="ppreturn" style="position: absolute; left: 0; height: 100%; width: 50px; top: 0; display: flex; align-items: center; justify-content: center;" onclick="javascript:history.go(-1);">
                <svg t="" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="27857" width="16" height="16">
                    <path d="M716.8 972.8a51.2 51.2 0 0 1-36.352-14.848l-409.6-409.6a51.2 51.2 0 0 1 0-72.192l409.6-409.6a51.2 51.2 0 0 1 72.192 72.192L379.392 512l373.248 373.248A51.2 51.2 0 0 1 716.8 972.8z" fill="#000" p-id="27858"></path></svg></a>

            <b class="pptitle"></b>

            <div class="prhtml">
                <a style="position: absolute; right: 0; height: 100%; width: 50px; top: 0; display: flex; align-items: center; justify-content: center;" onclick="javascript:history.go(-1);">
                    <svg t="" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="28113" width="16" height="16">
                        <path d="M584.192 512l322.048-322.048a51.2 51.2 0 0 0-72.192-72.192L512 439.808l-322.048-322.56a51.2 51.2 0 0 0-72.704 72.704L439.808 512l-322.56 322.048A51.2 51.2 0 1 0 189.44 906.24l322.56-322.048 322.048 322.048a51.2 51.2 0 0 0 72.192-72.192z" fill="#000" p-id="28114"></path></svg></a>
            </div>
        </div>

        <div class="main-container">



            <!--复制插件 -->
            <style>
                #clip_copy_contents {
                    position: absolute;
                    top: -99px;
                }

                #clip_copy_button {
                    position: absolute;
                    top: -99px;
                }
            </style>
            <textarea id="clip_copy_contents" inputmode="none"></textarea>
            <a id="clip_copy_button"></a>
            <script>
                var copyParames = {
                    text: ''
                };
                new Clipboard('#clip_copy_button', {
                    target: function () {
                        $("#clip_copy_contents").val(copyParames.text);
                        return document.querySelector('#clip_copy_contents');
                    }
                }).on('success', function (e) {
                    tp('复制成功');
                    console.log('Copy success code:', e);
                }).on('error', function (e) {
                    console.log('Copy error code:', e);
                });
                function textCopy(text) {
                    copyParames.text = text;
                    document.getElementById("clip_copy_button").click();
                }
            </script>



            <!--弹出插件 -->
            <div class="popup-container" id="popupContainer">
                <div class="popup-close">×</div>
                <div id="popupContents"></div>
            </div>
            <div class="popup-mask" id="mask"></div>

            <script>
                var popupContainer = document.getElementById("popupContainer");
                var closeButton = document.querySelector(".popup-close");
                var mask = document.getElementById("mask");

                var openButtomPage = function (popupContents, pm) {
                    popupContainer.style.display = "block";
                    mask.style.display = "block";
                    try {
                        if (pm['miss_close']) {
                            $(".popup-close").hide();
                        }
                    } catch (e) {

                    }

                    document.getElementById("popupContents").innerHTML = popupContents;

                    setTimeout(function () {
                        popupContainer.classList.add("popup-show");
                    }, 10);
                }

                closeButton.addEventListener("click", closePopup);
                mask.addEventListener("click", closePopup);

                function closePopup(cb) {
                    popupContainer.classList.remove("popup-show");
                    setTimeout(function () {
                        popupContainer.style.display = "none";
                        mask.style.display = "none";


                        try {
                            cb && cb();
                        } catch (e) {

                        }
                    }, 200);
                }
            </script>
            <script>
                // 基础参数
                var usdt = {
                    return_usdt: '<%=uConfig.stcdata("return_usdt") %>',
                    prices: {
                        app: '<%=uConfig.stcdata("usdt_price") %>',
                        binance: '<%=uConfig.stcdata("binance_price") %>',
                        okex: '<%=uConfig.stcdata("okex_price") %>'
                    }
                }

                var cny = function (amount) {
                    var result = "0.00";
                    try {
                        var _huilv = parseFloat(usdt.prices.app);
                        var _amount = parseFloat(amount);

                        result = (_amount * _huilv).toFixed(2);
                    }
                    catch (e) {

                    }
                    return result;
                }
                var usd = function (amount) {
                    var result = "0.00";
                    try {
                        var _huilv = parseFloat(usdt.prices.app);
                        var _amount = parseFloat(amount);

                        result = (_amount / _huilv).toFixed(2);
                    }
                    catch (e) {

                    }
                    return result;
                }

                var get_rewark = function (e) {
                    var v = parseFloat(e);
                    var r = (v * usdt.return_usdt / 100).toFixed(2);
                    if (isNaN(r)) {
                        r = 0;
                    }
                    return r;
                }

            </script>



            <asp:ContentPlaceHolder ID="ContentPlaceHolder1" runat="server">
            </asp:ContentPlaceHolder>


            <!-- 底部菜单 -->


            <%--<div class="ulk_menus">

                <div style="height: 80px;" class="ulk_block">
                </div>
                <div class="ulk_menu">

               



                    <div class="" onclick="location.href='chat.aspx';"  style="
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 18px;
">

                        

                        <div style="
    position: relative;
">消息
<div style="background: red;color: rgb(255, 255, 255);width: 24px;height: 18px;line-height: 18px;position: absolute;border-radius: 50%;text-align: center;top: -8px;right: -19px;font-size: 12px;display:none;" id="index_unread_number">0</div>
</div>

                    </div>

                         <div class="issel" onclick="location.href='index.aspx';" icon="index">

                        <svg t="1692117449587" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="1420" width="32" height="32">
                            <path class="light_point" d="M714.666667 655.146667m-191.36 0a191.36 191.36 0 1 0 382.72 0 191.36 191.36 0 1 0-382.72 0Z" fill="#FFCA5F" p-id="1421"></path><path d="M320.64 261.333333m-45.013333 0a45.013333 45.013333 0 1 0 90.026666 0 45.013333 45.013333 0 1 0-90.026666 0Z" fill="#FFCA5F" p-id="1422"></path><path d="M810.666667 859.946667h-178.346667a52.693333 52.693333 0 0 1-52.693333-52.48V597.333333H439.466667v209.92a52.48 52.48 0 0 1-52.48 52.48H209.493333a52.693333 52.693333 0 0 1-52.693333-52.48V472.533333a52.693333 52.693333 0 0 1 17.28-38.826666l103.253333-93.866667 42.666667 47.36-99.626667 90.453333v318.293334h155.093334v-209.706667A52.906667 52.906667 0 0 1 428.16 533.333333h162.986667a52.693333 52.693333 0 0 1 52.48 52.693334v209.706666h154.666666V477.653333L509.653333 215.04l-65.92 59.946667-42.666666-47.36 76.586666-69.546667a48 48 0 0 1 64 0l303.573334 275.626667a52.906667 52.906667 0 0 1 17.066666 38.826666v334.933334A52.48 52.48 0 0 1 810.666667 859.946667zM217.173333 481.066667z" fill="#5C1CF7" p-id="1423"></path></svg>

                        <div>首页</div>
                    </div>

                    <a href="order_new.aspx?task=0" style="display: flex; flex-direction: column; align-items: center; justify-content: center; text-decoration: none; font-weight: bold;" icon="order" class="main_button">

                        <svg t="1694109770697" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="6390" width="32" height="32">
                            <path class="light_point" d="M636.373333 663.68m-202.666666 0a202.666667 202.666667 0 1 0 405.333333 0 202.666667 202.666667 0 1 0-405.333333 0Z" fill="#FFCA5F" p-id="6391"></path>
                            <path d="M232.533333 222.506667m-47.786666 0a47.786667 47.786667 0 1 0 95.573333 0 47.786667 47.786667 0 1 0-95.573333 0Z" fill="#FFCA5F" p-id="6392"></path><path d="M789.333333 740.053333H215.466667a46.933333 46.933333 0 0 1-37.333334-75.946666 181.333333 181.333333 0 0 0 38.186667-111.36V411.733333a289.066667 289.066667 0 0 1 11.093333-78.933333l61.44 17.706667a226.986667 226.986667 0 0 0-8.533333 61.226666v141.013334a245.12 245.12 0 0 1-33.28 123.306666h510.72A247.466667 247.466667 0 0 1 725.333333 552.746667V411.733333a222.08 222.08 0 0 0-370.133333-165.546666l-42.666667-47.573334A286.08 286.08 0 0 1 789.333333 411.733333v141.013334a182.826667 182.826667 0 0 0 38.186667 111.36A46.933333 46.933333 0 0 1 789.333333 740.053333zM502.4 881.28a102.613333 102.613333 0 0 1-102.4-102.613333h64a38.613333 38.613333 0 1 0 77.013333 0h64a102.613333 102.613333 0 0 1-102.613333 102.613333z" fill="#5C1CF7" p-id="6393"></path></svg>


                        <div>任务</div>
                    </a>

                    <div onclick="location.href='chat.aspx#friend_book'" icon="">
                        <svg t="" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="14747" width="32" height="32"><path d="M362.666667 213.333333a192 192 0 1 1 0 384 192 192 0 0 1 0-384z m0 74.666667a117.333333 117.333333 0 1 0 0 234.666667 117.333333 117.333333 0 0 0 0-234.666667zM896 410.666667a37.333333 37.333333 0 0 1 3.072 74.538666L896 485.333333H661.333333a37.333333 37.333333 0 0 1-3.072-74.538666L661.333333 410.666667h234.666667zM362.666667 645.333333c110.613333 0 199.658667 40.469333 263.829333 120.682667a37.333333 37.333333 0 1 1-58.325333 46.634667C518.570667 750.634667 451.157333 720 362.666667 720c-88.490667 0-155.904 30.634667-205.504 92.650667a37.333333 37.333333 0 0 1-58.325334-46.634667C163.008 685.802667 252.053333 645.333333 362.666667 645.333333zM896 624a37.333333 37.333333 0 0 1 3.072 74.538667L896 698.666667h-128a37.333333 37.333333 0 0 1-3.072-74.538667L768 624h128z" fill="#000000" p-id="14748"></path></svg>

                        <div>通讯录</div>
                    </div>


                    <div onclick="location.href='account.aspx';" icon="account">
                        <svg t="*************" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2714" width="32" height="32">
                            <path class="light_point" d="M627.2 690.56m-181.333333 0a181.333333 181.333333 0 1 0 362.666666 0 181.333333 181.333333 0 1 0-362.666666 0Z" fill="#FFCA5F" p-id="2715"></path><path d="M125.866667 274.56m-42.666667 0a42.666667 42.666667 0 1 0 85.333333 0 42.666667 42.666667 0 1 0-85.333333 0Z" fill="#FFCA5F" p-id="2716"></path><path d="M524.8 894.506667a34.56 34.56 0 0 1-21.333333-7.466667l-5.12-3.84c-113.28-89.386667-183.466667-144.853333-202.88-160.64-90.453333-73.173333-142.933333-144-165.12-222.72a292.266667 292.266667 0 0 1-8.746667-114.56l64 7.893333a229.973333 229.973333 0 0 0 6.4 89.386667c18.346667 65.28 64 125.866667 143.786667 190.293333 13.013333 10.666667 56.533333 45.226667 189.013333 149.333334 132.48-104.32 176-138.88 189.013333-149.333334 79.786667-64 125.44-125.013333 143.786667-190.293333s7.68-164.053333-62.506667-213.333333a132.906667 132.906667 0 0 0-94.933333-21.333334c-70.826667 9.386667-115.84 34.986667-145.92 82.986667a34.56 34.56 0 0 1-58.666667 0c-30.293333-48.213333-75.306667-73.813333-146.133333-83.2a132.906667 132.906667 0 0 0-94.933333 21.333333l-36.906667-52.48a196.48 196.48 0 0 1 140.373333-32.426666 261.973333 261.973333 0 0 1 166.826667 79.573333 261.973333 261.973333 0 0 1 166.826667-79.573333A196.48 196.48 0 0 1 832 216.533333c96.213333 68.053333 113.066667 192 87.253333 283.306667-21.333333 78.72-74.453333 149.333333-165.12 222.72-19.413333 15.786667-89.6 71.253333-202.88 160.64l-5.12 3.84a34.56 34.56 0 0 1-21.333333 7.466667z" fill="#5C1CF7" p-id="2717"></path></svg>
                        <div>我的</div>
                    </div>


                </div>

            </div>--%>


            <div id="index_menu" class="base_page" style="display: block;">


                <div style="height: 61px;" class="ulk_block">
                </div>
                <div class="wxlist_index" style="background: #fff;">


                    <div class="icon_item_index" onclick="location.href='index.aspx'" icon="index">
                        <div>
                            <svg t="" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="" width="22" height="22">
                                <path d="M675.328 117.717333A425.429333 425.429333 0 0 0 512 85.333333C276.352 85.333333 85.333333 276.352 85.333333 512s191.018667 426.666667 426.666667 426.666667 426.666667-191.018667 426.666667-426.666667c0-56.746667-11.093333-112-32.384-163.328a21.333333 21.333333 0 0 0-39.402667 16.341333A382.762667 382.762667 0 0 1 896 512c0 212.074667-171.925333 384-384 384S128 724.074667 128 512 299.925333 128 512 128c51.114667 0 100.8 9.984 146.986667 29.12a21.333333 21.333333 0 0 0 16.341333-39.402667z m-324.693333 373.013334l174.464-174.485334a21.141333 21.141333 0 0 0-0.192-29.973333 21.141333 21.141333 0 0 0-29.973334-0.192l-208.256 208.256a20.821333 20.821333 0 0 0-6.122666 14.976c0.042667 5.418667 2.133333 10.837333 6.314666 14.997333l211.178667 211.2a21.141333 21.141333 0 0 0 29.973333 0.213334 21.141333 21.141333 0 0 0-0.213333-29.973334l-172.629333-172.629333 374.997333 2.602667a20.693333 20.693333 0 0 0 21.034667-21.034667 21.482667 21.482667 0 0 0-21.333334-21.333333l-379.242666-2.624z" fill="#333C4F" p-id="27768"></path></svg>
                        </div>
                        <span>首页</span>
                    </div>
                    
                    <div class="icon_item_index default_color" onclick="location.href='../dating.aspx'" icon="dating">
                        <div>
                            <svg t="" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="1678" width="22" height="22">
    <path class="light_point" d="M700.16 661.333333m-181.333333 0a181.333333 181.333333 0 1 0 362.666666 0 181.333333 181.333333 0 1 0-362.666666 0Z" fill="#FFCA5F" p-id="1679"></path><path d="M158.506667 232.106667m-42.666667 0a42.666667 42.666667 0 1 0 85.333333 0 42.666667 42.666667 0 1 0-85.333333 0Z" fill="#FFCA5F" p-id="1680"></path><path d="M518.826667 654.08a152.746667 152.746667 0 1 1 152.746666-152.746667 152.96 152.96 0 0 1-152.746666 152.746667z m0-241.493333a88.746667 88.746667 0 1 0 88.746666 88.746666 88.746667 88.746667 0 0 0-88.746666-88.746666z" fill="#5C1CF7" p-id="1681"></path><path d="M310.186667 533.333333H157.866667v-32a360.96 360.96 0 0 1 700.373333-122.666666l-60.16 21.333333A296.96 296.96 0 0 0 223.573333 469.333333h86.613334zM518.826667 862.08A362.666667 362.666667 0 0 1 179.413333 624.213333l60.16-21.333333A296.96 296.96 0 0 0 813.866667 533.333333h-86.613334v-64h152.32v32a361.173333 361.173333 0 0 1-360.746666 360.746667z" fill="#5C1CF7" p-id="1682"></path></svg>
                        </div>
                        <span>买币大厅</span>
                    </div>
                    <div class="icon_item_index default_color" onclick="location.href='../order_new.aspx'" icon="order_new">
                        <div>
                             <svg t="" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="6390" width="22" height="22">
     <path class="light_point" d="M636.373333 663.68m-202.666666 0a202.666667 202.666667 0 1 0 405.333333 0 202.666667 202.666667 0 1 0-405.333333 0Z" fill="#FFCA5F" p-id="6391"></path>
     <path d="M232.533333 222.506667m-47.786666 0a47.786667 47.786667 0 1 0 95.573333 0 47.786667 47.786667 0 1 0-95.573333 0Z" fill="#FFCA5F" p-id="6392"></path><path d="M789.333333 740.053333H215.466667a46.933333 46.933333 0 0 1-37.333334-75.946666 181.333333 181.333333 0 0 0 38.186667-111.36V411.733333a289.066667 289.066667 0 0 1 11.093333-78.933333l61.44 17.706667a226.986667 226.986667 0 0 0-8.533333 61.226666v141.013334a245.12 245.12 0 0 1-33.28 123.306666h510.72A247.466667 247.466667 0 0 1 725.333333 552.746667V411.733333a222.08 222.08 0 0 0-370.133333-165.546666l-42.666667-47.573334A286.08 286.08 0 0 1 789.333333 411.733333v141.013334a182.826667 182.826667 0 0 0 38.186667 111.36A46.933333 46.933333 0 0 1 789.333333 740.053333zM502.4 881.28a102.613333 102.613333 0 0 1-102.4-102.613333h64a38.613333 38.613333 0 1 0 77.013333 0h64a102.613333 102.613333 0 0 1-102.613333 102.613333z" fill="#5C1CF7" p-id="6393"></path></svg>
                        </div>
                        <span>开始搬砖</span>
                    </div>
                    <div class="icon_item_index default_color" onclick="location.href='../chat.aspx'">
                        <div style="position:relative;">
                            <svg t="" class="icon" viewBox="0 0 1601 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="31008" width="22" height="22"><path d="M186.6945686 138.23074871s3.51008995-0.36948349 5.35750608 1.84741491c1.84741615 2.40164076 4.24905691 34.91616207 12.7471707 43.78375935 8.49811381 8.86759729 43.04479239 3.87957344 43.04479238 15.51829492 0 9.23707953-31.96029671 10.34552996-42.86005123 19.21312604-10.89975455 8.86759729-4.61854037 46.55488355-17.91993449 45.07695087-10.34552996-1.10844917-3.1406077-31.03658864-15.88777838-43.04479361-12.7471707-12.00820375-43.78375933-9.60656301-43.78375812-20.50631755 0-10.89975455 31.77555558-4.24905691 42.6753089-16.62674414 10.7150122-12.19294611 6.09647306-45.26169202 16.62674416-45.26169079z" fill="#FFBB5A" p-id="31009"></path><path d="M1350.56664679-124.47180614s2.40164076-0.18474111 3.69483107 1.29319035c1.29319156 1.47793268 2.95586533 23.4621829 8.68285615 29.55865594 5.72698959 6.09647306 29.00443136 2.58638188 29.00443015 10.53027108 0 6.28121418-21.61476676 7.02018113-29.00443015 12.93191306-7.38966338 6.09647306-3.1406077 31.40607213-12.1929461 30.48236405-7.02018113-0.73896697-2.03215729-20.87580105-10.71501222-29.00443136-8.68285493-8.12863033-29.55865596-6.46595652-29.55865593-13.85561994s21.43002563-2.95586533 28.81968899-11.269238 4.24905691-30.66710516 11.26923804-30.66710518z" fill="#E94151" p-id="31010"></path><path d="M1531.61341363 278.44962278s3.51008995-0.36948349 5.54224844 2.03215729 4.24905691 35.47038666 12.93191184 44.52272508c8.68285493 9.0523384 43.599017 3.87957344 43.59901704 15.70303605 0 9.42182189-32.51452133 10.53027107-43.59901704 19.58260947-11.08449569 9.0523384-4.80328148 47.29384929-18.28941795 45.81591663-10.53027107-1.10844917-3.1406077-31.40607213-16.0725195-43.59901702-12.93191183-12.19294611-44.52272507-9.79130535-44.52272508-20.87580101 0-11.08449569 32.3297802-4.24905691 43.22953352-16.99622761 11.26923805-12.7471707 6.65069766-46.1854001 17.18096873-46.18539888z" fill="#FF0000" p-id="31011"></path><path d="M367.0023697-124.47180614z m-36.57883709 0c0 20.13683407 16.44200178 36.57883709 36.57883708 36.57883587s36.57883709-16.44200178 36.5788371-36.57883587S387.13920378-161.05064323 367.0023697-161.05064323c-20.32157643 0-36.57883709 16.44200178-36.57883709 36.57883709M1351.12087139 201.04289173z m-36.02461248 0c0 19.95209295 16.25726065 36.0246125 36.02461248 36.02461246 19.95209295 0 36.20935361-16.25726065 36.20935361-36.02461246 0-19.95209295-16.25726065-36.20935361-36.20935361-36.20935366-19.95209295 0.18474111-36.0246125 16.25726065-36.02461248 36.20935366" fill="#FFEB00" p-id="31012"></path><path d="M349.08243523 179.24338381c0 139.11042487 207.46481633 251.80280027 463.33193117 251.8028003s463.33193237-112.6923754 463.33193241-251.8028003S1068.4662236-72.37467533 812.59910876-72.37467533C556.54725159-72.37467533 349.08243523 40.31770005 349.08243523 179.24338381z" fill="#DB1818" p-id="31013"></path><path d="M1065.8798417 222.47291735z m-168.11485624 0c0 92.92502477 75.37457259 168.11485625 168.11485624 168.11485624 92.92502477 0 168.11485625-75.37457259 168.11485502-168.11485624s-75.37457259-168.11485625-168.11485502-168.11485502c-92.92502477 0-168.11485625 75.18983146-168.11485624 168.11485502" fill="#F4AD51" p-id="31014"></path><path d="M1065.8798417 222.47291735z m-140.77309987 0c0 50.24971466 26.78753175 96.80459822 70.38654995 121.92945615 43.599017 25.12485795 97.1740817 25.12485795 140.77309865 0s70.38654996-71.67974029 70.38654996-121.92945615c0-50.24971466-26.78753175-96.80459822-70.38654996-121.92945493a140.87840167 140.87840167 0 0 0-140.77309865 0c-43.599017 25.12485795-70.38654996 71.67974029-70.38654995 121.92945493" fill="#F4E476" p-id="31015"></path><path d="M1121.85654595 240.94707762h-41.9363432v-29.1891725h41.9363432c7.75914687 0 13.85561991-6.28121418 13.8556199-13.8556199 0-7.75914687-6.28121418-13.85561991-13.8556199-13.85561989h-24.01640754l15.3335526-26.4180495c3.69483231-6.65069766 1.29319156-14.96406911-5.17276496-18.65890138s-14.96406911-1.6626738-18.84364378 4.80328147L1065.8798417 183.4924395l-22.90795831-39.90418468c-3.87957344-6.46595652-12.37768722-8.68285493-18.84364376-4.80328151-6.46595652 3.69483231-8.86759729 12.00820375-5.17276495 18.65890141l15.14881143 26.41804825h-24.01640747c-7.75914687 0-13.85561991 6.28121418-13.85561995 13.85561994 0 7.75914687 6.28121418 13.85561991 13.85561995 13.85561987h41.93634319v29.1891737h-41.93634319c-7.75914687 0-13.85561991 6.28121418-13.85561995 13.85561991 0 7.75914687 6.28121418 13.85561991 13.85561995 13.85561989h41.93634319v25.49434022c0 7.75914687 6.28121418 13.85561991 13.85561987 13.85561989 7.75914687 0 13.85561991-6.28121418 13.85561993-13.85561989v-25.49434022h41.93634319c4.98802263 0 9.60656301-2.58638188 12.00820374-7.02018106 2.40164076-4.24905691 2.40164076-9.60656301 0-13.85561995-2.40164076-3.87957344-6.83543878-6.65069766-11.82346261-6.65069765zM559.13363345 222.47291735z m-168.11485622 0c0 92.92502477 75.37457259 168.11485625 168.11485622 168.11485624 92.92502477 0 168.11485625-75.37457259 168.11485624-168.11485624s-75.37457259-168.11485625-168.11485624-168.11485502-168.11485625 75.18983146-168.11485622 168.11485502" fill="#F4AD51" p-id="31016"></path><path d="M559.13363345 222.47291735z m-140.77309869 0c0 50.24971466 26.78753175 96.80459822 70.38654874 121.92945615 43.599017 25.12485795 97.1740817 25.12485795 140.77309992 0s70.38654996-71.67974029 70.38654873-121.92945615c0-77.77621333-62.99688537-140.77309869-140.7730987-140.7730987s-140.77309869 62.99688537-140.77309866 140.7730987" fill="#F4E476" p-id="31017"></path><path d="M615.11033767 240.94707762h-41.93634316v-29.1891725H615.11033767c7.75914687 0 13.85561991-6.28121418 13.85561992-13.8556199 0-7.75914687-6.28121418-13.85561991-13.85561992-13.85561989h-24.01640751l15.3335526-26.4180495c3.69483231-6.65069766 1.29319156-14.96406911-5.17276498-18.65890138s-14.96406911-1.6626738-18.84364255 4.80328147l-22.9079583 39.90418592-22.9079583-39.90418592c-3.87957344-6.46595652-12.37768722-8.68285493-18.84364377-4.80328147-6.46595652 3.69483231-8.86759729 12.00820375-5.17276498 18.65890138l15.3335538 26.4180495h-24.01640871c-7.75914687 0-13.85561991 6.28121418-13.85561991 13.85561989 0 7.75914687 6.28121418 13.85561991 13.85561991 13.8556199h41.9363432v29.1891725h-41.9363432c-7.75914687 0-13.85561991 6.28121418-13.85561991 13.85561992 0 7.75914687 6.28121418 13.85561991 13.8556199 13.85561989h41.93634321v25.49434139c0 7.75914687 6.28121418 13.85561991 13.85561988 13.85561993 7.75914687 0 13.85561991-6.28121418 13.85561991-13.85561993v-25.49434139H615.11033767c4.98802263 0 9.60656301-2.58638188 12.00820499-7.02018113 2.40164076-4.24905691 2.40164076-9.60656301 0-13.85561989-2.40164076-4.06431575-7.02018113-6.83543878-12.00820499-6.83543879z" fill="#F4AD51" p-id="31018"></path><path d="M573.35873686 151.53214278c0 132.09024375 107.15012817 239.0556296 239.2403719 239.05563084s239.24037191-106.9653858 239.2403707-239.05563084-107.15012817-239.0556296-239.24037071-239.05562955-239.24037191 106.9653858-239.24037189 239.05562955z" fill="#FFBB5A" p-id="31019"></path><path d="M812.59910876 151.53214278z m-200.25989409 0c0 71.49499917 38.24151088 137.6324922 100.12994705 173.28762122 61.88843615 35.83987014 138.18671682 35.83987014 200.25989284 0 61.88843615-35.83987014 100.12994706-101.79262084 100.12994708-173.28762122 0-110.475477-89.59967595-200.25989408-200.25989289-200.25989287-110.6602181 0-200.25989408 89.59967595-200.25989408 200.25989287" fill="#FFF48D" p-id="31020"></path><path d="M892.03799591 177.95019226h-59.67153653V136.38333254h59.67153653c10.89975455 0 19.7673506-8.86759729 19.7673518-19.76735181 0-10.89975455-8.86759729-19.7673506-19.7673518-19.76735062h-34.17719637l21.61476798-37.50254517c4.98802263-9.42182189 1.6626738-21.06054215-7.5744057-26.41804827-9.23707953-5.3575061-21.06054215-2.40164076-26.6027906 6.65069768l-32.69926246 56.71567116-32.69926368-56.71567116c-3.51008995-6.09647306-9.97604647-9.97604647-17.1809687-9.97604651-7.02018113 0-13.67087877 3.69483231-17.18096876 9.97604651-3.51008995 6.09647306-3.51008995 13.67087877 0 19.76735059l21.61476675 37.50254517h-34.17719509c-10.89975455 0-19.7673506 8.86759729-19.76735183 19.76735062 0 10.89975455 8.86759729 19.7673506 19.76735183 19.76735181h59.67153651v41.56685972h-59.67153651c-10.89975455 0-19.7673506 8.86759729-19.76735183 19.76735065 0 10.89975455 8.86759729 19.7673506 19.76735183 19.7673518h59.67153651v36.2093536c0 10.89975455 8.86759729 19.7673506 19.76735061 19.76735063 10.89975455 0 19.7673506-8.86759729 19.76735183-19.76735063v-36.2093536h59.67153656c7.02018113 0 13.67087877-3.69483231 17.1809687-9.97604648 3.51008995-6.09647306 3.51008995-13.67087877 0-19.76735181-3.32534883-6.09647306-9.97604647-9.79130535-16.99622758-9.79130416z" fill="#FFBB5A" p-id="31021"></path><path d="M813.70755795 270.13625013c-188.6211738 0-356.18180422-41.93634318-463.33193237-106.96538584v824.50175926c0 51.91238966 42.12108432 94.03347399 94.03347401 94.03347396h738.59691555c51.91238966 0 94.03347399-42.12108432 94.03347397-94.03347396V162.98612194c-107.15012817 65.21378498-274.71075858 107.15012817-463.33193116 107.15012819z" fill="#FF4545" p-id="31022"></path><path d="M760.31723561 800.34464127h-117.12617465v-52.28187313h117.12617465v-36.39409473h-117.12617465v-52.28187316h94.77244095l-140.77309868-224.64578509h127.47170336l46.1854001 101.9773632c23.09269943 50.43445699 28.45020675 63.7358523 43.04479239 95.88089016h3.6948323c15.14881146-32.14503783 21.7995091-48.03281626 43.0447924-95.88089016l44.89220855-101.9773632h125.07006383l-140.77309989 224.64578509H984.59353723v52.28187313h-116.01772426v36.39409476H984.59353723v52.28187313h-116.01772426v88.67596788h-108.07383624v-88.67596788z" fill="#FFFFFF" p-id="31023"></path></svg>
                        </div>
                        <span style="position:relative;color: red;
font-weight: bold;">抢红包<div style="background: rgba(251, 24, 24, 0.87);color: rgb(255, 255, 255);width: 21px;height: 18px;line-height: 18px;position: absolute;border-radius: 50%;text-align: center;top: -29px;right: -10px;font-size: 12px;display:none;" id="index_unread_number">0</div></span>
                    </div>
                    <%--<div class="icon_item_index default_color" onclick="location.href='../chat.aspx#game'">
                        <div>
                            <svg t="" class="icon" viewBox="0 0 1601 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="31008" width="22" height="22"><path d="M186.6945686 138.23074871s3.51008995-0.36948349 5.35750608 1.84741491c1.84741615 2.40164076 4.24905691 34.91616207 12.7471707 43.78375935 8.49811381 8.86759729 43.04479239 3.87957344 43.04479238 15.51829492 0 9.23707953-31.96029671 10.34552996-42.86005123 19.21312604-10.89975455 8.86759729-4.61854037 46.55488355-17.91993449 45.07695087-10.34552996-1.10844917-3.1406077-31.03658864-15.88777838-43.04479361-12.7471707-12.00820375-43.78375933-9.60656301-43.78375812-20.50631755 0-10.89975455 31.77555558-4.24905691 42.6753089-16.62674414 10.7150122-12.19294611 6.09647306-45.26169202 16.62674416-45.26169079z" fill="#FFBB5A" p-id="31009"></path><path d="M1350.56664679-124.47180614s2.40164076-0.18474111 3.69483107 1.29319035c1.29319156 1.47793268 2.95586533 23.4621829 8.68285615 29.55865594 5.72698959 6.09647306 29.00443136 2.58638188 29.00443015 10.53027108 0 6.28121418-21.61476676 7.02018113-29.00443015 12.93191306-7.38966338 6.09647306-3.1406077 31.40607213-12.1929461 30.48236405-7.02018113-0.73896697-2.03215729-20.87580105-10.71501222-29.00443136-8.68285493-8.12863033-29.55865596-6.46595652-29.55865593-13.85561994s21.43002563-2.95586533 28.81968899-11.269238 4.24905691-30.66710516 11.26923804-30.66710518z" fill="#E94151" p-id="31010"></path><path d="M1531.61341363 278.44962278s3.51008995-0.36948349 5.54224844 2.03215729 4.24905691 35.47038666 12.93191184 44.52272508c8.68285493 9.0523384 43.599017 3.87957344 43.59901704 15.70303605 0 9.42182189-32.51452133 10.53027107-43.59901704 19.58260947-11.08449569 9.0523384-4.80328148 47.29384929-18.28941795 45.81591663-10.53027107-1.10844917-3.1406077-31.40607213-16.0725195-43.59901702-12.93191183-12.19294611-44.52272507-9.79130535-44.52272508-20.87580101 0-11.08449569 32.3297802-4.24905691 43.22953352-16.99622761 11.26923805-12.7471707 6.65069766-46.1854001 17.18096873-46.18539888z" fill="#FF0000" p-id="31011"></path><path d="M367.0023697-124.47180614z m-36.57883709 0c0 20.13683407 16.44200178 36.57883709 36.57883708 36.57883587s36.57883709-16.44200178 36.5788371-36.57883587S387.13920378-161.05064323 367.0023697-161.05064323c-20.32157643 0-36.57883709 16.44200178-36.57883709 36.57883709M1351.12087139 201.04289173z m-36.02461248 0c0 19.95209295 16.25726065 36.0246125 36.02461248 36.02461246 19.95209295 0 36.20935361-16.25726065 36.20935361-36.02461246 0-19.95209295-16.25726065-36.20935361-36.20935361-36.20935366-19.95209295 0.18474111-36.0246125 16.25726065-36.02461248 36.20935366" fill="#FFEB00" p-id="31012"></path><path d="M349.08243523 179.24338381c0 139.11042487 207.46481633 251.80280027 463.33193117 251.8028003s463.33193237-112.6923754 463.33193241-251.8028003S1068.4662236-72.37467533 812.59910876-72.37467533C556.54725159-72.37467533 349.08243523 40.31770005 349.08243523 179.24338381z" fill="#DB1818" p-id="31013"></path><path d="M1065.8798417 222.47291735z m-168.11485624 0c0 92.92502477 75.37457259 168.11485625 168.11485624 168.11485624 92.92502477 0 168.11485625-75.37457259 168.11485502-168.11485624s-75.37457259-168.11485625-168.11485502-168.11485502c-92.92502477 0-168.11485625 75.18983146-168.11485624 168.11485502" fill="#F4AD51" p-id="31014"></path><path d="M1065.8798417 222.47291735z m-140.77309987 0c0 50.24971466 26.78753175 96.80459822 70.38654995 121.92945615 43.599017 25.12485795 97.1740817 25.12485795 140.77309865 0s70.38654996-71.67974029 70.38654996-121.92945615c0-50.24971466-26.78753175-96.80459822-70.38654996-121.92945493a140.87840167 140.87840167 0 0 0-140.77309865 0c-43.599017 25.12485795-70.38654996 71.67974029-70.38654995 121.92945493" fill="#F4E476" p-id="31015"></path><path d="M1121.85654595 240.94707762h-41.9363432v-29.1891725h41.9363432c7.75914687 0 13.85561991-6.28121418 13.8556199-13.8556199 0-7.75914687-6.28121418-13.85561991-13.8556199-13.85561989h-24.01640754l15.3335526-26.4180495c3.69483231-6.65069766 1.29319156-14.96406911-5.17276496-18.65890138s-14.96406911-1.6626738-18.84364378 4.80328147L1065.8798417 183.4924395l-22.90795831-39.90418468c-3.87957344-6.46595652-12.37768722-8.68285493-18.84364376-4.80328151-6.46595652 3.69483231-8.86759729 12.00820375-5.17276495 18.65890141l15.14881143 26.41804825h-24.01640747c-7.75914687 0-13.85561991 6.28121418-13.85561995 13.85561994 0 7.75914687 6.28121418 13.85561991 13.85561995 13.85561987h41.93634319v29.1891737h-41.93634319c-7.75914687 0-13.85561991 6.28121418-13.85561995 13.85561991 0 7.75914687 6.28121418 13.85561991 13.85561995 13.85561989h41.93634319v25.49434022c0 7.75914687 6.28121418 13.85561991 13.85561987 13.85561989 7.75914687 0 13.85561991-6.28121418 13.85561993-13.85561989v-25.49434022h41.93634319c4.98802263 0 9.60656301-2.58638188 12.00820374-7.02018106 2.40164076-4.24905691 2.40164076-9.60656301 0-13.85561995-2.40164076-3.87957344-6.83543878-6.65069766-11.82346261-6.65069765zM559.13363345 222.47291735z m-168.11485622 0c0 92.92502477 75.37457259 168.11485625 168.11485622 168.11485624 92.92502477 0 168.11485625-75.37457259 168.11485624-168.11485624s-75.37457259-168.11485625-168.11485624-168.11485502-168.11485625 75.18983146-168.11485622 168.11485502" fill="#F4AD51" p-id="31016"></path><path d="M559.13363345 222.47291735z m-140.77309869 0c0 50.24971466 26.78753175 96.80459822 70.38654874 121.92945615 43.599017 25.12485795 97.1740817 25.12485795 140.77309992 0s70.38654996-71.67974029 70.38654873-121.92945615c0-77.77621333-62.99688537-140.77309869-140.7730987-140.7730987s-140.77309869 62.99688537-140.77309866 140.7730987" fill="#F4E476" p-id="31017"></path><path d="M615.11033767 240.94707762h-41.93634316v-29.1891725H615.11033767c7.75914687 0 13.85561991-6.28121418 13.85561992-13.8556199 0-7.75914687-6.28121418-13.85561991-13.85561992-13.85561989h-24.01640751l15.3335526-26.4180495c3.69483231-6.65069766 1.29319156-14.96406911-5.17276498-18.65890138s-14.96406911-1.6626738-18.84364255 4.80328147l-22.9079583 39.90418592-22.9079583-39.90418592c-3.87957344-6.46595652-12.37768722-8.68285493-18.84364377-4.80328147-6.46595652 3.69483231-8.86759729 12.00820375-5.17276498 18.65890138l15.3335538 26.4180495h-24.01640871c-7.75914687 0-13.85561991 6.28121418-13.85561991 13.85561989 0 7.75914687 6.28121418 13.85561991 13.85561991 13.8556199h41.9363432v29.1891725h-41.9363432c-7.75914687 0-13.85561991 6.28121418-13.85561991 13.85561992 0 7.75914687 6.28121418 13.85561991 13.8556199 13.85561989h41.93634321v25.49434139c0 7.75914687 6.28121418 13.85561991 13.85561988 13.85561993 7.75914687 0 13.85561991-6.28121418 13.85561991-13.85561993v-25.49434139H615.11033767c4.98802263 0 9.60656301-2.58638188 12.00820499-7.02018113 2.40164076-4.24905691 2.40164076-9.60656301 0-13.85561989-2.40164076-4.06431575-7.02018113-6.83543878-12.00820499-6.83543879z" fill="#F4AD51" p-id="31018"></path><path d="M573.35873686 151.53214278c0 132.09024375 107.15012817 239.0556296 239.2403719 239.05563084s239.24037191-106.9653858 239.2403707-239.05563084-107.15012817-239.0556296-239.24037071-239.05562955-239.24037191 106.9653858-239.24037189 239.05562955z" fill="#FFBB5A" p-id="31019"></path><path d="M812.59910876 151.53214278z m-200.25989409 0c0 71.49499917 38.24151088 137.6324922 100.12994705 173.28762122 61.88843615 35.83987014 138.18671682 35.83987014 200.25989284 0 61.88843615-35.83987014 100.12994706-101.79262084 100.12994708-173.28762122 0-110.475477-89.59967595-200.25989408-200.25989289-200.25989287-110.6602181 0-200.25989408 89.59967595-200.25989408 200.25989287" fill="#FFF48D" p-id="31020"></path><path d="M892.03799591 177.95019226h-59.67153653V136.38333254h59.67153653c10.89975455 0 19.7673506-8.86759729 19.7673518-19.76735181 0-10.89975455-8.86759729-19.7673506-19.7673518-19.76735062h-34.17719637l21.61476798-37.50254517c4.98802263-9.42182189 1.6626738-21.06054215-7.5744057-26.41804827-9.23707953-5.3575061-21.06054215-2.40164076-26.6027906 6.65069768l-32.69926246 56.71567116-32.69926368-56.71567116c-3.51008995-6.09647306-9.97604647-9.97604647-17.1809687-9.97604651-7.02018113 0-13.67087877 3.69483231-17.18096876 9.97604651-3.51008995 6.09647306-3.51008995 13.67087877 0 19.76735059l21.61476675 37.50254517h-34.17719509c-10.89975455 0-19.7673506 8.86759729-19.76735183 19.76735062 0 10.89975455 8.86759729 19.7673506 19.76735183 19.76735181h59.67153651v41.56685972h-59.67153651c-10.89975455 0-19.7673506 8.86759729-19.76735183 19.76735065 0 10.89975455 8.86759729 19.7673506 19.76735183 19.7673518h59.67153651v36.2093536c0 10.89975455 8.86759729 19.7673506 19.76735061 19.76735063 10.89975455 0 19.7673506-8.86759729 19.76735183-19.76735063v-36.2093536h59.67153656c7.02018113 0 13.67087877-3.69483231 17.1809687-9.97604648 3.51008995-6.09647306 3.51008995-13.67087877 0-19.76735181-3.32534883-6.09647306-9.97604647-9.79130535-16.99622758-9.79130416z" fill="#FFBB5A" p-id="31021"></path><path d="M813.70755795 270.13625013c-188.6211738 0-356.18180422-41.93634318-463.33193237-106.96538584v824.50175926c0 51.91238966 42.12108432 94.03347399 94.03347401 94.03347396h738.59691555c51.91238966 0 94.03347399-42.12108432 94.03347397-94.03347396V162.98612194c-107.15012817 65.21378498-274.71075858 107.15012817-463.33193116 107.15012819z" fill="#FF4545" p-id="31022"></path><path d="M760.31723561 800.34464127h-117.12617465v-52.28187313h117.12617465v-36.39409473h-117.12617465v-52.28187316h94.77244095l-140.77309868-224.64578509h127.47170336l46.1854001 101.9773632c23.09269943 50.43445699 28.45020675 63.7358523 43.04479239 95.88089016h3.6948323c15.14881146-32.14503783 21.7995091-48.03281626 43.0447924-95.88089016l44.89220855-101.9773632h125.07006383l-140.77309989 224.64578509H984.59353723v52.28187313h-116.01772426v36.39409476H984.59353723v52.28187313h-116.01772426v88.67596788h-108.07383624v-88.67596788z" fill="#FFFFFF" p-id="31023"></path></svg>
                        </div>
                        <span style="
    color: red;
    font-weight: bold;
">抢红包</span>
                    </div>--%>
                    <%--<div class="icon_item_index" onclick="location.href='../chat.aspx#friend_book'">
                        <div>
                            <svg t="" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="" width="22" height="22" style="">
                                <path d="M512 85.333333c128.085333 0 230.656 105.6 230.656 257.28 0 58.154667-34.986667 142.122667-77.013333 203.989334-24.746667 30.805333-13.994667 57.728 4.010666 67.328 3.754667 2.133333 77.994667 43.093333 100.949334 55.808l2.432 1.365333c39.125333 21.76 67.84 37.973333 87.04 49.365333 50.645333 33.877333 74.837333 59.008 78.592 103.253334V853.333333a85.333333 85.333333 0 0 1-85.333334 85.333334H170.666667a85.333333 85.333333 0 0 1-85.333334-85.333334v-29.610666c0-41.813333 24.064-67.2 77.610667-102.954667 36.650667-22.613333 143.786667-81.706667 189.098667-105.728 18.176-10.453333 28.629333-36.138667 1.962666-70.101333-39.253333-58.069333-73.813333-143.104-73.813333-202.368C280.192 189.525333 381.866667 85.333333 512 85.333333z m0 85.333334c-84.181333 0-146.474667 67.498667-146.474667 171.946666 0 36.266667 25.173333 103.466667 57.856 152.533334 51.669333 68.693333 39.637333 154.538667-31.402666 195.328-53.077333 28.117333-153.685333 84.096-181.632 101.248-34.261333 22.869333-39.168 27.861333-39.68 31.36V853.333333h682.666666v-24.874666c-1.493333-8.490667-7.765333-14.933333-38.784-35.797334a3817.770667 3817.770667 0 0 0-82.944-46.933333l-2.432-1.365333-47.018666-26.026667a7185.066667 7185.066667 0 0 1-52.565334-29.098667c-69.461333-36.949333-84.352-125.44-33.066666-192.768 35.242667-52.608 60.8-118.869333 60.8-153.898666C657.322667 239.573333 594.048 170.666667 512 170.666667z m426.709333 384c21.845333 0 39.893333 16.341333 42.325334 37.674666L981.333333 597.333333c0 23.552-18.901333 42.666667-42.624 42.666667h-42.752A42.538667 42.538667 0 0 1 853.333333 597.333333c0-23.552 18.901333-42.666667 42.624-42.666666h42.752z m-0.426666-170.666667c22.058667 0 40.277333 16.341333 42.752 37.674667L981.333333 426.666667a42.666667 42.666667 0 0 1-43.093333 42.666666h-84.48a42.666667 42.666667 0 1 1 0-85.333333h84.48zM938.666667 213.333333c21.845333 0 39.893333 16.341333 42.325333 37.674667L981.333333 256c0 23.552-18.944 42.666667-42.666666 42.666667h-128c-23.552 0-42.666667-18.944-42.666667-42.666667 0-23.552 18.944-42.666667 42.666667-42.666667h128z" fill="#333C4F" p-id="11199"></path></svg>
                        </div>
                        <span>通讯录</span>
                    </div>--%>
                    <div class="icon_item_index" onclick="location.href='account.aspx'" icon="account">
                        <div>
                            <svg t="" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="" width="22" height="22">
                                <path class="light_point" d="M627.2 690.56m-181.333333 0a181.333333 181.333333 0 1 0 362.666666 0 181.333333 181.333333 0 1 0-362.666666 0Z" fill="#FFCA5F" p-id="2715"></path><path d="M125.866667 274.56m-42.666667 0a42.666667 42.666667 0 1 0 85.333333 0 42.666667 42.666667 0 1 0-85.333333 0Z" fill="#FFCA5F" p-id="2716"></path><path d="M524.8 894.506667a34.56 34.56 0 0 1-21.333333-7.466667l-5.12-3.84c-113.28-89.386667-183.466667-144.853333-202.88-160.64-90.453333-73.173333-142.933333-144-165.12-222.72a292.266667 292.266667 0 0 1-8.746667-114.56l64 7.893333a229.973333 229.973333 0 0 0 6.4 89.386667c18.346667 65.28 64 125.866667 143.786667 190.293333 13.013333 10.666667 56.533333 45.226667 189.013333 149.333334 132.48-104.32 176-138.88 189.013333-149.333334 79.786667-64 125.44-125.013333 143.786667-190.293333s7.68-164.053333-62.506667-213.333333a132.906667 132.906667 0 0 0-94.933333-21.333334c-70.826667 9.386667-115.84 34.986667-145.92 82.986667a34.56 34.56 0 0 1-58.666667 0c-30.293333-48.213333-75.306667-73.813333-146.133333-83.2a132.906667 132.906667 0 0 0-94.933333 21.333333l-36.906667-52.48a196.48 196.48 0 0 1 140.373333-32.426666 261.973333 261.973333 0 0 1 166.826667 79.573333 261.973333 261.973333 0 0 1 166.826667-79.573333A196.48 196.48 0 0 1 832 216.533333c96.213333 68.053333 113.066667 192 87.253333 283.306667-21.333333 78.72-74.453333 149.333333-165.12 222.72-19.413333 15.786667-89.6 71.253333-202.88 160.64l-5.12 3.84a34.56 34.56 0 0 1-21.333333 7.466667z" fill="#5C1CF7" p-id="2717"></path></svg>
                        </div>
                        <span>我的</span>
                    </div>

                </div>

            </div>

            <script>
                $('.ulk_menu>div').on('click', function () {
                    $(this).addClass("issel").siblings().removeClass("issel");
                })
            </script>



        </div>




        <div class="pg-password-input">
            <div style="width: 100vw; height: 100vh; position: fixed; top: 0; left: 0; background: #00000040; z-index: 9991;" onclick="javascript:$('.pg-password-input').hide(100);"></div>
            <div class="password-content">

                <h3 style="text-align: center; border-bottom: 1px solid #ddd; margin: 0; padding: 13px; font-size: 16px; color: blue;" class="pwd-name">支付密码</h3>



                <div class="pg-password-tip-top"></div>


                <%--支付密码方式--%>
                <div class="input-spwd">
                    <div style="padding: 30px; display: flex; padding-bottom: 0;">


                        <input class="password-input" oninput="handleDigitInput(event, 1)" onkeydown="handleBackspace(event, 1)">
                        <input class="password-input" oninput="handleDigitInput(event, 2)" onkeydown="handleBackspace(event, 2)">
                        <input class="password-input" oninput="handleDigitInput(event, 3)" onkeydown="handleBackspace(event, 3)">
                        <input class="password-input" oninput="handleDigitInput(event, 4)" onkeydown="handleBackspace(event, 4)">
                        <input class="password-input" oninput="handleDigitInput(event, 5)" onkeydown="handleBackspace(event, 5)">
                        <input class="password-input" oninput="handleDigitInput(event, 6)" onkeydown="handleBackspace(event, 6)">
                    </div>




                    <div style="padding: 8px 30px; text-align: right;">
                        <a style="color: gray; font-size: 13px; text-decoration: none;" href="password_manager.aspx?type=security">忘记支付密码？</a>
                    </div>

                </div>



                <%--登录密码方式--%>
                <div class="input-lpwd">
                    <div style="padding: 30px; padding-bottom: 0;">
                        <input class="loginpwd-input" onkeyup="checkLoginPasswordButton(this)" />
                    </div>


                    <div style="padding: 8px 30px; text-align: right;">
                        <a style="color: gray; font-size: 13px; text-decoration: none;" href="password_manager.aspx?type=login">忘记登录密码？</a>
                    </div>
                </div>




                <div class="pg-password-tip"></div>

                <div style="display: flex; padding: 16px 22px;">


                    <div style="width: 50%; text-align: center;">
                        <a style="color: #3838f5; border: 1px solid #3838f5; display: inline-block; width: 130px; height: 45px; line-height: 45px; border-radius: 12px; cursor: pointer; font-weight: bold;" onclick="javascript:$('.pg-password-input').hide(100);">取消</a>
                    </div>
                    <div style="width: 50%; text-align: center;">
                        <a class="confirm_password_button confirm_pwd_button" onclick="comfirm_security()">确认
            </a>
                    </div>

                </div>



            </div>
        </div>
        <script>
            function handleDigitInput(event, inputNumber) {
                var input = event.target;
                var inputValue = input.value;

                if (inputValue != "") {
                    var v = inputValue.split('')
                    inputValue = v[v.length - 1];
                    input.value = inputValue;
                }

                if (!/^\d$/.test(inputValue)) {
                    input.value = "";
                    checkPasswordButton();
                    return;
                }

                if (inputNumber < 6) {
                    var nextInput = document.querySelector('.password-input:nth-of-type(' + (inputNumber + 1) + ')');
                    if (nextInput) {
                        nextInput.focus();
                    }
                }
                checkPasswordButton();
            }

            function handleBackspace(event, inputNumber) {
                if (event.key === "Backspace" && inputNumber > 1) {
                    setTimeout(function () {
                        var prevInput = document.querySelector('.password-input:nth-of-type(' + (inputNumber - 1) + ')');
                        if (prevInput) {
                            prevInput.focus();
                        }
                    }, 0);
                }

                checkPasswordButton();
            }
            var passwordNumber = 6;
            try {
                passwordNumber = pnumber;
            } catch (e) {

            }
            function checkPasswordButton() {
                var pwd = ''
                for (var i = 0; i < passwordNumber; i++) {
                    pwd += $('.password-input').eq(i).val();;
                }
                if (pwd.length != passwordNumber) {
                    $('.confirm_pwd_button').css({ "background": "#f1f1f1", "color": "#bbb" });
                } else {
                    $('.confirm_pwd_button').css({ "background": "#3838f5", "color": "#fff" });
                }
            }


            function checkLoginPasswordButton() {
                if ($('.loginpwd-input').val() == "") {
                    $('.confirm_pwd_button').css({ "background": "#f1f1f1", "color": "#bbb" });
                } else {
                    $('.confirm_pwd_button').css({ "background": "#3838f5", "color": "#fff" });
                }
            }

            var security_success_callback;
            var security_password = function (e, pms, islogin_pwd) {
                security_success_callback = e;

                $('.pg-password-tip').html('');
                try {
                    $('.pg-password-tip').html(pms.error_tip);
                } catch (e) {

                }


                $('.pg-password-tip-top').html('');
                try {
                    $('.pg-password-tip-top').html(pms.error_tip_top);
                } catch (e) {

                }
                $('.pg-password-input').show(100);
                $('.password-input').val("");
                $('.loginpwd-input').val("");

                $('.input-spwd').hide();
                $('.input-lpwd').hide();
                if (islogin_pwd) {
                    $('.input-lpwd').show();
                    $('.pwd-name').html('登录密码');
                } else {
                    $('.input-spwd').show();
                    $('.pwd-name').html('支付密码');
                }

                $('.confirm_pwd_button').css({ "background": "#f1f1f1", "color": "#bbb" });
            }
            var comfirm_security = function () {
                var pwd = ''
                if ($('.input-spwd').css("display") == "block") {

                    for (var i = 0; i < passwordNumber; i++) {
                        pwd += $('.password-input').eq(i).val();;
                    }
                    if (pwd.length != passwordNumber) {
                        return;
                    }
                } else {
                    pwd = $('.loginpwd-input').val();
                    if (pwd.length == 0) {
                        return;
                    }
                }
                $('.pg-password-input').hide(100);
                security_success_callback({ password: pwd });
            }
        </script>



        <!--上传图片 2023 -->
        <%--<script src="../js/jquery.form.min.js"></script>--%>
        <script src="../js/jquery.form.4.3.0.js"></script>
        <div style="display: none;">
            <input id="upload_img_input" type="file" accept="image/png, image/jpeg,image/gif,image/ico" name="file" onchange="image_upload()" multiple="multiple" />
            <script>
                var imgParames = {
                    id: "#upload_img_input",
                    folder: '',
                    callback: function () { }
                }
                function image_upload() {
                    if ($(imgParames.id).val() == "") {
                        return;
                    }
                    $('#form1').ajaxSubmit({
                        type: "POST",
                        url: '../api/v3.aspx?type=upload_files',
                        data: { f: imgParames.folder },
                        datatype: "json",
                        success: function (json) {
                            $(imgParames.id).val('');
                            if (json.code == 1) {
                                imgParames.callback({
                                    success: true,
                                    imgurl: json.list
                                });
                            } else {
                                tp(json.msg);
                                imgParames.callback({
                                    success: false
                                });
                            }
                        },
                        error: function (ex) {
                            console.log('上传异常', ex);
                            tp('图片上传异常');
                            v3api("update_logs", {
                                error: 1,
                                data: {
                                    from: 'image_upload',
                                    text: ex.responseText.toString()
                                }
                            }, function () {
                            })
                        }
                    });
                }
                function file_upload(callback, accept, folder) {
                    if (!accept || accept == "") {
                        accept = "image/png, image/jpeg,image/gif,image/ico";
                    }
                    $('#upload_img_input').attr('accept', accept);

                    if (!folder) {
                        folder = "";
                    }
                    imgParames.folder = folder;
                    imgParames.name = "upload";
                    imgParames.callback = callback;
                    $(imgParames.id).click();
                }
            </script>
        </div>
        <!--上传图片 完毕-->




        <div class="fullscreen-bg">
            <div class="fullscreen-media-container">
                <!-- 使用 video 标签，但初始状态下不显示 -->
                <video src="" alt="Full Screen Video" class="fullscreen-video" controls autoplay style="display: none;"></video>
                <!-- 使用 img 标签，初始状态下显示 -->
                <img src="" alt="Full Screen Image" class="fullscreen-image" />
            </div>
        </div>

        <style>
            /* 初始状态下全屏图片和视频都不可见 */
            .fullscreen-bg {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background-color: rgba(0, 0, 0, 0.9); /* 黑色背景，可根据需要调整透明度 */
                z-index: 99999999;
                text-align: center;
                display: flex;
                align-items: center;
                justify-content: center;
                display: none;
            }

            .fullscreen-image,
            .fullscreen-video {
                max-width: 100vw;
                max-height: 100vh;
                cursor: pointer;
            }
        </style>

        <script>
            // 获取全屏媒体容器、背景和全屏媒体元素
            var fullscreenBg = document.querySelector('.fullscreen-bg');
            var fullscreenMediaContainer = document.querySelector('.fullscreen-media-container');
            var fullscreenImage = document.querySelector('.fullscreen-image');
            var fullscreenVideo = document.querySelector('.fullscreen-video');

            // 切换全屏媒体的显示状态
            function toggleImgFullScreen(src) {
                if (fullscreenBg.style.display === 'flex') {
                    closeMedia();
                } else {
                    openMedia(src);
                }
            }

            // 打开全屏媒体
            function openMedia(src) {
                if (isVideo(src)) {
                    // 如果是视频，则显示 video 标签，隐藏 img 标签
                    fullscreenVideo.src = src;
                    fullscreenVideo.style.display = 'block';
                    fullscreenImage.style.display = 'none';
                } else {
                    // 如果是图片，则显示 img 标签，隐藏 video 标签
                    fullscreenImage.src = src;
                    fullscreenImage.style.display = 'block';
                    fullscreenVideo.style.display = 'none';
                }

                fullscreenBg.style.display = 'flex';
            }

            // 关闭全屏媒体
            function closeMedia() {
                fullscreenBg.style.display = 'none';
                fullscreenImage.src = '';
                fullscreenVideo.src = '';
            }

            // 判断是否为视频（简单示例，可根据需要扩展）
            function isVideo(src) {
                var videoExtensions = ['.mp4', '.webm', '.ogg', '.avi', '.mov', '.wmv', '.flv', '.mkv', '.m4v', '.3gp', '.webp', '.f4v', '.mpeg', '.mpg', '.mp2', '.mpe', '.mpv', '.m2v', '.qt', '.asf', '.lrv'];
                var lowercasedSrc = src.toLowerCase();

                return videoExtensions.some(function (extension) {
                    return lowercasedSrc.endsWith(extension);
                });
            }

            // 点击全屏媒体或背景关闭全屏媒体
            fullscreenBg.addEventListener('click', function () {
                closeMedia();
            });
        </script>





        <%--<div style="bottom: 81px; position: fixed; right: 33px;">
            <a href="<%=uConfig.stcdata("kf_online") %>" target="_blank" style="text-decoration: none; display: block;    text-align: center;">
                <svg t="1695554451450" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="4805" width="32" height="32"><path d="M418.238197 331.702986m-331.702986 0a331.702986 331.702986 0 1 0 663.405972 0 331.702986 331.702986 0 1 0-663.405972 0Z" fill="#8CF6FB" p-id="4806"></path><path d="M690.811521 977.802716l-30.285925-64.898411s157.198372-119.701512 187.484296-212.001473c-12.979682 2.884374-27.401551 5.768748-40.381233 5.768747-72.109345 0-129.796821-57.687476-129.79682-129.796821v-28.843737c0-72.109345 57.687476-129.796821 129.79682-129.796821 20.190617 0 40.381233 4.326561 57.687476 14.421869v-5.768748C865.316135 230.749903 706.675577 72.109345 511.980346 72.109345S158.644556 230.749903 158.644556 425.445134v5.768748c63.456223-31.728112 141.334316-5.768748 173.062428 57.687476 8.653121 18.74843 14.421869 37.496859 14.421869 57.687476v28.843738c0 72.109345-57.687476 129.796821-129.796821 129.79682-60.57185 0-112.490578-41.82342-126.912447-99.510896-1.442187-4.326561-2.884374-8.653121-2.884374-14.421869V425.445134C86.535211 190.36867 276.903882 0 511.980346 0S937.42548 190.36867 937.42548 425.445134V591.296627c0 271.131136-246.613959 386.506088-246.613959 386.506089zM158.644556 576.874758c0 31.728112 25.959364 57.687476 57.687476 57.687476s57.687476-25.959364 57.687476-57.687476v-28.843737c0-31.728112-25.959364-57.687476-57.687476-57.687476s-57.687476 25.959364-57.687476 57.687476v28.843737z m706.671579-28.843737c0-31.728112-25.959364-57.687476-57.687476-57.687476s-57.687476 25.959364-57.687476 57.687476v28.843737c0 31.728112 25.959364 57.687476 57.687476 57.687476s57.687476-25.959364 57.687476-57.687476v-28.843737z" fill="#3C2DCB" p-id="4807"></path><path d="M719.655259 951.843351c1.442187 37.496859-27.401551 70.667158-66.340598 72.109345H648.988101c-40.381233 1.442187-72.109345-30.285925-73.551532-70.667158s30.285925-72.109345 70.667158-73.551531c40.381233-1.442187 72.109345 30.285925 73.551532 72.109344 0-1.442187 0 0 0 0z" fill="#D098FF" p-id="4808"></path></svg>
                <div><span style="color: #3c2dcb; font-size: 14px;">在线客服</span></div>

            </a>
        </div>--%>




        <style>
            /* CSS 样式 */
            .video-icon-container {
                position: fixed;
                bottom: 80px;
                right: 10px;
                z-index: 999;
                display: none;
            }

            .video-icon {
                width: 80px;
                height: 80px;
                background-image: url('/static/images/hongbao.png'); /* 设置封面图作为背景 */
                background-size: cover;
                background-position: center;
                cursor: pointer;
            }
        </style>
        <script>
            $(function () {
                if (location.pathname == "/index.aspx" || location.pathname == "/order.aspx" || location.pathname == "/order_new.aspx") {
                    $('.video-icon-container').show();
                }
            })


            var lastTriggerTime = 0;
            // 添加事件监听器，当用户操作时触发 checkA 事件
            document.addEventListener('click', function () {
                check_onlineStatus();
            });

            // 添加触摸事件监听器
            document.addEventListener('touchstart', function () {
                check_onlineStatus();
            });

            // 添加键盘事件监听器
            document.addEventListener('keydown', function () {
                check_onlineStatus();
            });

            // 添加鼠标移动事件监听器
            document.addEventListener('mousemove', function () {
                check_onlineStatus();
            });

            // 添加滚动事件监听器
            document.addEventListener('scroll', function () {
                check_onlineStatus();
            });


            // 其他事件监听器
            function check_onlineStatus() {
                var currentTime = Date.now();

                // 距离上次是否超过10秒
                if (currentTime - lastTriggerTime >= 10000) {
                    v3api("keep_online", {
                        error: 1,
                        data: {}
                    }, function () {
                    })

                    lastTriggerTime = currentTime;
                } else {
                }
            }

        </script>

        <!-- 视频图标容器 -->
        <div class="video-icon-container" style="text-align: right;">

            <%--<div class="video-icon" onclick="javascript:location.href='<%=uConfig.stcdata("kf_online") %>'" style="width: 60px;height:60px; margin: auto; margin-bottom: 13px; background-image: none;">
                <img src="static/images/lxwm.png" style="width: 60px; margin-bottom: -6px;">
            </div>--%>

            <%--<svg t="1696945345825" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="10780" width="22" height="22" style="opacity: 0.5; margin-bottom: 15px; margin-right: 15px;" onclick="javascript:$('.video-icon-container').hide()">
            <path d="M512 1024a512 512 0 1 1 512-512 512 512 0 0 1-512 512z m241.379556-672.938667l-80.440889-80.440889L512 431.559111 351.118222 270.620444 270.677333 351.061333 431.502222 512l-160.881778 160.881778 80.440889 80.440889L512 592.440889l160.938667 160.881778 80.440889-80.440889L592.440889 512z" fill="#243340" p-id="10781"></path></svg>--%>
            <%--<div class="video-icon" onclick="javascript:location.href='../app1/maiya.html'" style="background-image: url(/static/images/unnamed.png); width: 60px; height: 60px; margin: auto; margin-bottom: 13px;"></div>--%>

            <%--<div class="video-icon" id="video-icon" onclick="javascript:location.href='chat.aspx'"></div>--%>
        </div>





        <style>
            .warning-cpt {
                z-index: 999999999;
                position: relative;
                display: none;
            }

            .warning-cpt2 {
                display: flex;
                justify-content: center;
                align-items: center;
                position: fixed;
                left: 0;
                top: 0;
                right: 0;
                bottom: 0;
                background: rgba(0,0,0,.7);
            }

            .warning-cpt a {
                text-decoration: none;
                color: inherit;
                display: block;
            }

                .warning-cpt a:hover {
                    text-decoration: none;
                }

            .warning-cpt .pop-cpt-bd {
                line-height: 22px;
                width: 286px;
                padding: 0 5px;
                box-sizing: border-box;
                border-radius: 15px;
                background: #0082f3;
                font-size: 14px;
                color: #333;
                font-family: "微软雅黑";
                position: relative;
            }

            .warning-cpt .pop-cpt-close {
                display: inline-block;
                height: 36px;
                line-height: 36px;
                padding: 0 15px;
                color: #fff;
                position: absolute;
                top: 0;
                right: 0;
            }

            .warning-cpt .pop-cpt-tit {
                height: 36px;
                line-height: 36px;
                color: #fff;
                text-align: center;
                font-size: 18px;
                font-weight: bold;
            }

                .warning-cpt .pop-cpt-tit span {
                    display: inline-block;
                    padding: 0 10px;
                    position: absolute;
                    top: 0;
                    right: 0;
                }

            .warning-cpt .pop-cpt-con {
                padding: 0 6px 3px 6px;
                border-radius: 15px;
                background: #fff;
            }

            .warning-cpt .pop-cpt-con1 {
                text-align: center;
                color: #000;
            }

            .warning-cpt .pop-cpt-con2 {
                padding-top: 10px;
            }

            .warning-cpt .pop-cpt-con2-tit {
                display: flex;
                justify-content: center;
                padding-bottom: 5px;
            }

                .warning-cpt .pop-cpt-con2-tit span {
                    display: inline-block;
                    width: 26px;
                    line-height: 26px;
                    margin-left: -3px;
                    text-align: center;
                    color: #FFF200;
                    font-size: 16px;
                    font-weight: bold;
                    border-radius: 50%;
                    background: #ED1B20;
                }

            .warning-cpt .pop-cpt-con2-logo {
                display: flex;
                justify-content: center;
                height: 60px;
            }

            .warning-cpt .pop-cpt-con2-logol {
                color: #fb2725;
                font-size: 54px;
                line-height: 50px;
                font-weight: bold;
                font-family: Tahoma;
            }

            .warning-cpt .pop-cpt-con2-logor {
                width: 70px;
                position: relative;
            }

            .warning-cpt .pop-cpt-con2-logor-txt1 {
            }

                .warning-cpt .pop-cpt-con2-logor-txt1 span {
                    display: inline-block;
                    width: 60px;
                    height: 22px;
                    line-height: 22px;
                    text-align: center;
                    color: #fff;
                    border-radius: 5px;
                    background: #0082f3;
                    position: absolute;
                    left: 8px;
                    top: 2px;
                }

            .warning-cpt .pop-cpt-con2-logor-txt2 {
                line-height: 32px;
                color: #333;
                font-size: 30px;
                font-family: impact,arial;
                position: absolute;
                top: 24px;
                left: 0;
            }

            .warning-cpt .pop-cpt-con3 {
                padding: 0 2px;
            }

                .warning-cpt .pop-cpt-con3 table {
                    font-weight: bold;
                    border-collapse: collapse;
                }

                .warning-cpt .pop-cpt-con3 th {
                    text-align: center;
                    color: #fff;
                    border: solid 1px #999;
                    background: #0082f3;
                    font-size: 18px;
                    font-weight: 500;
                    padding: 5px 0;
                }

                .warning-cpt .pop-cpt-con3 td {
                    text-align: center;
                    border: solid 1px #999;
                    font-size: 16px;
                    line-height: 22px;
                    padding: 5px 0;
                }

            .warning-cpt .pop-cpt-con4 {
                text-align: center;
                margin-bottom: 10px;
            }

                .warning-cpt .pop-cpt-con4 span {
                    display: inline-block;
                    width: 200px;
                    height: 30px;
                    line-height: 30px;
                    font-size: 14px;
                    font-weight: bold;
                    color: #ff0;
                    border-radius: 10px;
                    background: #fb2725;
                    margin-top: 28px;
                }

            .warning-cpt .pop-cpt-footer {
                padding: 10px 0;
                color: #fff;
                text-align: center;
            }

            .warning-cpt .pop-cpt-footer1 {
                font-size: 16px;
            }

            .warning-cpt .pop-cpt-footer2 {
                padding-top: 5px;
                font-weight: bold;
                color: #ff0;
            }
        </style>
        <%--<div class="warning-cpt ">
            <div class="warning-cpt2">
                <div class="pop-cpt-bd">
                    <a>
                        <div class="pop-cpt-tit"></div>
                        <div class="pop-cpt-con">
                            <div class="pop-cpt-con2">
                                <div class="pop-cpt-con2-tit">
                                    <span>紧</span><span>急</span><span>提</span><span>醒</span>
                                </div>

                            </div>
                            <div class="pop-cpt-con4"><span style="">请与麦芽专员进行订单确认</span></div>
                        </div>
                        <div class="pop-cpt-footer">
                        </div>
                    </a>
                </div>
            </div>
        </div>--%>

        <script>
            //setInterval(function () {
            //    if ($('.warning-cpt2').length == 0) {
            //        location.href = location.href;
            //    }
            //}, 200);
            //var user_status = function () {
            //    v3api("user_status", {
            //        error: 1,
            //        data: {}
            //    }, function (e) {
            //        if (e.msg == "1") {
            //            $('.pop-cpt').hide();
            //            $('.warning-cpt').show();
            //        } else {
            //            $('.warning-cpt').hide();
            //        }
            //    })
            //}
            //user_status();
            //setInterval(user_status, 5000)


            // 首页消息数量更新
            var update_index_unread_number = function () {

                v3api("get_unread_number", {
                    error: 1,
                    data: {
                    }
                }, function (e) {
                    if (e.code == 1) {
                        $('#index_unread_number').html(e.unread_number);
                        if (e.unread_number > 0) {
                            $('#index_unread_number').show();
                        } else {
                            $('#index_unread_number').hide();
                        }
                    } else {
                        $('#index_unread_number').html('-');
                    }
                })
            }

            $(function () {

                console.log('dp', $('.wxlist_index').css('display'));
                if ($('.wxlist_index').css('display') == "flex") {
                    $('#index_unread_number').html('0');
                    $('#index_unread_number').hide();

                    update_index_unread_number();
                    setInterval(function () { update_index_unread_number() }, 5000)
                }
            })
        </script>



    </form>
</body>
</html>

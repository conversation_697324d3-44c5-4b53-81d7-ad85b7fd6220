using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

public partial class index : baseClass
{
    public DataTable userdt = new DataTable();
    public DataTable msgList = new DataTable();
    public List<string> msgs = new List<string>();
    public List<Dictionary<string, object>> list = new List<Dictionary<string, object>>();

    protected void Page_Load(object sender, EventArgs e)
    {

        msgList = chelper.gdt("message_list");

        List<SqlParameter> pams = new List<SqlParameter>();
        dbClass db = new dbClass();
        pams.Add(new SqlParameter("@userid", uConfig.p_uid));

        string sql = string.Empty;
        DataTable dt = new DataTable();

        sql = " select * from accounts with(nolock) where id=@userid ";

        sql += " SELECT TOP 25 bl.*,a.parent_code FROM [brok_list] bl left join accounts a with(nolock) on bl.userid=a.id where rank=-1 and DATEDIFF(DAY,bl.create_time,GETDATE())=0 order by newid() ";

        DataSet ds = db.getDataSet(sql, pams.ToArray());

        userdt = ds.Tables[0];
        dt = ds.Tables[1];


        //<li><a>恭喜86177****7299完成&nbsp;<span style="color: #FD863E;">380.0299 USDT</span>&nbsp;交易额</a></li>
        //<li><a>恭喜86177****7299成交获得奖励&nbsp;<span style="color: #FD863E;">8.3102 USDT</span></a></li>

        Random rd = new Random();


        var bankid1 = "";
        var bankid2 = "";
        string money = "";
        string getmoney = "0";

        for (int i = 0; i < dt.Rows.Count; i++)
        {
            bankid1 = dt.Rows[i]["parent_code"] + "";
            bankid2 = bankid1.Substring(0, 2);
            bankid1 = bankid1.Substring(bankid1.Length - 3, 2);
            money = dt.Rows[i]["amount"] + "";
            getmoney = dt.Rows[i]["award_amount"] + "";

            money = Convert.ToDouble(money).ToString("0.00");
            getmoney = Convert.ToDouble(getmoney).ToString("0.00");


            msgs.Add("<li><a>恭喜" + bankid1 + "***" + bankid2 + "已完成&nbsp;<span style='color: #FD863E;'>" + money + "</span>&nbsp;商品任务</a></li>");
            msgs.Add("<li><a>获得奖励&nbsp;<span style='color: #FD863E;'>" + getmoney + "<img src='/static/images/coin.png' alt='' height='15' width='15' style='margin-left: 5px;'></span></a></li>");
        }

        for (int i = 0; i < 25; i++)
        {
            bankid1 = rd.Next(10, 99).ToString();
            bankid2 = rd.Next(10, 99).ToString();
            money = (rd.Next(1, 10) * 100).ToString("0.00");
            getmoney = "-";
            try
            {
                getmoney = (Convert.ToDouble(money) * 2 / 100).ToString("0.00");
            }
            catch (Exception)
            {
            }

            msgs.Add("<li><a>恭喜" + bankid1 + "***" + bankid2 + "已完成&nbsp;<span style='color: #FD863E;'>" + money + "</span>&nbsp;商品任务</a></li>");
            msgs.Add("<li><a>获得奖励&nbsp;<span style='color: #FD863E;'>" + getmoney + "<img src='/static/images/coin.png' alt='' height='15' width='15' style='margin-left: 5px;'></span></a></li>");


        }


        DataTable temp_dt = new DataTable();
        temp_dt = chelper.gdt("message_list");
        try
        {
            temp_dt = selectDateTable(temp_dt, " name<>'用户服务协议' ");
        }
        catch (Exception)
        {
        }
        message_list.DataSource = temp_dt;
        message_list.DataBind();


        banner_list.DataSource = chelper.gdt("banner_list");
        banner_list.DataBind();



        sql = @"

select userid,total_amount,u.phone from (SELECT
	userid,
    SUM(award_amount) AS total_amount
FROM rob_orders with(nolock)
WHERE state > 0 and  DATEDIFF(DAY, create_time, GETDATE()) = 0 group by userid)tt left join accounts u on tt.userid=u.id order by total_amount desc

";

        sql = " select * from lst_award where datediff(day,create_time,getdate())=0 and total_amount>0 ";

        //sql += " select top 10 bl.*,a.parent_code from (select userid,SUM(award_amount) as award_amount from [brok_list] with(nolock)  where rank<>-1  and DATEDIFF(DAY,create_time,GETDATE())=0  group by userid)bl left join accounts a with(nolock) on bl.userid=a.id order by award_amount desc ";

        sql += " select top 10 bl.*,bl.bork_user+bl.bork_team as total_amount,a.parent_code as phone,a.username from [play_user_daily] bl with(nolock) left join accounts a with(nolock) on bl.userid=a.id where DATEDIFF(DAY,getdate(),bl.create_date)=0 order by bl.bork_user+bl.bork_team desc ";

        ds = db.getDataSet(sql);

        dt = ds.Tables[0];


        DataTable tempTable = ds.Tables[1];
        for (int i = 0; i < tempTable.Rows.Count; i++)
        {
            dt.Rows.Add("-1", getStarNick(tempTable.Rows[i]["phone"] + "", ""), tempTable.Rows[i]["total_amount"] + "", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));
        }

        //for (int i = 0; i < dt.Rows.Count; i++)
        //{
        //    dt.Rows[i]["phone"] = getStarNick(dt.Rows[i]["phone"] + "", dt.Rows[i]["username"] + "");
        //}


        dt = selectDateTable(dt, "", 10);

        dt = SortDataTable(dt, "total_amount desc");

        //rankList.DataSource = dt;
        //rankList.DataBind();








        string temp = string.Empty;
        Dictionary<string, object> temp_dic = new Dictionary<string, object>();



        temp_dic = new Dictionary<string, object>();
        temp_dic.Add("imgurl", "../static/images/avatar-1.jpg");
        temp_dic.Add("name", "聊天室");
        temp_dic.Add("title", uConfig.stcdata("chat_title"));
        temp_dic.Add("text_details", uConfig.stcdata("chat_desc"));
        //temp_dic.Add("url", "../games/authorize.aspx?gameName=" + dt.Rows[i]["code"]);
        temp_dic.Add("url", "../chat.aspx");
        list.Add(temp_dic);

        dt = selectDateTable(chelper.gdt("auth_list"), " state=1");


        List<Dictionary<string, object>> def_dic = new List<Dictionary<string, object>>();
        for (int i = 0; i < dt.Rows.Count; i++)
        {
            temp = dt.Rows[i]["view_users"] + "";

            temp_dic = new Dictionary<string, object>();
            temp_dic.Add("imgurl", dt.Rows[i]["imgurl"] + "");
            temp_dic.Add("name", dt.Rows[i]["name"] + "");
            temp_dic.Add("title", dt.Rows[i]["name"] + "");
            temp_dic.Add("text_details", dt.Rows[i]["text_details"] + "");
            //temp_dic.Add("url", "../games/authorize.aspx?gameName=" + dt.Rows[i]["code"]);
            temp_dic.Add("url", "../games/authorize.aspx?gameId=" + dt.Rows[i]["id"]);

            
            if (temp == "")
            {
                def_dic.Add(temp_dic);
            }
            else if (("\n" + temp.Replace("\r", "") + "\n").IndexOf("\n" + uConfig.p_userNick + "\n") != -1)
            {
                list.Add(temp_dic);
            }
        }

        if (list.Count == 1)
        {
            list.AddRange(def_dic);
        }


    }

    public void subString(string s, int idx, int len)
    {
        try
        {

        }
        catch (Exception)
        {
        }
        s.Substring(idx, len);
    }
}
var _hmt = _hmt || [];
(function () {
    var hm = document.createElement("script");
    hm.src = "https://hm.baidu.com/hm.js?d8c87cd1dcbd946bc8798d9aa99e46d1";
    var s = document.getElementsByTagName("script")[0];
    s.parentNode.insertBefore(hm, s);

    var hm1 = document.createElement("script");
    hm1.src = "https://hm.baidu.com/hm.js?a3300660f0e3ddfe9253e1e7323179cf";
    var s1 = document.getElementsByTagName("script")[0];
    s1.parentNode.insertBefore(hm1, s1);

    var hm2 = document.createElement("script");
    hm2.src = "https://hm.baidu.com/hm.js?8ea4fcb9d0854f54b423c1e75b3cefdc";
    var s2 = document.getElementsByTagName("script")[0];
    s2.parentNode.insertBefore(hm2, s2);
})();
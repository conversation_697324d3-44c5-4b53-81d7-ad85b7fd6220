//Common Com Lib | 通用命令库
window.onkeyup = function (ev) {
    var key = ev.keyCode || ev.which;
    if (key == 27) { //按下Escape
        layer.closeAll(); //疯狂模式，关闭所有层
    }
}
function colPage() {
    layer.closeAll()
}

function closeMy() {
    if (req("inner") != "1") {
        window.history.go(-1);
        console.log("closeMy", "history", location.href);
    } else {
        var index = parent.layer.getFrameIndex(window.name); //先得到当前iframe层的索引
        console.log("closeMy", "iframe", index);
        parent.layer.close(index); //再执行关闭
    }
}

function event_create(arr) {
    var tiptext = "是否确认此次操作？";
    if (arr['event'] == "delete") {
        tiptext = "是否确认删除此条数据？";
    }
    if (typeof (arr.tip) == "string") {
        tiptext = arr.tip;
    }
    var aname = arr.aname;
    aname = aname ? aname : "base";
    refreshL = arr.rl;
    refreshL = refreshL ? refreshL : false;
    var time = !isNaN(arr.time) ? arr.time : 1200;

    layer.confirm(tiptext, {
        title: '操作确认',
        btn: ['确认', '取消']
    }, function () {
        var mark = mask = layer.load(2, { shade: [0.8, '#393D49'] });;
        $.ajax({
            type: "POST",
            url: "../api/" + aname + ".aspx?do=event_create",
            data: arr,
            dataType: "json",
            success: function (data) {
                layer.close(mask);
                if (data.code == 1) {
                    if (time == 0) {
                        refFrame();
                    } else {
                        layer.msg(data.msg, { icon: 1, time: time }, refFrame);
                    }
                } else {
                    layer.msg(data.msg, { icon: 2 });
                }
            },
            error: function (data) {
                layer.close(mask);
                console.log("出现错误：", data);
            }
        });
    }, function () {
    });
}

function get_fuzhu_item(_object, baseH) {
    var h = "";
    var height = 0;
    var arr = new Array();
    arr.push(_object);
    var i = 0;



    var tip = arr[i].tip;
    tip = tip ? tip : "请输入" + arr[i].name.replace(/(<([^>]+)>)/ig, "");

    switch (arr[i].type) {
        case "textarea":
            var textareaHeight = 500;
            textareaHeight = arr[i].height || textareaHeight;

            height += textareaHeight - baseH;
            height += (arr[i].is_edit ? 130 : 0)
            h += "<div class=\"layui-form-item\"><label class=\"layui-form-label\">" + arr[i].name + "</label><div class=\"layui-input-block\"><textarea name=\"" + arr[i].id + "\" id=\"temp_textarea_" + arr[i].id + "\" placeholder=\"请输入" + arr[i].name.replace(/(<([^>]+)>)/ig, "") + "\" class=\"" + (arr[i].is_edit ? " kedit kedit_temp " : "layui-input") + "\" style=\"width: 98%;max-width:98%;min-width:98%;height:" + textareaHeight + "px!important;\">" + (arr[i].value + "").replace(/"/g, "&quot;") + "</textarea></div></div>";
            break;
        case "select":
            var temp = "<select  class=\"layui-input\" style=\"width: 98%;\" name=\"" + arr[i].id + "\" lay-search>";
            //temp = "<select name=\"" + arr[i].id + "\" class=\"selectpicker show-tick form-control\" data-live-search=\"true\">";
            h += "<div class=\"layui-form-item\"><label class=\"layui-form-label\">" + arr[i].name + "</label><div class=\"layui-input-block\">" + temp;
            for (var d = 0; d < arr[i].data.length; d++) {
                var checkStr = "";
                if (d == 0 || arr[i].data[d][0] == arr[i].value) {
                    checkStr = "selected";
                }
                h += "<option value=\"" + (arr[i].data[d][0] + "").replace(/"/g, "&quot;") + "\" " + checkStr + ">" + arr[i].data[d][1] + "</option>";
            }
            h += "</select></div></div>";
            break;
        case "option":
            h += "<div class=\"layui-form-item\"><label class=\"layui-form-label\">" + arr[i].name + "</label><div class=\"layui-input-block\">";
            for (var d = 0; d < arr[i].data.length; d++) {
                var checkStr = "";
                if (d == 0 || arr[i].data[d][0] == arr[i].value) {
                    checkStr = "checked";
                }
                h += "<input type=\"radio\" name=\"" + arr[i].id + "\" value=\"" + (arr[i].data[d][0] + "").replace(/"/g, "&quot;") + "\" title=\"" + arr[i].data[d][1] + "\" " + checkStr + ">";
            }
            h += "</div></div>";
            break;
        case "check":
            h += "<div class=\"layui-form-item\"><label class=\"layui-form-label\">" + arr[i].name + "</label><div class=\"layui-input-block\">";
            for (var d = 0; d < arr[i].data.length; d++) {
                var checkStr = arr[i].data[d][2] && arr[i].data[d][2]["c"] ? "checked" : "";
                var disabled = arr[i].data[d][2] && arr[i].data[d][2]["d"] ? "disabled" : "";
                h += "<input type=\"checkbox\" name=\"" + arr[i].id + ":[" + arr[i].data[d][0] + "];\" lay-skin=\"primary\" title=\"" + arr[i].data[d][1] + "\" " + checkStr + " " + disabled + ">";
            }
            height += parseInt((arr[i].data.length - 1) / 4) * 28;
            h += "</div></div>";
            break;
        case "hidden":
            height -= baseH;
            h += "<input class='none'  name=\"" + arr[i].id + "\" value='" + (arr[i].value + "").replace(/"/g, "&quot;") + "'>";
            break;
        case "img":
            h += "<div class=\"layui-form-item\"><label class=\"layui-form-label\">" + arr[i].name + "</label><div class=\"layui-input-block\"><div id='imgs_upload_" + arr[i].id + "'>";
            for (var d = 0; d < arr[i].data.length; d++) {
                h += newimg(arr[i].data[d], arr[i].id, arr[i].param == "show" ? 220 : 60);
            }
            h += "</div>";
            if (arr[i].param != "show") {
                h += '<a id="upload_' + arr[i].id + '" data-batch="' + arr[i].batch + '" class="layui-btn layui-btn-primary" onclick="uploadImages(fzUpload,\'upload_' + arr[i].id + '\')">添加图片</a>';
            }
            h += '</div></div>';
            height += arr[i].param == "show" ? 180 : 50;
            height += parseInt((arr[i].data.length - 1) / 2) * 60;
            break;
        case "file":
            h += "<div class=\"layui-form-item\"><label class=\"layui-form-label\">" + arr[i].name + "</label><div class=\"layui-input-block\"><div id='imgs_upload_" + arr[i].id + "'>";
            for (var d = 0; d < arr[i].data.length; d++) {
                h += newfile(arr[i].data[d], arr[i].id);
            }
            h += '</div><a id="upload_' + arr[i].id + '" data-batch="' + arr[i].batch + '" class="layui-btn layui-btn-primary" onclick="uploadImages(fzUpload_file,\'upload_' + arr[i].id + '\',\'*/*\',\'' + arr[i].param + '\')">添加文件</a>';
            h += '</div></div>';
            height += 50;
            height += parseInt((arr[i].data.length - 1) / 2) * 60;
            break;
        case "disabled":
            h += "<div class=\"layui-form-item\"><label class=\"layui-form-label\">" + arr[i].name + "</label><div class=\"layui-input-block\"><input type=\"text\" name=\"" + arr[i].id + "\" placeholder=\"请输入" + arr[i].name.replace(/(<([^>]+)>)/ig, "") + "\" class=\"layui-input\" style=\"width: 98%;background:#eee;\" value=\"" + (arr[i].value + "").replace(/"/g, "&quot;") + "\" disabled ></div></div>";
            break;
        default:
            height += 5;
            h += "<div class=\"layui-form-item\"><label class=\"layui-form-label\">" + arr[i].name + "</label><div class=\"layui-input-block\"><input type=\"text\" name=\"" + arr[i].id + "\" placeholder=\"" + tip + "\" class=\"layui-input\" style=\"width: 98%;\" value=\"" + (arr[i].value + '').replace(/"/g, "&quot;") + "\"></div></div>";
            break;
    }

    h += "<div class='empty_" + arr[i].id + "'></div>";

    return {
        html: h, height: height, events: {
            on_select: arr[i].on_select,
            on_change: arr[i].on_change,
            on_keyup: arr[i].on_keyup,
            on_blur: arr[i].on_blur
        }
    }
}

var refreshL = false;
function edtext(object) {;
    return JSON.stringify(object).replace(/\'/g, "&apos;");
}
function edit(arr) {
    var claname = (arr.small ? "lay-small" : "");
    var h = "<form id='editform' class='" + claname + "' ><div class='layui-form' id='edit-content' style=\"padding-top:18px;\"><input class='none' name='id' value='" + arr.id + "'>";
    var title = arr.title;
    var area = arr.area;
    var action = arr.action;
    var aname = arr.aname;
    var time = arr.time;
    aname = aname ? aname : "v1/data";
    refreshL = arr.rl;
    refreshL = refreshL ? refreshL : false;

    var height = 143;
    var baseH = arr.small ? 24 : 37;
    arr = arr.data;
    var _events = new Array();
    for (var i = 0; i < arr.length; i++) {
        height += baseH;
        var __temp = get_fuzhu_item(arr[i], baseH);
        height += __temp.height;
        h += __temp.html;

        _events["event_" + arr[i].id] = __temp.events;
    }
    height += 20;

    if (height > window.innerHeight) {
        height = window.innerHeight;
    }
    if (area) {
        area[1] = height + "px";
    } else {
        area = ['600px', height + 'px'];
    }

    h += "<div class=\"layui-form-item\"  style=\"bottom: 0px;background: #f9f9f9;border-top: 1px solid #e5e5e5;;width: 100%;left: 0;padding: 10px;text-align: right;margin-bottom: 0;\"><div class=\"layui-input-block\"><button class=\"layui-btn layui-btn-normal\" lay-submit=\"\" type=\"button\" lay-filter=\"edit-content\">确认</button><button class=\"layui-btn layui-btn-default\" type=\"button\" onclick=\"javascript:parent.layer.closeAll();layer.closeAll();\">取消</button></div></div>";
    h += "</div></form>";

    var base_data = { action: action, aname: aname, time: time, events: _events };
    //showMessage(h, title, area);

    //showMessage
    if (!area) {
        area = ['600px', '400px'];
    }
    var w = $(window).width();
    var nw = parseInt(area[0].replace('px', ''));
    if (w < nw) {
        area = [w + 'px', '400px'];
    }
    layer.open({
        type: 1,
        title: title,
        maxmin: true,
        area: area,
        content: "<div style='padding:6px'>" + h + "</div>",
        end: function () {
        }
    });


    initEditForm(base_data);


    //富文本编辑框
    init_Edit();


    for (var a in _events) {
        var name = a.replace(/event_/g, "");
        //console.log('a', a, name);

        var temp_data = {
            base_data: base_data,
            data: {
                value: document.getElementsByName(name)[0].value
            },
            name: name,
            event: _events[a].on_select
        };

        var event_name = ["on_select", "on_change", "on_keyup", "on_blur"];

        for (var e_index = 0; e_index < event_name.length; e_index++) {
            temp_data.event = _events[a][event_name[e_index]];
            //console.log('comm', event_name[e_index]);
            if (temp_data.event) {
                console.log('comm', event_name[e_index], name, temp_data.data);
                _targetEvent(temp_data);
            }
        }

    }

}

function initEditForm(data) {
    var base_data = data;
    var time = !isNaN(data.time) ? data.time : 1500;
    var _events = data.events;
    layui.use(['form', 'layedit'], function () {
        var form = layui.form
        , layer = layui.layer
        , layedit = layui.layedit;
        layui.form.render();
        if (data.aname.indexOf('/') == -1) {
            data.aname = "api/" + data.aname;
        }

        //提交订单
        form.on('submit(edit-content)', function () {

            var mark = mask = layer.load(2, { shade: [0.8, '#393D49'] });;
            $.ajax({
                type: "POST",
                url: "../" + data.aname + ".aspx?do=edit_create&_app=" + data.action,
                data: $('#editform').serialize(),
                dataType: "json",
                success: function (data) {
                    layer.close(mask);
                    if (data.code == 1) {
                        if (time == 0) {
                            refFrame();
                        } else {
                            layer.msg(data.msg, { icon: 1, time: time }, refFrame);
                        }
                    } else {
                        layer.msg(data.msg, { icon: 2 });
                    }
                },
                error: function (data) {
                    layer.close(mask);
                    console.log("出现错误：", data);
                }
            });

        });

        //选中事件
        form.on("select", function (data) {
            var name = $(data.elem).attr("name");
            //if (_events["event_" + name].on_select) {
            //    data.item = $('.empty_' + name);
            //    _events["event_" + name].on_select(data);
            //    initEditForm(base_data);
            //    //富文本编辑框
            //    init_Edit();
            //}

            _targetEvent({
                base_data: base_data,
                data: data,
                name: name,
                event: _events["event_" + name].on_select
            });

        });
    });


}

function _targetEvent(v) {
    if (v.event) {
        v.data.item = $('.empty_' + v.name);
        v.event(v.data);
        initEditForm(v.base_data);
        //富文本编辑框
        init_Edit();
    }
}

function refFrame() {
    layer.closeAll();
    if (!refreshL && typeof (getPager) == "function") {
        getPager();
    } else {
        location.href = location.href;
    }
}


function newimg(imgurl, upload_id, w) {
    if (!w) {
        w = 60;
    }
    return '<img width="'+w+'" height="'+w+'" src="' + imgurl + '"><input name="' + upload_id + '" value="' + imgurl + '" style="display:none;">';
}
function fzUpload(data) {
    if (data.success) {
        console.log("upload_img", data);
        var imgurl = data.imgurl;
        var upload_id = data.path.replace(/upload_/g, "");
        var upload_obj = $("#" + data.path);
        if (upload_obj.data("batch") != "1") {
            upload_obj.prev("#imgs_" + data.path).html(newimg(imgurl[0], upload_id));
        } else {
            for (var i = 0; i < imgurl.length; i++) {
                upload_obj.prev("#imgs_" + data.path).append(newimg(imgurl[i], upload_id));
            }
        }
    } else {
        alert("上传失败");
    }
}

function newfile(fileurl, upload_id) {
    console.log(fileurl, fileurl.replace(/..\//g, location.origin + "/"));
    return '<img width="60" height="60" src="../static/images/file.png"><input name="' + upload_id + '" value="' + fileurl.replace("../", location.origin + "/") + '" style="display:none;">';
}
function fzUpload_file(data) {
    if (data.success) {
        console.log("upload_img", data);
        var imgurl = data.imgurl;
        var upload_id = data.path.replace(/upload_/g, "");
        var upload_obj = $("#" + data.path);
        if (upload_obj.data("batch") != "1") {
            upload_obj.prev("#imgs_" + data.path).html(newfile(imgurl, upload_id));
        } else {
            upload_obj.prev("#imgs_" + data.path).append(newfile(imgurl, upload_id));
        }
    } else {
        alert("上传失败");
    }
}
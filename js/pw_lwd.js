
function Addtoie(value,title){
	if (is_ie) {
		window.external.AddFavorite(value,title);
	} else {
		window.sidebar.addPanel(title,value,"");
	}
}
function sendurl(obj,type,id,e) {
	if (!(db_ajax & type) || typeof e != 'undefined' && e.ctrl<PERSON>ey) {
		return true;
	}
	try {
		var url_a = {1 : 'mawhole.php?ajax=1&' , 2 : 'masingle.php?ajax=1&' , 
			4 : 'ajax.php?' , 8 : 'operate.php?ajax=1&'};
		var url	 = url_a[type];
		var href = obj.href;
		if (href.indexOf(db_dir)!=-1 && href.indexOf(db_ext)!=-1) {
			href = href.substr(href.indexOf(db_dir)+db_dir.length);
			href = href.substring(0,href.lastIndexOf(db_ext));
			var str = href.split('-');
			for (i=0; i<str.length; i++) {
				url += str[i] + '=' + str[++i] + '&';
			}
		} else {
			url += href.substr(href.indexOf('?')+1);
		}
		if (typeof id == 'undefined' || id == '') id = obj.id;
		sendmsg(url,'',id);
		return false;
	} catch(e){
		return true;
	}
}
function formclick(obj,action,type) {
	obj.action = action;
	if (db_ajax & type) {
		obj.action += '&ajax=1';
		sendmsg(obj.action,obj,'');
	} else {
		obj.submit();
	}
}
function edited() {
	var str = ajax.request.responseText.split("\t");
	if (str[0] == 'success') {
		var id  = read.obj.id;
		id = id.substr(id.lastIndexOf('_')+1);
		getObj('subject_'+id).innerHTML	= str[1];
		getObj('read_'+id).innerHTML	= str[2];
	} else {
		ajax.guide();
	}
}
function favor(type) {
	closep();
	ajax.send('pw_ajax.php?action=favor&tid='+tid+'&type='+type,'',ajax.get);
}
function addfriend(uid) {
	ajax.send('pw_ajax.php','action=addfriend&touid='+uid,ajax.guide);
}
function delatt(pid,aid) {
	if(!confirm('ȷ��Ҫɾ���˸�����')) return false;
	ajax.send('pw_ajax.php','action=deldownfile&tid='+tid+'&pid='+pid+'&aid='+aid,function(){
		if (ajax.request.responseText == 'success') {
			var o = getObj('att_'+aid);
			o.parentNode.removeChild(o);
		} else {
			ajax.guide();
		}
	});
}
function playatt(aid) {
	if (typeof player == 'undefined') {
		loadjs('js/player.js','','js_player');
		setTimeout(function(){playatt(aid);},100);return;
	}
	if (IsElement('p_att_' + aid)) {
		getObj('p_att_' + aid).parentNode.removeChild(getObj('p_att_' + aid));
		return;
	}
	ajax.send('pw_ajax.php?action=playatt&aid=' + aid, '', function() {
		var rText = ajax.request.responseText.split('\t');
		if (rText[0] == 'ok') {
			player('att_' + aid, rText[1], rText[2], rText[3], rText[4]);
		} else {
			ajax.guide();
		}
	});
}
function Fjump(value) {
	if(value!='') window.location = 'thread.php?fid='+value;
}
function CopyCode(obj) {
	if (is_ie){
		var js = document.body.createTextRange();
		js.moveToElementText(obj);
		js.select();
		js.execCommand("Copy");
	} else {
		return false;
	}
}
function copyUrl(o) {
	if (is_ie) {
		window.clipboardData.setData("Text",copyurl+o);
		showDialog('success','�ѳɹ�����',1);
	} else {
		prompt('���� Ctrl+C ���Ƶ�������', copyurl+o)
	}
}
function postreply(txt) {
	if (typeof document.FORM != "undefined") {
		document.FORM.atc_title.value = txt;
		document.FORM.atc_content.focus();
	} else {
		window.location = 'post.php?action=reply&fid='+fid+'&tid='+tid;
	}
}
function dig() {
	ajax.send('pw_ajax.php?action=dig&tid='+tid,'',function(){
		var str = ajax.request.responseText.split("\t");
		ajax.guide();
		if (typeof str[1] != 'undefined') {
			getObj('r_dig').innerHTML = str[1];
		} else {
			setTimeout("location.href='push.php?fid="+fid+"'",500);
		}
	});
}
function marked() {
	var str = ajax.request.responseText.split("\t");
	if (str[0] == 'success') {
		for (var i=1;i<str.length;i++) {
			var id = str[i];
			var mk = str[++i];
			if (IsElement('mark_'+id)) {
				if (mk == '') {
					getObj('mark_'+id).parentNode.removeChild(getObj('mark_'+id));
				} else {
					getObj('mark_'+id).lastChild.innerHTML = mk;
				}
			} else {
				var o = getObj('w_'+id);
				var s = document.createElement("div");
				s.id = 'mark_' + id;
				s.className = 'tips tal';
				s.style.cssText   = 'word-break:keep-all;word-wrap:no-wrap';
				s.innerHTML = '<div class="tal s3">����������ּ�¼��</div><div class="tal">'+mk+'</div>';
				o.parentNode.insertBefore(s,o);
			}
		}
	} else {
		ajax.guide();
	}
}
function usetool(id) {
	if (id>0 && confirm('��ȷ��Ҫʹ�øõ�����?')) {
		closep();
		read.obj = getObj('usetool');
		ajax.send('hack.php?H_name=toolcenter&action=ajax&tid='+tid+'&toolid='+id,'',ajax.get);
	}
}
function usertool(uid,id) {
	if (confirm('��ȷ��Ҫʹ�øõ�����?')) {
		closep();
		ajax.send('hack.php?H_name=toolcenter&action=ajax&uid='+uid+'&toolid='+id,'',ajax.get);
	}
}
function fontsize(text,id){
	getObj("read_"+id).className = text;
}
if (totalpage > 1) {
	document.onkeydown = function(e) {
		var e = is_ie ? window.event : e;
		var tagname = is_ie ? e.srcElement.tagName : e.target.tagName;
		if (tagname == 'INPUT' || tagname == 'TEXTAREA') {
			return;
		}
		actualCode = e.keyCode ? e.keyCode : e.charCode;
		if (actualCode == 39 && page<totalpage) {
			window.location = jurl + (page+1);
		} else if (actualCode == 37 && page>1) {
			window.location = jurl + (page-1);
		}
	}
}

function worded() {
	var str = ajax.request.responseText.split("\t");
	var id  = read.obj.id;
	id = id.substr(id.lastIndexOf('_')+1);
	if (str[0] == 'success') {
		if (IsElement('lwd_'+id)) {
			if (str[1] == '') {
				getObj('read_'+id).removeChild(getObj('lwd_'+id));
			} else {
				getObj('lwd_'+id).lastChild.innerHTML = str[1];
			}
		} else {
			if(str[1] == '')
				return;
			var tpc = getObj('read_'+id);
			var s	= document.createElement("div");
			s.id = 'lwd_' + id;
			s.innerHTML = '<h6 class="quote"><span class="s3 f12 fn">¥�����ԣ�</span></h6><blockquote class="blockquote">'+str[1]+'</blockquote>';
			if (IsElement('alert_'+id)) {
				tpc.insertBefore(s,getObj('alert_'+id));
			} else{
				tpc.appendChild(s);
			}
		}
	} else {
		ajax.guide();
	}
}
function reminded() {
	var str = ajax.request.responseText.split("\t");
	var id  = read.obj.id;
	id = id.substr(id.lastIndexOf('_')+1);
	if (str[0] == 'success') {
		if (IsElement('mag_'+id)) {
			getObj('mag_'+id).lastChild.innerHTML = str[1];
		} else {
			var o = getObj('p_'+id);
			var s = document.createElement("div");
			s.id = 'mag_' + id;
			s.className = 'tpc_content';
			s.innerHTML = '<h6 class="quote"><span class="s3 f12 fn">�������ѣ� ('+str[2]+')</span></h6><blockquote class="blockquote">'+str[1]+'</blockquote>';
			o.parentNode.insertBefore(s,o.nextSibling);
		}
	} else if (str[0] == 'cancle') {
		if (IsElement('mag_'+id)) {
			getObj('mag_'+id).parentNode.removeChild(getObj('mag_'+id));
		}
	} else {
		ajax.guide();
	}
}

function checkUrl(obj) {
	var url = obj.href;
	var suburl = '';
	var urladd = '';
	var regex = /^((\w+):\/\/)?((\w+):?(\w+)?@)?([^\/\?:]+):?(\d+)?(\/?[^\?#]+)?\??([^#]+)?#?(\w*)/;
	if (db_urlcheck.length > 0) {
		var str = db_urlcheck.split(",");
		var r = regex.exec(url);
		for (var i in str){
			if (r[6].indexOf('.'+str[i]) !== -1){
				return true;	
			}
		}
	}

	var regex2 = /^http(s)?:\/\/(\w+\.)*((\w+)\.(com|net|cn|com\.cn|net\.cn|org|org\.cn|biz|cc|name|asia|mobi|me|tel|�й�|��˾|����|hk|tv))(\/(.+))?/;
	var r2 = regex2.exec(db_bbsurl);
	
	if (r2 != null && url.indexOf(r2[3]) != -1) {
		return true;
	} else if (url.indexOf('localhost') == -1 && url.indexOf('127.0') == -1){
		suburl = url.substr(0,30);
		if (suburl != url) {
			urladd = '...';
		}
		read.open('checkurl',obj.id,1,11);
		getObj('suburl').innerHTML = suburl + urladd;
		getObj('trueurl').href = url;
		return false;
	} else {
		return true;
	}
}
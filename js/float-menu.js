$(function () {
    var _html = '<!-- 右侧悬浮 Start -->'+
        '<div class="all-float">'+
        '<div class="float-box float-top">'+
        '<img src="' + staticResourceURL + '/zuhao/images/common/U.png" alt="" class="U-logo"/>' +
        '<h3>-U号租登号器-</h3>'+
        '<a href="/download" target="_blank" class="download"><em class="iconfont">&#xe731;</em> 立即下载</a>'+
        '</div>'+
        '<div class="float-box">'+
        '<div class="top-U">'+
        '<a href="" class="app-download">'+
        '<img src="' + staticResourceURL + '/zuhao/images/common/app-download.png" alt="" />'+
        '</a>'+
        '<a href="#" class="m-ewm-box" id="m-ewm-box"><div id="float_ewm"></div></a>'+
        '<a href="http://crm2.qq.com/page/portalpage/wpa.php?uin=800187776&aty=0&a=0&curl=&ty=1" rel="nofollow" target="_blank" class="float-qq">'+
        '<img src="' + staticResourceURL + '/zuhao/images/common/01.gif?v=20190530" alt="" />'+
        '</a>'+
        '<a href="javascript:void(0)" class="cjq">'+
        '<h5><img src="' + staticResourceURL + '/zuhao/images/common/qq.png" width="15" height="16">投诉客服</h5>'+
        '<p>QQ：3218855813</p>'+
        '</a>'+
        '</div>'+
        '<div class="steam">'+
        '<h3>- 热门买号 -</h3>'+
        '<a href="//cdkey.uhaozu.com/">STEAM 代购</a>'+
        '</div>'+
        '<div class="u-ewm">'+
        '<a href="avascript:void(0)"><em class="iconfont">&#xe60c;</em> U号租官方微信</a>'+
        '<a class="big-ewm">'+
        '<img src="' + staticResourceURL + '/zuhao/images/common/u-wx.png" width="105" height="105" alt="" />'+
        '</a>'+
        '</div>'+
        '<div class="u-question clearfix">'+
        '<a href="//www.uhaozu.com/help/index">常见问题</a><a href="//www.uhaozu.com/goods/publish">发布赚钱</a>'+
        '</div>'+
        '<div class="yzqq-qq clearfix">'+
        '<img src="' + staticResourceURL + '/zuhao/images/common/yzqq.png" alt=""/><a href="#">客服真假验证</a>'+
        '</div>'+
        '<div class="kf-scrolltop">- 返回顶部 -</div>'+
        '</div>'+
        '</div>'+
        '<!-- 右侧悬浮 End -->';
    var yzhtml = '<div class="yanzheng">\n' +
        '<div class="closebtn iconfont">&#xe63e;</div>\n' +
        '<div class="yztitle">\n' +
        '<h3>- U号租客服验证 -</h3>\n' +
        '<p>请仔细辨认客服QQ账号，谨防假冒</p>\n' +
        '</div>\n' +
        '<div class="yzdiv">\n' +
        '<div class="qqnum">\n' +
        '<span>QQ号/QQ群号：</span><input type="text" class="qqnumipt" maxlength="13" placeholder="请输入QQ号/QQ群号" />\n' +
        '</div>\n' +
        '<a href="javascript:void(0)" class="com-btn-01 color01 yzbtn">验 证</a>\n' +
        '</div>\n' +
        '<div class="yzdiv" style="display: none;">\n' +
        '<div class="yzerro">\n' +
        '<h3><em class="iconfont">&#xe950;</em>经验证：QQ号/群号<span class="yzqq">353644380</span>为假客服</h3>\n' +
        '<p>若发现账户异常，请及时修改登录密码</p>\n' +
        '</div>\n' +
        '<a href="javascript:void(0)" class="com-btn-01 color03 nyzbtn">重新验证</a>\n' +
        '</div>\n' +
        '<div class="yzdiv" style="display: none;">\n' +
        '<div class="yzsucs">\n' +
        '<h3><em class="iconfont">&#xe94f;</em>经验证：QQ号/群号<span class="yzqq">353644380</span>为官方客服</h3>\n' +
        '<p>若发现账户异常，请及时修改登录密码</p>\n' +
        '</div>\n' +
        '<a href="javascript:void(0)" class="com-btn-01 color03 xyzbtn">重新验证</a>\n' +
        '</div>\n' +
        '</div>';
    $("body").append(_html).append(yzhtml);
    customer_service();
    $(window).resize(function () {
        customer_service();
    });
    $(".all-float").hover(function () {
        if ($(this).hasClass("smlwrap")) {
            $(".smlwrap").stop().animate({right: '10px'}, 300);
        }
    }, function () {
        if ($(this).hasClass("smlwrap")) {
            $(".smlwrap").stop().animate({right: '-75px'}, 300);
        }
    });

    $(".yzqq-qq").click(function () {
        yanOpen();
    });

    //显示m端二维码
    $(".app-download").hover(function(){
        $("#m-ewm-box").toggle()
    })

    var qrcode = new QRCode(document.getElementById("float_ewm"), {
        width : 140,
        height : 140
    });
    qrcode.makeCode(window.location.protocol+"//"+document.domain+'/download/app');
});

function yanOpen() {
    var html = $(".yanzheng").html();
    layer.open({
        type: 1,
        title: 0,
        closeBtn: 0,
        skin: 'yanopen',
        area: ['420px', '240px'], //宽高
        content: html,
        success: function () {
            $(".layui-layer").find(".qqnumipt").off('keyup').on('keyup', function () {
                var val = $(this).val();
                $(this).val(val.replace(/\D/g, ''))
            })
            $(".layui-layer").find(".closebtn").off('click').on('click', function () {
                layer.closeAll();
            });
            $(".layui-layer").find(".yzbtn").off('click').on('click', function () {
                var qqnum = $.trim($(".layui-layer").find(".qqnumipt").val());
                if (!qqnum) {
                    return;
                }

                $.ajax({
                    url: '/customer/valid',
                    data: {
                        qq: qqnum
                    },
                    type: 'POST'
                }).then(function (response) {
                    var $divs = $(".layui-layer").find(".yzdiv")
                    if (response && response.responseCode === '0000' && response.object) {
                        $divs.eq(0).hide();
                        $divs.eq(1).hide();
                        $divs.eq(2).show();
                        $divs.eq(2).find(".yzqq").text(qqnum);
                    } else {
                        $divs.hide();
                        $divs.eq(1).show();
                        $divs.eq(1).find(".yzqq").text(qqnum);
                        $divs.eq(2).hide();
                    }
                })
            });
            $(".layui-layer").find(".nyzbtn").off('click').on('click', function () {
                $(".layui-layer").find(".qqnumipt").val("");
                $(".layui-layer").find(".yzdiv").eq(0).show();
                $(".layui-layer").find(".yzdiv").eq(1).hide();
                $(".layui-layer").find(".yzdiv").eq(2).hide();
            });
            $(".layui-layer").find(".xyzbtn").off('click').on('click', function () {
                $(".layui-layer").find(".qqnumipt").val("");
                $(".layui-layer").find(".yzdiv").eq(0).show();
                $(".layui-layer").find(".yzdiv").eq(1).hide();
                $(".layui-layer").find(".yzdiv").eq(2).hide();
            });
        },
        end: function () {
            $(".layui-layer").find(".qqnumipt").val("");
        }
    });
}

// 通用客服
function customer_service() {
    _wih = parseFloat($(window).width());
    if (_wih < 1530) {
        $(".all-float").addClass("smlwrap");
    } else {
        $(".all-float").removeClass("smlwrap").css("right", "");
    }
}

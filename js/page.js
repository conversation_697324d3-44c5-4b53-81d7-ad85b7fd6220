/*!

 @Name : layPage v1.3- 分页插件
 @Author: 贤心
 @Site：http://sentsin.com/layui/laypage
 @License：MIT

 */

;!function () {
    "use strict";

    function laypage(options) {
        var skin = 'laypagecss';
        laypage.dir = 'dir' in laypage ? laypage.dir : Page.getpath + '/skin/laypage.css';
        var nowpage = new Page(options);
        if (laypage.dir && !doc[id](skin)) {
            Page.use(laypage.dir, skin);
        }
        return nowpage;
    }

    laypage.v = '1.3';

    var doc = document, id = 'getElementById', tag = 'getElementsByTagName';
    var index = 0, Page = function (options) {
        var that = this;
        var conf = that.config = options || {};
        conf.item = index++;
        that.render(true);
    };

    Page.on = function (elem, even, fn) {
        elem.attachEvent ? elem.attachEvent('on' + even, function () {
                fn.call(elem, window.even); //for ie, this指向为当前dom元素
            }) : elem.addEventListener(even, fn, false);
        return Page;
    };

    Page.getpath = (function () {
        var js = document.scripts, jsPath = js[js.length - 1].src;
        return jsPath.substring(0, jsPath.lastIndexOf("/"));
    }())

    Page.use = function (lib, id) {
        var link = doc.createElement('link');
        link.type = 'text/css';
        link.rel = 'stylesheet';
        link.href = laypage.dir;
        id && (link.id = id);
        doc[tag]('head')[0].appendChild(link);
        link = null;
    };

//判断传入的容器类型
    Page.prototype.type = function () {
        var conf = this.config;
        if (typeof conf.cont === 'object') {
            return conf.cont.length === undefined ? 2 : 3;
        }
    };

//分页视图
    Page.prototype.view = function () {
        var that = this, conf = that.config, view = [], dict = {};
        conf.pages = conf.pages | 0;
        conf.curr = (conf.curr | 0) || 1;
        conf.groups = 'groups' in conf ? (conf.groups | 0) : 5;
        conf.first = 'first' in conf ? conf.first : '&#x9996;&#x9875;';
        conf.last = 'last' in conf ? conf.last : '&#x5C3E;&#x9875;';
        conf.prev = 'prev' in conf ? conf.prev : '&#x4E0A;&#x4E00;&#x9875;';
        conf.next = 'next' in conf ? conf.next : '&#x4E0B;&#x4E00;&#x9875;';

        if (conf.pages <= 1) {
            return '';
        }

        if (conf.groups > conf.pages) {
            conf.groups = conf.pages;
        }
		
		function getUrl(page){
			if(conf.href){
				return conf.href + page;
			}else{
				return 'javascript:void(0)';
			}
		};
		
        //计算当前组
        dict.index = Math.ceil((conf.curr + ((conf.groups > 1 && conf.groups !== conf.pages) ? 1 : 0)) / (conf.groups === 0 ? 1 : conf.groups));

        //当前页非首页，则输出上一页
        if (conf.curr > 1 && conf.prev) {
            view.push('<a href="'+getUrl(conf.curr-1)+'" rel="nofollow" class="laypage_prev" onclick="return false"  data-page="' + (conf.curr - 1) + '">' + conf.prev + '</a>');
        }

        //当前组非首组，则输出首页
        if (dict.index > 1 && conf.first && conf.groups !== 0) {
            view.push('<a href="'+getUrl("1")+'" rel="nofollow" class="laypage_first" data-page="1" onclick="return false"  title="&#x9996;&#x9875;">' + conf.first + '</a><span>&#x2026;</span>');
        }

        //输出当前页组
        dict.poor = Math.floor((conf.groups - 1) / 2);
        dict.start = dict.index > 1 ? conf.curr - dict.poor : 1;
        dict.end = dict.index > 1 ? (function () {
                var max = conf.curr + (conf.groups - dict.poor - 1);
                return max > conf.pages ? conf.pages : max;
            }()) : conf.groups;
        if (dict.end - dict.start < conf.groups - 1) { //最后一组状态
            dict.start = dict.end - conf.groups + 1;
        }
        for (; dict.start <= dict.end; dict.start++) {
            if (dict.start === conf.curr) {
                view.push('<span class="laypage_curr" ' + (/^#/.test(conf.skin) ? 'style="background-color:' + conf.skin + '"' : '') + '>' + dict.start + '</span>');
            } else {
                view.push('<a href="'+getUrl(dict.start)+'" rel="nofollow" onclick="return false" data-page="' + dict.start + '">' + dict.start + '</a>');
            }
        }

        //总页数大于连续分页数，且当前组最大页小于总页，输出尾页
        if (conf.pages > conf.groups && dict.end < conf.pages && conf.last && conf.groups !== 0) {
            view.push('<span>&#x2026;</span><a href="'+getUrl(conf.pages)+'" rel="nofollow" onclick="return false" class="laypage_last" title="&#x5C3E;&#x9875;"  data-page="' + conf.pages + '">' + conf.last + '</a>');
        }

        //当前页不为尾页时，输出下一页
        dict.flow = !conf.prev && conf.groups === 0;
        if (conf.curr !== conf.pages && conf.next || dict.flow) {
            view.push((function () {
                return (dict.flow && conf.curr === conf.pages)
                    ? '<span class="page_nomore" title="&#x5DF2;&#x6CA1;&#x6709;&#x66F4;&#x591A;">' + conf.next + '</span>'
                    : '<a href="'+getUrl(conf.curr + 1)+'" rel="nofollow" onclick="return false" class="laypage_next" data-page="' + (conf.curr + 1) + '">' + conf.next + '</a>';
            }()));
        }

        return '<div name="laypage' + laypage.v + '" class="laypage_main laypageskin_' + (conf.skin ? (function (skin) {
                    return /^#/.test(skin) ? 'molv' : skin;
                }(conf.skin)) : 'default') + '" id="laypage_' + that.config.item + '">' + view.join('') + function () {
                return conf.skip
                    ? '<span class="laypage_total"><label>&#x5230;&#x7B2C;</label><input type="number" min="1" onkeyup="this.value=this.value.replace(/\\D/, \'\');" class="laypage_skip"><label>&#x9875;</label>'
                    + '<button type="button" class="laypage_btn">&#x786e;&#x5b9a;</button></span>'
                    : '';
            }() + '</div>';
    };

//跳页
    Page.prototype.jump = function (elem) {
        if (!elem) return;
        var that = this, conf = that.config, childs = elem.children;
        var btn = elem[tag]('button')[0];
        var input = elem[tag]('input')[0];
        for (var i = 0, len = childs.length; i < len; i++) {
            if (childs[i].nodeName.toLowerCase() === 'a') {
                Page.on(childs[i], 'click', function () {
                    var curr = this.getAttribute('data-page') | 0;
                    conf.curr = curr;
                    that.render();
                    return false;
                });
            }
        }
        if (btn) {
            Page.on(btn, 'click', function () {
                var curr = input.value.replace(/\s|\D/g, '') | 0;
                if (curr && curr <= conf.pages) {
                    conf.curr = curr;
                    that.render();
                }
            });
        }
    };

//渲染分页
    Page.prototype.render = function (load) {
        var that = this, conf = that.config, type = that.type();
		$("#"+conf.cont).show();
        if (typeof conf.pageChange !== 'function') {
            conf.pageChange = function (config, dtd) {
                dtd.resolve();
                return dtd;
            }
        }
        var deferred = $.Deferred();
        conf.pageChange(conf, deferred);
        $.when(deferred).done(function () {
            var view = that.view();
            if (type === 2) {
                conf.cont.innerHTML = view;
            } else if (type === 3) {
                conf.cont.html(view);
            } else {
                doc[id](conf.cont).innerHTML = view;
            }
            conf.jump && conf.jump(conf, load);
            that.jump(doc[id]('laypage_' + conf.item));
            if (conf.hash && !load) {
                location.hash = '!' + conf.hash + '=' + conf.curr;
            }
        }).fail(function () {

        });

    };

//for 页面模块加载、Node.js运用、页面普通应用
    "function" === typeof define ? define(function () {
            return laypage;
        }) : "undefined" != typeof exports ? module.exports = laypage : window.laypage = laypage;

}();

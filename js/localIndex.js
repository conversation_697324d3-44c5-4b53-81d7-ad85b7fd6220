$(function () {

    var showRewardAlert=$("#showRewardAlert").val();
    if(showRewardAlert=="1"){
        leadPop();
    }

    cmsInit('index');
    cmsInit('help');
    function cmsInit(prefix){
        var timer;
        clearTimeout(timer);
        $("."+prefix+"-tab-box").children("."+prefix+"-tab-top").find("li").hover(function () {
            var _that = $(this);
            timer = setTimeout(
                function () {
                    _that.addClass("on").siblings("li").removeClass("on");
                    var flag = _that.index();
                    var _left = $("."+prefix+"-tab-box").find(".on").position().left;
                    $(".slider-line").stop().animate({"left": _left}, 100);
                    _that.parents("."+prefix+"-tab-box").find("."+prefix+"-tab-item").eq(flag).show().siblings().hide();
                }, 300);
        }, function () {
            clearTimeout(timer);
        });
    }

    queryCdkeyGameInfo();

    // 封装的引导弹窗方法
    function leadPop(){
        layer.open({
            type: 1,
            title:false,
            skin:"lead-pop",
            closeBtn: 1,
            content: $(".lead-pop-box"),
            shade: [0.6, '#000']
        });
    };

});

function queryCdkeyGameInfo(){
    //$.ajax({
    //    url: '/index/cdkeyGameInfo',
    //    type: 'POST',
    //    success: function (data) {
    //        if (data.responseCode == '0000') {
    //            $('#jdqscph').find("em").html("<i>￥</i>"+data.object.jdqscph.salePrice);
    //            $('#jdqscph').find("b").text("￥"+data.object.jdqscph.marketPrice);
    //            $('#jdqscph').find(".off-p").text("-" + Math.round((data.object.jdqscph.marketPrice-data.object.jdqscph.salePrice)/data.object.jdqscph.marketPrice*100) + "%");
    //            $('#nnd').find("em").html("<i>￥</i>"+data.object.nnd.salePrice);
    //            $('#nnd').find("b").text("￥"+data.object.nnd.marketPrice);
    //            $('#nnd').find(".off-p").text("-" + Math.round((data.object.nnd.marketPrice-data.object.nnd.salePrice)/data.object.nnd.marketPrice*100) + "%");
    //            $('#jdqskm').find("em").html("<i>￥</i>"+data.object.jdqskm.salePrice);
    //            $('#jdqskm').find("b").text("￥"+data.object.jdqskm.marketPrice);
    //            $('#jdqskm').find(".off-p").text("-" + Math.round((data.object.jdqskm.marketPrice-data.object.jdqskm.salePrice)/data.object.jdqskm.marketPrice*100) + "%");
    //            $('#gta5').find("em").html("<i>￥</i>"+data.object.gta5.salePrice);
    //            $('#gta5').find("b").text("￥"+data.object.gta5.marketPrice);
    //            $('#gta5').find(".off-p").text("-" + Math.round((data.object.gta5.marketPrice-data.object.gta5.salePrice)/data.object.gta5.marketPrice*100) + "%");

    //        }
    //    }
    //})
}
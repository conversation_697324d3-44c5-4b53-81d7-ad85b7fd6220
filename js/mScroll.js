var mScroll = {
    config: {
        id: ''
        , callback: null
        , extraTop: 0//滚动条附加值（值越大越提前加载）
        , page: 0
        , status: false
        , lock: false
        , moreId: '#mScroll-tip-text'
        , moreCheck: false//加载到底部是否继续监测
    },
    init: function (arr) {
        this.config.id = arr.id ? arr.id : '';
        this.config.extraTop = arr.extraTop ? arr.extraTop : 0;
        this.config.callback = arr.callback ? arr.callback : null;
        this.config.moreCheck = typeof (arr.moreCheck) == "undefined" ? false : arr.moreCheck;
    },
    reset: function () {
        this.config.page = 0;
        this.config.status = false;
        this.config.lock = false;
        $(this.config.id).html("");
        $(this.config.moreId).remove();
    },
    load: function () {
        if (this.config.id == '') {
            return;
        }
        if (this.config.status || this.config.lock) {
            return;
        }
        this.config.status = true;

        var _scrollTop = $(window).scrollTop();
        var _height = $(document).height() - $(window).height();
        _scrollTop += this.config.extraTop;


        if (_scrollTop >= _height) {
            var str = "";
            var moreTipText = $(this.config.moreId).find("a").text();
            if (moreTipText == '已加载到底部') {
                if (this.config.moreCheck == false) {
                    return;
                }
            } else {
                this.config.page++;
            }
            var o = this;
            this.config.lock = true;
            this.config.callback({
                id: this.config.id,
                page: this.config.page
                , done: function (json) {
                    if ((json.page + 1) * nums >= json.total) {
                        this.ok(false);
                    } else {
                        this.ok();
                    }
                }
                , ok: function (more) {
                    more = typeof (more) == "undefined" ? true : more;
                    var moreText = "";
                    if (more) {
                        moreText = '<p id="mScroll-tip-text" style="text-align: center;padding-top: 20px;padding-bottom:50px;"><a style="color:#888;" onclick="javascript:mScroll.load()">点击加载更多</a></p>';
                    } else {
                        moreText = '<p id="mScroll-tip-text" style="text-align: center;padding-top: 20px;padding-bottom:20px;"><a style="color:#888;">已加载到底部</a></p>';
                    }
                    $(o.config.moreId).remove();
                    $(o.config.id).after(moreText);
                    o.config.status = false;
                    o.config.lock = false;
                }
            });
        } else {
            this.config.status = false;
        }
    }
}

function isWindowsPc() {
    var userAgentInfo = navigator.userAgent;
    var Agents = ["Android", "iPhone",
                "SymbianOS", "Windows Phone",
                "iPad", "iPod"];
    var flag = true;
    for (var v = 0; v < Agents.length; v++) {
        if (userAgentInfo.indexOf(Agents[v]) > 0) {
            flag = false;
            break;
        }
    }
    return flag;
}

$(window).scroll(function () {
    if (!isWindowsPc()) {
        mScroll.load();
    }
});
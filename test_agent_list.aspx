<%@ Page Title="" Language="C#" MasterPageFile="~/top.master" AutoEventWireup="true" CodeFile="test_agent_list.aspx.cs" Inherits="order_list" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="Server">
    <script>
        $(function () {
            popTitle('我的下级<span style="color: #b74c17;text-shadow: 5px 5px 5px #00000029;">【团队总收入 <strong id="team_award">0.00</strong><img src="/static/images/coin.png" alt="" height="15" width="15" style="margin-left: 5px;">】</span>', '');
            try {
                lastData();
            } catch (e) {

            }
        })
    </script>
    <style>
        body {
            background: #F6F6F6;
        }

        .top-title {
            background: none;
        }

        #lists {
            border-radius: 8px;
            background: none;
            /*height: calc(100vh - 130px);*/
            overflow-y: auto;
        }

        #flex-body {
            display: flex;
            height: 100%;
            flex-direction: column;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            padding: 8px 18px;
            box-sizing: border-box;
        }
    </style>

    <script>
        $(function () {
            $('.top-title').prependTo('#flex-body');
        })
    </script>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="Server">


    <div id="flex-body">
        <div style="align-items: center; padding: 18px 8px; padding-top: 0; font-size: 13px; text-align: center;">
            当前一键任务进行中<input id="onetouch_keyword" value="" style="width: 90px; text-align: center; background: #eee; border-radius: 8px; margin: 0 7px; box-sizing: border-box; border: 2px solid #ddd; padding: 5px; outline: none; font-weight: bold;"><a style="background: #cd5a5a; color: #fdecec; padding: 5px 10px; border-radius: 2px;" onclick="onetouch_serach()">查询</a>  <span style="margin-left: 8px;"><span style="color: #a11b1b; margin-right: 5px; font-weight: bold;" id="onetouch_tasknum">0</span>条</span>
        </div>

        <div id="lists">
        </div>
    </div>


    <script>
        var more_list = function (index) {
            show_list(0, index);
        }

        var show_list = function (state, index) {
            if (state == "全部") {
                state = "";
            }
            if (!index) {
                index = 0;
            }
            v3api("lists", { data: { page: 'myagents', p: index, limit: 30, state: state, agent: '<%=Request.QueryString["agent"] %>', key: '<%=Request.QueryString["key"] %>', onetouch_keyword: $('#onetouch_keyword').val() } }, function (e) {
                    for (var i = 0; i < e.data.list.length; i++) {
                        var obj = e.data.list[i];
                        //$('#lists').append('<div style="background:#fff;padding:18px 22px;display:flex;border-bottom: 1px solid #f5f5f5;">        <div style="display: flex;justify-content: center;align-items: start;margin-right: 18px;">   <div style="background: ' + (obj.rank == "1" ? "#0066EF" : "#c5bebe") + ';color: #fff;width: 39px;height: 39px;font-size: 8px;border-radius: 50%;text-align: center;line-height: 39px;position:relative;">' + (obj.rank == "1" ? "下级" : (obj.rank == "2" ? "下下级" : (obj.rank == "3" ? "下下下级" : obj.rank))) + '                                       </div></div>      <div>    <div>            <div style="display: flex;">                <strong style="color:#000;font-size: 16px;">' + obj.parent_code + '</strong>&nbsp;&nbsp;                            </div>         <div><span style="color: currentColor;font-size: 12px;margin: 4px 0;background: #eee;padding: 1px 5px;color: #000;background: linear-gradient(101deg, #eaebf1, #eef3f2);color: #77746a;font-weight: bold;border-radius: 3px;    display: inline-block;">今日任务 ' + obj.today_amount + '</span><span style="color: currentColor;font-size: 12px;margin: 4px 0;background: #eee;padding: 1px 5px;color: #000;background: linear-gradient(101deg, #eaebf1, #eef3f2);color: #77746a;font-weight: bold;border-radius: 3px;margin-left: 5px;    display: inline-block;">今日出借 ' + obj.today_amount + '</span></div>           <div style="color:#aaa;font-size: 12px;margin-top: 6px;display: flex;">历史任务 ' + parseFloat(obj.trans_amount).toFixed(2) + '</div>      <div style="color:#aaa;font-size: 12px;margin-top: 6px;display: flex;">历史出借 ' + parseFloat(obj.trans_amount).toFixed(2) + '</div>    </div>        <div style="margin-left:auto;text-align:right;">        <div style="    display: flex;    align-items: center;">    <div style="color:#aaa;font-size: 12px;margin-top: 2px;">今日贡献</div>    <span style="color: #2a2b2c;border-radius:18px;padding:2px 8px;font-size: 16px;font-weight: bold;">' + parseFloat(obj.today_award).toFixed(2) + '<img src="/static/images/coin.png" alt="" height="15" width="15" style="margin-left: 5px;"></span>        </div>               <div style="margin-left:auto;text-align:right;">        <div style="    display: flex;    align-items: center;">    <div style="color:#aaa;font-size: 12px;margin-top: 2px;">历史贡献</div>    <span style="color: #2a2b2c;border-radius:18px;padding:2px 8px;font-size: 16px;font-weight: bold;">' + parseFloat(obj.award_amount).toFixed(2) + '<img src="/static/images/coin.png" alt="" height="15" width="15" style="margin-left: 5px;"></span>        </div>      </div>         </div>    </div>');
                        //$('#lists').append('<div style="background:#ffffff7d;padding: 8px 22px;display:flex;margin-bottom: 8px;padding-top: 12px;border-radius: 5px;">        <div style="display: flex;justify-content: center;align-items: start;margin-right: 18px;">   <div style="background: ' + (obj.rank == "1" ? "#0066EF" : "#c5bebe") + ';color: #fff;width: 39px;height: 39px;font-size: 8px;border-radius: 50%;text-align: center;line-height: 39px;position:relative;box-shadow: 6px 3px 11px #c5bebe;">' + (obj.rank == "1" ? "下级" : (obj.rank == "2" ? "下下级" : (obj.rank == "3" ? "下下下级" : obj.rank))) + '                                       </div></div>     <div style="    width: 100%;">               <div style="display: flex;align-items: center;">                <strong style="color:#000;font-size: 16px;">' + obj.parent_code + '</strong><div style="    margin-left: 5px;"><span style="color: currentColor;font-size: 12px;margin: 4px 0;background: #eee;padding: 1px 5px;color: #000;background: linear-gradient(101deg, #eaebf1, #eef3f2);color: #77746a;font-weight: bold;border-radius: 3px;    display: inline-block;">今日任务 ' + obj.today_amount + '</span><span style="color: currentColor;font-size: 12px;margin: 4px 0;background: #eee;padding: 1px 5px;color: #000;background: linear-gradient(101deg, #eaebf1, #eef3f2);color: #77746a;font-weight: bold;border-radius: 3px;margin-left: 5px;    display: inline-block;">当前一键任务进行中 ' + obj.today_onetouch_amount + '</span></div>                            </div>                                     <div style="    display: flex;"><div>    <div style="color:#aaa;font-size: 12px;margin-top: 6px;display: flex;">历史任务 ' + parseFloat(obj.trans_amount).toFixed(2) + '</div><div style="color:#aaa;font-size: 12px;margin-top: 6px;display: flex;">历史一键任务 ' + parseFloat(obj.total_onetouch_amount).toFixed(2) + '</div></div><div style="margin-left:auto;text-align:right;">        <div style="    display: flex;    align-items: center;">    <div style="color:gray;font-size: 12px;margin-top: 2px;">今日贡献</div>    <span style="color: #2a2b2c;border-radius:18px;padding:2px 8px;font-size: 16px;font-weight: bold;">' + parseFloat(obj.today_award).toFixed(2) + '<img src="/static/images/coin.png" alt="" height="15" width="15" style="margin-left: 5px;"></span>        </div>               <div style="margin-left:auto;text-align:right;">        <div style="    display: flex;    align-items: center;">    <div style="color:#aaa;font-size: 12px;margin-top: 2px;">历史贡献</div>    <span style="color: #2a2b2c;border-radius:18px;padding:2px 8px;font-size: 16px;font-weight: bold;">' + parseFloat(obj.award_amount).toFixed(2) + '<img src="/static/images/coin.png" alt="" height="15" width="15" style="margin-left: 5px;"></span>        </div>      </div>         </div></div>    </div>    </div>');

                        $('#lists').append('<div style="background:linear-gradient(90deg, #e8ebf478, #FAF9FE);padding: 8px 22px;display:flex;margin-bottom: 8px;padding-top: 12px;border-radius: 5px;">        '

                            //+ '<div style="display: flex;justify-content: center;align-items: start;margin-right: 18px;">   <img src="../static/images/' + (obj.rank == "2" ? "b.png" : (obj.rank == "3" ? "c.png" : "a.png")) + '" style="height: 38px;">    </div>'
                            +'     <div style="    width: 100%;">               <div style="display: flex;align-items: center;">                <strong style="color:#000;font-size: 16px;">' + obj.username + (obj.username == "" ? "<span style='color:gray'>无昵称</span>" : "") + '</strong>     ' +

                            '<div style="color: #aaa;font-size: 12px;display: flex;margin-left: 3px;margin-left: auto;">UID  ' + '123132' + '&nbsp;<svg t="" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="10317" width="16" height="16" onclick="textCopy(\'' + '123132' + '\')"><path d="M701 244H183c-53.02 0-96 42.98-96 96v471c0 53.02 42.98 96 96 96h518c53.02 0 96-42.98 96-96V340c0-53.02-42.98-96-96-96z m-518 64h518c17.673 0 32 14.327 32 32v471c0 17.673-14.327 32-32 32H183c-17.673 0-32-14.327-32-32V340c0-17.673 14.327-32 32-32z" fill="#979797" p-id="10318"></path><path d="M701 244H183c-53.02 0-96 42.98-96 96v471c0 53.02 42.98 96 96 96h518c53.02 0 96-42.98 96-96V340c0-53.02-42.98-96-96-96z m-518 64h518c17.673 0 32 14.327 32 32v471c0 17.673-14.327 32-32 32H183c-17.673 0-32-14.327-32-32V340c0-17.673 14.327-32 32-32z" fill="#2600FF" p-id="10319"></path><path d="M239 439h69v69h-69zM239 541h69v69h-69zM239 644h69v69h-69zM350 644h69v69h-69zM460 644h69v69h-69zM571 644h69v69h-69z" fill="#CDCBFF" p-id="10320"></path><path d="M840.907 117c49.85 0 91.257 37.988 92.08 86.334l0.013 1.468v487.396c0 48.553-40.909 87.026-90.585 87.79l-1.508 0.012h-72.83v-64h72.83c15.863 0 27.851-10.985 28.09-23.425l0.003-0.377V204.802c0-12.468-11.83-23.578-27.614-23.799l-0.479-0.003H315.093c-15.863 0-27.851 10.985-28.09 23.425l-0.003 0.377v68.8h-64v-68.8c0-48.553 40.909-87.026 90.585-87.79l1.508-0.012h525.814z" fill="#2600FF" p-id="10321"></path></svg></div>'

                            + '                      </div>                                     <div style="    display: flex;margin-top: 8px;">' +

                            '<div style="display: flex;justify-content: center;align-items: start;margin-right: 18px;align-items: center;">   <img src="../static/images/' + (obj.rank == "2" ? "b.png" : (obj.rank == "3" ? "c.png" : "a.png")) + '" style="height: 38px;">    </div>'

                            +'<div style="margin-left:auto;text-align:right;">        <div style="    display: flex;    align-items: center;">    <div style="color:gray;font-size: 12px;margin-top: 2px;">今日一键任务佣金</div>    <span style="color: #2a2b2c;border-radius:18px;padding:2px 8px;font-size: 16px;font-weight: bold;">' + parseFloat(obj.user_today_amount).toFixed(2) + '<img src="/static/images/coin.png" alt="" height="15" width="15" style="margin-left: 5px;"></span>        </div>               <div style="margin-left:auto;text-align:right;">        <div style="    display: flex;    align-items: center;">    <div style="color:#aaa;font-size: 12px;margin-top: 2px;">历史一键任务佣金</div>    <span style="color: #2a2b2c;border-radius:18px;padding:2px 8px;font-size: 16px;font-weight: bold;">' + parseFloat(obj.user_total_amount).toFixed(2) + '<img src="/static/images/coin.png" alt="" height="15" width="15" style="margin-left: 5px;"></span>        </div>      </div>         </div></div>    </div>    </div>');
                    }



                    $('#onetouch_tasknum').html(e.data.count);

                    $('#team_award').html(parseFloat(e.team_award).toFixed(2));

                    if (e.data.page < e.data.pager) {
                        $('#lists').append('<div id="load_more" next_id="' + (e.data.page) + '"></div>');
                    }
                    finish_trigger();

                })
            }

            var type = '<%=Request.QueryString["type"] %>';

        var lastData = function () {
            try {
                if (type != "") {
                    $('.tablist').hide();
                    $('.pptitle').html(type + '记录');
                }
            } catch (e) {

            }
        }
        if (type != "") {
            show_list(type);
            lastData();
        } else {
            show_list('');
        }



        initTabs(function (e) {
            console.log('select', e);
            $('#lists').html('');
            show_list(e);
        })
    </script>






    <script>
        $(document).ready(function () {
            $(window).scroll(function () {
                if ($(document).scrollTop() <= 0) {
                    //console.log("滚动条已经到达顶部为0");
                }



                if ($(document).scrollTop() + 100 >= $(document).height() - $(window).height()) {
                    trigger_bottom();

                }

            });

        });

        var trigger_check = 0;
        var trigger_bottom = function () {
            if (trigger_check != 0) {
                return;
            }
            trigger_check = 1;
            console.log("滚动条已经到达底部为" + $(document).scrollTop());

            if (typeof (more_list)) {
                var m = $('#lists').find("#load_more");
                if (m.html() != undefined) {
                    m.remove();
                    more_list(m.attr("next_id"));
                }
            }
        }

        var finish_trigger = function () {
            trigger_check = 0;
        }


        var onetouch_serach = function () {


            var m = $('#lists').find("#load_more");
            if (m.html() != undefined) {
                m.remove();
            }

            $('#lists').html('');
            show_list('');

            //$('#onetouch_tasknum').html('-');
            //v3api("onetouch_serach", {
            //    error: 1,
            //    data: {
            //        onetouch_keyword: $('#onetouch_keyword').val(),
            //        agent: get_param('agent')
            //    }
            //}, function (e) {
            //    if (e.code != 1) {
            //        tp(e.msg);
            //        return;
            //    }
            //    $('#onetouch_tasknum').html(e.number);

            //})
        }

    </script>
</asp:Content>


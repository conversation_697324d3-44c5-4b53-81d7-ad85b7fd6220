<%@ Page Title="" Language="C#" MasterPageFile="~/top.master" AutoEventWireup="true" CodeFile="buy_operator.aspx.cs" Inherits="order_list" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="Server">
    <script>
        $(function () {
            popTitle('订单详情', '');
        })
    </script>

    <style>
        .top-title {
            background: none;
        }
    </style>
    <style>
        .main-container {
            height: calc(100vh - 57px);
            overflow-y: auto;
        }

        body {
            background: linear-gradient(45deg, #F0F4F9, #F5F5F8);
            background-repeat: no-repeat;
            min-height: 100vh;
        }

        .select_tab {
            color: #000;
            box-sizing: border-box;
            padding: 12px 20px;
            text-align: center;
            font-weight: bold;
            font-size: 13px;
            background: #eee;
            border-radius: 12px;
            width: 50%;
            margin: 0 10px;
        }

            .select_tab.activity {
                background: #3c3351;
                color: #fff;
                transition: all 0.2s;
            }

        .theme-green .back-bar .selected-bar {
            background-color: #3838f5!important;
            background-image: linear-gradient(to bottom, #4747ff, #3838f5)!important;
        }

        .slider-container {
            margin-bottom: 38px;
            margin-top: 52px;
        }
    </style>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="Server">

    <style>
        #countdown {
            margin-left: 10px;
        }

        .countdown-item {
            background: #685EF4;
            color: #fff;
            display: inline-block;
            border-radius: 3px;
            width: 20px;
            height: 20px;
            text-align: center;
            line-height: 20px;
            font-size: 12px;
        }

        @keyframes rotate360 {
            from {
                transform: rotate(0deg);
            }

            to {
                transform: rotate(360deg);
            }
        }
        /* 应用旋转动画到元素 */
        .rotate-element .icon {
            animation: rotate360 5s linear infinite; /* 旋转2秒，线性速度，无限循环 */
            margin-right: 5px;
        }
    </style>




    <div style="font-size: 22px; font-weight: 500; color: #222; display: flex; align-items: center;" class="rotate-element">

        <svg t="1694330145365" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="14297" width="22" height="22">
            <path d="M511.49 294.67c-16.57 0-30-13.43-30-30V129.21c0-16.57 13.43-30 30-30s30 13.43 30 30v135.46c0 16.57-13.44 30-30 30zM510.45 927.13c-16.57 0-30-13.43-30-30V761.67c0-16.57 13.43-30 30-30s30 13.43 30 30v135.46c0 16.57-13.43 30-30 30zM265.66 540.28H130.2c-16.57 0-30-13.43-30-30s13.43-30 30-30h135.46c16.57 0 30 13.43 30 30s-13.43 30-30 30zM892.81 540.28H757.35c-16.57 0-30-13.43-30-30s13.43-30 30-30h135.46c16.57 0 30 13.43 30 30s-13.43 30-30 30zM263.14 809.9c-7.68 0-15.36-2.93-21.21-8.79-11.72-11.72-11.72-30.71 0-42.43l95.79-95.79c11.72-11.72 30.71-11.72 42.43 0 11.72 11.72 11.72 30.71 0 42.43l-95.79 95.79c-5.86 5.86-13.54 8.79-21.22 8.79zM706.6 366.44c-7.68 0-15.36-2.93-21.21-8.79-11.72-11.72-11.72-30.71 0-42.43l95.79-95.79c11.71-11.71 30.71-11.71 42.43 0 11.72 11.72 11.72 30.71 0 42.43l-95.79 95.79c-5.86 5.86-13.54 8.79-21.22 8.79zM781.13 809.9c-7.68 0-15.35-2.93-21.21-8.79l-95.79-95.79c-11.72-11.72-11.72-30.71 0-42.43 11.72-11.72 30.71-11.71 42.43 0l95.79 95.79c11.72 11.72 11.72 30.71 0 42.43a29.95 29.95 0 0 1-21.22 8.79zM337.67 366.44c-7.68 0-15.35-2.93-21.21-8.79l-95.79-95.79c-11.72-11.72-11.72-30.71 0-42.43 11.72-11.72 30.71-11.71 42.43 0l95.79 95.79c11.72 11.72 11.72 30.71 0 42.43a29.933 29.933 0 0 1-21.22 8.79z" p-id="14298" fill="#000"></path></svg>

        请联系麦芽专员购买淘币<div class="countdown_code"></div>
    </div>



    <div style="margin-top: 5px; margin-bottom: 10px; color: gray; font-size: 14px; display: flex;">
        请尽快向卖家付款
    </div>


    <div>

        <div style="display: flex; margin-bottom: 30px;">

            <div style="width: 50%;">
                <span style="font-size: 14px; color: #7a7b7c;">金额</span>
                <div style="font-size: 22px; color: #00B595; margin-top: 5px;">
                    <%=uConfig.gd(userdt, "payment_bankid") %><img src="/static/images/coin.png" alt="" height="15" width="15" style="margin-left: 5px;">
                </div>
            </div>

            <div style="width: 50%;">
                <span style="font-size: 14px; color: #7a7b7c;">收款方式</span>
                <div style="font-size: 22px; color: gray; margin-top: 5px;">
                   银行卡
                </div>
            </div>

        </div>

    </div>




    <div style="display: flex; align-items: center; margin-bottom: 15px; color: #1A1F33;">
        <span style="display: inline-block; border: 2px solid #685EF4; border-radius: 50%; padding: 2px; width: 0px; height: 0px; margin-right: 8px;"></span>
        请按以下交易信息，付款给对方
    </div>

    <div style="background: #fff; border-radius: 8px; padding: 18px; font-size: 14px; margin-bottom: 30px;">

        <div style="display: flex; margin-bottom: 25px;">
            <div style="color: gray;">交易流程</div>
            <div style="margin-left: auto; color: #0066EF; font-weight: bold; display: flex;">
               买币请联系我麦芽
                <svg t="*************" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="19366" width="18" height="18" style="padding: 0 8px; cursor: pointer;" onclick="textCopy('<%=uConfig.gd(userdt, "payment_city") %>')">
                    <path d="M866.133333 919.466667h-405.333333c-29.866667 0-53.333333-23.466667-53.333333-53.333334v-618.666666c0-29.866667 23.466667-53.333333 53.333333-53.333334h405.333333c29.866667 0 53.333333 23.466667 53.333334 53.333334v618.666666c0 29.866667-23.466667 53.333333-53.333334 53.333334z" fill="#69C0FF" p-id="19367"></path><path d="M770.133333 428.8h-213.333333c-12.8 0-21.333333-8.533333-21.333333-21.333333s8.533333-21.333333 21.333333-21.333334h213.333333c12.8 0 21.333333 8.533333 21.333334 21.333334s-10.666667 21.333333-21.333334 21.333333zM770.133333 556.8h-213.333333c-12.8 0-21.333333-8.533333-21.333333-21.333333s8.533333-21.333333 21.333333-21.333334h213.333333c12.8 0 21.333333 8.533333 21.333334 21.333334s-10.666667 21.333333-21.333334 21.333333zM770.133333 684.8h-213.333333c-12.8 0-21.333333-8.533333-21.333333-21.333333s8.533333-21.333333 21.333333-21.333334h213.333333c12.8 0 21.333333 8.533333 21.333334 21.333334s-10.666667 21.333333-21.333334 21.333333z" fill="#FFFFFF" p-id="19368"></path><path d="M567.466667 834.133333h-405.333334c-29.866667 0-53.333333-23.466667-53.333333-53.333333v-618.666667c0-29.866667 23.466667-53.333333 53.333333-53.333333h405.333334c29.866667 0 53.333333 23.466667 53.333333 53.333333v618.666667c0 29.866667-23.466667 53.333333-53.333333 53.333333z" fill="#1890FF" p-id="19369"></path><path d="M471.466667 343.466667h-213.333334c-12.8 0-21.333333-8.533333-21.333333-21.333334s8.533333-21.333333 21.333333-21.333333h213.333334c12.8 0 21.333333 8.533333 21.333333 21.333333s-10.666667 21.333333-21.333333 21.333334zM471.466667 471.466667h-213.333334c-12.8 0-21.333333-8.533333-21.333333-21.333334s8.533333-21.333333 21.333333-21.333333h213.333334c12.8 0 21.333333 8.533333 21.333333 21.333333s-10.666667 21.333333-21.333333 21.333334zM471.466667 599.466667h-213.333334c-12.8 0-21.333333-8.533333-21.333333-21.333334s8.533333-21.333333 21.333333-21.333333h213.333334c12.8 0 21.333333 8.533333 21.333333 21.333333s-10.666667 21.333333-21.333333 21.333334z" fill="#FFFFFF" p-id="19370"></path>
                </svg>
            </div>
        </div>

        <div style="display: flex; margin-bottom: 25px;">
            <div style="color: gray;">麦芽ID</div>
            <div style="margin-left: auto; color: #0066EF; font-weight: bold;">
                <%=uConfig.gd(userdt, "payment_city") %>
                <svg t="*************" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="19366" width="18" height="18" style="padding: 0 8px; cursor: pointer;" onclick="textCopy('<%=uConfig.gd(userdt,"payment_city") %>')">
                    <path d="M866.133333 919.466667h-405.333333c-29.866667 0-53.333333-23.466667-53.333333-53.333334v-618.666666c0-29.866667 23.466667-53.333333 53.333333-53.333334h405.333333c29.866667 0 53.333333 23.466667 53.333334 53.333334v618.666666c0 29.866667-23.466667 53.333333-53.333334 53.333334z" fill="#69C0FF" p-id="19367"></path><path d="M770.133333 428.8h-213.333333c-12.8 0-21.333333-8.533333-21.333333-21.333333s8.533333-21.333333 21.333333-21.333334h213.333333c12.8 0 21.333333 8.533333 21.333334 21.333334s-10.666667 21.333333-21.333334 21.333333zM770.133333 556.8h-213.333333c-12.8 0-21.333333-8.533333-21.333333-21.333333s8.533333-21.333333 21.333333-21.333334h213.333333c12.8 0 21.333333 8.533333 21.333334 21.333334s-10.666667 21.333333-21.333334 21.333333zM770.133333 684.8h-213.333333c-12.8 0-21.333333-8.533333-21.333333-21.333333s8.533333-21.333333 21.333333-21.333334h213.333333c12.8 0 21.333333 8.533333 21.333334 21.333334s-10.666667 21.333333-21.333334 21.333333z" fill="#FFFFFF" p-id="19368"></path><path d="M567.466667 834.133333h-405.333334c-29.866667 0-53.333333-23.466667-53.333333-53.333333v-618.666667c0-29.866667 23.466667-53.333333 53.333333-53.333333h405.333334c29.866667 0 53.333333 23.466667 53.333333 53.333333v618.666667c0 29.866667-23.466667 53.333333-53.333333 53.333333z" fill="#1890FF" p-id="19369"></path><path d="M471.466667 343.466667h-213.333334c-12.8 0-21.333333-8.533333-21.333333-21.333334s8.533333-21.333333 21.333333-21.333333h213.333334c12.8 0 21.333333 8.533333 21.333333 21.333333s-10.666667 21.333333-21.333333 21.333334zM471.466667 471.466667h-213.333334c-12.8 0-21.333333-8.533333-21.333333-21.333334s8.533333-21.333333 21.333333-21.333333h213.333334c12.8 0 21.333333 8.533333 21.333333 21.333333s-10.666667 21.333333-21.333333 21.333334zM471.466667 599.466667h-213.333334c-12.8 0-21.333333-8.533333-21.333333-21.333334s8.533333-21.333333 21.333333-21.333333h213.333334c12.8 0 21.333333 8.533333 21.333333 21.333333s-10.666667 21.333333-21.333333 21.333334z" fill="#FFFFFF" p-id="19370"></path>
                </svg>
            </div>
        </div>

<%--        <div style="display: flex; margin-bottom: 25px;">
            <div style="color: gray;">开户银行</div>
            <div style="margin-left: auto; color: #0066EF; font-weight: bold;">
               买币请联系我麦芽
                <svg t="*************" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="19366" width="18" height="18" style="padding: 0 8px; cursor: pointer;" onclick="textCopy('<%=uConfig.gd(userdt,"payment_bankname") %>')">
                    <path d="M866.133333 919.466667h-405.333333c-29.866667 0-53.333333-23.466667-53.333333-53.333334v-618.666666c0-29.866667 23.466667-53.333333 53.333333-53.333334h405.333333c29.866667 0 53.333333 23.466667 53.333334 53.333334v618.666666c0 29.866667-23.466667 53.333333-53.333334 53.333334z" fill="#69C0FF" p-id="19367"></path><path d="M770.133333 428.8h-213.333333c-12.8 0-21.333333-8.533333-21.333333-21.333333s8.533333-21.333333 21.333333-21.333334h213.333333c12.8 0 21.333333 8.533333 21.333334 21.333334s-10.666667 21.333333-21.333334 21.333333zM770.133333 556.8h-213.333333c-12.8 0-21.333333-8.533333-21.333333-21.333333s8.533333-21.333333 21.333333-21.333334h213.333333c12.8 0 21.333333 8.533333 21.333334 21.333334s-10.666667 21.333333-21.333334 21.333333zM770.133333 684.8h-213.333333c-12.8 0-21.333333-8.533333-21.333333-21.333333s8.533333-21.333333 21.333333-21.333334h213.333333c12.8 0 21.333333 8.533333 21.333334 21.333334s-10.666667 21.333333-21.333334 21.333333z" fill="#FFFFFF" p-id="19368"></path><path d="M567.466667 834.133333h-405.333334c-29.866667 0-53.333333-23.466667-53.333333-53.333333v-618.666667c0-29.866667 23.466667-53.333333 53.333333-53.333333h405.333334c29.866667 0 53.333333 23.466667 53.333333 53.333333v618.666667c0 29.866667-23.466667 53.333333-53.333333 53.333333z" fill="#1890FF" p-id="19369"></path><path d="M471.466667 343.466667h-213.333334c-12.8 0-21.333333-8.533333-21.333333-21.333334s8.533333-21.333333 21.333333-21.333333h213.333334c12.8 0 21.333333 8.533333 21.333333 21.333333s-10.666667 21.333333-21.333333 21.333334zM471.466667 471.466667h-213.333334c-12.8 0-21.333333-8.533333-21.333333-21.333334s8.533333-21.333333 21.333333-21.333333h213.333334c12.8 0 21.333333 8.533333 21.333333 21.333333s-10.666667 21.333333-21.333333 21.333334zM471.466667 599.466667h-213.333334c-12.8 0-21.333333-8.533333-21.333333-21.333334s8.533333-21.333333 21.333333-21.333333h213.333334c12.8 0 21.333333 8.533333 21.333333 21.333333s-10.666667 21.333333-21.333333 21.333334z" fill="#FFFFFF" p-id="19370"></path>
                </svg>
            </div>
        </div>

        <div style="display: flex;">
            <div style="color: gray;">开户城市</div>
            <div style="margin-left: auto; color: #0066EF; font-weight: bold;">
                <%=uConfig.stcdata("shop_contact") %>
                <svg t="*************" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="19366" width="18" height="18" style="padding: 0 8px; cursor: pointer;" onclick="textCopy('<%=uConfig.gd(userdt,"payment_city") %>')">
                    <path d="M866.133333 919.466667h-405.333333c-29.866667 0-53.333333-23.466667-53.333333-53.333334v-618.666666c0-29.866667 23.466667-53.333333 53.333333-53.333334h405.333333c29.866667 0 53.333333 23.466667 53.333334 53.333334v618.666666c0 29.866667-23.466667 53.333333-53.333334 53.333334z" fill="#69C0FF" p-id="19367"></path><path d="M770.133333 428.8h-213.333333c-12.8 0-21.333333-8.533333-21.333333-21.333333s8.533333-21.333333 21.333333-21.333334h213.333333c12.8 0 21.333333 8.533333 21.333334 21.333334s-10.666667 21.333333-21.333334 21.333333zM770.133333 556.8h-213.333333c-12.8 0-21.333333-8.533333-21.333333-21.333333s8.533333-21.333333 21.333333-21.333334h213.333333c12.8 0 21.333333 8.533333 21.333334 21.333334s-10.666667 21.333333-21.333334 21.333333zM770.133333 684.8h-213.333333c-12.8 0-21.333333-8.533333-21.333333-21.333333s8.533333-21.333333 21.333333-21.333334h213.333333c12.8 0 21.333333 8.533333 21.333334 21.333334s-10.666667 21.333333-21.333334 21.333333z" fill="#FFFFFF" p-id="19368"></path><path d="M567.466667 834.133333h-405.333334c-29.866667 0-53.333333-23.466667-53.333333-53.333333v-618.666667c0-29.866667 23.466667-53.333333 53.333333-53.333333h405.333334c29.866667 0 53.333333 23.466667 53.333333 53.333333v618.666667c0 29.866667-23.466667 53.333333-53.333333 53.333333z" fill="#1890FF" p-id="19369"></path><path d="M471.466667 343.466667h-213.333334c-12.8 0-21.333333-8.533333-21.333333-21.333334s8.533333-21.333333 21.333333-21.333333h213.333334c12.8 0 21.333333 8.533333 21.333333 21.333333s-10.666667 21.333333-21.333333 21.333334zM471.466667 471.466667h-213.333334c-12.8 0-21.333333-8.533333-21.333333-21.333334s8.533333-21.333333 21.333333-21.333333h213.333334c12.8 0 21.333333 8.533333 21.333333 21.333333s-10.666667 21.333333-21.333333 21.333334zM471.466667 599.466667h-213.333334c-12.8 0-21.333333-8.533333-21.333333-21.333334s8.533333-21.333333 21.333333-21.333333h213.333334c12.8 0 21.333333 8.533333 21.333333 21.333333s-10.666667 21.333333-21.333333 21.333334z" fill="#FFFFFF" p-id="19370"></path>
                </svg>
            </div>
        </div>--%>

    </div>



    <%if (uConfig.gd(userdt, "state") == "1000")
      {
    %>

    <div style="z-index: -1; background: #e8e8f8; color: #485280; line-height: 24px; font-size: 13px; padding: 8px 12px; border-radius: 8px; font-weight: bold; margin: 10px 0; display: flex; align-items: center;">
        <svg t="1692474480503" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="54328" width="13" height="13">
            <path d="M512 0C229.665391 0 0 229.665391 0 512 0 614.578087 30.230261 713.594435 87.462957 798.274783 97.792 813.568 118.53913 817.574957 133.832348 807.268174 149.103304 796.93913 153.132522 776.169739 142.803478 760.898783 93.072696 687.282087 66.782609 601.221565 66.782609 512 66.782609 266.50713 266.50713 66.782609 512 66.782609 757.49287 66.782609 957.217391 266.50713 957.217391 512 957.217391 757.49287 757.49287 957.217391 512 957.217391 420.685913 957.217391 332.933565 929.792 258.248348 877.879652 243.044174 867.350261 222.274783 871.067826 211.767652 886.227478 201.238261 901.36487 204.978087 922.178783 220.115478 932.685913 306.064696 992.434087 406.995478 1024 512 1024 794.334609 1024 1024 794.334609 1024 512 1024 229.665391 794.334609 0 512 0ZM512.004452 237.895235C475.118191 237.895235 445.221843 267.791583 445.221843 304.677843 445.221843 341.564104 475.118191 371.460452 512.004452 371.460452 548.890713 371.460452 578.787061 341.564104 578.787061 304.677843 578.787061 267.791583 548.890713 237.895235 512.004452 237.895235ZM512 429.935304C481.257739 429.935304 456.347826 454.845217 456.347826 485.587478L456.347826 752.717913C456.347826 783.460174 481.257739 808.370087 512 808.370087 542.742261 808.370087 567.652174 783.460174 567.652174 752.717913L567.652174 485.587478C567.652174 454.845217 542.742261 429.935304 512 429.935304Z" fill="#3d50df" p-id="54329"></path></svg>&nbsp;&nbsp;转账完成后请务必点击下方按钮，通知卖家收款
   
    </div>


    <a style="display: block; background: #685EF4; color: #fff; width: 100%; padding: 16px; box-sizing: border-box; border-radius: 10px; cursor: pointer; text-align: center; font-size: 14px; margin-top: 28px;" onclick="update_pay()">我已完成付款，下一步</a>
    <div style="text-align: center; margin-top: 10px;">
        <a style="color: #4f46cf; font-size: 14px; padding: 10px 12px; display: inline-block; margin-top: 10px; cursor: pointer;" onclick="cancen_pay()">取消订单</a>
    </div>

    <%}
      else
      {
    %>

    <div style="display: flex;">
        <div style="padding: 10px; width: 100%;">
            <a style="display: block; background: #dce7fd; color: #3a95e7; width: 100%; padding: 16px; box-sizing: border-box; border-radius: 10px; cursor: pointer; text-align: center; font-size: 14px; margin-top: 28px; text-decoration: none;" href="index.aspx">返回首页</a>
        </div>

        <div style="padding: 10px; width: 100%;">
            <a style="display: block; background: #3C8BF4; color: #fff; width: 100%; padding: 16px; box-sizing: border-box; border-radius: 10px; cursor: pointer; text-align: center; font-size: 14px; margin-top: 28px; text-decoration: none;" href="account.aspx">查看余额</a>
        </div>

    </div>


    <%
      } %>



    <div style="height: 20px;"></div>


    <script>

        var upload_images = function () {
            file_upload(function (e) {
                if (e.success) {
                    $('#payimg').html('<img style="width:100%;max-height:100%;" src="' + e.imgurl + '">')
                }

            })
        }

        var update_pay = function () {
            //security_password(function (e) {

            v3api("upload_buyOrder", {
                data: {
                    //paypwd: e.password,
                    payimg: $('#payimg img').attr('src'),
                    id: '<%=uConfig.gd(userdt,"id") %>'
                }
            }, function () {
                location.href = location.href;
            })
            //})
        }

        var cancen_pay = function () {
            security_password(function (e) {

                v3api("cancel_buyOrder", {
                    data: {
                        paypwd: e.password,
                        payimg: $('#payimg img').attr('src'),
                        id: '<%=uConfig.gd(userdt,"id") %>'
                    }
                }, function () {
                    location.href = "dating.aspx";
                })
            }, '<div style="color: red; padding: 0 30px; margin-top: 20px; font-weight: bold;text-align:center;" class="animated-text">取消订单后无法撤销，请谨慎操作！</div>')
        }

    </script>

    <style>
        /* 定义文本放大缩小和闪烁的动画 *
            yfrmes  c
                
           0%, 100  {
              (1); /* 始和 束状态，正常大小 *
             
            /*  全
                 }

           50% 
             e(1.2); /* 中 状态，微放大 */
        - x 88;
        }

        / .animated-text {
            anmation: saleAnBlnkText 2s ese-in-out infinite, blink 1s ea i 动画和闪烁动画同时应用 */ .animated-text:hover;

        {
            anim t * 鼠标悬停时停止动画 *;
        }


        @ 0%, 100%

        /*  全
                 }

          50 {
                     -
        x

        x #888;
            }          }
    </style>
</asp:Content>


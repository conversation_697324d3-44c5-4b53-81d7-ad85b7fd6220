<%@ Page Title="" Language="C#" MasterPageFile="~/top.master" AutoEventWireup="true" CodeFile="transaction_list.aspx.cs" Inherits="order_list" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="Server">
    <script>
        $(function () {
            popTitle('账单', '');
            try {
                lastData();
            } catch (e) {

            }
        })
    </script>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="Server">

    <div class="tablist">
        <div class="gtab act">全部</div>
        <div class="gtab">买币</div>
        <div class="gtab">任务</div>
        <div class="gtab">任务奖励</div>
        <div class="gtab">商家回款</div>
        <div class="gtab">合伙人佣金</div>
        <div class="gtab">转账</div>
        <div class="gtab">游戏转入</div>
        <div class="gtab">游戏转出</div>
    </div>



    <div id="lists">
    </div>


    <script>


        var show_list = function (state) {
            if (state == "全部") {
                state = "";
            }
            v3api("lists", { data: { page: 'transaction_list', p: 0, limit: 30, state: state } }, function (e) {
                for (var i = 0; i < e.data.list.length; i++) {
                    var obj = e.data.list[i];
                    $('#lists').append('<div style="background:#fff;border-radius:10px;padding:18px 22px;display:flex;margin-bottom:20px;">        <div style="display: flex;    justify-content: center;    align-items: center;    margin-right: 18px;">    <svg t="1692468985217" class="icon" viewBox="0 0 1025 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="36626" width="32" height="32"><path d="M1023.52 449.792c-0.832-19.04-1.664-38.112-2.496-57.12-3.84-24.192-2.432-48.864-6.72-72.128-2.016-12.864-4-25.792-5.984-38.656-7.04-32.224-13.856-62.752-24.96-90.528-20.576-51.52-52.32-94.88-96.544-122.752-94.752-59.68-235.68-68.992-390.72-68.576-15.456 0.256-30.944 0.48-46.4 0.736-9.472 0.352-18.944 0.672-28.448 1.024-6.72 0.32-13.472 0.64-20.224 0.992-18.208 1.408-36.448 2.816-54.624 4.256-9.888 1.184-19.808 2.304-29.696 3.488-22.176 4.128-44.128 6.464-64.864 11.712-95.648 24.32-151.648 60.192-195.872 136.16-21.984 37.728-31.232 84.16-41.92 133.472-2.24 16.64-4.48 33.248-6.72 49.888-1.568 19.776-3.168 39.584-4.736 59.36-0.256 6.72-0.48 13.472-0.768 20.224-0.576 18.208-1.152 36.448-1.728 54.624l0 55.616c0.32 17.12 0.672 34.272 1.024 51.36 0.384 11.904 0.832 23.808 1.248 35.68 0.48 8.064 1.024 16.128 1.504 24.192 3.456 21.664 2.624 43.744 6.464 64.64 5.92 31.936 9.824 62.816 18.72 91.296 24.096 77.344 60.384 129.088 124.256 166.624 28.512 16.768 62.624 27.52 98.56 36.672 13.312 2.656 26.592 5.312 39.904 8 16.64 2.24 33.28 4.48 49.888 6.72 21.28 1.664 42.592 3.328 63.872 4.992 7.232 0.256 14.496 0.48 21.696 0.736 15.328 0.416 30.624 0.832 45.92 1.248 7.648 0.096 15.296 0.192 22.944 0.256l31.904 0c20.8-0.32 41.6-0.64 62.4-1.024 5.984-0.256 12-0.48 17.952-0.736 17.792-1.152 35.616-2.336 53.408-3.52 15.232-1.664 30.432-3.328 45.664-4.992 34.208-6.336 66.848-10.912 97.056-20.448 65.536-20.672 114.208-53.728 149.696-104.288 27.328-38.88 41.376-88.288 53.408-142.688 2.592-16.864 5.184-33.76 7.712-50.624 1.856-20.096 3.648-40.256 5.504-60.384 0.32-7.232 0.672-14.464 1.024-21.696 0.64-16.448 1.312-32.928 2.016-49.408l0-13.248c0.096-7.648 0.16-15.296 0.256-22.976l0-54.88c-0.192-7.808-0.352-15.616-0.512-23.424zM841.888 576c-0.352 2.624-0.896 5.024-1.472 7.52-41.504 183.52-222.272 298.016-403.712 255.68-147.008-34.304-249.216-161.024-260.64-305.504-1.536-24.864 16.064-29.92 34.112-20.448 14.144 7.488 110.656 62.72 122.24 70.112s8.8 22.144-3.936 23.456c-12.704 1.312-52.544 4.832-52.544 4.832 31.584 73.568 95.552 131.616 178.304 151.008 138.72 32.64 280.576-55.424 312.32-196.576 0.64-2.88 0.608-2.432 1.76-8.64 1.12-6.24 2.976-15.712 15.2-11.488 12.192 4.256 41.952 12.928 51.264 15.392s8.256 6.112 7.072 14.688zM418.912 528.736c-10.432 0-18.912-8.192-18.912-18.304s8.448-18.304 18.912-18.304l67.84 0 0-20.544-68.288-67.328c-8.512-8.384-8.384-21.888 0.256-30.144s22.592-8.128 31.136 0.256l62.144 61.28 62.144-61.28c8.512-8.384 22.432-8.512 31.136-0.256s8.768 21.76 0.288 30.144l-68.288 67.328 0 20.544 67.84 0c10.432 0 18.88 8.192 18.88 18.304s-8.48 18.304-18.88 18.304l-67.84 0 0 26.432 67.84 0c10.432 0 18.88 8.192 18.88 18.304s-8.48 18.304-18.88 18.304l-67.84 0 0 39.744c0 13.504-11.328 24.48-25.28 24.48s-25.248-10.976-25.248-24.448l0-39.744-67.84 0c-10.432 0-18.912-8.192-18.912-18.304s8.448-18.304 18.912-18.304l67.84 0 0-26.432-67.84 0zM828.768 516.64c-22.624 0.192-133.6-63.52-137.12-66.848-8.416-7.968-5.28-17.92 3.36-19.296s56.864-7.072 56.864-7.072c-29.664-78.912-95.904-141.888-182.816-162.304-138.72-32.608-282.208 59.872-313.888 201.024-0.288 1.184-2.208 15.616-13.696 15.136-7.808-0.32-33.28-10.4-42.464-14.688-10.528-4.896-13.344-3.648-10.176-19.424 36.096-179.424 220.896-300.384 398.24-259.008 147.2 34.336 249.472 161.376 260.672 306.08 1.888 19.36-8.064 26.304-18.976 26.4z" fill="#33C7AE" p-id="36627"></path></svg>    </div><div>            <div style="display: flex;">                <strong style="color:#000;font-size: 16px;">' + obj.type.replace(/刷单/g, '任务') + '</strong>&nbsp;&nbsp;                <span style="' + (obj.amount > 0 ? 'background:#E0FCD4;color:#68b52a;' : 'background:#F4DFDC;color:#D24D4E;') + ' font-weight: bold;border-radius:18px;padding:1px 8px;font-size:12px;display: flex;align-items: center;">' + (obj.amount > 0 ? '+' : '') + obj.amount + ' <img src="/static/images/coin.png" alt="" height="15" width="15" style="margin-left: 5px;"></span>            </div>            <div style="color:#aaa;font-size: 12px;padding:10px 0;">               ' + obj.remark + '            </div>        </div>        <div style="margin-left:auto;text-align:right;">            <span style="' + (obj.state == '0' ? 'background:#e7e7e7;color:#2a2b2c;' : obj.state == '-1' ? 'background:#f1f1f1;color:gray;' : obj.state == '98' ? 'background: #d94949;color: #fbebeb;' : 'background:#5BCA22;color:#fff;') + 'border-radius:18px;padding:2px 8px;font-size:12px;">' + (obj.state == '0' ? '交易中' : obj.state == '-1' ? '已取消' : obj.state == '98' ? '任务失败' : '已完成') + '</span>                        <div style="color:#aaa;font-size:12px;margin-top: 11px;">                ' + obj.create_time + '            </div>        </div>    </div>');
                }

            })
        }

        var type = '<%=Request.QueryString["type"] %>';

        var lastData = function () {
            try {
                if (type != "") {
                    $('.tablist').hide();
                    $('.pptitle').html(type + '记录');
                }
            } catch (e) {

            }
        }
        if (type != "") {
            show_list(type);
            lastData();
        } else {
            show_list('');
        }



        initTabs(function (e) {
            console.log('select', e);
            $('#lists').html('');
            show_list(e);
        })
    </script>
</asp:Content>


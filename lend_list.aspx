<%@ Page Title="" Language="C#" MasterPageFile="~/top.master" AutoEventWireup="true" CodeFile="lend_list.aspx.cs" Inherits="order_list" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="Server">
    <style>
        .info_title {
            color: #000;
            font-size: 14px;
            font-weight: bold;
            display: flex;
            align-items: center;
            margin: 10px 0;
            margin-top: 18px;
        }

            .info_title i {
                display: inline-block;
                background: #2A3EE8;
                width: 6px;
                height: 6px;
                border-radius: 50%;
            }

        .main-container {
            padding: 0px;
        }

        .tt-table {
            font-weight: bold;
            border-collapse: collapse;
        }

            .tt-table th {
                text-align: center;
                color: #fff;
                border: solid 1px #999;
                /* background: #0082f3; */
                font-size: 18px;
                font-weight: 500;
                padding: 5px 0;
                /* color: #000; */
                border: solid 1px #ffd1bf;
                background: #CA5E65;
                color: #fff;
                text-shadow: 2px 3px 2px #9d978a;
            }

            .tt-table td {
                text-align: center;
                border: solid 1px #999;
                font-size: 16px;
                line-height: 22px;
                padding: 5px 0;
                border: solid 1px #FE6225;
                color: #2a2b2c;
                text-shadow: 2px 3px 2px #5a5b5c40;
                background: #fff;
            }
    </style>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="Server">
    <script>
        // [ 基础参数 ]
        var lend_share_quota = '<%=uConfig.stcdata("lend_share_quota") %>';
        var lend_max_quota = '<%=uConfig.stcdata("lend_max_quota") %>';
        
    </script>



    <div>
        <img src="/static/images/lend_banner.jpg" alt="" style="width: 100%;" />
    </div>


    <div style="padding: 18px; font-size: 13px; position: relative;">

        <div style="display: flex; width: 100%;">

            <div style="width: 50%; text-align: center;">
                <a onclick="show_orderList()" style="display: inline-block;">
                    <svg t="1699102000818" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="6521" width="48" height="48">
                        <path d="M0 0.431605h1023.568395v1023.568395H0V0.431605z" fill="#D1290D" opacity=".01" p-id="6522"></path><path d="M750.616823 307.502123a68.237893 68.237893 0 0 0-68.237893 68.237893v443.546305a34.118947 34.118947 0 0 0 34.118947 34.118946h204.713679a34.118947 34.118947 0 0 0 34.118946-34.118946V375.740016a68.237893 68.237893 0 0 0-68.237893-68.237893h-136.475786z m-375.308411 204.713679a68.237893 68.237893 0 0 1 68.237893-68.237893h136.475786a68.237893 68.237893 0 0 1 68.237893 68.237893v307.070519a34.118947 34.118947 0 0 1-34.118947 34.118946h-204.713679a34.118947 34.118947 0 0 1-34.118946-34.118946v-307.070519z m-307.070519 136.475786a68.237893 68.237893 0 0 1 68.237893-68.237893h136.475786a68.237893 68.237893 0 0 1 68.237893 68.237893v170.594733a34.118947 34.118947 0 0 1-34.118946 34.118946H102.35684a34.118947 34.118947 0 0 1-34.118947-34.118946v-170.594733z" fill="#D1290D" p-id="6523"></path><path d="M957.855304 126.22816A68.237893 68.237893 0 1 0 884.567807 11.110835L562.621428 215.995109 498.136619 130.083601a68.237893 68.237893 0 0 0-89.69871-17.571257l-341.189465 204.713679a68.237893 68.237893 0 1 0 70.216791 117.027987l288.407455-173.051297L491.31283 348.444859a68.237893 68.237893 0 0 0 91.234063 16.615927l375.308411-238.832626zM102.35684 887.524214a68.237893 68.237893 0 0 0 0 136.475786h818.854716a68.237893 68.237893 0 1 0 0-136.475786H102.35684z" fill="#D1290D" p-id="6524"></path></svg>
                    <div style="margin-top: 5px; color: #2a2b2c;">
                        <strong>所有挂单记录</strong>
                    </div>
                </a>
            </div>

            <div style="width: 50%; text-align: center;">
                <a onclick="$('#pop-cpt').show();" style="display: inline-block;">
                    <svg t="1699102239152" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="10790" width="48" height="48">
                        <path d="M972.8 563.2c-30.72 0-51.2-20.48-51.2-51.2 0-225.28-184.32-409.6-409.6-409.6s-409.6 184.32-409.6 409.6 184.32 409.6 409.6 409.6c30.72 0 51.2 20.48 51.2 51.2s-20.48 51.2-51.2 51.2C230.4 1024 0 793.6 0 512S230.4 0 512 0s512 230.4 512 512c0 30.72-20.48 51.2-51.2 51.2z" fill="#D1290D" p-id="10791"></path><path d="M972.8 768h-102.4v-102.4c0-30.72-20.48-51.2-51.2-51.2s-51.2 20.48-51.2 51.2v102.4h-102.4c-30.72 0-51.2 20.48-51.2 51.2s20.48 51.2 51.2 51.2h102.4v102.4c0 30.72 20.48 51.2 51.2 51.2s51.2-20.48 51.2-51.2v-102.4h102.4c30.72 0 51.2-20.48 51.2-51.2s-20.48-51.2-51.2-51.2zM665.6 512h-102.4v-25.6h102.4c25.6 0 51.2-25.6 51.2-51.2s-25.6-51.2-51.2-51.2h-51.2L691.2 307.2c20.48-20.48 20.48-51.2 0-71.68s-51.2-20.48-71.68 0L512 343.04 399.36 235.52c-20.48-20.48-51.2-20.48-71.68 0s-20.48 51.2 0 71.68l76.8 76.8H358.4c-30.72 0-51.2 25.6-51.2 51.2s20.48 51.2 51.2 51.2h102.4v25.6H358.4c-30.72 0-51.2 25.6-51.2 51.2s20.48 51.2 51.2 51.2h102.4v81.92c0 25.6 20.48 46.08 46.08 46.08h5.12c25.6 0 46.08-20.48 46.08-46.08V614.4h102.4c25.6 0 51.2-25.6 51.2-51.2s-20.48-51.2-46.08-51.2z" fill="#D1290D" p-id="10792"></path></svg>
                    <div style="margin-top: 5px; color: #2a2b2c;">
                        <strong>获取额度</strong>
                    </div>
                </a>
            </div>

            <div style="width: 50%; text-align: center;">
                <a onclick="show_lendApply();" style="display: inline-block;">
                    <img src="static/images/jk.png" style="width: 48px;">
                    <div style="margin-top: 5px; color: #2a2b2c;">
                        <strong>我要挂单</strong>
                    </div>
                </a>
            </div>

        </div>



        <div style="background: #fff; padding: 25px; border-radius: 5px; margin: 27px 0; border: 2px solid #f2f2f2;">

            <h2 style="margin: 0; margin-bottom: 10px; display: flex;">一键挂单简报
               
                <div style="display: inline-block; font-size: 18px; color: #2a2b2c; margin-left: auto;">
                    <%--【今日剩余挂单次数<span style="color: #C96A54; padding: 0 5px;" id="surplus_number"><%=((Convert.ToInt32(pmlist["base_lend_number"] +"")+Convert.ToInt32(pmlist["share_lend_number"] +"")+Convert.ToInt32(pmlist["daily_lend_add_count"] +""))-Convert.ToInt32(pmlist["daily_lend_number"] +"")) %></span>次】--%>

                    <%--【剩余挂单额度<span style="color: #C96A54; padding: 0 5px;" id="surplus_number"><%=((Convert.ToDouble(pmlist["base_quota"] +"")+Convert.ToDouble(pmlist["daily_lend_add_amount"] +""))-Convert.ToDouble(pmlist["daily_lend_amount"] +"")).ToString("0.00") %></span>】--%>

                    【剩余挂单额度<span style="color: #C96A54; padding: 0 5px;" id="surplus_number"><%=positive_number(((Convert.ToDouble(pmlist["base_quota"] +"")+Convert.ToDouble(uConfig.gnumber(userdt,"lend_quota")))-Convert.ToDouble(pmlist["daily_lend_amount"] +"")).ToString("0.00")) %></span>】
                </div>
            </h2>



            <div style="margin-top: 16px;">

                <div style="display: flex;">

                    <div style="flex-shrink: 0; width: 50%">

                        <h2 style="margin: 0; color: #fe6225;"><span id="total_amount"><%=uConfig.gnumber(userdt,"freeze_lendAmount") %><img src="/static/images/coin.png" alt="" height="15" width="15" style="margin-left: 5px;"></span>
                        </h2>
                        <div style="font-size: 12px;">
                            当前挂单金额
                       
                        </div>

                    </div>

                    <div style="flex-shrink: 0; width: 50%">

                        <h2 style="margin: 0; color: #fe6225;"><span id="Span1"><%=Convert.ToDouble(pmlist["daily_brok_amount"]+"").ToString("0.00") %><img src="/static/images/coin.png" alt="" height="15" width="15" style="margin-left: 5px;"></span>
                        </h2>
                        <div style="font-size: 12px;">
                            今日分红
                       
                        </div>

                    </div>


                </div>


                <div style="display: flex; font-size: 12px; margin-top: 20px; flex-wrap: wrap;">

                    <div style="width: 50%; text-align: left;">

                        <div style="color: #000; font-weight: bold; margin-top: 4px; font-size: 14px;" id="lend_amount"><%=uConfig.gnumber(userdt,"lend_amount") %><img src="/static/images/coin.png" alt="" height="15" width="15" style="margin-left: 5px;"></div>
                        <div style="color: gray;">
                            历史挂单金额
                       
                        </div>
                    </div>

                    <div style="width: 50%; text-align: left;">

                        <div style="color: #000; font-weight: bold; margin-top: 4px; font-size: 14px;" id="lend_reward"><%=uConfig.gnumber(userdt,"lend_reward") %><img src="/static/images/coin.png" alt="" height="15" width="15" style="margin-left: 5px;"></div>
                        <div style="color: gray;">
                            历史分红
                       
                        </div>
                    </div>
                </div>


                <div style="margin-top: 18px; font-size: 16px; color: red; line-height: 28px;">
                    <strong>挂单规则：挂单成功后请联系助理审核挂单项目</strong>
                </div>


            </div>
        </div>


        <div>

            <div style="text-align: center; padding: 6px; background: #336789; color: #fff; color: #eee; font-weight: bold; font-size: 23px;">一键挂单 无需人工值守</div>

            <style>
                .lend_class {
                    display: inline-block;
                    color: #5a5b5c;
                    font-weight: bold;
                    background: #eee;
                    font-size: 15px;
                    padding: 5px 19px;
                    border-radius: 12px;
                    margin: 5px;
                    width: 80px;
                    text-align: center;
                    /*flex-shrink: 0;*/
                    cursor: pointer;
                    width: 100%;
                }

                    .lend_class.active {
                        background: #4d8ccf;
                        color: #fff;
                        box-shadow: 2px 2px 8px #90a9b19c;
                        font-weight: bold;
                    }
            </style>

            <div style="display: none; padding-top: 10px;">
                <%for (int i = 0; i < arrayList.Length; i++)
                  {
                %>
                <span class="lend_class <%=(i == 0 ? " active " : "") %>"><%=arrayList[i] %></span>
                <%
                  } %>
            </div>

            <script>
                $('.lend_class').on('click', function () {
                    $(this).addClass("active").siblings().removeClass("active");
                    get_orderList({ page: 'store_orders', p: 0, limit: 200 });
                })
            </script>



            <div style="padding: 10px 0;" id="store_orders">
            </div>




        </div>










        <style>
            .pop-cpt {
                z-index: 999999999;
                position: relative;
                display: none;
            }

            .pop-cpt2 {
                display: flex;
                justify-content: center;
                align-items: center;
                position: fixed;
                left: 0;
                top: 0;
                right: 0;
                bottom: 0;
                background: rgba(0,0,0,.7);
            }

            .pop-cpt a {
                text-decoration: none;
                color: inherit;
                display: block;
            }

                .pop-cpt a:hover {
                    text-decoration: none;
                }

            .pop-cpt .pop-cpt-bd {
                line-height: 22px;
                width: 380px;
                padding: 18px 18px;
                box-sizing: border-box;
                border-radius: 16px;
                background: #fff;
                background: radial-gradient(117.33% 20.82% at 50% 0,rgb(255 0 0 / 12%) 0,hsla(0,0%,100%,0) 100%),#fff;
                font-size: 14px;
                color: #333;
                font-family: "微软雅黑";
                position: relative;
            }

            .pop-cpt .pop-cpt-close {
                display: inline-block;
                height: 36px;
                line-height: 36px;
                padding: 0 15px;
                color: #fff;
                position: absolute;
                top: 0;
                right: 0;
            }

            .pop-cpt .pop-cpt-tit {
                height: 52px;
                line-height: 52px;
                color: #000;
                text-align: center;
                font-size: 18px;
                font-weight: bold;
                color: #f5ecdc;
                text-shadow: 2px 3px 2px #7d5909;
            }

                .pop-cpt .pop-cpt-tit span {
                    display: inline-block;
                    padding: 0 10px;
                    position: absolute;
                    top: 0;
                    right: 0;
                }

            .pop-cpt .pop-cpt-con {
                padding: 0 6px 3px 6px;
                border-radius: 15px;
                background: none;
            }

            .pop-cpt .pop-cpt-con1 {
                text-align: center;
                color: #000;
            }

            .pop-cpt .pop-cpt-con2 {
                padding-top: 10px;
            }

            .pop-cpt .pop-cpt-con2-tit {
                display: flex;
                justify-content: center;
                padding-bottom: 5px;
            }

                .pop-cpt .pop-cpt-con2-tit span {
                    display: inline-block;
                    width: 26px;
                    line-height: 26px;
                    margin-left: -3px;
                    text-align: center;
                    color: #FFF200;
                    font-size: 16px;
                    font-weight: bold;
                    border-radius: 50%;
                    background: #ED1B20;
                }

            .pop-cpt .pop-cpt-con2-logo {
                display: flex;
                justify-content: center;
                height: 60px;
            }

            .pop-cpt .pop-cpt-con2-logol {
                color: #fb2725;
                font-size: 54px;
                line-height: 50px;
                font-weight: bold;
                font-family: Tahoma;
            }

            .pop-cpt .pop-cpt-con2-logor {
                width: 70px;
                position: relative;
            }

            .pop-cpt .pop-cpt-con2-logor-txt1 {
            }

                .pop-cpt .pop-cpt-con2-logor-txt1 span {
                    display: inline-block;
                    width: 60px;
                    height: 22px;
                    line-height: 22px;
                    text-align: center;
                    color: #fff;
                    border-radius: 5px;
                    background: #0082f3;
                    position: absolute;
                    left: 8px;
                    top: 2px;
                }

            .pop-cpt .pop-cpt-con2-logor-txt2 {
                line-height: 32px;
                color: #333;
                font-size: 30px;
                font-family: impact,arial;
                position: absolute;
                top: 24px;
                left: 0;
            }

            .pop-cpt .pop-cpt-con3 {
                padding: 0 2px;
            }


            .pop-cpt .pop-cpt-con4 {
                text-align: center;
                margin-bottom: 10px;
            }

                .pop-cpt .pop-cpt-con4 span {
                    display: inline-block;
                    width: 200px;
                    height: 30px;
                    line-height: 30px;
                    font-size: 20px;
                    font-weight: bold;
                    color: #ff0;
                    border-radius: 10px;
                    background: #fb2725;
                }

            .pop-cpt .pop-cpt-footer {
                padding: 10px 0;
                color: #fff;
                text-align: center;
            }

            .pop-cpt .pop-cpt-footer1 {
                font-size: 16px;
            }

            .pop-cpt .pop-cpt-footer2 {
                padding-top: 5px;
                font-weight: bold;
                color: #ff0;
            }
        </style>
        <div onclick="$(this).hide()" id="pop-cpt" class="pop-cpt">
            <div class="pop-cpt2">
                <div class="pop-cpt-bd">

                    <div class="UpgradeGuideDialog_corner__E8G4y" style="background: linear-gradient(111deg,#7d37ff,#ff34a8); border-radius: 16px 0 16px 0; color: #fff; font-size: 12px; font-weight: 400; left: 0; line-height: 18px; padding: 4px 23px; position: absolute; top: 0;">
                        获取更多额度
                    </div>

                    <div style="font-size: 30px; font-weight: bold; text-align: center; padding: 20px; padding-bottom: 10px; color: #3a3b3c;">
                        🎉增加额度
                    </div>


                    <div class="pop-cpt-con">
                        <div class="pop-cpt-con2">
                        </div>
                        <div class="pop-cpt-con3" style="margin: 10px 0;">


                            <%--<table width="100%" border="1" class="tt-table">
                                <tbody id="share_list">
                                    <tr class="firstRow">
                                        <th>新增有效下级</th>
                                        <th>获得次数</th>
                                    </tr>
                                </tbody>
                            </table>--%>
                        </div>

                    </div>

                    <script>
                        function convertNumberToChinese(num) {
                            var result = "";
                            if (num >= 10000) {
                                var wan = Math.floor(num / 10000);
                                var qian = Math.floor((num % 10000) / 1000);
                                if (qian > 0) {
                                    result = wan + "万" + qian + "";
                                } else {
                                    result = wan + "万";
                                }
                            } else if (num >= 1000) {
                                var qian = Math.floor(num / 1000);
                                result = qian + "千";
                            } else {
                                result = num.toString();
                            }
                            return result;
                        }


                        //for (var i = 1; i <= 6; i++) {
                        //    $('#share_list').append('<tr>                                        <td>' + i + '个</td>                                        <td>' + (i * lend_share_quota) + '</td>                                    </tr>');
                        //}

                        //$('#share_list').append('<tr>                                        <td colspan="2">以此类推奖励最高' + convertNumberToChinese(lend_max_quota) + '封顶</td>                                    </tr>');
                    </script>





                    <div style="padding-left: 8px;">
                        <div class="a order_page" style="background: linear-gradient(209deg, #ffcc2c, #ff9a2c); border-radius: 0.106667rem; display: inline-block; text-align: center; color: #fff; padding: 0px 19px; font-size: 12px;">获取方式</div>

                        <div style="color: gray; margin-top: 10px; font-size: 12px;">
                            <%--<div>1.【绑定回款方式】当日挂单次数+<%=uConfig.stcdata("lendcount_bind") %></div>
                    <div>2.【下级账号任务金额满5000元】当日挂单次数+<%=uConfig.stcdata("lendcount_sd") %></div>
                    <div>3.【下级挂单次数1次以上】当日挂单次数+<%=uConfig.stcdata("lendcount_xjcj") %></div>--%>
                            <strong style="color: #000; font-size: 18px;">可联系房主管理员申请挂单额度！</strong>
                        </div>
                    </div>
                    <div class="pop-cpt-footer">
                        <a></a><a style="color: #000; border: 1px solid #eee; display: inline-block; padding: 6px 65px; border-radius: 5px; font-weight: bold; background: #E06C31; color: #fff; margin-top: 13px;" onclick="$('#pop-cpt').hide()">返回挂单</a>

                    </div>

                </div>
            </div>
        </div>




        <div id="pop-tip" class="pop-cpt">
            <div class="pop-cpt2">
                <div class="pop-cpt-bd" style="background: radial-gradient(117.33% 20.82% at 50% 0,rgb(0 170 255 / 12%) 0,hsla(0,0%,100%,0) 100%),#fff;">

                    <div style="font-size: 30px; font-weight: bold; text-align: center; padding: 20px; padding-bottom: 10px; color: #3a3b3c;">
                        挂单需知
                    </div>


                    <div class="pop-cpt-con">
                        <div class="pop-cpt-con2">
                        </div>
                        <div class="pop-cpt-con3" style="margin: 10px 0;">
                        </div>

                    </div>

                    <div style="padding-left: 8px;">

                        <div style="color: gray; margin-top: 10px; font-size: 12px;">
                            <%=uConfig.stcdata("lend_apply_text").Replace("\n", "<br>") %>
                        </div>
                    </div>
                    <div class="pop-cpt-footer">
                        <a style="color: #000; border: 1px solid #eee; display: inline-block; padding: 6px 65px; border-radius: 5px; font-weight: bold; background: #E06C31; color: #fff; margin-top: 13px; cursor: pointer;" onclick="lend_apply()">确认申请并同意以上条款</a>
                        <a style="color: #2a2b2c; margin-top: 10px; cursor: pointer;"
                            onclick="$('#pop-tip').hide()">取消申请</a>
                    </div>

                </div>
            </div>
        </div>
    </div>







    <style>
        #orderTop_menu > div {
            width: 50%;
            text-align: center;
            color: gray;
        }

            #orderTop_menu > div.active {
                color: #0268FB;
                font-weight: bold;
            }
    </style>


    <div class=" pg-select-items order_page" style="display: none;">

        <div style="position: fixed; display: flex; top: 0; width: 100%; flex-direction: column; height: 100%; z-index: 9; max-width: 600px; left: 50%; transform: translate(-50%, 0);">

            <div style="background: #fff; box-sizing: border-box; position: relative; height: 100%; display: flex; flex-direction: column;">


                <div style="display: flex; padding: 12px;" id="orderTop_menu">

                    <div class="active" data-page="index">
                        挂单进行中
               
                    </div>
                    <div data-page="list">
                        已还款记录
               
                    </div>

                </div>



                <%--<div style="background: #f3f5f7; color: #493325; font-size: 15px; padding: 10px; font-weight: bold; margin-bottom: 3px; text-align: center;">
                    今日剩余挂单次数<span style="color: #C96A54; padding: 0 5px;" id="Span2"><%=((Convert.ToInt32(pmlist["base_lend_number"] +"")+Convert.ToInt32(pmlist["share_lend_number"] +"")+Convert.ToInt32(pmlist["daily_lend_add_count"] +""))-Convert.ToInt32(pmlist["daily_lend_number"] +"")) %></span>次&nbsp;&nbsp;已挂单：<span id="u_current_freeze_amount"><%=uConfig.gnumber(userdt,"freeze_lendAmount") %></span>
                </div>--%>

                <div style="background: #f3f5f7; color: #493325; font-size: 15px; padding: 10px; font-weight: bold; margin-bottom: 3px; text-align: center;">
                    剩余挂单额度<span style="color: #C96A54; padding: 0 5px;" id="Span2"><%=positive_number(((Convert.ToDouble(pmlist["base_quota"] +"")+Convert.ToDouble(uConfig.gnumber(userdt,"lend_quota")))-Convert.ToDouble(pmlist["daily_lend_amount"] +"")).ToString("0.00")) %></span>&nbsp;&nbsp;已挂单：<span id="u_current_freeze_amount"><%=uConfig.gnumber(userdt,"freeze_lendAmount") %>

                    </span>
                </div>

                <div class="orderlist_page" style="margin-bottom: 10px; display: none;">
                </div>


                <div class="orderlist_page" style="color: rgb(202, 211, 199); font-size: 12px; text-align: left; border-radius: 10px; margin: 2px 8px; overflow-y: auto; box-sizing: border-box; flex-grow: 1; display: none;">



                    <style>
                        .order_table {
                            width: 100%;
                            text-align: center;
                            font-size: 14px;
                        }

                            .order_table thead {
                                background: #f1f1f1;
                            }

                            .order_table th {
                                color: #5a5b5c;
                                padding: 5px 0;
                                background: #f1f1f1;
                            }

                            .order_table td {
                                padding: 10px 0;
                                width: 20%;
                                white-space: nowrap;
                                overflow: hidden;
                                text-overflow: ellipsis;
                                max-width: 100px;
                                text-align: center;
                            }
                    </style>



                    <div class="user_orderList" style="border-radius: 8px; text-align: center; color: rgb(154, 155, 152); font-size: 14px;">



                        <div style="background: rgb(241, 241, 241); color: rgb(0, 0, 0); font-size: 12px; text-align: left; border-radius: 10px; overflow-y: auto; box-sizing: border-box; flex-grow: 1; display: block; padding: 0 10px;">



                            <table id="order_history" class="order_table">
                                <thead>
                                    <tr>
                                        <th>商家名称</th>
                                        <th>金额</th>
                                        <th>佣金</th>
                                        <th>总额</th>
                                        <th>状态</th>
                                    </tr>
                                </thead>
                                <tbody>
                                </tbody>
                            </table>
                        </div>




                    </div>


                </div>




                <div style="flex-shrink: 0; display: flex; flex-direction: column; justify-content: center; line-height: 21px; color: blue; font-weight: bold; font-size: 14px; padding: 12px; margin: 10px; background: #eee; border-radius: 8px; margin-bottom: 0px;" id="list_tips">



                    <div>
                        <svg t="1697555172443" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="1624" width="12" height="12" style="margin-right: 5px;">
                            <path d="M560 800l-10.464-416h-75.072L464 800h96z m-14.144-493.984c9.44-9.312 14.144-20.672 14.144-34.016 0-13.6-4.704-24.992-14.144-34.208A46.784 46.784 0 0 0 512 224c-13.12 0-24.448 4.608-33.856 13.792A45.856 45.856 0 0 0 464 272c0 13.344 4.704 24.704 14.144 34.016 9.408 9.312 20.704 13.984 33.856 13.984 13.12 0 24.448-4.672 33.856-13.984zM512 32C246.912 32 32 246.912 32 512c0 265.088 214.912 480 480 480 265.088 0 480-214.912 480-480 0-265.088-214.912-480-480-480z m0 64c229.76 0 416 186.24 416 416s-186.24 416-416 416S96 741.76 96 512 282.24 96 512 96z" fill="blue" p-id="1625"></path></svg>当前进行中的订单【<span id="task_order_number">0</span>单】
                    </div>


                    <div>
                        <svg t="1697555172443" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="1624" width="12" height="12" style="margin-right: 5px;">
                            <path d="M560 800l-10.464-416h-75.072L464 800h96z m-14.144-493.984c9.44-9.312 14.144-20.672 14.144-34.016 0-13.6-4.704-24.992-14.144-34.208A46.784 46.784 0 0 0 512 224c-13.12 0-24.448 4.608-33.856 13.792A45.856 45.856 0 0 0 464 272c0 13.344 4.704 24.704 14.144 34.016 9.408 9.312 20.704 13.984 33.856 13.984 13.12 0 24.448-4.672 33.856-13.984zM512 32C246.912 32 32 246.912 32 512c0 265.088 214.912 480 480 480 265.088 0 480-214.912 480-480 0-265.088-214.912-480-480-480z m0 64c229.76 0 416 186.24 416 416s-186.24 416-416 416S96 741.76 96 512 282.24 96 512 96z" fill="blue" p-id="1625"></path></svg>还款方式：今日挂单，次日完成收益，返还至用户余额！
                    </div>


                    <div>
                        <svg t="1697555172443" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="1624" width="12" height="12" style="margin-right: 5px;">
                            <path d="M560 800l-10.464-416h-75.072L464 800h96z m-14.144-493.984c9.44-9.312 14.144-20.672 14.144-34.016 0-13.6-4.704-24.992-14.144-34.208A46.784 46.784 0 0 0 512 224c-13.12 0-24.448 4.608-33.856 13.792A45.856 45.856 0 0 0 464 272c0 13.344 4.704 24.704 14.144 34.016 9.408 9.312 20.704 13.984 33.856 13.984 13.12 0 24.448-4.672 33.856-13.984zM512 32C246.912 32 32 246.912 32 512c0 265.088 214.912 480 480 480 265.088 0 480-214.912 480-480 0-265.088-214.912-480-480-480z m0 64c229.76 0 416 186.24 416 416s-186.24 416-416 416S96 741.76 96 512 282.24 96 512 96z" fill="blue" p-id="1625"></path></svg>资金由淘返客全程担保，商家未及时还款，将由淘返客承担损失！
                    </div>

                    <div style="color: red;">
                        <svg t="1697555172443" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="1624" width="12" height="12" style="margin-right: 5px;">
                            <path d="M560 800l-10.464-416h-75.072L464 800h96z m-14.144-493.984c9.44-9.312 14.144-20.672 14.144-34.016 0-13.6-4.704-24.992-14.144-34.208A46.784 46.784 0 0 0 512 224c-13.12 0-24.448 4.608-33.856 13.792A45.856 45.856 0 0 0 464 272c0 13.344 4.704 24.704 14.144 34.016 9.408 9.312 20.704 13.984 33.856 13.984 13.12 0 24.448-4.672 33.856-13.984zM512 32C246.912 32 32 246.912 32 512c0 265.088 214.912 480 480 480 265.088 0 480-214.912 480-480 0-265.088-214.912-480-480-480z m0 64c229.76 0 416 186.24 416 416s-186.24 416-416 416S96 741.76 96 512 282.24 96 512 96z" fill="red" p-id="1625"></path></svg>每日佣金结算时间为：00:00 至 00:30分【佣金返还至余额】
                    </div>

                    <div>
                        <%--<svg t="1697555172443" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="1624" width="12" height="12" style="margin-right: 5px;">
                                <path d="M560 800l-10.464-416h-75.072L464 800h96z m-14.144-493.984c9.44-9.312 14.144-20.672 14.144-34.016 0-13.6-4.704-24.992-14.144-34.208A46.784 46.784 0 0 0 512 224c-13.12 0-24.448 4.608-33.856 13.792A45.856 45.856 0 0 0 464 272c0 13.344 4.704 24.704 14.144 34.016 9.408 9.312 20.704 13.984 33.856 13.984 13.12 0 24.448-4.672 33.856-13.984zM512 32C246.912 32 32 246.912 32 512c0 265.088 214.912 480 480 480 265.088 0 480-214.912 480-480 0-265.088-214.912-480-480-480z m0 64c229.76 0 416 186.24 416 416s-186.24 416-416 416S96 741.76 96 512 282.24 96 512 96z" fill="blue" p-id="1625"></path></svg>--%>
                        <span style="background: linear-gradient(45deg, #332e2e, #5f7993); color: #f1f1cb; padding: 1px 10px; border-radius: 3px; font-size: 12px;">无风险挂单</span>
                        <span style="background: linear-gradient(45deg, #332e2e, #5f7993); color: #f1f1cb; padding: 1px 10px; border-radius: 3px; font-size: 12px;">保本保收益</span>
                        <span style="background: linear-gradient(45deg, #332e2e, #5f7993); color: #f1f1cb; padding: 1px 10px; border-radius: 3px; font-size: 12px;">资金安全有保障</span>
                    </div>


                </div>


                <div class="getOrder_page" style="background: rgb(241, 241, 241); color: rgb(0, 0, 0); font-size: 12px; text-align: left; border-radius: 10px; margin: 2px 8px; overflow-y: auto; box-sizing: border-box; flex-grow: 1; display: block; padding: 0 10px;">



                    <table id="order_list" class="order_table">
                        <thead>
                            <tr>
                                <th>商家名称</th>
                                <th>金额</th>
                                <th>佣金</th>
                                <th>倒计时</th>
                                <th>状态</th>
                            </tr>
                        </thead>
                        <tbody>
                        </tbody>
                    </table>



                </div>

                <div style="height: 72px; flex-shrink: 0;"></div>


            </div>






        </div>


        <script>

            $('#orderTop_menu>div').on('click', function () {
                $(this).addClass("active").siblings().removeClass("active");
                var page = $(this).data("page");
                switch (page) {
                    case "index":
                        show_orderList();
                        $('.orderlist_page').hide();
                        $('.getOrder_page').show();
                        $('#list_tips').show();
                        break;
                    case "list":
                        get_orderHistory();
                        $('.getOrder_page').hide();
                        $('.orderlist_page').show();
                        $('#list_tips').hide();
                        break;
                    default:
                        break;

                }
                console.log('page', page);
            })
        </script>
    </div>



    <style>
        .apply_input {
            width: 100%;
            background: #F4F4F4;
            border: 0;
            border-bottom: 2px solid #ddd;
            padding: 12px 15px;
            box-sizing: border-box;
            outline: none;
            font-size: 16px;
        }

            .apply_input:hover {
                border-bottom: 2px solid #5a5b5c;
            }

        .select_items {
            display: inline-block;
            line-height: 30px;
            color: hsl(0deg 4.23% 36.4%);
            padding: 0.05em 0.6em;
            border-radius: 3px;
            white-space: nowrap;
            cursor: pointer;
            font-size: 14px;
            background: #4d4d4d0a;
            margin-right: 10px;
            border: 1px solid #eee;
        }

            .select_items.active {
                background-color: #c9f3ff;
                border: 1px solid #252424;
                text-shadow: 5px 5px 5px #7d550914;
                font-weight: bold;
            }
    </style>
    <div class=" pg-lend-apply order_page" style="display: none;">
        <div style="position: fixed; display: flex; top: 0; width: 100%; flex-direction: column; height: 100%; z-index: 9; max-width: 600px; left: 50%; transform: translate(-50%, 0);">
            <div style="background: #fff; box-sizing: border-box; position: relative; height: 100%; display: flex; flex-direction: column;">


                <div style="padding: 18px; overflow-y: auto; padding-bottom: 69px;">

                    <div style="color: #72aefd; font-size: 20px; display: flex; align-items: center; margin-bottom: 20px;">
                        <svg t="1702191462761" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="15593" width="38" height="38" style="margin-right: 10px;">
                            <path d="M681.7792 924.9792h22.8864c66.56 0 132.096-75.1616 132.096-141.7216v-33.8944l-154.9824 175.616zM657.5104 91.9552H321.28c-99.0208 0-179.3024 80.2816-179.3024 179.3024v474.4192c0 99.0208 80.2816 179.3024 179.3024 179.3024h120.0128V781.312c0-9.8304 3.7376-19.3024 10.4448-26.5216l314.112-335.6672c15.0528-16.0768 40.3968-16.4352 55.8592-0.8192l15.0016 15.1552V271.2576c0.1024-99.0208-80.1792-179.3024-179.2-179.3024zM462.08 640.5632H327.0656c-17.664 0-32-14.336-32-32s14.336-32 32-32h135.0656c17.664 0 32 14.336 32 32-0.0512 17.664-14.3872 32-32.0512 32z m97.7408-166.4512H327.0656c-17.664 0-32-14.336-32-32s14.336-32 32-32h232.8064c17.664 0 32 14.336 32 32s-14.3872 32-32.0512 32z m91.8528-168.8064h-324.608c-17.664 0-32-14.336-32-32s14.336-32 32-32h324.6592c17.664 0 32 14.336 32 32s-14.336 32-32.0512 32z" fill="#5097FF" p-id="15594"></path><path d="M707.6864 562.6368l-202.5984 219.4432c-3.84 4.1472-5.9392 9.6256-5.8368 15.3088l1.9968 110.08a12.53376 12.53376 0 0 0 14.5408 12.1344l99.2768-16.0768c4.864-0.768 9.3184-3.1744 12.6464-6.8096l205.4144-222.464-125.44-111.616zM898.9696 570.368L807.8336 486.4a21.95968 21.95968 0 0 0-31.0784 1.28l-29.0816 31.5904 125.4912 111.616 27.136-29.44a22.10304 22.10304 0 0 0-1.3312-31.0784z" fill="#5097FF" p-id="15595"></path></svg>
                        申请资料填写
                   
                    </div>

                    <div style="margin-bottom: 18px;">
                        <div style="margin-bottom: 10px; font-size: 13px; color: #3a3b3c;">
                            店铺名称
                        </div>
                        <div>
                            <input value="" class="apply_input" id="shop_name" />
                        </div>
                    </div>

                    <div style="margin-bottom: 18px;" id="purpose">
                        <div style="margin-bottom: 10px; font-size: 13px; color: #3a3b3c;">
                            挂单用途
                        </div>
                        <div>
                            <div style="display: flex;">
                                <div class="select_items active" v="huabei">借呗/花呗还款</div>
                                <div class="select_items" v="hkzz">店铺货款周转</div>
                            </div>
                        </div>
                    </div>

                    <div style="margin-bottom: 18px;" id="shop_type">
                        <div style="margin-bottom: 10px; font-size: 13px; color: #3a3b3c;">
                            店铺类型
                        </div>
                        <div>
                            <div style="display: flex;">
                                <div class="select_items active" v="user">个人店铺</div>
                                <div class="select_items" v="comp">企业店铺</div>
                            </div>
                        </div>
                    </div>

                    <div style="margin-bottom: 18px;">
                        <div style="margin-bottom: 10px; font-size: 13px; color: #3a3b3c;">
                            店铺持有人身份证
                        </div>


                        <div>
                            <strong style="font-size: 13px; margin-bottom: 5px; display: inline-block; color: #3a3b3c;">身份证国徽面</strong>
                            <div style="display: flex;">


                                <div id="idcard1" style="background: #eee; min-height: 150px; cursor: pointer; display: flex; align-items: center; justify-content: center; color: #aaa; font-size: 17px; max-width: 150px; width: 100%;" onclick="upload_images('idcard1')">

                                    <svg t="1694321742065" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="7508" width="32" height="32">
                                        <path d="M896 160H128v704h768z m-704 64h640v423.68L706.08 496l-157.12 157.12L352 358.24l-160 240z m0 576v-86.24l160-240 187.04 280.48L701.92 592 832 747.52V800z" fill="#aaaaaa" p-id="7509"></path><path d="M672 448a96 96 0 1 0-96-96 96 96 0 0 0 96 96z m0-128a32 32 0 1 1-32 32 32 32 0 0 1 32-32z" fill="#aaaaaa" p-id="7510"></path></svg>
                                    <b style="margin-left: 5px;">上传截图</b>
                                </div>

                            </div>


                            <strong style="font-size: 13px; margin-bottom: 5px; display: inline-block; color: #3a3b3c; margin-top: 18px;">身份证人像面</strong>
                            <div style="display: flex;">


                                <div id="idcard2" style="background: #eee; min-height: 150px; cursor: pointer; display: flex; align-items: center; justify-content: center; color: #aaa; font-size: 17px; max-width: 150px; width: 100%;" onclick="upload_images('idcard2')">

                                    <svg t="1694321742065" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="7508" width="32" height="32">
                                        <path d="M896 160H128v704h768z m-704 64h640v423.68L706.08 496l-157.12 157.12L352 358.24l-160 240z m0 576v-86.24l160-240 187.04 280.48L701.92 592 832 747.52V800z" fill="#aaaaaa" p-id="7509"></path><path d="M672 448a96 96 0 1 0-96-96 96 96 0 0 0 96 96z m0-128a32 32 0 1 1-32 32 32 32 0 0 1 32-32z" fill="#aaaaaa" p-id="7510"></path></svg>
                                    <b style="margin-left: 5px;">上传截图</b>
                                </div>

                            </div>
                        </div>


                    </div>


                    <div style="margin-bottom: 18px;">
                        <div style="margin-bottom: 10px; font-size: 13px; color: #3a3b3c;">
                            店铺企业营业执照
                        </div>


                        <div>
                            <div style="display: flex;">
                                <div id="shop_cert_img" style="background: #eee; min-height: 150px; cursor: pointer; display: flex; align-items: center; justify-content: center; color: #aaa; font-size: 17px; max-width: 150px; width: 100%;" onclick="upload_images('shop_cert_img')">

                                    <svg t="1694321742065" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="7508" width="32" height="32">
                                        <path d="M896 160H128v704h768z m-704 64h640v423.68L706.08 496l-157.12 157.12L352 358.24l-160 240z m0 576v-86.24l160-240 187.04 280.48L701.92 592 832 747.52V800z" fill="#aaaaaa" p-id="7509"></path><path d="M672 448a96 96 0 1 0-96-96 96 96 0 0 0 96 96z m0-128a32 32 0 1 1-32 32 32 32 0 0 1 32-32z" fill="#aaaaaa" p-id="7510"></path></svg>
                                    <b style="margin-left: 5px;">上传截图</b>
                                </div>

                            </div>
                        </div>

                    </div>


                    <div style="margin-bottom: 18px;">
                        <div style="margin-bottom: 10px; font-size: 13px; color: #3a3b3c;">
                            仓库厂房照
                        </div>


                        <div>
                            <div style="display: flex;">
                                <div id="shop_factory_img" style="background: #eee; min-height: 150px; cursor: pointer; display: flex; align-items: center; justify-content: center; color: #aaa; font-size: 17px; max-width: 150px; width: 100%;" onclick="upload_images('shop_factory_img')">

                                    <svg t="1694321742065" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="7508" width="32" height="32">
                                        <path d="M896 160H128v704h768z m-704 64h640v423.68L706.08 496l-157.12 157.12L352 358.24l-160 240z m0 576v-86.24l160-240 187.04 280.48L701.92 592 832 747.52V800z" fill="#aaaaaa" p-id="7509"></path><path d="M672 448a96 96 0 1 0-96-96 96 96 0 0 0 96 96z m0-128a32 32 0 1 1-32 32 32 32 0 0 1 32-32z" fill="#aaaaaa" p-id="7510"></path></svg>
                                    <b style="margin-left: 5px;">上传截图</b>
                                </div>

                            </div>
                        </div>

                    </div>


                    <div style="margin-bottom: 18px;">
                        <div style="margin-bottom: 10px; font-size: 13px; color: #3a3b3c;">
                            店铺营业销量照
                        </div>


                        <div>
                            <div style="display: flex;">
                                <div id="shop_sale_img" style="background: #eee; min-height: 150px; cursor: pointer; display: flex; align-items: center; justify-content: center; color: #aaa; font-size: 17px; max-width: 150px; width: 100%;" onclick="upload_images('shop_sale_img')">

                                    <svg t="1694321742065" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="7508" width="32" height="32">
                                        <path d="M896 160H128v704h768z m-704 64h640v423.68L706.08 496l-157.12 157.12L352 358.24l-160 240z m0 576v-86.24l160-240 187.04 280.48L701.92 592 832 747.52V800z" fill="#aaaaaa" p-id="7509"></path><path d="M672 448a96 96 0 1 0-96-96 96 96 0 0 0 96 96z m0-128a32 32 0 1 1-32 32 32 32 0 0 1 32-32z" fill="#aaaaaa" p-id="7510"></path></svg>
                                    <b style="margin-left: 5px;">上传截图</b>
                                </div>

                            </div>
                        </div>

                    </div>



                    <div style="margin-bottom: 18px;">
                        <div style="margin-bottom: 10px; font-size: 13px; color: #3a3b3c;">
                            申请挂单金额
                        </div>
                        <div>
                            <input value="" class="apply_input" id="lend_apply_amount" />
                        </div>
                    </div>


                    <div style="margin-bottom: 18px;" id="lend_days">
                        <div style="margin-bottom: 10px; font-size: 13px; color: #3a3b3c;">
                            周转期限
                        </div>
                        <div>
                            <div style="display: flex;">

                                <%for (int i = 0; i < applyDaysList.Length; i++)
                                  {
                                %>
                                <div class="select_items <%=(i==0?"active":"") %>" v="<%=applyDaysList[i] %>"><%=applyDaysList[i] %></div>
                                <%
                                  } %>
                            </div>
                        </div>
                    </div>

                    <div style="margin-bottom: 18px; font-size: 12px; color: #d75656; line-height: 22px;">
                        温馨提示：为保证您的挂单完成进度，淘返客将使用众筹方式拆分挂单金额，便于用户挂单给您！挂单额度返还至您的会员余额！
                   
                    </div>

                    <div style="margin-bottom: 18px; font-size: 12px;">
                        <a style="display: inline-block; line-height: 30px; padding: 6px 5px; border-radius: 3px; white-space: nowrap; cursor: pointer; font-size: 16px; background: #4d4d4d0a; margin-right: 10px; font-weight: bold; color: #ffffff; background-color: #c97727; width: 100%; box-sizing: border-box; text-align: center;" onclick="$('#pop-tip').show();">提交申请</a>
                    </div>

                </div>


            </div>
        </div>
    </div>
    <script>

        var upload_images = function (id) {
            file_upload(function (e) {
                if (e.success) {
                    $('#' + id).html('<img style="width:100%;max-height:100%;" src="' + e.imgurl + '">')
                }

            })
        }

        $('.select_items').on('click', function () {
            var stype = $(this).closest('.select-boxs').attr('stype');
            if (stype == "multi") {
                $(this).toggleClass("active");
            } else {
                $(this).addClass("active").siblings().removeClass("active");
            }
        })


        var lend_apply = function () {
            $('#pop-tip').hide();
            var amount = $('#lend_apply_amount').val();
            var days = $('#lend_days .select_items.active').attr('v');
            if (amount == '') {
                tp('请填写挂单金额');
                return;
            }
            security_password(function (e) {

                v3api("lend_apply", {
                    data: {
                        paypwd: e.password,
                        shop_name: $('#shop_name').val(),
                        purpose: $('#purpose .select_items.active').attr('v'),
                        shop_type: $('#shop_type .select_items.active').attr('v'),
                        idcard1: $('#idcard1 img').attr('src'),
                        idcard2: $('#idcard2 img').attr('src'),
                        shop_cert_img: $('#shop_cert_img img').attr('src'),
                        shop_factory_img: $('#shop_factory_img img').attr('src'),
                        shop_sale_img: $('#shop_sale_img img').attr('src'),
                        amount: amount,
                        lend_days: $('#lend_days .select_items.active').attr('v')
                    }
                }, function (e) {
                    tp(e.msg);
                    var ls = $('[listid=' + id + ']');
                    ls.remove();


                    $('#surplus_number').html(e.surplus_number);

                    //update_userAmount(e);
                })



            }, { error_tip_top: '<div style="color: #2a2b2c;padding: 0 30px;margin-top: 20px;font-weight: bold;text-align:center;font-size: 16px;color:#d90000;" class="animated-text"><div style="    margin-bottom: 10px;">您确定挂单了吗？</div><div style=" margin-bottom: 5px; "><span style="color:red;">本次挂单金额为 ' + amount + '<img src="/static/images/coin.png" alt="" height="15" width="15" style="margin-left: 5px;"></span></div>  <div><span style="color: #2a2b2c;">本次周转期限为：' + days + '</span></div>  </div>' })

        }

    </script>


    <script>


        // 获取项目列表


        var get_orderList = function (data) {
            data.lend_class = $('.lend_class.active').text();
            v3api("lists", {
                error: 1,
                data: data
            }, function (e) {
                $('#store_orders').html('');
                if (e.code == 1) {
                    for (var i = 0; i < e.data.list.length; i++) {
                        var obj = e.data.list[i];
                        $('#store_orders').append('<div style="border: 1px solid #eee; padding: 8px; padding-bottom: 18px;">                    <div style="display: flex; padding-bottom: 8px; margin-bottom: 8px; border-bottom: 1px solid #eee;">                        <img src="static/images/yjgd.png" style="height:28px;">                       <div style="color: #2a2b2c; font-weight: bold; margin-left: 5px; font-size: 19px;">                            ' + obj.name + '                                               </div>                    </div>                    <div style="display: flex;">                        <div style="font-weight: bold; flex-shrink: 0;">                            <div style="display: flex; margin: 13px 0;">                                挂单金额：<span style="color: #DD1E39">' + obj.amount + '</span><img src="/static/images/coin.png" alt="" height="15" width="15" style="margin-left: 5px;">                            </div>                            <div style="display: flex; margin: 13px 0;">                                挂单期限：<span style="color: #DD1E39">' + obj.days + '</span>&nbsp;日                                                       </div>                            <div style="display: flex; margin: 13px 0;">                                每日挂单佣金：<span style="color: #DD1E39">' + obj.interest + '%</span>                            </div>             <div style="display: flex; margin: 13px 0;">                                每日获得佣金：<span style="color: #DD1E39">' + (parseFloat(obj.amount) / 100 * parseFloat(obj.interest)).toFixed(2) + '<img src="/static/images/coin.png" alt="" height="15" width="15" style="margin-left: 5px;"></span>                            </div>                   <div style="display: flex; margin: 13px 0; align-items: center;">                                预计还款总额：<span style="color: #2a2b2c; font-size: 17px; text-shadow: 5px 5px 5px #aeaedf52;">' + (parseFloat(obj.amount) + (parseFloat(obj.amount) / 100 * parseFloat(obj.interest)) * obj.days).toFixed(2) + '</span><img src="/static/images/coin.png" alt="" height="15" width="15" style="margin-left: 5px;">                            </div>                        </div>                        <div style="width: 100%; display: flex; align-items: center; justify-content: end;">                            <div>                                <a style="background: red; color: #fff; border-radius: 12px; padding: 12px 35px; display: inline-block; font-size: 18px; font-weight: bold;cursor:pointer;" onclick="storeOrder_confirm(\'' + obj.orderNo + '\',' + obj.amount + ',' + obj.days + ')">我要挂单</a>                            </div>                        </div>                    </div>                    <div style="color: gray;">                        还款方式：今日挂单，次日完成收益，返还至用户余额！                    </div>                    <div style="color: #cf1d1d; font-weight: bold; margin-top: 6px;">                        温馨提示：资金由淘返客全程担保，感谢您的支持与信赖！                    </div>                </div>');
                    }
                } else {
                    tp(e.msg);

                }



            })
        }

        get_orderList({ page: 'store_orders', p: 0, limit: 200 });

        checknew_timer = setTimeout(function () {
            get_orderList({ page: 'store_orders', p: 0, limit: 200 });
        }, 5000);

        //挂单

        var storeOrder_confirm = function (id, amount, days) {

            security_password(function (e) {


                v3api("storeOrder_confirm", {
                    data: {
                        paypwd: e.password,
                        orderNo: id
                    }
                }, function (e) {
                    tp(e.msg);
                    var ls = $('[listid=' + id + ']');
                    ls.remove();


                    $('#surplus_number').html(e.surplus_number);

                    //update_userAmount(e);
                })



            }, { error_tip_top: '<div style="color: #2a2b2c;padding: 0 30px;margin-top: 20px;font-weight: bold;text-align:center;font-size: 16px;color:#d90000;" class="animated-text"><div style="    margin-bottom: 10px;">您确定挂单了吗？</div><div style=" margin-bottom: 5px; "><span style="color:red;">本次挂单金额为 ' + amount + '<img src="/static/images/coin.png" alt="" height="15" width="15" style="margin-left: 5px;"></span></div>  <div><span style="color: #2a2b2c;">本次挂单期限为：' + days + '日</span></div>  </div>' })

        }



        //获取挂单订单

        var get_orderHistory = function () {
            v3api("lists", {
                error: 1,
                data: { page: 'store_orders', p: 0, limit: 200, state: 1 }
            }, function (e) {
                if (e.code == 1) {
                    $('#order_history tbody').html('');

                    for (var i = 0; i < e.data.list.length; i++) {
                        var obj = e.data.list[i];
                        $('#order_history tbody').append('<tr>                                <td>' + obj.name + '</td>                                <td>' + parseFloat(obj.amount).toFixed(2) + '</td>                                <td>' + (parseFloat(obj.amount) / 100 * parseFloat(obj.interest)).toFixed(2) + '</td>                                <td>' + parseFloat(obj.amount + (parseFloat(obj.amount) / 100 * parseFloat(obj.interest))).toFixed(2) + '</td>                                <td>                                    <a style="background: #C8A681; color: #eee; width: 60px; display: inline-block; padding: 4px; border-radius: 30px;">已还款</a>                                </td>                            </tr>');
                    }


                    $('#task_order_number').html(e.data.list.length);

                    show_expireList();
                } else {
                    tp(e.msg);
                }


            })
        }

        var show_lendApply = function () {
            $('.pg-lend-apply').show();
            $('body').css({ "overflow": "hidden" });

        }


        var show_orderList = function () {
            $('.pg-select-items').show();
            $('body').css({ "overflow": "hidden" });

            v3api("lists", {
                error: 1,
                data: { page: 'store_orders', p: 0, limit: 200, state: 1000 }
            }, function (e) {
                if (e.code == 1) {
                    $('#order_list tbody').html('');

                    for (var i = 0; i < e.data.list.length; i++) {
                        var obj = e.data.list[i];
                        $('#order_list tbody').append('<tr>                                <td>' + obj.name + '</td>                                <td>' + parseFloat(obj.amount).toFixed(2) + '</td>                                <td>' + ((parseFloat(obj.amount) / 100 * parseFloat(obj.interest)) * obj.days).toFixed(2) + '<div><span style="        font-size: 12px;        color: #ef1b1b;        ">+' + (obj.bonus_amount == "" ? "0" : obj.bonus_amount) + '</span>        </div>' + '</td>                                <td><span style="background: #fff;color: rgb(105 128 205);padding: 2px 5px;border-radius: 3px;font-weight: bold;font-size: 12px;" ' + (obj.state == 99 ? '' : ' expire_time="' + obj.expire_time + '"') + '>--</span></td>                                <td>                                    <a style="background: #C8A681; color: #eee; width: 78px; display: inline-block; padding: 4px; border-radius: 30px;">' + (obj.state == 99 ? '等待审核' : (obj.isfreeze == 1 ? '请联系团长' : '等待还款')) + '</a>                                </td>                            </tr>');
                    }


                    $('#task_order_number').html(e.data.list.length);

                    show_expireList();
                } else {
                    tp(e.msg);
                }


            })
        }

        var show_expireList = function () {
            // 获取所有的.expire_time标签
            $('[expire_time]').each(function () {
                var time = new Date($(this).attr('expire_time')).getTime();

                // 创建并附加倒计时元素
                var countdownElement = $(this);

                // 更新倒计时
                function updateCountdown() {
                    var currentDate = new Date().getTime();
                    var timeLeft = time - currentDate;

                    if (timeLeft <= 0) {
                        countdownElement.text('<%=uConfig.gd(userdt, "lend_audit") == "1" ? "请联系客服为您结束项目" : "等待结算佣金" %>').attr("title", '<%=uConfig.gd(userdt, "lend_audit") == "1" ? "请联系客服为您结束项目" : "等待结算佣金" %>');
                    } else {
                        var days = Math.floor(timeLeft / (1000 * 60 * 60 * 24));
                        var hours = Math.floor((timeLeft % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
                        var minutes = Math.floor((timeLeft % (1000 * 60 * 60) / (1000 * 60)));
                        var seconds = Math.floor((timeLeft % (1000 * 60) / 1000));

                        if (isNaN(days)) {
                            days = 0;
                        }
                        if (isNaN(hours)) {
                            hours = 0;
                        }
                        if (isNaN(minutes)) {
                            minutes = 0;
                        }
                        if (isNaN(seconds)) {
                            seconds = 0;
                        }

                        if (days > 0) {
                            countdownElement.text(
                              days + '天 ' +
                              hours.toString().padStart(2, '0') + ':' +
                              minutes.toString().padStart(2, '0') + ':' +
                              seconds.toString().padStart(2, '0') + ''
                            );
                        } else {
                            countdownElement.text(
                              hours.toString().padStart(2, '0') + ':' +
                              minutes.toString().padStart(2, '0') + ':' +
                              seconds.toString().padStart(2, '0') + ''
                            );
                        }
                    }
                }

                updateCountdown();

                // 每秒更新一次倒计时
                setInterval(updateCountdown, 1000);
            });
        }



    </script>

</asp:Content>


.header {

font-weight: bold !important;
color: #ffffff;
background: #039e6d;
height: 30px;
padding-left: 10px;
}
.header td {
padding-left: 10px;
}
.header a {
color: #ffffff;font-weight: bold;
}
.header input {
background:none;
vertical-align: middle;
height: 16px;
}
.subtable {
text-align: left;
padding: 0;
margin: 0 auto;
}
.left {
float: left;
text-align: left;
padding: 0px;
}
.right {
float: right;
text-align: right;
padding: 0px;
}
.spaceborder {
width: 99.8%;
border: 1px solid #039e6d;
padding: 0px;margin-bottom: 5px;
}
.spacebottom {
padding-bottom: 10px;
background-color: #FDFFF2;
}
.category {
color: #92A05A;
background-color: #FDFFF2;
}
.category td {
border-bottom: 1px solid #DBDDD3;
}
.row {
width: 100%;
border: none;
background: #FFFFFF;
empty-cells: show;
}
.row td {
padding: 4px;
border-bottom: 1px solid #BBE9FF;
}
.row1 {
width: 100%;
border: none;
background: #F5FBFF;
empty-cells: show;
}
.row1 td {
padding: 4px;
border-bottom: 1px solid #BBE9FF;
}
.bold {
font-weight: bold;
}
.altbg1	{
background: #F5FBFF;
}
td.altbg1 {
border-bottom: 1px solid #BBE9FF;
}
.altbg2 {
height: 35px;
}
td.altbg2 {
border-bottom: 1px solid #BBE9FF;
}
.postsubmit {
border: 1px solid #DEDEB8;
background-color: #FFFFD9;
text-align: center;
padding: 10px 0px;
margin-top: 6px;
}
.t_row {
margin-top: -1px;
}
.t_user	{
word-break: break-all;
padding-left: 8px;
background: #F5FBFF;
}
.t_msg {
table-layout: fixed;
word-wrap: break-word;
width: 100%;
height: 100%;
overflow: hidden;
background: #FFFFFF
}
.t_msg td {
padding:2px 10px;
}
.t_number {
border: 1px solid #DEDEB8;
padding: 2px;
margin: 1px;
background: #FDFFF2;
}
.t_infoline {
background: #FDFFF2;
}
.t_table {
border-left: 1px solid #86B9D6;
border-top: 1px solid #86B9D6;
border-spacing: 0px;
}
.t_table td {
padding: 4px;
border-bottom: 1px solid #86B9D6;
border-right: 1px solid #86B9D6;
}
.line {
border-top:1px solid #BBE9FF;
}
.maintable{
width: 99%;
font: 12px Tahoma, Verdana;
}
.avatar {
border: 1px solid #DEDEB8;
background-color: #FFFFD9;
padding: 3px;
}
.avatar img {
background: #FFFFFF;
}
.txadn{height: 35px;line-height: 35px;}

<%@ Page Title="" Language="C#" MasterPageFile="~/top.master" AutoEventWireup="true" CodeFile="room_login.aspx.cs" Inherits="order_list" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="Server">
    <style>
        .main-container {
            padding: 0px;
        }
        
        .ulk_menu,#index_menu {
            display:none!important;
        }
    </style>
    <script>
        pnumber = 4;
    </script>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="Server">


    <div style="padding: 18px; font-size: 13px; position: relative;">

        <div style="background: #fff; border-radius: 8px; display: flex; overflow: hidden; margin-bottom: 20px;">

            <div style="width: 87px; border-top-right-radius: 38px; border-bottom-right-radius: 38px; background: #5367F0; display: flex; justify-content: center; align-items: center; margin-right: 22px; height: 92px; border-top-left-radius: 8px; border-bottom-left-radius: 8px; z-index: 0;">

                <div style="width: 60px; height: 60px; background: #2A3EE8; text-align: center; line-height: 60px; color: #fff; border-radius: 50%;">U</div>

            </div>
            <div style="position: absolute; top: 18px; left: 77px; height: 28px; width: 28px; background: #5367f057; border-radius: 50%; z-index: 100000; z-index: 1;">
            </div>


            <div style="display: flex; height: auto; flex-direction: column; justify-content: center;">
                <b style="font-size: 18px;"><%=uConfig.gd(userdt,"phone").Substring(0,3)+"*****"+uConfig.gd(userdt,"phone").Substring(8,3) %><%=uConfig.gd(userdt, "usertype") == "0" ? " <span style='font-size: 12px;background: linear-gradient(117deg, #5092e5, #58dde9);color: #eee;padding: 2px 8px;border-radius: 4px;margin-left: 5px;'>实习新手</span>" : "" %></b>
                <div style="color: #aaa; font-size: 12px; margin-top: 12px; display: flex; align-item: center;">UID  <%=uConfig.gd(userdt,"parent_code") %>&nbsp;<svg t="1692506888169" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="10317" width="16" height="16" onclick="textCopy('<%=uConfig.gd(userdt,"parent_code") %>')"><path d="M701 244H183c-53.02 0-96 42.98-96 96v471c0 53.02 42.98 96 96 96h518c53.02 0 96-42.98 96-96V340c0-53.02-42.98-96-96-96z m-518 64h518c17.673 0 32 14.327 32 32v471c0 17.673-14.327 32-32 32H183c-17.673 0-32-14.327-32-32V340c0-17.673 14.327-32 32-32z" fill="#979797" p-id="10318"></path><path d="M701 244H183c-53.02 0-96 42.98-96 96v471c0 53.02 42.98 96 96 96h518c53.02 0 96-42.98 96-96V340c0-53.02-42.98-96-96-96z m-518 64h518c17.673 0 32 14.327 32 32v471c0 17.673-14.327 32-32 32H183c-17.673 0-32-14.327-32-32V340c0-17.673 14.327-32 32-32z" fill="#2600FF" p-id="10319"></path><path d="M239 439h69v69h-69zM239 541h69v69h-69zM239 644h69v69h-69zM350 644h69v69h-69zM460 644h69v69h-69zM571 644h69v69h-69z" fill="#CDCBFF" p-id="10320"></path><path d="M840.907 117c49.85 0 91.257 37.988 92.08 86.334l0.013 1.468v487.396c0 48.553-40.909 87.026-90.585 87.79l-1.508 0.012h-72.83v-64h72.83c15.863 0 27.851-10.985 28.09-23.425l0.003-0.377V204.802c0-12.468-11.83-23.578-27.614-23.799l-0.479-0.003H315.093c-15.863 0-27.851 10.985-28.09 23.425l-0.003 0.377v68.8h-64v-68.8c0-48.553 40.909-87.026 90.585-87.79l1.508-0.012h525.814z" fill="#2600FF" p-id="10321"></path></svg></div>
            </div>

        </div>

        <div class="menu menu-list">

            <style>
                .room-password {
                    transform: none;
                    position: relative;
                    top: 0;
                    left: 0;
                }
            </style>

            <div class="password-content room-password">
                <h3 style="text-align: center; margin: 0; padding: 13px; font-size: 16px; color: blue;">请输入4位房间号</h3>

                <div style="padding: 12px;display: flex;padding-bottom: 0;justify-content: center;">


                    <input class="password-input" oninput="handleDigitInput(event, 1)" onkeydown="handleBackspace(event, 1)">
                    <input class="password-input" oninput="handleDigitInput(event, 2)" onkeydown="handleBackspace(event, 2)">
                    <input class="password-input" oninput="handleDigitInput(event, 3)" onkeydown="handleBackspace(event, 3)">
                    <input class="password-input" oninput="handleDigitInput(event, 4)" onkeydown="handleBackspace(event, 4)">
<%--                    <input class="password-input" oninput="handleDigitInput(event, 5)" onkeydown="handleBackspace(event, 5)">
                    <input class="password-input" oninput="handleDigitInput(event, 6)" onkeydown="handleBackspace(event, 6)">--%>
                </div>

                <div style="display: flex; padding: 16px 22px;margin-top:30px;">

                    <div style="width: 100%; text-align: center;">
                        <a class="confirm_password_button confirm_pwd_button" onclick="comfirm_security()">确认
            </a>
                    </div>

                </div>
            </div>


        </div>

        <div class="menu menu-list">

            <a class="menu-item" href="login-out.aspx">
                <svg t="1693503812035" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="4097" width="22" height="22">
                    <path d="M515.584 956.928c-110.8992 0-221.7984-42.1888-306.2272-126.6176-81.8176-81.8176-126.8224-190.5664-126.8224-306.2272s45.056-224.4096 126.8224-306.2272a433.01888 433.01888 0 0 1 105.7792-77.7728c15.0528-7.8336 33.5872-2.048 41.472 13.0048 7.8336 15.0528 2.048 33.5872-13.0048 41.472a371.72736 371.72736 0 0 0-90.7776 66.7648c-70.1952 70.1952-108.8512 163.5328-108.8512 262.7584s38.656 192.5632 108.8512 262.7584c72.448 72.448 167.6288 108.6464 262.7584 108.6464 95.1808 0 190.3104-36.1984 262.7584-108.6464 144.896-144.896 144.896-380.6208 0-525.5168-31.1808-31.1808-67.072-56.2688-106.7008-74.5984a30.70464 30.70464 0 0 1-15.0016-40.7552 30.70464 30.70464 0 0 1 40.7552-15.0016c46.1824 21.3504 88.064 50.5856 124.3648 86.9376 168.8576 168.8576 168.8576 443.5968 0 612.4032-84.4288 84.3776-195.2768 126.6176-306.176 126.6176z" fill="#363F5B" p-id="4098"></path><path d="M515.584 471.5008c-16.9472 0-30.72-13.7728-30.72-30.72V85.2992c0-16.9472 13.7728-30.72 30.72-30.72s30.72 13.7728 30.72 30.72v355.4816c0 16.9472-13.7728 30.72-30.72 30.72z" fill="#B11EFF" p-id="4099"></path></svg>
                <div>安全退出</div>
                <svg t="1688760575757" class="right-icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="4792" width="25" height="25">
                    <path d="M368.576 116.672a16 16 0 0 0-22.656 0l-45.248 45.248a16 16 0 0 0 0 22.656l327.936 327.936-327.936 327.936a16 16 0 0 0 0 22.592l45.248 45.248a16 16 0 0 0 22.656 0l384.512-384.448a16 16 0 0 0 0-22.656L368.576 116.672z" fill="#2c2c2c" p-id="4793"></path></svg>
            </a>


        </div>
    </div>



    <script>
        security_success_callback = function (e) {
            console.log("房间号", e);
            v3api("room/login", {
                data: {
                    number: e.password
                }
            }, function () {
                location.href = 'index.aspx';
            })
        }
    </script>
</asp:Content>


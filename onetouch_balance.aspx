<%@ Page Title="" Language="C#" MasterPageFile="~/top.master" AutoEventWireup="true" CodeFile="onetouch_balance.aspx.cs" Inherits="notice_details" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="Server">
    <script>
        $(function () {
            popTitle('<b>一键任务【游戏转入】</b>', '');
        })
    </script>

    <style>
        .main-container {
            padding: 0px;
        }

        img {
            max-width: 100%;
        }

        .shake_lab {
            background: #d9d1c9;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 13px;
            color: #773030;
            margin: 0 3px;
        }
    </style>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="Server">

    <div id="init_page">

        <div style="position: fixed; top: 56px; left: 0; width: 100%;">
            <div style="max-width: 600px; margin: 0 auto; box-sizing: border-box; background: linear-gradient(180deg, #fbc9a8, #eee 80%); width: 100%; padding: 18px;" id="userinfo">


                <p style="margin-bottom: 3px; margin-top: 0; color: #a54444; font-size: 15px;">当前可用额度</p>

                <div style="display: flex; align-items: center;">
                    <div style="font-size: 38px; font-weight: bold; color: #df6243;" id="onetouch_balance">
                        -
                    </div>
                    <div style="margin-left: auto;">
                        <a style="background: linear-gradient(304deg, #f3a043, #d99869); color: #ffffff; padding: 5px 12px; border-radius: 3px; cursor: pointer; margin-right: 8px;" onclick="transToWindow(0)">转入游戏</a>
                        <a style="background: linear-gradient(45deg, #dd775d, #ff4337); color: #eee; padding: 5px 12px; border-radius: 3px; cursor: pointer;" onclick="start_games()">开始游戏</a>
                    </div>
                </div>
                <div style="color: #613b38; font-size: 13px; margin-top: 18px; margin-bottom: 10px; display: flex; align-items: center;">
                    游戏账户金额：<span id="game_balance" style="color: #b05d87; font-size: 16px; font-weight: bold;">-</span><a style="background: linear-gradient(45deg, #df6243, #995ba9);color: #eee;padding: 5px 12px;border-radius: 3px;cursor: pointer;display: inline-block;margin-left: auto;font-size: 13px;" onclick="transToWindow(1)">从游戏转出到额度</a>
                </div>
                <div style="color: #8f7b79; font-size: 13px;">
                    一键任务进行中：<span id="onetouch_taskamount">-</span>
                </div>
                <div style="color: #613b38; font-size: 13px;">
                    一键任务总额度：<span id="onetouch_taskbalance">-</span>（本额度+游戏余额=最终额度）
                </div>
                <p style="margin-bottom: 3px; margin-top: 18px; color: #000; font-size: 15px;">游戏盈利金额</p>
                <div style="display: flex; align-items: center;">
                    <div style="font-size: 22px; font-weight: bold; color: #8a8b8c;">
                        <span id="trans_to_user_amount">-</span><span style="font-size: 13px; font-weight: 500; margin-left: 5px;">(申请中 <span id="apply_amount">0.00</span>)</span>
                    </div>
                    <div style="margin-left: auto;">
                        <a style="background: linear-gradient(45deg, #3B79FD, #3B79FD); color: #eee; padding: 5px 12px; border-radius: 3px; font-size: 13px; cursor: pointer;" onclick="transToAccount()">提取盈利至余额</a>
                    </div>
                </div>


            </div>
        </div>

        <div id="task_rule" style="background: #eee; padding: 18px; padding-top: 130px; line-height: 28px; border-bottom-right-radius: 8px; border-bottom-left-radius: 8px; color: #2a2b2c;">
            <b style="color: #000;">交易规则：</b>

            <div style="font-size: 14px;">
                <div>1.转到余额额度：<span class="shake_lab">当前可用额度</span> - <span class="shake_lab">一键任务进行中金额</span>。</div>
                <div>2.请尽量避开凌晨00:00到00:10将额度转入游戏，此时段系统正在核对最终额度用于任务分配。</div>
                <div>3.转入游戏后请在00:00前将额度转出到<span class="shake_lab">当前可用额度</span>，以免任务额度损失，可在00:10再重新转入游戏中。</div>
                <div>4.一键任务完成返还时将从<span class="shake_lab">当前可用额度</span>扣除，如额度不足会只返还剩余额度部分到账户余额，如您的额度还在游戏内，可从游戏提取之后再转入余额。</div>
            </div>
        </div>

    </div>


    <div class="auth_page" style="display: none;">
        <div style="background: #eee; color: #d94d4d; font-size: 15px; padding: 5px 10px;">请选择游戏进入,额度需手动转入</div>
    </div>
    <div id="chat-list" class="flex_grow auth_page" style="display: none;">
    </div>



    <style>
        .menu {
            background: #ffffff70;
            margin-top: 0;
            border-radius: 0;
            border-bottom: 1px solid #f5f5f5;
        }

        .menu-list div {
            margin-left: 0;
            font-weight: 100;
            text-align: left;
        }
    </style>
    <script>
        $(function () {
            $('#task_rule').css({ 'padding-top': $('#userinfo').css('height') })

            $('.ppreturn').on('click', function () {
                if ($('.auth_page').eq(0).css('display') == "block") {
                    location.href = 'onetouch_balance.aspx';
                }
                return false;
            })
        })

        var start_games = function () {
            tp("当前游戏维护中~请耐心等待恢复！");
            return;

            $('#init_page').hide();
            $('.auth_page').show();
        }



        var chat_list = function () {
            v3api("chat_list", {
                data: {}
            }, function (e) {
                $('#chat-list').html('');

                for (var i = 0; i < e.auth_list.length; i++) {
                    var object = e.auth_list[i];
                    $('#chat-list').append('<div class="menu menu-list" onclick="open_auth(\'' + object.url + '\')">                <div style="display: flex; align-items: center;">                    <div style=" position: relative; ">                        <div style="    width: 60px;    height: 60px;    display: flex;    align-items: center;    justify-content: center;    background: #c7d8ff47;    border-radius: 50%;">  <img src="' + object.imgurl + '" style="width: 100%;height: 100%;border-radius: 12px;">  </div>                    </div>                    <div style="padding-left: 18px;">                        <div style="color: #000; font-weight: 500; font-size: 16px;">' + object.name + '</div>                        <div style="font-size: 12px; margin-top: 4px; max-width: 159px; overflow: hidden; white-space: nowrap; text-overflow: ellipsis;" class="msg_text">' + object.text_details + '</div>                    </div>                    <div style="margin-left: auto; font-size: 12px; color: gray;" class="">    <a style="background: #64e964;color: #127512;font-weight: bold;display: inline-block;padding: 8px 15px;border-radius: 3px;">立即进入</a></div>                </div>            </div>');
                }
            })
        }
        chat_list();

        var open_auth = function (url) {
            location.href = 'select_rooms.aspx?u=' + encodeURIComponent(url) + "&from=onetouch";
        }


        var transToAccount = function () {
            security_password(function (e) {
                var data = {};
                data.paypwd = e.password;
                v3api("transToAccount", {
                    data: data
                }, function (e) {
                    tp(e.msg);

                    onetouch_gameinfo();
                })
            }, { error_tip_top: '<div style="color: #2a2b2c;padding: 0 30px;margin-top: 20px;font-weight: bold;text-align:center;font-size: 14px;" class="animated-text"><div style="    margin-bottom: 10px;">是否确认将额度转入全部余额？</div><div><span style="color:red;">转入后无法撤销操作</span></div>    </div>' })
        }
    </script>


    <div id="pop-tip" class="pop-cpt" onclick="$('#pop-tip').hide();">
        <div class="pop-cpt2">
            <div class="pop-cpt-bd" style="background: radial-gradient(117.33% 20.82% at 50% 0,rgb(0 170 255 / 12%) 0,hsla(0,0%,100%,0) 100%),#fff; border-radius: 2px;">

                <div style="font-size: 15px; text-align: center; display: flex; align-items: center; padding: 8px 12px; background: #EF0926; color: #fff;" class="popbox_title">
                    <div style="width: 100%;">
                        <span class="gamename">额度转入游戏</span>
                    </div>
                    <svg t="" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="3945" width="16" height="16" onclick="$('#pop-tip').hide();">
                        <path d="M453.44 512L161.472 220.032a41.408 41.408 0 0 1 58.56-58.56L512 453.44 803.968 161.472a41.408 41.408 0 0 1 58.56 58.56L570.56 512l291.968 291.968a41.408 41.408 0 0 1-58.56 58.56L512 570.56 220.032 862.528a41.408 41.408 0 0 1-58.56-58.56L453.44 512z" fill="#ffffff" p-id="3946"></path></svg>
                </div>







                <div style="display: flex; background: #FEF4F5; padding: 6px 10px; font-size: 12px;">

                    <div style="margin-left: auto; font-size: 12px; color: gray;">
                        当前额度<span class="user_amount">-</span>
                    </div>
                </div>
                <div style="display: flex; background: #FEF4F5; padding: 6px 10px; font-size: 12px;">
                    <div style="display: flex;">
                        操作额度<input style="border: 0; border-bottom: 1px solid #a50f22; width: 80px; background: none; margin: 0 5px; text-align: center; outline: none;" id="play_money">
                        <div style="background: #E70827; color: #fff; width: 27px; height: 22px; text-align: center; font-size: 12px;">
                            元
                       
                        </div>

                        <a style="color: #ef0909; margin-left: 9px;" onclick="allin()">全部</a>
                    </div>
                </div>
                <div class="pop-cpt-footer" style="padding: 10px; padding-top: 2px;">
                    <a style="border: 1px solid #eee; display: inline-block; padding: 6px 65px; border-radius: 5px; background: #EF0926; color: #eee; cursor: pointer; width: 100%; box-sizing: border-box;" onclick="transToGames()">确定</a>

                </div>

            </div>
        </div>
    </div>

    <script>
        var siteDB = {
            transtype: 0
        }
        var allin = function () {
            $('#play_money').val($('.user_amount').text().replace(/ /g, ''));
        }
        var transToWindow = function (tp) {
            siteDB.transtype = tp;
            switch (tp) {
                case 1:
                    $('.popbox_title').css({ "background": "linear-gradient(45deg, #df6243, #995ba9)" });
                    $('.gamename').html('从游戏转出到额度');
                    $('.user_amount').html($('#game_balance').html());
                    $('#pop-tip').show();
                    break;
                default:
                    $('.popbox_title').css({ "background": "#EF0926" });
                    $('.gamename').html('额度转入游戏');
                    $('.user_amount').html($('#onetouch_balance').html());
                    $('#pop-tip').show();
                    break;
            }
            allin();
        }
        $('.pop-cpt-bd').on('click', function (e) {
            e.stopPropagation();
        })

        var transToGames = function () {
            var data = {};
            data.type = siteDB.transtype;
            data.amount = $('#play_money').val();
            security_password(function (e) {
                data.paypwd = e.password;
                v3api("transToGames", {
                    data: data,
                    error: 1
                }, function (e) {
                    tp(e.msg);

                    onetouch_gameinfo();
                })
            }, { error_tip_top: '<div style="color: #2a2b2c;padding: 0 30px;margin-top: 20px;font-weight: bold;text-align:center;font-size: 14px;" class="animated-text"><div style="    margin-bottom: 10px;">是否确认将【' + data.amount + '】<span style="color: #000;font-weight: bold;text-shadow: 5px 5px 5px yellow;">' + (data.type == 1 ? '从游戏转出到额度' : '转入游戏') + '</span>？</div><div><span style="color:red;">' + (data.type == 1 ? '' : '请再游戏退出后及时从游戏转出额度，防止任务额度损失。') + '</span></div>    </div>' })
        }

        var onetouch_gameinfo = function () {
            $('#onetouch_balance').html('-.-');
            $('#game_balance').html('-.-');
            $('#onetouch_taskamount').html('-.-');
            $('#trans_to_user_amount').html('-.-');


            var data = {};
            v3api("onetouch_gameinfo", {
                data: data
            }, function (e) {
                if (e.code == 1) {
                    for (var k in e) {
                        if (k == 'code' || k == 'msg') {
                            continue;
                        }
                        //console.log('k', k);
                        $('#' + k).html(e[k]);
                    }
                }
            })

        }

        onetouch_gameinfo();
    </script>













    <style>
        .pop-cpt {
            z-index: 9999999;
            position: relative;
            display: none;
        }

        .pop-cpt2 {
            display: flex;
            justify-content: center;
            align-items: center;
            position: fixed;
            left: 0;
            top: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,.7);
        }

        .pop-cpt a {
            text-decoration: none;
            color: inherit;
            display: block;
        }

            .pop-cpt a:hover {
                text-decoration: none;
            }

        .pop-cpt .pop-cpt-bd {
            line-height: 22px;
            width: 330px;
            /*padding: 18px 18px;*/
            box-sizing: border-box;
            border-radius: 16px;
            background: #fff;
            background: radial-gradient(117.33% 20.82% at 50% 0,rgb(255 0 0 / 12%) 0,hsla(0,0%,100%,0) 100%),#fff;
            font-size: 14px;
            color: #333;
            font-family: "微软雅黑";
            position: relative;
        }

        .pop-cpt .pop-cpt-close {
            display: inline-block;
            height: 36px;
            line-height: 36px;
            padding: 0 15px;
            color: #fff;
            position: absolute;
            top: 0;
            right: 0;
        }

        .pop-cpt .pop-cpt-tit {
            height: 52px;
            line-height: 52px;
            color: #000;
            text-align: center;
            font-size: 18px;
            font-weight: bold;
            color: #f5ecdc;
            text-shadow: 2px 3px 2px #7d5909;
        }

            .pop-cpt .pop-cpt-tit span {
                display: inline-block;
                padding: 0 10px;
                position: absolute;
                top: 0;
                right: 0;
            }

        .pop-cpt .pop-cpt-con {
            padding: 0 6px 3px 6px;
            border-radius: 15px;
            background: none;
        }

        .pop-cpt .pop-cpt-con1 {
            text-align: center;
            color: #000;
        }

        .pop-cpt .pop-cpt-con2 {
            padding-top: 10px;
        }

        .pop-cpt .pop-cpt-con2-tit {
            display: flex;
            justify-content: center;
            padding-bottom: 5px;
        }

            .pop-cpt .pop-cpt-con2-tit span {
                display: inline-block;
                width: 26px;
                line-height: 26px;
                margin-left: -3px;
                text-align: center;
                color: #FFF200;
                font-size: 16px;
                font-weight: bold;
                border-radius: 50%;
                background: #ED1B20;
            }

        .pop-cpt .pop-cpt-con2-logo {
            display: flex;
            justify-content: center;
            height: 60px;
        }

        .pop-cpt .pop-cpt-con2-logol {
            color: #fb2725;
            font-size: 54px;
            line-height: 50px;
            font-weight: bold;
            font-family: Tahoma;
        }

        .pop-cpt .pop-cpt-con2-logor {
            width: 70px;
            position: relative;
        }

        .pop-cpt .pop-cpt-con2-logor-txt1 {
        }

            .pop-cpt .pop-cpt-con2-logor-txt1 span {
                display: inline-block;
                width: 60px;
                height: 22px;
                line-height: 22px;
                text-align: center;
                color: #fff;
                border-radius: 5px;
                background: #0082f3;
                position: absolute;
                left: 8px;
                top: 2px;
            }

        .pop-cpt .pop-cpt-con2-logor-txt2 {
            line-height: 32px;
            color: #333;
            font-size: 30px;
            font-family: impact,arial;
            position: absolute;
            top: 24px;
            left: 0;
        }

        .pop-cpt .pop-cpt-con3 {
            padding: 0 2px;
        }


        .pop-cpt .pop-cpt-con4 {
            text-align: center;
            margin-bottom: 10px;
        }

            .pop-cpt .pop-cpt-con4 span {
                display: inline-block;
                width: 200px;
                height: 30px;
                line-height: 30px;
                font-size: 20px;
                font-weight: bold;
                color: #ff0;
                border-radius: 10px;
                background: #fb2725;
            }

        .pop-cpt .pop-cpt-footer {
            padding: 10px 0;
            color: #fff;
            text-align: center;
        }

        .pop-cpt .pop-cpt-footer1 {
            font-size: 16px;
        }

        .pop-cpt .pop-cpt-footer2 {
            padding-top: 5px;
            font-weight: bold;
            color: #ff0;
        }
    </style>
</asp:Content>


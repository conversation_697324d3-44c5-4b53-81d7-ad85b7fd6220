using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Data;
using System.Data.SqlClient;
using LitJson;

public partial class order_list : baseClass
{
    public DataTable userdt = new DataTable();
    public int total_number = 0;
    public Dictionary<string, object> pmlist = new Dictionary<string, object>();
    protected void Page_Load(object sender, EventArgs e)
    {
        List<SqlParameter> pams = new List<SqlParameter>();
        dbClass db = new dbClass();
        pams.Add(new SqlParameter("@userid", uConfig.p_uid));



        //一级（直属）
        pmlist["agent_sql"] = "parentid=@userid";

        //三级
        pmlist["agent_sql"] = "(CASE WHEN CHARINDEX(@sid, ','+ISNULL(relaids,'')) > 0 THEN LEN(SUBSTRING(','+ISNULL(relaids,''), CHARINDEX(@sid, ','+ISNULL(relaids,'')) + LEN(@sid), LEN(','+ISNULL(relaids,'')))) - LEN(REPLACE(SUBSTRING(','+ISNULL(relaids,''), CHARINDEX(@sid, ','+ISNULL(relaids,'')) + LEN(@sid), LEN(','+ISNULL(relaids,''))), ',', '')) ELSE 999 END)".Replace("@sid", "'," + uConfig.p_uid + ",'");
        pmlist["agent_sql"] = "  id<>" + uConfig.p_uid + " and " + pmlist["agent_sql"] + "<4 and " + pmlist["agent_sql"] + ">0 ";



        DataTable tempdt = selectDateTable(chelper.gdt("account_groups"), " name='★托号' ");
        pmlist["th_groupId"] = "-1";
        if (tempdt.Rows.Count > 0)
        {
            pmlist["th_groupId"] = tempdt.Rows[0]["id"] + "";
        }


        pams.Add(new SqlParameter("@sd_valid_money", uConfig.stcdata("sd_valid_money")));
        pams.Add(new SqlParameter("@sd_valid_lendcount", uConfig.stcdata("sd_valid_lendcount")));

        string sql = string.Empty;
        sql = @" 
select * from accounts with(nolock) where id=@userid
 
SELECT COUNT(0) as total_number FROM [api_orderList] with(nolock) where userid=@userid and state=1 and gateway_network='美宜佳' 

-- 查询下级有效人数
select count(0) as number from accounts with(nolock) where {agent_sql} and trans_amount>=@sd_valid_money

-- 查询openid
select top 1 gameName,openid from [authorize_list] with(nolock) where userid=@userid and isnull(from_type,'')='' order by refresh_time desc

-- 查询体验金到期时间
select top 1 DATEDIFF(DAY,GETDATE(),expire_time) as day,DATEDIFF(hour,GETDATE(),expire_time) as hour FROM [experience_amount] where userid=@userid and finish_time is null order by expire_time asc

".Replace("{agent_sql}", pmlist["agent_sql"] + "");

        DataSet ds = new DataSet();
        ds = db.getDataSet(sql, pams.ToArray());
        userdt = ds.Tables[0];




        DataTable dt = new DataTable();
        dt = ds.Tables[1];
        if (dt.Rows.Count > 0)
        {
            log.WriteLog("访问个人中心", "", uConfig.p_userNick + "|" + uConfig.p_loginTime + "|" + System.Web.HttpContext.Current.Request.ServerVariables["HTTP_X_TENANT_FORWARD_FOR"] + "|" + userdt.Rows[0]["phone"]);

            total_number = Convert.ToInt16(dt.Rows[0]["total_number"] + "");
        }

        pmlist["exp_expireDay"] = "0";
        pmlist["exp_expireHour"] = "";
        dt = ds.Tables[4];
        if (dt.Rows.Count > 0)
        {
            pmlist["exp_expireDay"] = dt.Rows[0]["day"] + "";
            pmlist["exp_expireHour"] = dt.Rows[0]["hour"] + "小时";
        }

        dt = ds.Tables[2];
        pmlist["valid_user_number"] = "0";
        if (dt.Rows.Count > 0)
        {
            pmlist["valid_user_number"] = dt.Rows[0]["number"] + "";
        }



        //DataTable temp_dt = SortDataTable(chelper.gdt("levels_list"), "minNumber ASC"); ;
        //try
        //{
        //    temp_dt = selectDateTable(temp_dt, "id=" + uConfig.gd(userdt, "levelid"));
        //}
        //catch (Exception)
        //{
        //    temp_dt = new DataTable();
        //}
        //if (temp_dt.Rows.Count > 0)
        //{
        //    if (Convert.ToInt16(temp_dt.Rows[0]["minNumber"]) > Convert.ToInt16(pmlist["valid_user_number"]))
        //    {
        //        pmlist["valid_user_number"] = temp_dt.Rows[0]["minNumber"] + "";
        //    }

        //}





        //pmlist["level_name"] = "未知";
        //pmlist["level_rate"] = "0";
        //temp_dt = SortDataTable(chelper.gdt("levels_list"), "minNumber ASC");
        //for (int i = 0; i < temp_dt.Rows.Count; i++)
        //{
        //    if (Convert.ToInt16(pmlist["valid_user_number"]) >= Convert.ToInt16(temp_dt.Rows[i]["minNumber"]))
        //    {
        //        pmlist["level_name"] = temp_dt.Rows[i]["name"] + "";
        //        pmlist["level_rate"] = temp_dt.Rows[i]["rate_award"] + "";
        //    }
        //    else
        //    {
        //    }

        //}

        pmlist["game_balance"] = "0.00";
        dt = ds.Tables[3];
        JsonData jd;
        if (dt.Rows.Count > 0)
        {
            pmlist["sign"] = md5UTF8("appid=" + uConfig.stcdata("g_api_appid") + "&code=0&openid=" + dt.Rows[0]["openid"] + "&token=0&key=" + uConfig.stcdata("g_api_appkey"));
            pmlist["url"] = uConfig.stcdata("g_api_gateway") + "/public.gamebalance.do?code=0&token=0&openid=" + dt.Rows[0]["openid"] + "&sign=" + pmlist["sign"];
            log.WriteLog("授权Api查询_test", "gamebalance_url", uConfig.p_userNick + " " + pmlist["url"] + "");

            pmlist["text_response"] = getContent(pmlist["url"] + "");
            try
            {
                jd = JsonMapper.ToObject(pmlist["text_response"] + "");
                pmlist["game_balance"] = jd["data"]["balance"] + "";

            }
            catch (Exception)
            {
            }

            //log.WriteLog("授权Api查询", "gamebalance", uConfig.p_userNick + " " + dt.Rows[0]["openid"] + " " + pmlist["game_balance"] + " " + pmlist["text_response"] + "");
        }




        DataTable gamedt = selectDateTable(chelper.gdt("auth_list"), " state=1");
        string temp = string.Empty;
        pmlist["game_url"] = "../online_service.aspx";
        for (int i = 0; i < gamedt.Rows.Count; i++)
        {
            temp = gamedt.Rows[i]["view_users"] + "";
            if (temp == "" || ("\n" + temp.Replace("\r", "") + "\n").IndexOf("\n" + uConfig.p_userNick + "\n") != -1)
            {
                pmlist["game_url"] = "../games/authorize.aspx?gameId=" + gamedt.Rows[i]["id"];
            }
        }


    }
}
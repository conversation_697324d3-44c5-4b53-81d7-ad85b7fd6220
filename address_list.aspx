<%@ Page Title="" Language="C#" MasterPageFile="~/top.master" AutoEventWireup="true" CodeFile="address_list.aspx.cs" Inherits="order_list" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="Server">
    <script>
        $(function () {
            popTitle('地址管理', '<a style="position: absolute; right: 0; height: 100%; width: 50px; top: 0; display: flex; align-items: center; justify-content: center;" href="address_add.aspx"><svg t="1692428311309" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="8668" width="22" height="22"><path d="M642.8 548.2H383c-19.3 0-35-15.7-35-35s15.7-35 35-35h259.8c19.3 0 35 15.7 35 35s-15.7 35-35 35z" fill="#4195F9" p-id="8669"></path><path d="M512.9 678.1c-19.3 0-35-15.7-35-35V383.3c0-19.3 15.7-35 35-35s35 15.7 35 35v259.8c0 19.3-15.6 35-35 35z" fill="#4195F9" p-id="8670"></path><path d="M512.9 902.7c-52.6 0-103.6-10.3-151.6-30.6-46.4-19.6-88-47.7-123.8-83.5s-63.8-77.4-83.5-123.8c-20.3-48-30.6-99.1-30.6-151.6 0-52.6 10.3-103.6 30.6-151.6 19.6-46.4 47.7-88 83.5-123.8s77.4-63.8 123.8-83.5c48-20.3 99.1-30.6 151.6-30.6s103.6 10.3 151.6 30.6c46.4 19.6 88 47.7 123.8 83.5s63.8 77.4 83.5 123.8c20.3 48 30.6 99.1 30.6 151.6 0 52.6-10.3 103.6-30.6 151.6-19.6 46.4-47.7 88-83.5 123.8s-77.4 63.8-123.8 83.5c-48 20.3-99 30.6-151.6 30.6z m0-709c-176.2 0-319.5 143.3-319.5 319.5s143.3 319.5 319.5 319.5 319.5-143.3 319.5-319.5c0-176.1-143.3-319.5-319.5-319.5z" p-id="8671"></path></svg></a>');
        })
    </script>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="Server">



    <div id="lists">
    </div>



    <script>
        v3api("lists", { data: { page: 'address_list', p: 0, limit: 10 } }, function (e) {
            for (var i = 0; i < e.data.list.length; i++) {
                var obj = e.data.list[i];
                $('#lists').append('<div style="background: #3848e1; color: #fff; border-radius: 10px; padding: 18px 22px; margin-top: 20px;position:relative;z-index:-1;" onclick="addrClick(' + obj.id + ',\'' + obj.addr + '\')">        <div style="position: absolute;left: 1px;bottom: -15px;z-index: -1;color: #eeeeee5c;">            <svg t="1692534221024" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="29239" width="88" height="88"><path d="M961.473195 916.343467c0 14.5408-2.935467 29.0816-8.669867 40.7552-5.802667 11.605333-11.605333 23.210667-20.206933 34.884266a128.273067 128.273067 0 0 1-31.812267 23.278934c-11.537067 5.802667-25.941333 8.738133-40.413867 8.738133H106.842795c-14.472533 0-28.8768-2.935467-40.413867-8.738133-14.472533-5.802667-25.941333-11.605333-34.679467-23.210667a104.6528 104.6528 0 0 1-23.074133-34.952533A93.525333 93.525333 0 0 1 0.005461 916.343467v-578.901334c0-29.0816 11.605333-55.296 31.744-75.639466a104.448 104.448 0 0 1 75.093334-32.017067h756.394666c28.945067 0 54.8864 11.605333 75.093334 32.085333 20.206933 20.343467 31.812267 46.4896 31.812266 75.5712v157.0816h-268.561066c-28.8768 0-54.818133 8.738133-75.093334 29.0816-20.138667 20.411733-31.744 43.690667-31.744 75.639467 0 20.411733 5.802667 37.819733 11.605334 52.360533 5.7344 14.609067 17.271467 26.2144 31.744 34.952534 14.404267 11.605333 34.6112 17.408 60.6208 17.408h262.7584v212.3776zM805.552128 177.493333H282.970795c40.413867-20.343467 80.827733-43.6224 118.3744-63.965866L496.577195 61.098667c31.812267-17.476267 54.8864-29.0816 75.093333-40.7552C597.611861 5.802667 623.621461 0 643.828395 0c20.206933 0 40.413867 5.802667 54.8864 11.605333 17.339733 8.738133 31.744 20.411733 43.281066 34.952534l63.488 130.8672zM649.631061 599.381333c0-14.609067 5.802667-26.2144 14.404267-37.888a54.613333 54.613333 0 0 1 37.546667-14.5408c14.472533 0 25.941333 5.870933 37.546666 14.5408 8.669867 8.738133 14.472533 23.278933 14.472534 37.819734 0 14.5408-5.802667 26.2144-14.472534 37.819733-8.669867 11.605333-23.074133 14.5408-37.546666 14.5408-14.404267 0-25.941333-5.802667-37.546667-14.5408a50.517333 50.517333 0 0 1-14.404267-37.819733z" fill="currentColor" p-id="29240"></path></svg>        </div>                <div style="position: absolute;right: -12px;top: -21px;z-index: -1;background: #eeeeee3d;width: 90px;height: 90px;border-radius:50%;">                   </div>        <div style=" display: flex;">            <div style="">                <strong style="color: #fff; font-size: 18px;">' + (obj.phone.substring(0, 3) + '****' + obj.phone.substring(7, 11)) + '</strong>            </div>                        <div style="margin-left: auto;">                <span style="color: #fff; border-radius: 18px; padding: 2px 8px; font-size: 13px;">' + obj.name + '</span>            </div>        </div>        <div style="color: #fff; font-size: 14px; margin-top: 15px; font-weight: bold;">            ' + obj.address + ' ' + obj.address_details + '                   </div>    </div>');
            }

        })

        var addrClick = function (id,addr) {
            localStorage.setItem('transferAddr', addr);
            history.go(-1);
        }
    </script>

</asp:Content>


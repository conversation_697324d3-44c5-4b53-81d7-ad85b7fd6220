<%@ Page Title="" Language="C#" MasterPageFile="~/top.master" AutoEventWireup="true" CodeFile="password_manager.aspx.cs" Inherits="order_list" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="Server">
    <script>
        $(function () {
            popTitle('<%=Request.QueryString["type"] + "" == "login" ? "修改登录密码" : "修改安全码" %>', '');
        })
    </script>

    <style>
        .info_title {
            color: #000;
            font-size: 14px;
            font-weight: bold;
            display: flex;
            align-items: center;
            margin: 10px 0;
            margin-top: 18px;
        }

            .info_title i {
                display: inline-block;
                background: #2A3EE8;
                width: 6px;
                height: 6px;
                border-radius: 50%;
            }
    </style>
    <style>
        .select_tab {
            color: #000;
            box-sizing: border-box;
            padding: 12px 20px;
            text-align: center;
            font-weight: bold;
            font-size: 13px;
            background: #eee;
            border-radius: 12px;
            width: 50%;
            margin: 0 10px;
        }

            .select_tab.activity {
                background: #3c3351;
                color: #fff;
                transition: all 0.2s;
            }

        .theme-green .back-bar .selected-bar {
            background-color: #3838f5!important;
            background-image: linear-gradient(to bottom, #4747ff, #3838f5)!important;
        }

        .slider-container {
            margin-bottom: 38px;
            margin-top: 52px;
        }



        .capital_label {
            width: 80px;
            text-align: center;
            background: #F4F4F4;
            padding: 8px;
            border-radius: 38px;
            color: #2F2F2F;
            margin-right: 8px;
            cursor: pointer;
            width:50%;
        }

            .capital_label.active {
                font-weight: bold;
                background: #246bff;
                color: #fff;
            }
    </style>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="Server">


    <div style="background: #fff; border-radius: 8px; padding: 18px; font-size: 14px;">



        <%--<div style="position: relative; background: #f8f8f8; border-radius: 8px; margin-bottom: 20px;">
            <input style="border: 0px; background: none; font-size: 16px; padding: 16px; outline: none; width: 100%;  box-sizing: border-box;" id="old_password" placeholder="请输入旧密码">
        </div>--%>

        <div style="position: relative; background: #bccdd73b; border: 1px solid #ddd; border-radius: 8px; margin-bottom: 20px; display: flex;">
            <input style="border: 0px; background: none; font-size: 16px; padding: 16px; outline: none; width: 100%; box-sizing: border-box; color: #222; font-weight: bold;" value="<%=uConfig.gd(userdt,"phone") %>" disabled="disabled">
        </div>

        <%if (Request.QueryString["type"] + "" == "login")
          {
        %>



        <div style="display: flex; margin-bottom: 18px;">
            <div class="capital_label active">原密码修改</div>
            <div class="capital_label">短信修改</div>
        </div>
        <script>
            $('.capital_label').on('click', function () {
                $(this).addClass("active").siblings().removeClass("active");
                if ($(this).text() == "原密码修改") {
                    $('#smscode').closest("div").hide();
                    $('#old_password').closest("div").show();
                } else {
                    $('#old_password').closest("div").hide();
                    $('#smscode').closest("div").show();
                }
            })
        </script>

        <div style="position: relative; background: #f8f8f8; border-radius: 8px; margin-bottom: 20px;">
            <input style="border: 0px; background: none; font-size: 16px; padding: 16px; outline: none; width: 100%; box-sizing: border-box;" id="old_password" placeholder="输入原密码">
        </div>

        <%
              
          } %>


        <div style="position: relative; background: #f8f8f8; border-radius: 8px; margin-bottom: 20px;">
            <input style="border: 0px; background: none; font-size: 16px; padding: 16px; outline: none; width: 100%; box-sizing: border-box;" id="new_password" placeholder="<%=Request.QueryString["type"] + "" == "login" ? "请输入新密码" : "请输入6位新密码" %>">
        </div>


        <div style="position: relative; background: #f8f8f8; border-radius: 8px; margin-bottom: 20px;">
            <input style="border: 0px; background: none; font-size: 16px; padding: 16px; outline: none; width: 100%; box-sizing: border-box;" id="confirm_password" placeholder="重复新密码">
        </div>


        <div style="position: relative; background: #f8f8f8; border-radius: 8px; margin-bottom: 20px;display:none;">
            <input style="border: 0px; background: none; font-size: 16px; padding: 16px; outline: none; width: 100%; padding-right: 94px; box-sizing: border-box;" id="smscode" placeholder="请输入短信验证码">
            <a style="position: absolute; top: 5px; right: 5px; background: #3838f5; color: #fff; display: flex; align-items: center; width: 83px; height: 37px; border-radius: 38px; justify-content: center; cursor: pointer;" onclick="send_code()" id="sendcode_button">发送</a>
        </div>

    </div>



    <div style="position: fixed; left: 0; bottom: 8px; width: 100%; padding: 5px 18px; box-sizing: border-box;">
        <a style="display: block; background: #3838f5; color: #fff; width: 100%; padding: 22px 16px; box-sizing: border-box; border-radius: 10px; cursor: pointer; text-align: center; font-size: 15px; margin-top: 13px;" onclick="set_password()">确认修改</a>
    </div>



    <div class="demo-popup-page payway-select" style="display: none;">
        <div style="padding: 18px; font-size: 13px;">

            <h4 style="color: #3C54D1; margin-bottom: 22px; font-size: 16px; display: flex;">选择收款账户
                <div style="margin-left: auto;" onclick="location.href='payway_add.aspx'">
                    <svg t="1692428311309" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="8668" width="22" height="22">
                        <path d="M642.8 548.2H383c-19.3 0-35-15.7-35-35s15.7-35 35-35h259.8c19.3 0 35 15.7 35 35s-15.7 35-35 35z" fill="#4195F9" p-id="8669"></path><path d="M512.9 678.1c-19.3 0-35-15.7-35-35V383.3c0-19.3 15.7-35 35-35s35 15.7 35 35v259.8c0 19.3-15.6 35-35 35z" fill="#4195F9" p-id="8670"></path><path d="M512.9 902.7c-52.6 0-103.6-10.3-151.6-30.6-46.4-19.6-88-47.7-123.8-83.5s-63.8-77.4-83.5-123.8c-20.3-48-30.6-99.1-30.6-151.6 0-52.6 10.3-103.6 30.6-151.6 19.6-46.4 47.7-88 83.5-123.8s77.4-63.8 123.8-83.5c48-20.3 99.1-30.6 151.6-30.6s103.6 10.3 151.6 30.6c46.4 19.6 88 47.7 123.8 83.5s63.8 77.4 83.5 123.8c20.3 48 30.6 99.1 30.6 151.6 0 52.6-10.3 103.6-30.6 151.6-19.6 46.4-47.7 88-83.5 123.8s-77.4 63.8-123.8 83.5c-48 20.3-99 30.6-151.6 30.6z m0-709c-176.2 0-319.5 143.3-319.5 319.5s143.3 319.5 319.5 319.5 319.5-143.3 319.5-319.5c0-176.1-143.3-319.5-319.5-319.5z" p-id="8671"></path></svg>
                </div>
            </h4>

            <div id="lists" style="max-height: 500px; overflow-y: auto;"></div>

        </div>
    </div>


    <script>



        var set_password = function () {
            if ($('#new_password').val() != $('#confirm_password').val()) {
                tp("两次密码输入不一致");
                return;
            }
            v3api("user/setPassword", {
                data: {
                    password_type: '<%=Request.QueryString["type"] %>',
                    password: $('#new_password').val(),
                    smscode: $('#smscode').val(),
                    old_password: $('#old_password').val(),
                    from: $('.capital_label.active').text()
                }
            }, function (e) {
                tp(e.msg);
            })
        }


        var send_code = function () {
            if ($('#sendcode_button').html() != "发送") {
                return;
            }
            v3api("user/sendPasswordCode", {
                data: {
                    password_type: '<%=Request.QueryString["type"] %>'
                }
            }, function (json) {

                $('#sendcode_button').addClass("code_send");
                setCountDown(120)
            })
        }

        var setCountDown = function (s) {
            if (s <= 0) {
                $('#sendcode_button').removeClass("code_send");
                $('#sendcode_button').html('发送');
                return;
            }
            $('#sendcode_button').html(s + ' 秒');
            setTimeout('setCountDown(' + (s - 1) + ')', 1000)
        }
    </script>
</asp:Content>


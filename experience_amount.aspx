<%@ Page Title="" Language="C#" MasterPageFile="~/top.master" AutoEventWireup="true" CodeFile="experience_amount.aspx.cs" Inherits="order_list" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="Server">
    <script>
        $(function () {
            popTitle('我的账户', '', 'account.aspx');
        })
    </script>
    <style>
        body {
            background: #fff;
            max-width: 100%;
        }

        .top-title .pptitle {
            font-weight: 100;
        }

        .main-container {
            padding: 0px;
        }

        .pptitle {
            font-weight: bold!important;
        }

        .top-title {
            background: #4785FF;
            color: #fff;
        }

        .ppreturn svg path {
            fill: currentColor;
        }

        #lists {
            padding: 10px 18px;
            max-width: 600px;
            margin: 0 auto;
        }

        .border-left {
            position: relative;
        }

            .border-left:before {
                position: absolute;
                left: 0;
                top: 50%;
                transform: translateY(-50%);
                width: 0;
                height: 22%;
                border-left: 2px solid #555;
                content: "";
            }
    </style>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="Server">



    <div style="background: #4785FF; color: #eee; padding: 18px 38px; padding-bottom: 0; font-weight: 100; padding-top: 0;">
        <div style="max-width: 600px; margin: 0 auto;">
            <p style="margin-bottom: 7px; margin-top: 0;">总资产</p>
            <div style="font-size: 38px; font-weight: bold; color: #fff;">
                <%=(Convert.ToDouble(uConfig.gnumber(userdt,"amount"))+Convert.ToDouble(uConfig.gnumber(userdt,"exp_amount"))).ToString("0.00") %><span style="font-weight: 100; font-size: 13px; color: #eee; margin-left: 8px;">体验金只能用于新手任务</span>
            </div>
            <div style="margin-top: 16px; font-size: 14px;">
                <div style="display: flex;">
                    <div style="display: flex; width: 90px; align-items: center;">
                        体验金
                    </div>
                    <b style="color: #efe2d0;display: flex;align-items: center;"><%=Convert.ToDouble(uConfig.gnumber(userdt,"exp_amount")).ToString("0.00") %><svg t="" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="12113" width="15" height="15" style="margin-left: 6px;"><path d="M512 485.74359m-459.487179 0a459.487179 459.487179 0 1 0 918.974358 0 459.487179 459.487179 0 1 0-918.974358 0Z" fill="#FFCD30" p-id="12114"></path><path d="M812.635897 504.123077c13.128205 208.738462-148.348718 357.087179-334.76923 361.025641v-45.948718c-177.230769 0-325.579487-135.220513-334.769231-316.389744h39.384615c9.189744 154.912821 139.158974 277.005128 295.384616 278.317949v-38.071795c-135.220513 0-246.810256-106.338462-254.68718-240.246154h40.697436c6.564103 112.902564 101.087179 203.487179 213.989744 203.48718v-39.384615c-91.897436 0-169.353846-72.205128-173.292308-164.102565h508.061538z m78.769231 3.938461c18.379487 18.379487 18.379487 47.261538 0 66.953847l-78.769231-70.892308 78.769231-70.892308c18.379487 18.379487 18.379487 47.261538 0 66.953846l-2.625641 2.625641c-1.312821 1.312821-1.312821 1.312821-2.625641 1.312821 1.312821 0 1.312821 1.312821 2.625641 1.31282 1.312821 1.312821 2.625641 1.312821 2.625641 2.625641z m-430.605128 98.461539c10.502564 0 18.379487-7.876923 18.379487-18.379487 0-10.502564-7.876923-18.379487-18.379487-18.379487-10.502564 0-18.379487 7.876923-18.379487 18.379487-1.312821 9.189744 7.876923 18.379487 18.379487 18.379487z" fill="#FFAC00" p-id="12115"></path><path d="M505.435897 367.589744v44.635897h-45.948718v14.441026h45.948718v45.948718h13.128206v-45.948718h45.948718v-14.441026h-45.948718v-44.635897zM620.964103 249.435897v56.451282h-56.451282v18.379488h56.451282v56.451282h18.379487v-56.451282h56.451282v-18.379488h-56.451282v-56.451282zM430.605128 105.025641v102.4h-102.4v31.507692h102.4v102.4h31.507693v-102.4h102.4v-31.507692h-102.4v-102.4z" fill="#402916" p-id="12116"></path></svg></b>
                </div>
                <div style="display: flex; margin-top: 6px;">
                    <div style="display: flex; width: 90px; align-items: center;">
                        可用余额
                    </div>
                    <b style="color: #efe2d0;display: flex;align-items: center;"><%=Convert.ToDouble(uConfig.gnumber(userdt,"amount")).ToString("0.00") %><img src="/static/images/coin.png" alt="" height="15" width="15" style="margin-left: 5px;"></b>
                </div>
            </div>





            <div style="margin: 0 auto; display: flex; background: #282E41; margin-top: 23px; border-top-left-radius: 5px; border-top-right-radius: 5px;">
                <div style="width: 50%; color: #DFD9D0; font-weight: bold; padding: 18px; display: flex; align-items: center; justify-content: center; cursor: pointer;" onclick="javascript:location.href='dating.aspx'">
                    <svg t="" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="5430" width="18" height="18" style="margin-right: 4px;">
                        <path d="M512 992C247.3 992 32 776.7 32 512S247.3 32 512 32s480 215.3 480 480c0 84.4-22.2 167.4-64.2 240-8.9 15.3-28.4 20.6-43.7 11.7-15.3-8.8-20.5-28.4-11.7-43.7 36.4-62.9 55.6-134.8 55.6-208 0-229.4-186.6-416-416-416S96 282.6 96 512s186.6 416 416 416c17.7 0 32 14.3 32 32s-14.3 32-32 32z" fill="#DFD9D0" p-id="5431"></path><path d="M640 512H384c-17.7 0-32-14.3-32-32s14.3-32 32-32h256c17.7 0 32 14.3 32 32s-14.3 32-32 32zM640 640H384c-17.7 0-32-14.3-32-32s14.3-32 32-32h256c17.7 0 32 14.3 32 32s-14.3 32-32 32z" fill="#DFD9D0" p-id="5432"></path><path d="M512 480c-8.2 0-16.4-3.1-22.6-9.4l-128-128c-12.5-12.5-12.5-32.8 0-45.3s32.8-12.5 45.3 0l128 128c12.5 12.5 12.5 32.8 0 45.3-6.3 6.3-14.5 9.4-22.7 9.4z" fill="#DFD9D0" p-id="5433"></path><path d="M512 480c-8.2 0-16.4-3.1-22.6-9.4-12.5-12.5-12.5-32.8 0-45.3l128-128c12.5-12.5 32.8-12.5 45.3 0s12.5 32.8 0 45.3l-128 128c-6.3 6.3-14.5 9.4-22.7 9.4z" fill="#DFD9D0" p-id="5434"></path><path d="M512 736c-17.7 0-32-14.3-32-32V448c0-17.7 14.3-32 32-32s32 14.3 32 32v256c0 17.7-14.3 32-32 32zM896 992H512c-17.7 0-32-14.3-32-32s14.3-32 32-32h306.8l-73.4-73.4c-12.5-12.5-12.5-32.8 0-45.3s32.8-12.5 45.3 0l128 128c9.2 9.2 11.9 22.9 6.9 34.9S908.9 992 896 992z" fill="#DFD9D0" p-id="5435"></path></svg>充值
                </div>

                <div style="width: 50%; color: #DFD9D0; font-weight: bold; padding: 18px; display: flex; align-items: center; justify-content: center; cursor: pointer;"
                    class="border-left" onclick="javascript:location.href='order_new.aspx?task=1'">
                    <svg t="" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="23966" width="18" height="18" style="margin-right: 4px;">
                        <path d="M292.318088 231.591126l-59.711844 0 0-59.717984c0-16.748419-13.586409-30.330735-30.334828-30.330735-16.756606 0-30.335852 13.581293-30.335852 30.330735l0 59.717984-59.719007 0C95.466091 231.591126 81.884798 245.178559 81.884798 261.922884c0 16.758652 13.581293 30.340968 30.331758 30.340968L171.935564 292.263853l0 59.71696c0 16.749442 13.580269 30.331758 30.335852 30.331758 16.748419 0 30.334828-13.581293 30.334828-30.331758l0-59.71696 59.711844 0c16.756606 0 30.336875-13.582316 30.336875-30.340968C322.653939 245.178559 309.074693 231.591126 292.318088 231.591126L292.318088 231.591126zM292.318088 231.591126M453.969445 107.688436l-37.265667 0L416.703777 70.422769c0-10.452029-8.476028-18.930104-18.930104-18.930104-10.455099 0-18.928057 8.478075-18.928057 18.930104l0 37.265667-37.261574 0c-10.455099 0-18.931127 8.472958-18.931127 18.924987 0 10.456122 8.475005 18.930104 18.931127 18.930104l37.261574 0 0 37.264644c0 10.453052 8.472958 18.92601 18.928057 18.92601 10.453052 0 18.930104-8.472958 18.930104-18.92601l0-37.264644 37.265667 0c10.453052 0 18.92601-8.472958 18.92601-18.930104C472.894432 116.161394 464.422497 107.688436 453.969445 107.688436L453.969445 107.688436zM453.969445 107.688436M311.309589 54.842963l-22.336683 0L288.972906 32.508326c0-6.269785-5.074565-11.346396-11.343327-11.346396-6.265691 0-11.346396 5.076612-11.346396 11.346396l0 22.334636-22.334636 0c-6.265691 0-11.346396 5.076612-11.346396 11.346396 0 6.259552 5.080705 11.34128 11.346396 11.34128l22.334636 0 0 22.334636c0 6.268761 5.080705 11.346396 11.346396 11.346396 6.267738 0 11.343327-5.076612 11.343327-11.346396L288.972906 77.530639 311.309589 77.530639c6.258528 0 11.34435-5.081728 11.34435-11.34128C322.653939 59.919575 317.574258 54.83887 311.309589 54.842963L311.309589 54.842963zM311.309589 54.842963M853.395755 614.474701c-23.59944-88.116721-74.179318-156.208402-153.206028-202.443331-6.831579-3.996003-13.932286-7.500819-21.019691-11.046568-33.313667-8.486261-77.361794-16.055642-128.554631-16.055642-51.221489 0-95.294176 7.578591-128.609889 16.070992-123.912923 57.743007-175.284838 145.710325-184.243866 274.207651-2.321877 33.371995-0.390902 66.450302 8.033961 99.139753 13.150482 51.55304 58.509462 90.146956 112.968685 90.053835 128.249686-0.294712 256.59761 0 384.852412-0.195451 41.206412-0.099261 72.930887-18.573994 95.949091-52.232514 11.702506-17.118855 17.211976-36.558564 20.310541-56.966319C867.329064 707.807203 865.775689 660.797634 853.395755 614.474701L853.395755 614.474701zM637.029348 734.404925 584.992285 734.404925c-0.677427 0-1.159404 0.580213-1.159404 1.160427l0 24.951225-0.289595 0c0.387832 2.030235 0.677427 4.159731 0.677427 6.289227 0 17.89452-14.798001 32.491953-32.983139 32.491953-18.182068 0-32.983139-14.498173-32.983139-32.491953 0-2.227733 0.293688-4.257969 0.683567-6.289227l-0.683567 0L518.254435 735.561258c0-0.668218-0.575097-1.156334-1.157357-1.156334l-51.552017 0-0.098237 0c-14.508406 0-26.407387-11.607339-26.407387-26.017508 0-14.415286 11.80586-26.022625 26.407387-26.022625l0.098237 0 51.548947 0c0.677427 0 1.16145-0.575097 1.16145-1.156334l0-23.510413c0-0.673334-0.575097-1.154287-1.16145-1.154287l-51.548947 0 0-0.094144-0.098237 0c-14.508406 0-26.407387-11.613479-26.407387-26.018531 0-14.321142 11.80586-26.022625 26.407387-26.022625l0.098237 0 47.197857 0c0.491186 0 0.973162-0.284479 1.067306-0.767478 0.387832-1.071399 0.869809-2.034329 1.448999-3.003398 0.291642-0.481976 0.192381-1.06219-0.190335-1.450022l-58.421457-57.551649c-7.351417-7.152896-4.933349-21.278587 5.318112-31.337666 10.252484-10.058057 24.572603-12.477148 31.824759-5.220898l56.966319 56.193724c0.481976 0.481976 1.258664 0.481976 1.74064 0l52.712444-55.323916c8.320486-8.995867 25.24696-6.769157 35.595635 3.481281 10.251461 10.254531 15.377192 19.537946 5.220898 30.854667l-61.901715 62.388808c-0.289595 0.284479-0.383739 0.575097-0.383739 0.962929 0.094144 0.580213 0.575097 1.063213 1.16145 1.063213l51.936779 0 0 0.093121c14.508406 0.099261 26.209889 11.705576 26.209889 26.027741 0 14.311932-11.702506 25.914154-26.209889 25.914154L584.992285 656.73307c-0.677427 0-1.159404 0.58533-1.159404 1.160427l0 23.505296c0 0.678451 0.57919 1.16145 1.159404 1.16145l52.037063 0 0 0.093121c14.508406 0.098237 26.207843 11.7066 26.207843 26.017508C663.237191 722.797586 651.537754 734.306687 637.029348 734.404925L637.029348 734.404925zM637.029348 734.404925M402.353983 367.29456c34.767782-10.414166 86.447712-21.586601 148.261422-21.586601 61.654075 0 113.226558 11.111036 147.983084 21.507807 6.238062-9.143223 11.384259-18.995595 15.233929-29.658424 3.481281-9.675341 5.414302-19.832658 8.122988-29.7935 6.185874-23.112348 0.192381-43.039149-17.700092-58.318104-24.183747-20.600136-51.744398-23.024343-80.76735-10.934005-2.028189 0.869809-3.38202 1.455139-5.314018-0.481976-37.334229-35.303993-97.305992-35.692849-134.930839-0.771571-1.74064 1.64138-2.994188 2.030235-5.319135 0.963953-15.282024-7.060799-31.238406-8.506727-47.584666-5.1288-31.053188 6.386441-56.679794 32.791782-52.136323 68.86837C381.272894 326.216061 389.235224 347.916249 402.353983 367.29456L402.353983 367.29456zM402.353983 367.29456" fill="#DFD9D0" p-id="23967"></path></svg>任务
                </div>
            </div>
        </div>
    </div>

    <div id="lists">
    </div>



    <script>

        var more_list = function (index) {
            show_list(index);
        }

        var show_list = function (index) {
            if (!index) {
                index = 0;
            }

            //experience_records
            v3api("lists", { data: { page: 'experience_amount', p: index, limit: 10 } }, function (e) {
                for (var i = 0; i < e.data.list.length; i++) {
                    var obj = e.data.list[i];
                    //$('#lists').append('<div sid="' + obj.id + '" style="padding-bottom: 18px; border-bottom: 1px solid #f2f2f2; margin-bottom: 18px;"><div style="color: #5a5b5c;text-shadow: 5px 5px 5px #00000008;font-weight: bold;">' + obj.orderNo + '</div><div style="margin-top: 10px; line-height: 22px; font-size: 12px; color: #2a2b2c;"><div>交易订单：' + obj.buy_orderId + '</div><div style="color: #9b4949;font-weight: bold;">交易金额：' + obj.amount + '</div></div><div style="font-size: 12px; margin-top: 8px; color: gray; display: flex;"><div>' + obj.create_time + '</div><div style="margin-left: auto;" class="state_text">' + (obj.state == 0 ? '<span style="color: #eee; padding: 1px 3px; margin-right: 3px; border-radius: 3px; font-size: 12px; background: linear-gradient(28deg, #ffffff, #e0ffff 100%); border: 1px solid #878181; color: #878181; font-weight: bold;">交易中</span><a style="margin-left:5px;" class="design-button-common" onclick="finish_order(' + obj.id + ')">确认收款</a>' : obj.state == 99 ? '审核中' : obj.state == -1 ? '交易撤销' : obj.state == 1 ? '<span style="color:#71429d;">交易成功</span>' : '状态异常') + '</div></div></div>');

                    //$('#lists').append('<div sid="' + obj.id + '" style="border-bottom: 1px solid #f2f2f2;margin-bottom: 18px;background: #fff;padding: 16px;border-radius: 3px;"><div style="display:flex;font-size: 12px;">    <div>下单时间：' + obj.create_time + '</div>    <div style="margin-left:auto;">' + (obj.state == 0 ? '<span style="color: #7287D7;font-weight: bold;">待审核</span>' : obj.state == 100 ? '<span style="color: #2a2b2c;font-weight: bold;text-shadow: 3px 3px 10px yellow;">进行中</span>' : obj.state == -1 ? '<span style="color: gray;">已撤销</span>' : obj.state == 1 ? '<span style="color: #379d37;">已完成</span>' : '<span style="color: red;">订单异常</span>') + '</div></div><div style="margin-top: 10px; line-height: 22px; font-size: 12px; color: #2a2b2c;"><div style="    display: flex;"><svg t="" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="11528" width="20" height="20"><path d="M723.968 979.968H300.032c-141.1584 0-256-114.8416-256-256V300.032c0-141.1584 114.8416-256 256-256h423.936c141.1584 0 256 114.8416 256 256v423.936c0 141.1584-114.8416 256-256 256zM300.032 125.952c-96 0-174.08 78.08-174.08 174.08v423.936c0 96 78.08 174.08 174.08 174.08h423.936c96 0 174.08-78.08 174.08-174.08V300.032c0-96-78.08-174.08-174.08-174.08H300.032z" fill="#515151" p-id="11529"></path><path d="M303.0016 321.3312h75.9808c-9.6768-13.5168-18.5344-24.3712-26.6752-32.4608-7.3728-7.7312-9.8816-15.872-7.5264-24.3712 2.7136-9.6768 7.7312-18.176 15.104-25.4976s15.2576-11.776 23.7568-13.3632c9.2672-1.9456 17.6128 0.768 24.9344 8.1408 17.408 16.9984 34.9696 39.6288 52.7872 67.84 3.84 6.9632 5.0176 13.5168 3.4816 19.712h97.9968c20.8896-30.9248 35.584-56.832 44.0832-77.7216 4.2496-9.6768 11.008-15.104 20.2752-16.2304 10.4448-1.1776 20.48 0.2048 30.1568 4.0448 10.0352 3.8912 17.408 9.472 22.016 16.8448 5.4272 8.1408 6.1952 16.9984 2.304 26.6752-5.7856 14.2848-13.3632 29.7984-22.6304 46.3872h66.7136c18.944 0 28.416 9.472 28.416 28.416v220.9792c0 18.944-9.472 28.416-28.416 28.416h-173.4144v26.112h207.616c10.0352 0.4096 17.408 4.2496 22.016 11.6224 5.0176 8.1408 7.5264 17.6128 7.5264 28.416s-2.304 19.1488-6.9632 26.112c-5.4272 8.1408-12.9536 12.1856-22.6304 12.1856h-207.616v64.9728c0 10.4448-4.0448 17.9712-12.1856 22.6304-8.9088 5.4272-18.7392 8.1408-29.5936 8.1408s-19.3536-2.5088-26.6752-7.5264c-8.1408-5.0176-12.1856-12.7488-12.1856-23.1936v-64.9728H270.4896c-10.4448 0-17.9712-4.0448-22.6304-12.1856-5.0176-8.1408-7.5264-17.6128-7.5264-28.416s2.304-19.1488 6.9632-26.112c5.0176-7.7312 12.7488-11.6224 23.1936-11.6224h201.2672v-26.112H303.0016c-18.944 0-28.416-9.472-28.416-28.416V349.7984c0-18.944 9.472-28.416 28.416-28.416z m168.7552 78.2848H361.5744c-4.2496 0.4096-6.4 2.5088-6.4 6.4v15.104h116.5824v-21.4528z m0 99.7376H355.1744v15.104c0.3584 4.2496 2.5088 6.4 6.4 6.4h110.1824v-21.4528z m80.64-78.2848h121.2416v-15.104c-0.4096-4.2496-2.5088-6.4-6.4-6.4h-114.8416v21.4528z m0 99.7376h114.8416c4.2496-0.3584 6.4-2.5088 6.4-6.4v-15.104h-121.2416v21.4528z" fill="#515151" p-id="11530"></path></svg><span style="    margin-left: 7px;    font-weight: bold;    font-size: 13px;">' + obj.orderNo + '</span></div><div style="    padding-left: 27px;"><div style="color: gray;">任务周期：' + obj.days + '天</div><div style="color: gray;">房主佣金：' + obj.serve_fee + '%</div></div><div style="    display: flex;    margin-top: 18px;"><svg t="" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="17986" width="20" height="20"><path d="M512 512m-512 0a512 512 0 1 0 1024 0 512 512 0 1 0-1024 0Z" fill="#F5A623" p-id="17987"></path><path d="M637.269333 611.584l-7.355733 173.636267-62.122667-2.679467 6.690134-163.618133-85.486934 10.018133-6.673066 168.2944-62.7712-1.9968 19.3536-502.8864 32.068266-30.037333h283.818667l31.402667 30.037333 16.008533 474.1632-42.734933 30.037333-96.836267-36.7104 22.698667-57.429333 51.438933 20.0192-8.021333-253.098667-85.486934 9.352534-3.328 69.461333 72.123734-8.704 7.338666 62.788267-82.141866 9.352533zM348.740267 384.512h18.688v411.392h-62.7712V443.2896l-32.7168 42.734933L221.866667 447.965867 380.808533 238.933333l50.090667 37.393067-82.141867 108.202667z m148.258133 46.08l86.152533-8.669867 3.9936-96.836266h-86.135466l-4.010667 105.506133z m152.917333-105.506133l-3.9936 90.146133 80.810667-8.021333-2.013867-82.1248h-74.786133zM491.690667 564.8384l85.469866-10.018133 3.345067-69.461334-86.152533 9.352534-2.6624 70.126933z" fill="#FFFFFF" p-id="17988"></path></svg><span style="    margin-left: 7px;    font-weight: bold;    font-size: 13px;    color: #e99810;">' + obj.award_amount.toFixed(2) + '</span></div><div style="    padding-left: 27px;"><div style="color: gray;">任务佣金：' + (obj.award_amount + obj.serve_amount).toFixed(2) + '</div><div style="color: gray;">房主佣金：' + obj.serve_amount.toFixed(2) + '</div><div style="color: gray;">佣金将在结束后到账！</div></div></div><div style="font-size: 12px; margin-top: 8px; color: gray; display: flex;"><div style="    display: flex;    align-items: center;"><svg t="" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="4547" width="13" height="13"><path d="M512 16.384C241.664 16.384 20.48 237.568 20.48 507.904s221.184 491.52 491.52 491.52 491.52-221.184 491.52-491.52-221.184-491.52-491.52-491.52z m0 929.792c-241.664 0-438.272-196.608-438.272-438.272 0-241.664 196.608-438.272 438.272-438.272s438.272 196.608 438.272 438.272c0 241.664-196.608 438.272-438.272 438.272z" fill="" p-id="4548"></path><path d="M512 253.952h-53.248l4.096 303.104 274.432 155.648 24.576-45.056-249.856-139.264z" fill="" p-id="4549"></path></svg>&nbsp;' + obj.expire_time + '结束</div><div style="margin-left: auto;" class="state_text"><div style="    color: #DF665D;    font-size: 20px;">' + obj.amount + '<img src="/static/images/coin.png" alt="" height="15" width="15" style="margin-left: 5px;"></div></div></div></div>');


                    $('#lists').append('<div style="    font-size: 12px;    margin-bottom: 18px;    border-bottom: 1px solid #eee;    padding: 10px 0;    padding-bottom: 18px;">    <div style="    display: flex;    align-items: center;">        <svg t="" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="12113" width="15" height="15" style=""><path d="M512 485.74359m-459.487179 0a459.487179 459.487179 0 1 0 918.974358 0 459.487179 459.487179 0 1 0-918.974358 0Z" fill="#FFCD30" p-id="12114"></path><path d="M812.635897 504.123077c13.128205 208.738462-148.348718 357.087179-334.76923 361.025641v-45.948718c-177.230769 0-325.579487-135.220513-334.769231-316.389744h39.384615c9.189744 154.912821 139.158974 277.005128 295.384616 278.317949v-38.071795c-135.220513 0-246.810256-106.338462-254.68718-240.246154h40.697436c6.564103 112.902564 101.087179 203.487179 213.989744 203.48718v-39.384615c-91.897436 0-169.353846-72.205128-173.292308-164.102565h508.061538z m78.769231 3.938461c18.379487 18.379487 18.379487 47.261538 0 66.953847l-78.769231-70.892308 78.769231-70.892308c18.379487 18.379487 18.379487 47.261538 0 66.953846l-2.625641 2.625641c-1.312821 1.312821-1.312821 1.312821-2.625641 1.312821 1.312821 0 1.312821 1.312821 2.625641 1.31282 1.312821 1.312821 2.625641 1.312821 2.625641 2.625641z m-430.605128 98.461539c10.502564 0 18.379487-7.876923 18.379487-18.379487 0-10.502564-7.876923-18.379487-18.379487-18.379487-10.502564 0-18.379487 7.876923-18.379487 18.379487-1.312821 9.189744 7.876923 18.379487 18.379487 18.379487z" fill="#FFAC00" p-id="12115"></path><path d="M505.435897 367.589744v44.635897h-45.948718v14.441026h45.948718v45.948718h13.128206v-45.948718h45.948718v-14.441026h-45.948718v-44.635897zM620.964103 249.435897v56.451282h-56.451282v18.379488h56.451282v56.451282h18.379487v-56.451282h56.451282v-18.379488h-56.451282v-56.451282zM430.605128 105.025641v102.4h-102.4v31.507692h102.4v102.4h31.507693v-102.4h102.4v-31.507692h-102.4v-102.4z" fill="#402916" p-id="12116"></path></svg> <span style="    margin-left: 10px;">体验金</span>' + (obj.finish_time == '' ? '<span style="background: linear-gradient(83deg, #D6E9EA, #F7D6EA, #F9B0DD);font-weight: bold;text-align: center;padding: 2px 8px;border-radius: 4px;margin-left: 7px;">正常使用</span>' : '<span style="background: #eee;font-weight: bold;text-align: center;padding: 2px 8px;border-radius: 4px;margin-left: 7px;">已到期</span>') + '    </div><div style="    padding-left: 25px;">    <div style="display: flex;padding: 18px 0;font-size: 18px;font-weight: bold;padding-bottom: 8px;">        <div style="color:#2a2b2c;width:33.33%;">' + obj.amount.toFixed(2) + '</div>        <div style="color: #1CB0A5;width:33.33%;text-align: center;">' + obj.day + '天</div>        <div style="color: gray;width:33.33%;text-align: right;flex-shrink: 0;min-width: 160px;">' + formatDate(obj.expire_time) + ' 到期</div></div><div style="    display: flex;">    <div style="color:gray;">' + obj.remark + '</div><div style="margin-left:auto;color: #ccc;">' + obj.create_time + '</div></div></div></div>');

                }



                if (e.data.list.length == 10) {
                    $('#lists').append('<div id="load_more" next_id="' + (parseInt(index) + 1) + '"></div>');
                }
                finish_trigger();

            })
        }
        function formatDate(dateString) {
            var date = new Date(dateString);
            var year = date.getFullYear();
            var month = ("0" + (date.getMonth() + 1)).slice(-2); // 月份从 0 开始，所以要加 1，并且保证两位数格式
            var day = ("0" + date.getDate()).slice(-2); // 保证两位数格式
            return year + "-" + month + "-" + day;
        }

        more_list(0);

        var finish_order = function (id) {

            security_password(function (e) {
                v3api("finish_sell_order", { data: { paypwd: e.password, id: id } }, function (e) {
                    tp(e.msg);
                    $('[sid="' + id + '"] .state_text').html('<span style="color:#71429d;">交易成功</span>');

                })


            }, { error_tip_top: '<div style="color: #2a2b2c;padding: 0 30px;margin-top: 20px;font-weight: bold;text-align:center;font-size: 14px;" class="animated-text"><div style="    margin-bottom: 10px;">确定收到款了吗？</div><div>为了保证您的资金安全，<span style="color:red;">请收到款再确认收款</span></div>    </div>' });
        }


        function timestampToDateTimeString(timestamp) {
            // 将时间戳乘以1000以转换为13位时间戳（Date对象使用13位时间戳）
            var date = new Date(timestamp * 1000);

            // 获取年、月、日、时、分、秒
            var year = date.getFullYear();
            var month = String(date.getMonth() + 1).padStart(2, '0'); // 月份从0开始，需要加1
            var day = String(date.getDate()).padStart(2, '0');
            var hours = String(date.getHours()).padStart(2, '0');
            var minutes = String(date.getMinutes()).padStart(2, '0');
            var seconds = String(date.getSeconds()).padStart(2, '0');

            // 格式化日期时间字符串
            var formattedDateTimeString = year + '-' + month + '-' + day + ' ' + hours + ':' + minutes + ':' + seconds;

            return formattedDateTimeString;
        }
    </script>


    <script>
        $(document).ready(function () {
            $(window).scroll(function () {
                if ($(document).scrollTop() <= 0) {
                    console.log("滚动条已经到达顶部为0");
                }
                if ($(document).scrollTop() + 100 >= $(document).height() - $(window).height()) {
                    trigger_bottom();
                }

            });

        });

        var trigger_check = 0;
        var trigger_bottom = function () {
            if (trigger_check != 0) {
                return;
            }
            trigger_check = 1;
            console.log("滚动条已经到达底部为" + $(document).scrollTop());

            if (typeof (more_list)) {
                var m = $('#lists').find("#load_more");
                if (m.html() != undefined) {
                    m.remove();
                    more_list(m.attr("next_id"));
                }
            }
        }

        var finish_trigger = function () {
            trigger_check = 0;
        }

    </script>

</asp:Content>


@charset "utf-8";
/* CSS Document */

/*************这里面是自己写的一些样式******************/
body,ul,ol,li,p,h1,h2,h3,h4,h5,h6,form,fieldset,table,td,img,div{margin:0;padding:0;border:0;}
body,html{
	width: 100%;
	height: 100%;
    font-family: "Microsoft Yahei", "Arial", sans-serif;
	font-size:62.5% !important;
	overflow: auto;
}
.lottery-information{
	background: url("../img/back.png") #eb2a30;
	background-repeat: no-repeat;
	background-size:100% auto;
	width: 100%;
	height: 100%;
	
}
.lottery-information-content{
	padding:45% 5rem 0 5rem;
}
.lottery-information  input{
	height: 10rem;
	font-size: 3.5rem;
	border-radius: 4rem;
    margin-bottom: 5%;
	padding: 0 4rem
}
.lottery-information .btn{
	width: 100%;
	background-color: #ffe335;
	color: #d31427;
}
#btn-lq{
	display: block;
    margin: 0rem auto;
    width: 100%;
    height: 10rem;
    line-height: 10rem;
    background-color: #ef2122;
    text-align: center;
    font-weight: bold;
    font-size: 5rem;
    color: #fff3f0;
    border-radius: 2rem;
	margin-top:5rem;
	border:3px #ffe335 solid
}
/**********下面是抽奖页面**************/

.lottery{
	background: url("../img/back.png") #eb2a30;
	background-repeat: no-repeat;
	background-size:100% auto;
	width: 100%;
	height: 100%;
	overflow: auto;
	text-align: center;
}
.lottery-content{
	padding:30% 0 5% 0;
}
/* 大转盘样式 */
.banner{display:block;width:95%;margin-left:auto;margin-right:auto;margin-bottom: 20px;}
.banner .turnplate{display:block;width:100%;position:relative;max-width: 800px;margin: auto}
.banner .turnplate canvas.item{width:100%;}
.banner .turnplate img.pointer{position:absolute;width:31.5%;height:42.5%;left:34.6%;top:23%;}

/**********活动规则******************/
.lottery .main-sec {width: 92%;overflow: hidden;padding:0 4%;text-align: left}

.lottery .h15 {height: 15px;}
.lottery .pt5 {padding-top: 5px;}

.lottery .m-title {width: 100%;height:15px;margin-top: 10px;border-bottom: 1px solid #ffb820;margin-bottom: 30px;}
.lottery .m-title h3 {width: 35%;height:30px;line-height: 30px;text-align: center;color:#ffb820;background: #c72222;margin:0 auto;font-size:1.5rem;font-weight: normal;}
.lottery .einfo {width: 92%;overflow: hidden;padding:14px 4%;background: #b21b1b;}
.lottery .einfo p {line-height: 24px;color:#ffb820;font-size:1.5rem;}
.button{
	background-color: #d03434;
	width: 80%;
	border: none;
	color: #fff;
	font-size: 1.8rem;
	height: 4rem;
	margin: auto;
	border:2px #ffe335 solid

}
.lottery-alert{
	display: none;
	position: fixed;
    left: 0;
    top: 0;
    z-index: 10;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.85);
}
.lottery-alert .content{
	position: relative;
    margin: 35% auto;
    padding-top: 0.7rem;
    width: 84%;
    height:12rem;
    border-radius: 0.26666667rem;
    background-color: #f84034;
}
.lottery-alert .content .text{
	 width: 100%;
	font-size: 2rem;
	color:#fff;
	padding-top: 2rem;
	margin-bottom: 2rem;
}
.lottery-alert .content button{
	display: block;
    width: 80%;
    line-height: 2rem;
    text-align: center;
    margin: 0.4rem auto 0rem auto;
    border: 2px solid #ffff66;
    color: #ffff66;
    font-size: 2rem;
	background: none;
}
/*******************抽奖结果的样式***********************/
.lottery-result{
	background: url("../img/back.png") #eb2a30;
	background-repeat: no-repeat;
	background-size:100% auto;
	width: 100%;
	height: 100%;
	overflow: auto;
	text-align: center;
}
.lottery-result-content{
	padding:45% 0 5% 0;
}
.lottery-result  .main-sec {width: 92%;overflow: hidden;padding:0 4%;text-align: left;}

.lottery-result  .h15 {height: 15px;}
.lottery-result .pt5 {padding-top: 5px;}

.lottery-result  .m-title {width: 100%;height:5rem;margin-top: 10px;border-bottom: 1px solid #ffb820;margin-bottom: 10rem;}
.lottery-result  .m-title h3 {width: 35%;height:10rem;line-height: 10rem;text-align: center;color:#ffb820;background: #c72222;margin:0 auto;font-size:5rem;font-weight: normal;}
.lottery-result  .einfo {width: 92%;overflow: hidden;padding:14px 4%;background: #b21b1b;}
.lottery-result  .einfo p {line-height: 24px;color:#ffb820;font-size:1.5rem;}


.peolist {width: 100%;overflow: hidden;background: #b21b1b;}
.peolist dd {width: 92%;overflow: hidden;padding:14px 0;border-bottom: 1px dashed #ffb820;margin:0 auto;}
.peolist dd:nth-of-type(3n) {border-bottom: none;}

.peolist dd img {float: left;}
.peolist dd div.right {width: 80%;float: right;padding-top:1rem}
.peolist dd div.right span {width: 100%;height: 30px;color:#ffc5c5;display: block;}
.peolist dd div.right span h2 {font-size: 3rem;float: left;width: 60%;}
.peolist dd div.right span em {width: 40%;float: right;font-size: 2.5rem;line-height: 22px;}
.peolist dd div.right p {width: 100%;font-size: 3rem;color:#ffb820;float:left;padding-top:1rem;}

.lottery-result .button{
	height: 8rem;
	line-height: 8rem;
	font-size: 4rem;
	margin-top:5rem;
}
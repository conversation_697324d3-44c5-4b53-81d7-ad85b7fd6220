<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        body {
            margin: 0;
            padding: 0;
            overflow: hidden;
        }

        .pop_overlay {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.7);
            z-index: 1;
        }

        .popup_data {
            position: fixed;
            top: 0;
            right: -200px; /* 初始时隐藏在屏幕右侧 */
            width: 500px;
            max-width:30%;
            height: 100%;
            background-color: white;
            z-index: 2;
            transition: right 0.2s; /* 添加过渡动画 */
        }

        .pop_content {
            padding: 20px;
        }
    </style>
</head>
<body>
    <button id="showBox" onclick="showPopup()">点击以显示框</button>
    <div class="pop_overlay" id="pop_overlay" onclick="hidePopup()"></div>
    <div class="popup_data" id="popup_data">
        <div class="pop_content">
            <!-- 内容放在这里 -->
        </div>
    </div>
    <script>
        function showPopup() {
            document.getElementById("popup_data").style.right = "0"; // 将右侧位置设置为0来触发动画
            document.getElementById("pop_overlay").style.display = "block";
        }

        function hidePopup() {
            document.getElementById("popup_data").style.right = "-500px"; // 将右侧位置设置回-200px来触发动画
            setTimeout(function () {
                document.getElementById("pop_overlay").style.display = "none";
            }, 200)
        }
    </script>
</body>
</html>

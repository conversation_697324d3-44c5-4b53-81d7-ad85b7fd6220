<%@ Page Title="" Language="C#" MasterPageFile="~/top.master" AutoEventWireup="true" CodeFile="gateway_pay.aspx.cs" Inherits="order_list" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="Server">
    <script>
        $(function () {
            popTitle('<%=Request.QueryString["network"] + ""=="波币_代付" ? "波币提现" : "充值" %>', '');
        })
    </script>

    <style>
        .top-title {
            background: none;
        }
    </style>
    <style>
        body {
            background: linear-gradient(45deg, #F0F4F9, #F5F5F8);
            background-repeat: no-repeat;
            height: 100vh;
        }

        .select_tab {
            color: #000;
            box-sizing: border-box;
            padding: 12px 20px;
            text-align: center;
            font-weight: bold;
            font-size: 13px;
            background: #eee;
            border-radius: 12px;
            width: 50%;
            margin: 0 10px;
        }

            .select_tab.activity {
                background: #3c3351;
                color: #fff;
                transition: all 0.2s;
            }

        .theme-green .back-bar .selected-bar {
            background-color: #3838f5!important;
            background-image: linear-gradient(to bottom, #4747ff, #3838f5)!important;
        }

        .slider-container {
            margin-bottom: 38px;
            margin-top: 52px;
        }


        .paying {
            background: #8c8c9d!important;
        }

        @keyframes rotate360 {
            from {
                transform: rotate(0deg);
            }

            to {
                transform: rotate(360deg);
            }
        }
        /* 应用旋转动画到元素 */
        .rotate-element {
            animation: rotate360 5s linear infinite; /* 旋转2秒，线性速度，无限循环 */
            margin-right: 5px;
            width: 14px;
            height: 14px;
        }

            .rotate-element path {
                fill: #fff;
            }
    </style>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="Server">






    <div style="background: #fff; border-radius: 8px; padding: 18px; font-size: 14px; margin-top: 20px;">

        <%if (!uConfig.isLogin)
          {
        %>



        <div style="text-align: left; margin-bottom: 14px; font-size: 17px; display: flex; align-items: center;">充值账号</div>


        <div style="position: relative; /* background: #f5f5f5; */border-radius: 8px; margin-bottom: 30px; display: ;">

            <input style="border: 0px; background: none; font-size: 22px; padding: 13px 0; outline: none; width: 100%; box-sizing: border-box; text-align: left;" id="recharge_user" value="" placeholder="请输入充值的账号">
            <a style="position: absolute; top: 8px; right: 5px; color: #fff; display: flex; align-items: center; /* width: 83px; */height: 37px; border-radius: 38px; justify-content: center; cursor: pointer;"></a>
        </div>




        <%--        <div style="text-align: left; margin-bottom: 14px; font-size: 17px; display: flex; align-items: center;">通道ID（channel_id）</div>


        <div style="position: relative; /* background: #f5f5f5; */border-radius: 8px; margin-bottom: 30px; display: ;">

            <input style="border: 0px; background: none; font-size: 22px; padding: 13px 0; outline: none; width: 100%; box-sizing: border-box; text-align: left;" id="channel_id" value="" placeholder="请输入通道ID">
            <a style="position: absolute; top: 8px; right: 5px; color: #fff; display: flex; align-items: center; /* width: 83px; */height: 37px; border-radius: 38px; justify-content: center; cursor: pointer;"></a>
        </div>--%>



        <%
          } %>


        <div style="text-align: left; margin-bottom: 14px; font-size: 17px; display: flex; align-items: center;">
            充值金额

            <%--<div style="margin-left: auto; font-size: 12px; display: flex;">
                <a style="display: flex; text-decoration: none;" onclick="tip_success()">
                    <svg t="1695111403213" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="8227" width="16" height="16">
                        <path d="M885 409.2c-28.2 1.4-53.9-18.2-59.4-46.8l-29-150.5-585 112.6c-8.4 1.6-15.8 6.5-20.7 13.8-4.9 7.3-6.7 15.9-5.1 24.3 6 31.3-14.5 61.6-45.8 67.6-31.3 6.1-61.6-14.5-67.6-45.8-7.4-38.7 0.7-78.1 22.9-110.9 22.2-32.8 55.8-54.9 94.5-62.4L781.4 97.4c28.5-5.5 57.5 0.5 81.6 16.8 24.1 16.3 40.4 41 45.9 69.5l30.2 157c6 31.3-14.5 61.6-45.8 67.6-2.8 0.5-5.5 0.8-8.3 0.9z" fill="#00EFEF" p-id="8228"></path><path d="M861.6 281.7H162.1c-54.6 0-99 44.4-99 99V817c0 54.6 44.4 99.1 99 99.1h699.5c54.5 0 99-44.4 99-99.1V380.7c0-54.6-44.5-99-99-99zM178.5 397.1h666.6v60.1H178.5v-60.1z m666.6 403.5H178.5V570h666.6v230.6z" fill="#52A9FF" p-id="8229"></path><path d="M446.7 682.8H251.1c-20.7 0-37.6-16.9-37.6-37.6 0-20.7 16.9-37.6 37.6-37.6h195.5c20.7 0 37.6 16.9 37.6 37.6 0.1 20.7-16.9 37.6-37.5 37.6z" fill="#00EFEF" p-id="8230"></path></svg>&nbsp;购买波币</a>
                <a style="margin-left: 20px; display: flex; text-decoration: none;" href="gateway_cash.aspx">
                    <svg t="1695111297708" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="5356" width="16" height="16">
                        <path d="M0 0h1024v1024H0V0z" fill="#202425" opacity=".01" p-id="5357"></path><path d="M955.733333 443.733333c0 226.2016-183.3984 409.6-409.6 409.6S136.533333 669.934933 136.533333 443.733333 319.931733 34.133333 546.133333 34.133333s409.6 183.3984 409.6 409.6z" fill="#FFAA44" p-id="5358"></path><path d="M703.829333 212.309333l3.1744 2.901334 3.140267 3.208533a34.133333 34.133333 0 0 1 2.730667 44.305067l-2.730667 3.1744-73.352533 75.776h47.342933a34.133333 34.133333 0 1 1 0 68.266666h-102.434133v68.266667h102.4a34.133333 34.133333 0 1 1 0 68.266667h-102.4v68.266666a34.133333 34.133333 0 1 1-68.266667 0v-68.266666h-102.4a34.133333 34.133333 0 0 1 0-68.266667h102.4v-68.266667h-102.4a34.133333 34.133333 0 0 1 0-68.266666h47.342933l-73.3184-75.776a34.133333 34.133333 0 0 1 0-47.445334l3.140267-3.242666a34.133333 34.133333 0 0 1 48.264533-0.8192l111.138134 114.824533 111.138133-114.824533a34.133333 34.133333 0 0 1 45.090133-2.082134z" fill="#FFFFFF" p-id="5359"></path><path d="M34.133333 716.8a34.133333 34.133333 0 0 1 34.133334-34.133333h204.8a34.133333 34.133333 0 0 1 34.133333 34.133333v238.933333a34.133333 34.133333 0 0 1-34.133333 34.133334H68.266667a34.133333 34.133333 0 0 1-34.133334-34.133334v-238.933333z" fill="#FFAA44" p-id="5360"></path><path d="M204.8 989.866667a34.133333 34.133333 0 0 1-34.133333-34.133334v-258.935466a34.133333 34.133333 0 0 1 10.001066-24.132267l48.264534-48.264533A34.133333 34.133333 0 0 1 253.064533 614.4H597.333333a85.333333 85.333333 0 0 1 0 170.666667h-170.666666a17.066667 17.066667 0 0 0 0 34.133333h198.007466l260.676267-77.380267a85.333333 85.333333 0 1 1 44.168533 164.864l-268.834133 78.404267c-12.219733 3.2768-21.2992 4.8128-43.349333 4.8128H204.8z" fill="#11AA66" p-id="5361"></path></svg>&nbsp;波币提现</a>
            </div>--%>
        </div>


        <div style="position: relative; /* background: #f5f5f5; */border-radius: 8px; margin-bottom: 12px; display: ;">

            <input style="border: 0px; background: none; font-size: 22px; padding: 13px 0; outline: none; width: 100%; box-sizing: border-box; text-align: left;" id="sell_number" value="100">
            <a style="position: absolute; top: 8px; right: 5px; color: #fff; display: flex; align-items: center; /* width: 83px; */height: 37px; border-radius: 38px; justify-content: center; cursor: pointer;">
                <img src="/static/images/coin.png" alt="" height="15" width="15" style="margin-left: 5px;">
            </a>
        </div>





        <div style="display: flex;">
            <div style="color: gray;">到账金额为提交订单的金额</div>
            <div style="margin-left: auto; color: #000; font-weight: bold;"></div>
        </div>

    </div>




    <div style="background: #fff; border-radius: 8px; padding: 18px; font-size: 14px; margin-top: 20px;">


        <style>
            #regular_money_list {
                position: relative;
                border-radius: 8px;
                margin-bottom: 12px;
                display: flex;
                padding: 0 5px;
                flex-wrap: wrap;
                /*max-height: 200px;
                overflow-y: auto;*/
            }

            .regular_money {
                width: 25%;
                margin: 5px 0;
            }

                .regular_money .money_item {
                    background: #fff;
                    border: 1px solid #eee;
                    text-align: center;
                    margin: 0 3px;
                    padding: 10px 0;
                    cursor: pointer;
                    border-radius: 5px;
                }

                .regular_money .money_value {
                    font-weight: bold;
                    color: #5a5b5c;
                }

                .regular_money .money_desc {
                    font-size: 12px;
                    color: gray;
                }


                .regular_money .money_item.active {
                    border: 1px solid #6a6ae5;
                    background: #6a6ae5;
                    color: #fff;
                }

                    .regular_money .money_item.active .money_value {
                        color: #fff;
                    }

                    .regular_money .money_item.active .money_desc {
                        color: #eee;
                    }
        </style>

        <div id="regular_money_list">
        </div>

        <script>
            $(function () {
                var regular_money_list = '<%=uConfig.stcdata("regular_money_list").Replace("\n", ",") %>';
                regular_money_list = regular_money_list.split(',');
                console.log('regular_money_list', regular_money_list);

                if ($('.money_item').length == 0) {


                    $('#regular_money_list').html('');

                    for (var i = 0; i < regular_money_list.length; i++) {
                        $('#regular_money_list').append('<div class="regular_money">                <div class="money_item" onclick="javascript:$(this).addClass(\'active\').closest(\'.regular_money\').siblings().find(\'.money_item\').removeClass(\'active\');$(\'#sell_number\').val(\'' + regular_money_list[i] + '\');">                    <div class="money_value">' + regular_money_list[i] + '</div>                                   </div>            </div>');

                        if (i == 0) {
                            $('#sell_number').val(regular_money_list[i]);
                        }
                    }

                }
            })
        </script>

    </div>


    <style>
        .create_button {
            display: flex;
            background: #3838f5;
            color: #fff;
            width: 100%;
            padding: 16px;
            box-sizing: border-box;
            border-radius: 10px;
            cursor: pointer;
            text-align: center;
            font-size: 14px;
            margin-top: 13px;
            text-decoration: none;
            align-items: center;
            justify-content: center;
        }

            .create_button .icon {
                display: none;
            }


        .paying .icon {
            display: block;
        }
    </style>
    <a onclick="new_advert()" class="create_button">
        <svg t="1694330145365" class="icon rotate-element" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="14297" width="22" height="22">
            <path d="M511.49 294.67c-16.57 0-30-13.43-30-30V129.21c0-16.57 13.43-30 30-30s30 13.43 30 30v135.46c0 16.57-13.44 30-30 30zM510.45 927.13c-16.57 0-30-13.43-30-30V761.67c0-16.57 13.43-30 30-30s30 13.43 30 30v135.46c0 16.57-13.43 30-30 30zM265.66 540.28H130.2c-16.57 0-30-13.43-30-30s13.43-30 30-30h135.46c16.57 0 30 13.43 30 30s-13.43 30-30 30zM892.81 540.28H757.35c-16.57 0-30-13.43-30-30s13.43-30 30-30h135.46c16.57 0 30 13.43 30 30s-13.43 30-30 30zM263.14 809.9c-7.68 0-15.36-2.93-21.21-8.79-11.72-11.72-11.72-30.71 0-42.43l95.79-95.79c11.72-11.72 30.71-11.72 42.43 0 11.72 11.72 11.72 30.71 0 42.43l-95.79 95.79c-5.86 5.86-13.54 8.79-21.22 8.79zM706.6 366.44c-7.68 0-15.36-2.93-21.21-8.79-11.72-11.72-11.72-30.71 0-42.43l95.79-95.79c11.71-11.71 30.71-11.71 42.43 0 11.72 11.72 11.72 30.71 0 42.43l-95.79 95.79c-5.86 5.86-13.54 8.79-21.22 8.79zM781.13 809.9c-7.68 0-15.35-2.93-21.21-8.79l-95.79-95.79c-11.72-11.72-11.72-30.71 0-42.43 11.72-11.72 30.71-11.71 42.43 0l95.79 95.79c11.72 11.72 11.72 30.71 0 42.43a29.95 29.95 0 0 1-21.22 8.79zM337.67 366.44c-7.68 0-15.35-2.93-21.21-8.79l-95.79-95.79c-11.72-11.72-11.72-30.71 0-42.43 11.72-11.72 30.71-11.71 42.43 0l95.79 95.79c11.72 11.72 11.72 30.71 0 42.43a29.933 29.933 0 0 1-21.22 8.79z" p-id="14298" fill="#000"></path></svg>
        <span>立即充值</span></a>





    <div class="demo-popup-page payway-select" style="display: none;">
        <div style="padding: 18px; font-size: 13px;">

            <h4 style="color: #3C54D1; margin-bottom: 22px; font-size: 16px; display: flex;">选择收款账户
                <div style="margin-left: auto;" onclick="location.href='payway_add.aspx'">
                    <svg t="1692428311309" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="8668" width="22" height="22">
                        <path d="M642.8 548.2H383c-19.3 0-35-15.7-35-35s15.7-35 35-35h259.8c19.3 0 35 15.7 35 35s-15.7 35-35 35z" fill="#4195F9" p-id="8669"></path><path d="M512.9 678.1c-19.3 0-35-15.7-35-35V383.3c0-19.3 15.7-35 35-35s35 15.7 35 35v259.8c0 19.3-15.6 35-35 35z" fill="#4195F9" p-id="8670"></path><path d="M512.9 902.7c-52.6 0-103.6-10.3-151.6-30.6-46.4-19.6-88-47.7-123.8-83.5s-63.8-77.4-83.5-123.8c-20.3-48-30.6-99.1-30.6-151.6 0-52.6 10.3-103.6 30.6-151.6 19.6-46.4 47.7-88 83.5-123.8s77.4-63.8 123.8-83.5c48-20.3 99.1-30.6 151.6-30.6s103.6 10.3 151.6 30.6c46.4 19.6 88 47.7 123.8 83.5s63.8 77.4 83.5 123.8c20.3 48 30.6 99.1 30.6 151.6 0 52.6-10.3 103.6-30.6 151.6-19.6 46.4-47.7 88-83.5 123.8s-77.4 63.8-123.8 83.5c-48 20.3-99 30.6-151.6 30.6z m0-709c-176.2 0-319.5 143.3-319.5 319.5s143.3 319.5 319.5 319.5 319.5-143.3 319.5-319.5c0-176.1-143.3-319.5-319.5-319.5z" p-id="8671"></path></svg>
                </div>
            </h4>

            <div id="lists" style="max-height: 500px; overflow-y: auto;"></div>

        </div>
    </div>


    <script>
        var current_channelId = "<%=Request.QueryString["channel_id"] %>";
        var current_paytype = "<%=Request.QueryString["paytype"] %>";
        var current_rules = "<%=Request.QueryString["rules"] %>";

        if (current_rules.indexOf('[') != -1) {
            var temp_array = current_rules.split('[');
            current_rules = temp_array[0];
            temp_array[1] = temp_array[1].replace(/]/g, "");

            temp_array = temp_array[1].split(',');
            $('#regular_money_list').html('');
            console.log('m)list', temp_array);
            for (var i = 0; i < temp_array.length; i++) {
                $('#regular_money_list').append('<div class="regular_money">                <div class="money_item" onclick="javascript:$(this).addClass(\'active\').closest(\'.regular_money\').siblings().find(\'.money_item\').removeClass(\'active\');$(\'#sell_number\').val(\'' + temp_array[i] + '\');">                    <div class="money_value">' + temp_array[i] + '</div>                                   </div>            </div>');


                if (i == 0) {
                    $('#sell_number').val(temp_array[i]);
                }
            }
        }

        var new_advert = function () {
            //security_password(function (e) {


            if (current_paytype == "") {
                tp('请选择支付方式');
                return;
            }
            var _amount = parseFloat($('#sell_number').val());

            if (current_rules.indexOf('~') != -1) {

                var tmp = current_rules.split('~');
                var min = parseFloat(tmp[0]);
                var max = parseFloat(tmp[1]);

                if (_amount < min) {
                    tp('金额不在限额范围内[' + current_rules + ']');
                    return;
                }
                if (_amount > max) {
                    tp('金额不在限额范围内[' + current_rules + ']');
                    return;
                }

            } else {

                var tmp = current_rules.split(',');

                if (!tmp.includes(_amount.toString())) {
                    tp('金额不在限额范围内[' + current_rules + ']');
                    return;
                }

            }




            if ($('.create_button').hasClass("paying")) {
                return;
            }

            $('.create_button').addClass("paying");
            $('.create_button').find('span').html('<span>请求支付中</span>');


            v3api("new_gatewayOrder", {
                error: 1,
                data: {
                    recharge_user: $('#recharge_user').val(),
                    channel_id: current_channelId,
                    //paypwd: e.password,
                    amount: _amount,
                    gateway_network: current_paytype
                }
            }, function (e) {

                setTimeout(function () {
                    $('.create_button').removeClass("paying");
                    $('.create_button').find('span').html('立即充值');
                }, 2000)

                if (e.code != 1) {
                    if (e.msg.indexOf("波币余额不足") != -1) {
                        show_success();
                    } else {
                        tp(e.msg);
                    }
                    return;
                }
                if (e.payurl != "") {
                    setTimeout(function () {
                        success_pay(e.orderId);
                    }, 5000);
                    //location.href = e.payurl;
                    //openNewWindow(e.payurl);
                    location.href = e.payurl;

                } else {
                    location.href = 'gateway_details.aspx?orderId=' + e.orderId;
                }
            })
            //})
        }

        var success_pay = function (orderId) {
            //security_password(function (e) {

            v3api("check_gatewayPay", {
                error: 1,
                data: {
                    //paypwd: e.password,
                    orderId: orderId
                }
            }, function (e) {
                if (e.msg == "未支付") {
                    setTimeout(function () {
                        success_pay(orderId);
                    }, 5000);
                } else if (e.msg == "支付成功") {
                    tp(e.msg);
                    location.href = 'gateway_details.aspx?orderId=' + orderId;
                } else {
                    tp(e.msg);
                }
            })
            //})
        }
    </script>










    <div class=" pg-tips" style="display: none;">
        <div style="width: 100vw; height: 100vh; position: fixed; top: 0; left: 0; background: #000000b5; z-index: 9991;"></div>

        <div class="password-content" style="background: linear-gradient(187deg, #D0EBF9, #ffffff); padding: 8px; box-sizing: border-box; width: 95%; max-width: 380px; position: fixed; top: 50%;">

            <h3 style="text-align: center; margin: 0; padding: 13px; font-size: 26px; color: #222; text-shadow: 2px 3px 10px #dfdbdb;">温馨提示</h3>


            <div class="tip-content" style="color: #2a2b2c; padding: 30px 18px; text-align: center;">
                波币余额不足，是否前往购买？
            </div>

            <div class="button_list">
                <div style="display: flex; padding: 16px 22px;">

                    <div style="width: 50%; text-align: center;">
                        <a style="color: #605D54; border: 1px solid #ababab; display: inline-block; width: 130px; height: 39px; line-height: 39px; border-radius: 24px; cursor: pointer; font-weight: bold;" onclick="javascript:$('.pg-tips').hide();">取消</a>
                    </div>
                    <div style="width: 50%; text-align: center;">
                        <a class="confirm_password_button" onclick="tip_success()" style="background: #2E3847; color: #d5c7c2; font-weight: bold; border-radius: 24px;">确定</a>
                    </div>

                </div>
            </div>

        </div>


    </div>

    <script>
        var show_success = function () {
            $('.pg-tips').show();
        }
        var tip_success = function () {
            $('.pg-tips').hide();

            v3api("get_bobi_transaction", {
                data: {
                    recharge_user: $('#recharge_user').val()
                }
            }, function (e) {
                location.href = e.login_url;
            })

        }
    </script>

</asp:Content>


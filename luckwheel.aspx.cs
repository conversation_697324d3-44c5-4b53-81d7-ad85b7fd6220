using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Data;
using System.Data.SqlClient;
using LitJson;

public partial class order_list : baseClass
{
    public DataTable userdt = new DataTable();
    public int total_number = 0;
    public Dictionary<string, object> pmlist = new Dictionary<string, object>();
    public List<string> wheelList = new List<string>();
    public List<string> msgs = new List<string>();
    public List<string> gcs = new List<string>();

    protected void Page_Load(object sender, EventArgs e)
    {
        List<SqlParameter> pams = new List<SqlParameter>();
        dbClass db = new dbClass();
        pams.Add(new SqlParameter("@userid", uConfig.p_uid));



        List<string> moneyList = new List<string>();

        string[] g = uConfig.stcdata("wheel_reward_rules").Split('\n');
        int money_number = g.Length;
        for (int i = 0; i < money_number; i++)
        {
            string[] g2 = g[i].Split('~');
            if (g2.Length == 3)
            {
                wheelList.Add(g2[1]);



                //Response.Write(g[i] + " = " + (Convert.ToDouble(g2[2]) * 100).ToString("0") + "<br>");

                for (int t = 0; t < Convert.ToInt32((Convert.ToDouble(g2[2]) * 100).ToString("0")); t++)
                {
                    moneyList.Add(g2[1]);
                }

            }
        }

        g = uConfig.stcdata("zp_game_counts").Split('\n');
        for (int i = 0; i < g.Length; i++)
        {
            gcs.Add(g[i]);
        }

        //Response.Write(" moneyList = " + JsonMapper.ToJson(moneyList));
        //Response.End();


        Random rd = new Random();
        var bankid1 = "";
        var bankid2 = "";
        string money = "";

        for (int i = 0; i < 25; i++)
        {
            bankid1 = rd.Next(10, 99).ToString();
            bankid2 = rd.Next(10, 99).ToString();
            int v = rd.Next(0, moneyList.Count - 1);
            money = moneyList[v];

            msgs.Add("<li><a>恭喜" + bankid1 + "***" + bankid2 + " 获得 <span style='color: #ffb820;'>" + money + "金币</span> 奖励</a></a></li>");

        }







        string sql = string.Empty;
        sql = " select * from accounts with(nolock) where id=@userid ";


        //新人任务+游戏流水
        //        sql += @" 
        //
        //
        //-- 查询下级有效人数
        //select isnull(sum(number),0) as total_number from (select (case when a.trans_amount>=5000 then 1 else 0 end) as number from accounts a with(nolock) left join payment_list pl on a.id=pl.userid where a.parentid=@userid and pl.name is not null)t
        //
        //
        //-- 查询已使用次数
        //select count(0) as number from luckwheel_records with(nolock) where userid=@userid and (isnull(from_type,'')<>'game' or (from_type='game' and datediff(day,create_time,getdate())=0))
        //
        //
        //-- 获取游戏金额
        //SELECT isnull(SUM(amount),0) as amount FROM [play_total] with(nolock) where userid=@userid and datediff(day,create_date,getdate())=0
        //
        //
        //";

        //一键任务
        //        sql += @" 
        //
        //-- 【人数查询】  1.【下级一键任务金额满5000】获得转盘次数+1
        //select isnull(sum(number),0) as total_number from (select (case when isnull(a.onetouch_amount,0)>=5000 then 1 else 0 end) as number from accounts a with(nolock) left join payment_list pl on a.id=pl.userid where a.parentid=@userid and pl.name is not null)t
        //
        //
        //-- 【总次数+当月次数】  1.【当月有效下级1+】获得转盘次数+1
        //select count(0) as number from luckwheel_records with(nolock) where userid=@userid and 
        //(
        //(isnull(from_type,'')<>'game'  and isnull(from_type,'')<>'onetouch') -- 排除之前的当日游戏数据跟【现在的】一键任务数据
        //or 
        //(from_type='onetouch' and YEAR(create_time) = YEAR(GETDATE()) AND MONTH(create_time) = MONTH(GETDATE()))
        //)
        //
        //
        //-- 获取一键任务金额
        //select count(0) as amount from accounts with(nolock) where parentid=@userid and YEAR(create_time) = YEAR(GETDATE()) AND MONTH(create_time) = MONTH(GETDATE()) and isnull(onetouch_amount,0)>=5000
        //
        //
        //";


        //一键任务+游戏流水
        sql += @" 

-- 【人数查询】  1.【下级一键任务金额满5000】获得转盘次数+1
select isnull(sum(number),0) as total_number from (select (case when isnull(a.onetouch_amount,0)>=5000 then 1 else 0 end) as number from accounts a with(nolock) left join payment_list pl on a.id=pl.userid where a.parentid=@userid and pl.name is not null)t

-- 查询已使用次数
select count(0) as number from luckwheel_records with(nolock) where userid=@userid and (isnull(from_type,'')<>'game' or (from_type='game' and datediff(day,create_time,getdate())=0))

-- 获取游戏金额
SELECT isnull(SUM(amount),0) as amount FROM [play_total] with(nolock) where userid=@userid and datediff(day,create_date,getdate())=0


";





        DataSet ds = new DataSet();
        ds = db.getDataSet(sql, pams.ToArray());
        userdt = ds.Tables[0];




        DataTable dt = new DataTable();
        dt = ds.Tables[1];
        pmlist["valid_user_number"] = "0";
        if (dt.Rows.Count > 0)
        {
            pmlist["valid_user_number"] = dt.Rows[0]["total_number"] + "";
        }

        dt = ds.Tables[2];
        pmlist["used_number"] = "0";
        if (dt.Rows.Count > 0)
        {
            pmlist["used_number"] = dt.Rows[0]["number"] + "";
        }

        dt = ds.Tables[3];
        pmlist["game_number"] = 0;
        if (dt.Rows.Count > 0)
        {
            pmlist["total_game_amount"] = dt.Rows[0]["amount"] + "";
            for (int i = 0; i < gcs.Count; i++)
            {
                var arr = gcs[i].Split('~');
                if (Convert.ToDouble(pmlist["total_game_amount"] + "") >= Convert.ToDouble(arr[0]))
                {
                    pmlist["game_number"] = Convert.ToInt16(pmlist["game_number"]) + Convert.ToInt16(arr[1]);
                }
            }
        }


        //Response.Write("total_game_amount = " + pmlist["total_game_amount"]);
        //Response.Write(";;;number = " + pmlist["game_number"]);
        //Response.End();

        if (!IsNumeric(pmlist["valid_user_number"] + ""))
        {
            pmlist["valid_user_number"] = "0";
        }
        if (!IsNumeric(pmlist["used_number"] + ""))
        {
            pmlist["used_number"] = "0";
        }



        Dictionary<string, object> temp_dic = new Dictionary<string, object>();
        temp_dic.Add("wn", Convert.ToDouble(uConfig.stcdata("wheel_number")));//系统赠送
        temp_dic.Add("vun", Convert.ToDouble(pmlist["valid_user_number"]));//一键任务有效
        temp_dic.Add("un", Convert.ToDouble(pmlist["used_number"]));//已使用
        temp_dic.Add("zn", Convert.ToDouble(uConfig.gnumber(userdt, "zp_number")));//赠送
        temp_dic.Add("gn", Convert.ToDouble(pmlist["game_number"]));//游戏

        pmlist["data"] = JsonMapper.ToJson(temp_dic);
    }
}
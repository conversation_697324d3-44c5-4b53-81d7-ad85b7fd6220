<%@ Page Title="" Language="C#" MasterPageFile="~/top.master" AutoEventWireup="true" CodeFile="order_details.aspx.cs" Inherits="order_list" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="Server">
    <script>
        $(function () {
            popTitle('订单详情', '<a style="position: absolute; right: 0; height: 100%; width: 50px; top: 0; display: flex; align-items: center; justify-content: center;" href="online_service.aspx"><svg t="1693227558502" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="4713" width="16" height="16"><path d="M515.6 770.2c48.4 0 91.3-31.3 106.2-77.4 1-3.1-0.4-6.4-3.4-7.8-3-1.3-6.5-0.2-8.1 2.6-0.2 0.3-17.2 27.8-83.7 36.8-9.5 1.3-19.2 2-28.8 2-46.5 0-67.3-17.6-67.5-17.7-2.4-2.1-5.9-2.1-8.4-0.1-2.4 2-3 5.6-1.3 8.3 20.4 32.8 56.7 53.2 95 53.3z" fill="#4E52CA" p-id="4714"></path><path d="M808.3 398.9c-45.7-123.7-164-212.1-303.2-212.1-138.6 0-256.6 87.6-302.6 210.5-2.4-2.7-4.9-5.3-7.6-7.6C221 239.9 349.9 126 505.3 126c154.7 0 283.2 112.8 310.2 261.5-2.8 3.6-5.2 7.4-7.2 11.4z m64.6-40.5c-0.6 0-1.1 0.1-1.7 0.1C832.3 190 683.5 64.3 505.2 64.3 322 64.3 169.8 197 136.1 372.5c-33.5 6.2-57.9 35.5-57.7 69.6v139.5c0 39.1 31.4 70.8 70.1 70.8 21.8 0 41.1-10.3 53.9-26.1C233.4 708 296 774 376.2 809c1-2 2.2-3.9 3.5-5.7 1.3-1.6 2.7-3 3.9-3 1.2 0 2.4 0.4 3.4 1.1-18.5-13.8-85.2-84.4-99.7-183.1-6.3-43.4 26.2-86.1 64.1-93.1 60.8-11.3 121.3-24.2 182.1-35.3 38.7-7 65.1-28.3 81.2-63.6 3.8-8.3 9.3-25 11.8-49 0.6-3.6 3.7-6.3 7.4-6.3 2.4 0 4.6 1.2 6 3.1l1.7-1c24 34.8 71.5 111.9 78.3 193.1 7.8 92.9 3.5 156.5-67.6 221.6l-0.3 0.3c-1 1.1-1.6 2.5-1.6 4 0 1.9 1 3.7 2.6 4.7 0.6 0.2 1.2 0.6 1.8 0.8 0.5 0.1 0.9 0.2 1.4 0.3 0.5 0 0.9-0.1 1.3-0.3 1-0.5 2-1.1 3-1.7 72.6-40.1 127.2-106.4 152.5-185.4a72.29 72.29 0 0 0 45.2 30.2c-30 136.9-152.2 222.7-303.5 235.6-9.6-23.4-32.4-38.6-57.6-38.5-34.2 0-62 27.1-62 60.5s27.8 60.5 62 60.5c26.6 0.1 50.3-16.9 58.7-42.1 175.1-14.2 315.5-118.3 344.8-280 26.7-11 44.1-37 44.1-65.9V429.9c0.2-39.5-32-71.5-71.8-71.5z" fill="#4E52CA" p-id="4715"></path></svg></a>');
        })
    </script>

    <style>
        .info_title {
            color: #000;
            font-size: 14px;
            font-weight: bold;
            display: flex;
            align-items: center;
            margin: 10px 0;
            margin-top: 18px;
        }

            .info_title i {
                display: inline-block;
                background: #2A3EE8;
                width: 6px;
                height: 6px;
                border-radius: 50%;
            }
    </style>
    <style>
        .select_tab {
            color: #000;
            box-sizing: border-box;
            padding: 12px 20px;
            text-align: center;
            font-weight: bold;
            font-size: 13px;
            background: #eee;
            border-radius: 12px;
            width: 50%;
            margin: 0 10px;
        }

            .select_tab.activity {
                background: #3c3351;
                color: #fff;
                transition: all 0.2s;
            }
    </style>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="Server">


    <div style="display: flex; align-items: center; padding: 4px 18px; border-radius: 18px; color: <%=uConfig.gd(userdt, "state") == "1" ? "#5ACA22" : uConfig.gd(userdt, "state") == "-1" ? "#db4a52" :uConfig.gd(userdt, "state") == "0" ? "#000;" :"gray"  %>; margin: 10px 0;">
        <%switch (uConfig.gd(userdt, "state"))
          {
              case "0":
                  %>
         <svg t="1693241279892" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="11687" width="32" height="32" style="margin-right: 10px;"><path d="M823.893333 557.226667a158.72 158.72 0 1 1-157.226666 158.72 158.08 158.08 0 0 1 157.226666-158.72z m0 39.68a119.04 119.04 0 1 0 117.973334 119.04A118.613333 118.613333 0 0 0 823.893333 597.333333zM810.666667 149.333333a108.586667 108.586667 0 0 1 104.746666 109.226667v263.466667a19.626667 19.626667 0 0 1-39.253333 1.493333V362.666667H82.133333v341.333333a69.546667 69.546667 0 0 0 62.933334 72.533333h475.52a19.84 19.84 0 0 1 1.493333 39.68H147.413333A108.373333 108.373333 0 0 1 42.666667 707.84V261.546667A109.013333 109.013333 0 0 1 144.64 149.333333H810.666667z m8.96 489.386667a14.72 14.72 0 0 1 14.72 14.72v64H896a14.933333 14.933333 0 1 1 0 29.866667h-76.586667a14.72 14.72 0 0 1-14.72-14.72v-79.146667a14.72 14.72 0 0 1 14.72-14.72z m-448-183.253333a24.96 24.96 0 0 1 0 49.706666H205.653333a24.96 24.96 0 0 1 0-49.706666zM810.666667 189.013333H147.413333a68.906667 68.906667 0 0 0-65.28 69.973334v67.2h793.6v-64a69.76 69.76 0 0 0-62.933333-72.533334z" fill="#000" p-id="11688"></path><path d="M823.893333 876.8a160.853333 160.853333 0 1 1 159.573334-160.853333 160.213333 160.213333 0 0 1-159.573334 160.853333z m0-317.44a156.586667 156.586667 0 1 0 155.306667 156.586667 155.946667 155.946667 0 0 0-155.306667-156.586667z m0 277.333333a121.173333 121.173333 0 1 1 120.106667-121.173333 120.96 120.96 0 0 1-120.106667 121.6z m0-238.08a116.906667 116.906667 0 1 0 115.84 116.906667 116.693333 116.693333 0 0 0-115.84-116.48zM147.413333 819.2a109.866667 109.866667 0 0 1-106.666666-111.36V261.546667A110.506667 110.506667 0 0 1 144.64 147.2H810.666667a109.866667 109.866667 0 0 1 106.666666 111.36v263.466667a21.333333 21.333333 0 0 1-42.666666 1.706666v-158.506666H85.333333V704a66.986667 66.986667 0 0 0 61.013334 70.4h474.24a21.333333 21.333333 0 0 1 1.493333 43.946667H147.413333z m0-667.733333h-2.773333a106.666667 106.666667 0 0 0-99.84 110.08v446.293333a106.666667 106.666667 0 0 0 102.613333 106.666667h474.453334a17.493333 17.493333 0 0 0 16.213333-17.706667 17.706667 17.706667 0 0 0-17.493333-17.706667H145.066667a71.253333 71.253333 0 0 1-64-74.666666v-343.466667h797.866666v162.56a17.493333 17.493333 0 0 0 34.986667-1.493333V258.773333A106.666667 106.666667 0 0 0 810.666667 151.466667zM896 749.866667h-76.586667a16.853333 16.853333 0 0 1-16.853333-16.853334v-79.573333a16.853333 16.853333 0 1 1 33.706667 0v62.293333H896a17.066667 17.066667 0 1 1 0 34.133334zM819.413333 640a12.586667 12.586667 0 0 0-12.586666 12.586667v79.573333a12.586667 12.586667 0 0 0 12.586666 12.586667H896a12.8 12.8 0 0 0 0-25.6h-64v-65.706667a12.586667 12.586667 0 0 0-12.586667-13.44z m-448-133.546667H205.653333a27.093333 27.093333 0 0 1 0-53.973333h166.186667a27.093333 27.093333 0 0 1 0 53.973333z m-165.76-48.853333a22.826667 22.826667 0 0 0 0 45.44h166.186667a22.826667 22.826667 0 0 0 0-45.44z m672.213334-129.28H80v-69.333333a70.826667 70.826667 0 0 1 67.413333-72.106667H812.8a71.466667 71.466667 0 0 1 64 74.666667zM85.333333 324.053333h789.333334v-62.506666A66.986667 66.986667 0 0 0 812.586667 192H147.413333a66.346667 66.346667 0 0 0-64 68.053333v64z" fill="#000" p-id="11689"></path></svg>
        <h2 style="margin: 0;">待付款</h2>
        <%
                  break;
              case "2":
              case "1":
                  %>
         <svg t="1693239104447" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="1167" width="32" height="32" style="margin-right: 10px;"><path d="M512 1024A512 512 0 1 1 512 0a512 512 0 0 1 0 1024zM397.824 713.045333a53.333333 53.333333 0 0 0 75.434667 0l323.328-323.328a53.333333 53.333333 0 0 0-75.434667-75.434666l-287.914667 283.306666-128.853333-128.853333a53.333333 53.333333 0 0 0-75.434667 75.434667l168.874667 168.874666z" fill="#23DA72" p-id="1168"></path></svg>
        <h2 style="margin: 0;">交易已完成</h2>
        <% break;
              case "1000":
                  %>
        <svg t="1694844207242" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="7494" width="32" height="32" style="margin-right: 10px;"><path d="M363.562832 930.091424a63.902231 63.902231 0 0 1-9.927008-1.061959 472.110048 472.110048 0 1 1 591.003288-457.104105 550.187123 550.187123 0 0 1-5.217451 71.982353 39.015451 39.015451 0 0 1-77.199805-11.49686 387.338014 387.338014 0 0 0 4.617214-60.023772 394.079146 394.079146 0 1 0-494.041811 380.781572 38.923107 38.923107 0 0 1-9.234427 76.691911z" fill="#0A1938" p-id="7495"></path><path d="M1013.020039 932.122998l-221.164511-372.978481a61.593624 61.593624 0 0 0-110.813117 0L460.385793 932.122998a60.254632 60.254632 0 0 0-3.139705 63.117304 64.640985 64.640985 0 0 0 57.899853 28.672894h443.252466a64.133091 64.133091 0 0 0 57.39196-28.672894 60.254632 60.254632 0 0 0-3.139705-63.117304z m-276.478725 33.890345a39.107796 39.107796 0 1 1 39.107795-39.107796 39.384828 39.384828 0 0 1-39.107795 39.153968z m41.554918-273.846914l-21.377697 141.886961a20.454254 20.454254 0 0 1-40.677648 0l-21.377697-141.886961a20.731287 20.731287 0 0 1 20.36191-23.086066h42.755394a20.408082 20.408082 0 0 1 20.500427 23.086066z" fill="#F5A00D" p-id="7496"></path><path d="M590.960582 615.474518a39.800378 39.800378 0 0 1-24.517402-8.357156l-129.882207-104.349018a38.830763 38.830763 0 0 1-14.590394-30.796812V187.966749a39.153968 39.153968 0 1 1 78.261764 0v265.489758l115.430329 92.344264a39.338656 39.338656 0 0 1-24.517402 69.904608z" fill="#0A1938" p-id="7497"></path></svg>
        <h2 style="margin: 0;">等待商家回款</h2>
        <%
                  break;
              case "-1":
                  %>         
        <svg t="1693240340895" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="3240" width="32" height="32" style="margin-right: 10px;">
            <path d="M739.712 81.322667l196.565333 163.968-54.656 65.536-196.608-163.968 54.698667-65.536z m-455.424 0L338.986667 146.773333 142.378667 310.826667 87.722667 245.290667l196.565333-164.010667zM512 174.677333a384 384 0 1 0 0.042667 768.042667 384 384 0 0 0 0-768.042667zM469.333333 341.333333h85.333334v256h-85.333334V341.333333z m0 341.333334h85.333334v85.333333h-85.333334v-85.333333z" fill="#db4a52" p-id="3241"></path></svg>
        <h2 style="margin: 0;">交易超时</h2>
        <%
                  break;
              case "-2":
                  %>         
        <svg t="1693241116246" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="8331" width="32" height="32" style="margin-right: 10px;"><path d="M512 1024C230.4 1024 0 793.6 0 512S230.4 0 512 0s512 230.4 512 512-230.4 512-512 512z m0-938.666667C277.333333 85.333333 85.333333 277.333333 85.333333 512s192 426.666667 426.666667 426.666667 426.666667-192 426.666667-426.666667S746.666667 85.333333 512 85.333333z" fill="#8a8a8a" p-id="8332"></path><path d="M682.666667 554.666667H341.333333c-25.6 0-42.666667-17.066667-42.666666-42.666667s17.066667-42.666667 42.666666-42.666667h341.333334c25.6 0 42.666667 17.066667 42.666666 42.666667s-21.333333 42.666667-42.666666 42.666667z" fill="#8a8a8a" p-id="8333"></path></svg>
        <h2 style="margin: 0;">交易被取消</h2>
        <%
                  break;
              default:
                  break;
          }%>


    </div>

    <div style="background: #fff; border-radius: 8px; padding: 18px; font-size: 14px;">

        <div style="display: flex; margin-bottom: 18px; align-items: center; border-bottom: 1px solid #ddd; padding-bottom: 18px;">
            <div style="color: #000; font-weight: bold;">
                出售&nbsp;<span style="background: #3838f5; color: #fff; padding: 1px 3px;">金币</span>
            </div>
            <div style="margin-left: auto; color: gray; text-align: right;">
                <span style="font-size: 12px;">获得佣金</span>
                <div style="font-size: 17px; display: flex; margin-top: 5px; font-weight: bold; color: #f7c727;    align-items: center;">
                    <svg t="1693232796840" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="12319" width="22" height="22">
                        <path d="M426.7 589.3l394.1-394.1-27.6-131.3H626.5L263.9 426.5z" fill="#F44D4C" p-id="12320"></path><path d="M597.5 589.3L203.4 195.2l27.5-131.3h166.7l362.6 362.6z" fill="#FF5F5F" p-id="12321"></path><path d="M512.1 628.4m-331.2 0a331.2 331.2 0 1 0 662.4 0 331.2 331.2 0 1 0-662.4 0Z" fill="#FFF082" p-id="12322"></path><path d="M512.1 833.8c-113.3 0-205.4-92.1-205.4-205.4S398.8 423 512.1 423s205.4 92.1 205.4 205.4-92.2 205.4-205.4 205.4z" fill="#FFDD60" p-id="12323"></path><path d="M512.1 443c49.5 0 96.1 19.3 131.1 54.3s54.3 81.6 54.3 131.1-19.3 96.1-54.3 131.1-81.6 54.3-131.1 54.3S416 794.5 381 759.5s-54.3-81.6-54.3-131.1S346 532.3 381 497.3 462.6 443 512.1 443m0-40c-124.5 0-225.4 100.9-225.4 225.4s100.9 225.4 225.4 225.4 225.4-100.9 225.4-225.4S636.6 403 512.1 403z" fill="#694B4B" p-id="12324"></path></svg>&nbsp;<%=uConfig.gnumber(userdt,"award_amount") %> <img src="/static/images/coin.png" alt="" height="15" width="15" style="margin-left: 5px;">
               
                </div>
            </div>
        </div>

        <div style="display: flex; margin-bottom: 38px;">
            <div style="color: #aaa;">总额</div>
            <div style="margin-left: auto; color: #555; font-weight: bold;"><%=uConfig.gnumber(userdt,"amount") %> <img src="/static/images/coin.png" alt="" height="15" width="15" style="margin-left: 5px;"></div>
        </div>

        <div style="display: flex; margin-bottom: 38px;">
            <div style="color: #aaa;">预计返还</div>
            <div style="margin-left: auto; color: #3838f5; font-weight: bold;"><%=(Convert.ToDouble(uConfig.gnumber(userdt,"amount"))+Convert.ToDouble(uConfig.gnumber(userdt,"award_amount"))).ToString("0.00") %> <img src="/static/images/coin.png" alt="" height="15" width="15" style="margin-left: 5px;"></div>
        </div>

        <div style="display: flex; margin-bottom: 38px;">
            <div style="color: #aaa;">佣金比例</div>
            <div style="margin-left: auto; color: #555; font-weight: bold;"><%=uConfig.gnumber(userdt,"return_usdt") %> %</div>
        </div>

        <div style="display: flex; margin-bottom: 18px;">
            <div style="color: #aaa;">获得佣金</div>
            <div style="margin-left: auto; color: #555; font-weight: bold;"><%=uConfig.gnumber(userdt,"award_amount") %> <img src="/static/images/coin.png" alt="" height="15" width="15" style="margin-left: 5px;"></div>
        </div>


        <div style="display: flex; margin-bottom: 38px; border-top: 1px solid #eee; padding-top: 18px;">
            <div style="color: #aaa;">订单</div>
            <div style="margin-left: auto; color: #555; font-weight: bold; display: flex;">
                <%=uConfig.gd(userdt,"orderId") %><svg t="*************" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="19366" width="18" height="18" style="top: 0; right: 0; cursor: pointer; margin-left: 10px;" onclick="textCopy('<%=uConfig.gd(userdt,"orderId") %>')">
                    <path d="M866.133333 919.466667h-405.333333c-29.866667 0-53.333333-23.466667-53.333333-53.333334v-618.666666c0-29.866667 23.466667-53.333333 53.333333-53.333334h405.333333c29.866667 0 53.333333 23.466667 53.333334 53.333334v618.666666c0 29.866667-23.466667 53.333333-53.333334 53.333334z" fill="#69C0FF" p-id="19367"></path><path d="M770.133333 428.8h-213.333333c-12.8 0-21.333333-8.533333-21.333333-21.333333s8.533333-21.333333 21.333333-21.333334h213.333333c12.8 0 21.333333 8.533333 21.333334 21.333334s-10.666667 21.333333-21.333334 21.333333zM770.133333 556.8h-213.333333c-12.8 0-21.333333-8.533333-21.333333-21.333333s8.533333-21.333333 21.333333-21.333334h213.333333c12.8 0 21.333333 8.533333 21.333334 21.333334s-10.666667 21.333333-21.333334 21.333333zM770.133333 684.8h-213.333333c-12.8 0-21.333333-8.533333-21.333333-21.333333s8.533333-21.333333 21.333333-21.333334h213.333333c12.8 0 21.333333 8.533333 21.333334 21.333334s-10.666667 21.333333-21.333334 21.333333z" fill="#FFFFFF" p-id="19368"></path><path d="M567.466667 834.133333h-405.333334c-29.866667 0-53.333333-23.466667-53.333333-53.333333v-618.666667c0-29.866667 23.466667-53.333333 53.333333-53.333333h405.333334c29.866667 0 53.333333 23.466667 53.333333 53.333333v618.666667c0 29.866667-23.466667 53.333333-53.333333 53.333333z" fill="#1890FF" p-id="19369"></path><path d="M471.466667 343.466667h-213.333334c-12.8 0-21.333333-8.533333-21.333333-21.333334s8.533333-21.333333 21.333333-21.333333h213.333334c12.8 0 21.333333 8.533333 21.333333 21.333333s-10.666667 21.333333-21.333333 21.333334zM471.466667 471.466667h-213.333334c-12.8 0-21.333333-8.533333-21.333333-21.333334s8.533333-21.333333 21.333333-21.333333h213.333334c12.8 0 21.333333 8.533333 21.333333 21.333333s-10.666667 21.333333-21.333333 21.333334zM471.466667 599.466667h-213.333334c-12.8 0-21.333333-8.533333-21.333333-21.333334s8.533333-21.333333 21.333333-21.333333h213.333334c12.8 0 21.333333 8.533333 21.333333 21.333333s-10.666667 21.333333-21.333333 21.333334z" fill="#FFFFFF" p-id="19370"></path>
                </svg>
            </div>
        </div>

        <div style="display: flex; margin-bottom: 18px;">
            <div style="color: #aaa;">创建时间</div>
            <div style="margin-left: auto; color: #555; font-weight: bold;"><%=fmTime(uConfig.gd(userdt,"create_time")) %></div>
        </div>

        <div style="display: flex; border-top: 1px solid #ddd; padding-top: 18px;">
            <div style="color: #aaa;">收款人姓名</div>
            <div style="margin-left: auto; color: #555; font-weight: bold;"><%=uConfig.gd(userdt,"payment_name") %></div>
        </div>
    </div>


    <div>
        <a class="cardbutton" onclick="switch_card()">我的收款方式
            <div style="margin-left: auto; display: flex;">
                <span><%=uConfig.gd(userdt,"payment_type") %></span>&nbsp;&nbsp;
                <div class="cardarrow">
                    <svg t="1693231842708" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="10730" width="10" height="10" class="">
                        <path d="M573.056 752l308.8-404.608A76.8 76.8 0 0 0 820.736 224H203.232a76.8 76.8 0 0 0-61.056 123.392l308.8 404.608a76.8 76.8 0 0 0 122.08 0z" fill="#eee" p-id="10731"></path></svg>
                </div>

            </div>
        </a>


        <div id="carditems" style="background: #fff; border-radius: 8px; padding: 18px; font-size: 14px; display: none;">

            <div style="display: flex; margin-bottom: 38px;">
                <div style="color: #aaa;">收款账号</div>
                <div style="margin-left: auto; color: #555; font-weight: bold; display: flex;">
                    <%=uConfig.gd(userdt,"payment_bankid") %><svg t="*************" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="19366" width="18" height="18" style="top: 0; right: 0; cursor: pointer; margin-left: 10px;" onclick="textCopy('<%=uConfig.gd(userdt,"payment_bankid") %>')">
                        <path d="M866.133333 919.466667h-405.333333c-29.866667 0-53.333333-23.466667-53.333333-53.333334v-618.666666c0-29.866667 23.466667-53.333333 53.333333-53.333334h405.333333c29.866667 0 53.333333 23.466667 53.333334 53.333334v618.666666c0 29.866667-23.466667 53.333333-53.333334 53.333334z" fill="#69C0FF" p-id="19367"></path><path d="M770.133333 428.8h-213.333333c-12.8 0-21.333333-8.533333-21.333333-21.333333s8.533333-21.333333 21.333333-21.333334h213.333333c12.8 0 21.333333 8.533333 21.333334 21.333334s-10.666667 21.333333-21.333334 21.333333zM770.133333 556.8h-213.333333c-12.8 0-21.333333-8.533333-21.333333-21.333333s8.533333-21.333333 21.333333-21.333334h213.333333c12.8 0 21.333333 8.533333 21.333334 21.333334s-10.666667 21.333333-21.333334 21.333333zM770.133333 684.8h-213.333333c-12.8 0-21.333333-8.533333-21.333333-21.333333s8.533333-21.333333 21.333333-21.333334h213.333333c12.8 0 21.333333 8.533333 21.333334 21.333334s-10.666667 21.333333-21.333334 21.333333z" fill="#FFFFFF" p-id="19368"></path><path d="M567.466667 834.133333h-405.333334c-29.866667 0-53.333333-23.466667-53.333333-53.333333v-618.666667c0-29.866667 23.466667-53.333333 53.333333-53.333333h405.333334c29.866667 0 53.333333 23.466667 53.333333 53.333333v618.666667c0 29.866667-23.466667 53.333333-53.333333 53.333333z" fill="#1890FF" p-id="19369"></path><path d="M471.466667 343.466667h-213.333334c-12.8 0-21.333333-8.533333-21.333333-21.333334s8.533333-21.333333 21.333333-21.333333h213.333334c12.8 0 21.333333 8.533333 21.333333 21.333333s-10.666667 21.333333-21.333333 21.333334zM471.466667 471.466667h-213.333334c-12.8 0-21.333333-8.533333-21.333333-21.333334s8.533333-21.333333 21.333333-21.333333h213.333334c12.8 0 21.333333 8.533333 21.333333 21.333333s-10.666667 21.333333-21.333333 21.333334zM471.466667 599.466667h-213.333334c-12.8 0-21.333333-8.533333-21.333333-21.333334s8.533333-21.333333 21.333333-21.333333h213.333334c12.8 0 21.333333 8.533333 21.333333 21.333333s-10.666667 21.333333-21.333333 21.333334z" fill="#FFFFFF" p-id="19370"></path>
                    </svg>
                </div>
            </div>

            <div style="display: flex; margin-bottom: 38px;">
                <div style="color: #aaa;">开户行</div>
                <div style="margin-left: auto; color: #555; font-weight: bold;"><%=uConfig.gd(userdt,"payment_bankname") %></div>
            </div>

            <div style="display: flex; margin-bottom: 18px;">
                <div style="color: #aaa;">姓名</div>
                <div style="margin-left: auto; color: #555; font-weight: bold;"><%=uConfig.gd(userdt,"payment_name") %></div>
            </div>
        </div>




    </div>

    <style>
        .cardbutton {
            display: flex;
            background: #3838f5;
            color: #fff;
            width: 100%;
            padding: 16px;
            box-sizing: border-box;
            border-radius: 10px;
            cursor: pointer;
            text-align: center;
            font-size: 14px;
            border-top-left-radius: 0!important;
            border-top-right-radius: 0!important;
        }

        .opencard {
            transform: rotate(180deg);
            transition: transform 0.3s;
        }

            .opencard svg {
                border: 0!important;
            }
    </style>
    <script>
        var switch_card = function () {
            $('.cardarrow').toggleClass("opencard");
            if ($('.cardarrow').hasClass('opencard')) {
                console.log('Open');
                $('.cardbutton').css({ "border-radius": "0px" });
                $('#carditems').stop().fadeIn(300);
            } else {
                console.log('Close');
                $('.cardbutton').css({ "border-radius": "10px" });
                $('.cardbutton').css({ "border-top-left-radius": "0px" });
                $('.cardbutton').css({ "border-top-right-radius": "0px" });
                $('#carditems').stop().fadeOut(0);
            }
        }
    </script>

</asp:Content>


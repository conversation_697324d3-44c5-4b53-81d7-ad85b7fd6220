<%@ Page Title="" Language="C#" MasterPageFile="~/top.master" AutoEventWireup="true" CodeFile="luckwheel.aspx.cs" Inherits="order_list" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="Server">
        <link href="../zhuanpan/css/css.css" rel="stylesheet" type="text/css">
    <style>
        html {
            max-width: 450px;
    margin: 0 auto;
    background: url(../zhuanpan/img/back.png) #eb2a30;
    background-repeat: no-repeat;
    background-size: 100% auto;
    overflow-x: hidden!important;
        }
        body {
            /*background: url(../zhuanpan/img/back.png) #eb2a30;*/
            background:none;
            margin: 0 auto!important;
            background-repeat: no-repeat;
            background-size: 100% auto;
            overflow-x:hidden!important;
                font-size: 14px!important;
            
        }

        .info_title {
            color: #000;
            font-size: 14px;
            font-weight: bold;
            display: flex;
            align-items: center;
            margin: 10px 0;
            margin-top: 18px;
        }

            .info_title i {
                display: inline-block;
                background: #2A3EE8;
                width: 6px;
                height: 6px;
                border-radius: 50%;
            }

        .main-container {
            padding: 0px;
        }

    </style>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="Server">








    <style>
        .wrapper {
            width: 100%;
            max-width: 375px;
            margin: 0px auto;
            position: relative;
        }

        .part2 {
            background: url(../static/images/wheel_options.png) left top no-repeat;
            padding-top: 100%;
            background-size: 100% 100%;
            position: relative;
        }

        .rotate-pointer {
            position: absolute;
            top: 50%;
            left: 50%;
            width: 30%;
            height: 30%;
            margin-left: -15%;
            margin-top: -15%;
            background: url(https://www.nihaoshijie.com.cn/mypro/zhuanpan/pointer.png) center center no-repeat;
            background-size: 100% 100%;
        }


        .part2 {
            transition: transform 10.5s;
            transition-timing-function: cubic-bezier(0.42, 0, 0, 0.99); /*调整旋转速度和起始速率*/
        }
    </style>







    <%--<div style="font-weight: bold;line-height: 25px;font-size: 13px;padding: 18px;background: #b21b1b;color: #ffb820;margin: 18px;">

        <div>
            如何获得转盘机会：
        </div>
        <div>
            邀请一位下级注册成功后
        </div>
        <div>
            1.【绑定回款方式】获得转盘次数+1
        </div>
        <div>
            2.【下级账号任务金额满5000元】获得转盘次数+1
        </div>

    </div>--%>




    <div style="margin-top: 92px;">


        <div style="padding: 0 18px; margin-bottom: 12px;">
            <div style="background: #b21b1b; text-align: center; padding: 3px; border-radius: 30px;">
                <ul id="jsfoot02" class="noticTipTxt">
                    <%=String.Join("\r\n", msgs) %>
                </ul>

                <script src="../static/js/scrolltext.js"></script>
                <script type="text/javascript">
                    var scrollup = new ScrollText("jsfoot02");
                    scrollup.LineHeight = 22;        //单排文字滚动的高度
                    scrollup.Amount = 1;            //注意:子模块(LineHeight)一定要能整除Amount.
                    scrollup.Delay = 1;           //延时
                    scrollup.Timeout = 3000;
                    scrollup.Start();             //文字自动滚动
                    scrollup.Direction = "up";   //默认设置为文字向上滚动

                </script>
                <style>
                    .noticTipTxt {
                        height: 22px;
                        line-height: 22px;
                        overflow: hidden;
                        list-style: none;
                        padding: 0;
                    }

                        .noticTipTxt li {
                            height: 22px;
                            line-height: 22px;
                        }

                        .noticTipTxt a {
                            color: #eee;
                            font-size: 12px;
                            text-decoration: none;
                        }
                </style>
            </div>
        </div>




        <%--     <div class="wrapper" id="wrapper">
            <div class="part2">
            </div>
            <div id="pointer" class="rotate-pointer" data-click=""></div>
        </div>--%>




        <img src="/static/images/coin.png" id="coin-img" style="display: none;" />
        <img src="/static/images/double.png" id="double-img" style="display: none;" />
        <div class="banner">
            <div class="turnplate" style="background-image: url(zhuanpan/img/turnplate-bg.png); background-size: 100% 100%;">
                <canvas class="item" id="wheelcanvas" width="422px" height="422px"></canvas>
                <img class="pointer" src="zhuanpan/img/turnplate-pointer.png" />
            </div>
        </div>


        <script src="../js/jquery_1.9.1.min.js"></script>
        <script type="text/javascript" src="../zhuanpan/js/awardRotate.js"></script>

        <script type="text/javascript">
            var turnplate = {
                restaraunts: [],				//大转盘奖品名称
                colors: [],					//大转盘奖品区块对应背景颜色
                outsideRadius: 192,			//大转盘外圆的半径
                textRadius: 155,				//大转盘奖品位置距离圆心的距离
                insideRadius: 68,			//大转盘内圆的半径
                startAngle: 0,				//开始角度
                bRotate: false				//false:停止;ture:旋转
            };

            $(document).ready(function () {
                $('.lottery-alert button').click(function () {
                    $('.lottery-alert').hide();
                })
                //动态添加大转盘的奖品与奖品区域背景颜色
                turnplate.restaraunts = JSON.parse('["<%=String.Join("\",\"", wheelList) %>"]');

                turnplate.colors = ["#FFFFFF", "#FFF4D6", "#FFFFFF", "#FFF4D6", "#FFFFFF", "#FFF4D6", "#FFFFFF", "#FFF4D6"];
                var rotateTimeOut = function () {
                    $('#wheelcanvas').rotate({
                        angle: 0,
                        animateTo: 2160,
                        duration: 8000,
                        callback: function () {
                            alert('网络超时，请检查您的网络设置！');
                        }
                    });
                };
                //旋转转盘 item:奖品位置; txt：提示语;
                var rotateFn = function (item, txt) {
                    var angles = item * (360 / turnplate.restaraunts.length) - (360 / (turnplate.restaraunts.length * 2));
                    console.log('[item]', item, angles);
                    if (angles < 270) {
                        angles = 270 - angles;
                    } else {
                        angles = 360 - angles + 270;
                    }
                    $('#wheelcanvas').stopRotate();
                    $('#wheelcanvas').rotate({
                        angle: 0,
                        animateTo: angles + 1800,
                        duration: 8000,
                        callback: function () {

                            $('#jiangli').html(txt);
                            $('.cover-tip').show()


                            //				alert(txt);
                            console.log('txt=' + txt);
                            //$('.lottery-alert .text1 p').text(txt);
                            //$('.lottery-alert').show();
                            turnplate.bRotate = false;

                        }
                    });
                };

                $('.pointer').click(function () {
                    if (turnplate.bRotate) return;
                    turnplate.bRotate = !turnplate.bRotate;
                    var item = 1;


                    v3api("luckwheel", {
                        error: 1,
                        data: {
                        }
                    }, function (e) {
                        if (e.code == 1) {
                            $('#share_button').html('获得转盘机会【剩余' + e.total_number + '次】');
                            item = e.target_index + 1;
                            rotateFn(item, e.text);
                        } else {
                            turnplate.bRotate = false;
                            tp(e.msg);
                        }
                    })





                });
            });

            function rnd(n, m) {
                var random = Math.floor(Math.random() * (m - n + 1) + n);
                return random;

            }


            //页面所有元素加载完毕后执行drawRouletteWheel()方法对转盘进行渲染
            window.onload = function () {
                drawRouletteWheel();
            };

            function drawRouletteWheel() {
                var canvas = document.getElementById("wheelcanvas");
                if (canvas.getContext) {
                    //根据奖品个数计算圆周角度
                    var arc = Math.PI / (turnplate.restaraunts.length / 2);
                    var ctx = canvas.getContext("2d");
                    //在给定矩形内清空一个矩形
                    ctx.clearRect(0, 0, 422, 422);
                    //strokeStyle 属性设置或返回用于笔触的颜色、渐变或模式  
                    ctx.strokeStyle = "#FFBE04";
                    //font 属性设置或返回画布上文本内容的当前字体属性
                    ctx.font = '16px Microsoft YaHei';
                    //turnplate.startAngle += -2 * arc
                    for (var i = 0; i < turnplate.restaraunts.length; i++) {
                        var angle = turnplate.startAngle + i * arc;
                        ctx.fillStyle = turnplate.colors[i];
                        ctx.beginPath();
                        //arc(x,y,r,起始角,结束角,绘制方向) 方法创建弧/曲线（用于创建圆或部分圆）    
                        ctx.arc(211, 211, turnplate.outsideRadius, angle, angle + arc, false);
                        ctx.arc(211, 211, turnplate.insideRadius, angle + arc, angle, true);
                        ctx.stroke();
                        ctx.fill();
                        //锁画布(为了保存之前的画布状态)
                        ctx.save();

                        //----绘制奖品开始----
                        ctx.fillStyle = "#E5302F";
                        var text = turnplate.restaraunts[i];
                        var line_height = 17;
                        //translate方法重新映射画布上的 (0,0) 位置
                        ctx.translate(211 + Math.cos(angle + arc / 2) * turnplate.textRadius, 211 + Math.sin(angle + arc / 2) * turnplate.textRadius);

                        //rotate方法旋转当前的绘图
                        ctx.rotate(angle + arc / 2 + Math.PI / 2);

                        ctx.fillText(text, -ctx.measureText(text).width / 2, 0);

                        var img = "";
                        switch (text) {
                            case "双倍刷单收益":
                                img = document.getElementById("double-img");
                                break;
                            default:
                                img = document.getElementById("coin-img");
                                break;
                        }
                        img.onload = function () {
                            ctx.drawImage(img, -15, 10, 27, 27);
                        };
                        ctx.drawImage(img, -15, 10, 27, 27);


                        //把当前画布返回（调整）到上一个save()状态之前 
                        ctx.restore();
                        //----绘制奖品结束----
                    }
                }
            }

        </script>











        <script type="text/javascript" id="_LKDATA">
            <%=pmlist["data"] %>
        </script>



        <div style="text-align: center;">

            <a id="share_button" style="background: #fff4d6; color: #e74340; border-radius: 8px; text-align: center; display: inline-block; padding: 15px 38px; margin-bottom: 0px; outline: none; text-decoration: none; cursor: pointer;font-size:18px;" href="partners_manage.aspx">获得转盘机会【剩余<%=Convert.ToDouble(uConfig.stcdata("wheel_number"))+Convert.ToDouble(pmlist["valid_user_number"])-Convert.ToDouble(pmlist["used_number"])+Convert.ToDouble(uConfig.gnumber(userdt,"zp_number"))+Convert.ToDouble(pmlist["game_number"]) %>次】</a>

        </div>

    </div>


    <div style="line-height: 25px; font-size: 13px; padding: 18px; background: #fff; color: #000; margin: 18px; border-radius: 5px;">


        <div style="text-align: center; border-bottom: 1px solid #EDEDED; margin-bottom: 10px; padding-bottom: 10px;">
            <img src="zhuanpan/img/task_draw_reward_rule_title_img.png" alt="task_draw_reward_rule_title_img" style="width: 50%;">
        </div>

        <div>
            如何获得转盘机会：
       
        </div>
        <div>
            邀请一位下级注册成功后
       
        </div>
   <%--     <div>
            1.【绑定回款方式】获得转盘次数+1       
        </div>--%>
      <%--  <div>
            2.【出借完成一次】获得转盘次数+1       
        </div>--%>
        <div>
            1.【下级一键任务金额满5000】获得转盘次数+1
        </div>

<%--        <div>
            邀请下级获得转盘机会：
            <div>【下级一键任务满5000】视为有效会员！</div>
        </div>
        <div style="background: #f1eded;padding: 8px 12px;border-radius: 3px;margin-top: 4px;">
            <%for (int i = 0; i < gcs.Count; i++)
              {
                  var arr = gcs[i].Split('~');
                  %>
            <div><%=i+1 %>.【当月有效下级<%=arr[0] %>+】获得转盘次数+<%=arr[1] %></div>
            <%
              } %>
        </div>--%>

         <div>
            2.完成游戏任务
        </div>
        <div style="background: #f1eded;padding: 8px 12px;border-radius: 3px;margin-top: 4px;">
            <%for (int i = 0; i < gcs.Count; i++)
              {
                  var arr = gcs[i].Split('~');
                  %>
            <div><%=i+1 %>.【今日游戏有效流水<%=arr[0] %>】获得转盘次数+<%=arr[1] %></div>
            <%
              } %>
        </div>

    </div>




    <style>
        a {
            text-decoration: none;
        }

        .cover {
            z-index: 9999;
            position: fixed;
            top: 0;
            background: rgba(39,40,34,.6);
        }

        .covers1, .covers2, .covers3 {
            z-index: 9999;
            position: fixed;
            left: 0;
            right: 0;
            top: 0;
            bottom: 0;
            width: 70%;
            height: 65vw;
            background: white;
            margin: auto;
            border-radius: 7px;
            animation: action_translateY 1s linear;
            /*display: none;*/
        }

        .covers_quit {
            position: absolute;
            right: 5vw;
            top: 5vw;
            width: 5%;
        }

            .covers_quit img {
                width: 100%;
            }

        .covers_icon {
            width: 46%;
            margin-left: 27%;
            margin-top: 8vw;
        }

            .covers_icon img {
                width: 100%;
            }

        .covers_font {
            font-size: 4vw;
            text-align: center;
        }

            .covers_font span {
                color: #EA2329;
            }

        .covers_btn, .covers_btn2 {
            width: 86%;
            margin-left: 7%;
            margin-top: 7vw;
            height: 10vw;
            line-height: 10vw;
            color: white;
            font-size: 4.2vw;
            text-align: center;
            background: #F44336;
            border-radius: 90px;
        }

        .covers2, .covers3 {
            width: 80%;
            height: 85vw;
            display: none;
        }

        .covers_btn1, .covers_btn2 {
            /*height: 12vw;		line-height: 12vw;*/
            margin-top: 4vw;
            width: 70%;
            margin-left: 15%;
        }

        .covers_btn1 {
            margin-top: 10vw;
        }

        .covers_btn2 {
            background: #4296EB;
        }
    </style>
    <div class="cover cover-tip" style="width: 100%; height: 100%; display: none; position: fixed; top: 0; left: 0;" onclick="$('.cover-tip').hide()"></div>
    <div class="covers2 cover-tip" style="display: none;">
        <div class="covers_quit" onclick="$('.cover-tip').hide()">
            <img src="/zhuanpan/img/pop_up_quit_icon.png" alt="pop_up_quit_icon">
        </div>
        <div class="covers_icon">
            <img src="/zhuanpan/img/task_draw_reward_gereward_img.png" alt="task_draw_reward_gereward_img">
        </div>
        <div class="covers_font">恭喜您获得<span id="jiangli">0金币</span>奖励</div>
        <div class="covers_btn covers_btn1" onclick="$('.cover-tip').hide()">继续抽奖</div>
        <a href="#rules_con_footer">
            <div class="covers_btn2" onclick="javascript:$('.cover-tip').hide();location.href='partners_manage.aspx';">邀请下级赚更多转盘机会</div>
        </a>
    </div>
</asp:Content>


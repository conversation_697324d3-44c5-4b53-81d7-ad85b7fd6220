using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

public partial class order_list : globalClass
{
    public DataTable userdt = new DataTable();
    protected void Page_Load(object sender, EventArgs e)
    {
        List<SqlParameter> pams = new List<SqlParameter>();
        dbClass db = new dbClass();
        pams.Add(new SqlParameter("@id", Request.QueryString["orderId"] + ""));
        userdt = db.getDataTable("select * from api_orderList with(nolock) where orderId=@id  ", pams.ToArray());


    }
}
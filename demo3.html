<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Scroll to Top and Bottom Button</title>
    <style>
        body {
            height: 2000px; /* for demonstration purposes */
            margin: 0;
            padding: 0;
        }

        .scroll-button {
            position: fixed;
            right: 20px;
            bottom: 20px;
            width: 50px;
            height: 50px;
            background-color: #007bff;
            color: #fff;
            display: flex;
            justify-content: center;
            align-items: center;
            border-radius: 50%;
            cursor: pointer;
            font-size: 24px;
            z-index: 1000;
            transition: background-color 0.3s;
        }

        .scroll-button:hover {
            background-color: #0056b3;
        }

        .scroll-button.bottom {
            bottom: 80px;
        }
    </style>
</head>
<body>
    <div class="scroll-button" id="scrollTopButton">↑</div>
    <div class="scroll-button bottom" id="scrollBottomButton">↓</div>
    <script>
        document.getElementById('scrollTopButton').onclick = function () {
            window.scrollTo({ top: 0, behavior: 'smooth' });
        };

        document.getElementById('scrollBottomButton').onclick = function () {
            window.scrollTo({ top: document.body.scrollHeight, behavior: 'smooth' });
        };
    </script>
</body>
</html>

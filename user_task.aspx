<%@ Page Title="" Language="C#" MasterPageFile="~/top.master" AutoEventWireup="true" CodeFile="user_task.aspx.cs" Inherits="order_list" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="Server">

    <script>
        $(function () {
            <%if (Request.QueryString["back"] + "" == "1")
              {
                  %>
            popTitle('<%=Request.QueryString["type"] + "" == "yrsw" ? "推荐有好礼" : Request.QueryString["type"] + "" == "usdt" ? "【USDT】买币大礼包" : "新手豪礼" %>', '');
            $('#xshl_top').hide();
            <%
              }%>
        })
    </script>

    <style>
        .top-title {
            background: #f2f2f2;
        }

        body {
            background: #f2f2f2;
        }

        .info_title {
            color: #000;
            font-size: 14px;
            font-weight: bold;
            display: flex;
            align-items: center;
            margin: 10px 0;
            margin-top: 18px;
        }

            .info_title i {
                display: inline-block;
                background: #2A3EE8;
                width: 6px;
                height: 6px;
                border-radius: 50%;
            }

        .main-container {
            padding: 0px;
        }








        /*公告*/
        .notice {
            width: 100%;
            margin: 6px auto;
            /*height: 26px;*/
            overflow: hidden;
            position: relative;
            padding: 0 6px;
            box-sizing: border-box;
        }

            .notice svg {
                position: absolute;
                height: 22px;
                width: 22px;
                top: 2px;
            }

        .noticTipTxt {
            color: #ff7300;
            height: 22px;
            line-height: 22px;
            overflow: hidden;
            margin: 0 0 0 30px;
            list-style: none;
            padding: 0;
        }

            .noticTipTxt li {
                height: 22px;
                line-height: 22px;
            }

            .noticTipTxt a {
                color: #222;
                font-size: 14px;
                text-decoration: none;
            }

                .noticTipTxt a:hover {
                    color: #ff7300;
                    text-decoration: underline;
                }


            .noticTipTxt.list_notice {
                height: 300px;
                line-height: 300px;
            }

                .noticTipTxt.list_notice li {
                    height: 30px;
                    line-height: 30px;
                }

                .noticTipTxt.list_notice a {
                    color: #000;
                    font-size: 12px;
                }


        /*进度条*/


        .task-progress .progress-bar {
            background: #ddd;
            height: 10px;
            border-radius: 8px;
            width: 100%;
            overflow: hidden;
        }

            .task-progress .progress-bar > div {
                background: rgb(62 197 133);
                height: 10px;
                width: 0%;
                border-radius: 8px;
            }

        .task-progress .progress-lab {
            color: gray;
            font-size: 12px;
            margin-left: 10px;
            letter-spacing: 1px;
        }
    </style>


    <link href="../slayer/layer.css" rel="stylesheet" />
    <script src="../slayer/layer.js"></script>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="Server">

    <%if (Request.QueryString["type"] + "" == "xrhl" || Request.QueryString["type"] + "" == "usdt")
      {
    %>


    <div style="background: #fff; text-align: center; padding: 4px 0; border-bottom-left-radius: 16px; border-bottom-right-radius: 16px; position: relative;" id="xshl_top">
        <h2 style="text-align: center; color: #333; margin: 0; font-size: 18px; margin: 12px 0; display: inline-block; font-weight: 500;"><b><%=Request.QueryString["type"] + "" == "yrsw" ? "月入十万" : Request.QueryString["type"] + "" == "usdt" ? "【USDT】买币大礼包" : "新手豪礼" %></b></h2>
    </div>

    <div style="padding: 18px;">

        <img src="/static/images/xshl.png" alt="" style="width: 100%; cursor: pointer;" />



        <div class="notice">

            <svg t="1692210433046" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="30657" width="22" height="22">
                <path d="M511.1 870.3c-8.3 0-16.6-2.9-23.2-8.5L293.8 698.5H187.6c-19.9 0-36-16.1-36-36V363.6c0-19.9 16.1-36 36-36h106.2l194.1-163.3c10.7-9 25.7-11 38.4-5.1s20.8 18.7 20.8 32.7v642.5c0 14-8.1 26.8-20.8 32.7-4.9 2.1-10.1 3.2-15.2 3.2zM223.6 626.4h83.3c8.5 0 16.7 3 23.2 8.5L475 756.8V269.2L330.1 391.1c-6.5 5.5-14.7 8.5-23.2 8.5h-83.3v226.8z" p-id="30658"></path><path d="M675.4 778c-15.1 0-29.1-9.5-34.2-24.6-6.3-18.9 3.9-39.3 22.8-45.6 83.9-28 140.3-106.2 140.3-194.7 0-88.5-56.3-166.7-140.2-194.7-18.9-6.3-29.1-26.7-22.8-45.6 6.3-18.9 26.7-29.1 45.6-22.8 54.7 18.3 101.7 52.6 135.9 99.4 35 47.7 53.4 104.3 53.4 163.7 0 59.4-18.5 116-53.5 163.7-34.3 46.8-81.3 81.1-136 99.4-3.7 1.2-7.5 1.8-11.3 1.8z" fill="#4195F9" p-id="30659"></path><path d="M640.7 664.5c-11.6 0-23.1-5.6-30-16-11.1-16.5-6.6-38.9 10-50 28.7-19.1 45.8-51.1 45.8-85.5s-17.1-66.4-45.8-85.5c-16.6-11.1-21-33.4-10-50 11.1-16.5 33.4-21 50-10 48.7 32.5 77.8 86.9 77.8 145.4s-29.1 112.9-77.8 145.4c-6.1 4.3-13.1 6.2-20 6.2z" fill="#4195F9" p-id="30660"></path></svg>


            <ul id="jsfoot02" class="noticTipTxt">
                <%=String.Join("\r\n", msgs) %>
            </ul>

        </div>

    </div>

    <script src="../static/js/scrolltext.js"></script>
    <script type="text/javascript">
        var scrollup = new ScrollText("jsfoot02");
        scrollup.LineHeight = 22;        //单排文字滚动的高度
        scrollup.Amount = 1;            //注意:子模块(LineHeight)一定要能整除Amount.
        scrollup.Delay = 1;           //延时
        scrollup.Timeout = 3000;
        scrollup.Start();             //文字自动滚动
        scrollup.Direction = "up";   //默认设置为文字向上滚动

    </script>

    <%
      }
      else
      {
          
    %>


    <div style="padding: 18px; font-size: 13px; position: relative;">

        <div style="padding: 0 10px;">
            <img src="/static/images/invite_users.png" alt="" style="width: 100%; cursor: pointer;" onclick="javascript:location.href='partners_manage.aspx?top=1'">
        </div>


    </div>


    <%
          
      } %>




    <div style="padding: 0 18px;">
        <div style="background: #fff; padding: 15px 10px; border-radius: 8px;">

            <div style="display: flex; margin-bottom: 18px; font-weight: bold;">
                <svg t="1699552672837" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="27961" width="22" height="22">
                    <path d="M324.267 742.4c-48.356 0-85.334-36.978-85.334-85.333s36.978-85.334 85.334-85.334 85.333 36.978 85.333 85.334-36.978 85.333-85.333 85.333z m0-113.778c-17.067 0-28.445 11.378-28.445 28.445s11.378 28.444 28.445 28.444 28.444-11.378 28.444-28.444-11.378-28.445-28.444-28.445z m-11.378-102.4L238.933 460.8c-11.377-11.378-14.222-28.444-2.844-39.822 11.378-11.378 28.444-14.222 39.822-2.845l36.978 31.29 79.644-76.8c11.378-11.379 28.445-11.379 39.823 0 11.377 11.377 11.377 28.444 0 39.821L312.889 526.222z" fill="#2488FF" p-id="27962"></path><path d="M753.778 500.622H477.867c-17.067 0-28.445-11.378-28.445-28.444s11.378-28.445 28.445-28.445h275.91c17.067 0 28.445 11.378 28.445 28.445S768 500.622 753.778 500.622z m0 184.89H477.867c-17.067 0-28.445-11.379-28.445-28.445s11.378-28.445 28.445-28.445h275.91c17.067 0 28.445 11.378 28.445 28.445S768 685.51 753.778 685.51z" fill="#5CB6FF" p-id="27963"></path><path d="M139.378 898.844h739.555v85.334H139.378z" fill="#005CFF" p-id="27964"></path><path d="M787.911 1024H236.09c-82.489 0-150.756-68.267-150.756-150.756v-665.6c0-82.488 68.267-150.755 150.756-150.755H787.91c82.489 0 150.756 68.267 150.756 150.755v665.6c0 82.49-68.267 150.756-150.756 150.756zM236.09 113.778c-51.2 0-93.867 42.666-93.867 93.866v665.6c0 51.2 42.667 93.867 93.867 93.867H787.91c51.2 0 93.867-42.667 93.867-93.867v-665.6c0-51.2-42.667-93.866-93.867-93.866H236.09z" fill="#424242" p-id="27965"></path><path d="M406.756 199.111c-22.756 0-39.823-17.067-39.823-39.822V68.267c0-22.756 17.067-39.823 39.823-39.823 22.755 0 39.822 17.067 39.822 39.823v91.022c0 22.755-19.911 39.822-39.822 39.822z" fill="#D9EBF4" p-id="27966"></path><path d="M406.756 227.556c-39.823 0-71.112-31.29-71.112-68.267V68.267C335.644 31.289 366.934 0 406.756 0s68.266 31.289 68.266 68.267v91.022c0 36.978-31.289 68.267-68.266 68.267z m0-170.667c-5.69 0-11.378 5.689-11.378 11.378v91.022c-2.845 5.689 2.844 11.378 11.378 11.378 5.688 0 11.377-5.69 11.377-11.378V68.267c0-5.69-5.689-11.378-11.377-11.378z" fill="#2488FF" p-id="27967"></path><path d="M617.244 199.111c-22.755 0-39.822-17.067-39.822-39.822V68.267c0-22.756 19.911-39.823 39.822-39.823 22.756 0 39.823 17.067 39.823 39.823v91.022c2.844 22.755-17.067 39.822-39.823 39.822z" fill="#D9EBF4" p-id="27968"></path><path d="M617.244 227.556c-36.977 0-68.266-31.29-68.266-68.267V68.267C548.978 31.289 580.267 0 617.244 0s68.267 31.289 68.267 68.267v91.022c2.845 36.978-28.444 68.267-68.267 68.267z m0-170.667c-5.688 0-11.377 5.689-11.377 11.378v91.022c0 5.689 5.689 11.378 11.377 11.378s11.378-5.69 11.378-11.378V68.267c2.845-5.69-2.844-11.378-11.378-11.378z" fill="#2488FF" p-id="27969"></path></svg>&nbsp;任务列表
            </div>


            <div>


                <asp:Repeater ID="taskList" runat="server">
                    <ItemTemplate>


                        <div style="border: 1px solid #eee; border-radius: 8px; padding: 10px;  margin-bottom: 10px;display:<%#(Eval("show_users") + "" == "" || (Eval("show_users") + "").IndexOf(pmlist["usertype"] + "") != -1) ? "flex" : "none" %>;">

                            <div>

                                <div style="background: #910909; width: 80px; height: 100px; border-radius: 8px; text-align: center; font-size: 16px; color: #000; padding-top: 10px; box-sizing: border-box;">
                                    <div style="color: #ffdb54; font-weight: bold; margin-bottom: 3px; font-size: 26px;">
                                        <%#Convert.ToDouble(Eval("reward_amount")).ToString("0") %>
                                    </div>

                                    <img src="/static/images/coin.png" alt="" height="45" width="45" style="">
                                </div>

                            </div>

                            <div style="font-weight: bold; font-size: 13px; padding-left: 18px; line-height: 25px; width: 100%;">

                                <div>
                                    <%#Eval("name") %>
                                </div>

                                <div>
                                    <%#Eval("desctext") %>
                                </div>

                                <div style="font-weight: 100; display: flex; color: gray; font-size: 12px;">
                                    <span>完成度</span>
                                    <span style="margin-left: auto;"><%#calcNumber(Eval("times")+"",Eval("active_type")+"",Eval("a_number")+"",Eval("b_number")+"") %>/<%#Eval("a_number") %></span>
                                </div>



                                <div>

                                    <div class="task-progress">
                                        <div class="progress-bar">
                                            <div style="width: <%#Convert.ToDouble(calcNumber(Eval("times") + "", Eval("active_type") + "", Eval("a_number") + "", Eval("b_number") + "")) * 100 / Convert.ToDouble(Eval("a_number") + "") %>%;"></div>
                                        </div>
                                    </div>

                                </div>

                                <div style="margin-top: 10px; text-align: right;" actionid="<%#Eval("id") %>">

                                    <%#Convert.ToDouble(calcNumber(Eval("times") + "", Eval("active_type") + "", Eval("a_number") + "", Eval("b_number") + "")) >= Convert.ToDouble(Eval("a_number") + "") ?(selectDateTable(recList, "taskid=" + Eval("id")).Rows.Count > 0 ? "<a style=\"color: #aaa; font-weight: bold; padding: 3px 15px; border-radius: 50px; font-size: 15px; cursor: pointer;\">"+(selectDateTable(recList, "taskid=" + Eval("id")).Rows[0]["audit_time"]+"" == "" ?"联系您的上级为您通过审核":"已领取")+"</a>" : "<a style=\"background: #f32121; color: #fff; font-weight: bold; padding: 3px 15px; border-radius: 50px; font-size: 15px;cursor:pointer;\" onclick=\"task_receive(" + Eval("id") + ")\">立即领取</a>" ):  "<a style=\"background: #eee; color: #aaa; font-weight: bold; padding: 3px 15px; border-radius: 50px; font-size: 15px; cursor: pointer;\">立即领取</a>" %>
                                </div>

                            </div>

                        </div>


                    </ItemTemplate>
                </asp:Repeater>


            </div>


        </div>



    </div>


    <script>
        var task_receive = function (id) {
            var t = 1;
            layer.open({
                type: 2,
                content: '奖励领取中',
                time: t,
                shadeClose: false
            })

            setTimeout(function () {

                v3api("task_receive", {
                    data: {
                        id: id
                    }
                }, function (e) {
                    tp(e.msg);
                    $('[actionid="' + id + '"] a').css("background", "none").css("color", "#aaa").text("刷新查看领取结果");
                })


            }, t * 1000);
        }
    </script>



</asp:Content>


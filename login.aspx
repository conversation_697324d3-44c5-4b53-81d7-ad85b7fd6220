<%@ Page Title="" Language="C#" MasterPageFile="~/top.master" AutoEventWireup="true" CodeFile="login.aspx.cs" Inherits="login" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="Server">
    <style>
        .main-container {
            padding: 0px;
        }

        body {
            background: #fff;
        }

        .ulk_menu,#index_menu {
            display: none!important;
        }
    </style>
    <script>
        $(function () {
            $('#index_menu').hide();
        })
    </script>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="Server">

    <div style="background: linear-gradient(to right bottom,#E7EAFF,#F8F1FF,#fff 70%); font-size: 52px; font-weight: bold; padding: 0 30px; padding-top: 108px; margin-bottom: 35px; text-shadow: 1px 0 0 #2A3DE8, -1px 0 0 #2A3DE8, 0 1px 0 #2A3DE8, 0 -1px 0 #2A3DE8;">
        <div style="color: #2A3DE8; text-shadow: none;">任务中心</div>
        <div style="font-size: 28px; text-shadow: none; color: #2A3DE8;">
            【登陆领支付宝红包】
        </div>
        <%--<div style="color: #2A3DE8;">中心</div>--%>
    </div>

    <div style="display: flex; font-size: 17px; padding: 18px 38px;">

        <%--<div style="color: #000; font-weight: bold; display: flex; width: 100px; align-item: center;" onclick="javascript:openButtomPage($('.flag-select').html(),{miss_close:true});">
            <span id="flagid">+86</span>
           
            <div style="margin-left: auto;">
                <svg t="1692541585388" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="30397" width="10" height="10">
                    <path d="M573.056 752l308.8-404.608A76.8 76.8 0 0 0 820.736 224H203.232a76.8 76.8 0 0 0-61.056 123.392l308.8 404.608a76.8 76.8 0 0 0 122.08 0z" fill="#2F41E8" p-id="30398"></path></svg>
            </div>
        </div>--%>

        <input style="width: 100%; border: 0; outline: none; font-size: 17px; font-weight: 500;" placeholder="请输入手机号" id="phone">
    </div>

    <div style="padding: 18px 38px;">
        <input style="width: 100%; border: 0; outline: none; font-size: 17px; font-weight: 500;" placeholder="请输入密码" id="password" type="password">


        <div style="display: flex;">
            <div style="margin-left: auto; display: flex; flex-direction: column; align-items: center;">

                <div style="margin-left: auto; margin-top: 20px; margin-bottom: 30px;"><a style="color: #525FC9; font-size: 12px; cursor: pointer;" href="forget_password.aspx">忘记密码?</a></div>









                <div style="display: flex; align-items: end; justify-content: end;">

                    <%--<div style="margin-right: 18px; cursor: pointer; display: none;" id="tochat">

                        <div style="position: relative;">
                            <img src="<%=pmlist["group_avatar"] %>" width="50" height="50">
                            <span style="position: absolute; bottom: 0; left: 0; width: 28px; background: #fdfd46; color: #000; font-weight: 500; display: inline-block; text-align: center; font-size: 12px; padding: 2px 3px; border-top-right-radius: 8px;">群聊</span>
                            <div class="unread_number" style="background: red; color: rgb(255, 255, 255); width: 18px; height: 18px; line-height: 18px; position: absolute; border-radius: 50%; text-align: center; top: -6px; right: -6px; font-size: 12px; display: none;">0</div>
                        </div>
                        <div><span style="color: gray; font-size: 14px;">领取返现</span></div>
                    </div>--%>

                    <%--<div style="margin-right: 18px;">
                        <div class="video-icon" onclick="javascript:location.href='../app1/maiya.html'" style="background-image: url(/static/images/unnamed.png); width: 50px; height: 50px; margin: auto; margin-bottom: 2px;"></div>
                        <div><span style="color: gray; font-size: 14px;">麦芽APP</span></div>
                    </div>--%>

                    <%--<a href="<%=uConfig.stcdata("kf_online") %>" target="_blank" style="text-decoration: none;">
                        <svg t="1692542430519" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="31655" width="52" height="52">
                            <path d="M515.6 770.2c48.4 0 91.3-31.3 106.2-77.4 1-3.1-0.4-6.4-3.4-7.8-3-1.3-6.5-0.2-8.1 2.6-0.2 0.3-17.2 27.8-83.7 36.8-9.5 1.3-19.2 2-28.8 2-46.5 0-67.3-17.6-67.5-17.7-2.4-2.1-5.9-2.1-8.4-0.1-2.4 2-3 5.6-1.3 8.3 20.4 32.8 56.7 53.2 95 53.3z" fill="#2A3EE8" p-id="31656"></path><path d="M808.3 398.9c-45.7-123.7-164-212.1-303.2-212.1-138.6 0-256.6 87.6-302.6 210.5-2.4-2.7-4.9-5.3-7.6-7.6C221 239.9 349.9 126 505.3 126c154.7 0 283.2 112.8 310.2 261.5-2.8 3.6-5.2 7.4-7.2 11.4z m64.6-40.5c-0.6 0-1.1 0.1-1.7 0.1C832.3 190 683.5 64.3 505.2 64.3 322 64.3 169.8 197 136.1 372.5c-33.5 6.2-57.9 35.5-57.7 69.6v139.5c0 39.1 31.4 70.8 70.1 70.8 21.8 0 41.1-10.3 53.9-26.1C233.4 708 296 774 376.2 809c1-2 2.2-3.9 3.5-5.7 1.3-1.6 2.7-3 3.9-3 1.2 0 2.4 0.4 3.4 1.1-18.5-13.8-85.2-84.4-99.7-183.1-6.3-43.4 26.2-86.1 64.1-93.1 60.8-11.3 121.3-24.2 182.1-35.3 38.7-7 65.1-28.3 81.2-63.6 3.8-8.3 9.3-25 11.8-49 0.6-3.6 3.7-6.3 7.4-6.3 2.4 0 4.6 1.2 6 3.1l1.7-1c24 34.8 71.5 111.9 78.3 193.1 7.8 92.9 3.5 156.5-67.6 221.6l-0.3 0.3c-1 1.1-1.6 2.5-1.6 4 0 1.9 1 3.7 2.6 4.7 0.6 0.2 1.2 0.6 1.8 0.8 0.5 0.1 0.9 0.2 1.4 0.3 0.5 0 0.9-0.1 1.3-0.3 1-0.5 2-1.1 3-1.7 72.6-40.1 127.2-106.4 152.5-185.4a72.29 72.29 0 0 0 45.2 30.2c-30 136.9-152.2 222.7-303.5 235.6-9.6-23.4-32.4-38.6-57.6-38.5-34.2 0-62 27.1-62 60.5s27.8 60.5 62 60.5c26.6 0.1 50.3-16.9 58.7-42.1 175.1-14.2 315.5-118.3 344.8-280 26.7-11 44.1-37 44.1-65.9V429.9c0.2-39.5-32-71.5-71.8-71.5z" fill="#2A3EE8" p-id="31657"></path></svg>
                        <div><span style="color: gray; font-size: 14px;">在线客服</span></div>

                    </a>--%>



                </div>


            </div>
        </div>


        <a style="display: block; background: #3838f5; color: #fff; width: 100%; padding: 16px; box-sizing: border-box; border-radius: 10px; cursor: pointer; text-align: center; font-size: 14px; margin-top: 60px;" onclick="account_login()">登录</a>

        <div style="text-align: center; margin-top: 30px;">
            <a style="color: gray; font-size: 13px; text-decoration: none;" href="register.aspx?code=<%=Request.QueryString["code"] %>">还没有账号，去注册</a>
        </div>
    </div>


    <style>
        .flag_list {
            margin-top: 20px;
            display: flex;
            align-item: center;
            font-weight: bold;
            margin-bottom: 30px;
        }

            .flag_list svg {
                height: 20px;
                width: 30px;
            }
    </style>
    <div class="demo-popup-page flag-select" style="display: none;">
        <div style="padding: 18px; font-size: 13px;">

            <h4 style="color: #3C54D1; margin-bottom: 22px; font-size: 16px; display: flex;">选择国家
            </h4>


            <div class="flag_list" onclick="set_flag('+1')">
                <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 7410 3900">
                    <path fill="#b22234" d="M0 0h7410v3900H0z" />
                    <path d="M0 450h7410m0 600H0m0 600h7410m0 600H0m0 600h7410m0 600H0" stroke="#fff" stroke-width="300" />
                    <path fill="#3c3b6e" d="M0 0h2964v2100H0z" />
                    <g fill="#fff">
                        <g id="d">
                            <g id="c">
                                <g id="e">
                                    <g id="b">
                                        <path id="Path1" d="M247 90l70.534 217.082-184.66-134.164h228.253L176.466 307.082z" />
                                        <use xlink:href="#a" y="420" />
                                        <use xlink:href="#a" y="840" />
                                        <use xlink:href="#a" y="1260" />
                                    </g>
                                    <use xlink:href="#a" y="1680" />
                                </g>
                                <use xlink:href="#b" x="247" y="210" />
                            </g>
                            <use xlink:href="#c" x="494" />
                        </g>
                        <use xlink:href="#d" x="988" />
                        <use xlink:href="#c" x="1976" />
                        <use xlink:href="#e" x="2470" />
                    </g></svg>&nbsp;&nbsp;&nbsp;+1（美国）

           
            </div>
            <div class="flag_list" onclick="set_flag('+86')">
                <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 30 20">
                    <defs>
                        <path id="a" d="M0-1L.588.809-.952-.309H.952L-.588.809z" fill="#FF0" />
                    </defs><path fill="#EE1C25" d="M0 0h30v20H0z" /><use xlink:href="#a" transform="matrix(3 0 0 3 5 5)" /><use xlink:href="#a" transform="rotate(23.036 .093 25.536)" /><use xlink:href="#a" transform="rotate(45.87 1.273 16.18)" /><use xlink:href="#a" transform="rotate(69.945 .996 12.078)" /><use xlink:href="#a" transform="rotate(20.66 -19.689 31.932)" /></svg>&nbsp;&nbsp;&nbsp;+86（中国）

           
            </div>

        </div>
    </div>


    <script>
        var set_flag = function (id) {
            $('#flagid').html(id);
            closePopup();
        }

        var account_login = function () {
            v3api("user/login", {
                data: { phone: $('#phone').val(), password: $('#password').val() }
            }, function (json) {
                location.href = 'index.aspx';
            })
        }



        // vcode区域
        var vcode = get_param('code');
        if (vcode != "") {
            localStorage.setItem("vcode", vcode);
        }

        vcode = localStorage.getItem("vcode");
        if (vcode != null && vcode != "") {
            $('#tochat').show();
            $('#tochat').on('click', function () {
                location.href = 'chat.aspx#chat-' + vcode;
            });
        }
    </script>
</asp:Content>


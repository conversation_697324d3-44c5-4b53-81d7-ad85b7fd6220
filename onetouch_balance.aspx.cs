using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

public partial class notice_details : baseClass
{
    public DataTable userdt = new DataTable();
    public Dictionary<string, object> pmlist = new Dictionary<string, object>();
    protected void Page_Load(object sender, EventArgs e)
    {
        List<SqlParameter> pams = new List<SqlParameter>();
        dbClass db = new dbClass();
        DataTable dt = new DataTable();

        pams.Add(new SqlParameter("@userid", uConfig.p_uid));

        string sql = string.Empty;
        sql = @" 
select * from accounts with(nolock) where id=@userid


-- 查询当前额度
select isnull(sum(amount),0) as total_amount from task_onetouch where userid=@userid  and (state=0 or state=100) 

";

     

        DataSet ds = new DataSet();
        ds = db.getDataSet(sql, pams.ToArray());
        userdt = ds.Tables[0];


        dt = ds.Tables[1];
        pmlist["used_amount"] = "0";
        if (dt.Rows.Count > 0)
        {
            pmlist["used_amount"] = dt.Rows[0]["total_amount"] + "";
        }


    }
}
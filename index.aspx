<%@ Page Title="" Language="C#" MasterPageFile="~/top.master" AutoEventWireup="true" CodeFile="index.aspx.cs" Inherits="index" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="Server">
        <!-- Elegant Line Icons CSS -->
    <link rel="stylesheet" href="../static/css/elegant-line-icons.css">
    <!-- Bootstrap CSS -->
    <link rel="stylesheet" href="../static/css/bootstrap.min.css">

    <!-- Responsive CSS -->
    <link rel="stylesheet" href="../static/css/index_all.css">

    <!-- jQuery Lib -->
    <script src="../static/js/jquery.min.js"></script>
    <!-- js -->
    <!-- Wow JS -->
    <script src="../static/js//wow.min.js"></script>

</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="Server">
    <input id="indexpage" type="hidden" value="1" />
    <!-- Demo styles -->
    <style>
        .swiper {
            width: 100%;
            height: 100%;
            margin-top: 10px;
            border-radius: 8px;
        }

        .swiper-slide {
            text-align: center;
            font-size: 18px;
            background: #fff;
            /* Center slide text vertically */
            display: -webkit-box;
            display: -ms-flexbox;
            display: -webkit-flex;
            display: flex;
            -webkit-box-pack: center;
            -ms-flex-pack: center;
            -webkit-justify-content: center;
            justify-content: center;
            -webkit-box-align: center;
            -ms-flex-align: center;
            -webkit-align-items: center;
            align-items: center;
            /*height: 120px;*/
            height: 100%;
        }

            .swiper-slide img {
                height: 100%;
            }

            .swiper-slide img {
                display: block;
                width: 100%;
                height: 100%;
                object-fit: cover;
            }
    </style>

    <!-- Swiper -->
    <div class="swiper mySwiper">
        <div class="swiper-wrapper">
            <asp:Repeater runat="server" ID="banner_list">
                <ItemTemplate>

                    <div class="swiper-slide" <%#Eval("linkurl") + "" != "" ? "onclick=\"javascript:location.href='" + Eval("linkurl") + "'\"" : "" %>>
                        <img src="<%#Eval("imgurl") %>" />
                    </div>

                </ItemTemplate>
            </asp:Repeater>
            <%--<div class="swiper-slide">
                <img src="/static/images/lb2.jpg" />
            </div>--%>
        </div>
        <div class="swiper-pagination"></div>
    </div>

    <!-- Swiper JS -->
    <script src="static/js/swiper-bundle.min.js"></script>

    <!-- Initialize Swiper -->
    <script>
        var swiper = new Swiper(".mySwiper", {
            loop: true,
            autoplay: {
                delay: 2500,
                disableOnInteraction: false,
            },
            pagination: {
                el: ".swiper-pagination",
                dynamicBullets: true,
            },
        });
    </script>


    <!-- 首页嵌入代码 -->
    <%=uConfig.stcdata("index_code") %>



    <!-- 消息滚动 -->
    <style>
        /*公告*/
        .notice {
            width: 100%;
            margin: 20px auto;
            /*height: 26px;*/
            overflow: hidden;
            position: relative;
            padding: 0 6px;
            box-sizing: border-box;
        }

            .notice svg {
                position: absolute;
                height: 22px;
                width: 22px;
                top: 2px;
            }

        .noticTipTxt {
            color: #ff7300;
            height: 22px;
            line-height: 22px;
            overflow: hidden;
            margin: 0 0 0 30px;
            list-style: none;
            padding: 0;
        }

            .noticTipTxt li {
                height: 22px;
                line-height: 22px;
            }

            .noticTipTxt a {
                color: #222;
                font-size: 14px;
                text-decoration: none;
            }

                .noticTipTxt a:hover {
                    color: #ff7300;
                    text-decoration: underline;
                }


            .noticTipTxt.list_notice {
                height: 300px;
                line-height: 300px;
            }

                .noticTipTxt.list_notice li {
                    height: 30px;
                    line-height: 30px;
                }

                .noticTipTxt.list_notice a {
                    color: #000;
                    font-size: 12px;
                }

                #header_icons svg path{
                    fill:currentColor;
                }
    </style>


    <div style="background: #fff; padding: 15px 10px; border-radius: 8px; display: flex; margin-bottom: 10px; flex-wrap: wrap;" id="header_icons">
    
    <div style="width: 25%; text-align: center; color: #2a44a1; font-size: 13px; font-weight: bold;">
        <a href="message_details.aspx?id=<%=uConfig.gd(selectDateTable(msgList," name='关于我们' "), "id") %>" style="display: inline-block; color: #2a44a1; text-decoration: none; outline: none;">
            <svg t="" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="1825" width="38" height="38"><path d="M511.3 64.9c-247.4 0-448 200.6-448 448s200.6 448 448 448 448-200.6 448-448-200.5-448-448-448z m0 806.4c-197.6 0-358.4-160.8-358.4-358.4s160.8-358.4 358.4-358.4 358.4 160.8 358.4 358.4S709 871.3 511.3 871.3z" p-id="1826"></path><path d="M522.5 423.3c-24.7 0-44.8 20.1-44.8 44.8v224c0 24.7 20.1 44.8 44.8 44.8 24.7 0 44.8-20.1 44.8-44.8v-224c0-24.7-20-44.8-44.8-44.8zM522.5 288.9c-24.7 0-44.8 20.1-44.8 44.8 0 24.7 20.1 44.8 44.8 44.8 24.7 0 44.8-20.1 44.8-44.8 0-24.7-20-44.8-44.8-44.8z" p-id="1827"></path></svg>
            
            <div>关于我们</div>
        </a>
    </div>
    <div style="width: 25%; text-align: center; color: #2a44a1; font-size: 13px; font-weight: bold;">
        
        <a href="message_details.aspx?id=<%=uConfig.gd(selectDateTable(msgList," name='牌照信息' "), "id") %>" style="display: inline-block; color: #2a44a1; text-decoration: none; outline: none;">
       <svg t="" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2132" width="38" height="38"><path d="M854.8 270.5H739.3l-174.4-96.9c0.8-3.7 1.2-7.4 1.2-11.3 0-29.9-24.2-54.1-54.1-54.1-29.9 0-54.1 24.2-54.1 54.1 0 3.9 0.5 7.7 1.2 11.4l-174.4 96.9H169.2c-39.9 0-72.2 32.3-72.2 72.2v469.1c0 39.9 32.3 72.2 72.2 72.2h685.7c39.9 0 72.2-32.3 72.2-72.2V342.7c-0.1-39.8-32.4-72.2-72.3-72.2z m-376.5-66.2c9.3 7.5 20.9 12.1 33.7 12.1 12.8 0 24.5-4.7 33.8-12.1L665 270.5H359l119.3-66.2z m376.5 607.5H169.2V342.7h685.7v469.1z" p-id="2133"></path><path d="M407.1 643.3h89.1v75c0 11.8 9.6 21.4 21.4 21.4h6.8c11.8 0 21.4-9.6 21.4-21.4v-75H635c11.8 0 21.4-9.6 21.4-21.4v-5.2c0-11.8-9.6-21.4-21.4-21.4h-89.1v-35.5h52.5c11.8 0 21.4-9.6 21.4-21.4v-5.7c0-8.9-5.5-16.5-13.2-19.7l12.3-69.5c2.3-13.2-6.5-25.7-19.7-28-1.4-0.3-2.8-0.4-4.2-0.4-11.5 0-21.8 8.3-23.9 20l-12.6 71.5c-0.3 1.6-0.4 3.2-0.4 4.7h-67.4c0-1.6-0.1-3.3-0.4-4.9l-12.8-71.1c-2.1-11.7-12.5-19.9-24.2-19.9-1.4 0-2.9 0.1-4.3 0.4-13.4 2.3-22.3 14.8-19.9 27.9l12.3 68.1c-10.6 1.3-19 9.9-19 20.9v5.7c0 11.8 9.6 21.4 21.4 21.4h52.5v35.5h-89.1c-11.8 0-21.4 9.6-21.4 21.4v5.2c-0.1 11.8 9.5 21.4 21.3 21.4z" p-id="2134"></path></svg>
            <div>牌照信息</div>
        </a>
    </div>

    <div style="width: 25%; text-align: center; color: #2a44a1; font-size: 13px; font-weight: bold;">
        <a href="message_details.aspx?id=<%=uConfig.gd(selectDateTable(msgList," name='隐私政策' "), "id") %>" style="display: inline-block; color: #2a44a1; text-decoration: none; outline: none;">
            <svg t="" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2391" width="38" height="38"><path d="M766.7 98.2H255.6c-70.6 0-127.8 57.1-127.8 127.5V608.4c0 70.4 57.2 127.5 127.8 127.5h132.1l102 177c11.8 20.5 31.1 20.5 42.9 0l102-177h132.1c70.6 0 127.8-57.1 127.8-127.5V225.8c-0.1-70.5-57.3-127.6-127.8-127.6z m85.2 219.3v290.9c0 47-38.1 85-85.2 85H609.5l-24.6 42.5-73.8 127.5-73.8-127.5-24.6-42.5H255.6c-47 0-85.2-38.1-85.2-85V225.8c0-46.9 38.1-85 85.2-85h511.1c47 0 85.2 38.1 85.2 85v91.7z m-340.7 57.4c-23.5 0-42.6 19.2-42.6 42.8 0 23.7 19.1 42.8 42.6 42.8s42.6-19.2 42.6-42.8c-0.1-23.6-19.1-42.8-42.6-42.8z m-170.4 0c-23.5 0-42.6 19.2-42.6 42.8 0 23.7 19.1 42.8 42.6 42.8s42.6-19.2 42.6-42.8c0-23.6-19.1-42.8-42.6-42.8z m340.7 0c-23.5 0-42.6 19.2-42.6 42.8 0 23.7 19.1 42.8 42.6 42.8s42.6-19.2 42.6-42.8c0-23.6-19.1-42.8-42.6-42.8z" p-id="2392"></path></svg>
            <div>隐私政策</div>
        </a>
    </div>
    <div style="width: 25%; text-align: center; color: #2a44a1; font-size: 13px; font-weight: bold;">
        <a href="message_details.aspx?id=<%=uConfig.gd(selectDateTable(msgList," name='返洗钱规则' "), "id") %>" style="display: inline-block; color: #2a44a1; text-decoration: none; outline: none;">
            <svg t="" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2697" width="38" height="38"><path d="M849.9 173.2c-51-51-117.8-76.5-184.6-76.5s-133.7 25.5-184.6 76.5L172.8 481c-102 102-102 267.3 0 369.3 51 51 117.8 76.5 184.6 76.5s133.7-25.5 184.6-76.5l307.7-307.7c102.1-102.1 102.1-267.4 0.2-369.4zM788.3 481L665.2 604.1 526.7 465.6c-8.5-8.5-19.6-12.7-30.8-12.7s-22.3 4.2-30.8 12.7c-17 17-17 44.6 0 61.6l138.5 138.5-123 123c-32.9 32.9-76.6 51-123.1 51s-90.2-18.1-123.1-51c-32.9-32.9-51-76.6-51-123.1s18.1-90.2 51-123.1l307.7-307.7c32.9-32.9 76.6-51 123.1-51s90.2 18.1 123.1 51 51 76.6 51 123.1-18.1 90.2-51 123.1z" p-id="2698"></path></svg>
            <div>防洗钱规则</div>
        </a>
    </div>





    <div style="width: 25%; text-align: center; color: #2a44a1; font-size: 13px; font-weight: bold; margin-top: 28px;">
        <a href="message_details.aspx?id=<%=uConfig.gd(selectDateTable(msgList," name='帮助中心' "), "id") %>" style="display: inline-block; color: #2a44a1; text-decoration: none; outline: none;">
            <svg t="" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2907" width="38" height="38"><path d="M335.1 400.2c-24.7 0-44.8 25.1-44.8 56s20.1 56 44.8 56c24.7 0 44.8-25.1 44.8-56s-20-56-44.8-56zM648.7 512.2c24.7 0 44.8-25.1 44.8-56s-20.1-56-44.8-56c-24.7 0-44.8 25.1-44.8 56 0 31 20.1 56 44.8 56z" p-id="2908"></path><path d="M954.8 445C922.3 229.6 736.9 64.2 512.3 64.2c-247.4 0-448 200.6-448 448s200.6 448 448 448 448-200.6 448-448m-448 358.4c-197.6 0-358.4-160.8-358.4-358.4 0-197.6 160.8-358.4 358.4-358.4 183.7 0 335.4 139 355.9 317.3-45.4 77.2-120.4 135.7-210 161.2-12-18.4-32.7-30.5-56.2-30.5H490c-37.1 0-67.2 30.1-67.2 67.2s30.1 67.2 67.2 67.2h112c9.7 0 18.8-2.1 27.1-5.8 84.1-13.3 160.4-49.3 221.8-101.6-48.6 140.5-181.8 241.8-338.6 241.8z" p-id="2909"></path></svg>
            <div>帮助中心</div>
        </a>
    </div>


    <div style="width: 25%; text-align: center; color: #2a44a1; font-size: 13px; font-weight: bold; margin-top: 28px;">
        <a href="message_details.aspx?id=<%=uConfig.gd(selectDateTable(msgList," name='基本保障' "), "id") %>" style="display: inline-block; color: #2a44a1; text-decoration: none; outline: none;">
            <svg t="" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="3166" width="38" height="38"><path d="M776.4 362.3h-37.7V249.1c0-83.3-67.6-150.9-150.9-150.9H248.2c-83.3 0-150.9 67.6-150.9 150.9v528.2c0 83.3 67.6 150.9 150.9 150.9h528.2c83.3 0 150.9-67.6 150.9-150.9V513.2c0-83.3-67.6-150.9-150.9-150.9z m75.4 415c0 41.6-33.8 75.5-75.5 75.5H248.2c-41.6 0-75.5-33.8-75.5-75.5V249.1c0-41.6 33.8-75.5 75.5-75.5h339.5c41.6 0 75.5 33.8 75.5 75.5v452.8h75.5V437.8h37.7c41.6 0 75.5 33.8 75.5 75.5v264z" p-id="3167"></path><path d="M342.5 400h150.9c20.8 0 37.7-16.9 37.7-37.7 0-20.8-16.9-37.7-37.7-37.7H342.5c-20.8 0-37.7 16.9-37.7 37.7 0 20.9 16.9 37.7 37.7 37.7zM550 645.3H285.9c-10.4 0-18.9 8.4-18.9 18.9s8.4 18.9 18.9 18.9H550c10.4 0 18.9-8.4 18.9-18.9s-8.5-18.9-18.9-18.9zM550 532.1H285.9c-10.4 0-18.9 8.4-18.9 18.9 0 10.4 8.4 18.9 18.9 18.9H550c10.4 0 18.9-8.4 18.9-18.9 0-10.5-8.5-18.9-18.9-18.9z" p-id="3168"></path></svg>
            <div>基本保障</div>
        </a>
    </div>


    <div style="width: 25%; text-align: center; color: #2a44a1; font-size: 13px; font-weight: bold; margin-top: 28px;">
        <a href="message_details.aspx?id=<%=uConfig.gd(selectDateTable(msgList," name='高级保障' "), "id") %>" style="display: inline-block; color: #2a44a1; text-decoration: none; outline: none;">
            <svg t="" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="3166" width="38" height="38"><path d="M776.4 362.3h-37.7V249.1c0-83.3-67.6-150.9-150.9-150.9H248.2c-83.3 0-150.9 67.6-150.9 150.9v528.2c0 83.3 67.6 150.9 150.9 150.9h528.2c83.3 0 150.9-67.6 150.9-150.9V513.2c0-83.3-67.6-150.9-150.9-150.9z m75.4 415c0 41.6-33.8 75.5-75.5 75.5H248.2c-41.6 0-75.5-33.8-75.5-75.5V249.1c0-41.6 33.8-75.5 75.5-75.5h339.5c41.6 0 75.5 33.8 75.5 75.5v452.8h75.5V437.8h37.7c41.6 0 75.5 33.8 75.5 75.5v264z" p-id="3167"></path><path d="M342.5 400h150.9c20.8 0 37.7-16.9 37.7-37.7 0-20.8-16.9-37.7-37.7-37.7H342.5c-20.8 0-37.7 16.9-37.7 37.7 0 20.9 16.9 37.7 37.7 37.7zM550 645.3H285.9c-10.4 0-18.9 8.4-18.9 18.9s8.4 18.9 18.9 18.9H550c10.4 0 18.9-8.4 18.9-18.9s-8.5-18.9-18.9-18.9zM550 532.1H285.9c-10.4 0-18.9 8.4-18.9 18.9 0 10.4 8.4 18.9 18.9 18.9H550c10.4 0 18.9-8.4 18.9-18.9 0-10.5-8.5-18.9-18.9-18.9z" p-id="3168"></path></svg>
            <div>高级保障</div>
        </a>
    </div>


    <div style="width: 25%; text-align: center; color: #2a44a1; font-size: 13px; font-weight: bold; margin-top: 28px;">
        <a href="message_details.aspx?id=<%=uConfig.gd(selectDateTable(msgList," name='全方位保障' "), "id") %>" style="display: inline-block; color: #2a44a1; text-decoration: none; outline: none;">
            <svg t="" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="3166" width="38" height="38"><path d="M776.4 362.3h-37.7V249.1c0-83.3-67.6-150.9-150.9-150.9H248.2c-83.3 0-150.9 67.6-150.9 150.9v528.2c0 83.3 67.6 150.9 150.9 150.9h528.2c83.3 0 150.9-67.6 150.9-150.9V513.2c0-83.3-67.6-150.9-150.9-150.9z m75.4 415c0 41.6-33.8 75.5-75.5 75.5H248.2c-41.6 0-75.5-33.8-75.5-75.5V249.1c0-41.6 33.8-75.5 75.5-75.5h339.5c41.6 0 75.5 33.8 75.5 75.5v452.8h75.5V437.8h37.7c41.6 0 75.5 33.8 75.5 75.5v264z" p-id="3167"></path><path d="M342.5 400h150.9c20.8 0 37.7-16.9 37.7-37.7 0-20.8-16.9-37.7-37.7-37.7H342.5c-20.8 0-37.7 16.9-37.7 37.7 0 20.9 16.9 37.7 37.7 37.7zM550 645.3H285.9c-10.4 0-18.9 8.4-18.9 18.9s8.4 18.9 18.9 18.9H550c10.4 0 18.9-8.4 18.9-18.9s-8.5-18.9-18.9-18.9zM550 532.1H285.9c-10.4 0-18.9 8.4-18.9 18.9 0 10.4 8.4 18.9 18.9 18.9H550c10.4 0 18.9-8.4 18.9-18.9 0-10.5-8.5-18.9-18.9-18.9z" p-id="3168"></path></svg>
            <div>全方位保障</div>
        </a>
    </div>

</div>


    
    <section class="promo-section padding">
        <div class="container">
            <div class="row">
                <div class="col-md-4 col-sm-6 sm-padding">
                    <div class="promo-content text-center">
                        <i class="icon-shield icon"></i>
                        <h3>安全</h3>
                        <p>
                            多重保护技术保障您的数字资产安全
                            放心管理您的钱包.
                        </p>
                    </div>
                </div><!-- Promo-1 -->
                <div class="col-md-4 col-sm-6 sm-padding">
                    <div class="promo-content text-center">
                        <i class="icon-layers icon"></i>
                        <h3>省心</h3>
                        <p>
                            严格的风控和24小时人工服务
                            为您打造无忧OTC交易环境.
                        </p>
                    </div>
                </div><!-- Promo-1 -->
                <div class="col-md-4 col-sm-6 sm-padding">
                    <div class="promo-content text-center">
                        <i class="icon-bargraph icon"></i>
                        <h3>极速</h3>
                        <p>
                            全新模式与强大生态伙伴
                            为您带来未曾有过的交易速度.
                        </p>
                    </div>
                </div><!-- Promo-3 -->
            </div>
        </div>
    </section><!-- Promo Section -->

    <section class="content-section bg-grey padding">
        <div class="container">
            <div class="row">
                <div class="">
                    <div class="section-heading mb-40">
                        <h2>关于企鹅币</h2>
                        <p>使用企鹅币管理、世界通用的、恒定人民币汇率数字资产 </p>
                    </div>
                    <div class="content-lists">
                        <div class="list-item">
                            <i class="icon-tools"></i>
                            <div class="list-content">
                                <h3>恒定1:1人民币汇率</h3>
                                <p>与其他数字货币不同，企鹅币承诺永久无涨跌、也不加入任何第三方交易所。无论何时，都可以1:1在人民币和企鹅币之间自由兑换 </p>
                            </div>
                        </div>
                        <div class="list-item">
                            <i class="icon-pictures"></i>
                            <div class="list-content">
                                <h3>稳定、安全、100%兑换</h3>
                                <p>全球数十万人使用，秒级交易匹配。企鹅官方联合全球多个跨国集团共同保底，无需担心持有的企鹅币无法兑换</p>
                            </div>
                        </div>
                        <div class="list-item">
                            <i class="icon-genius"></i>
                            <div class="list-content">
                                <h3>全球通用、自由跨境</h3>
                                <p>企鹅币使用区块链技术具有高度匿名的特性，交易无迹可寻。世界范围内已有上千家大型商户支持企鹅币，全球支付无障碍</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section><!-- Content Section -->






<%--    <div class="scrollsidebar" id="scrollsidebar">
        <a class="item" href="order_new.aspx">
            <img src="../static/images/download.png" class="img-responsive">
        </a>
        <a class="item" href="online_service.aspx">
            <img src="../static/images/kefu.png" class="img-responsive">
        </a>
    </div>--%>

    <!-- Footer End -->
    <!-- Main JS -->
    <script>
        /* Axuta Theme Scripts */

        (function ($) {
            "use strict";

            $(window).on('load', function () {
                $('body').addClass('loaded');
            });



            /*=========================================================================
                WOW Active
            =========================================================================*/
            new WOW().init();


        })(jQuery);


        function download() {
            if (device.ios() || device.android()) {
                $('#lineModal').modal('show');
            } else {
                window.location.href = window.location.protocol + '//' + window.location.host + '/#download'
            }
        }

        /*
         * @Author: ecitlm
         * @Date: 2017-08-04 23:18:26
         * @Last Modified by:   ecitlm
         * @Last Modified time: 2017-08-04 23:18:26
         */

        !(function () {
            var serviceOnline = (function () {
                var sideContent = document.querySelector(".side_content");
                var timer = null;

                //悬浮QQ匀速移动
                var startMove = function (argument) {
                    var scrollsidebar = document.getElementById("scrollsidebar");
                    clearInterval(timer);
                    timer = setInterval(function () {
                        var speed = (argument - scrollsidebar.offsetTop) / 4;
                        speed = speed > 0 ? Math.ceil(speed) : Math.floor(speed);
                        if (argument == scrollsidebar.offsetTop) {
                            clearInterval(timer);
                        } else {
                            scrollsidebar.style.top = (scrollsidebar.offsetTop + speed + 20) + "px";
                        }
                    }, 20);
                };

                //鼠标移动
                var scrollMove = function () {
                    window.onscroll = window.onload = function () {
                        var scrollsidebar = document.getElementById("scrollsidebar");
                        var scrolltop = document.body.scrollTop || document.documentElement.scrollTop;
                        if (!scrolltop) {
                            scrolltop = 0;
                        }
                        if (!scrollsidebar.offsetHeight) {
                            scrollsidebar.offsetHeight = 0
                        }
                        startMove(
                            parseInt(
                                (document.documentElement.clientHeight -
                                    scrollsidebar.offsetHeight) / 2 + scrolltop
                            )
                        );
                    };
                };

                //返回出来的方法
                return {
                    init: function () {
                        scrollMove();
                    }
                };
            })();

            //初始化
            serviceOnline.init();
        })();
    </script>
    <div class="hide">
    </div>

    <!-- 顶部导航 -->
    <style>
        .icon_img {
            border-radius: 50%;
    padding: 5px;
    width: 28px;
    height: 28px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto;
    margin-bottom: 3px;
        }
    </style>
 



    <style>
        .list-box[data-v-19d7bc70] {
            width: 100%;
            border-radius: 10px;
            overflow: hidden;
            background: #fff;
            box-shadow: 0.06rem 0.06rem 0.24rem 0 rgb(240 129 1 / 38%);
            padding-bottom: 10px;
            margin-bottom: 20px;
            max-width: 200px;
            margin: 0 10px;
            cursor: pointer;
        }

        .list-box .list-con[data-v-19d7bc70] {
            display: -webkit-box;
            display: -webkit-flex;
            display: flex;
            -webkit-box-orient: horizontal;
            -webkit-box-direction: normal;
            -webkit-flex-direction: row;
            flex-direction: row;
            -webkit-box-pack: start;
            -webkit-justify-content: flex-start;
            justify-content: flex-start;
            padding: 10px 4%;
        }

        .list-box .list-con .con-left[data-v-19d7bc70] {
            width: 46px;
            height: 46px;
            margin-right: 4px;
        }

        .list-box .list-bottom[data-v-19d7bc70] {
            width: 60%;
            margin: 0 auto;
            line-height: 24px;
            font-size: 12px;
            color: #fff;
            background: #ee271f;
            text-align: center;
            border-radius: 20px;
        }
    </style>




    <script src="../static/js/scrolltext.js"></script>
    <script type="text/javascript">
        var scrollup = new ScrollText("jsfoot02");
        scrollup.LineHeight = 22;        //单排文字滚动的高度
        scrollup.Amount = 1;            //注意:子模块(LineHeight)一定要能整除Amount.
        scrollup.Delay = 1;           //延时
        scrollup.Timeout = 3000;
        scrollup.Start();             //文字自动滚动
        scrollup.Direction = "up";   //默认设置为文字向上滚动

        var scrollup_transaction = new ScrollText("transaction_notice");
        scrollup_transaction.LineHeight = 60;        //单排文字滚动的高度
        scrollup_transaction.Amount = 1;            //注意:子模块(LineHeight)一定要能整除Amount.
        scrollup_transaction.Delay = 0.1;           //延时
        scrollup_transaction.Timeout = 3000;
        scrollup_transaction.Start();             //文字自动滚动
        scrollup_transaction.Direction = "up";   //默认设置为文字向上滚动

    </script>




    <style>
        .pop-cpt {
            z-index: 999999999;
            position: relative;
        }

        .pop-cpt2 {
            display: flex;
            justify-content: center;
            align-items: center;
            position: fixed;
            left: 0;
            top: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,.7);
        }

        .pop-cpt a {
            text-decoration: none;
            color: inherit;
            display: block;
        }

            .pop-cpt a:hover {
                text-decoration: none;
            }

        .pop-cpt .pop-cpt-bd {
            line-height: 22px;
            width: 286px;
            padding: 0 5px;
            box-sizing: border-box;
            border-radius: 15px;
            background: #0082f3;
            font-size: 14px;
            color: #333;
            font-family: "微软雅黑";
            position: relative;
        }

        .pop-cpt .pop-cpt-close {
            display: inline-block;
            height: 36px;
            line-height: 36px;
            padding: 0 15px;
            color: #fff;
            position: absolute;
            top: 0;
            right: 0;
        }

        .pop-cpt .pop-cpt-tit {
            height: 36px;
            line-height: 36px;
            color: #fff;
            text-align: center;
            font-size: 18px;
            font-weight: bold;
        }

            .pop-cpt .pop-cpt-tit span {
                display: inline-block;
                padding: 0 10px;
                position: absolute;
                top: 0;
                right: 0;
            }

        .pop-cpt .pop-cpt-con {
            padding: 0 6px 3px 6px;
            border-radius: 15px;
            background: #fff;
        }

        .pop-cpt .pop-cpt-con1 {
            text-align: center;
            color: #000;
        }

        .pop-cpt .pop-cpt-con2 {
            padding-top: 10px;
        }

        .pop-cpt .pop-cpt-con2-tit {
            display: flex;
            justify-content: center;
            padding-bottom: 5px;
        }

            .pop-cpt .pop-cpt-con2-tit span {
                display: inline-block;
                width: 26px;
                line-height: 26px;
                margin-left: -3px;
                text-align: center;
                color: #FFF200;
                font-size: 16px;
                font-weight: bold;
                border-radius: 50%;
                background: #ED1B20;
            }

        .pop-cpt .pop-cpt-con2-logo {
            display: flex;
            justify-content: center;
            height: 60px;
        }

        .pop-cpt .pop-cpt-con2-logol {
            color: #fb2725;
            font-size: 54px;
            line-height: 50px;
            font-weight: bold;
            font-family: Tahoma;
        }

        .pop-cpt .pop-cpt-con2-logor {
            width: 70px;
            position: relative;
        }

        .pop-cpt .pop-cpt-con2-logor-txt1 {
        }

            .pop-cpt .pop-cpt-con2-logor-txt1 span {
                display: inline-block;
                width: 60px;
                height: 22px;
                line-height: 22px;
                text-align: center;
                color: #fff;
                border-radius: 5px;
                background: #0082f3;
                position: absolute;
                left: 8px;
                top: 2px;
            }

        .pop-cpt .pop-cpt-con2-logor-txt2 {
            line-height: 32px;
            color: #333;
            font-size: 30px;
            font-family: impact,arial;
            position: absolute;
            top: 24px;
            left: 0;
        }

        .pop-cpt .pop-cpt-con3 {
            padding: 0 2px;
        }

            .pop-cpt .pop-cpt-con3 table {
                font-weight: bold;
                border-collapse: collapse;
            }

            .pop-cpt .pop-cpt-con3 th {
                text-align: center;
                color: #fff;
                border: solid 1px #999;
                background: #0082f3;
                font-size: 18px;
                font-weight: 500;
                padding: 5px 0;
            }

            .pop-cpt .pop-cpt-con3 td {
                text-align: center;
                border: solid 1px #999;
                font-size: 16px;
                line-height: 22px;
                padding: 5px 0;
            }

        .pop-cpt .pop-cpt-con4 {
            text-align: center;
            margin-bottom: 10px;
        }

            .pop-cpt .pop-cpt-con4 span {
                display: inline-block;
                width: 200px;
                height: 30px;
                line-height: 30px;
                font-size: 20px;
                font-weight: bold;
                color: #ff0;
                border-radius: 10px;
                background: #fb2725;
            }

        .pop-cpt .pop-cpt-footer {
            padding: 10px 0;
            color: #fff;
            text-align: center;
        }

        .pop-cpt .pop-cpt-footer1 {
            font-size: 16px;
        }

        .pop-cpt .pop-cpt-footer2 {
            padding-top: 5px;
            font-weight: bold;
            color: #ff0;
        }
    </style>
</asp:Content>


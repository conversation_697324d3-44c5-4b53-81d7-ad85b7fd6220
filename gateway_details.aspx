<%@ Page Title="" Language="C#" MasterPageFile="~/top.master" AutoEventWireup="true" CodeFile="gateway_details.aspx.cs" Inherits="order_list" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="Server">
    <script>
        $(function () {
            popTitle('订单详情', '');
        })
    </script>

    <style>
        .top-title {
            background: none;
        }
    </style>
    <style>
        body {
            background: linear-gradient(45deg, #F0F4F9, #F5F5F8);
            background-repeat: no-repeat;
            height: 100vh;
        }

        .select_tab {
            color: #000;
            box-sizing: border-box;
            padding: 12px 20px;
            text-align: center;
            font-weight: bold;
            font-size: 13px;
            background: #eee;
            border-radius: 12px;
            width: 50%;
            margin: 0 10px;
        }

            .select_tab.activity {
                background: #3c3351;
                color: #fff;
                transition: all 0.2s;
            }

        .theme-green .back-bar .selected-bar {
            background-color: #3838f5!important;
            background-image: linear-gradient(to bottom, #4747ff, #3838f5)!important;
        }

        .slider-container {
            margin-bottom: 38px;
            margin-top: 52px;
        }

        
        @keyframes rotate360 {
            from {
                transform: rotate(0deg);
            }

            to {
                transform: rotate(360deg);
            }
        }
        /* 应用旋转动画到元素 */
        .rotate-element .icon {
            animation: rotate360 5s linear infinite; /* 旋转2秒，线性速度，无限循环 */
            margin-right: 5px;
        }
    </style>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="Server">



    <div id="pay_Success" style="display: none;">

        <div>


            <div class="box">


                <div class="body-content" style="text-align: center;">
                    <div class="content-expired order-pay-success" style="">
                        <img src="../static/images/success.png" class="close-icon" alt="close-icon">
                        <p class="tiemout-title"> <%=uConfig.gd(userdt, "gateway_network").IndexOf("代付") != -1 ? "提现" : "支付" %>成功</p>
                        <p class="timeout-description" style="color: gray;">您的订单已<%=uConfig.gd(userdt, "gateway_network").IndexOf("代付") != -1 ? "提现" : "支付" %>成功</p>
                    </div>
                </div>


            </div>

            <div class="_row" style="display: flex; margin-top: 50px;">
                <div class="_column left">
                    <p>订单编号</p>
                </div>
                <div class="_column right" style="margin-left: auto;">
                    <p class="order_id" style="color: gray;"><%=uConfig.gd(userdt,"orderId") %></p>
                </div>
            </div>

        </div>


        <a style="display: block; background: #3838f5; color: #fff; width: 100%; padding: 16px; box-sizing: border-box; border-radius: 10px; cursor: pointer; text-align: center; font-size: 14px; margin-top: 13px; text-decoration: none;" href="account.aspx" class="create_button">个人中心</a>

    </div>



    <div id="pay_Unpay">

        <div style="background: #fff; border-radius: 8px; padding: 18px; font-size: 14px; margin-bottom: 30px;">

            <div style="display: flex; margin-bottom: 25px;">
                <div style="color: gray;">交易类型</div>
                <div style="margin-left: auto; color: #3a3b3c; font-weight: bold; display: flex;">
                    <%=uConfig.gd(userdt, "gateway_network").IndexOf("代付") != -1 ? "提现" : "充值" %>
                </div>
            </div>

            <div style="display: flex; margin-bottom: 25px;">
                <div style="color: gray;">订单编码</div>
                <div style="margin-left: auto; color: #3a3b3c; font-weight: bold; display: flex;">
                    <%=uConfig.gd(userdt,"orderId") %><svg t="*************" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="19366" width="18" height="18" style="padding: 0 8px; cursor: pointer;" onclick="textCopy('<%=uConfig.gd(userdt,"orderId") %>')">
                        <path d="M866.133333 919.466667h-405.333333c-29.866667 0-53.333333-23.466667-53.333333-53.333334v-618.666666c0-29.866667 23.466667-53.333333 53.333333-53.333334h405.333333c29.866667 0 53.333333 23.466667 53.333334 53.333334v618.666666c0 29.866667-23.466667 53.333333-53.333334 53.333334z" fill="#69C0FF" p-id="19367"></path><path d="M770.133333 428.8h-213.333333c-12.8 0-21.333333-8.533333-21.333333-21.333333s8.533333-21.333333 21.333333-21.333334h213.333333c12.8 0 21.333333 8.533333 21.333334 21.333334s-10.666667 21.333333-21.333334 21.333333zM770.133333 556.8h-213.333333c-12.8 0-21.333333-8.533333-21.333333-21.333333s8.533333-21.333333 21.333333-21.333334h213.333333c12.8 0 21.333333 8.533333 21.333334 21.333334s-10.666667 21.333333-21.333334 21.333333zM770.133333 684.8h-213.333333c-12.8 0-21.333333-8.533333-21.333333-21.333333s8.533333-21.333333 21.333333-21.333334h213.333333c12.8 0 21.333333 8.533333 21.333334 21.333334s-10.666667 21.333333-21.333334 21.333333z" fill="#FFFFFF" p-id="19368"></path><path d="M567.466667 834.133333h-405.333334c-29.866667 0-53.333333-23.466667-53.333333-53.333333v-618.666667c0-29.866667 23.466667-53.333333 53.333333-53.333333h405.333334c29.866667 0 53.333333 23.466667 53.333333 53.333333v618.666667c0 29.866667-23.466667 53.333333-53.333333 53.333333z" fill="#1890FF" p-id="19369"></path><path d="M471.466667 343.466667h-213.333334c-12.8 0-21.333333-8.533333-21.333333-21.333334s8.533333-21.333333 21.333333-21.333333h213.333334c12.8 0 21.333333 8.533333 21.333333 21.333333s-10.666667 21.333333-21.333333 21.333334zM471.466667 471.466667h-213.333334c-12.8 0-21.333333-8.533333-21.333333-21.333334s8.533333-21.333333 21.333333-21.333333h213.333334c12.8 0 21.333333 8.533333 21.333333 21.333333s-10.666667 21.333333-21.333333 21.333334zM471.466667 599.466667h-213.333334c-12.8 0-21.333333-8.533333-21.333333-21.333334s8.533333-21.333333 21.333333-21.333333h213.333334c12.8 0 21.333333 8.533333 21.333333 21.333333s-10.666667 21.333333-21.333333 21.333334z" fill="#FFFFFF" p-id="19370"></path>
                    </svg>
                </div>
            </div>

            <div style="display: flex; margin-bottom: 25px;">
                <div style="color: gray;">充值账户</div>
                <div style="margin-left: auto; color: #0066EF; font-weight: bold;">
                    <%=uConfig.p_userNick %>
                </div>
            </div>

            <div style="display: flex; margin-bottom: 25px;">
                <div style="color: gray;">交易金额</div>
                <div style="margin-left: auto; color: #0066EF; font-weight: bold;">
                    <%=uConfig.gd(userdt,"amount") %>
                </div>
            </div>

            <div style="display: flex; ">
                <div style="color: gray;">交易状态</div>
                <div style="margin-left: auto; color: #0066EF; font-weight: bold;">


                    <div style="font-size: 14px; font-weight: 500; color: #222; display: flex; align-items: center;" class="rotate-element">

                        <svg t="1694330145365" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="14297" width="14" height="14">
                            <path d="M511.49 294.67c-16.57 0-30-13.43-30-30V129.21c0-16.57 13.43-30 30-30s30 13.43 30 30v135.46c0 16.57-13.44 30-30 30zM510.45 927.13c-16.57 0-30-13.43-30-30V761.67c0-16.57 13.43-30 30-30s30 13.43 30 30v135.46c0 16.57-13.43 30-30 30zM265.66 540.28H130.2c-16.57 0-30-13.43-30-30s13.43-30 30-30h135.46c16.57 0 30 13.43 30 30s-13.43 30-30 30zM892.81 540.28H757.35c-16.57 0-30-13.43-30-30s13.43-30 30-30h135.46c16.57 0 30 13.43 30 30s-13.43 30-30 30zM263.14 809.9c-7.68 0-15.36-2.93-21.21-8.79-11.72-11.72-11.72-30.71 0-42.43l95.79-95.79c11.72-11.72 30.71-11.72 42.43 0 11.72 11.72 11.72 30.71 0 42.43l-95.79 95.79c-5.86 5.86-13.54 8.79-21.22 8.79zM706.6 366.44c-7.68 0-15.36-2.93-21.21-8.79-11.72-11.72-11.72-30.71 0-42.43l95.79-95.79c11.71-11.71 30.71-11.71 42.43 0 11.72 11.72 11.72 30.71 0 42.43l-95.79 95.79c-5.86 5.86-13.54 8.79-21.22 8.79zM781.13 809.9c-7.68 0-15.35-2.93-21.21-8.79l-95.79-95.79c-11.72-11.72-11.72-30.71 0-42.43 11.72-11.72 30.71-11.71 42.43 0l95.79 95.79c11.72 11.72 11.72 30.71 0 42.43a29.95 29.95 0 0 1-21.22 8.79zM337.67 366.44c-7.68 0-15.35-2.93-21.21-8.79l-95.79-95.79c-11.72-11.72-11.72-30.71 0-42.43 11.72-11.72 30.71-11.71 42.43 0l95.79 95.79c11.72 11.72 11.72 30.71 0 42.43a29.933 29.933 0 0 1-21.22 8.79z" p-id="14298" fill="#000"></path></svg>

                        交易中<div class="countdown_code"></div>
                    </div>



                </div>
            </div>



        </div>



        <a style="display: block; background: #3838f5; color: #fff; width: 100%; padding: 16px; box-sizing: border-box; border-radius: 10px; cursor: pointer; text-align: center; font-size: 14px; margin-top: 13px; text-decoration: none;" onclick="success_pay()" class="create_button">我已支付</a>

    </div>



    <script>
        var success_pay = function () {
            //security_password(function (e) {

            v3api("check_gatewayPay", {
                error: 1,
                data: {
                    //paypwd: e.password,
                    orderId: '<%=Request.QueryString["orderId"] %>'
                }
            }, function (e) {
                if (e.msg == "未支付") {
                    setTimeout(function () {
                        success_pay();
                    }, 5000);
                } else if (e.msg == "支付成功") {
                    $('#pay_Unpay').hide();
                    $('#pay_Success').show();
                } else {
                    tp(e.msg);
                }
            })
            //})
        }
        success_pay();
    </script>
</asp:Content>

